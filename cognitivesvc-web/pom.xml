<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>cognitivesvc-web</artifactId>
	<packaging>jar</packaging>

	<parent>
		<groupId>com.sensetime.intersense</groupId>
		<artifactId>cognitivesvc</artifactId>
		<version>2.14.0-SNAPSHOT</version>
		<relativePath>../</relativePath>
	</parent>

	<dependencies>
		<dependency>
			<groupId>com.sensetime.intersense</groupId>
			<artifactId>cognitivesvc-stream-face</artifactId>
			<version>2.14.0-SNAPSHOT</version>
		</dependency>
		
		<dependency>
			<groupId>com.sensetime.intersense</groupId>
			<artifactId>cognitivesvc-stream-ped</artifactId>
			<version>2.14.0-SNAPSHOT</version>
		</dependency>
		
		<dependency>
			<groupId>com.sensetime.intersense</groupId>
			<artifactId>cognitivesvc-xswitcher</artifactId>
			<version>2.14.0-SNAPSHOT</version>
		</dependency>
        
		<dependency>
			<groupId>com.sensetime.intersense</groupId>
			<artifactId>cognitivesvc-xworker</artifactId>
			<version>2.14.0-SNAPSHOT</version>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.sensetime.lib</groupId>-->
<!--			<artifactId>sensetime-monitor</artifactId>-->
<!--		</dependency>-->

		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-impl</artifactId>
		</dependency>

		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-core</artifactId>
		</dependency>

		<dependency>
			<groupId>javax.activation</groupId>
			<artifactId>activation</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<phase>compile</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<copy failonerror="false" overwrite="true" force="true"
											todir="${project.build.outputDirectory}/refs/">
									<fileset dir="${project.basedir}/.git/refs" includes="**"/>
								</copy>
								<copy failonerror="false" overwrite="true" force="true"
											file="${project.basedir}/.git/HEAD"
											tofile="${project.build.outputDirectory}/HEAD" />
								<copy failonerror="false" overwrite="true" force="true"
											todir="${project.build.outputDirectory}/refs/">
									<fileset dir="${project.basedir}/../.git/refs" includes="**"/>
								</copy>
								<copy failonerror="false" overwrite="true" force="true"
											file="${project.basedir}/../.git/HEAD"
											tofile="${project.build.outputDirectory}/HEAD" />
								<copy failonerror="false" overwrite="true" force="true"
											file="${project.build.outputDirectory}/application-stg.yml"
											tofile="${project.build.outputDirectory}/application.yml" />
								<copy failonerror="false" overwrite="true" force="true"
											file="${project.build.outputDirectory}/config-stg.properties"
											tofile="${project.build.outputDirectory}/config.properties" />
								<delete quiet="true" dir="${project.build.outputDirectory}/sql"/>
								<delete quiet="true" dir="${project.build.outputDirectory}/cpp"/>
								<delete quiet="true" dir="${project.build.outputDirectory}/testcase"/>
								<delete quiet="true">
									<fileset dir="${project.build.outputDirectory}/" includes="*.sql" />
								</delete>
								<delete quiet="true">
									<fileset dir="${project.build.outputDirectory}/" includes="rebel.xml" />
								</delete>
								<delete quiet="true">
									<fileset dir="${project.build.outputDirectory}/" includes="*lic.yml" />
								</delete>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<layout>ZIP</layout>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <configuration>
          <skip>false</skip>
        </configuration>
      </plugin>
		</plugins>
	</build>
</project>
