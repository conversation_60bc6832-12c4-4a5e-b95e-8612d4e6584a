package com.sensetime.intersense.cognitivesvc.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

/**
 * @Author: Cirmons
 * @Date: 2022-12-26
 */
@Configuration
public class LocaleConfig {
    
    @Autowired
    private MessageSource messageSource;
    
    @Bean  // 这个Validator是javax包下的
    public Validator getValidator() {
        LocalValidatorFactoryBean localValidatorFactoryBean = new LocalValidatorFactoryBean();
        localValidatorFactoryBean.setValidationMessageSource(messageSource);
        return localValidatorFactoryBean;
    }
    
}
