package com.sensetime.intersense.cognitivesvc.web;

import java.io.File;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;

public class TestApplication{

	public static void main(String[] args) throws Exception{
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(10000);
//        requestFactory.setReadTimeout(10000);
        RestTemplate rest = new RestTemplate(requestFactory);
        
		String adds[] = new String[]{
				"http://************:3342/cognitive/face/retrieveFaceFeature", 
				"http://************:3343/cognitive/face/retrieveFaceFeature"};
        
		String dels[] = new String[]{
				"http://************:3342/cognitive/face/deleteByPId", 
				"http://************:3343/cognitive/face/deleteByPId"};
				
		File files[] = new File("D:/29image/").listFiles();
		
		Thread addings[] = new Thread[20];
		Thread deletes[] = new Thread[20];
		
		for(int index = 0 ;index < addings.length; index ++) {
			addings[index] = new Thread(() -> {
				while(true) {
					int num = ThreadLocalRandom.current().nextInt(files.length);
					String id = files[num].getAbsoluteFile().getName().split("\\.")[0];
					String url = adds[ThreadLocalRandom.current().nextInt(adds.length)] + "?figureImageUrl=/images/29image/" + id + ".jpg&personCnName=test&personId=" + id;
					try {
						Object obj = rest.getForObject(url, Object.class);
						obj.toString();
					}catch(Exception e) {
						
					}
					try { Thread.sleep(500); } catch (InterruptedException e) { }
				}
			}, "adding-" + index);
			
			addings[index].start();
		}
		
		for(int index = 0 ;index < deletes.length; index ++) {
			deletes[index] =  new Thread(() -> {
				while(true) {
					int num = ThreadLocalRandom.current().nextInt(files.length);
					String id = files[num].getAbsoluteFile().getName().split("\\.")[0];
					String url = dels[ThreadLocalRandom.current().nextInt(dels.length)] + "?pids=" + id;
					try {
						Object obj = rest.getForObject(url, Object.class);
						obj.toString();
					}catch(Exception e) {
						
					}
					try { Thread.sleep(500); } catch (InterruptedException e) { }
				}
			}, "delete-" + index);
			
			deletes[index].start();
		}
	}
	
	@SuppressWarnings("unchecked")
	public static void main2(String[] args) throws Exception{
		List<String> txt1 = Lists.newArrayList(FileUtils.readFileToString(new File("D:/node1.txt"), Charset.defaultCharset()).split(","));
		List<String> txt2 = Lists.newArrayList(FileUtils.readFileToString(new File("D:/node2.txt"), Charset.defaultCharset()).split(","));
		
		List<String> inters = (List<String>)CollectionUtils.intersection(txt1, txt2);
		
		txt1.removeAll(inters);
		txt2.removeAll(inters);
		
		txt1.toString();
		txt1.toString();
	}
	
	public static void main1(String[] args) throws Exception{
		System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", "128");
		
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(10000);
//        requestFactory.setReadTimeout(10000);
        RestTemplate rest = new RestTemplate(requestFactory);
        
		String adds[] = new String[]{
				"http://************:3342/cognitive/face/retrieveFaceFeature", 
				"http://************:3343/cognitive/face/retrieveFaceFeature"};
		
		File files[] = new File("D:/29image/").listFiles();
		
		Arrays.stream(files).parallel().forEach(file -> {
			String id = file.getAbsoluteFile().getName().split("\\.")[0];
			String url = adds[ThreadLocalRandom.current().nextInt(adds.length)] + "?figureImageUrl=/images/29image/0000a022-37d7-433a-b299-98c8c5a9c6e3.jpg&personCnName=test&personId=" + id;
			try {
				Object obj = rest.getForObject(url, Object.class);
				obj.toString();
			}catch(Exception e) {
				
			}
		});
	}
	
	@SuppressWarnings("unchecked")
	public static void main11(String[] args) throws Exception{
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(1000000);
//        requestFactory.setReadTimeout(1000000);
        RestTemplate rest = new RestTemplate(requestFactory);
        
		String querys[] = new String[]{
				"http://************:3342/cognitive/face/compareFaceIdentity", 
				"http://************:3343/cognitive/face/compareFaceIdentity"};

		System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", "128");
		
		List<Pair<List<String>, List<String>>> error1 = new ArrayList<Pair<List<String>, List<String>>>();
		List<Pair<List<String>, List<String>>> error2 = new ArrayList<Pair<List<String>, List<String>>>();
		List<Pair<List<String>, List<String>>> error3 = new ArrayList<Pair<List<String>, List<String>>>();
		List<Pair<List<String>, String>> error4 = new ArrayList<Pair<List<String>, String>>();
		
		List<MutableTriple<String, Map<String, Object>, Map<String, Object>>> lists = Arrays.stream(new File("D:/29image/").listFiles()).limit(111111).parallel().map(file -> {
				String id = file.getAbsoluteFile().getName().split("\\.")[0];
				
				Map<String, Object> obj1 = (Map<String, Object>)rest.getForObject(querys[0] + "?count=10&threshold=0.95&figureImageUrl=/images/29image/" + id + ".jpg", Object.class);
				Map<String, Object> obj2 = (Map<String, Object>)rest.getForObject(querys[1] + "?count=10&threshold=0.95&figureImageUrl=/images/29image/" + id + ".jpg", Object.class);
				
				return new MutableTriple<String, Map<String, Object>, Map<String, Object>>(id, obj1, obj2);
			})
			.collect(Collectors.toList());
		
		for(Triple<String, Map<String, Object>, Map<String, Object>> pair : lists) {
			String id = pair.getLeft();
			List<Map<String, Object>> data1 = (List<Map<String, Object>>)pair.getMiddle().getOrDefault("data", List.of());
			List<Map<String, Object>> data2 = (List<Map<String, Object>>)pair.getRight().getOrDefault("data", List.of());
			
			if(data1 == null || data2 == null)
				continue;
			
			if(data1.size() == 0 && data2.size() == 0)
				continue;
			
			List<String> pid1 = data1.stream().map(item -> item.get("personID").toString()).distinct().collect(Collectors.toList());
			List<String> pid2 = data2.stream().map(item -> item.get("personID").toString()).distinct().collect(Collectors.toList());
			
			if(pid1.size() != pid2.size()) {
				error1.add(new MutablePair<List<String>, List<String>>(pid1, pid2));
			}else if(pid1.size() > 1 || pid2.size() > 1) {
				if(!CollectionUtils.isEqualCollection(pid1, pid2))
					error2.add(new MutablePair<List<String>, List<String>>(pid1, pid2));
			}else {
				boolean equal = Objects.equal(pid1.get(0), pid2.get(0));
				if(!equal)
					error3.add(new MutablePair<List<String>, List<String>>(pid1, pid2));
				else if(!pid1.contains(id)) 
					error4.add(new MutablePair<List<String>, String>(pid1, id));
			}
		}
		System.out.println("************************************");
		System.out.println(JSON.toJSONString(error1));
		System.out.println("************************************");
		System.out.println(JSON.toJSONString(error2));
		System.out.println("************************************");
		System.out.println(JSON.toJSONString(error3));
		System.out.println("************************************");
		System.out.println(JSON.toJSONString(error4));
	}
}
