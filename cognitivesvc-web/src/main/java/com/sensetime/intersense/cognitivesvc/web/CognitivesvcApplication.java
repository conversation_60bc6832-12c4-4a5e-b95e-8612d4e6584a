package com.sensetime.intersense.cognitivesvc.web;

import com.google.j2objc.annotations.Property;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageSaveUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.lib.weblib.config.LogAop;
import com.sensetime.storage.autoconfigure.StorageProperties;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import swagger.SwaggerUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.sensetime.storage.factory.FileStorageType.OSG;

@SpringBootApplication
@EnableScheduling
@Slf4j
@ComponentScan
public class CognitivesvcApplication {

    @Autowired
    XDynamicModelRepository xDynamicModelRepository;

    @Value("${preMakeDirs}")
    private String preMakeDirs;

//    @Autowired
//    private Utils.Sync sync;

    @Autowired
    StorageProperties storageProperties;

    public static void main(String[] args) throws Exception {
        String hsqlPath = Utils.getProperty("hsqldb.storage.path");
        if ("default".equalsIgnoreCase(hsqlPath)) {
            System.setProperty("hsqldb.storage.path", "/kestrel/other/hsqldb/cognitivesvc");
            hsqlPath = Utils.getProperty("hsqldb.storage.path");
        }

        if (StringUtils.isNotBlank(hsqlPath)) {
            System.setProperty("spring.datasource.driver-class-name", "org.hsqldb.jdbc.JDBCDriver");
            System.setProperty("spring.datasource.url", "jdbc:hsqldb:file:" + hsqlPath + ";sql.syntax_mys=true;shutdown=true");
            System.setProperty("spring.datasource.username", "sa");
            System.setProperty("spring.datasource.password", "sa");
            System.setProperty("spring.liquibase.change-log", "classpath:/db/changelog/db.changelog-hsql.yaml");
        }

        if (StringUtils.isBlank(Utils.getProperty("senseye.event.output.callback"))
                && StringUtils.isBlank(Utils.getProperty("senseye.event.output.file"))
                && StringUtils.isBlank(Utils.getProperty("senseye.event.output.none")))
            System.setProperty("senseye.event.using.kafka", "yes");
        else
            System.setProperty("senseye.event.using.kafka", "no");

        LogAop logAop = new LogAop();

        logAop.logAopEnabled = Boolean.valueOf(Utils.getProperty("log.aop.enabled"));

        SwaggerUtils.basePackage = "com.sensetime.intersense.cognitivesvc";

        System.setProperty("spring.main.allow-bean-definition-overriding", "true");

        ConfigurableApplicationContext context = SpringApplication.run(CognitivesvcApplication.class, args);

        context.toString();

        String seeker_enabled = System.getProperty("seeker.enabled");
        log.info("env seeker.enabled :{}",seeker_enabled);
    }

    @PostConstruct
    @Scheduled(cron = "${preMakeDirsCron: 0 0 10,18,22 * * ?}")
    //@Scheduled(cron = "${preMakeDirsCron: 0 0/2 * * * ?}")
    public void preMakeDirsCron() {

        if(storageProperties.getFileStorageType().equals(OSG)){
            ImageUtils.preMkDirPostContructCompleted = true;
            log.info("******* use osg, no need preMkdirs");
            return;
        }

        try {
            //sync.sync();

            List<String> allDirs = new ArrayList<>(Arrays.asList(preMakeDirs.split(",")));

            xDynamicModelRepository.findAll().stream()
                    .filter(e -> e.getSts().equals(0))
                    .forEach(model -> allDirs.add(model.getAnnotatorName()));

            ImageUtils.mkdirsByHour(allDirs, false);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
