management:
  tracing:
    brave:
      span-joining-supported: true
    propagation:
      produce: b3
      consume: b3
    enabled: true
    baggage:
      enabled: true
      correlation:
        fields: Deploy<PERSON><PERSON>r,Use<PERSON><PERSON><PERSON>,<PERSON>gg<PERSON>,Role,DeptId,SubDeptIds
      remote-fields: Deploy<PERSON>olor,User<PERSON>ame,Baggage,Role,DeptId,SubDeptIds
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include:
          - health
          - info
          - prometheus
  endpoint:
    prometheus:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}


server:
  port: 3332
  maxPostSize: -1
  maxHttpHeaderSize: 102400
  tomcat:
    max-http-form-post-size: 10MB
spring.profiles.include:
  - kafka-${KAFKA_ACTIVE:default}  #default、cloud
logging.level:
  root: info
  io.swagger.models.parameters.AbstractSerializableParameter: ERROR
  org.springframework.cloud.stream.binder.kafka: ERROR
  org.springframework.scheduling.concurrent: ERROR
  org.apache.kafka: ERROR
  com.netflix.discovery.shared.resolver.aws: ERROR
  com.ulisesbocchio.jasyptspringboot: ERROR
  org.apache.http.client.protocol.ResponseProcessCookies: ERROR
  com.sensetime.intersense.cognitivesvc.seekerface.seeker: info
  com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker: info
  org.hibernate.SQL: INFO
  org.hibernate.type: INFO


database.name: cognitivesvc

spring.cloud.config.enabled: false

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: cognitivesvc
  messages.basename: i18n/messages
  datasource:
    #type: com.alibaba.druid.pool.DruidDataSource
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.jdbc.Driver
    # for mysql8.0 - url
    url: ${MYSQL_CONNECT:********************************/}${database.name}${MYSQL_CONNECT_OPTS:?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&clientCertificateKeyStorePassword=123456&clientCertificateKeyStoreType=JKS&clientCertificateKeyStoreUrl=file:/galera-ss/keystore.jks&trustCertificateKeyStorePassword=123456&trustCertificateKeyStoreType=JKS&trustCertificateKeyStoreUrl=file:/galera-ss/truststore.jks}
#    url: ********************************************************/${database.name}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&clientCertificateKeyStorePassword=123456&clientCertificateKeyStoreType=JKS&clientCertificateKeyStoreUrl=file:/executive/galera-ss/keystore.jks&trustCertificateKeyStorePassword=123456&trustCertificateKeyStoreType=JKS&trustCertificateKeyStoreUrl=file:/executive/galera-ss/truststore.jks
    username: ${MYSQL_USERNAME:mscognitivesvc}
    password: ${MYSQL_PWD:ENC(ycnuWd0apeSZm5/r9Og4WPBMOs7MqtVj)}
    
spring.data.rest.base-path: /spring/data/rest

ribbon.ConnectTimeout: 10000
ribbon.ReadTimeout: 10000

spring.servlet.multipart.max-file-size: 1000MB
spring.servlet.multipart.max-request-size: 1000MB

endpoints.env.enabled: false
endpoints.mappings.enabled: false
endpoints.beans.enabled: false

spring.jpa:
  open-in-view: false
  hibernate.naming.physical-strategy: com.sensetime.intersense.cognitivesvc.server.mapper.ZpringPhysicalNamingStrategy

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 10
    leaseExpirationDurationInSeconds: 10
  server:
    enableSelfPreservation: false
  client:
    registerWithEureka: true
    fetchRegistry: true
    registryFetchIntervalSeconds: 20
    serviceUrl:
      defaultZone: ${EUREKA_URL:"http://eureka-external:8761/eureka/"}

intersense:
  midface:
    imageRoot: /sensetime/tmp/
    deleteOnDone: 1
   # memberIdCacheClearFixedDelayMillis: 60000 # 清理seek face中的人组映射缓存，改为yaml 配置参数，单位：毫秒, 默认：60*1000ms
  purpose: prod # poc | prod
  reboot-schedule: "0 3 0 * * ?"
  cronDispatch: 15000
  cronGpuRate: "0/30 * * * * ?"
#  faissSeeker:
#    dailyReFetchDataCron: "0 0 1 * * ?"  # 每天凌晨一点 全量重建faiss索引
#    selfCheckCron: "0 0 * * * ?"         # 定时检查当前节点的faiss 索引数据是否有问题，有问题就全量重建索引
  stranger:
    simplify-seeker:
      rolling-window-count: 180         # 时间窗口数量(默认 30mins)
      rolling-window-length: 10         # 时间窗口长度，单位：s，可选： 10、15、20、30、60
      tsgap-condition: 3000             # 同一时间窗口，两张相似照片小于该时间间隔认为是同一次抓拍，单位：ms
      period-showtimes-condition: 2     # 归档条件，时间窗口出现次数（至少要在n个时间窗口中出现）
      device-showtimes-condition: 1     # 归档条件，设备出现次数（至少要在n个设备中出现）
      enrole-window-cron: "0/10 * * * * ?"         # 初始化下一个时间窗口（跟随rolling-window-length改变需要调整）
      aggregate-stranger-cron: "9/10 * * * * ?"   # 归档计算（跟随rolling-window-length改变需要调整）

#spring.cloud.stream:
#  binders:
#    _kafka:
#      type: kafka
#      environment:
#        #  org.springframework.cloud.stream.binder.kafka.properties.KafkaBinderConfigurationProperties
#        #  https://docs.spring.io/spring-cloud-stream/docs/Ditmars.SR4/reference/htmlsingle/
#        spring.cloud.stream.kafka.binder:
#          brokers: ${KAFKA_ADDR:kafka-external:9092}
#          zkNodes: ${ZOOKEEPER_ADDR:zk-external:2181}
#          autoCreateTopics: true
#          autoAddPartitions: true
#          minPartitionCount: 256
#          replicationFactor: 1
#          configuration:
#            security.protocol: SASL_SSL
#            sasl.mechanism: SCRAM-SHA-512
#            sasl.jaas.config: org.apache.kafka.common.security.scram.ScramLoginModule required username="admin" password="${KAFKA_PASS:nJFyptzMd1tV}";
#            ssl.truststore.location: /certificates/client.truststore.jks
#            ssl.truststore.password:  ${KAFKA_TLS_PASS:ENC(35dcBRBhxkU13py3XN+FSIimI4yO6ImF)}
#            ssl.keystore.location: /certificates/client.keystore.jks
#            ssl.keystore.password:  ${KAFKA_TLS_PASS:ENC(35dcBRBhxkU13py3XN+FSIimI4yO6ImF)}
#            ssl.endpoint.identification.algorithm:

cognitivesvc:
  lic: "/executive/token.lic"
  viperEncKey: ${SFD_ENC_KEY:2NVp7Lc60V0644j9}
  featureBatchLimit: 32

sensehatch:
  enc:
    host: ENC(nfNCx9+4m/XCCQFiqhXVnQ==)
    user: ENC(gL4Yenv7CQi9NhaqJ4fRZg==)

preMakeDirs: tmp_face,tmp_ped,offlineImages,faceped,face


storage:
  fileStorageType: ${FILE_STORAGE_TYPE:OSG}
  oSGStorageConfig:
     osgHost: ${SFD_OSG_GRPC_URL:osg.sfe:8088}
  generalStorageConfig:
     basePath: /images/

st.library.logaop:
  enable: false
  httpheader: false
  fullparams: false
  maxloglength: 8192

lpr-service:
  plateOcrByte-url: http://licenseplaterecognitionservice.sfe.svc.cluster.local:80/lpr/plateOcrByte

vaxtor:
  enableVaxtorPlateRecognition: ${VAXTOR_PLATE_RECOGNITION_ENABLE:false} # 是否启用 vaxtor 车牌识别
  ocrDataPath: /kestrel/other/libs/vaxtor/ocr_data.bin # vaxtor sdk 数据文件
  countryList: # 支持的国家配置，参考： https://support.vaxtor.com/portal/en/kb/articles/vaxalpr
    - Singapore
  detectorCount: 20 # processor 数量 --》 对应最大并发数，需要sdk授权支持
  queueSize: 100 # 每个processor 缓存队列大小
  scaleFactors: # 车牌识别过程，小图放大比例；数组，如果有多个值，会按先后顺序多次进行放大后检测车牌
    - 1.0
    - 1.5
    - 2.0
  init-conf:
    min-char-height: 12 # 最小字符高度 ,Min is 12px max is 70px. Optimal character height is average from 25 to 35px in height
    max-char-height: 70 # 最大字符高度
    ocr-complexity: 3 # OCR algorithm complexity (1:low, 2:normal, 3:high)
  mmc-config:
    analytic-type: 1 # 分析类型,可选值1-3;  1 - 仅分析车辆品牌; 2 - 分析车辆品牌和型号; 3 - 分析车辆品牌、型号和颜色
    analytics-quality: 3 # 分析质量,可选值1-3;  1 - 低质量(快速); 2 - 中等质量; 3 - 高质量(较慢)
    min-globalconfidence: 20 # 最小全局置信度,范围0-100,建议值:20-30

cognitivesvcx:
  availableGpuMemory: 1024

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator


# sensexperience:
#   url: ${SFD_OSG_URL:http://osg-default.default.svc.cluster.local:8087}
#   timeout: 2000
#   osgBucket: "face"

file:
  acceptImageFormat: jpg,jpeg,png,bmp
  acceptFileFormat: txt,log,docx,xlsx,csv,zip         # 接受文件格式，可修改
  acceptImageSize: 10485760         # 接受图片大小：10MB 10 * 1024 * 1024
  acceptFileSize: 1048576000          # 接受文件大小：10MB 1000 * 1024 * 1024
  offline: offline
  image-prehandle-config:
    enabled: true                             # 是否启用图片预处理，启用后会包含人脸质量检测逻辑 true|false
    enabledSaveImage: false                   # 是否存图下来
    enableMultiface: true                     # 是否允许多人脸，默认取最大脸
    delete-failed-image: true                 # 删除预处理发生失败的图片，true|false
    handle-image-type:                        # 预处理涉及的图片类型，详情见 ImageServiceEnum.class (不区分大小写)
      - person
    face-detect-config:
      enabled: true                           # 是否进行人像检查（当开启时会将图片截取成人像图），true|false
      face-enlarge-rate: 2.1                  # 人像图（框）放大比率
      rotated-image-detect-enabled: false      # 是否检测图片旋转（采用旋转图片进行人脸检测的方式），true|false
      resize-image-enabled: false              # 是否缩放图片，true|false
      resize-image-width: 540                 # 缩放图片时指定图片的宽度（等比缩放，单位：像素）
    quality-detect-config:
      image-approve-score: 0.65                # 图片质量分数阈值


sfd:
  face_1_N: "/v1/databases/{0}/batch_search"
  face_multi_wrapper: "/v1/face/search_image_in_dbs"
  apiWrapperHost: ${WRAPPER_URL:http://engine-api-wrapper-service.default:50001}
  staticFaceDbServiceHost: ${SFD_URL:http://afd-proxy.default:8080} #${INSIGHT_FEATURE_URL:http://engine-alert-feature-db-proxy-internal:8080}
  sfdEncKey: ${SFD_ENC_KEY:2NVp7Lc60V0644j9}
  dbList: "/v1/databases"
  db:
    name: studioDefaultSfdDb
    objectType: face
    featureVersion: 25400
    bodyFeatureVersion: 15600
    description: studioDefaultSfdDb
    dbSize: 10000000
    strangerName: "-99"
  
  