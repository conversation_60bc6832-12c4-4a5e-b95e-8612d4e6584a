#!/bin/bash

CIDR_MASK=`ip ad show br0 | grep br0 | grep noprefixroute | awk '{print $2}' | cut -d '/' -f 2`;

ansible all -b -f 3 -m shell -a 'systemctl enable pcsd corosync pacemaker; sleep 5; systemctl start pcsd; sleep 5; systemctl start corosync; sleep 5; systemctl start pacemaker';
ansible all -b -f 3 -m shell -a 'echo youtellme | passwd --stdin hacluster'

pcs cluster auth pc1 pc2 pc3 -u hacluster -p youtellme --force

pcs cluster setup --start --name k8s_cluster pc1 pc2 pc3

pcs property set stonith-enabled=false

pcs resource create VIP ocf:heartbeat:IPaddr2 ip=************** cidr_netmask=${CIDR_MASK} op monitor OCF_CHECK_LEVEL="0" timeout=30s interval=5s

pcs resource create haproxy ocf:heartbeat:haproxy binpath=/usr/sbin/haproxy conffile=/etc/haproxy/haproxy.cfg op monitor interval=10s

pcs resource group add HAproxyGroup VIP haproxy

pcs constraint order VIP then haproxy

ansible all -b -f 3 -m shell -a 'systemctl enable pcsd corosync pacemaker';


