#!/bin/bash
. /etc/profile
a=`ps -ef | grep 18cognitivexworker5554.sh | grep -v grep | grep -v -<PERSON> "vim|vi" | grep " 1  0"`
echo $a
a=`ps -ef | grep 18cognitivexworker5554.sh | grep -v grep | grep -v -E "vim|vi" | grep " 1  0" | wc -l`
echo $a
if [ $a -gt 1 ]; then
  echo "18cognitivexworker5554.sh is already running"
  exit 0
fi

while :
do
  ncat -z localhost 8777
  a=`echo $?`
  echo "8777: $a"
  if [ "$a" == "0" ]; then
	echo "swagger ready, start cognitive"
	sh /ceph/springconfig/preflightcheck.sh &&
    docker run --memory 20g --name cognitivexworker5554 --privileged --net=host --restart always --env DeviceType=T4 --env LD_LIBRARY_PATH=/lib64:/usr/lib64:/usr/cognitivesvc:/usr/local/cuda/targets/x86_64-linux/lib --env NVIDIA_DRIVER_CAPABILITIES=compute,video,utility --env LANG=C.UTF-8 -v /ceph/springconfig:/springconfig -v /ceph/executive/galera-ss:/executive/galera-ss -v /etc/localtime:/etc/localtime -v /ceph/kestrel/other/:/usr/share/fonts -v /ceph/kestrel:/kestrel -v /ceph/executive:/executive -v /ceph/images/:/images/ -d nvidia/cuda:11.0.3-cudnn8-devel-ubuntu20.04 /bin/bash -c "ln -sf /kestrel/other/cudnn8/libcudnn_adv_infer.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_adv_infer.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_adv_train.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_adv_train.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_cnn_infer.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_cnn_infer.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_cnn_train.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_cnn_train.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_ops_infer.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_ops_infer.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_ops_train.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_ops_train.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn.so.8.0.5; mkdir -p /usr/cognitivesvc; command /kestrel/other/jdk-11/bin/java -server -Xms2048m -Xmx12288m -jar -Djava.security.auth.login.config=/springconfig/.client-jaas -Djasypt.encryptor.password=youtellme -Dspring.profiles.include=swagger -Duser.timezone=$TIMEZONE -DDeviceId=1 -Dserver.port=5554 -Dspring.application.name=cognitivexworker -Ddatabase.name=cognitivesvcx -Dseeker.enabled=false -Dstream.enabled=false /executive/jars/cognitivesvc.jar; cp /*.log /images/core.5554.txt;"
    break
  fi
  echo "in 18cognitive3342"
  sleep 5
done
