#!/bin/bash

docker rm -f cognitivesuper;

. /etc/profile
a=`ps -ef | grep 18cognitivezuper.sh | grep -v grep | grep -v -<PERSON> "vim|vi" | grep " 1  0"`
echo $a
a=`ps -ef | grep 18cognitivezuper.sh | grep -v grep | grep -v -E "vim|vi" | grep " 1  0" | wc -l`
echo $a
if [ $a -gt 1 ]; then
  echo "18cognitivezuper.sh is already running"
  exit 0
fi

while :
do
  ncat -z localhost 8777
  a=`echo $?`
  echo "8777: $a"
  if [ "$a" == "0" ]; then
        echo "swagger ready, start cognitive"
        sh /ceph/springconfig/preflightcheck.sh &&
        docker run --name cognitivesuper --privileged --net=host --restart always --env DeviceType=T4 --env LD_LIBRARY_PATH=/lib64:/usr/lib64:/usr/cognitivesvc:/usr/local/cuda/targets/x86_64-linux/lib --env NVIDIA_DRIVER_CAPABILITIES=compute,video,utility --env LANG=C.UTF-8 -v /ceph/springconfig:/springconfig -v /ceph/executive/galera-ss:/executive/galera-ss -v /etc/localtime:/etc/localtime -v /ceph/kestrel/other/:/usr/share/fonts -v /ceph/kestrel:/kestrel -v /ceph/executive:/executive -v /ceph/images/:/images/ -d nvidia/cuda:11.0.3-cudnn8-devel-ubuntu20.04 /bin/bash -c "ln -sf /kestrel/other/cudnn8/libcudnn_adv_infer.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_adv_infer.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_adv_train.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_adv_train.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_cnn_infer.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_cnn_infer.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_cnn_train.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_cnn_train.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_ops_infer.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_ops_infer.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn_ops_train.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn_ops_train.so.8.0.5;ln -sf /kestrel/other/cudnn8/libcudnn.so.8.0.4 /usr/lib/x86_64-linux-gnu/libcudnn.so.8.0.5; mkdir -p /usr/cognitivesvc; command /kestrel/other/jdk-11/bin/java -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=*:26666 -Xms2048m -Xmx12288m -jar -Djava.security.auth.login.config=/springconfig/.client-jaas -Djasypt.encryptor.password=youtellme -Dspring.profiles.include=swagger -Duser.timezone=$TIMEZONE -DDeviceId=1 -Dserver.port=6666 -Dhsqldb.storage.path=/images/cognitiveStorage//hsqldb/cognitivesvc -Dsenseye.event.output.file=/images/cognitiveStorage/cogmessage -Dspring.application.name=cognitivesuper /executive/jars/cognitivesvc.jar; cp /*.log /images/core.6666.txt;"
  break
  fi
  echo "in 18cognitive3342"
  sleep 5
done
