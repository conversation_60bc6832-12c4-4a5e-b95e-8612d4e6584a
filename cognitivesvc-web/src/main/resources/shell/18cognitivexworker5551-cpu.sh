#!/bin/bash
. /etc/profile
a=`ps -ef | grep 18cognitivexworker5551.sh | grep -v grep | grep -v -<PERSON> "vim|vi" | grep " 1  0"`
echo $a
a=`ps -ef | grep 18cognitivexworker5551.sh | grep -v grep | grep -v -E "vim|vi" | grep " 1  0" | wc -l`
echo $a
if [ $a -gt 1 ]; then
  echo "18cognitivexworker5551.sh is already running"
  exit 0
fi

while :
do
  ncat -z localhost 8777
  a=`echo $?`
  echo "8777: $a"
  if [ "$a" == "0" ]; then
	echo "swagger ready, start cognitive"
	sh /ceph/springconfig/preflightcheck.sh &&
    docker run --memory 20g --name cognitivexworker5551 --privileged --net=host --restart always  --env DeviceType=HOST --env LD_LIBRARY_PATH=/usr/cognitivesvc --env NVIDIA_DRIVER_CAPABILITIES=compute,video,utility --env LANG=C.UTF-8 -v /ceph/springconfig:/springconfig -v /ceph/executive/galera-ss:/executive/galera-ss -v /etc/localtime:/etc/localtime -v /ceph/kestrel/other/:/usr/share/fonts -v /ceph/kestrel:/kestrel -v /ceph/executive:/executive -v /ceph/images/:/images/ -d openjdk:11.0.6-jdk /bin/bash -c "mkdir -p /usr/cognitivesvc; java -server -Xms2048m -Xmx8192m -jar -Djava.security.auth.login.config=/springconfig/.client-jaas -Djasypt.encryptor.password=youtellme -Dspring.profiles.include=swagger -Duser.timezone=$TIMEZONE -Dserver.port=5551 -Dspring.application.name=cognitivexworker -Ddatabase.name=cognitivesvcx -Dface.enabled=false -Dseeker.enabled=false -Dstream.enabled=false /executive/jars/cognitivesvc.jar; cp /*.log /images/core.5551.txt;"
	break
  fi
  echo "in 18cognitivexworker5551"
  sleep 5
done
