#!/bin/bash

docker rm -f cognitivesuper;

. /etc/profile
a=`ps -ef | grep 18cognitivezuper.sh | grep -v grep | grep -v -<PERSON> "vim|vi" | grep " 1  0"`
echo $a
a=`ps -ef | grep 18cognitivezuper.sh | grep -v grep | grep -v -E "vim|vi" | grep " 1  0" | wc -l`
echo $a
if [ $a -gt 1 ]; then
  echo "18cognitivezuper.sh is already running"
  exit 0
fi

while :
do
  ncat -z localhost 8777
  a=`echo $?`
  echo "8777: $a"
  if [ "$a" == "0" ]; then
        echo "swagger ready, start cognitive"
        sh /ceph/springconfig/preflightcheck.sh &&
        docker run --name cognitivesuper --privileged --net=host --restart always --env DeviceType=HOST --env LD_LIBRARY_PATH=/usr/cognitivesvc --env LANG=C.UTF-8 -v /ceph/springconfig:/springconfig -v /ceph/executive/galera-ss:/executive/galera-ss -v /etc/localtime:/etc/localtime -v /ceph/kestrel/other/:/usr/share/fonts -v /ceph/kestrel:/kestrel -v /ceph/executive:/executive -v /ceph/images/:/images/ -d openjdk:11.0.6-jdk /bin/bash -c "mkdir -p /usr/cognitivesvc; command java -server -Xms2048m -Xmx8192m -jar -Djava.security.auth.login.config=/springconfig/.client-jaas -Djasypt.encryptor.password=youtellme -Dspring.profiles.include=swagger -Duser.timezone=$TIMEZONE -Dserver.port=6666 -Dhsqldb.storage.path=/images/cognitiveStorage//hsqldb/cognitivesvc -Dsenseye.event.output.file=/images/cognitiveStorage/cogmessage -Dspring.application.name=cognitivesuper /executive/jars/cognitivesvc.jar; cp /*.log /images/core.6666.txt;"
  break
  fi
  echo "in 18cognitive3342"
  sleep 5
done
