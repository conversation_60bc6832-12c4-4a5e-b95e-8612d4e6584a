<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20210118_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="x_dynamic_model" columnName="runtime_status" />
		</preConditions>
		
		<sql>
		    ALTER TABLE `x_dynamic_model` DROP COLUMN `runtime_exception`;
            ALTER TABLE `x_dynamic_model` CHANGE COLUMN `runtime_status` `runtime` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'waiting to initialize' COMMENT '当前算法的状态' AFTER `monopolized_identity`;
		</sql>
	</changeSet>
</databaseChangeLog>