<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20200708_alter_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_infra" columnName="frame_skip" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE video_stream_infra ADD COLUMN frame_skip int(11) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '3' COMMENT '检测1帧跳N帧' AFTER decoder_format;
		</sql>
	</changeSet>
</databaseChangeLog>