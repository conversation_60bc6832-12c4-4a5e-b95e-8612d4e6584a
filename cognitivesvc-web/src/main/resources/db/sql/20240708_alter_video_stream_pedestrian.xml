<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20240708_alter_video_stream_pedestrian" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="video_stream_pedestrian" />
		</preConditions>
		<sql>
			ALTER TABLE `video_stream_pedestrian` ADD COLUMN `processors`   mediumblob DEFAULT NULL COMMENT '人体扩展' ;
		</sql>
	</changeSet>
</databaseChangeLog>