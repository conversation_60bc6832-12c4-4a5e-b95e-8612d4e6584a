<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20240911_update_cognitive_param" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="cognitive_param" />
		</preConditions>
		<sql>
			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'xStreamReopenErrorTime', '15', 'reopen error time(x stream)') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'xStreamReopenErrorTime'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'faceStreamReopenErrorTime', '60', 'reopen error time(face, low stream)') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'faceStreamReopenErrorTime'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'streamReopenSleepTime', '3', 'reopen sleep time(s)') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'streamReopenSleepTime'
			) LIMIT 1;
		</sql>
	</changeSet>
</databaseChangeLog>