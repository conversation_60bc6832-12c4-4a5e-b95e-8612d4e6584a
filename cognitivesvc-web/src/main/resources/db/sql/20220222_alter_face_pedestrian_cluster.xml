<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20220222_alter_face_pedestrian_cluster" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="face_pedestrian_cluster" columnName="face_person_type" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE `face_pedestrian_cluster` ADD COLUMN `face_person_type`       int(10) NOT NULL DEFAULT 0 COMMENT '人脸类型, 0person, 1passer' AFTER `face_person_id`;
		    ALTER TABLE `face_pedestrian_cluster` ADD COLUMN `pedestrian_person_type` int(10) NOT NULL DEFAULT 0 COMMENT '人体类型, 0person, 1passer' AFTER `pedestrian_person_id`;
		</sql>
	</changeSet>
</databaseChangeLog>