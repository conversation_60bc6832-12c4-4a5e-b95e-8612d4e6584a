<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <property name="now" value="now()" dbms="mysql" />

    <changeSet id="20240911_alter_video_stream_pedestrian" author="COG" runOnChange="true">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="video_stream_pedestrian" />
        </preConditions>
        <sql>
            ALTER TABLE `video_stream_pedestrian` ADD COLUMN `ped_quality_threshold`   float DEFAULT NULL COMMENT '人脸人体追踪质量阈值' AFTER `quality_threshold` ;
            ALTER TABLE `video_stream_pedestrian` ADD COLUMN `run_face_attr_model`   int(11) NOT NULL DEFAULT '0' AFTER `quality_threshold` ;
            ALTER TABLE `video_stream_pedestrian` ADD COLUMN `run_ped_attr_model`    int(11) NOT NULL DEFAULT '0' AFTER `quality_threshold` ;

        </sql>
    </changeSet>
</databaseChangeLog>