<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20180724_strangerfacefeature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="stranger_face_feature" />
			</not>
		</preConditions>
		
		<sql>
			CREATE TABLE stranger_face_feature (
				id int(20) unsigned NOT NULL AUTO_INCREMENT,
				avatar_image_url varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
				image_feature text COLLATE utf8mb4_unicode_ci NOT NULL,
				attribute text COLLATE utf8mb4_unicode_ci NULL,
				create_ts timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
				group_key varchar(20) COLLATE utf8mb4_unicode_ci NULL,
				device_id varchar(20) COLLATE utf8mb4_unicode_ci NULL,
				PRIMARY KEY (id),
				INDEX group_key (group_key) USING BTREE,
				INDEX create_ts (create_ts) USING BTREE
			) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		</sql>
	</changeSet>
</databaseChangeLog>