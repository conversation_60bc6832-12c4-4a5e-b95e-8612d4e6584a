<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20201106_alter_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_infra" columnName="dispatch_desc" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE video_stream_infra ADD COLUMN dispatch_desc varchar(128) COMMENT 'cogx高频流分配信息' AFTER `seed`;
		</sql>
	</changeSet>
</databaseChangeLog>