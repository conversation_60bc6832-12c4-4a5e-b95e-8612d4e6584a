<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20200905_alter_feature_uuid_index.xml" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="person_face_feature" />
		</preConditions>
		
		<sql>
			ALTER TABLE `person_face_feature` DROP INDEX `person_uuid`, ADD INDEX `person_uuid`(`person_uuid`) USING BTREE;
			ALTER TABLE `passer_face_feature` DROP INDEX `person_uuid`, ADD INDEX `person_uuid`(`person_uuid`) USING BTREE;
			ALTER TABLE `person_pedestrian_feature` DROP INDEX `person_uuid`, ADD INDEX `person_uuid`(`person_uuid`) USING BTREE;
			ALTER TABLE `passer_pedestrian_feature` DROP INDEX `person_uuid`, ADD INDEX `person_uuid`(`person_uuid`) USING BTREE;
		</sql>
	</changeSet>
</databaseChangeLog>