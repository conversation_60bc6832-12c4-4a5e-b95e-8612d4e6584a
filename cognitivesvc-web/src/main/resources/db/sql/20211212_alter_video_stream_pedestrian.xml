<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20211212_alter_video_stream_pedestrian" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="video_stream_pedestrian" />
		</preConditions>
		<sql>
			ALTER TABLE `video_stream_pedestrian` MODIFY COLUMN `store_scene` bit(1) DEFAULT NULL COMMENT '0为不存大图,1为存大图,null为默认' AFTER `roi_ids`;
			ALTER TABLE `video_stream_pedestrian` MODIFY COLUMN `store_passer` bit(1) DEFAULT NULL COMMENT '0为不存陌生人,1为存陌生人,null为默认' AFTER `store_scene`;
		</sql>
	</changeSet>
</databaseChangeLog>