<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20191212_alter_person_face_feature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="person_face_feature" columnName="create_ts" />
		</preConditions>
		<sql>
			ALTER TABLE person_face_feature MODIFY COLUMN create_ts timestamp(3) DEFAULT CURRENT_TIMESTAMP(3);
		</sql>
	</changeSet>
</databaseChangeLog>