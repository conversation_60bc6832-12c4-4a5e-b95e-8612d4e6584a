<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20210628_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="additional" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE `x_dynamic_model` ADD COLUMN `additional` varchar(1024) COMMENT '一些可选的额外信息用来描述算法行为' AFTER `monopolized_identity`;
		</sql>
	</changeSet>
</databaseChangeLog>