<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20180724_personfacefeature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="person_face_feature" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE person_face_feature (
				uuid varchar(36) NOT NULL,
				avatar_image_url varchar(512) NOT NULL,
				image_feature text NOT NULL,
				model_version varchar(512) NOT NULL,
				person_uuid varchar(36) NOT NULL,
				person_cn_name varchar(128) NOT NULL,
				person_en_name varchar(128) NULL,
				sts smallint(4) NOT NULL,
				create_user varchar(18) NOT NULL,
				create_ts timestamp DEFAULT CURRENT_TIMESTAMP,
				last_mod_user varchar(18) NULL,
				last_mod_ts timestamp NULL,
				tag varchar(128) NULL,
				PRIMARY KEY (uuid),
				INDEX create_ts (create_ts) USING BTREE,
				UNIQUE INDEX (person_uuid) USING HASH
			) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		</sql>
	</changeSet>
</databaseChangeLog>