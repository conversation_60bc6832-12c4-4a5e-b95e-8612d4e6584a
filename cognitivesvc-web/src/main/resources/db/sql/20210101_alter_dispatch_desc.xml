<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20210101_alter_dispatch_desc" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="video_stream_infra" columnName="dispatch_desc" />
			<and>
				<not>
					<columnExists tableName="video_stream_xswitcher" columnName="dispatch_desc"/>
				</not>
			</and>
		</preConditions>
		
		<sql>
		   ALTER TABLE `video_stream_infra` DROP COLUMN `dispatch_desc`;
		   ALTER TABLE `video_stream_xswitcher` ADD COLUMN `dispatch_desc` varchar(128) COMMENT 'cogx高频流分配信息' AFTER `processors`;
		</sql>
	</changeSet>
</databaseChangeLog>