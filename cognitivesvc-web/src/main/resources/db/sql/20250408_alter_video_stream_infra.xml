<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <property name="now" value="now()" dbms="mysql" />

    <changeSet id="20250408_alter_video_stream_infra" author="COG" runOnChange="true">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="video_stream_pedestrian" />
        </preConditions>
        <sql>
            ALTER TABLE `video_stream_infra` ADD COLUMN `priority`   float DEFAULT NULL COMMENT '解析优先级' AFTER `privilege` ;

        </sql>
    </changeSet>
</databaseChangeLog>