<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20220223_alter_video_stream_pedestrian" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_pedestrian" columnName="seek_num" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE `video_stream_pedestrian` ADD COLUMN `seek_num`            int(11) DEFAULT 1 COMMENT '搜索数量' AFTER `body_feature_threshold`;
			ALTER TABLE `video_stream_pedestrian` ADD COLUMN `refinement`          bit(1) COMMENT '是否全追踪特征融合' AFTER `seek_num`;
			
		    ALTER TABLE `video_stream_pedestrian` ADD COLUMN `quick_response_time` int(11) DEFAULT  1 COMMENT '快速人脸识别响应时间'  AFTER `target_group`;
			ALTER TABLE `video_stream_pedestrian` ADD COLUMN `time_interval`       int(11) DEFAULT -1 COMMENT '选帧触发的时间间隔'  AFTER `quick_response_time`;
			ALTER TABLE `video_stream_pedestrian` ADD COLUMN `max_track_time`      int(11) DEFAULT -1 COMMENT '目标最大的跟踪时长' AFTER `time_interval`;
			ALTER TABLE `video_stream_pedestrian` ADD COLUMN `max_tracklet_num`    int(11) DEFAULT NULL COMMENT '追踪目标上限(影响性能)'   AFTER `max_track_time`;
		</sql>
	</changeSet>
</databaseChangeLog>