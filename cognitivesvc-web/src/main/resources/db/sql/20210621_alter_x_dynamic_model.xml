<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20210621_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="cpu_model_dup" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE `x_dynamic_model` ADD COLUMN `cpu_model_dup` int(11) DEFAULT 0 COMMENT '大于零表示该能力是cpu且有该值个副本' AFTER `java_code`;
		    update `x_dynamic_model` set `cpu_model_dup` = 0;
		    ALTER TABLE `x_dynamic_model` MODIFY COLUMN `cpu_model_dup` int(11) NOT NULL DEFAULT 0 COMMENT '大于零表示该能力是cpu且有该值个副本' AFTER `java_code`;
		</sql>
	</changeSet>
</databaseChangeLog>