<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20201113_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="monopolized_group" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE x_dynamic_model ADD COLUMN `monopolized_group` varchar(16) COMMENT '相同group的模型独占一张卡'  AFTER `affinity_group`;
		    ALTER TABLE x_dynamic_model ADD COLUMN `monopolized_identity` varchar(128) COMMENT '独占信息'  AFTER `monopolized_group`;
		</sql>
	</changeSet>
</databaseChangeLog>