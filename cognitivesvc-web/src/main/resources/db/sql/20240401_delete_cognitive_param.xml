<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


	<changeSet id="20240401_delete_cognitive_param" author="COG" runOnChange="true">
		<sql>

			DELETE FROM cognitive_param
			WHERE s_key = 'integrateQuality'
			AND s_key IN (SELECT s_key FROM (SELECT * FROM cognitive_param) AS temp_table WHERE s_key = 'integrateQuality');
		</sql>
	</changeSet>
</databaseChangeLog>