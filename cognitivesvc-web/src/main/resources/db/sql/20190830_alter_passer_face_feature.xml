<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20190830_alter_passer_face_feature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="passer_face_feature" columnName="privilege" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE passer_face_feature ADD COLUMN privilege varchar(128) NOT NULL DEFAULT '';
		</sql> 
	</changeSet>
</databaseChangeLog>