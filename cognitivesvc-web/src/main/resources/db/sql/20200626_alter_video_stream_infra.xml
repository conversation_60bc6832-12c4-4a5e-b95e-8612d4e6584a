<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20200626_alter_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_infra" columnName="decoder_format" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE video_stream_infra ADD COLUMN decoder_format varchar(32) COLLATE utf8mb4_unicode_ci NULL COMMENT '解码类型' AFTER video_rate;
		</sql>
	</changeSet>
</databaseChangeLog>