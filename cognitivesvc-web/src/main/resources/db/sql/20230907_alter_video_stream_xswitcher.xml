<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20230907_alter_video_stream_xswitcher" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="video_stream_xswitcher" columnName="update_time" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE video_stream_xswitcher ADD COLUMN create_time timestamp not null default CURRENT_TIMESTAMP COMMENT '创建时间';

			ALTER TABLE video_stream_xswitcher ADD COLUMN update_time timestamp not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '更新时间';
		</sql>
	</changeSet>
</databaseChangeLog>