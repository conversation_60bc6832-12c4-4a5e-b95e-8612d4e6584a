<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200826_create_face_pedestrian_cluster.xml" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="face_pedestrian_cluster" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE `face_pedestrian_cluster` (
			  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
			  `face_person_id` varchar(36) NOT NULL COMMENT '人脸personId',
			  `pedestrian_person_id` varchar(36) NOT NULL COMMENT '人体personId',
			  PRIMARY KEY (`id`)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
			
			ALTER TABLE `face_pedestrian_cluster` ADD INDEX `face_person_id_index`(`face_person_id`) USING BTREE;
			ALTER TABLE `face_pedestrian_cluster` ADD INDEX `pedestrian_person_id_index`(`pedestrian_person_id`) USING BTREE;
			ALTER TABLE `face_pedestrian_cluster` ADD UNIQUE INDEX `unique_index`(`face_person_id`, `pedestrian_person_id`) USING BTREE;
		</sql>
	</changeSet>
</databaseChangeLog>