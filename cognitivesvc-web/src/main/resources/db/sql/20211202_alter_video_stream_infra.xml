<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20211202_alter_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_infra" columnName="frame_max" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE `video_stream_infra` ADD COLUMN `frame_max` int(11) COMMENT '该路视频最大检测帧数，达到数值后关闭流' AFTER `decoder_format`;
		</sql>
	</changeSet>
</databaseChangeLog>