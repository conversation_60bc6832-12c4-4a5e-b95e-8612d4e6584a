<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20230412_alter_cognitive_param" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="cognitive_param" />
		</preConditions>
		<sql>
			ALTER TABLE `cognitive_param` MODIFY COLUMN  `s_value`  varchar(256) not null;
		</sql>
	</changeSet>
</databaseChangeLog>