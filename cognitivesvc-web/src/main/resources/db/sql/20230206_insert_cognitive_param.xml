<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20230206_insert_cognitive_param" author="COG" runOnChange="true">
		<sql>

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'shieldFace_switch', '0', '0 aligner逻辑') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'shieldFace_switch'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'imageCapture_switch', '0', '1不发送cap message') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'imageCapture_switch'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'faceRecognitionMode', '0', '0精准模式,1快速模式') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'faceRecognitionMode'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'selectFrameTimeInterVal', '10', '默认10s') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'selectFrameTimeInterVal'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'strangerRecognition', '1', '陌生人识别开关0-开，1-关') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'strangerRecognition'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'seekType', '1', '特征对比库类型，可以使0(java比对), 1(faiss暴力比对), 2(faiss倒排索引比对)') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'seekType'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'integrateQuality', '0.45', '综合质量') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'integrateQuality'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'Lv0Threshold', '0.90', '阈值0') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'Lv0Threshold'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'Lv3Threshold', '1', '阈值3') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'Lv3Threshold'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'Lv1Threshold', '0.80', '阈值1') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'Lv1Threshold'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'dropFace', '1', '1关0开，生成dropface的图片和信息') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'dropFace'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'duplicateTargetsTime', '1', '重复过滤模块时间窗口,秒') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'duplicateTargetsTime'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'displayTarget', 'pageant_quality,quality,integrate_quality,yaw,pitch,roll', 'face回显字段,逗号分割') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'displayTarget'
			) LIMIT 1;

		</sql>
	</changeSet>
</databaseChangeLog>