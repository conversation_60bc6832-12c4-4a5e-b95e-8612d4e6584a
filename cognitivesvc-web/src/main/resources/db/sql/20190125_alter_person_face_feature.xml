<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20190125_alter_person_face_feature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="person_face_feature" columnName="tag" />
		</preConditions>
		<sql>
			ALTER TABLE person_face_feature MODIFY COLUMN tag  varchar(255) NULL;
		</sql>
	</changeSet>
</databaseChangeLog>