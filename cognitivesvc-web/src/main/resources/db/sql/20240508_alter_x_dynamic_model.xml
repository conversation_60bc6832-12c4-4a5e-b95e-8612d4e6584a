<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20240508_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="x_dynamic_model" columnName="runtime" />
		</preConditions>
		
		<sql>
			ALTER TABLE `x_dynamic_model` CHANGE COLUMN `runtime` `runtime` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '当前算法的状态' AFTER `monopolized_identity`;

			alter table x_dynamic_model modify monopolized_identity varchar(512) null comment '独占信息';

		</sql>
	</changeSet>
</databaseChangeLog>