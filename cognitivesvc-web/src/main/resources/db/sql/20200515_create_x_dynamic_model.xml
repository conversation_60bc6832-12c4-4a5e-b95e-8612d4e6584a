<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200515_create_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="x_dynamic_model" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE if not exists x_dynamic_model (
				annotator_name        varchar(64)    COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '算法名称',
				plugin_paths          varchar(2048)  COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '算法依赖的插件',
				annotator_paths       varchar(2048)  COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '算法依赖的模型',
				sts                   int            COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 0 COMMENT '状态0,1',
				update_ts             datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '',
				java_class_name       varchar(64)    COLLATE utf8mb4_unicode_ci NULL COMMENT '动态java类名',
				java_code             blob           NULL COMMENT '动态java代码',
				estimate_gpu_memory   int            COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 1000 COMMENT '预估模型使用显存默认1G',
				estimate_count        int            COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 1 COMMENT '期望部署副本个数',
				batch_size            int            COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 0 COMMENT '算法执行批量数',
				suggest_identity      varchar(512)   COLLATE utf8mb4_unicode_ci NULL COMMENT '期望部署的目标显卡,非强制',
				affinity_group        varchar(64)    COLLATE utf8mb4_unicode_ci NULL COMMENT '亲和性组，相同组名的模型优先部署相同显卡上',
				runtime_status        varchar(128)   COLLATE utf8mb4_unicode_ci NULL DEFAULT 'waiting to initialize' COMMENT '当前算法的状态',
				runtime_exception     varchar(512)   COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '当前算法爆出的异常',
				PRIMARY KEY (annotator_name)
			) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		</sql>
	</changeSet>
</databaseChangeLog>