<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20221219_model_source" author="menu" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="model_source" />
			</not>
		</preConditions>

		<sql>
		    ALTER TABLE `x_dynamic_model` ADD COLUMN `model_source` int COMMENT '模型来源' AFTER `runtime`;
		</sql>
	</changeSet>


	<changeSet id="20221219_gpu_model" author="menu" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="gpu_model" />
			</not>
		</preConditions>

		<sql>
		    ALTER TABLE `x_dynamic_model` ADD COLUMN `gpu_model` int COMMENT '是否gpu ' AFTER `model_source`;
		</sql>
	</changeSet>


	<changeSet id="20221219_gpu_mem_usage" author="menu" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="gpu_mem_usage" />
			</not>
		</preConditions>

		<sql>
		    ALTER TABLE `x_dynamic_model` ADD COLUMN `gpu_mem_usage` int COMMENT 'gpu模型使用大小 ' AFTER `gpu_model`;
		</sql>
	</changeSet>

	<changeSet id="20221219_model_cn_name" author="menu" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="model_cn_name" />
			</not>
		</preConditions>

		<sql>
		    ALTER TABLE `x_dynamic_model` ADD COLUMN `model_cn_name` varchar(128)  COMMENT '是否gpu ' AFTER `gpu_mem_usage`;
		</sql>
	</changeSet>


	<changeSet id="20221219_model_type" author="menu" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="model_type" />
			</not>
		</preConditions>

		<sql>
		    ALTER TABLE `x_dynamic_model` ADD COLUMN `model_type` int  COMMENT '1、检测 2、分类 3、分隔 ' AFTER `gpu_mem_usage`;
		</sql>
	</changeSet>



</databaseChangeLog>