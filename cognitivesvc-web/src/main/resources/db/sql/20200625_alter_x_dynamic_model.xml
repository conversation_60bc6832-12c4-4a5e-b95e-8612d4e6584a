<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20200625_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="attached" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE x_dynamic_model ADD COLUMN attached  varchar(2048) COLLATE utf8mb4_unicode_ci NULL COMMENT '额外文件(optional)' AFTER annotator_paths;
		</sql>
	</changeSet>
</databaseChangeLog>