<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200501_create_cognitive_lock" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="cognitive_lock" />
			</not>
		</preConditions>
		
		<sql>
			CREATE TABLE cognitive_lock (
				lock_key varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
				seed      varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
				create_ts  timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
				update_ts  timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
				PRIMARY KEY (lock_key)
			) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		</sql>
	</changeSet>
</databaseChangeLog>