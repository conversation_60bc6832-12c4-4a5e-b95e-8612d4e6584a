<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200405_alter_face_feature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<indexExists tableName="person_face_feature" indexName="create_ts" />
		</preConditions>
		
		<sql>
		    ALTER TABLE person_face_feature DROP INDEX create_ts;
		</sql>
	</changeSet>
</databaseChangeLog>