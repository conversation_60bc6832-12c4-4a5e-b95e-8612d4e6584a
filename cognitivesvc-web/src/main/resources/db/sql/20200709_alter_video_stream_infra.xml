<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20200709_alter_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_infra" columnName="rtmp_option" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE video_stream_infra ADD COLUMN rtmp_option varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'tune:zerolatency,preset:ultrafast' COMMENT 'ffmpeg的option参数' AFTER rtmp_destination;
		</sql>
	</changeSet>
</databaseChangeLog>