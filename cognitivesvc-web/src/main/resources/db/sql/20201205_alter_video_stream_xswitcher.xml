<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20201205_alter_video_stream_xswitcher" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_xswitcher" columnName="id" />
			</not>
		</preConditions>
		
		<sql>
			CREATE TABLE video_stream_xswitcher_temp (
			`id` int NOT NULL AUTO_INCREMENT,
			`device_id`       varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备id',
			`type`            int(10) NOT NULL DEFAULT '0' COMMENT '流类型: 0低频, 1高频CPU解码, 2高频GPU解码, 3高频GPU复用解码器, 4低频GPU复用解码器',
			processors       varchar(2048) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理器配置',
			PRIMARY KEY (id)
			) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
			INSERT INTO video_stream_xswitcher_temp (`device_id`, `type`, `processors`)
			SELECT `device_id`, `type`, `processors`
			FROM video_stream_xswitcher;
			DROP TABLE video_stream_xswitcher;
			RENAME TABLE video_stream_xswitcher_temp TO video_stream_xswitcher;
		</sql>
	</changeSet>
</databaseChangeLog>