<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200902_create_video_stream_pedestrian" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="video_stream_pedestrian" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE `video_stream_pedestrian` (
			  `device_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
			  `quality_threshold` float DEFAULT NULL COMMENT '人脸人体追踪质量阈值',
			  `face_feature_threshold` float DEFAULT NULL COMMENT '人脸特征比对阈值',
			  `body_feature_threshold` float DEFAULT NULL COMMENT '人体特征比对阈值',
			  `yaw` float DEFAULT NULL COMMENT '人脸角度',
			  `pitch` float DEFAULT NULL COMMENT '人脸角度',
			  `roll` float DEFAULT NULL COMMENT '人脸角度',
			  `min_face_size` int(11) DEFAULT NULL COMMENT '最小人脸框',
			  `min_body_size` int(11) DEFAULT NULL COMMENT '最小人体框',
			  `roi` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '热区',
			  `store_scene` int(11) DEFAULT NULL COMMENT '是否存大图',
			  `target_group` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标人员组',
			  PRIMARY KEY (`device_id`)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		</sql>
	</changeSet>
</databaseChangeLog>