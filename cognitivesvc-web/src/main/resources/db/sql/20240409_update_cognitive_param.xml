<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


	<changeSet id="20240409_update_cognitive_param" author="COG" runOnChange="true">
		<sql>

			<!--INSERT INTO cognitive_param VALUES ('imageSceneSave', 'false', '人脸存储大图,默认false不存') ON DUPLICATE KEY UPDATE s_value = 'false';!-->
			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'videoStatusErrorTime', '1', '取帧出错次数判断流状态error') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'videoStatusErrorTime'
			) LIMIT 1;


			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'videoStatusErrorInternalTime', '10', '取帧出错次数判断流状态Internal error') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'videoStatusErrorInternalTime'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'videoStatusErrorKestrelAgainTime', '10', '取帧出错次数判断流状态Again error') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'videoStatusErrorKestrelAgainTime'
			) LIMIT 1;


			DELETE FROM cognitive_param
			WHERE s_key = 'displayTarget'
			AND s_key IN (SELECT s_key FROM (SELECT * FROM cognitive_param) AS temp_table WHERE s_key = 'displayTarget');

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'displayTarget', 'pageant_quality,quality,integrate_quality,yaw,pitch,roll,image', 'face回显字段,逗号分割') AS tmp
			WHERE NOT EXISTS (
			SELECT s_key FROM cognitive_param WHERE s_key = 'displayTarget'
			) LIMIT 1;

		</sql>
	</changeSet>
</databaseChangeLog>