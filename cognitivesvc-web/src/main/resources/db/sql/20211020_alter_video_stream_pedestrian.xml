<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20211020_alter_video_stream_pedestrian" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_pedestrian" columnName="store_passer" />
			</not>
		</preConditions>
	
		<sql>
			ALTER TABLE `video_stream_pedestrian` ADD COLUMN `store_passer` bit(1) DEFAULT NULL COMMENT '0为不存陌生人 , 1或者null为存陌生人' AFTER `store_scene`;
		</sql>
	</changeSet>
</databaseChangeLog>