<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20220111_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="flock_config" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE `x_dynamic_model` MODIFY COLUMN `annotator_name` varchar(128)          NOT NULL COMMENT '算法名称' FIRST;
		    ALTER TABLE `x_dynamic_model` MODIFY COLUMN `attached`       varchar(4096) DEFAULT NULL COMMENT     '额外文件(optional)' AFTER `annotator_paths`;
			ALTER TABLE `x_dynamic_model` MODIFY COLUMN `additional`     varchar(2048) DEFAULT NULL COMMENT     '一些可选的额外信息用来描述算法行为' AFTER `monopolized_identity`;
			
			ALTER TABLE `x_dynamic_model` ADD COLUMN `flock_config` mediumblob  DEFAULT NULL COMMENT 'flock config'                AFTER `java_code`;
		</sql>
	</changeSet>
</databaseChangeLog>