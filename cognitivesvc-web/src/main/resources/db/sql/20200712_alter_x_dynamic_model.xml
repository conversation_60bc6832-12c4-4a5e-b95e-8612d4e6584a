<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20200712_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="required_identity" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE x_dynamic_model CHANGE COLUMN suggest_identity preferred_identity varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '建议部署的显卡' AFTER `batch_size`;
			ALTER TABLE x_dynamic_model ADD COLUMN required_identity  varchar(512) COLLATE utf8mb4_unicode_ci NULL COMMENT '固定部署的显卡' AFTER preferred_identity;
            ALTER TABLE x_dynamic_model CHANGE COLUMN antisuggest_identity rejected_identity varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拒绝部署的显卡' AFTER `required_identity`;
		</sql>
	</changeSet>
</databaseChangeLog>