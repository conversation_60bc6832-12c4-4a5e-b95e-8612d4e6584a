<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20221111_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
				<columnExists tableName="x_dynamic_model" columnName="annotator_paths" />
		</preConditions>
		
		<sql>
		    ALTER TABLE `x_dynamic_model` MODIFY COLUMN `annotator_paths` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '算法依赖的模型' AFTER `plugin_paths`;
		</sql>
	</changeSet>
</databaseChangeLog>