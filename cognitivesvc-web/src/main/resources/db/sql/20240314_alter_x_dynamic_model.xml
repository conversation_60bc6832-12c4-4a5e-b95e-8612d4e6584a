<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20240314_alter_x_dynamic_model.xml" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="x_dynamic_model" columnName="check_force" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE x_dynamic_model ADD COLUMN check_force int(11) DEFAULT  0 COMMENT '导入包是否强制校验';

		</sql>
	</changeSet>
</databaseChangeLog>