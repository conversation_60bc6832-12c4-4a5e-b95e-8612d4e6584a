<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20201218_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="x_dynamic_model" columnName="java_code" />
		</preConditions>
		
		<sql>
		    ALTER TABLE x_dynamic_model MODIFY COLUMN `java_code` mediumblob COMMENT '动态java代码' AFTER `java_class_name`;
		</sql>
	</changeSet>
</databaseChangeLog>