<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20210110_alter_video_stream_xswitcher" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="video_stream_xswitcher" columnName="processors" />
		</preConditions>
		
		<sql>
		   ALTER TABLE `video_stream_xswitcher` MODIFY COLUMN `processors` mediumblob NOT NULL COMMENT '处理器配置' AFTER `type`;
		</sql>
	</changeSet>
</databaseChangeLog>