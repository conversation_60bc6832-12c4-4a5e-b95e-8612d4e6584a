<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


	<changeSet id="20240327_update_cognitive_param" author="COG" runOnChange="true">
		<sql>

			<!--INSERT INTO cognitive_param VALUES ('imageSceneSave', 'false', '人脸存储大图,默认false不存') ON DUPLICATE KEY UPDATE s_value = 'false';!-->
			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'imageSceneSave', 'false', '人脸存储大图,默认false不存') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'imageSceneSave'
			) LIMIT 1;




		</sql>
	</changeSet>
</databaseChangeLog>