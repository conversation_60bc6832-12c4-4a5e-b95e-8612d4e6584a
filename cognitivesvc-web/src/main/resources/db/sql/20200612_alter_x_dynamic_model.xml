<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20200612_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="antisuggest_identity" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE x_dynamic_model ADD COLUMN antisuggest_identity  varchar(512)   COLLATE utf8mb4_unicode_ci NULL COMMENT '不允许部署的显卡,强制';
		</sql>
	</changeSet>
</databaseChangeLog>