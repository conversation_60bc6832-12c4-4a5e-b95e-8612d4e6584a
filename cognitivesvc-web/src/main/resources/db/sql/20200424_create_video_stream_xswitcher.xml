<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200420_create_video_stream_xswitcher" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="video_stream_xswitcher" />
			</not>
		</preConditions>
		
		<sql>
			CREATE TABLE video_stream_xswitcher (
				device_id        varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备id',
				type             int(10) NOT NULL DEFAULT '0' COMMENT '流类型: 0低频, 1高频CPU解码, 2高频GPU解码, 3高频GPU复用解码器, 4低频GPU复用解码器',
				processors       varchar(2048) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理器配置',
				PRIMARY KEY (device_id)
			) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		</sql>
	</changeSet>
</databaseChangeLog>