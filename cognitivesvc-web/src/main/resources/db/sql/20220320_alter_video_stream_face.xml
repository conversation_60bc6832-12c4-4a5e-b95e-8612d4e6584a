<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20220320_alter_video_stream_face" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_face" columnName="quick_response_time" />
			</not>
		</preConditions>
		
		<sql>			
		    ALTER TABLE `video_stream_face` ADD COLUMN `quick_response_time` int(11) DEFAULT NULL COMMENT '快速人脸识别响应时间'  AFTER `target_group`;
			ALTER TABLE `video_stream_face` ADD COLUMN `time_interval`       int(11) DEFAULT NULL COMMENT '选帧触发的时间间隔'   AFTER `quick_response_time`;
			ALTER TABLE `video_stream_face` ADD COLUMN `max_track_time`      int(11) DEFAULT NULL COMMENT '目标最大的跟踪时长'   AFTER `time_interval`;
			ALTER TABLE `video_stream_face` ADD COLUMN `max_tracklet_num`    int(11) DEFAULT NULL COMMENT '追踪目标上限(影响性能)'   AFTER `max_track_time`;
			ALTER TABLE `video_stream_face` ADD COLUMN `processors`          mediumblob DEFAULT NULL COMMENT '人脸告警-1持续告警' ;

		</sql>
	</changeSet>
</databaseChangeLog>