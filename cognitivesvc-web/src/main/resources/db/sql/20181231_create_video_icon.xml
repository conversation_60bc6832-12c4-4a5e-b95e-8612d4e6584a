<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20181231_create_video_icon" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="video_icon" />
			</not>
		</preConditions>
		
		<sql>
			CREATE TABLE video_icon (
				person_tag        varchar(255)  COLLATE utf8mb4_unicode_ci NOT NULL,
				device_id         varchar(32)   COLLATE utf8mb4_unicode_ci NOT NULL,
				rgb               varchar(16)   COLLATE utf8mb4_unicode_ci NOT NULL,
				text              varchar(16)   COLLATE utf8mb4_unicode_ci NOT NULL,
				font_size         smallint(4)   NULL,
				font_thick        smallint(4)   NULL,
				show_attr         smallint(4)   NULL,
				update_ts         timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP,
				icon_image_base64 Blob          NULL,
				PRIMARY KEY (person_tag,device_id)
			) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
			
					
		<!-- 	
			insert into video_icon values('default_person_icon',   '*', '255,0,0', 'VIP',     2, 2, '1',now(), '/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCAAWABoDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD8v/25vBNsdb8FfEXRdF0fQvDHxS8N2upWtlpFkLTT9PvbWJLO/tYkDNkpPD5pbjP2lc/NuJ779mH9mPxT48/4Jj/HvxfpXiC6tdKfUdOim0aFo/J1QaV/p1w0+5SyiKK7jlj2kbmRgc4FezfsBfswRf8ABQL/AIJs6l4Evp9O0vVPDnia6t/DOr3Fs07aU2La8kwFZTskNzOjLkjDhtrMi4+0/Af/AAT20z9k79gRvhDqV3Z6lda7bal/wkWq6dE8A1J7wyR+Yqsdyutp5EWe5hzX47nvHlHA4Z5c3atRrRi01e8IyUlL7klve59vhMklWqqsvhnBv5tWt95+Kf7Kv7Pup/tW/tH+DPhzpDCK98XapHYmcosgtIeXuLgoXTzFigWWUoHVmEZUHJr3T4z/APBQvwXcfGHxZJ4Z/Z1/Z3uPDcms3jaVLN4ZYSSWhnfyWby5VTcY9pO1QuegA4r6Z/ZF/Yzuf2EvgZ8ZfHMWrafrfja28JancaVexWZj/stLaynn2xsSWzJKqF+2IUAPUV+WFvCLeBI16IoUfgK+wy3McHxFi60oNulS5YxabV5PWT0tpsl8zycZha2AhFSXvSu2nrZdP1P1F/4N1PHF3qVn8QfDbE/YNJv7LUohn/lrdRTxOcduLOOv04/aqUrosR/6Yf0NFFfzv4kwUeKMWo94f+kRZ97w972Aot+f5nwd+2/4yv8AwH+wL8Tr7TmWO4ms4dNckkZiu7yG0lHHrHO9fjqrBlBAwDyB6UUV+yeDiX9k159XVf8A6RD/ADPmuMm/rkV/dX5s/9k=');
			insert into video_icon values('default_passer_icon',   '*', '0,0,255', 'WARNING', 2, 2, '1',now(), '/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCAAWABoDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDxv9iTwH4M1a90TXfHmkf21ZXj3L3Ngim6EUAWSJCsfRz5yqdxGQGIzwK/YT4K+FPCGreBGk0XQvDA0V3kCDT9ItoLRhwHGwRqQc7s7lGfXGK/Er4H+GvBHjvwBpPhTxD4m8ZeFNVsr6Zrf+wbITMItkknlpKCZNhMkjEFdu4EYzhq9b8Lfst+NvBcFloPgbxb4J1PwB4kfy9buNR1SUXOoCVzDcQSM0UToDCiAExHAAPzcV+f8SYOlikk5crj62Z7/D86lBWkubmd79V5HuH/AAUA+A3wn8cfEu6m8L6V4b0yF9LSH7ZokUdtEl/iTY6+UFWQBDHkEMpZcEErivzX/tPWf+fbSv8AwPb/AOKr6Ll+E3wx/Zv8YySyfEjx5reuWejPew2sFiL2xDCUsksc0vk5AKBTtQOGLgNgV8vf8K/j/wCfC8/8DF/+N1w5XQxUINU6jkvOL09Nz7PF1suqqKxEFCUV/Mrvzex6J4I1lPCfjS11KbTINajjV4zYS3TWschKN83mqjEEdeVZTjG0Eh1+gvhp+2jZzeGrrUtP8OXH9g6cJLS8kbVLiC8hfAmcwxh3UgRspDCSJt+R8uN7FFfSZjTjKhK6Pgcsk3VgzxP4v/F7SfjN4ng1TQ9DXRITbLamaWWSW6vIwzllcM7RohLYwql/kB8wBig5Lcf+ejfkKKK1y6nFUVZHTmVSVWq5Td2f/9k=');
			insert into video_icon values('default_stranger_icon', '*', '0,255,0', 'UNKNOWN', 2, 2, '1',now(), '/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCAAUABYDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD3b4w/8En9D8I/HnxHZaWfEskdl4itb62aPUYrfS7GzluYpTbXIkuGRzJG6xR/u7V83UpiNw8CRPx2n/8AByDrf7OfwY8N+G9Z8J6fd6R4fEnh+LxL4Xu2mmvTZyyW8afY7wOsTtDCG/eXL72WQlosbT92/wDBSPxJpnhoajZ6ZosZv71Yb3UU022hS88R3zobOzgmcod+EZU3Pny1dWBXygV/Hr9uf9gXxh8XviOPAvhiPwZpmuWfhaz8Q6No0Fimlw+Kp457lL20sruRirtYxSRMxnkEsguUeRljCld8Nga0L1p6p/Cr3W70s/l1d72SVkjhxGOoykqK923xPZ7LqtfwVrat3bP1U/YL/wCCyHhz9qfwtf61pGt3PimytAkd5YXVlHY6tosz8xpNGg27GVZMMpdGKkLISjgFfKX/AAbef8EfPGPwb8Y+OviP8XdJs9JTUdMj0LTNC+1QXsoJlWeSeZ4WeEMAkaqiuzASPu2nC0V1ueHf8aNpdbafgcrp4pP/AGed49G9X9/U7T/gsX8V7uL/AIKL+H9AutO0jU9D8IaPB4wt7C8haSC81NWFtbzzjcN32dZZHiVdoWUrIQzJGV4H4QfEi/8A20vjN8M7DxXHZ2dh4b8WRa5BBpsWwTXMMckMTO0pkcBBcOwCMmWC7iVypKK7sP8AwPu/Q4cSl9Z+/wDNn7R+GfDdj4Q0K203TLaOzsbNPLhiTOFGfU8kk5JJySSSSSaKKK+fu3qz6VJJWR//2Q==');
		-->
		</sql>
	</changeSet>
</databaseChangeLog>