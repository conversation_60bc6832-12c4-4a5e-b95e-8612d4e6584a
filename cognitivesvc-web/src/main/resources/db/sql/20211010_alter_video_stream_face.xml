<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20211010_alter_video_stream_face" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_face" columnName="store_passer" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE `video_stream_face` ADD COLUMN `store_passer` bit COMMENT '0为不存陌生人 , 1或者null为存陌生人'   AFTER `target_group`;
		</sql>
	</changeSet>
</databaseChangeLog>