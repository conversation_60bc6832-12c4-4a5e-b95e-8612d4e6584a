<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


	<changeSet id="20230707_update_cognitive_param" author="COG" runOnChange="true">
		<sql>

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'useSelfCalCapturedTime', 'false', '计算抓拍时间,false=系统时') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'useSelfCalCapturedTime'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'stabilityFlag', 'false', '稳定性默认false') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'stabilityFlag'
			) LIMIT 1;
		</sql>
	</changeSet>
</databaseChangeLog>