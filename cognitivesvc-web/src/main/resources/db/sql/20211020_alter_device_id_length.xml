<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20211020_alter_device_id_length" author="COG" runOnChange="true">
		<sql>
			ALTER TABLE `video_stream_face`       MODIFY COLUMN `device_id` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL FIRST;
			ALTER TABLE `video_stream_infra`      MODIFY COLUMN `device_id` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL FIRST;
			ALTER TABLE `video_stream_pedestrian` MODIFY COLUMN `device_id` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL FIRST;
			ALTER TABLE `video_stream_xswitcher`  MODIFY COLUMN `device_id` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备id' AFTER `id`;
			ALTER TABLE `stranger_face_feature`   MODIFY COLUMN `device_id` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL AFTER `group_key`;
		</sql>
	</changeSet>
</databaseChangeLog>