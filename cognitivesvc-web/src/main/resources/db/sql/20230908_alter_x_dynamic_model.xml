<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20230908_alter_x_dynamic_model.xml" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="x_dynamic_model" columnName="img_save_tag" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE x_dynamic_model ADD COLUMN img_save_tag varchar(2) default 0 COMMENT '存图开关 0存图 1不存图';
		</sql>
	</changeSet>
</databaseChangeLog>