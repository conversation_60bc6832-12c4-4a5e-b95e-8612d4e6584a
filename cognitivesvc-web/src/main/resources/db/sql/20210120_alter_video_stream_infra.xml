<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20210120_alter_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_infra" columnName="frame_buffer" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE `video_stream_infra` ADD COLUMN `frame_buffer` int(11) COMMENT '预留帧池大小' AFTER `frame_skip`;
		    ALTER TABLE `video_stream_infra` ADD COLUMN `frame_buffer_strategy` varchar(16) COMMENT '预留帧池策略' AFTER `frame_buffer`;
		</sql>
	</changeSet>
</databaseChangeLog>