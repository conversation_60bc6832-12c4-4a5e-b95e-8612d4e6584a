<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20230516_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="version" />
			</not>
			<and>
				<not>
					<columnExists tableName="x_dynamic_model" columnName="commit_id" />
				</not>
			</and>
		</preConditions>

		<sql>
			ALTER TABLE `x_dynamic_model` ADD COLUMN `version` varchar(128)          DEFAULT NULL COMMENT '版本' AFTER `annotator_name`;
			ALTER TABLE `x_dynamic_model` ADD COLUMN `commit_id` varchar(128)        DEFAULT NULL COMMENT 'commitId' AFTER `version`;
		</sql>
	</changeSet>
</databaseChangeLog>