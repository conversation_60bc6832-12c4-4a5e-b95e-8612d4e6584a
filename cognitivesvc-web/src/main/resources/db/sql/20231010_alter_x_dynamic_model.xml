<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20231010_alter_x_dynamic_model.xml" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
			<columnExists tableName="x_dynamic_model" columnName="fmd_paths" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE `x_dynamic_model` ADD COLUMN `fmd_paths` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '算法依赖的fmd' AFTER `attached`;
		</sql>
	</changeSet>
</databaseChangeLog>