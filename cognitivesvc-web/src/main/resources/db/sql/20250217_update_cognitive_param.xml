<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


	<changeSet id="20250217_update_cognitive_param" author="COG" runOnChange="true">
		<sql>

			<!--INSERT INTO cognitive_param VALUES ('reindexIdentifier', '', '人脸是否主动全量重建person索引的数据库锁，不为空则有pod在执行') ON DUPLICATE KEY UPDATE s_value = 'false';!-->
			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'reindexIdentifier', '', '人脸是否主动全量重建person索引的数据库锁，不为空则有pod在执行') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'reindexIdentifier'
			) LIMIT 1;



            INSERT INTO cognitive_param  (s_key, s_value, s_desc)
            SELECT * FROM (SELECT 'reindexFinishTime', '', '人脸是否主动全量重建person索引完成时间') AS tmp
            WHERE NOT EXISTS (
                SELECT s_key FROM cognitive_param WHERE s_key = 'reindexFinishTime'
            ) LIMIT 1;

		</sql>
	</changeSet>
</databaseChangeLog>