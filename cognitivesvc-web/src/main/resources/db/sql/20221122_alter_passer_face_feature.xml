<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20221122_alter_passer_face_feature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="passer_face_feature" columnName="image_quality" />
			</not>	
		</preConditions>
		
		<sql>
		    ALTER TABLE `passer_face_feature` ADD COLUMN `image_quality` float COMMENT '入库时候的人脸质量分' AFTER `privilege`;
		</sql>
	</changeSet>
</databaseChangeLog>