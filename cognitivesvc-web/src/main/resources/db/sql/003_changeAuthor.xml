<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.10.xsd">

    <changeSet id="003_changeAuthor" author="COG">

        <preConditions onFail="MARK_RAN">
            <tableExists tableName="DATABASECHANGELOG" />
        </preConditions>
        <!-- 添加主键约束 001 最先执行,需要保证ID不重复-->
        <sql>
            update DATABASECHANGELOG set AUTHOR = 'COG' where AUTHOR = 'FUCK';
        </sql>
    </changeSet>

</databaseChangeLog>