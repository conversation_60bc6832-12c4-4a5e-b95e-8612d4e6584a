<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20250428_alter_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_infra" columnName="processors" />
			</not>
		</preConditions>
		
		<sql>

			ALTER TABLE `video_stream_infra` ADD COLUMN `processors` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '存储JSON配置（最大8KB，字符集兼容特殊符号）';

		</sql>
	</changeSet>
</databaseChangeLog>