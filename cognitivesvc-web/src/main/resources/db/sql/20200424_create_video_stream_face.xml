<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200424_create_video_stream_face" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="video_stream_face" />
			</not>
		</preConditions>
		
		<sql>
			CREATE TABLE `video_stream_face` (
			  `device_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
			  `run_face_model` int(11) NOT NULL DEFAULT '1',
			  `run_face_attr_model` int(11) NOT NULL DEFAULT '1',
			  `base_threshold` float DEFAULT NULL,
			  `base_image_quality` float DEFAULT NULL,
			  `yaw` float DEFAULT NULL,
			  `pitch` float DEFAULT NULL,
			  `roll` float DEFAULT NULL,
			  `roi` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
			  `min_face_size` int(11) DEFAULT NULL,
			  `store_scene` int(11) DEFAULT NULL,
			  `target_group` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
			  PRIMARY KEY (`device_id`)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
		</sql>
	</changeSet>
</databaseChangeLog>