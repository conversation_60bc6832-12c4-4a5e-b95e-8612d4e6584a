<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20230206_alter_video_stream_face" author="COG" runOnChange="true">

		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_face" columnName="processors" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE `video_stream_face` ADD COLUMN `processors`  mediumblob DEFAULT NULL COMMENT '人脸告警-1持续告警' AFTER `max_tracklet_num`;
		</sql>
	</changeSet>
</databaseChangeLog>