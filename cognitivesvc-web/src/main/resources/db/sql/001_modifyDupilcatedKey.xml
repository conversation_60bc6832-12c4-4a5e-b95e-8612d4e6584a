<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.10.xsd">

    <changeSet id="001_modifyDupilcatedKey" author="COG">

        <preConditions onFail="MARK_RAN">
            <tableExists tableName="DATABASECHANGELOG" />
        </preConditions>
        <!-- 286 和之前版本id写错 需要纠正-->
        <sql>
            update DATABASECHANGELOG set ID = '20220627_alter_video_stream_infra' where FILENAME = 'db/sql/20220627_alter_video_stream_infra.xml'
        </sql>
    </changeSet>

</databaseChangeLog>