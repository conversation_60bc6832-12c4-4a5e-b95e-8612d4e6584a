<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20221111_alter_video_stream_face_ped" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_face" columnName="max_tracklet_num" />
				<columnExists tableName="video_stream_pedestrian" columnName="max_tracklet_num" />
			</not>
		</preConditions>
		
		<sql>			
		    ALTER TABLE `video_stream_face` ADD COLUMN `max_tracklet_num` int(11) COMMENT '追踪目标上限(影响性能)' AFTER `max_track_time`;
		    ALTER TABLE `video_stream_pedestrian` ADD COLUMN `max_tracklet_num` int(11) COMMENT '追踪目标上限(影响性能)' AFTER `max_track_time`;
		</sql>
	</changeSet>
</databaseChangeLog>