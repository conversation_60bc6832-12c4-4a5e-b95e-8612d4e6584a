<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20230830_alter_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="x_dynamic_model" columnName="max_stream_count" />
			</not>

		</preConditions>

		<sql>

			ALTER TABLE `x_dynamic_model` ADD COLUMN `max_stream_count` int(11)  not null  DEFAULT 0 COMMENT 'pod流限制' AFTER `model_cn_name`;

		</sql>
	</changeSet>
</databaseChangeLog>