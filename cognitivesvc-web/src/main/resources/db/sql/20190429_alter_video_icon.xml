<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20190429_alter_video_icon" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="video_icon" columnName="text" />
		</preConditions>
		
		<sql>
			ALTER TABLE video_icon MODIFY COLUMN text  varchar(128) NOT NULL;
		</sql>
	</changeSet>
</databaseChangeLog>