<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200326_alter_face_feature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="person_face_feature" columnName="uuid" />
		</preConditions>
		
		<sql>
			ALTER TABLE person_face_feature DROP COLUMN uuid, ADD COLUMN id int(0) UNSIGNED NOT NULL AUTO_INCREMENT FIRST, DROP PRIMARY KEY, ADD PRIMARY KEY (`id`) USING BTREE;
			ALTER TABLE passer_face_feature DROP COLUMN uuid, ADD COLUMN id int(0) UNSIGNED NOT NULL AUTO_INCREMENT FIRST, DROP PRIMARY KEY, ADD PRIMARY KEY (`id`, `group_id`) USING BTREE;
		</sql>
	</changeSet>
</databaseChangeLog>