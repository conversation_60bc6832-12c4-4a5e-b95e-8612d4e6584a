<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20201215_alter_video_stream_face" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_face" columnName="roi_ids" />
			</not>
		</preConditions>
		
		<sql>
			ALTER TABLE video_stream_face ADD COLUMN `roi_ids` varchar(255) COMMENT '热区的外部id' AFTER `roi`;
			ALTER TABLE video_stream_pedestrian ADD COLUMN `roi_ids` varchar(255) COMMENT '热区的外部id' AFTER `roi`;
		</sql>
	</changeSet>
</databaseChangeLog>