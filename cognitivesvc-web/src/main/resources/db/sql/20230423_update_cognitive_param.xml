<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20230423_update_cognitive_param" author="COG" runOnChange="true">
		<sql>

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'useSelfCalCapturedTime', 'false', '计算抓拍时间') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'useSelfCalCapturedTime'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'dropFaceQuality', '{"inteGrateQuality":0.01,"quality":0.01,"faceSizeLimit":80,"yaw":0,"pitch":0,"roll":0}', 'dropfaceFilter') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'dropFaceQuality'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'dropFaceFlag', '1', '1关0开，filterPerson') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'dropFaceFlag'
			) LIMIT 1;

			INSERT INTO cognitive_param  (s_key, s_value, s_desc)
			SELECT * FROM (SELECT 'videoSaveImageWay', '0', '0 for kestrel') AS tmp
			WHERE NOT EXISTS (
				SELECT s_key FROM cognitive_param WHERE s_key = 'videoSaveImageWay'
			) LIMIT 1;
		</sql>
	</changeSet>
</databaseChangeLog>