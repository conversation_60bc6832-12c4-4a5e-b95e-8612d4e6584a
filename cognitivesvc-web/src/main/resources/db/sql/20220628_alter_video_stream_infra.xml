<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="20220628_alter_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="video_stream_infra" columnName="rtsp_mapped_source" />
			</not>
		</preConditions>
		
		<sql>
		    ALTER TABLE `video_stream_infra` ADD COLUMN `rtsp_mapped_source` varchar(256) COMMENT '映射成内部rtsp流' AFTER `rtsp_source`;
		</sql>
	</changeSet>
</databaseChangeLog>