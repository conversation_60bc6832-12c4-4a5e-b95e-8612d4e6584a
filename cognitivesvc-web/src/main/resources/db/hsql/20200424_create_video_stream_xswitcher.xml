<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200420_create_video_stream_xswitcher" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="video_stream_xswitcher" />
			</not>
		</preConditions>
		
		<sql>
			CREATE TABLE video_stream_xswitcher (
			  id int(10) NOT NULL AUTO_INCREMENT,
			  device_id varchar(96) NOT NULL ,
			  type int(10) NOT NULL DEFAULT '0' ,
			  processors text NOT NULL ,
			  dispatch_desc varchar(128) DEFAULT NULL ,
			  PRIMARY KEY (id)
			);
		</sql>
	</changeSet>
</databaseChangeLog>