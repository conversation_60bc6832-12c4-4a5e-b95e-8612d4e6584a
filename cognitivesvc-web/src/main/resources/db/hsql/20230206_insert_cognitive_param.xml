<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20230206_insert_cognitive_param" author="COG" runOnChange="true">
		<sql>

			INSERT INTO cognitive_param
			VALUES ('shieldFace_switch', '0', '0 aligner逻辑') ON DUPLICATE KEY
			UPDATE s_value = '0';

			INSERT INTO cognitive_param
			VALUES ('imageCapture_switch', '0', '1不发送cap message') ON DUPLICATE KEY
			UPDATE s_value = '0';

			INSERT INTO cognitive_param
			VALUES ('faceRecognitionMode', '1', '0精准模式,1快速模式') ON DUPLICATE KEY
			UPDATE s_value = '1';

			INSERT INTO cognitive_param
			VALUES ('selectFrameTimeInterVal', '10', '默认10s') ON DUPLICATE KEY
			UPDATE s_value = '10';

			INSERT INTO cognitive_param
			VALUES ('strangerRecognition', '1', '陌生人识别开关0-开，1-关') ON DUPLICATE KEY
			UPDATE s_value = '1';

			INSERT INTO cognitive_param
			VALUES ('seekType', '1', '特征对比库类型，可以使0(java比对), 1(faiss暴力比对), 2(faiss倒排索引比对)') ON DUPLICATE KEY
			UPDATE s_value = '1';

		</sql>
	</changeSet>
</databaseChangeLog>