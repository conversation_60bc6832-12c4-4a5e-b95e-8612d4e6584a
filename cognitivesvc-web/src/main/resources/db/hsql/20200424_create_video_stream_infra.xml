<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200424_create_video_stream_infra" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="video_stream_infra" />
			</not>
		</preConditions>
		
		<sql>
			CREATE TABLE video_stream_infra(
			  device_id             varchar(96) NOT NULL,
			  device_tag            varchar(64) DEFAULT NULL,
			  rtsp_source           varchar(256) NOT NULL,
			  rtsp_mapped_source    varchar(256) NOT NULL,
			  rtsp_width            int(11) NOT NULL,
			  rtsp_height           int(11) NOT NULL,
			  video_rate            int(11) NOT NULL,
			  decoder_format        varchar(32) DEFAULT NULL,
			  frame_max             int(11) NULL,
			  frame_skip            int(11) NOT NULL DEFAULT '3',
			  frame_buffer          int(11) DEFAULT NULL,
			  frame_buffer_strategy varchar(16) DEFAULT NULL,
			  rtmp_on               varchar(8) DEFAULT NULL,
			  rtmp_destination      varchar(128) DEFAULT NULL,
			  rtmp_option           varchar(256) NOT NULL DEFAULT 'tune:zerolatency,preset:ultrafast',
			  sts                   int(11) NOT NULL DEFAULT '0',
			  seed                  varchar(64) DEFAULT '',
			  update_ts             datetime DEFAULT CURRENT_TIMESTAMP,
			  keep_alive            datetime DEFAULT NULL,
			  privilege             varchar(1024) DEFAULT '',
			  PRIMARY KEY (device_id)
			);
		</sql>
	</changeSet>
</databaseChangeLog>