<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200826_create_face_pedestrian_cluster.xml" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="face_pedestrian_cluster" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE face_pedestrian_cluster (
			    id int(10) NOT NULL AUTO_INCREMENT,
			    face_person_id varchar(36) NOT NULL ,
			    face_person_type int(10) NOT NULL ,
			    pedestrian_person_id varchar(36) NOT NULL ,
			    pedestrian_person_type int(10) NOT NULL ,
			    PRIMARY KEY (id)
			);
			
		</sql>
	</changeSet>
</databaseChangeLog>