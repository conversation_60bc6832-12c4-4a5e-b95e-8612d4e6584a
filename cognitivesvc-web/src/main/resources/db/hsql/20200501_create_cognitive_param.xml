<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200501_create_cognitive_param" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="cognitive_param" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE if not exists cognitive_param (
				s_key   varchar(64) NOT NULL,
				s_value varchar(64) NOT NULL,
				s_desc  varchar(256) NOT NULL,
				PRIMARY KEY (s_key)
			);
			
			INSERT INTO cognitive_param VALUES ('decoderCpuCount', '0', '可支持路数中的cpu的路数的count') ON DUPLICATE KEY UPDATE s_value = '0';
			
			INSERT INTO cognitive_param VALUES ('faceSizeLimit', '20', '视频流中低于此像素数的脸被丢弃') ON DUPLICATE KEY UPDATE s_value = '20';
			INSERT INTO cognitive_param VALUES ('nonseenGpuMemLimit', '0.95', '开启新的非渲染流的最小显存') ON DUPLICATE KEY UPDATE s_value = '0.95';
			INSERT INTO cognitive_param VALUES ('seenGpuMemLimit', '0.90', '开启新渲染流的最小显存') ON DUPLICATE KEY UPDATE s_value = '0.90';
			INSERT INTO cognitive_param VALUES ('unknownRegTimeCount', '5', '视频流中最大识别次数') ON DUPLICATE KEY UPDATE s_value = 5;
			INSERT INTO cognitive_param VALUES ('logged', 'false', '测试用，用来将senseye消息打印到日志') ON DUPLICATE KEY UPDATE s_value = 'false';
			
			INSERT INTO cognitive_param VALUES ('deviceShowupNum', '1', '陌生人的脸至少出现在的设备数量') ON DUPLICATE KEY UPDATE s_value = '1';
			INSERT INTO cognitive_param VALUES ('period', '120', '陌生人归档最大周期') ON DUPLICATE KEY UPDATE s_value = '120';
			INSERT INTO cognitive_param VALUES ('periodShowupNum', '2', '陌生人的脸至少出现在的时间窗口数量') ON DUPLICATE KEY UPDATE s_value = '2';
			INSERT INTO cognitive_param VALUES ('rollingWindow', '1', '时间窗口') ON DUPLICATE KEY UPDATE s_value = '1';
			INSERT INTO cognitive_param VALUES ('runningType', 'just', '陌生人归档的方式') ON DUPLICATE KEY UPDATE s_value = 'just';
			INSERT INTO cognitive_param VALUES ('imagesSaveExt', 'jpg', '视频流保存脸图和大图的保存格式') ON DUPLICATE KEY UPDATE s_value = 'jpg';
			INSERT INTO cognitive_param VALUES ('seekSplit', '0', '0(默认)每个cog全量load特征, 负数是根据eureka里面的cog数量平均load特征，正数是固定切成正数个份由各个cog抢夺load') ON DUPLICATE KEY UPDATE s_value = '0';

			INSERT INTO cognitive_param VALUES ('imwriteFlags', '1,95', 'cog视频流保存图片的opencv的imwirte的参数,详细配置去网上找') ON DUPLICATE KEY UPDATE s_value = '1,95';
			INSERT INTO cognitive_param VALUES ('rebalanceOn', 'true', '默认开启，用来达到cog之间接入流数量均衡，关闭之后cog会尽可能多的接入流，而即使各个cog接入流数量不均衡，也不会主动互换') ON DUPLICATE KEY UPDATE s_value = 'true';
			INSERT INTO cognitive_param VALUES ('lockVideoExpire', '120', '（秒）视频流心跳超时时间，如果表内updatets在超时时间内没有更新，会被其他cog抢占') ON DUPLICATE KEY UPDATE s_value = '120';
			INSERT INTO cognitive_param VALUES ('keepAliveExpire', '60', '（秒）渲染流keepalive超时时间，如果超过60秒没有页面发送keepalive，则将其转化为后台流') ON DUPLICATE KEY UPDATE s_value = '60';
			INSERT INTO cognitive_param VALUES ('attributeAggressive', '0.0', '更加激进的显示情绪') ON DUPLICATE KEY UPDATE s_value = '0.0';
			INSERT INTO cognitive_param VALUES ('faceRawImageNeedAttribute', 'false', 'dlc发来的脸是否提取属性') ON DUPLICATE KEY UPDATE s_value = 'false';
			
			INSERT INTO cognitive_param VALUES ('useTypeAttribute', '0', '0(classify-默认), 1(mtnet)') ON DUPLICATE KEY UPDATE s_value = '0';
			INSERT INTO cognitive_param VALUES ('useTypeHunter',    '1', '1(Large检测不成功返回空-默认),2(Common检测不成功返回空),3(Small检测不成功返回空),4(Accurate极慢检测不成功返回空),0(不检测直接返回框),负数(检测为空时返回整张框)') ON DUPLICATE KEY UPDATE s_value = '1';
			INSERT INTO cognitive_param VALUES ('retrieveType',     '0', '0（默认）入库提特征必须成功，1入库提特征失败保存假特征，多张脸用最大的特征') ON DUPLICATE KEY UPDATE s_value = '0';
			INSERT INTO cognitive_param VALUES ('nonSeenAttributeEverySecond', 'true', '非渲染流是否每秒抓取人脸属性并上报') ON DUPLICATE KEY UPDATE s_value = 'false';
			INSERT INTO cognitive_param VALUES ('seenMaxCount', '3', '最大渲染流数量') ON DUPLICATE KEY UPDATE s_value = '3';
			INSERT INTO cognitive_param VALUES ('nonSeenAttributeBeginEnd', 'true', '非渲染流是否在imagecapture和paterndetect中获取attr') ON DUPLICATE KEY UPDATE s_value = 'true';
			INSERT INTO cognitive_param VALUES ('seenAttributeEverySecond', 'true', '渲染流是否每秒抓取人脸属性并上报') ON DUPLICATE KEY UPDATE s_value = 'true';
			
			INSERT INTO cognitive_param VALUES ('xWorkerHighRateMaxCount', '', '（空：32）') ON DUPLICATE KEY UPDATE s_value = '';
			INSERT INTO cognitive_param VALUES ('nonSeenMaxCount', '', '（空：cog根据显卡使用建议值）最大非渲染流数量(T4建议为32, P4建议为16)') ON DUPLICATE KEY UPDATE s_value = '';
			INSERT INTO cognitive_param VALUES ('nonSeenQueueSize', '', '（空：cog根据显卡使用建议值）非渲染流最大缓冲帧数 适度降低可以减少显卡压力而引起丢帧(T4建议为768, P4建议为256)') ON DUPLICATE KEY UPDATE s_value = '';
			INSERT INTO cognitive_param VALUES ('nonSeenReusedFrameCount', '', '（空：cog根据显卡使用建议值）每一路视频都预留若干帧的显存用来提升性能(T4建议为24, P4建议为16)') ON DUPLICATE KEY UPDATE s_value = '';
			
			INSERT INTO cognitive_param VALUES ('imageQuality', '0.3', '视频流检测图片质量的最低值') ON DUPLICATE KEY UPDATE s_value = '0.3';
			INSERT INTO cognitive_param VALUES ('InspectThreshold', '0.55', '入门阈值') ON DUPLICATE KEY UPDATE s_value = '0.55';
			INSERT INTO cognitive_param
			VALUES ('Lv0Threshold', '0.60', '阈值0') ON DUPLICATE KEY
			UPDATE s_value = '0.60';
			INSERT INTO cognitive_param
			VALUES ('Lv1Threshold', '0.70', '阈值1') ON DUPLICATE KEY
			UPDATE s_value = '0.65';
			INSERT INTO cognitive_param
			VALUES ('Lv2Threshold', '0.80', '阈值2') ON DUPLICATE KEY
			UPDATE s_value = '0.70';
			INSERT INTO cognitive_param
			VALUES ('Lv3Threshold', '0.85', '阈值3') ON DUPLICATE KEY
			UPDATE s_value = '0.80';

			INSERT INTO cognitive_param
			VALUES ('kestrelLogLevel', '4', '日志级别') ON DUPLICATE KEY
			UPDATE s_value = '4';
			INSERT INTO cognitive_param
			VALUES ('senseyexOriginalMessage', 'false', 'senseyex消息是否发送kestrel原始数据') ON DUPLICATE KEY
			UPDATE s_value = 'false';
			INSERT INTO cognitive_param
			VALUES ('senseyexVideoCreateAffinity', 'false',
					'senseyex高频流建立后，是否对该流的processor建立亲和性组') ON DUPLICATE KEY
			UPDATE s_value = 'true';

			INSERT INTO cognitive_param
			VALUES ('watchFrameTiktokLevel', '0', '测试用 打各种流程得型耗时') ON DUPLICATE KEY
			UPDATE s_value = '0';

			INSERT INTO cognitive_param
			VALUES ('shieldFace_switch', '0', '0 aligner逻辑') ON DUPLICATE KEY
			UPDATE s_value = '0';
			INSERT INTO cognitive_param
			VALUES ('imageCapture_switch', '0', '1不发送cap message') ON DUPLICATE KEY
			UPDATE s_value = '0';

			INSERT INTO cognitive_param
			VALUES ('faceRecognitionMode', '0', '0精准模式,1快速模式') ON DUPLICATE KEY
			UPDATE s_value = '0';

			INSERT INTO cognitive_param
			VALUES ('maxTrackingTime', '20', '默认20s') ON DUPLICATE KEY
			UPDATE s_value = '20';

		</sql>
	</changeSet>
</databaseChangeLog>