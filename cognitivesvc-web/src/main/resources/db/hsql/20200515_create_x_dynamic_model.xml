<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200515_create_x_dynamic_model" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="x_dynamic_model" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE x_dynamic_model (
			  annotator_name varchar(128) NOT NULL,
			  plugin_paths varchar(2048) NOT NULL,
			  annotator_paths varchar(4096) NOT NULL,
			  attached varchar(8192) DEFAULT NULL ,
			  sts int(11) NOT NULL DEFAULT '0' ,
			  update_ts datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
			  java_class_name varchar(64) DEFAULT NULL ,
			  java_code text DEFAULT NULL,
			  flock_config text DEFAULT NULL,
			  cpu_model_dup int(11) NOT NULL DEFAULT '0' ,
			  estimate_gpu_memory int(11) NOT NULL DEFAULT '1000' ,
			  estimate_count int(11) NOT NULL DEFAULT '1' ,
			  batch_size int(11) NOT NULL DEFAULT '0' ,
			  preferred_identity varchar(512) DEFAULT NULL ,
			  required_identity varchar(512) DEFAULT NULL ,
			  rejected_identity varchar(512) DEFAULT NULL ,
			  affinity_group varchar(64) DEFAULT NULL ,
			  monopolized_group varchar(16) DEFAULT NULL,
			  monopolized_identity varchar(128) DEFAULT NULL,
			  additional varchar(2048) DEFAULT NULL ,
			  runtime varchar(512) DEFAULT 'waiting to initialize' ,
			  PRIMARY KEY (annotator_name)
			);
		</sql>
	</changeSet>
</databaseChangeLog>