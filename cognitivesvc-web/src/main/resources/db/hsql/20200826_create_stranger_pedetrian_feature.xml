<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200826_create_stranger_pedestrian_feature.xml" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="stranger_pedestrian_feature" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE stranger_pedestrian_feature (
			  id int(20) NOT NULL AUTO_INCREMENT,
			  avatar_image_url varchar(256) NOT NULL,
			  image_feature text NOT NULL,
			  attribute text ,
			  create_ts timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
			  group_key varchar(20) DEFAULT NULL,
			  device_id varchar(20) DEFAULT NULL,
			  PRIMARY KEY (id)
			);
		</sql>
	</changeSet>
</databaseChangeLog>