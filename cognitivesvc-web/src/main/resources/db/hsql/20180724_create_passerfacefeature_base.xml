<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20180724_passerfacefeature" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="passer_face_feature" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE passer_face_feature (
			  id int(10) NOT NULL AUTO_INCREMENT,
			  uuid varchar(36) NOT NULL,
			  avatar_image_url varchar(512) NOT NULL,
			  image_feature text NOT NULL,
			  image_quality float NULL,
			  person_uuid varchar(36) NOT NULL,
			  group_id varchar(36) NOT NULL,
			  track_id varchar(36) NOT NULL,
			  sts smallint(4) NOT NULL,
			  create_ts timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
			  last_mod_ts timestamp NULL DEFAULT CURRENT_TIMESTAMP,
			  privilege varchar(128) DEFAULT '',
			  PRIMARY KEY (id)
			) ;
		</sql>
	</changeSet>
</databaseChangeLog>