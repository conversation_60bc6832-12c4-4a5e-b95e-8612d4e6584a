<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<property name="now" value="now()" dbms="mysql" />

	<changeSet id="20200902_create_video_stream_pedestrian" author="COG" runOnChange="true">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="video_stream_pedestrian" />
			</not>
		</preConditions>
		<sql>
			CREATE TABLE video_stream_pedestrian (
			  device_id varchar(96) NOT NULL,
			  quality_threshold float DEFAULT NULL ,
			  face_feature_threshold float DEFAULT NULL ,
			  body_feature_threshold float DEFAULT NULL ,
			  seek_num  int(11) DEFAULT 1,
			  refinement bit(1) DEFAULT NULL,
			  yaw float DEFAULT NULL ,
			  pitch float DEFAULT NULL ,
			  roll float DEFAULT NULL ,
			  min_face_size int(11) DEFAULT NULL ,
			  min_body_size int(11) DEFAULT NULL,
			  roi varchar(512) DEFAULT NULL ,
			  roi_ids varchar(255) DEFAULT NULL ,
			  store_scene bit(1) DEFAULT NULL ,
			  store_passer bit(1) DEFAULT NULL,
			  target_group varchar(128) DEFAULT NULL,
			  quick_response_time int(11) DEFAULT 1,
			  time_interval  int(11) DEFAULT -1,
			  max_track_time int(11) DEFAULT -1,
			  PRIMARY KEY (device_id)
			);
		</sql>
	</changeSet>
</databaseChangeLog>