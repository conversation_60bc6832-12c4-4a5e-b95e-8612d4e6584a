###################  kafka-steam配置  ###################
spring.cloud.stream:
  binders:
    _kafka:
      type: kafka
      environment:
        #  org.springframework.cloud.stream.binder.kafka.properties.KafkaBinderConfigurationProperties
        #  https://docs.spring.io/spring-cloud-stream/docs/Ditmars.SR4/reference/htmlsingle/
        spring.cloud.stream.kafka.binder:
          brokers: ${KAFKA_ADDR:kafka-external:9092}
          zkNodes: ${ZOOKEEPER_ADDR:zk-external:2181}
          autoCreateTopics: true
          autoAddPartitions: true
          minPartitionCount: 256
          replicationFactor: 1
          configuration:
            security.protocol: ${SECURITY_PROTOCOL:SASL_SSL}
            sasl.mechanism: ${SASL_MECHANISM:SCRAM-SHA-512}
            sasl.jaas.config: org.apache.kafka.common.security.scram.ScramLoginModule required username="${KAFKA_USER:admin}" password="${KAFKA_PASS:nJFyptzMd1tV}";
            ssl.truststore.location: ${KAFKA_SSL_TRUSTSTORE_LOCATION:/certificates/client.truststore.jks}
            ssl.truststore.password:  ${KAFKA_TLS_PASS:ENC(35dcBRBhxkU13py3XN+FSIimI4yO6ImF)}
            ssl.keystore.location: ${KAFKA_SSL_KEYSTORE_LOCATION:/certificates/client.keystore.jks}
            ssl.keystore.password:  ${KAFKA_TLS_PASS:ENC(35dcBRBhxkU13py3XN+FSIimI4yO6ImF)}
            ssl.endpoint.identification.algorithm: