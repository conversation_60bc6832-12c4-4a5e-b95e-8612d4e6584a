###################  kafka-steam配置  ###################
spring.cloud.stream:
  binders:
    _kafka:
      type: kafka
      environment:
        #  org.springframework.cloud.stream.binder.kafka.properties.KafkaBinderConfigurationProperties
        #  https://docs.spring.io/spring-cloud-stream/docs/Ditmars.SR4/reference/htmlsingle/
        spring.cloud.stream.kafka.binder:
          brokers: ${KAFKA_ADDR:kafka-external:9092}
          zkNodes: ${ZOOKEEPER_ADDR:zk-external:2181}
          autoCreateTopics: true
          autoAddPartitions: true
          minPartitionCount: 256
          replicationFactor: 1
          configuration:
            security.protocol: ${SECURITY_PROTOCOL:SASL_SSL}
            sasl.mechanism: ${SASL_MECHANISM:SCRAM-SHA-512}
            sasl.jaas.config: >
              ${SASL_CONFIG_LOGIN_MODULE:org.apache.kafka.common.security.plain.PlainLoginModule required}
              username="${KAFKA_USER:$ConnectionString}"
              password="${KAFKA_CLOUD_PASS}";
            ssl.endpoint.identification.algorithm: