package com.sensetime.intersense.cognitivesvc;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import lombok.Builder;

/**
 * @Author: Cirmons
 * @Date: 2023-10-12
 */
public  class FacePipelineOutputTest {
    
    public static final String stageOne = "video_face_track_stream";
    
    public static final String stageTwo = "video_face_analyze_stream";
    
    public Long contextId;
    
    @Builder.Default
    public int video_width = 1920;
    
    @Builder.Default
    public int video_height = 1080;
    
    @Builder.Default
    public float expand_ratio = 1f;
    
    @Builder.Default
    public int max_tracklet_num = 16;
    
    @Builder.Default
    public int max_tracklet_item_size = 1;
    
    @Builder.Default
    public int duplicate_targets_time = 1;
    
    @Builder.Default
    public float quality_thresh = 0.3f;
    
    @Builder.Default
    public float large_quality_thresh = 0.4f;
    
    @Builder.Default
    public float small_quality_thresh = 0.3f;
    
    @Builder.Default
    public float pageant_quality_thresh = 0f;
    
    @Builder.Default
    public float ingrate_quality_thresh = 0f;
    
    @Builder.Default
    public int quick_response_time = -1;
    
    @Builder.Default
    public String roi_filter =   "[]";
    
    @Builder.Default
    public int time_interval = -1;
    
    @Builder.Default
    public int max_track_time = -1;
    
    private String contextIdString() {
        if(contextId == null)
            return "";
        
        return "\"source_id\": " + contextId + ", \"context_id\": " + contextId + ",";
    }
    
    @Override
    public String toString() {
        return "{" +
                "    \"streams\": [" +
                "        {" +
                "            \"name\": \"" + stageOne + "\"," + contextIdString() +
                "            \"module_plugins\": [" +
                "                \"detection.fmd\"," +
                "                \"multiple_target_tracking.fmd\"," +
                "                \"plugin.fmd\"," +
                "                \"roi_expand.fmd\"," +
                "                \"operation.fmd\"," +
                "                \"mergence.fmd\"," +
                "                \"track_recall.fmd\"" +
                "            ]," +
                "            \"modules\": [" +
                "                {" +
                "                    \"name\": \"input\"," + contextIdString() +
                "                    \"type\": \"Input\"," +
                "                    \"parallel_group\": \"input\"," +
                "                    \"inputs\": []," +
                "                    \"outputs\": [\"images\"]" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_detector\"," + contextIdString() +
                "                    \"type\": \"Detection\"," +
                "                    \"parallel_group\": \"detect\"," +
                "                    \"inputs\": [\"images\"]," +
                "                    \"outputs\": [\"detected_faces_small\"]," +
                "                    \"config\": {" +
                "                        \"plugin\": \"hunter\"," +
                "                        \"model\": \"hunter_small_module\"," +
                "                        \"max_batch_size\": 48," +
                "                        \"confidence_threshold\": " + small_quality_thresh  + "," +
                "                        \"model_key\": \"small_face_detection\"" +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_tracker\"," + contextIdString() +
                "                    \"type\": \"MultipleTargetTracking\"," +
                "                    \"parallel_group\": \"target_tracking\"," +
                "                    \"inputs\": [" +
                "                        \"detected_faces_small\"" +
                "                    ]," +
                "                    \"outputs\": [" +
                "                        \"tracked_faces_small\"," +
                "                        \"dropped_ids\"" +
                "                    ]" +
                "                }," +
                "                {" +
                "                    \"name\": \"roi_expander\"," + contextIdString() +
                "                    \"type\": \"RoiExpand\"," +
                "                    \"parallel_group\": \"detect\"," +
                "                    \"inputs\": [\"tracked_faces_small\"]," +
                "                    \"outputs\": [\"tracked_faces_expanded\"]," +
                "                    \"config\": {" +
                "                        \"roi_expand_ratio\": 1" +
                "                    }" +
                "                }," +
                
                "                {" +
                "                    \"name\": \"face_detector_large\"," + contextIdString() +
                "                    \"type\": \"DetectionInROI\"," +
                "                    \"parallel_group\": \"detect\"," +
                "                    \"inputs\": [\"tracked_faces_expanded\"]," +
                "                    \"outputs\": [\"tracked_faces_filtered\"]," +
                "                    \"config\": {" +
                "                        \"plugin\": \"hunter\"," +
                "                        \"model\": \"hunter_large_module\"," +
                "                        \"max_batch_size\": 48," +
                "                        \"confidence_threshold\": " + large_quality_thresh +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"track_recall\"," + contextIdString() +
                "                    \"type\": \"TrackRecall\"," +
                "                    \"parallel_group\": \"detect\"," +
                "                    \"inputs\": [\"tracked_faces_filtered\"]," +
                "                    \"outputs\": [\"tracked_faces\"]" +
                "                }," +
                "                {" +
                "                    \"name\": \"confidence_to_quality\"," + contextIdString() +
                "                    \"type\": \"Operation\"," +
                "                    \"parallel_group\": \"target_tracking\"," +
                "                    \"inputs\": [\"tracked_faces\"]," +
                "                    \"outputs\": [\"tracked_faces\"]," +
                "                    \"config\": {" +
                "                        \"operations\": [" +
                "                            {" +
                "                                \"cmd\": \"copy\"," +
                "                                \"args\": [" +
                "                                    \"confidence\"," +
                "                                    \"quality\"" +
                "                                ]" +
                "                            }" +
                "                        ]" +
                "                    }" +
                "                }," +
                
                "                {" +
                "                    \"name\": \"aligner\"," + contextIdString() +
                "                    \"type\": \"Plugin\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [\"tracked_faces\"]," +
                "                    \"outputs\": [\"face_landmarks\"]," +
                "                    \"config\": {" +
                "                        \"plugin\": \"aligner\"," +
                "                        \"model\": \"aligner_106_module\"," +
                "                        \"max_batch_size\": 64," +
                "                        \"model_key\": \"face_aligner_with_occlusion\"" +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_headpose\"," + contextIdString() +
                "                    \"type\": \"Plugin\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [\"face_landmarks\"]," +
                "                    \"outputs\": [\"face_headpose\"]," +
                "                    \"config\": {" +
                "                        \"plugin\": \"headpose\"," +
                "                        \"model\": \"headpose_module\"," +
                "                        \"max_batch_size\": 16," +
                "                        \"model_key\": \"face_headpose\"" +
                "                    }" +
                "                }," +
                
                "                {" +
                "                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
                "                    \"type\": \"Mergence\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [" +
                "                        \"tracked_faces\"," +
                "                        \"face_headpose\"" +
                "                    ]," +
                "                    \"outputs\": [" +
                "                        \"tracked_faces_headpose\"" +
                "                    ]" +
                "                }," +
                
                "                {" +
                "                    \"name\": \"output\"," + contextIdString() +
                "                    \"type\": \"Output\"," +
                "                    \"parallel_group\": \"output\"," +
                "                    \"inputs\": [" +
                "                        \"tracked_faces_headpose\"," +
                "                        \"dropped_ids\"" +
                "                    ]," +
                "                    \"outputs\": []" +
                "                }" +
                "            ]" +
                "        }," +
                "        {" +
                "            \"name\": \"" + stageTwo + "\"," + contextIdString() +
                "            \"module_plugins\": [" +
                "                \"plugin.fmd\"," +
                "                \"mergence.fmd\"," +
                "                \"target_selection.fmd\"," +
                "                \"refinement.fmd\"," +
                "                \"roi_filter.fmd\"," +
                "                \"operation.fmd\"," +
                "                \"face_quality.fmd\"," +
                "                \"face_quality_filter.fmd\"," +
                "                \"duplicated_targets_filter.fmd\"," +
                "                \"concat.fmd\"," +
                "                \"lightweight_targets_slice.fmd\"," +
                "                \"face_quality_evaluator.fmd\"," +
                "                \"multidim_face_quality.fmd\"" +
                "            ]," +
                "            \"modules\": [" +
                "                {" +
                "                    \"name\": \"input\"," + contextIdString() +
                "                    \"type\": \"Input\"," +
                "                    \"parallel_group\": \"input\"," +
                "                    \"inputs\": []," +
                "                    \"outputs\": [" +
                "                        \"face_targets_with_quality\"," +
                "                        \"dropped_ids\"" +
                "                    ]" +
                "                }," +
                "                {" +
                "                    \"name\": \"roi_filter\"," + contextIdString() +
                "                    \"type\": \"RoiFilter\"," +
                "                    \"parallel_group\": \"target_select\"," +
                "                    \"inputs\": [\"face_targets_with_quality\"]," +
                "                    \"outputs\": [\"filtered_face_targets\"]," +
                "                    \"config\": {" +
                "                        \"roi_filter\": [" +
                "                            {" +
                "                                \"label_id\": 37017," +
                "                                \"polygons\": " + roi_filter  +
                "                            }" +
                "                        ]" +
                "                    }" +
                "                }," +
                
                "                {" +
                "                    \"name\": \"face_selector\"," + contextIdString() +
                "                    \"type\": \"TargetSelection\"," +
                "                    \"parallel_group\": \"target_select\"," +
                "                    \"inputs\": [" +
                "                        \"filtered_face_targets\"," +
                "                        \"dropped_ids\"" +
                "                    ]," +
                "                    \"outputs\": [\"selected_faces\"]," +
                "                    \"config\": {" +
                "                        \"max_device_memory_usage_per_source\": 128," +
                "                        \"max_roi_ref_frame_size\": 512," +
                "                        \"max_source_tracklet_num\": " + max_tracklet_num + "," +
                "                        \"keep_low_quality_target\": true," +
                "                        \"selection\": [" +
                "                            {" +
                "                                \"label_id\": "               + "37017" + "," +
                "                                \"quick_response_time\": "    + quick_response_time + "," +
                "                                \"time_interval\": "          + time_interval + "," +
                "                                \"max_track_time\": "         + max_track_time + "," +
                "                                \"roi_expand_ratio\": "       + expand_ratio + "," +
                "                                \"quality_threshold\": "      + pageant_quality_thresh + "," +
                "                                \"max_tracklet_num\": "       + 512 + "," +
                "                                \"max_tracklet_item_size\": " + max_tracklet_item_size +
                "                            }" +
                "                        ]" +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"aligner\"," + contextIdString() +
                "                    \"type\": \"Plugin\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [\"selected_faces\"]," +
                "                    \"outputs\": [\"face_landmarks\"]," +
                "                    \"config\": {" +
                "                        \"plugin\": \"aligner\"," +
                "                        \"model\": \"aligner_106_module\"," +
                "                        \"max_batch_size\": 64," +
                "                        \"model_key\": \"face_aligner_with_occlusion\"" +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_landmarks_pack\"," + contextIdString() +
                "                    \"type\": \"Operation\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [\"face_landmarks\"]," +
                "                    \"outputs\": [\"face_landmarks\"]," +
                "                    \"config\": {" +
                "                        \"operations\": [" +
                "                            {" +
                "                                \"cmd\": \"move\"," +
                "                                \"args\": [" +
                "                                    \"confidence\"," +
                "                                    \"aligner_confidence\"" +
                "                                ]" +
                "                            }" +
                "                        ]" +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_headpose\"," + contextIdString() +
                "                    \"type\": \"Plugin\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [\"face_landmarks\"]," +
                "                    \"outputs\": [\"face_headpose\"]," +
                "                    \"config\": {" +
                "                        \"plugin\": \"headpose\"," +
                "                        \"model\": \"headpose_module\"," +
                "                        \"max_batch_size\": 16," +
                "                        \"model_key\": \"face_headpose\"" +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
                "                    \"type\": \"Mergence\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [" +
                "                        \"face_landmarks\"," +
                "                        \"face_headpose\"," +
                "                        \"selected_faces\"" +
                "                    ]," +
                "                    \"outputs\": [\"face_targets_quality_element\"]" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_quality_update\"," + contextIdString() +
                "                    \"type\": \"FaceQuality\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [\"face_targets_quality_element\"]," +
                "                    \"outputs\": [\"new_face_tracklets\"]" +
                "                }," +
                
                "                {" +
                "                    \"name\": \"quality_transfer\"," + contextIdString() +
                "                    \"type\": \"Operation\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [\"new_face_tracklets\"]," +
                "                    \"outputs\": [\"new_face_tracklets\"]," +
                "                    \"config\": {" +
                "                        \"operations\": [" +
                "                            {" +
                "                                \"cmd\": \"copy\"," +
                "                                \"args\": [" +
                "                                    \"quality\"," +
                "                                    \"pageant_quality\"" +
                "                                ]" +
                "                            }" +
                "                        ]" +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_quality_filter\"," + contextIdString() +
                "                    \"type\": \"FaceQualityFilter\"," +
                "                    \"parallel_group\": \"face_landmarks\"," +
                "                    \"inputs\": [\"new_face_tracklets\"]," +
                "                    \"outputs\": [\"selected_faces_with_landmarks\"]," +
                "                    \"config\": {\"quality_threshold\": " + ingrate_quality_thresh + "}" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_quality_filtered_lightweight_slice\"," + contextIdString() +
                "                    \"type\": \"LightweightTargetsSlice\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [\"selected_faces_with_landmarks\"]," +
                "                    \"outputs\": [" +
                "                        \"face_quality_filtered_target_tracklets_normal\"," +
                "                        \"face_quality_filtered_target_tracklets_lightweight\"" +
                "                    ]" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_feature_extraction\"," + contextIdString() +
                "                    \"type\": \"Plugin\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [\"face_quality_filtered_target_tracklets_normal\"]," +
                "                    \"outputs\": [\"face_features\"]," +
                "                    \"config\": {" +
                "                        \"plugin\": \"feature\"," +
                "                        \"model\": \"feature_module\"," +
                "                        \"max_batch_size\": 32," +
                "                        \"model_key\": \"face_feature\"" +
                "                     }" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_feature_mergence\"," + contextIdString() +
                "                    \"type\": \"Mergence\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [" +
                "                        \"face_features\"," +
                "                        \"face_quality_filtered_target_tracklets_normal\"" +
                "                    ]," +
                "                    \"outputs\": [\"face_tracklets_with_feature\"]" +
                "                }," +
                "                {" +
                "                    \"name\": \"faces_refinement\"," + contextIdString() +
                "                    \"type\": \"Refinement\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [" +
                "                        \"face_tracklets_with_feature\"" +
                "                    ]," +
                "                    \"outputs\": [\"best_faces\"]" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_attribute_extraciton\"," + contextIdString() +
                "                    \"type\": \"Plugin\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [\"best_faces\"]," +
                "                    \"outputs\": [\"face_attributes\"]," +
                "                    \"config\": {" +
                "                        \"plugin\": \"attribute\"," +
                "                        \"model\": \"attribute_classify_module\"," +
                "                        \"max_batch_size\": 64," +
                "                        \"filter\": false," +
                "                        \"model_key\": \"face_attribute\"" +
                "                     }" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_targets_mergence\"," + contextIdString() +
                "                    \"type\": \"Mergence\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [" +
                "                        \"face_attributes\"," +
                "                        \"best_faces\"" +
                "                    ]," +
                "                    \"outputs\": [\"face_targets_with_attr_feature\"]" +
                "                }," +
                "                {" +
                "                    \"name\": \"analyzed_lightweight_slice\"," + contextIdString() +
                "                    \"type\": \"LightweightTargetsSlice\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [\"face_targets_with_attr_feature\"]," +
                "                    \"outputs\": [" +
                "                        \"analyzed_face_targets\"," +
                "                        \"analyzed_face_targets_lightweight\"" +
                "                    ]," +
                "                    \"config\": {" +
                "                        \"target_label_configs\": [" +
                "                            {" +
                "                                \"cmd\": 37017," +
                "                               \"min_width\": 32," +
                "                               \"min_height\": 32" +
                "                            }" +
                "                        ]" +
                "                    }" +
                "                }," +
                "                {" +
                "                    \"name\": \"face_multidim_quality\"," + contextIdString() +
                "                    \"type\": \"MultidimFaceQuality\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [" +
                "                        \"analyzed_face_targets\"" +
                "                    ]," +
                "                    \"outputs\": [\"analyzed_face_targets\"]," +
                "                    \"config\": {" +
                "                        \"quality_model\": {" +
                "                            \"plugin\": \"classifier\"," +
                "                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_quality_module") + "\"," +
                "                            \"max_batch_size\": 128," +
                "                            \"model_key\": \"face_multidim_quality\"" +
                "                         }" +
                "                     }" +
                "                }," +
                
                
                "                {" +
                "                    \"name\": \"lightweight_targets_concat\"," + contextIdString() +
                "                    \"type\": \"Concat\"," +
                "                    \"parallel_group\": \"target_analysis\"," +
                "                    \"inputs\": [" +
                "                        \"face_quality_filtered_target_tracklets_lightweight\"," +
                "                        \"analyzed_face_targets_lightweight\"" +
                "                    ]," +
                "                    \"outputs\": [\"lightweight_targets\"]," +
                "                    \"config\": {" +
                "                        \"concat_items\": [" +
                "                          \"targets\"" +
                "                        ]" +
                "                     }" +
                "                }," +
                "                {" +
                "                    \"name\": \"output\"," + contextIdString() +
                "                    \"type\": \"Output\"," +
                "                    \"parallel_group\": \"output\"," +
                "                    \"inputs\": [" +
                "                        \"analyzed_face_targets\"," +
                "                        \"lightweight_targets\"" +
                "                    ]," +
                "                    \"outputs\": []" +
                "                }" +
                "            ]" +
                "        }" +
                "    ]" +
                "}" +
                "";
    }
    
    
    public static void main(String[] args) {
        String pipeline = new FacePipelineOutputTest().toString();
        System.out.println(pipeline);
    }
    
    
}