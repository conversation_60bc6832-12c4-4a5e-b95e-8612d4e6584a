package com.sensetime.intersense.cognitivesvc;

/**
 * @Author: Cirmons
 * @Date: 2023-10-10
 */
public class FeatureScoreTest {
    
    
    public static void main(String[] args) {
        
        
        
        float[] kSrcPoint = new float[] { -1.0f , 0.0f , 0.3f , 0.316f, 0.347f, 0.383f, 0.418f, 0.468f, 1.0f };
        float[]  kDstPoint = new float[] { 0.0f  , 0.0f , 0.1f , 0.6f  , 0.7f  , 0.8f  , 0.85f , 0.95f , 1.0f };
        
        
        
        
        System.out.println(normalize_feature_score(0.44805223f, kSrcPoint, kDstPoint));
    }
    
    public static float normalize_feature_score(float score, float[] kSrcPoint, float[] kDstPoint) {
        if (score <= kSrcPoint[0])
            return kDstPoint[0];
        
        if (score >= kSrcPoint[kSrcPoint.length - 1])
            return kDstPoint[kDstPoint.length - 1];
        
        for (int index = 0; index < kSrcPoint.length; index++)
            if (kSrcPoint[index] > score)
                return (score - kSrcPoint[index - 1]) * (kDstPoint[index] - kDstPoint[index - 1]) / (kSrcPoint[index] - kSrcPoint[index - 1]) + kDstPoint[index - 1];
        
        if (Float.isNaN(score))
            return 0f;
        else
            throw new RuntimeException("should not be here , score : [" + score + "]");
    }
    
}
