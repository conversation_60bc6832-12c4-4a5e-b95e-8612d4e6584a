#!/bin/bash

set -x

PROFILE=$1
shift

if [ "$PROFILE" != "dev" ] && [ "$PROFILE" != "k8s" ] && [ "$PROFILE" != "stg" ]; then
echo "PROFILE must be one of [dev, k8s, stg]";
exit 1;
fi

PATH=/mnt/d/apache-maven-3.6.0/bin:${PATH}

poms="$(find ./ -name "pom.xml")"
for p in ${poms}; do
cp -f ${p} ${p}.0;
sed -i "s|\${env}|${PROFILE}|g" ${p};
sed -i "s|\${target.env}|${PROFILE}|g" ${p};
done

which mvn;
mvn -P${PROFILE} $@

for p in ${poms}; do
mv ${p}.0 ${p};
done
