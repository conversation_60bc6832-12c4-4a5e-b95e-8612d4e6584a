<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.mycompany</groupId>
	<artifactId>my-native-bindings</artifactId>
	<name>my-native-bindings</name>
	<version>1.0-SNAPSHOT</version>
	<packaging>jar</packaging>
	
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.5</maven.compiler.source>
		<maven.compiler.target>1.5</maven.compiler.target>
	</properties>
	
	<dependencies>

		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
			<version>4.1.0</version>
		</dependency>
		<!-- Uncomment to use Rococoa:
		<dependency>
			<groupId>org.rococoa</groupId>
			<artifactId>rococoa</artifactId>
			<version>0.5</version>
		</dependency>
		-->
	
		
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.10</version>
			<scope>test</scope>
		</dependency>
		
	</dependencies>
	
	<build>
		<plugins>
			<!--
				If you're okay that your bindings will be regenerated at each build : 
			-->
			<plugin>
				<groupId>com.nativelibs4java</groupId>
				<artifactId>maven-jnaerator-plugin</artifactId>
				<version>0.12</version>
				<executions>
					<execution>
						<goals>
							<goal>generate</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			
			<!--
				If you'd rather re-generate bindings on demand inside src/main/java (with "mvn jnaerator:generate"), maybe to tweak them after generation and version them :
			
			<plugin>
				<groupId>com.nativelibs4java</groupId>
				<artifactId>maven-jnaerator-plugin</artifactId>
				<version>0.12</version>
				<configuration>
					<javaOutputDirectory>src/main/java</javaOutputDirectory>
				</configuration>
			</plugin>
			-->
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>1.4</version>
				
				<configuration>
					<shadedArtifactAttached>true</shadedArtifactAttached>
					<!--shadedClassifierName>shaded</shadedClassifierName-->
					<artifactSet>
						<excludes>
							<exclude>org.scala-lang:scala-library</exclude>
						</excludes>
					</artifactSet>
					<filters>
						<filter>
							<artifact>*:*</artifact>
							<excludes>
								<exclude>META-INF/*.SF</exclude>
								<exclude>META-INF/*.DSA</exclude>
								<exclude>META-INF/*.RSA</exclude>
								<exclude>META-INF/maven/**</exclude>
							</excludes>
						</filter>
					</filters>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<repositories>
		<repository>
			<id>sonatype</id>
			<name>Sonatype OSS Snapshots Repository</name>
			<url>http://oss.sonatype.org/content/groups/public</url>
		</repository>
		<!-- Uncomment to use Rococoa:
        <repository>
            <id>nativelibs4java-legacy</id>
            <name>NativeLibs4Java Legacy Repository</name>
            <url>http://nativelibs4java.sourceforge.net/maven/</url>
        </repository>
        -->
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>sonatype</id>
			<name>Sonatype OSS Snapshots Repository</name>
			<url>http://oss.sonatype.org/content/groups/public</url>
		</pluginRepository>
	</pluginRepositories>
</project>





