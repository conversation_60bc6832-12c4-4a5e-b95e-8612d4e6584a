<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>cognitivesvc-stream-face</artifactId>
	<packaging>jar</packaging>

	<parent>
		<groupId>com.sensetime.intersense</groupId>
		<artifactId>cognitivesvc</artifactId>
		<version>2.14.0-SNAPSHOT</version>
		<relativePath>../</relativePath>
	</parent>

	<dependencies>
		<dependency>
			<groupId>com.sensetime.intersense</groupId>
			<artifactId>cognitivesvc-stream</artifactId>
			<version>2.14.0-SNAPSHOT</version>
		</dependency>
		
		<dependency>
			<groupId>com.sensetime.intersense</groupId>
			<artifactId>cognitivesvc-seeker-face</artifactId>
			<version>2.14.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.sensetime.intersense</groupId>
			<artifactId>cognitivesvc-client</artifactId>
		</dependency>
	</dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.4</version><!--$NO-MVN-MAN-VER$-->
        <configuration>
          <skip>false</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
