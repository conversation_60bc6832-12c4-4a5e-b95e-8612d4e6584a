package com.sensetime.intersense.cognitivesvc.streamface.handler;

import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

import com.sensetime.intersense.cognitivesvc.face.handler.UtilsReader;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_packet_t;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.face.handler.RoiAttributeModelHandler;
import com.sensetime.intersense.cognitivesvc.face.handler.AttributeModelHandler.Attribute;
import com.sensetime.intersense.cognitivesvc.face.handler.FeatureAttributeModelHandler.FeatureAttribute;
import com.sensetime.intersense.cognitivesvc.face.handler.RoiFeatureAttributeModelHandler;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PasserFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PersonFaceObject;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;
import com.sensetime.intersense.cognitivesvc.strangerface.service.StrangerFaceSeekerFacade;
import com.sensetime.intersense.cognitivesvc.streamface.zutils.FaceTrack;
import com.sensetime.intersense.cognitivesvc.streamface.zutils.FaceTrack.RegStage;
import com.sun.jna.Pointer;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class FaceTrackerHandler {
	
	@Getter
	private final AtomicLong modelTrackingUnhandled = new AtomicLong();
	
	@Getter
	private final AtomicLong modelTrackletUnhandled = new AtomicLong();
	
	@Getter
	private final AtomicLong extrackedUnhandled     = new AtomicLong();
	
	@Autowired
	private ExecutorService cogThreadPool;
	
	@Autowired
	private SeekerFaceFacade seekerFaceFacade;
	
	@Autowired
	private StrangerFaceSeekerFacade strangerFaceFacade;
	
	@Autowired
	private MessageSendHandler messageSendHandler;
	
	@Autowired
	private RoiAttributeModelHandler roiAttributeModelHandler;
	
	@Autowired
	private RoiFeatureAttributeModelHandler roiFeatureAttributeModelHandler;
	
	@SuppressWarnings("unchecked")
	public void handleTargetStart(FaceTrack track, Pointer targetBson, Map<String, Object> targetJson, long now) {
		track.setTarget(targetJson);
		if(!track.check(targetJson)) {
			if(Utils.instance.faceRecognitionMode == 1) {
				track.setReason("handleTargetStart");
				if(Utils.instance.dropFaceFlag == 0) {
					handleTargetDrop(track, targetBson, targetJson, now);
					track.closeImage();
				}
			}
			if(Utils.instance.logged) {
				log.info("handleTargetStart checkFail{}", targetJson);
			}
			return;
		}

		track.setRegFaceStage(RegStage.queued);
		track.setRegAttributeStage(RegStage.queued);
		track.setQueuedTime(now);
		
		Pointer image = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(targetBson, "image")).getValue();
		track.setImage(image, (Map<String, Number>)track.getTarget().get("roi"));

		Consumer<FeatureAttribute[]> consumer = featureAttributes -> {
			if(ArrayUtils.isEmpty(featureAttributes)) {
				track.setRegFaceStage(RegStage.idle);
				track.setRegAttributeStage(RegStage.idle);
				track.closeImage();
				log.info("handleTargetStart featureAttributes checkFail{}", targetJson);
				return;
			}
			
			track.setFeature(featureAttributes[0].getFeature());
			track.setRegFaceStage(RegStage.extracted);
			
			track.setAttribute(featureAttributes[0].getAttribute());
			track.setRegAttributeStage(RegStage.idle);
			
			try {
				cogThreadPool.execute(() -> {
					try {
						handleExtractedTrack(track);
					}finally {
						track.closeImage();
					}
				});
			}catch(Exception e) {
				extrackedUnhandled.getAndIncrement();
				track.setRegFaceStage(RegStage.idle);
				track.closeImage();
			}
		};
		
		boolean ret = roiFeatureAttributeModelHandler.extractAsyncByFrame(track.getImage().getFaceImage(), consumer);
		if(!ret)
			modelTrackingUnhandled.getAndIncrement();
	}

	@SuppressWarnings("unchecked")
	public void handleTargetAgain(FaceTrack track, Pointer targetBson, Map<String, Object> targetJson, long now) {
		if(Utils.instance.faceRecognitionMode == 0){
			return;
		}
		if(!track.check(targetJson)) {
			if(Utils.instance.faceRecognitionMode == 1) {
				track.setReason("handleTargetAgain");
				if(Utils.instance.dropFaceFlag == 0){
					handleTargetDrop(track, targetBson, targetJson, now);
					track.closeImage();
				}
			}
			if(Utils.instance.logged) {
				log.info("handleTargetAgain checkFail{}", targetJson);
			}
			return;
		}

		boolean timeReady = now - track.getQueuedTime() >= 1000;
		if(!timeReady) {
//			if(Utils.instance.logged) {
//				log.info("handleTargetAgain timeReady checkFail{}", targetJson);
//			}
			return;
		}

		if( (track.getRegFaceStage() == RegStage.idle  && track.getRegTimes() > 0) || Utils.instance.faceRecognitionMode == 1) {
			//快速模式
			if( Utils.instance.faceRecognitionMode == 1){
				track.setTarget(targetJson);

				track.setRegFaceStage(RegStage.queued);
				track.setRegAttributeStage(RegStage.queued);
				track.setQueuedTime(now);
				track.setRegTimes(track.getRegTimes() - 1);
				
				Consumer<FeatureAttribute[]> consumer = featureAttributes -> {
					if(ArrayUtils.isEmpty(featureAttributes)) {
						track.setRegFaceStage(RegStage.idle);
						track.setRegAttributeStage(RegStage.idle);
						track.closeImage();
						log.info("handleTargetAgain featureAttributes checkFail{}", targetJson);
						track.getImage().setSavedFacePath(FrameUtils.NOIMAGE);
						track.getImage().setSavedScenePath(FrameUtils.NOIMAGE);
						return;
					}
					
					track.setFeature(featureAttributes[0].getFeature());
					track.setRegFaceStage(RegStage.extracted);
					
					track.setAttribute(featureAttributes[0].getAttribute());
					track.setRegAttributeStage(RegStage.idle);
					track.setHandleTargetAgainFlag(0);

					try {
						cogThreadPool.execute(() -> {
							try {
								handleExtractedTrack(track);
							}finally {
								track.closeImage();
							}
						});
					}catch(Exception e) {
						extrackedUnhandled.getAndIncrement();
						track.setRegFaceStage(RegStage.idle);
						track.closeImage();
					}
				};

//				if(track.isRegScoreVeryGood()) {
//					consumer.accept(new FeatureAttribute[]{FeatureAttribute.builder().feature(track.getFeature()).attribute(track.getAttribute()).build()});
//				}else {
					Pointer image = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(targetBson, "image")).getValue();
					synchronized (track) {
						track.setImage(image, (Map<String, Number>) track.getTarget().get("roi"));

						boolean ret = roiFeatureAttributeModelHandler.extractAsyncByFrame(track.getImage().getFaceImage(), consumer);
						if (!ret)
							modelTrackingUnhandled.getAndIncrement();
					}
				//}
			}
		}
	}
	
	public void handleTracklet(FaceTrack track, Pointer trackletBson, Map<String, Object> trackletJson, long now) {

		track.setTarget(trackletJson);
		trackletJson.put("roi", trackletJson.get("target_image_roi"));//更换目标roi

		Number sourceId = (Number) trackletJson.getOrDefault("image_id", 0);
		Number trackId = (Number) trackletJson.getOrDefault("track_id", 0);
		Number id = (Number) trackletJson.getOrDefault("id", 0);

		String fileName = sourceId.toString() +"_" + trackId.toString() + "_" + id.toString() + "_"  + UUID.randomUUID().toString().substring(1, 6);



		Pair<Boolean, String> pair = track.checkTracklet(trackletJson, track.getStreamInfra().getDeviceId(), fileName);
		if(!pair.getLeft()) {
			if(Utils.instance.dropFace == 0) {
				Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletBson, "image")).getValue();
				FrameUtils.save_image_as_jpg(face, ImageUtils.newFileName("dropFace" , fileName, "face"));
				trackletJson.put("reason", pair.getRight());
				ImageUtils.newFileNameJson("dropFaceJson" , fileName, trackletJson, "face");
				//Pointer scene_frame = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletBson, "scene_frame")).getValue();
				//FrameUtils.save_image_as_jpg(scene_frame, ImageUtils.newFileName("dropFace", fileName + "_large","face"));
			}

			if(Utils.instance.dropFaceFlag == 0) {
				try {
					Pair<Boolean, String> pairDrop = track.checkTrackletDrop(trackletJson, track.getStreamInfra().getDeviceId(), fileName);
					//if (pairDrop.getLeft() && track.isIntegrateQualityDrop(trackletJson)) {
						track.setReason("handleTracklet-" + pair.getRight() + "-" + pairDrop.getRight());
						handleTargetDropTracklet(track, trackletBson, trackletJson, now);
					//}
				} catch (Exception e) {
					log.error("dropErr", e);
				}
			}
			if(Utils.instance.logged) {
				log.info("handleTracklet checkFail{},{}", trackletJson, track.getStreamInfra().getDeviceId());
			}
			track.closeImage();
			return ;
		}
		//综合质量
		if(!track.isIntegrateQuality(trackletJson, track.getStreamFace().getIntegrateQuality())) {
			if(Utils.instance.dropFace == 0) {
				Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletBson, "image")).getValue();
				FrameUtils.save_image_as_jpg(face, ImageUtils.newFileName("dropFace", fileName,"face"));
				trackletJson.put("reason", "integrateQualityFilter");
				ImageUtils.newFileNameJson("dropFaceJson", fileName, trackletJson,"face");
				//Pointer scene_frame = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletBson, "scene_frame")).getValue();
				//FrameUtils.save_image_as_jpg(scene_frame, ImageUtils.newFileName("dropFace", fileName + "_large","face"));
			}
			if(Utils.instance.dropFaceFlag == 0) {
				try {
					Pair<Boolean, String> pairDrop = track.checkTrackletDrop(trackletJson, track.getStreamInfra().getDeviceId(), fileName);
					//if (pairDrop.getLeft() && track.isIntegrateQualityDrop(trackletJson)) {
						track.setReason("handleTracklet-" + pairDrop.getRight() + "-integrateQuality");
						handleTargetDropTracklet(track, trackletBson, trackletJson, now);
					//}
				} catch (Exception e) {
					log.error("dropErr", e);
				}
			}
			if(Utils.instance.logged) {
				log.info("handleTracklet isIntegrateQuality checkFail{}", trackletJson);
			}
			track.closeImage();
			return ;
		}
		boolean timeReady = now - track.getQueuedTime() >= 500;
		if(!timeReady) {
			if(Utils.instance.logged) {
				log.info("handleTracklets timeReady checkFail{}", trackletJson);
			}
			//return;
		}
		float[]  featureFloat = UtilsReader.readFeatureData(trackletBson);


		track.setTracklet(trackletJson);
		track.setQueuedTime(now);
		
		Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletBson, "image")).getValue();

		try{
			scgPipelineCheck(track, trackletBson, face);
		}catch (Exception e){
			e.printStackTrace();
		}
		//Consumer<FeatureAttribute[]> consumer = featureAttributes -> {
			if(ArrayUtils.isEmpty(featureFloat)) {
				track.setRegFaceStage(RegStage.idle);
				track.setRegAttributeStage(RegStage.idle);
				track.closeImage();
				track.getImage().setSavedFacePath(FrameUtils.NOIMAGE);
				track.getImage().setSavedScenePath(FrameUtils.NOIMAGE);
				log.info("handleTargetAgain featureAttributes checkFail tracklet{}", trackletBson);
				return;
			}
			
			track.setFeature(featureFloat);
			track.setRegFaceStage(RegStage.extracted);
			
			track.setAttribute(UtilsReader.readAttributeData(trackletBson));
			track.setRegAttributeStage(RegStage.idle);
			
			try {
				cogThreadPool.execute(() -> {
					try {
						handleExtractedTrack(track);
					}finally {
						track.closeImage();
					}
				});
			}catch(Exception e) {
				extrackedUnhandled.getAndIncrement();
				track.setRegFaceStage(RegStage.idle);
				track.closeImage();
			}
	//	};
		
//		if(track.isRegScoreVeryGood())
//			consumer.accept(new FeatureAttribute[] {FeatureAttribute.builder().feature(track.getFeature()).attribute(track.getAttribute()).build()});
//		else {
//			boolean ret = roiFeatureAttributeModelHandler.extractAsyncByFrame(track.getImage().getFaceImage(), consumer);
//			if(!ret)
//				modelTrackletUnhandled.getAndIncrement();
//		}
	}


	@SuppressWarnings("unchecked")
	public void handleTargetDrop(FaceTrack track, Pointer targetBson, Map<String, Object> targetJson, long now) {

		Pointer image = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(targetBson, "image")).getValue();
		track.setImage(image, (Map<String, Number>)track.getTarget().get("roi"));

		messageSendHandler.messageSendUnknownDrop(track);
	}
	public void handleTargetDropTracklet(FaceTrack track, Pointer targetBson, Map<String, Object> targetJson, long now) {


		Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(targetBson, "image")).getValue();

		scgPipelineCheck(track, targetBson, face);

		messageSendHandler.messageSendUnknownDropPattern(track);
	}

	private void scgPipelineCheck(FaceTrack track, Pointer targetBson, Pointer face) {
		if (Utils.instance.scgFacePipeline) {
			if(Boolean.TRUE.equals(Utils.instance.imageSceneSave)) {
				kestrel_packet_t packet_t = new kestrel_packet_t(KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(targetBson, "encoded_scene_frame")).getValue());
				packet_t.read();
				byte[] imageRes = null;
				if (packet_t.size > 0 && packet_t.data != null)
					imageRes = packet_t.data.getByteArray(0, packet_t.size);

				track.setImage(face, imageRes);
			}else{
				track.setImage(face);
			}
		}else{
			Pointer scene_frame = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(targetBson, "scene_frame")).getValue();
		    track.setImage(face, scene_frame);
		}
	}
	private void scgPipelineCheckNoSharp(FaceTrack track, Pointer targetBson, Pointer face) {
		if (Utils.instance.scgFacePipeline) {
			if(Boolean.TRUE.equals(Utils.instance.imageSceneSave)) {
				Pointer scene_frame = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(targetBson, "scene_frame")).getValue();
				track.setImage(face, scene_frame);
			}else{
				track.setImage(face);
			}
		}else{
			Pointer scene_frame = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(targetBson, "scene_frame")).getValue();
			track.setImage(face, scene_frame);
		}
	}

	public void handleTargetDropIndentity(FaceTrack track) {

		messageSendHandler.messageSendUnknownDropPattern(track);
	}
	
	@SuppressWarnings("unchecked")
	private void handleExtractedTrack(FaceTrack track) {

		if(track.getRegFaceStage() == RegStage.extracted) {
			if(Objects.isNull(track.getTracklet())) {
				if(Utils.instance.faceRecognitionMode == 0){
					return;
				}
				//imagecapture
				int changed = indentifyTrack(track);
				track.setRegFaceStage(RegStage.identified);
				//log.info("handleExtractedTrack{}", track);
				if(track.isRecognized()) {
					if(changed > 0 || Utils.instance.faceRecognitionMode == 1) {
						track.setImageCaptureSended(true);

						//人脸识别过程中同一个trackId仅会触发一次消息 如果是handleTargetAgain这里就没了(delete)
						//快速模式才输出 精准模式不需要(0精准1快速)
						//2 默认模式
						if(Utils.instance.faceRecognitionMode == 1 || Utils.instance.faceRecognitionMode == 2) {
							//track.getImage().save();
							messageSendHandler.messageSendImageCapture(track, false, changed);
						}
					}
				}else if(!track.isUnkownMessageSended()) {

					if(Utils.instance.logged) {
						log.info("isUnkownMessageSended checkFail{}", track);
					}
					track.setUnkownMessageSended(true);
					//周期告警不需要,周期告警：同一个人员在同一台相机下，指定时间段内只告警1次
					//2 默认模式
					if(track.getStreamFace().getProcessFace().getEventTrackTime() > 0){
						if(Utils.instance.faceRecognitionMode != 2) {
							if(Utils.instance.logged) {
								log.info("isUnkownMessageSended checkFail1{}", track);
							}
							return;
						}
					}
					//快速模式才输出 精准模式不需要(0精准1快速)
					//人脸识别过程中同一个trackId仅会触发一次消息 如果是handleTargetAgain这里就没了(delete)
					//快速模式去掉第一次的抓拍
					if(Utils.instance.faceRecognitionMode == 2) {
						//track.getImage().save();
						messageSendHandler.messageSendUnknownCapture(track);
					}
				}else{
					if(Utils.instance.logged) {
						log.info("isRecognized checkFail {}", track.getTarget());
					}
				}
			}else {
				//patterndetect
				//1 person 2 passer 0: 不走faiss，比对分1.0
				int regFlag = 0;
				if(!track.isRegScoreVeryGood())
					regFlag = indentifyTrack(track);

				//track.getImage().save();
				boolean hasSaveImage = false;
				//陌生人识别开关打开可以 陌生人识别开关0-开，1-关
				//陌生人识别开关关闭 + 是陌生人策略，只有配置陌生人策略的摄像头才会产生陌生人记录和陌生人档案
				//add默认模式
				boolean filterStrannger = (Utils.instance.strangerRecognition > 0 && track.getStreamFace().getProcessFace().getStrangerPolicy() > 0);
				if(!track.isRecognized()
						&& Objects.requireNonNullElse(track.getStreamFace().getStorePasser(), true)
				      && (!filterStrannger || Utils.instance.faceRecognitionMode == 2 ) ) {

					//add
					track.getImage().save();
					hasSaveImage = true;
					StrangerFaceObject.StrangerFaceObjectBuilder builder = StrangerFaceObject.builder().id(-1)
							.feature(track.getFeature())
							.trackId(track.getTrackId())						
							.imagePath(track.getImage().getSavedFacePath())
							.infra(track.getStreamInfra())
							.quality(((Number)track.getTarget().getOrDefault("quality", 0.0f)).floatValue());
					
					try {
						if(track.getAttribute()!=null) {
							Map<String, Number> gender = (Map<String, Number>) track.getAttribute().get("gender_code");
							if(gender !=null) {
								builder = builder.sex(gender.get("male").floatValue() > gender.get("female").floatValue() ? "0" : "1")
										.age(String.valueOf(((Map<String, Float>) track.getAttribute().get("st_age_value")).get("st_age_value")));
							}
						}
					}catch(Exception e) {
						e.printStackTrace();
					}

					try {
						String personUUID = strangerFaceFacade.strangerIsHere(builder.build());
						if(personUUID != null) {
							track.setRegPasserScore(1.0f);
							track.setPasserObj(PasserFaceObject.builder().pid(personUUID).avatar(track.getImage().getSavedFacePath()).build());
							track.setNewPasser(true);
							regFlag = 2;
						}
					}catch(Exception e) {
						e.printStackTrace();
					}
					//快速模式 第一次
					if(Utils.instance.faceRecognitionMode == 1){
						messageSendHandler.messageSendImageCapture(track, hasSaveImage, regFlag);
					}
				}

				//精准模式才有 add默认模式
				if((track.isRecognized() && Utils.instance.faceRecognitionMode == 0) || Utils.instance.faceRecognitionMode == 2 )
					messageSendHandler.messageSendPatternDetect(track, hasSaveImage, regFlag);
				
				track.setRegFaceStage(RegStage.identified);
				track.setPatternDetectSended(true);
			}
			
			track.setRegFaceStage(RegStage.idle);
		}else if(track.getRegAttributeStage() == RegStage.extracted) {
			//attributeupdate
			track.setRegAttributeStage(RegStage.identified);
			//人脸识别过程中同一个trackId仅会触发一次消息 如果是handleTargetAgain这里就没了
			//快速模式才输出 精准模式不需要
			if(Utils.instance.faceRecognitionMode == 1 || Utils.instance.faceRecognitionMode == 2 ) {
				messageSendHandler.messageSendAttributeUpdate(track);
			}
			track.setRegAttributeStage(RegStage.idle);
		}
	}

	private int indentifyTrack(FaceTrack track) {
		
		if(Utils.instance.featureSearchSfd) {
			return  1;
		}
		int changed = 0;

		List<Pair<PersonFaceObject, Float>> personPairs = null;

		PersonParam param = PersonParam.builder()
				.threshold(Objects.requireNonNullElse(track.getStreamFace().getBaseThreshold(), Utils.instance.Lv1Threshold))
				.count(1)
				.deptIds(track.getStreamInfra().privileges())
				.personGroups(StringUtils.split(track.getStreamFace().getTargetGroup(), ","))
				.feature(track.getFeature())
				.findLocal(true)
				.build();
		personPairs = seekerFaceFacade.findPerson(param);

		if(personPairs != null && !personPairs.isEmpty()) {
			Pair<PersonFaceObject, Float> personPair = personPairs.get(0);
			//if(personPair.getRight() >= Objects.requireNonNullElse(track.getRegPersonScore(), -1f)) {
				track.setPersonObj(personPair.getLeft());
				track.setRegPersonScore(personPair.getRight());
				changed = 1;
			//}
		}else {
			//陌生人识别开关关闭(0-开，1-关 默认关) + 无陌生人策略(0 陌生人策略)，系统不会产生任何陌生人记录和陌生人档案
			if(Utils.instance.strangerRecognition > 0 && track.getStreamFace().getProcessFace().getStrangerPolicy() > 0 ){
				if(Utils.instance.logged) {
					log.info("strangerRecognition checkFail error, strangerRecognition=0,will not generate any message,trackId={},deviceId={}",
							track.getTrackId(),track.getStreamFace().getDeviceId());
				}
				if(Utils.instance.dropFaceIndentify == 0) {
					try {
							track.setReason("handleTracklet-indentify-notMatch");
							handleTargetDropIndentity(track);
					} catch (Exception e) {
						log.error("dropErr identity", e);
					}
				}
				return 0;
			}
			PasserParam passerParam = PasserParam.builder()
					.threshold(Objects.requireNonNullElse(track.getStreamFace().getBaseThreshold(), Utils.instance.Lv0Threshold))
					.feature(track.getFeature())
					.deptIds(track.getStreamInfra().privileges())
					.count(1)
					.findLocal(true)
					.build();
			
			List<Pair<PasserFaceObject, Float>> passerPairs = seekerFaceFacade.findPasser(passerParam);
			if(!passerPairs.isEmpty()) {
				Pair<PasserFaceObject, Float> passerPair = passerPairs.get(0);
				//if(passerPair.getRight() >= Objects.requireNonNullElse(track.getRegPasserScore(), -1f) ) {
					track.setPasserObj(passerPair.getLeft());
					track.setRegPasserScore(passerPair.getRight());
					changed = 2;
				//}
			}
		}
		
		return changed;
	}
	
	@Scheduled(fixedDelay = 5000)
	public void reportUnhandled() throws Exception {
		long count = modelTrackingUnhandled.get() + modelTrackletUnhandled.get() + extrackedUnhandled.get();
		if(count <= 0) 
			return ;
		
		log.warn("*********************************!!!!!!!!!!!!!!!!***********************************");
		log.warn("****** !!!!!!!! reportUnhandled, tracking count[" + modelTrackingUnhandled.get() + "], tracklet count[" + modelTrackletUnhandled.get() + "], extracked count[" + extrackedUnhandled.get() + "].");
		log.warn("*********************************!!!!!!!!!!!!!!!!***********************************");
		
		modelTrackingUnhandled.set(0);
		modelTrackletUnhandled.set(0);
		extrackedUnhandled.set(0);
	}
}
