package com.sensetime.intersense.cognitivesvc.streamface;

import com.google.j2objc.annotations.Property;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.storage.service.FileAccessor;
import jakarta.annotation.PostConstruct;

import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streamface.controller.DeviceFaceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer.DeviceType;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils.FrameDefaultRequired;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoSeenFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.service.VideoFrameFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.service.VideoFrameFilter.VideoChecked;
import com.sensetime.intersense.cognitivesvc.streamface.handler.FaceTrackerPipeline;
import com.sensetime.intersense.cognitivesvc.streamface.handler.FaceTrackerHandler;
import com.sensetime.intersense.cognitivesvc.streamface.handler.VideoIconContainer;
import com.sensetime.intersense.cognitivesvc.streamface.video.filter.FaceNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.streamface.video.filter.FaceSeenFilter;
import com.sensetime.storage.autoconfigure.*;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.sensetime.storage.factory.FileStorageType.GENERAL;
import static com.sensetime.storage.factory.FileStorageType.OSG;

@Configuration("streamFaceConfiguation")
@EnableScheduling
@ComponentScan
@ConditionalOnExpression("${stream.face.enabled:true} && ${stream.enabled:true} && ${face.enabled:true} && ${seeker.enabled:true} && ${seeker.face.enabled:true}")
@Slf4j
public class CognitivesvcConfiguation{
	
	@Autowired
	private FaceTrackerPipeline faceTrackerContainer;
	
	@Autowired
	private FaceTrackerHandler faceTrackerHandler;
	
	@Autowired
	private VideoIconContainer videoIconContainer;

	@Value("${preMakeDirs}")
	private String preMakeDirs;

	@Autowired
	StorageProperties storageProperties;

//	@Autowired
//	private Utils.Sync sync;
	
	@PostConstruct
	public void initialize() {
		if(VideoNonSeenFilter.existingFilter.contains(FaceNonSeenFilter.class))
			return;
		
		VideoNonSeenFilter.existingFilter.add(FaceNonSeenFilter.class);
		VideoSeenFilter.existingFilter.add(FaceSeenFilter.class);

		FaceNonSeenFilter.setFaceTrackerContainer(faceTrackerContainer);
		FaceNonSeenFilter.setFaceTrackerHandler(faceTrackerHandler);

		FaceSeenFilter.setFaceTrackerContainer(faceTrackerContainer);
		FaceSeenFilter.setFaceTrackerHandler(faceTrackerHandler);
		FaceSeenFilter.setVideoIconContainer(videoIconContainer);


		DeviceFaceProvider.setFaceTrackerContainer(faceTrackerContainer);

		log.warn("\n");
		log.warn("************************************");
		log.warn("**********init stream face**********");
		log.warn("***stream.enabled=false to disable**");
		log.warn("***seeker.enabled=false to disable**");
		log.warn("seeker.face.enabled=false to disable");
		log.warn("****face.enabled=false to disable***");
		log.warn("stream.face.enabled=false to disable");
		log.warn("************************************");
		log.warn("\n");

		if(storageProperties.getFileStorageType().equals(OSG)){
			ImageUtils.preMkDirPostContructCompleted = true;
			log.info("******* use osg, no need preMkdirs");
			return;
		}

		List<String> preMakeDirList = Arrays.asList(preMakeDirs.split(","));
		//sync.sync();

		ExecutorService executorService = Executors.newSingleThreadExecutor();
		executorService.submit(new Runnable() {
			@Override
			public void run() {
				try {
					log.info("preMakeDirs stream-face init...");
					if (ImageUtils.preMkDirPostContructCompleted) {
						log.info("preMakeDir Completed already, skip...");
					}
					ImageUtils.mkdirsByHour(preMakeDirList, true);
					ImageUtils.mkdirsByHour(preMakeDirList, false);
				} finally {
					ImageUtils.preMkDirPostContructCompleted = true;
					log.info("[preMakeDirsCron] preMakeDir Completed");
				}
			}
		});
	}
	
	@Bean
	public VideoFrameFilter faceVideoFrameFilter(){
		return (infra, models) -> {
			if(faceTrackerContainer.getStreamFaces().containsKey(infra.getDeviceId())) {
				if("0".equals(infra.getRtmpOn()))
					return VideoChecked.ALL;
				else
					return VideoChecked.GPU;
			}else 
				return null;
		};
	}
    
    @Bean
    public FrameDefaultRequired faceFrameDefaultRequired() {
    	return new FrameDefaultRequired() {
			@Override
			public int requiredNonSeenReusedFrameCount(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
						return 16;
					case T4: 
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 32;
				}
			}

			@Override
			public int requiredNonSeenQueueSize(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
						return 256;
					case T4: 
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 768;
				}
			}

			@Override
			public int requiredNonSeenMaxCount(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
						return 16;
					case T4:
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 32;
				}
			}
		};
    }
}
