package com.sensetime.intersense.cognitivesvc.streamface.video.filter;

import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamFace;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.streamface.handler.FaceTrackerPipeline;
import com.sensetime.intersense.cognitivesvc.streamface.handler.FaceTrackerHandler;
import com.sensetime.intersense.cognitivesvc.streamface.zutils.FaceTrack;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Setter;

/** 处理异步提交过来的帧们
 */
@Order(100)
public class FaceNonSeenFilter extends VideoNonSeenFilter{
	
	private static final Logger log = LoggerFactory.getLogger(FaceNonSeenFilter.class);
	
	@Setter
	private static FaceTrackerHandler faceTrackerHandler;
	
	@Setter
	private static FaceTrackerPipeline faceTrackerContainer;

	@Override
	protected void handleStageOne(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {		
		Map<String, VideoStreamFace> streamFaces = faceTrackerContainer.getStreamFaces();
		
		Integer[] indexArray = Stream.iterate(0, i -> i + 1).limit(devices.length)
				.map(index -> {
					VideoStreamFace faceDevice = streamFaces.get(devices[index].getDeviceId());
					return (faceDevice != null && faceDevice.getRunFaceModel() == 0) ? index : -1;
				})
				.filter(index -> index >= 0)
				.toArray(Integer[]::new);
		
		if(indexArray.length <= 0)
			return ;
		
		int size = indexArray.length;
		
		Pointer[] gpuFrames = new Pointer[size];
		Long[]    sourceIds = new Long[size];
		
		for(int index = 0; index < size; index ++) {
			gpuFrames[index] = videoFrames[indexArray[index]].getGpuFrame();
			sourceIds[index] = Utils.keyToContextId(devices[indexArray[index]].getDeviceId());
		}
		
		PointerByReference input_keson    = new PointerByReference();
		PointerByReference output_stage_1 = new PointerByReference();
		PointerByReference output_stage_2 = new PointerByReference();
		
		context.get().put("input_keson",    input_keson);
		context.get().put("output_stage_1", output_stage_1);
		context.get().put("output_stage_2", output_stage_2);
		
		input_keson.setValue(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), KesonUtils.frameToKeson(gpuFrames, sourceIds)));
		
		try {
			faceTrackerContainer.getLock().readLock().lock();
			
			int ret = KestrelApi.flock_pipeline_input_and_output(faceTrackerContainer.pipeline(), FaceTrackerPipeline.stageOne, input_keson.getValue(), output_stage_1);
			if(ret != KestrelApi.KESTREL_OK)
				return ;
		}finally {
			faceTrackerContainer.getLock().readLock().unlock();
		}
	}
	
	@Override
	protected void handleStageTwo(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		PointerByReference output_stage_1 = (PointerByReference)context.get().get("output_stage_1");
		if(output_stage_1 == null)
			return ;

		PointerByReference output_stage_2 = new PointerByReference();
		context.get().put("output_stage_2", output_stage_2);
		
		try {
			faceTrackerContainer.getLock().readLock().lock();
			
			int ret = KestrelApi.flock_pipeline_input_and_output(faceTrackerContainer.pipeline(), FaceTrackerPipeline.stageTwo, output_stage_1.getValue(), output_stage_2);
			if(ret != KestrelApi.KESTREL_OK)
				return ;
		}finally {
			faceTrackerContainer.getLock().readLock().unlock();
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	protected void handleStageThree(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		PointerByReference output_stage_1 = (PointerByReference)context.get().get("output_stage_1");
		PointerByReference output_stage_2 = (PointerByReference)context.get().get("output_stage_2");
		if(output_stage_1 == null || output_stage_2 == null)
			return ;
		
		long now = System.currentTimeMillis();
		
		Map<String, VideoStreamFace> streamFaces = faceTrackerContainer.getStreamFaces();

		Pointer targetsBson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(output_stage_1.getValue(), 0), "targets");
		int targetSize = KestrelApi.keson_array_size(targetsBson);
		
		Map<Long, VideoFrame> sourceFrameMap = Stream.iterate(0, i -> i + 1)
				.limit(devices.length)
				.collect(Collectors.toMap(index -> Utils.keyToContextId(devices[index].getDeviceId()), index -> videoFrames[index]));
		
		for(int index = 0; index < targetSize; index ++) {
			Pointer targetBson = KestrelApi.keson_get_array_item(targetsBson, index);
			Map<String, Object> target = (Map<String, Object>)KesonUtils.kesonToJson(targetBson);
			
			Number sourceId = (Number)target.get("source_id");
			Number trackId = (Number)target.get("track_id");
			
			Map<Integer, FaceTrack> contextTrackMap = faceTrackerContainer.getOnGoingTracks().get(sourceId.longValue());
			FaceTrack track = contextTrackMap.get(trackId.intValue());
			
			if(track == null) {				
				VideoStreamInfra infra = Arrays.stream(devices).filter(device -> Utils.keyToContextId(device.getDeviceId()) == sourceId.longValue()).findAny().get();
				track = FaceTrack.builder().trackId(trackId.intValue()).streamInfra(infra).streamFace(streamFaces.get(infra.getDeviceId())).build();
				contextTrackMap.put(trackId.intValue(), track);
				
				faceTrackerHandler.handleTargetStart(track, targetBson, target, now);
			}else {
				faceTrackerHandler.handleTargetAgain(track, targetBson, target, now);
			}
			
			track.setFrameIndex(sourceFrameMap.get(sourceId).getFrameIndex());
			track.setFramePts(sourceFrameMap.get(sourceId).getCapturedTime());
			track.setFrameDecodeCost(sourceFrameMap.get(sourceId).getFrameDecodeCost());
		}
		
		Pointer trackletsBson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(output_stage_2.getValue(), 0), "targets");
		int trackletSize = KestrelApi.keson_array_size(trackletsBson);

		for(int index = 0; index < trackletSize; index ++) {

			Pointer trackletBson = KestrelApi.keson_get_array_item(trackletsBson, index);
			Map<String, Object> tracklet = (Map<String, Object>)KesonUtils.kesonToJson(trackletBson);
			
			Number sourceId = (Number)tracklet.get("source_id");
			Number trackId = (Number)tracklet.get("track_id");
			
			Map<Integer, FaceTrack> contextTrackMap = faceTrackerContainer.getOnGoingTracks().get(sourceId.longValue());
			FaceTrack track = contextTrackMap.get(trackId.intValue());
			if(track != null) {
				faceTrackerHandler.handleTracklet(track, trackletBson, tracklet, now);
			}else {
				log.warn("***************why tacklet is first to show up.******************");

				VideoStreamInfra infra = Arrays.stream(devices).filter(device -> Utils.keyToContextId(device.getDeviceId()) == sourceId.longValue()).findAny().get();
				track = FaceTrack.builder().trackId(trackId.intValue()).streamInfra(infra).streamFace(streamFaces.get(infra.getDeviceId())).build();
				contextTrackMap.put(trackId.intValue(), track);

				faceTrackerHandler.handleTracklet(track, trackletBson, tracklet, now);
			}
			track.setFrameIndex(sourceFrameMap.get(sourceId).getFrameIndex());
			track.setFramePts(sourceFrameMap.get(sourceId).getCapturedTime());
			track.setFrameDecodeCost(sourceFrameMap.get(sourceId).getFrameDecodeCost());

			//debug模式下选帧图片
			try{
				if(Utils.instance.cogDebug == 0) {
					Number id = (Number) tracklet.getOrDefault("id", 0);
					Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletBson, "image")).getValue();
					String fileName = sourceId.toString() +"_" + trackId.toString() + "_"  + id.toString() + "_"  + UUID.randomUUID().toString().substring(1, 10);
					FrameUtils.save_image_as_jpg(face, ImageUtils.newFileName("select_face" , fileName, "face"));
				}

			}catch (Exception e){
				e.printStackTrace();
			}

			
		}

		//TODO 人脸 status为5 finish的时候要删除
		Pointer dropTargetsBson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(output_stage_1.getValue(), 1), "targets");
		int dropTargetSize = KestrelApi.keson_array_size(dropTargetsBson);

		for(int index = 0; index < dropTargetSize; index ++) {
			Pointer dropTarget = KestrelApi.keson_get_array_item(dropTargetsBson, index);
			Map<String, Object> target = (Map<String, Object>)KesonUtils.kesonToJson(dropTarget);

			Number sourceId = (Number)target.get("image_id");
			Number trackId = (Number)target.get("dropped_track_id");

			Map<Integer, FaceTrack> contextTrackMap = faceTrackerContainer.getOnGoingTracks().get(sourceId.longValue());
			contextTrackMap.remove(trackId.intValue());
		}


			Pointer dropTrckletBson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(output_stage_2.getValue(), 1), "targets");
			int dropTrackSize = KestrelApi.keson_array_size(dropTrckletBson);

			for(int index = 0; index < dropTrackSize; index ++) {
				Pointer dropTarcklet = KestrelApi.keson_get_array_item(dropTrckletBson, index);
				Map<String, Object> target = (Map<String, Object>)KesonUtils.kesonToJson(dropTarcklet);

				Number sourceId = (Number)target.get("source_id");
				Number trackId = (Number)target.get("track_id");
				Number id = (Number)target.get("id");


				Map<Integer, FaceTrack> contextTrackMap = faceTrackerContainer.getOnGoingTracks().get(sourceId.longValue());
				FaceTrack track = contextTrackMap.get(trackId.intValue());
				if(track != null) {
					track.setReason("handleTargetDrop");
					if(Utils.instance.dropFaceFlag == 0){
						faceTrackerHandler.handleTargetDropTracklet(track, dropTarcklet, target, now);
						track.closeImage();
					}
				}

				if(Utils.instance.dropFace == 0) {
					String fileName = sourceId.toString() + trackId.toString() + id.toString() + UUID.randomUUID().toString().substring(1, 6);

					Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(dropTarcklet, "image")).getValue();
					FrameUtils.save_image_as_jpg(face, ImageUtils.newFileName("dropFace", fileName,"face"));

					Pointer scene_frame = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(dropTarcklet, "scene_frame")).getValue();
					FrameUtils.save_image_as_jpg(scene_frame, ImageUtils.newFileName("dropFace", fileName + "_large","face"));

					ImageUtils.newFileNameJson("dropFaceJson", fileName, target,"face");
				}
			}


	}	
	
	@Override
	protected void postHandleStageThree(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		PointerByReference input_keson    = (PointerByReference)context.get().get("input_keson");
		PointerByReference output_stage_1 = (PointerByReference)context.get().get("output_stage_1");
		PointerByReference output_stage_2 = (PointerByReference)context.get().get("output_stage_2");
		
		KesonUtils.kesonDeepDelete(input_keson, output_stage_1, output_stage_2);
	}
	
	/** 初始化 */
	@Override
	protected synchronized void initialize(VideoStreamInfra device, boolean changeFlag) {

		log.info("initialize change");
		VideoStreamFace face = faceTrackerContainer.getStreamFaces().get(device.getDeviceId());
		if(face == null)
			return ;
		
		long contextId = Utils.keyToContextId(device.getDeviceId());
		if(changeFlag){
			faceTrackerContainer.reInitContext(device.getDeviceId(), contextId);
		}else {
			faceTrackerContainer.initContext(device.getDeviceId(), contextId);
		}

	}
	
	/** 销毁 */
	@Override
	protected synchronized void destroy(VideoStreamInfra device) {
		try { Thread.sleep(2000); } catch (InterruptedException e) { }
		
		long contextId = Utils.keyToContextId(device.getDeviceId());
		faceTrackerContainer.destroyContext(device.getDeviceId(), contextId);
	}
}