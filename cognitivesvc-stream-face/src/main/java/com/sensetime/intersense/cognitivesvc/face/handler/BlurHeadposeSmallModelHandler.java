package com.sensetime.intersense.cognitivesvc.face.handler;

import com.sensetime.intersense.cognitivesvc.face.utils.FaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Map;

public class BlurHeadposeSmallModelHandler extends AbstractHandler<BlurHeadposeSmallModelHandler.BlurHeadposeSmall>{

	public BlurHeadposeSmallModelHandler() {
		super(false);
		this.handlerEntity  = HandlerEntity.builder().inputIndexMap(Map.of(2, 0, 3, 1)).build();
		this.pointers = new ModelHolder[] {FaceInitializer.hunter_small_holder, FaceInitializer.aligner_106_holder, FaceInitializer.blur_holder, FaceInitializer.headpose_holder};
	}

	@Override
	protected BlurHeadposeSmall[] readModelResult(ModelResult modelResult) {
		PointerByReference keson = modelResult.getResult();
		if(keson == null || keson.getValue() == null)
			return new BlurHeadposeSmall[0];
		
		Pointer targets = KestrelApi.keson_get_object_item(keson.getValue(), "targets");
		int arr_size = KestrelApi.keson_array_size(targets);

		BlurHeadposeSmall[] result = new BlurHeadposeSmall[arr_size];
		for(int index = 0 ; index < arr_size; index ++) {
			result[index] = new BlurHeadposeSmall();
			Pointer target = KestrelApi.keson_get_array_item(targets, index);
			UtilsReader.readHunterData(target, result[index]);
			
			result[index].setAlignerConfidence(KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "confidence_1")));
			
			result[index].blur  = (float)KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "blur"));
			result[index].roll  = (float)KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "roll"));
			result[index].pitch = (float)KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "pitch"));
			result[index].yaw   = (float)KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "yaw"));
			
			result[index].score = -1f;
			if(result[index].pitch >= -90 && result[index].pitch <= 90
					&& result[index].yaw >= -90 && result[index].yaw <= 90) {
				result[index].score = (float)(result[index].getConfidence() * 
						        result[index].getAlignerConfidence() * 
								Math.min(result[index].getBlur(), 1.0) * 
								Math.exp(-10 * result[index].yaw * result[index].yaw / 8100) * 
								Math.exp(-4  * result[index].pitch * result[index].pitch / 8100));
			}
		}
		
		return result;
		
	}
	
	@Data
	@EqualsAndHashCode(callSuper=false)
	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class BlurHeadposeSmall extends Detection{
		/** 置信度*/
		private double alignerConfidence;
		/** 模糊度*/
		private float blur;
		/** roll*/
		private float roll;
		/** pitch*/
		private float pitch;
		/** yaw*/
		private float yaw;
		/**质量分*/
		private float score;
	}
}
