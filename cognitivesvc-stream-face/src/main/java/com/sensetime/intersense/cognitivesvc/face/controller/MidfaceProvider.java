package com.sensetime.intersense.cognitivesvc.face.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveParam;
import com.sensetime.intersense.cognitivesvc.server.mapper.CognitiveParamRepository;
import com.sensetime.intersense.cognitivesvc.server.entities.QualityRes;
import com.sensetime.intersense.cognitivesvc.server.properties.SfdProperties;
import com.sensetime.intersense.cognitivesvc.server.utils.*;
import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.lib.weblib.utils.Baggages;
import com.sensetime.intersense.cognitivesvc.face.handler.*;
import com.sensetime.intersense.cognitivesvc.face.handler.AttributeModelHandler.Attribute;
import com.sensetime.intersense.cognitivesvc.face.handler.BlurHeadposeModelHandler.BlurHeadpose;
import com.sensetime.intersense.cognitivesvc.face.handler.FeatureModelHandler.Feature;
import com.sensetime.intersense.cognitivesvc.seekerface.controller.MidfaceSeekerFaceProvider;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;
import com.sensetime.intersense.cognitivesvc.strangerface.service.StrangerFaceSeekerFacade;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sensetime.storage.service.FileAccessor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SfdPersonService;
import com.sensetime.intersense.cognitivesvc.server.entities.sfd.FeatureItem;
import com.sensetime.intersense.cognitivesvc.server.entities.sfd.InsightFeature;

@Slf4j
@RestController("faceMidfaceProvider")
@RequestMapping(value = "/cognitive/face/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "MidfaceProvider", description = "face controller")
@SuppressWarnings("rawtypes")
public class MidfaceProvider extends BaseProvider {
    
    @Autowired
    @Qualifier("faceFeatureModelHandler")
    private FeatureModelHandler featureModelHandler;
    
    @Autowired
    private RoiFeatureModelHandler roiFeatureModelHandler;
    
    @Autowired
    private BlurHeadposeModelHandler blurHeadposeModelHandler;
    
    @Autowired
    private AttributeModelHandler attributeModelHandler;
    
    @Autowired
    private PersonFaceFeatureRepository personFaceFeatureMapper;
    
    @Autowired
    private MidfaceSeekerFaceProvider midfaceSeekerFaceProvider;
    
    @Autowired
    private SeekerFaceFacade seekerFacade;
    
    @Autowired
    private StrangerFaceSeekerFacade strangerFaceFacade;
    
    @Autowired
    @Qualifier("roiFeatureModelHandler")
    private RoiFeatureModelHandler RoiFeatureModelHandler;
    
    @Autowired
    private RoiFeatureAttributeModelHandler roiFeatureAttributeModelHandler;
    
    @Autowired
    private CognitiveParamRepository mapper;

    @Value("${spring.application.name}")
    private String appName;

    @Autowired
    private Broadcaster broadcastService;

    @Value("${preMakeDirs}")
    private String preMakeDirs;

    @Autowired
    FileAccessor fileAccessor;

    @Autowired
    private BlurHeadposeModelPipeline blurHeadposeModelPipeline;
    
    @Autowired
    private SfdPersonService sfdPersonService;

    @Autowired
    protected SfdProperties sfdProperties;

    @Operation(summary = "从人脸图片提取特征", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/retrieveFaceFeature", method = RequestMethod.GET)
    public BaseRes<Map> retrieveFaceFeature(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
                                            @Parameter(required = true, name = "personId", description = "人员UUID") @RequestParam String personId,
                                            @Parameter(required = true, name = "personCnName", description = "人员中文名争") @RequestParam String personCnName,
                                            @Parameter(required = false, name = "personEnName", description = "人员英文名争") @RequestParam(value = "personEnName", required = false) String personEnName,
                                            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag,
                                            @Parameter(required = false, name = "needFeature", description = "是否返回特征字符串") @RequestParam(value = "needFeature", required = false) Boolean needFeature) {
        List<PersonFaceFeature> list = personFaceFeatureMapper.findByPersonUuid(personId);
        if (list.size() > 0)
            midfaceSeekerFaceProvider.deleteByPid(List.of(personId), null, null);
        
        Map<String, Object> data = new HashMap<>();
        data.put("modelVersion", Initializer.modelPathMap.get("feature_module"));
        Feature features[] = featureModelHandler.extractByPath(figureImageUrl);
        
        try {
            String deptid = Baggages.getDeptId();
            PersonFaceFeature pff = new PersonFaceFeature();
            pff.setAvatarImageUrl(figureImageUrl);
            pff.setCreateUser(CREATE_MODIFY_USER);
            pff.setPrivilege("*".equals(deptid) ? "0" : deptid);
            pff.setPersonCnName(personCnName);
            if (!StringUtils.isBlank(personEnName)) {
                pff.setPersonEnName(personEnName);
            }
            
            pff.setCreateTs(new Date());
            pff.setLastModTs(new Date());
            pff.setTag(tag);
            pff.setPersonUuid(personId);
            pff.setSts(STS_VALID);
            
            if (ArrayUtils.isNotEmpty(features)) {
                //取最大脸
                Feature feature = Arrays.stream(features).max(Comparator.comparing(r -> (int)(r.getHunter().getHeight() * r.getHunter().getWidth()))).get();
                String featureString = FaissSeeker.featureToString(feature.getFeature());
                pff.setImageFeature(featureString);
                data.put("feature", Boolean.TRUE.equals(needFeature) ? featureString : "");
                pff.setModelVersion(Initializer.modelPathMap.get("feature_module"));
            } else if (Utils.instance.retrieveType == 1) {
                String featureString = FaissSeeker.featureToString(new float[256]);
                pff.setImageFeature(featureString);
                pff.setModelVersion("FAKE FEATURE");
                data.put("feature", Boolean.TRUE.equals(needFeature) ? featureString : "");
            } else if (Utils.instance.retrieveType == 0) {
                throw new BusinessException("3001", "no face in the image.[" + figureImageUrl + "]");
            } else {
                throw new RuntimeException("retrieveType is not 0 or 1.");
            }
            
            personFaceFeatureMapper.saveAndFlush(pff);
        } catch (Exception e) {
            log.error("error={}", e.getMessage(), e);
            throw new BusinessException(RESP_SERVER_ERROR_CODE, e.getMessage());
        }
        
        return BaseRes.success(data);
    }

    @Operation(summary = "检验人脸图片质量", method = "GET")
    @RequestMapping(value = "/verifyImageQuality", method = RequestMethod.GET)
    public BaseRes<Map> verifyImageQuality(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) {
        Map<String, Object> data = new HashMap<>();
        data.put("score", 0.0);
        
        try {
            BlurHeadpose blurHeadposes[] = blurHeadposeModelHandler.extractByPath(figureImageUrl);
            if (ArrayUtils.isEmpty(blurHeadposes) || blurHeadposes.length != 1)
                throw new RuntimeException("no face or more than one face. face count : [" + blurHeadposes.length + "]");
            
            data.put("score", blurHeadposes[0].getScore());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return BaseRes.success(data);
    }

    @Operation(summary = "检验人脸图片质量-多人脸", method = "GET")
    @RequestMapping(value = "/verifyImageMultiFaceQuality", method = RequestMethod.GET)
    public BaseRes<List<QualityRes>> verifyImageMultiFaceQuality(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) {
        List<QualityRes> result = new ArrayList();
        try {
            BlurHeadposeModelPipeline.FaceBody[] blurHeadposes = blurHeadposeModelPipeline.extractByPath(figureImageUrl);
//            if (ArrayUtils.isEmpty(blurHeadposes) || blurHeadposes.length != 1)
//                throw new RuntimeException("no face or more than one face. face count : [" + blurHeadposes.length + "]");
            if(ArrayUtils.isEmpty(blurHeadposes)){
                log.warn("verifyImageMultiFaceQuality figureImageUrl {}, has no face",figureImageUrl);
                return BaseRes.success(new ArrayList<>());
            }
            for (int i = 0; i < blurHeadposes.length; i++) {
                QualityRes qualityRes = QualityRes.builder()
                        .score((float) blurHeadposes[i].getIntegrateQuality())
                        .detect(blurHeadposes[i].getHunter())
                        .id(blurHeadposes[i].getId())
                        .quality(blurHeadposes[i].getQuality())
                        .confidence(blurHeadposes[i].getConfidence())
                        .alignerConfidence(blurHeadposes[i].getAlignerConfidence())
                        .build();
                result.add(qualityRes);
            }

        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        }

        return BaseRes.success(result);
    }

    @Operation(summary = "获取库内的所有personids", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/retrieveAllPersonIds", method = RequestMethod.GET)
    public BaseRes<List<Integer>> retrieveAllPersonIds() {
        String[] deptids = Baggages.getAllDeptIds();
        
        if (ArrayUtils.contains(deptids, "*"))
            deptids = null;
        
        List<Integer> list;
        
        if (ArrayUtils.isNotEmpty(deptids)) {
            list = personFaceFeatureMapper.queryIdsByDeptids(Lists.newArrayList(deptids));
        } else {
            list = personFaceFeatureMapper.queryIds();
        }
        
        return BaseRes.success(list);
    }

    @Operation(summary = "获取库内的所有personids return uuid", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/retrieveAllPersonIdsUuid", method = RequestMethod.GET)
    public BaseRes<List<String>> retrieveAllPersonIdsUuid() {
        String[] deptids = Baggages.getAllDeptIds();
        
        if (ArrayUtils.contains(deptids, "*"))
            deptids = null;
        
        List<String> list;
        
        if (ArrayUtils.isNotEmpty(deptids)) {
            list = personFaceFeatureMapper.queryIdsByDeptidsUuid(Lists.newArrayList(deptids));
        } else {
            list = personFaceFeatureMapper.queryIdsUuid();
        }
        
        return BaseRes.success(list);
    }

    @Operation(summary = "【处理转发请求的，不暴露】以图搜人，直接用图片base64，不应用于压测", method = "POST", hidden = true)
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/compareFaceImage/forward/local", method = RequestMethod.POST)
    public BaseRes compareFaceImageForwardHandler(@RequestBody Map<String, Object> param) throws IOException {

        log.info(">>> [compareFaceImageForwardHandler] param: {}", JSON.toJSONString(param));
        String figureImageBase64 = (String) param.get("figureImageBase64");
        String personType = (String) param.get("personType");
        String personGroup = (String) param.get("personGroup");
        String tag = (String) param.get("tag");
        Integer count = (Integer) param.get("count");

        Double _threshold = (Double) param.get("threshold");
        Float threshold = _threshold != null ? _threshold.floatValue() : null;
        Boolean faceAttr = (Boolean) param.get("faceAttr");

        //放到了dev/shm共享内存 里面存储，会有 memory leak，不应用于压测
        File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

        try {
            return compareFaceIdentity(tmpImage.getAbsolutePath(), personType, personGroup, tag, count, threshold, faceAttr);
        } finally {
            tmpImage.delete();
        }
    }


    @Operation(summary = "以图搜人，直接用图片base64，不应用于压测", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/compareFaceImage", method = RequestMethod.POST)
    public BaseRes compareFaceImage(
            @Parameter(required = true, name = "figureImageBase64", description = "图片") @RequestParam("figureImageBase64") String figureImageBase64,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold,
            @Parameter(required = false, name = "faceAttr", description = "返回属性") @RequestParam(value = "faceAttr", required = false) Boolean faceAttr) throws IOException {

        
        if(!Utils.instance.featureSearchSfd) {
            // 如果当前节点正在重建索引，则转发请求到其他节点
            String containerHostname = System.getenv("HOSTNAME");
            CognitiveParam reindexIdentifier = mapper.findById("reindexIdentifier").get();
            if (reindexIdentifier != null && containerHostname != null && reindexIdentifier.getSValue().equalsIgnoreCase(containerHostname)) {
                log.info(">>> [compareFaceImage by base64] faiss reindexing! containerHostname: {}", containerHostname);

                Map<String, Object> varibles = new HashMap<String, Object>();
                varibles.put("figureImageBase64", figureImageBase64);
                varibles.put("personType", personType);
                varibles.put("personGroup", personGroup);
                varibles.put("tag", tag);
                varibles.put("count", count);
                varibles.put("threshold", threshold);
                varibles.put("faceAttr", faceAttr);

                List<BaseRes> resList = broadcastService.postForObjectExcludeSelf(appName, "/cognitive/face/compareFaceImage/forward/local", varibles, BaseRes.class, 3);

                if (log.isDebugEnabled())
                    log.debug(">>> [compareFaceImage by base64] forward search result list= {}", JSON.toJSONString(resList));

                return resList.stream().findFirst().orElse(BaseRes.success());
            }
        }

        //放到了dev/shm共享内存 里面存储，会有 memory leak，不应用于压测
        File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
        
        try {
            return compareFaceIdentity(tmpImage.getAbsolutePath(), personType, personGroup, tag, count, threshold, faceAttr);
        } finally {
            tmpImage.delete();
        }
    }

    @Operation(summary = "添加陌生人，直接用图片base64", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/strangerIsHere", method = RequestMethod.POST)
    public BaseRes strangerIsHere(
            @Parameter(required = true, name = "figureImageBase64", description = "图片") @RequestParam("figureImageBase64") String figureImageBase64,
            @Parameter(required = false, name = "trackId", description = "trackId") @RequestParam(value = "trackId", required = false) Integer trackId,
            @Parameter(required = false, name = "quality", description = "得分阈值") @RequestParam(value = "quality", required = false) Float quality,
            @Parameter(required = false, name = "deviceId", description = "最多搜寻数量") @RequestParam(value = "deviceId", required = false) String deviceId,
            @Parameter(required = false, name = "personAge", description = "年龄") @RequestParam(value = "personAge", required = false) String personAge,
            @Parameter(required = false, name = "personSex", description = "性别") @RequestParam(value = "personSex", required = false) String personSex
    ) throws IOException {
        // 调用该接口的是待创建的陌生人，所以直接存到passer_face下面。
        List<String> preMakeDirList = Arrays.asList(preMakeDirs.split(","));
        File descImage = ImageUtils.newFileWithMkdir("passer_face", preMakeDirList.contains("passer_face"));
        ImageUtils.storeImageToFileName(figureImageBase64,descImage.getAbsolutePath());

        //File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
        
        try {
            log.info("strangerIsHere{}", trackId, quality);
            if(trackId == null){
                trackId = -1;
            }
            if(quality == null){
                quality = 0.0f;
            }
            return strangerIsHereIdentity(descImage.getAbsolutePath(), trackId, quality, deviceId,personAge, personSex);
        } finally {
            //descImage.delete();
        }
    }


    @Operation(summary = "添加陌生人，直接用图片Url", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/strangerIsHereByImageUrl", method = RequestMethod.POST)
    public BaseRes strangerIsHereByImageUrl(
            @Parameter(required = true, name = "figureImageUrl", description = "图片") @RequestParam("figureImageUrl") String figureImageUrl,
            @Parameter(required = false, name = "trackId", description = "trackId") @RequestParam(value = "trackId", required = false) Integer trackId,
            @Parameter(required = false, name = "quality", description = "得分阈值") @RequestParam(value = "quality", required = false) Float quality,
            @Parameter(required = false, name = "deviceId", description = "deviceId") @RequestParam(value = "deviceId", required = false) String deviceId,
            @Parameter(required = false, name = "personAge", description = "年龄") @RequestParam(value = "personAge", required = false) String personAge,
            @Parameter(required = false, name = "personSex", description = "性别") @RequestParam(value = "personSex", required = false) String personSex
    ) throws IOException {
        // 调用该接口的是待创建的陌生人，所以直接存到passer_face下面。
        try {
            String finalPath = figureImageUrl;
            if(!figureImageUrl.contains("passer_face")){
                try {
                    finalPath = fileAccessor.cpImage(figureImageUrl,"passer_face" );
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            log.info("strangerIsHere{}", trackId, quality);
            if(trackId == null){
                trackId = -1;
            }
            if(quality == null){
                quality = 0.0f;
            }
            return strangerIsHereIdentity(finalPath, trackId, quality, deviceId,personAge, personSex);
        } finally {
            //descImage.delete();
        }
    }
    
    private BaseRes<Map<String, Object>> strangerIsHereIdentity(String figureImageUrl, int trackId, float quality, String deviceId, String personAge,String personSex) {
        
        
        Map<String, Object> data = new HashMap<>();
        FeatureAttributeModelHandler.FeatureAttribute[] features = roiFeatureAttributeModelHandler.extractByPath(figureImageUrl);
        if (ArrayUtils.isEmpty(features))
            throw new BusinessException("3001", "no face in the image.[" + figureImageUrl + "]");
        
        
        StrangerFaceObject.StrangerFaceObjectBuilder builder = StrangerFaceObject.builder().id(-1)
                .feature(features[0].getFeature())
                .trackId(trackId)
                .infra(VideoStreamInfra.builder().deviceId(deviceId).privilege("0,0").build())
                .imagePath(figureImageUrl)
                .quality(quality);

        if(StringUtils.isNotBlank(personAge) && StringUtils.isNotBlank(personSex)){
            builder.sex(personSex).age(personAge);

        }else {

            try {
                Map<String, Number> gender = (Map<String, Number>) features[0].getAttribute().get("gender_code");
                builder = builder.sex(gender.get("male").floatValue() > gender.get("female").floatValue() ? "0" : "1")
                        .age(String.valueOf(((Map<String, Float>) features[0].getAttribute().get("st_age_value")).get("st_age_value")));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            String personUUID = strangerFaceFacade.strangerIsHere(builder.build());
            if (personUUID != null) {
                data.put("personUUID", personUUID);
                data.put("avatar", figureImageUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseRes.success(data);
        
    }

    @Operation(summary = "以图搜人NoHunter，直接用图片base64", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/compareFaceImageRoi", method = RequestMethod.POST)
    public BaseRes compareFaceImageRoi(
            @Parameter(required = true, name = "figureImageBase64", description = "图片") @RequestParam("figureImageBase64") String figureImageBase64,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold,
            @Parameter(required = false, name = "faceAttr", description = "返回属性") @RequestParam(value = "faceAttr", required = false) Boolean faceAttr) throws IOException {

        File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
        String figureImageUrl = tmpImage.getAbsolutePath();
        try {
            if (seekerFacade == null)
                throw new RuntimeException("seekerFacade is null, please import cognitivesvc-seeker");
            
            if (count != null && count <= 0)
                count = null;
            
            List<Map> result = new ArrayList<Map>();
            Feature features[] = roiFeatureModelHandler.extractByPath(figureImageUrl);
            if (ArrayUtils.isEmpty(features))
                throw new RuntimeException("no face in the image.[" + figureImageUrl + "]");
            
            String[] deptids = Baggages.getAllDeptIds();
//
//            if (ArrayUtils.contains(deptids, "*"))
//                deptids = null;
            
            
            if ("Target".equals(personType)) {
                seekerFacade.findPerson(PersonParam.builder()
                                .feature(features[0].getFeature())
                                .count(count)
                                .personGroups(personGroup == null ? null : personGroup.split(","))
                                .tags(tag == null ? null : tag.split(","))
                                .threshold(threshold)
                                .figureImageUrl(figureImageUrl)
                                .deptIds(deptids)
                                .build())
                        .stream()
                        .map(item -> {
                            Map map = new HashMap();
                            map.put("personID", item.getLeft().pid);
                            map.put("targetType", "Target");
                            map.put("score", item.getValue());
                            return map;
                        })
                        .forEach(result::add);
            } else if ("Passer".equals(personType)) {
                seekerFacade.findPasser(PasserParam.builder()
                                .feature(features[0].getFeature())
                                .count(count)
                                .deptIds(deptids)
                                .threshold(threshold)
                                .build())
                        .stream()
                        .map(item -> {
                            Map map = new HashMap();
                            map.put("personID", item.getLeft().pid);
                            map.put("targetType", "Passer");
                            map.put("score", item.getValue());
                            return map;
                        })
                        .forEach(result::add);
            } else {
                seekerFacade.findPerson(PersonParam.builder()
                                .feature(features[0].getFeature())
                                .count(count)
                                .personGroups(personGroup == null ? null : personGroup.split(","))
                                .tags(tag == null ? null : tag.split(","))
                                .threshold(threshold)
                                .figureImageUrl(figureImageUrl)
                                .deptIds(deptids)
                                .build())
                        .stream()
                        .map(item -> {
                            Map map = new HashMap();
                            map.put("personID", item.getLeft().pid);
                            map.put("targetType", "Target");
                            map.put("score", item.getValue());
                            return map;
                        })
                        .forEach(result::add);
                
                if (result.isEmpty()) {
                    seekerFacade.findPasser(PasserParam.builder()
                                    .feature(features[0].getFeature())
                                    .count(count)
                                    .deptIds(deptids)
                                    .threshold(threshold)
                                    .build())
                            .stream()
                            .map(item -> {
                                Map map = new HashMap();
                                map.put("personID", item.getLeft().pid);
                                map.put("targetType", "Passer");
                                map.put("score", item.getValue());
                                return map;
                            })
                            .forEach(result::add);
                }
            }
            
            if (Boolean.TRUE.equals(faceAttr) && result.size() > 0) {
                Attribute sttributes[] = attributeModelHandler.extractByPath(figureImageUrl);
                if (ArrayUtils.isNotEmpty(sttributes))
                    result.get(0).put("attribute", sttributes[0].getAttribute());
            }
            
            return BaseRes.success(result);
        } finally {
            tmpImage.delete();
        }
    }

    @Operation(summary = "以图搜人，直接用图片流", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/compareFaceStream", method = RequestMethod.POST)
    public BaseRes compareFaceImage(
            @Parameter(required = true, name = "figureImage", description = "图片") @RequestParam("figureImage") MultipartFile figureImage,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold,
            @Parameter(required = false, name = "faceAttr", description = "返回属性") @RequestParam(value = "faceAttr", required = false) Boolean faceAttr) throws IOException {


        File tmpImage = new File("/tmp/csf_" + ThreadLocalRandom.current().nextLong() + ".jpg");
        figureImage.transferTo(tmpImage);
        try {
            return compareFaceIdentity(tmpImage.getAbsolutePath(), personType, personGroup, tag, count, threshold, faceAttr);
        } finally {
            tmpImage.delete();
        }
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "以图搜人", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/compareFaceIdentity", method = RequestMethod.GET)
    public BaseRes<List<Map>> compareFaceIdentity(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag,逗号分隔") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold,
            @Parameter(required = false, name = "faceAttr", description = "返回属性") @RequestParam(value = "faceAttr", required = false) Boolean faceAttr) {
        if (seekerFacade == null)
            throw new RuntimeException("seekerFacade is null, please import cognitivesvc-seeker");
        
        if (count != null && count <= 0)
            count = null;
        
        List<Map> result = new ArrayList<Map>();
        Feature[] features = featureModelHandler.extractByPath(figureImageUrl);
        if (ArrayUtils.isEmpty(features)) {
            features = RoiFeatureModelHandler.extractByPath(figureImageUrl);
            if (ArrayUtils.isEmpty(features))
                throw new BusinessException("3001", "no face in the image compare.[" + figureImageUrl + "]");
        }
        
        String[] deptids = Baggages.getAllDeptIds();
        //??? 为什么？        // 目前删除会在队列里，延迟删除，所以先用32 去查 = null的话只会查count个
//        if (ArrayUtils.contains(deptids, "*"))
//            deptids = null;
//        if(Utils.instance.featureSearchSfd && "Passer".equals(personType)){
//            throw new BusinessException("3001", "not support passer from 2.14.[" + figureImageUrl + "]");
//        }
        if ("Target".equals(personType)) {
            seekerFacade.findPerson(PersonParam.builder()
                            .feature(features[0].getFeature())
                            .count(count)
                            .personGroups(personGroup == null ? null : personGroup.split(","))
                            .figureImageUrl(figureImageUrl)
                            .tags(tag == null ? null : tag.split(","))
                            .threshold(threshold)
                            .deptIds(deptids)
                            .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("avatar", item.getLeft().avatar);
                        map.put("targetType", "Target");
                        map.put("score", item.getValue());
                        return map;
                    })
                    .forEach(result::add);
        } else if ("Passer".equals(personType)) {
            seekerFacade.findPasser(PasserParam.builder()
                            .feature(features[0].getFeature())
                            .count(count)
                            .deptIds(deptids)
                            .threshold(threshold)
                            .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("targetType", "Passer");
                        map.put("avatar", item.getLeft().avatar);
                        map.put("score", item.getValue());
                        return map;
                    })
                    .forEach(result::add);
        } else {
            seekerFacade.findPerson(PersonParam.builder()
                            .feature(features[0].getFeature())
                            .count(count)
                            .personGroups(personGroup == null ? null : personGroup.split(","))
                            .tags(tag == null ? null : tag.split(","))
                            .threshold(threshold)
                            .figureImageUrl(figureImageUrl)
                            .deptIds(deptids)
                            .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("targetType", "Target");
                        map.put("score", item.getValue());
                        map.put("avatar", item.getLeft().avatar);
                        return map;
                    })
                    .forEach(result::add);
            
            if (result.isEmpty() ) {
                seekerFacade.findPasser(PasserParam.builder()
                                .feature(features[0].getFeature())
                                .count(count)
                                .deptIds(deptids)
                                .threshold(threshold)
                                .build())
                        .stream()
                        .map(item -> {
                            Map map = new HashMap();
                            map.put("personID", item.getLeft().pid);
                            map.put("targetType", "Passer");
                            map.put("score", item.getValue());
                            map.put("avatar", item.getLeft().avatar);
                            return map;
                        })
                        .forEach(result::add);
            }
        }
        
        if (Boolean.TRUE.equals(faceAttr) && result.size() > 0) {
            Attribute sttributes[] = attributeModelHandler.extractByPath(figureImageUrl);
            if (ArrayUtils.isNotEmpty(sttributes))
                result.get(0).put("attribute", sttributes[0].getAttribute());
        }
        
        return BaseRes.success(result);
    }


    @SuppressWarnings("unchecked")
    @Operation(summary = "多数据库搜索", method = "POST")
    @Parameters({
        @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
        @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/searchMulti", method = RequestMethod.POST)
    public BaseRes<List<Map>> searchMulti(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = false, name = "personGroups", description = "用户群组列表，逗号分隔的多个dbname") @RequestParam(value = "personGroups", required = true) String personGroups,
            @Parameter(required = false, name = "count", description = "每个库最多返回数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold,
            @Parameter(required = false, name = "faceAttr", description = "返回属性") @RequestParam(value = "faceAttr", required = false) Boolean faceAttr) {
        if (seekerFacade == null)
            throw new RuntimeException("seekerFacade is null, please import cognitivesvc-seeker");
        
        if (count != null && count <= 0)
            count = null;
        
        if (!Utils.instance.featureSearchSfd) {
            throw new BusinessException("3001", "searchMulti only supports featureSearchSfd=true mode.");
        }
        
        List<Map> result = new ArrayList<Map>();
        Feature[] features = featureModelHandler.extractByPath(figureImageUrl);
        if (ArrayUtils.isEmpty(features)) {
            features = RoiFeatureModelHandler.extractByPath(figureImageUrl);
            if (ArrayUtils.isEmpty(features))
                throw new BusinessException("3001", "no face in the image.[" + figureImageUrl + "]");
        }
        
        String[] deptids = Baggages.getAllDeptIds();
        
        // 处理多个数据库查询
        seekerFacade.findPerson(PersonParam.builder()
                        .feature(features[0].getFeature())
                        .count(count)
                        .personGroups(personGroups.split(","))
                        .threshold(threshold)
                        .figureImageUrl(figureImageUrl)
                        .deptIds(deptids)
                        .build())
                .stream()
                .map(item -> {
                    Map map = new HashMap();
                    map.put("personID", item.getLeft().pid);
                    map.put("avatar", item.getLeft().avatar);
                    map.put("targetType", "Target");
                    map.put("score", item.getValue());
                    return map;
                })
                .forEach(result::add);
                
        if (Boolean.TRUE.equals(faceAttr) && result.size() > 0) {
            Attribute sttributes[] = attributeModelHandler.extractByPath(figureImageUrl);
            if (ArrayUtils.isNotEmpty(sttributes))
                result.get(0).put("attribute", sttributes[0].getAttribute());
        }
        
        return BaseRes.success(result);
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "以图搜人", method = "POST")
    @RequestMapping(value = "/compareFaceIdentityPost", method = RequestMethod.POST)
    public BaseRes<List<Map>> compareFaceIdentityPost(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag,逗号分隔") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold,
            @Parameter(required = false, name = "faceAttr", description = "返回属性") @RequestParam(value = "faceAttr", required = false) Boolean faceAttr) {
        if (seekerFacade == null)
            throw new RuntimeException("seekerFacade is null, please import cognitivesvc-seeker");

        if (count != null && count <= 0)
            count = null;

        List<Map> result = new ArrayList<Map>();
        Feature[] features = featureModelHandler.extractByPath(figureImageUrl);
        if (ArrayUtils.isEmpty(features)) {
            features = RoiFeatureModelHandler.extractByPath(figureImageUrl);
            if (ArrayUtils.isEmpty(features))
                throw new BusinessException("3001", "no face in the image compare.[" + figureImageUrl + "]");
        }

        String[] deptids = Baggages.getAllDeptIds();
//        // null??
//        if (ArrayUtils.contains(deptids, "*"))
//            deptids = null;

        if ("Target".equals(personType)) {
            seekerFacade.findPerson(PersonParam.builder()
                    .feature(features[0].getFeature())
                    .count(count)
                    .personGroups(personGroup == null ? null : personGroup.split(","))
                    .tags(tag == null ? null : tag.split(","))
                    .threshold(threshold)
                    .figureImageUrl(figureImageUrl)
                    .deptIds(deptids)
                    .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("avatar", item.getLeft().avatar);
                        map.put("targetType", "Target");
                        map.put("score", item.getValue());
                        return map;
                    })
                    .forEach(result::add);
        } else if ("Passer".equals(personType)) {
            seekerFacade.findPasser(PasserParam.builder()
                    .feature(features[0].getFeature())
                    .count(count)
                    .deptIds(deptids)
                    .threshold(threshold)
                    .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("targetType", "Passer");
                        map.put("avatar", item.getLeft().avatar);
                        map.put("score", item.getValue());
                        return map;
                    })
                    .forEach(result::add);
        } else {
            seekerFacade.findPerson(PersonParam.builder()
                    .feature(features[0].getFeature())
                    .count(count)
                    .personGroups(personGroup == null ? null : personGroup.split(","))
                    .tags(tag == null ? null : tag.split(","))
                    .threshold(threshold)
                    .deptIds(deptids)
                    .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("targetType", "Target");
                        map.put("score", item.getValue());
                        map.put("avatar", item.getLeft().avatar);
                        return map;
                    })
                    .forEach(result::add);

            if (result.isEmpty()) {
                seekerFacade.findPasser(PasserParam.builder()
                        .feature(features[0].getFeature())
                        .count(count)
                        .deptIds(deptids)
                        .threshold(threshold)
                        .build())
                        .stream()
                        .map(item -> {
                            Map map = new HashMap();
                            map.put("personID", item.getLeft().pid);
                            map.put("targetType", "Passer");
                            map.put("score", item.getValue());
                            map.put("avatar", item.getLeft().avatar);
                            return map;
                        })
                        .forEach(result::add);
            }
        }

        if (Boolean.TRUE.equals(faceAttr) && result.size() > 0) {
            Attribute sttributes[] = attributeModelHandler.extractByPath(figureImageUrl);
            if (ArrayUtils.isNotEmpty(sttributes))
                result.get(0).put("attribute", sttributes[0].getAttribute());
        }

        return BaseRes.success(result);
    }

    @RequestMapping(value = "/compareFaceByMultiGroupByBase64/forward/local", method = RequestMethod.POST)
    public BaseRes compareFaceByMultiGroupByBase64ForwardHandler(@RequestBody Map<String, Object> param) throws IOException {

        log.info(">>> [compareFaceByMultiGroupByBase64ForwardHandler] param: {}", JSON.toJSONString(param));
        String figureImageBase64 = (String) param.get("figureImageBase64");
        String personType = (String) param.get("personType");
        List<String> groups = (List<String>) param.get("groups");
        String tag = (String) param.get("tag");
        Integer count = (Integer) param.get("count");

        List<Float> thresholds = (List<Float>) param.get("thresholds");
        Boolean useCache = (Boolean) param.get("useCache");

        //放到了dev/shm共享内存 里面存储，会有 memory leak，不应用于压测
        File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

        try {
            return compareFaceByMultiGroup(tmpImage.getAbsolutePath(), groups, thresholds, count, useCache);
        } finally {
            tmpImage.delete();
        }
    }

    @Operation(summary = "多人员组中以图搜人，直接用图片base64", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/compareFaceByMultiGroupByBase64", method = RequestMethod.POST)
    public BaseRes compareFaceByMultiGroupByBase64(
            @Parameter(name = "figureImageBase64", description = "图片") @RequestParam("figureImageBase64") String figureImageBase64,
            @Parameter(name = "groups", description = "用户群组") @RequestParam(value = "groups") List<String> groups,
            @Parameter(name = "thresholds", description = "得分阈值") @RequestParam(value = "thresholds") List<Float> thresholds,
            @Parameter(name = "count", description = "最多搜寻数量") @RequestParam(value = "count") int count,
            @Parameter(required = false, name = "useCache", description = "使用group缓存") @RequestParam(value = "useCache", required = false, defaultValue = "false") Boolean useCache
    ) throws IOException {

        // 如果当前节点正在重建索引，则转发请求到其他节点
        String containerHostname = System.getenv("HOSTNAME");
        CognitiveParam reindexIdentifier = mapper.findById("reindexIdentifier").get();
        if(reindexIdentifier != null && containerHostname!= null && reindexIdentifier.getSValue().equalsIgnoreCase(containerHostname)){
            log.info(">>> [compareFaceImage by base64] faiss reindexing! containerHostname: {}", containerHostname);

            Map<String, Object> varibles = new HashMap<String, Object>();
            varibles.put("figureImageBase64", figureImageBase64);
            varibles.put("groups", groups);
            varibles.put("thresholds", thresholds);
            varibles.put("count", count);
            varibles.put("useCache", useCache);

            List<BaseRes> resList = broadcastService.postForObjectExcludeSelf(appName, "/cognitive/face/compareFaceByMultiGroupByBase64/forward/local", varibles, BaseRes.class,3);

            if(log.isDebugEnabled())
                log.debug(">>> [compareFaceByMultiGroupByBase64 by base64] forward search result list= {}", JSON.toJSONString(resList));

            return resList.stream().findFirst().orElse(BaseRes.success());
        }
        //放到了dev/shm共享内存 里面存储，会有 memory leak，不应用于压测
        File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
        
        try {
            return compareFaceByMultiGroup(tmpImage.getAbsolutePath(), groups, thresholds, count, useCache);
        } finally {
            tmpImage.delete();
        }
    }

    @Operation(summary = "多人员组中以图搜人", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/compareFaceByMultiGroup", method = RequestMethod.GET)
    public BaseRes compareFaceByMultiGroup(
            @Parameter(name = "figureImageUrl", description = "图片路径") @RequestParam(value = "figureImageUrl") String figureImageUrl,
            @Parameter(name = "groups", description = "用户群组") @RequestParam(value = "groups") List<String> groups,
            @Parameter(name = "thresholds", description = "得分阈值") @RequestParam(value = "thresholds") List<Float> thresholds,
            @Parameter(name = "count", description = "最多搜寻数量") @RequestParam(value = "count") int count,
            @Parameter(required = false, name = "useGroupCache", description = "使用group缓存") @RequestParam(value = "useGroupCache", required = false, defaultValue = "false") Boolean useGroupCache
    ) {
        if (seekerFacade == null)
            throw new RuntimeException("seekerFacade is null, please import cognitivesvc-seeker");
        
        if (groups.size() != thresholds.size()) {
            log.error("[groups] and [thresholds] size is not equal.");
            throw new BusinessException("4001", "[groups] and [thresholds] size is not equal.");
        }
        
        Feature features[] = featureModelHandler.extractByPath(figureImageUrl);
        if (ArrayUtils.isEmpty(features)) {
            log.error("no face in the image.[" + figureImageUrl + "]");
            throw new BusinessException("3001", "no face in the image.[" + figureImageUrl + "]");
        }
        
        Map<String, List<Pair<String, Float>>> result = Stream.iterate(0, i -> i + 1)
                .limit(groups.size())
                .collect(Collectors.toMap(
                        index -> groups.get(index),
                        index -> {
                            PersonParam param = PersonParam.builder()
                                    .count(count)
                                    .feature(features[0].getFeature())
                                    .threshold(thresholds.get(index))
                                    .personGroups(new String[]{groups.get(index)})
                                    .deptIds(Baggages.getAllDeptIds())
                                    .useGroupCache(useGroupCache)
                                    .figureImageUrl(figureImageUrl)
                                    .build();
                            
                            return seekerFacade.findPerson(param).stream().map(pair -> new ImmutablePair<String, Float>(pair.getLeft().pid, pair.getRight())).collect(Collectors.toList());
                        }));
        
        return BaseRes.success(result);
    }

    @Operation(summary = "增加人员特征到SFD库", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/addPersonToSfd", method = RequestMethod.POST)
    public BaseRes<Map<String, Object>> addPersonToSfd(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = true, name = "personId", description = "人员ID") @RequestParam String personId,
            @Parameter(required = false, name = "personCnName", description = "中文名") @RequestParam(required = false) String personCnName,
            @Parameter(required = false, name = "imageId", description = "imageId") @RequestParam(required = false) String imageId,
            @Parameter(required = false, name = "personEnName", description = "英文名") @RequestParam(required = false) String personEnName,
            @Parameter(required = false, name = "dbId", description = "数据库ID") @RequestParam(required = false) String dbId) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 提取特征
            Feature[] features = featureModelHandler.extractByPath(figureImageUrl);
            
            if (ArrayUtils.isEmpty(features)) {
                features = roiFeatureModelHandler.extractByPath(figureImageUrl);
                if (ArrayUtils.isEmpty(features)) {
                    throw new BusinessException("3001", "no face in the image compare.[" + figureImageUrl + "]");
                }
            }
            
            // 将特征转换为String
            String featureStr = FaissSeeker.featureToString(features[0].getFeature());

            Base64.Encoder encoder = Base64.getEncoder();

            String featureBlob = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeader(
                    FeatureConversionEncryptionUtils.encodeBlob(featureStr, sfdProperties.getSfdEncKey())));
            
            // 构建额外信息
            com.alibaba.fastjson.JSONObject extraInfo = new com.alibaba.fastjson.JSONObject();
            extraInfo.put("avatar", figureImageUrl);
            if (personCnName != null) {
                extraInfo.put("cnName", personCnName);
            }
            if (personEnName != null) {
                extraInfo.put("enName", personEnName);
            }
            
            // 构建特征项
            FeatureItem featureItem = FeatureItem.builder()
                    .key(personId)
                    .seq_id(imageId)
                    .image_id(imageId)
                    .extra_info(extraInfo.toJSONString())
                    .feature(InsightFeature.builder()
                            .type("face")
                            .version(sfdPersonService.getFeatureVersionPublic())
                            .blob(featureBlob)
                            .build())
                    .build();
            
            // 调用batchAdd方法
            boolean success;
            if (dbId != null && !dbId.isEmpty()) {
                success = sfdPersonService.batchAdd(Collections.singletonList(featureItem), dbId);
            } else {
                success = sfdPersonService.batchAdd(Collections.singletonList(featureItem));
            }
            
            result.put("success", success);
            result.put("personId", personId);
            result.put("avatarUrl", figureImageUrl);
            
            if (success) {
                return BaseRes.success(result);
            } else {
                return BaseRes.error("500", "Failed to add person to SFD"+ result);
            }
            
        } catch (Exception e) {
            log.error("Error adding person to SFD: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return BaseRes.error("500", e.getMessage());
        }
    }
}
