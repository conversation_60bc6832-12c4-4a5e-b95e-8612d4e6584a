package com.sensetime.intersense.cognitivesvc.streamface.controller;

import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoIconEntity.VideoIcon;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoIconRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.lib.clientlib.response.BaseRes;


import io.swagger.v3.oas.annotations.Operation;

import java.util.Date;
import java.util.Map;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/cognitive/icon/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="DarwinProvider",description = "icon controller")
public class IconProvider extends BaseProvider {
	
	@Autowired
	private VideoIconRepository mapper;
	
	@Autowired
	private Broadcaster broadcastService;
	
	@Value("${spring.application.name}")
	private String appName;
	
	@Operation(summary = "显示视频配置总数",method = "GET")
	@RequestMapping(value = "/getRenderConfigCount", method = RequestMethod.GET)
	public BaseRes<Long> getRenderConfigCount() {
		return BaseRes.success(mapper.count());
	}
	
	@Operation(summary = "显示视频的配置-分页",method = "GET")
	@RequestMapping(value = "/getRenderConfig", method = RequestMethod.GET)
	public BaseRes<Object> getRenderConfig(@RequestParam(required = false) String person_tag, @RequestParam(required = false) String device_id, @RequestParam int current, @RequestParam int size) {
		Pageable pageable = PageRequest.of(current, size);
		
		Page<VideoIcon> page; 
		
		if(StringUtils.isNotBlank(person_tag) && StringUtils.isNotBlank(device_id)) {
			page = mapper.findByPersonTagAndDeviceId(person_tag, device_id, pageable);
		}else if(StringUtils.isNotBlank(person_tag)) {
			page = mapper.findByPersonTag(person_tag, pageable);
		}else if(StringUtils.isNotBlank(device_id)) {
			page = mapper.findByDeviceId(device_id, pageable);
		}else {
			page = mapper.findAll(pageable);
		}
		
		return BaseRes.success(page.toList());
	}
	
	@Operation(summary = "设置显示视频的配置", method = "POST")
	@RequestMapping(value = "/addOrUpdateRenderConfig", method = RequestMethod.POST)
	public BaseRes<String> addOrUpdateRenderConfig(@RequestBody VideoIcon icon) {
		icon.setUpdateTs(new Date());
		mapper.saveAndFlush(icon);
		broadcastService.getForObject(appName, "/test/icon_reload", Map.of(), String.class);
		return BaseRes.success("OK");
	}
	
	@Operation(summary = "删除显示视频的配置", method = "POST")
	@RequestMapping(value = "/deleteRenderConfig", method = RequestMethod.POST)
	public BaseRes<Integer> deleteRenderConfig(@RequestParam(required = false) String person_tag, @RequestParam(required = false) String device_id) {
		if(StringUtils.isNotBlank(person_tag) && StringUtils.isNotBlank(device_id)) {
			mapper.deleteByPersonTagAndDeviceId(person_tag, device_id);
		}else if(StringUtils.isNotBlank(person_tag)) {
			mapper.deleteByPersonTag(person_tag);
		}else if(StringUtils.isNotBlank(device_id)) {
			mapper.deleteByDeviceId(device_id);
		}
		
		return BaseRes.success(1);
	}
}
