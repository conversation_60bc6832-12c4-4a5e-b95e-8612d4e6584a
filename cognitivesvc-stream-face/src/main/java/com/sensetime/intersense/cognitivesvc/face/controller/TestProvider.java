package com.sensetime.intersense.cognitivesvc.face.controller;

import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.StrangerFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.mapper.StrangerFaceFeatureRepository;


import io.swagger.v3.oas.annotations.Operation;

import java.util.List;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController("faceTestProvider")
@RequestMapping(value = "/test", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="DarwinProvider",description = "Test")
public class TestProvider extends BaseProvider {
	
	@Operation(summary = "test_thres",method = "GET")
	@RequestMapping(value = "/test_thres", method = RequestMethod.GET)
	public float[][] test_thres() {
		return new float[][] {SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint};
	}
	
	@Operation(summary = "load_plugin",method = "GET")
	@RequestMapping(value = "/load_plugin", method = RequestMethod.GET)
	public String load_plugin(@RequestParam String path) {
		String pluginName = KestrelApi.kestrel_plugin_load(path, "");
		return pluginName;
	}
	
	@Autowired
	private StrangerFaceFeatureRepository strangerMapper;
	
	@Operation(summary = "最近一百个陌生人",method = "GET")
	@RequestMapping(value = "/last100stranger", method = RequestMethod.GET, produces = MediaType.TEXT_HTML_VALUE)
    public String last100stranger() {
		List<StrangerFaceFeature> datas = strangerMapper.findAll(PageRequest.of(0, 100, Sort.by("createTs").ascending())).getContent();
		StringBuilder builder = new StringBuilder();
		for(StrangerFaceFeature data : datas) {
			builder.append("<tr>");
			builder.append("<td>"+data.getDeviceId()+"</td>");
			builder.append("<td>"+data.getCreateTs()+"</td>");
			builder.append("<td><img src=\"http://www.stg.intersense.sensetime.com/intersense/"+data.getAvatarImageUrl()+"\"/></td>");
			builder.append("<td>"+data.getImageFeature()+"</td>");
			builder.append("</tr>");
		}
		return "<html><body><table>" + builder + "</table></body></html>";
    }
}
