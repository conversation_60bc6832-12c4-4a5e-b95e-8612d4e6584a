package com.sensetime.intersense.cognitivesvc.streamface.handler;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.face.handler.event.FaceRawEventOutput;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.FeatureConversionEncryptionUtils;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamNonSeen;
import com.sensetime.intersense.cognitivesvc.streamface.zutils.LRUCache;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streamface.zutils.FaceTrack;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class MessageSendHandler {


	private final ConcurrentHashMap<String, Long> onGoingTracksFilter = new ConcurrentHashMap<String, Long>();

	@Autowired
    private FaceRawEventOutput face_raw_event_output;

	@Value("${cognitivesvc.viperEncKey:}")
	private String viperEncKey;

	public void messageSendUnknownCapture(FaceTrack track) {
		if (Utils.instance.imageCaptureSwitch == 1) {
			return;
		}
		//陌生人识别开关关闭 + 无陌生人策略，系统不会产生任何陌生人记录和陌生人档案
		if(Utils.instance.strangerRecognition >0 && track.getStreamFace().getProcessFace().getStrangerPolicy() > 0 ){
			if(Utils.instance.faceRecognitionMode != 2) {
				if(Utils.instance.logged) {
					log.info("messageSendUnknownCapture checkFail in time {}", track);
				}
				return;
			}
		}
		// 周期告警：同一个人员在同一台相机下，指定时间段内只告警1次  这里不告警
		SenseyeEvent event = buildEvent(track, "senseyeFaceEvent", "imageCapture");

		track.getImage().save();
		event.setCaptureImage(track.getImage().getSavedFacePath());
		event.setSceneImage(track.getImage().getSavedScenePath());

		event.setPersonType("Stranger");
		event.setPersonId("-" + track.getTrackId());
		event.setScore(0.0f);
		event.setTargetImage(track.getImage().getSavedFacePath());
		sendMessage(event);
	}

	public void messageSendUnknownDrop(FaceTrack track) {


		SenseyeEvent event = buildEvent(track, "senseyeFaceEvent", "imageCapture");

		track.getImage().save();
		event.setCaptureImage(track.getImage().getSavedFacePath());
		event.setSceneImage(track.getImage().getSavedScenePath());

		event.setPersonType("Filter");
		event.setPersonId("-" + track.getTrackId());
		event.setScore(0.0f);
		event.setTargetImage(track.getImage().getSavedFacePath());
		event.setReason(track.getReason());
		sendMessage(event);
	}

	public void messageSendUnknownDropPattern(FaceTrack track) {

		SenseyeEvent event = buildEvent(track, "senseyeFaceEvent", "patternDetected");

		track.getImage().save();
		event.setCaptureImage(track.getImage().getSavedFacePath());
		event.setSceneImage(track.getImage().getSavedScenePath());

		event.setPersonType("Filter");
		event.setPersonId("-" + track.getTrackId());
		event.setScore(0.0f);
		event.setTargetImage(track.getImage().getSavedFacePath());
		event.setReason(track.getReason());
		sendMessage(event);
	}

	public void messageSendImageCapture(FaceTrack track, boolean hasSaveImage, int changed) {
		if (Utils.instance.imageCaptureSwitch == 1) {
			return;
		}
		SenseyeEvent event = buildEvent(track, "senseyeFaceEvent", "imageCapture");
		if (track.getPersonObj() != null && changed == 1) {
			event.setPersonType("Target");
			event.setPersonId(track.getPersonObj().pid);
			event.setScore(track.getRegPersonScore());
			event.setTargetImage(track.getPersonObj().avatar);
		} else if (track.getPasserObj() != null && changed == 2) {
			//陌生人识别开关关闭(0-开，1-关 默认关) + 无陌生人策略，系统不会产生任何陌生人记录和陌生人档案
			if(Utils.instance.strangerRecognition > 0 && track.getStreamFace().getProcessFace().getStrangerPolicy() > 0 ){
				log.info("strangerRecognition checkFail{}", track);
				if(Utils.instance.faceRecognitionMode != 2) {
					return;
				}
			}
			event.setPersonType("Passer");
			event.setPersonId(track.getPasserObj().pid);
			event.setScore(track.getRegPasserScore());
			event.setTargetImage(track.getPasserObj().avatar);
		}
		//快速模式持续告警 使用全局追踪时长
		Integer eventTrackTime = track.getStreamFace().getProcessFace().getEventTrackTime();
		if(eventTrackTime <= 0 ){
			//track.getStreamFace().getProcessFace().setEventTrackTime(Utils.instance.selectFrameTimeInterVal);
			eventTrackTime = Utils.instance.selectFrameTimeInterVal;
			if(Utils.instance.logged) {
				log.info("setEventTrackTime{},{},{},{}", track, track.getStreamFace().getProcessFace().getEventTrackTime(), Utils.instance.selectFrameTimeInterVal, eventTrackTime);
			}
		}
		//快速模式 周期告警 取最大
		if(track.getStreamFace().getProcessFace().getEventTrackTime() > 0){
             if(eventTrackTime <= Utils.instance.selectFrameTimeInterVal ){
				 eventTrackTime = Utils.instance.selectFrameTimeInterVal;
			 }
		}
		//周期告警：同一个人员在同一台相机下，指定时间段内只告警1次
		String tracFilterId = track.getStreamFace().getDeviceId() + "_track_" +event.getPersonType() + "_" + event.getPersonId();
		if(eventTrackTime > 0 && onGoingTracksFilter.containsKey(tracFilterId)
				&& onGoingTracksFilter.get(tracFilterId) > 0){
			if(( System.currentTimeMillis() - onGoingTracksFilter.get(tracFilterId)) < (eventTrackTime * 1000L) ){
				if(Utils.instance.logged) {
					log.info("onGoingTracksFilter checkFail {},{},{},{},{}", tracFilterId, System.currentTimeMillis(), onGoingTracksFilter.get(tracFilterId), System.currentTimeMillis() - onGoingTracksFilter.get(tracFilterId), Utils.dateFormat.get().format(new Date()));
				}
				if(Utils.instance.faceRecognitionMode != 2) {
					return;
				}
			}
		}
		onGoingTracksFilter.put(tracFilterId, System.currentTimeMillis() );
		//track.setModeTimeInterValTime(System.currentTimeMillis());
		if(!hasSaveImage) {
			track.getImage().save();
		}
		event.setCaptureImage(track.getImage().getSavedFacePath());
		event.setSceneImage(track.getImage().getSavedScenePath());
		event.setFeature(track.getFeature());
		sendMessage(event);
	}

	public void messageSendAttributeUpdate(FaceTrack track) {
		SenseyeEvent event = buildEvent(track, "senseyeFaceEvent", "attributeUpdate");
		// 周期告警：同一个人员在同一台相机下，指定时间段内只告警1次
		if(track.getPersonObj() != null) {
			event.setPersonType("Target");
			event.setPersonId(track.getPersonObj().pid);
			event.setScore(1.0f);
			event.setTargetImage(track.getPersonObj().avatar);
		}else if(track.getPasserObj() != null) {
			//陌生人识别开关关闭 + 无陌生人策略，系统不会产生任何陌生人记录和陌生人档案
			if(Utils.instance.strangerRecognition >0 && track.getStreamFace().getProcessFace().getStrangerPolicy() > 0 ){
				if(Utils.instance.faceRecognitionMode != 2) {
					return;
				}
			}
			event.setPersonType("Passer");
			event.setPersonId(track.getPasserObj().pid);
			event.setScore(1.0f);
			event.setTargetImage(track.getPasserObj().avatar);
		}
		
		event.setCaptureImage(FrameUtils.NOIMAGE);
		event.setSceneImage(FrameUtils.NOIMAGE);
		// 周期告警：同一个人员在同一台相机下，指定时间段内只告警1次
		String tracFilterId = track.getStreamFace().getDeviceId() + "_" +event.getPersonType() + event.getPersonId();
		if(track.getStreamFace().getProcessFace().getEventTrackTime() > 0 && onGoingTracksFilter.containsKey(tracFilterId)
				&& onGoingTracksFilter.get(tracFilterId) > 0){
			if(( System.currentTimeMillis() - onGoingTracksFilter.get(tracFilterId)) < (track.getStreamFace().getProcessFace().getEventTrackTime() * 1000) ){
				if(Utils.instance.logged) {
					log.info("onGoingTracksFilter in time {}", track);
				}
				return;
			}
		}
		onGoingTracksFilter.put(tracFilterId, System.currentTimeMillis() );
		
		sendMessage(event);
	}

	public void messageSendPatternDetect(FaceTrack track, boolean hasSaveImage, int regFlag) {
		SenseyeEvent event = buildEvent(track, "senseyeFaceEvent", "patternDetected");
		if(track.getPersonObj() != null && (regFlag == 1 || regFlag == 0)) {
			event.setPersonType("Target");
			event.setPersonId(track.getPersonObj().pid);
			event.setScore(track.getRegPersonScore());
			event.setTargetImage(track.getPersonObj().avatar);
		}else if(track.getPasserObj() != null  &&  (regFlag == 2 || regFlag == 0)) {
			//陌生人识别开关关闭 + 无陌生人策略，系统不会产生任何陌生人记录和陌生人档案
			if(Utils.instance.strangerRecognition >0 && track.getStreamFace().getProcessFace().getStrangerPolicy() > 0 ){
				if(Utils.instance.faceRecognitionMode != 2) {
					if(Utils.instance.logged) {
						log.info("messageSendPatternDetect checkFail in time {}", Utils.instance.strangerRecognition);
					}
					return;
				}
			}
			event.setPersonType("Passer");
			event.setPersonId(track.getPasserObj().pid);
			event.setScore(track.getRegPasserScore());
			event.setTargetImage(track.getPasserObj().avatar);
			event.setNewPasser(track.isNewPasser());
		}
		if(!Utils.instance.featureSearchSfd) {
			//精准模式 持续告警 使用全局追踪时长
			Integer eventTrackTime = track.getStreamFace().getProcessFace().getEventTrackTime();
//		if(eventTrackTime <= 0 ){
//			//track.getStreamFace().getProcessFace().setEventTrackTime(Utils.instance.selectFrameTimeInterVal);
//			eventTrackTime = Utils.instance.selectFrameTimeInterVal;
//			if(Utils.instance.logged) {
//				log.info("setEventTrackTime Pattern{},{},{},{}", event.getPersonType(), track.getStreamFace().getProcessFace().getEventTrackTime(), Utils.instance.selectFrameTimeInterVal, eventTrackTime);
//			}
//		}
			// 周期告警：同一个人员在同一台相机下，指定时间段内只告警1次
			String tracFilterId = track.getStreamFace().getDeviceId() + "_" + event.getPersonType() + event.getPersonId();
			if (eventTrackTime > 0 && onGoingTracksFilter.containsKey(tracFilterId)
					&& onGoingTracksFilter.get(tracFilterId) > 0) {
				if ((System.currentTimeMillis() - onGoingTracksFilter.get(tracFilterId)) < (eventTrackTime * 1000L)) {
					if (Utils.instance.logged) {
						log.info("onGoingTracksFilterPattern checkFail {},{},{},{}", tracFilterId, System.currentTimeMillis(), onGoingTracksFilter.get(tracFilterId), System.currentTimeMillis() - onGoingTracksFilter.get(tracFilterId));
					}
					if (Utils.instance.faceRecognitionMode != 2) {
						return;
					}
				}
			}
			onGoingTracksFilter.put(tracFilterId, System.currentTimeMillis());
		}

		if(!hasSaveImage) {
			track.getImage().save();
		}
		event.setCaptureImage(track.getImage().getSavedFacePath());
		event.setSceneImage(track.getImage().getSavedScenePath());
		event.setFeature(track.getFeature());
		sendMessage(event);
	}
	
	@SuppressWarnings("unchecked")
	private SenseyeEvent buildEvent(FaceTrack track, String eventName, String eventAction) {
		return  SenseyeEvent.builder()
							.eventName(eventName)
							.eventAction(eventAction)
							.device_id(track.getStreamInfra().getDeviceId())
							.device_tag(track.getStreamInfra().getDeviceTag())
							//.capturedTime(Utils.dateFormat.get().format(new Date()))
							//.capturedTime(Utils.dateFormat_MS.get().format(new Date()))
							.capturedTime(Utils.dateFormat_MS.get().format(track.getFramePts()))
							//.captureImage(track.getImage().getSavedFacePath())
							//.sceneImage(track.getImage().getSavedScenePath())
							.trackid(track.getTrackId())
							.rect((Map<String, Number>)track.getTarget().get("roi"))
							.roiId(track.queryFaceInRoiIds())
							.attributes(track.getAttribute())
							.width(track.getStreamInfra().getRtspWidth())
							.height(track.getStreamInfra().getRtspHeight())
							.framePts(track.getFramePts())
							.frameIndex(track.getFrameIndex())
							.quality(((Number)track.getTarget().getOrDefault("quality", -1)).floatValue())
				            .target(track.getTarget())
				            .frameDecodeCost(track.getFrameDecodeCost())
							.build();
	}
	
	private void sendMessage(SenseyeEvent event) {
		if(event.captureImage == null)
			event.captureImage = FrameUtils.NOIMAGE;
		
		if(event.targetImage == null)
			event.targetImage = FrameUtils.NOIMAGE;
		
		if(event.sceneImage == null)
			event.sceneImage = FrameUtils.NOIMAGE;

		event.setViperEncKey(viperEncKey);
		String message = buildEvent(event);
		if(Utils.instance.logged) {
			String text = "\n"
					    + "***********************************************************************************************************\n" 
						+ message 
					    + "\n"
					    + "event.captureImage[" + event.captureImage + "] :[" + new File(event.captureImage).exists() + "].\n"
					    + "event.targetImage[" + event.targetImage + "]  :[" + new File(event.targetImage).exists() + "].\n"
					    + "event.sceneImage[" + event.sceneImage + "]   :[" + new File(event.sceneImage).exists() + "].\n"
					    + "***********************************************************************************************************\n";
			
			log.info(text);
		}
		if(Utils.instance.kafkaHeaderKeyMode){
			face_raw_event_output.send(MessageBuilder.withPayload(message).setHeader(KafkaHeaders.KEY, event.getDevice_id().getBytes()).build());
		}else{
			face_raw_event_output.send(MessageBuilder.withPayload(message).build());
		}

		VideoStreamNonSeen.Monitor monitorMap = VideoStreamNonSeen.getMonitorMap().get(event.getDevice_id());
		if(monitorMap !=null) {
			monitorMap.getAndIncrementSended();
		}
	}
	
	private static String buildEvent(SenseyeEvent event) {
		String attr = "";
		String scene = "";
		String rect = "";
		String roiId = "";
		String extra = "";

		Map<String, Object> varibles = new HashMap<String, Object>();
		varibles.put("faceRecognitionMode", Utils.instance.faceRecognitionMode);
		varibles.put("strangerRecognition", Utils.instance.strangerRecognition);
		varibles.put("frameDecodeCost", event.getFrameDecodeCost());
		varibles.put("frameIndex", event.frameIndex);

		Base64.Encoder encoder = Base64.getEncoder();
		String originFeature = FaissSeeker.featureToString(event.feature);
		String featureBlob = null;
		if(originFeature != null) {
			try {
				// 确保viperEncKey不为null，如果为null使用空字符串代替
				String encKey = (event.viperEncKey != null) ? event.viperEncKey : "";
				featureBlob = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeader(
						FeatureConversionEncryptionUtils.encodeBlob(originFeature, encKey)));
			} catch(Exception e) {
				log.error("Error encoding feature blob: {}", e.getMessage());
			}
		}
		
		varibles.put("feature", originFeature);
		// 初始化 target，无论 displayTarget 是否为空
		Map<String, Object> target = null;
		if(StringUtils.isNotBlank(Utils.instance.displayTarget)) {
			target = filterTarget(event.getTarget());
		} else {
			target = new HashMap<>();
		}
		// 确保 target 不为 null
		if(target == null) {
			target = new HashMap<>();
		}
		// 添加 featureBlob 到 target
		if(featureBlob != null) {
			target.put("featureBlob", featureBlob);
		}
		varibles.put("target", target);
		varibles.put("receivedTime", System.currentTimeMillis());
		varibles.put("trackId", event.getTrackid());

		long cost = System.currentTimeMillis() - event.getFramePts();
		if (cost > Utils.instance.longCostTs) {
			log.warn("[VideoHandleLog] [Cost] frame long cost face deviceId: {}, annotator: {}, from capture to kafka time cost: {} ms",event.getDevice_id(), event.getTrackid(), cost);
		}

		varibles.put("reason", event.getReason());
		extra = ", \"extra\":" + JSONObject.toJSONString(varibles);

		if(MapUtils.isNotEmpty(event.attributes))
			attr = ", \"attributes\":" + JSONObject.toJSONString(event.attributes);
		
		if(StringUtils.isNotBlank(event.sceneImage)) 
			scene = ", \"image\":{\"url\":\"" + event.sceneImage + "\", \"width\":\"" + event.width + "\", \"height\":\"" + event.height + "\", \"framePts\":" + event.framePts + ",\"frameIndex\":" + event.frameIndex + "}";

		if(event.rect != null) 
			rect = ", \"quality\":" + event.getQuality() + ", \"detect\" : {\"left\":" + event.rect.get("left").intValue() + ", \"top\":" + event.rect.get("top").intValue() + ", \"right\":" + (event.rect.get("left").intValue() + event.rect.get("width").intValue()) + ", \"bottom\":" + (event.rect.get("top").intValue() + event.rect.get("height").intValue()) + "}";

		if(CollectionUtils.isNotEmpty(event.getRoiId()))
			roiId = ",\"roiId\":[\"" + StringUtils.join(event.getRoiId(), "\",\"") + "\"]";
		
		return  "{\"eventName\": \"" + event.eventName + "\",\"eventAction\": \"" + event.eventAction + "\",\"data\": {\"camera\": {\"referId\": \"" + event.device_id + "\",\"type\": 1,\"tag\": \"" + event.device_tag + "\"},\"capturedTime\": \"" + event.capturedTime + "\",\"detectTaskInfos\": [{\"recognisedInfos\": [{\"similars\": [{\"isNewPasser\":\"" + event.isNewPasser() + "\", \"score\": \"" + event.score + "\",\"target\": {\"trackid\": \"" + event.trackid + "\",\"personId\": \"" + event.personId + "\",\"url\": \"" + event.targetImage + "\",\"personType\": \"" + event.personType + "\"}}]}]}],\"faceInfo\": {\"image\": {\"url\": \"" + event.captureImage + "\"" +"}" + rect +"}" + roiId + scene + attr + extra +"}" + "}";
	}

	public static Map<String, Object> filterTarget(Map<String, Object> target){
		Map<String, Object> filterRes = new HashMap<>();

		if (Utils.instance.displayTarget.contains(",")) {
			filterRes = Arrays.stream(Utils.instance.displayTarget.split(","))
					.filter(target::containsKey)
					.collect(Collectors.toMap(key -> key, target::get));
		}
		return filterRes;
	}

	public void destroy(String deviceId) {
		for (Map.Entry<String, Long> entry : onGoingTracksFilter.entrySet()) {
			String key = entry.getKey();
			String[] splitKey = key.split("_");
			if (splitKey.length >0 && splitKey[0].equals(deviceId)) {
				onGoingTracksFilter.remove(key);
			}
		}
	}

	@Data
	@Accessors(chain = true)
	@Builder
	public static class SenseyeEvent{
		public String eventName;
		public String eventAction;
		public String device_id;
		public String device_tag;
		public String capturedTime;
		public String targetImage;
		public String captureImage;
		@Builder.Default
		public String personType = "";
		@Builder.Default
		public String personId = "-1";
		public List<String> roiId;
		public int trackid;
		@Builder.Default
		public Float score = 0.0f;
		public Float quality;
		public Map<String, Object> attributes;
		public String sceneImage;
		public Integer width;
		public Integer height;
		public Map<String, Number> rect;
		public boolean isNewPasser;
		public Long frameIndex;
		public Long framePts;
		public Map<String, Object> target;
		public float[] feature;
		public String reason;
		public Long frameDecodeCost;
		public String viperEncKey;
		
	}
}