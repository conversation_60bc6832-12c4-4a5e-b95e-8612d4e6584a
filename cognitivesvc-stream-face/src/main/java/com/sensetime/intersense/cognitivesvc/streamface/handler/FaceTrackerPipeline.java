package com.sensetime.intersense.cognitivesvc.streamface.handler;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamFace;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.event.RebalancedEvent;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamFaceRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streamface.zutils.FaceTrack;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class FaceTrackerPipeline{

	public static final String stageOne = "video_face_track_stream";

	public static final String stageTwo = "video_face_analyze_stream";

	private static final String roi_filter =    "[]";

	private static String faceConfigMem =   "";


	@Getter
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
	
	private Pointer pipeline;
	private Pointer rEPipeline;

	@Autowired
	private VideoStreamInfraRepository deviceMapper;

	@Autowired
	private VideoStreamFaceRepository faceMapper;

	@Getter
	private volatile Map<String, VideoStreamInfra> streamInfras = new HashMap<String, VideoStreamInfra>();

	@Getter
	private volatile Map<String, VideoStreamFace> streamFaces = new HashMap<String, VideoStreamFace>();

	@Getter
	private volatile Map<String, Pair<VideoStreamInfra, VideoStreamFace>> streamPairs = new HashMap<String, Pair<VideoStreamInfra, VideoStreamFace>>();

	@Autowired
	private MessageSendHandler messageSendHandler;

	@Autowired
	private Utils.Sync sync;

	@Getter
	/** <contextId, list<trackid, track>> */
	private final ConcurrentHashMap<Long, Map<Integer, FaceTrack>> onGoingTracks = new ConcurrentHashMap<Long, Map<Integer, FaceTrack>>();

	public String getFaceConfig(){
		return faceConfigMem;
	}
	
	public void initContext(String deviceId, long contextId) {
		if(onGoingTracks.containsKey(contextId))
			return ;
		
		VideoStreamInfra infra = streamInfras.get(deviceId);
		VideoStreamFace face = streamFaces.get(deviceId);

		String roiFilterStr = getSelectRoiFilter(face.getRoi(), roi_filter);
		log.info("[v2.8.7]change faceProcess start {},{},{},{},{},{},{}",deviceId, contextId,face.getProcessFace(), face.getBaseImageQuality(), face.getIntegrateQuality(), face.getRoi(), roiFilterStr);
		
		FaceTrackerContextConfig config = FaceTrackerContextConfig.builder()
				.scpPipelineBoolean(Utils.instance.scgFacePipeline)
				.contextId(contextId)
				.video_width(infra.getRtspWidth())
				.video_height(infra.getRtspHeight())
				.quality_thresh(Objects.requireNonNullElse(face.getBaseImageQuality(), 0.3f))
				.quick_response_time(Objects.requireNonNullElse(face.getQuickResponseTime(), -1))
				.time_interval(Objects.requireNonNullElse(face.getSelectFrameTimeInterVal(), face.getTimeInterval()))
				.max_track_time(Objects.requireNonNullElse(face.getMaxTrackTime(), -1))
				.max_tracklet_num(Objects.requireNonNullElse(face.getMaxTrackletNum(), 16))
				.roi_filter(roiFilterStr)
				.duplicate_targets_time(Utils.instance.duplicateTargetsTime)
				.small_quality_thresh(Objects.requireNonNullElse(face.getProcessFace().getSmallDetectThresh(), Objects.requireNonNullElse(Utils.instance.smallDetectThresh, 0.3f)))
				.large_quality_thresh(Objects.requireNonNullElse(face.getProcessFace().getLargeDetectThresh(), Objects.requireNonNullElse(Utils.instance.largeDetectThresh, 0.4f)))
				.pageant_quality_thresh(Objects.requireNonNullElse(Utils.instance.selectFrameThresh, 0.2f))
				.quality_step_thresh(Objects.requireNonNullElse(Utils.instance.qualityStepThresh, 0.01f))
				.build();

		String faceConfig =  config.toString();
		log.info("face_flock_pipeline_create_new : " + faceConfig);

		PointerByReference input = KesonUtils.stringToKeson(faceConfig);
		PointerByReference out = new PointerByReference();
		try {
			lock.writeLock().lock();
			
			KestrelApi.flock_pipeline_control(pipeline(), KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

			onGoingTracks.put(contextId, new HashMap<Integer, FaceTrack>());

		}finally {
			lock.writeLock().unlock();
			KesonUtils.kesonDeepDelete(input, out);
		}
		faceConfigMem = faceConfig;
	}
	public void reInitContext(String deviceId, long contextId) {
		if(!onGoingTracks.containsKey(contextId))
			return ;

		sync.sync();
		VideoStreamInfra infra = streamInfras.get(deviceId);
		VideoStreamFace face = streamFaces.get(deviceId);

		String roiFilterStr = getSelectRoiFilter(face.getRoi(), roi_filter);
		log.info("[v2.8.7]change faceProcess {},{},{},{},{},{},{}",deviceId, contextId, face.getProcessFace(), face.getBaseImageQuality(), face.getIntegrateQuality(),face.getRoi(), roiFilterStr);

		FaceTrackerContextConfig config = FaceTrackerContextConfig.builder()
				.contextId(contextId)
				.scpPipelineBoolean(Utils.instance.scgFacePipeline)
				.video_width(infra.getRtspWidth())
				.video_height(infra.getRtspHeight())
				.quality_thresh(Objects.requireNonNullElse(face.getBaseImageQuality(), 0.3f))
				.quick_response_time(Objects.requireNonNullElse(face.getQuickResponseTime(), -1))
				.time_interval(Objects.requireNonNullElse(face.getSelectFrameTimeInterVal(), face.getTimeInterval()))
				.max_track_time(Objects.requireNonNullElse(face.getMaxTrackTime(), -1))
				.max_tracklet_num(Objects.requireNonNullElse(face.getMaxTrackletNum(), 16))
				.roi_filter(roiFilterStr)
				.duplicate_targets_time(Utils.instance.duplicateTargetsTime)
				.small_quality_thresh(Objects.requireNonNullElse(face.getProcessFace().getSmallDetectThresh(), Objects.requireNonNullElse(Utils.instance.smallDetectThresh, 0.3f)))
				.large_quality_thresh(Objects.requireNonNullElse(face.getProcessFace().getLargeDetectThresh(), Objects.requireNonNullElse(Utils.instance.largeDetectThresh, 0.4f)))
				.pageant_quality_thresh(Objects.requireNonNullElse(Utils.instance.selectFrameThresh, 0f))
				.quality_step_thresh(Objects.requireNonNullElse(Utils.instance.qualityStepThresh, 0.01f))
				.build();

		String faceConfig =  config.toString();
		log.info("ReinitContext : " + faceConfig);
		PointerByReference input = KesonUtils.stringToKeson(faceConfig);

		PointerByReference out = new PointerByReference();
		try {
			lock.writeLock().lock();

			KestrelApi.flock_pipeline_control(pipeline(), KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

			//onGoingTracks.put(contextId, new HashMap<Integer, FaceTrack>());
		}finally {
			lock.writeLock().unlock();
			KesonUtils.kesonDeepDelete(input, out);
		}
		faceConfigMem = faceConfig;
	}
	
	public void destroyContext(String deviceId, long contextId) {
		Map<Integer, FaceTrack> contextTracks = onGoingTracks.remove(contextId);
		if(contextTracks == null)
			return ;

		Pointer input = KesonUtils.buildFlockRemoveInput(contextId, stageOne, stageTwo);
		PointerByReference out = new PointerByReference();
		try {
			lock.writeLock().lock();
			KestrelApi.flock_pipeline_control(pipeline, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input, out);
			
			for(FaceTrack track : contextTracks.values())
				track.closeImage();
		}finally {
			lock.writeLock().unlock();
			KesonUtils.kesonDeepDelete(input, out.getValue());
		}
		
		String ppl = "{\n" +
				"  \"streams\": [\n" +
				"    {\n" +
				"      \"name\": \"video_face_track_stream\",\n" +
				"      \"source_id\": 919524428,\n" +
				"      \"context_id\": 919524428,\n" +
				"      \"modules\": [\n" +
				"        {\n" +
				"          \"name\": \"input\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_detector\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_tracker\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"output\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        }\n" +
				"      ]\n" +
				"    },\n" +
				"    {\n" +
				"      \"name\": \"video_face_analyze_stream\",\n" +
				"      \"source_id\": 919524428,\n" +
				"      \"context_id\": 919524428,\n" +
				"      \"modules\": [\n" +
				"        {\n" +
				"          \"name\": \"input\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_quality_calculate\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_merge\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"roi_filter\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"target_selector\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"analyzed_lightweight_slice\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_classifier\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_classifier_filtered_lightweight_slice\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"aligner\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_landmarks_pack\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_headpose\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"blur_landmark_headpose_merge\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_quality_update\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_multidim_quality\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_multidim_quality_filtered_lightweight_slice\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_quality_filter\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_quality_filtered_lightweight_slice\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_feature_extraction\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_feature_mergence\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"faces_refinement\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_attribute_extraciton\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"face_targets_mergence\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"lightweight_targets_concat\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        },\n" +
				"        {\n" +
				"          \"name\": \"output\",\n" +
				"          \"source_id\": 919524428,\n" +
				"          \"context_id\": 919524428\n" +
				"        }\n" +
				"      ]\n" +
				"    }\n" +
				"  ]\n" +
				"}\n";


		ppl = ppl.replace("919524428",String.valueOf(contextId));
		PointerByReference inputPPl = KesonUtils.stringToKeson(ppl);
		PointerByReference outPPl = new PointerByReference();

		KestrelApi.flock_pipeline_control(pipeline, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, inputPPl.getValue(), outPPl);
		
		KesonUtils.kesonDeepDelete(inputPPl);
		KesonUtils.kesonDeepDelete(outPPl);

		messageSendHandler.destroy(deviceId);
	}
	public Integer getSelectFrameTimeInterVal(){

		//精准模式才有用
		if(Utils.instance.selectFrameTimeInterVal > 0 && Utils.instance.faceRecognitionMode == 0){
			return Utils.instance.selectFrameTimeInterVal;
		}
		return -1;
	}

	public String getSelectRoiFilter(String roi, String defRoi){
		if(StringUtils.isBlank(roi)) {
			return defRoi;
		}
		int[][][] rois = JSON.parseObject(roi, int[][][].class);
		if(rois.length <= 0){
			return defRoi;
		}
		return  JSON.toJSONString(toArrayStringRi(rois));
	}

	public static String[] toArrayStringRi(int[][][] arr) {
		int numRows = arr.length;
		String[] result = new String[numRows];
		for (int i = 0; i < numRows; i++) {
			result[i] = Arrays.deepToString(arr[i]);
		}
		return result;
	}

	public Pointer pipeline(){
		if(pipeline != null)
			return pipeline;
		
		synchronized(this) {
			if(pipeline != null)
				return pipeline;
			
			FaceTrackerContextConfig config = FaceTrackerContextConfig.builder()
					.scpPipelineBoolean(Utils.instance.scgFacePipeline)
					.quality_thresh(Utils.instance.imageQuality)
					.max_tracklet_num(Utils.instance.nonSeenMaxTrackletCount)
					.time_interval(getSelectFrameTimeInterVal())
					.build();
			
			pipeline = KestrelApi.flock_pipeline_create(config.toString());
		}
		
		return pipeline;
	}

	public Pointer rePipeline(){
		if(rEPipeline != null)
			return rEPipeline;

		synchronized(this) {
			if(rEPipeline != null)
				return rEPipeline;

			FaceTrackerContextConfig config = FaceTrackerContextConfig.builder()
					.quality_thresh(Utils.instance.imageQuality)
					.max_tracklet_num(Utils.instance.nonSeenMaxTrackletCount)
					.time_interval(getSelectFrameTimeInterVal())
					.build();

			log.info("face_flock_pipeline_create : " + config.toString());
			rEPipeline = KestrelApi.flock_pipeline_create(config.toString());
		}

		return rEPipeline;
	}

	@Order(value = RebalancedEvent.NoMatter)
	@EventListener(classes = RebalancedEvent.class)
	void onApplicationEvent(RebalancedEvent event){
		streamFaces = faceMapper.findAll().stream().collect(Collectors.toMap(VideoStreamFace::getDeviceId, Function.identity()));
		
		streamInfras = deviceMapper.findAll().stream().collect(Collectors.toMap(VideoStreamInfra::getDeviceId, Function.identity()));
		
		streamPairs = Stream.of(streamFaces.keySet(), streamInfras.keySet())
							.flatMap(Set::stream)
							.distinct()
							.filter(deviceid -> streamFaces.containsKey(deviceid) && streamInfras.containsKey(deviceid))
							.collect(Collectors.toMap(
								Function.identity(), 
								deviceId -> new MutablePair<VideoStreamInfra, VideoStreamFace>(streamInfras.get(deviceId), streamFaces.get(deviceId))
							));
	}
	
	@Data
	@Accessors(chain = true)
	@Builder
	public static class FaceTrackerContextConfig{
		
		public Long contextId;
		
		@Builder.Default
		public int video_width = 1920;
		
		@Builder.Default
		public int video_height = 1080;
		
		@Builder.Default
		public float expand_ratio = 1.5f;
		
		@Builder.Default
		public int max_tracklet_num = 16;
		
		@Builder.Default
		public int max_tracklet_item_size = 3;

		@Builder.Default
		public int duplicate_targets_time = 1;
		
		@Builder.Default
		public float quality_thresh = 0.3f;

		@Builder.Default
		public float large_quality_thresh = 0.4f;

		@Builder.Default
		public float small_quality_thresh = 0.2f;

		@Builder.Default
		public float pageant_quality_thresh = 0.2f;

		@Builder.Default
		public float quality_step_thresh = 0.01f;

		@Builder.Default
		public float ingrate_quality_thresh = 0.2f;

		@Builder.Default
		public int quick_response_time = -1;

		@Builder.Default
		public String roi_filter =   "[]";
		
		@Builder.Default
		public int time_interval = -1;
		
		@Builder.Default
		public int max_track_time = -1;

		public boolean scpPipelineBoolean;
		
		private String contextIdString() {
			if(contextId == null)
				return "";
			
			return "\"source_id\": " + contextId + ", \"context_id\": " + contextId + ",";
		}

		@Override
		public String toString() {
			if(scpPipelineBoolean){
				return faceScgPipelineConfig();
			} else {
				return faceStdConfig();
			}
		}

		public String faceStdConfig() {
			return "{" +
					"    \"streams\": [" +
					"        {" +
					"            \"name\": \"" + stageOne + "\"," + contextIdString() +
					"            \"module_plugins\": [" +
					"                \"detection.fmd\"," +
					"                \"multiple_target_tracking.fmd\"," +
					"                \"plugin.fmd\"," +
					"                \"roi_expand.fmd\"," +
					"                \"operation.fmd\"," +
					"                \"mergence.fmd\"," +
					"                \"track_recall.fmd\"" +
					"            ]," +
					"            \"modules\": [" +
					"                {" +
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," +
					"                    \"parallel_group\": \"input\"," +
					"                    \"inputs\": []," +
					"                    \"outputs\": [\"images\"]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_detector\"," + contextIdString() +
					"                    \"type\": \"Detection\"," +
					"                    \"parallel_group\": \"detect\"," +
					"                    \"inputs\": [\"images\"]," +
					"                    \"outputs\": [\"detected_faces_small\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"hunter\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module") + "\"," +
					"                        \"max_batch_size\": 48," +
					"                        \"confidence_threshold\": " + small_quality_thresh  + "," +
					"                        \"model_key\": \"small_face_detection\"" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_tracker\"," + contextIdString() +
					"                    \"type\": \"MultipleTargetTracking\"," +
					"                    \"parallel_group\": \"target_tracking\"," +
					"                    \"inputs\": [" +
					"                        \"detected_faces_small\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_faces_small\"," +
					"                        \"dropped_ids\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"roi_expander\"," + contextIdString() +
					"                    \"type\": \"RoiExpand\"," +
					"                    \"parallel_group\": \"detect\"," +
					"                    \"inputs\": [\"tracked_faces_small\"]," +
					"                    \"outputs\": [\"tracked_faces_expanded\"]," +
					"                    \"config\": {" +
					"                        \"roi_expand_ratio\": 1" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_detector_large\"," + contextIdString() +
					"                    \"type\": \"DetectionInROI\"," +
					"                    \"parallel_group\": \"detect\"," +
					"                    \"inputs\": [\"tracked_faces_expanded\"]," +
					"                    \"outputs\": [\"tracked_faces_filtered\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"hunter\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_large_module") + "\"," +
					"                        \"max_batch_size\": 48," +
					"                        \"confidence_threshold\": " + large_quality_thresh +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"track_recall\"," + contextIdString() +
					"                    \"type\": \"TrackRecall\"," +
					"                    \"parallel_group\": \"detect\"," +
					"                    \"inputs\": [\"tracked_faces_filtered\"]," +
					"                    \"outputs\": [\"tracked_faces\"]" +
					"                }," +
					"                {" +
					"                    \"name\": \"confidence_to_quality\"," + contextIdString() +
					"                    \"type\": \"Operation\"," +
					"                    \"parallel_group\": \"target_tracking\"," +
					"                    \"inputs\": [\"tracked_faces\"]," +
					"                    \"outputs\": [\"tracked_faces\"]," +
					"                    \"config\": {" +
					"                        \"operations\": [" +
					"                            {" +
					"                                \"cmd\": \"copy\"," +
					"                                \"args\": [" +
					"                                    \"confidence\"," +
					"                                    \"quality\"" +
					"                                ]" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"aligner\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"tracked_faces\"]," +
					"                    \"outputs\": [\"face_landmarks\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"aligner\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"model_key\": \"face_aligner_with_occlusion\"" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_headpose\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"face_landmarks\"]," +
					"                    \"outputs\": [\"face_headpose\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"headpose\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"model_key\": \"face_headpose\"" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_faces\"," +
					"                        \"face_headpose\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_faces_headpose\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," +
					"                    \"parallel_group\": \"output\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_faces_headpose\"," +
					"                        \"dropped_ids\"" +
					"                    ]," +
					"                    \"outputs\": []" +
					"                }" +
					"            ]" +
					"        }," +
					"        {" +
					"            \"name\": \"" + stageTwo + "\"," + contextIdString() +
					"            \"module_plugins\": [" +
					"                \"plugin.fmd\"," +
					"                \"mergence.fmd\"," +
					"                \"target_selection.fmd\"," +
					"                \"refinement.fmd\"," +
					"                \"roi_filter.fmd\"," +
					"                \"operation.fmd\"," +
					"                \"face_quality.fmd\"," +
					"                \"face_quality_filter.fmd\"," +
					"                \"duplicated_targets_filter.fmd\"," +
					"                \"concat.fmd\"," +
					"                \"lightweight_targets_slice.fmd\"," +
					"                \"face_quality_evaluator.fmd\"," +
					"                \"multidim_face_quality.fmd\"" +
					"            ]," +
					"            \"modules\": [" +
					"                {" +
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," +
					"                    \"parallel_group\": \"input\"," +
					"                    \"inputs\": []," +
					"                    \"outputs\": [" +
					"                        \"face_targets_with_quality\"," +
					"                        \"dropped_ids\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"roi_filter\"," + contextIdString() +
					"                    \"type\": \"RoiFilter\"," +
					"                    \"parallel_group\": \"target_select\"," +
					"                    \"inputs\": [\"face_targets_with_quality\"]," +
					"                    \"outputs\": [\"filtered_face_targets\"]," +
					"                    \"config\": {" +
					"                        \"roi_filter\": [" +
					"                            {" +
					"                                \"label_id\": 37017," +
					"                                \"polygons\": " + roi_filter  +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_selector\"," + contextIdString() +
					"                    \"type\": \"TargetSelection\"," +
					"                    \"parallel_group\": \"target_select\"," +
					"                    \"inputs\": [" +
					"                        \"filtered_face_targets\"," +
					"                        \"dropped_ids\"" +
					"                    ]," +
					"                    \"outputs\": [\"selected_faces\"]," +
					"                    \"config\": {" +
					"                        \"max_device_memory_usage_per_source\": 128," +
					"                        \"max_roi_ref_frame_size\": 512," +
					"                        \"max_source_tracklet_num\": " + max_tracklet_num + "," +
					"                        \"keep_low_quality_target\": true," +
					"                        \"selection\": [" +
					"                            {" +
					"                                \"label_id\": "               + "37017" + "," +
					"                                \"quick_response_time\": "    + quick_response_time + "," +
					"                                \"time_interval\": "          + time_interval + "," +
					"                                \"max_track_time\": "         + max_track_time + "," +
					"                                \"roi_expand_ratio\": "       + expand_ratio + "," +
					"                                \"quality_threshold\": "      + pageant_quality_thresh + "," +
					"                                \"max_tracklet_num\": "       + 512 + "," +
					"                                \"max_tracklet_item_size\": " + max_tracklet_item_size +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"aligner\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"selected_faces\"]," +
					"                    \"outputs\": [\"face_landmarks\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"aligner\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"model_key\": \"face_aligner_with_occlusion\"" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_landmarks_pack\"," + contextIdString() +
					"                    \"type\": \"Operation\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"face_landmarks\"]," +
					"                    \"outputs\": [\"face_landmarks\"]," +
					"                    \"config\": {" +
					"                        \"operations\": [" +
					"                            {" +
					"                                \"cmd\": \"move\"," +
					"                                \"args\": [" +
					"                                    \"confidence\"," +
					"                                    \"aligner_confidence\"" +
					"                                ]" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_headpose\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"face_landmarks\"]," +
					"                    \"outputs\": [\"face_headpose\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"headpose\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," +
					"                        \"max_batch_size\": 16," +
					"                        \"model_key\": \"face_headpose\"" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [" +
					"                        \"face_landmarks\"," +
					"                        \"face_headpose\"," +
					"                        \"selected_faces\"" +
					"                    ]," +
					"                    \"outputs\": [\"face_targets_quality_element\"]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_quality_update\"," + contextIdString() +
					"                    \"type\": \"FaceQuality\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"face_targets_quality_element\"]," +
					"                    \"outputs\": [\"new_face_tracklets\"]" +
					"                }," +

					"                {" +
					"                    \"name\": \"quality_transfer\"," + contextIdString() +
					"                    \"type\": \"Operation\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"new_face_tracklets\"]," +
					"                    \"outputs\": [\"new_face_tracklets\"]," +
					"                    \"config\": {" +
					"                        \"operations\": [" +
					"                            {" +
					"                                \"cmd\": \"copy\"," +
					"                                \"args\": [" +
					"                                    \"quality\"," +
					"                                    \"pageant_quality\"" +
					"                                ]" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_quality_filter\"," + contextIdString() +
					"                    \"type\": \"FaceQualityFilter\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"new_face_tracklets\"]," +
					"                    \"outputs\": [\"selected_faces_with_landmarks\"]," +
					"                    \"config\": {\"quality_threshold\": " + ingrate_quality_thresh + "}" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_quality_filtered_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [\"selected_faces_with_landmarks\"]," +
					"                    \"outputs\": [" +
					"                        \"face_quality_filtered_target_tracklets_normal\"," +
					"                        \"face_quality_filtered_target_tracklets_lightweight\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_feature_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [\"face_quality_filtered_target_tracklets_normal\"]," +
					"                    \"outputs\": [\"face_features\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"feature\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_face_module_scg") + "\"," +
					"                        \"max_batch_size\": 32," +
					"                        \"model_key\": \"face_feature\"" +
					"                     }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_feature_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_features\"," +
					"                        \"face_quality_filtered_target_tracklets_normal\"" +
					"                    ]," +
					"                    \"outputs\": [\"face_tracklets_with_feature\"]" +
					"                }," +
					"                {" +
					"                    \"name\": \"faces_refinement\"," + contextIdString() +
					"                    \"type\": \"Refinement\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets_with_feature\"" +
					"                    ]," +
					"                    \"outputs\": [\"best_faces\"]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_attribute_extraciton\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [\"best_faces\"]," +
					"                    \"outputs\": [\"face_attributes\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"attribute\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"filter\": false," +
					"                        \"model_key\": \"face_attribute\"" +
					"                     }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_targets_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_attributes\"," +
					"                        \"best_faces\"" +
					"                    ]," +
					"                    \"outputs\": [\"face_targets_with_attr_feature\"]" +
					"                }," +
					"                {" +
					"                    \"name\": \"analyzed_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [\"face_targets_with_attr_feature\"]," +
					"                    \"outputs\": [" +
					"                        \"analyzed_face_targets\"," +
					"                        \"analyzed_face_targets_lightweight\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"target_label_configs\": [" +
					"                            {" +
					"                                \"cmd\": 37017," +
					"                               \"min_width\": 10," +
					"                               \"min_height\": 10" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_multidim_quality\"," + contextIdString() +
					"                    \"type\": \"MultidimFaceQuality\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"analyzed_face_targets\"" +
					"                    ]," +
					"                    \"outputs\": [\"analyzed_face_targets\"]," +
					"                    \"config\": {" +
					"                        \"quality_model\": {" +
					"                            \"plugin\": \"classifier\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_quality_module") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"face_multidim_quality\"" +
					"                         }" +
					"                     }" +
					"                }," +


					"                {" +
					"                    \"name\": \"lightweight_targets_concat\"," + contextIdString() +
					"                    \"type\": \"Concat\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_quality_filtered_target_tracklets_lightweight\"," +
					"                        \"analyzed_face_targets_lightweight\"" +
					"                    ]," +
					"                    \"outputs\": [\"lightweight_targets\"]," +
					"                    \"config\": {" +
					"                        \"concat_items\": [" +
					"                          \"targets\"" +
					"                        ]" +
					"                     }" +
					"                }," +
					"                {" +
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," +
					"                    \"parallel_group\": \"output\"," +
					"                    \"inputs\": [" +
					"                        \"analyzed_face_targets\"," +
					"                        \"lightweight_targets\"" +
					"                    ]," +
					"                    \"outputs\": []" +
					"                }" +
					"            ]" +
					"        }" +
					"    ]" +
					"}" +
					"";
		}

		private String faceScgPipelineConfig() {
			return "{" + 
					"    \"streams\": [" + 
					"        {" + 
					"            \"name\": \"" + stageOne + "\"," + contextIdString() +
					"            \"module_plugins\": [" + 
					"                \"detection.fmd\"," + 
					"                \"multiple_target_tracking.fmd\"," + 
					"                \"plugin.fmd\"," +
					"                \"mergence.fmd\"" +
					"            ]," + 
					"            \"modules\": [" + 
					"                {" + 
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," +
					"                    \"inputs\": []," + 
					"                    \"outputs\": [\"images\"]" + 
					"                }," +
					"                {" +
					"                    \"name\": \"face_detector\"," + contextIdString() +
					"                    \"type\": \"Detection\"," +
					"                    \"parallel_group\": \"detect\"," +
					"                    \"inputs\": [\"images\"]," +
					"                    \"outputs\": [\"detected_faces\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"hunter\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module_scg") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"confidence_threshold\": " + small_quality_thresh  + "," +
					"                        \"model_key\": \"small_face_detection\"" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_tracker\"," + contextIdString() +
					"                    \"type\": \"MultipleTargetTracking\"," +
					"                    \"parallel_group\": \"target_tracking\"," +
					"                    \"inputs\": [" +
					"                        \"detected_faces\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_faces\"," +
					"                        \"dropped_ids\"" +
					"                    ]" +
					"                }," +

					"                {" + 
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," + 
					"                    \"parallel_group\": \"output\"," + 
					"                    \"inputs\": [" + 
					"                        \"tracked_faces\"," +
					"                        \"dropped_ids\"" + 
					"                    ]," + 
					"                    \"outputs\": []" + 
					"                }" + 
					"            ]" + 
					"        }," +
					"        {" + 
					"            \"name\": \"" + stageTwo + "\"," + contextIdString() +
					"            \"module_plugins\": [" + 
					"                \"plugin.fmd\"," + 
					"                \"mergence.fmd\"," + 
					"                \"target_selection_287.fmd\"," +
					"                \"refinement.fmd\"," + 
					"                \"roi_filter.fmd\"," + 
					"                \"operation.fmd\"," + 
					"                \"face_quality.fmd\"," + 
					"                \"face_quality_filter.fmd\"," +
					"                \"concat.fmd\"," +
					"                \"lightweight_targets_slice.fmd\"," +
					"                \"multidim_face_quality.fmd\"," +
					"                \"binary_classification_filter.fmd\"" +
					"            ]," + 
					"            \"modules\": [" + 
					"                {" + 
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," +
					"                    \"inputs\": []," + 
					"                    \"outputs\": [" + 
					"                        \"tracked_faces\"," +
					"                        \"dropped_ids\"" + 
					"                    ]" + 
					"                }," +

					"                {" +
					"                    \"name\": \"face_quality_calculate\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_quality\"," +
					"                    \"inputs\": [\"tracked_faces\"]," +
					"                    \"outputs\": [\"face_quality\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"pageant\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("pageant_module_scg") + "\"," +
					"                        \"max_batch_size\": 16," +
					"                        \"model_key\": \"face_quality\"" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"face_quality\"," +
					"                    \"inputs\": [" +
					"                        \"face_quality\"," +
					"                        \"tracked_faces\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_targets_with_quality\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"roi_filter\"," + contextIdString() +
					"                    \"type\": \"RoiFilter\"," +
					"                    \"parallel_group\": \"target_select\"," +
					"                    \"inputs\": [\"face_targets_with_quality\"]," +
					"                    \"outputs\": [\"filtered_face_targets\"]," +
					"                    \"config\": {" +
					"                        \"roi_filter\": [" +
					"                            {" +
					"                                \"label_id\": 37017," +
					"                                \"polygons\": " + roi_filter  +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" + 
					"                    \"name\": \"target_selector\"," + contextIdString() +
					"                    \"type\": \"TargetSelection\"," + 
					"                    \"parallel_group\": \"target_select\"," + 
					"                    \"inputs\": [" + 
					"                        \"filtered_face_targets\"," +
					"                        \"dropped_ids\"" + 
					"                    ]," + 
					"                    \"outputs\": [\"selected_faces\"]," +
					"                    \"config\": {" +
					"                        \"max_device_memory_usage_per_source\": 70," +
					"                        \"memory_pool_size\": 512," +
					"                        \"scene_frame_encoder\": {" +
					"                            \"encoder_plugin\": \"imagesharp\"," +
					"                            \"force_download\": false," +
					"                            \"max_ref_frame_size\": 25," +
					"                            \"scene_frame_encoder_quality\": 30" +
					"                        }," +
					"                        \"keep_low_quality_target\": true," +
					"                        \"selection\": [" +
					"                            {" +
					"                                \"label_id\": "               + 37017 + "," +
					"                                \"roi_expand_ratio\": "       + expand_ratio + "," +
					"                                \"quality_threshold\": "      + pageant_quality_thresh + "," +
					"                                \"max_tracklet_item_size\": "    + 3 + "," +
					"                                \"quality_step\": "           + quality_step_thresh + "," +
					"                                \"max_track_time\": " + -1 +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"analyzed_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"target_select\"," +
					"                    \"inputs\": [\"selected_faces\"]," +
					"                    \"outputs\": [" +
					"                        \"selected_faces\"," +
					"                        \"selected_face_tracklets_lightweight\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"target_label_configs\": [" +
					"                            {" +
					"                                \"label_id\": 37017," +
					"                               \"min_width\": 20," +
					"                               \"min_height\": 20" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_classifier\"," + contextIdString() +
					"                    \"type\": \"BinaryClassificationFilter\"," +
					"                    \"parallel_group\": \"classifier_filter_thread\"," +
					"                    \"inputs\": [\"selected_faces\"]," +
					"                    \"outputs\": [\"selected_faces\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"classifier\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("face_classifier_module_scg") + "\"," +
					"                        \"max_batch_size\": 128," +
					"                        \"support_label\": 37017," +
					"                        \"enable\": true," +
					"                        \"confidence_threshold\": 0.7," +
					"                        \"model_key\": \"face_binary_classifier\"" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_classifier_filtered_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"classifier_filter_thread\"," +
					"                    \"inputs\": [" +
					"                        \"selected_faces\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"selected_faces\"," +
					"                        \"face_classifier_filtered_targets_lightweight\"" +
					"                    ]" +
					"                }," +

					"                {" + 
					"                    \"name\": \"aligner\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"face_landmarks\"," + 
					"                    \"inputs\": [\"selected_faces\"]," + 
					"                    \"outputs\": [\"face_landmarks\"]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"aligner\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module_scg") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"model_key\": \"face_aligner_with_occlusion\"" +
					"                    }" + 
					"                }," +

					"                {" + 
					"                    \"name\": \"face_landmarks_pack\"," + contextIdString() +
					"                    \"type\": \"Operation\"," + 
					"                    \"parallel_group\": \"face_landmarks\"," + 
					"                    \"inputs\": [\"face_landmarks\"]," + 
					"                    \"outputs\": [\"face_landmarks\"]," + 
					"                    \"config\": {" + 
					"                        \"operations\": [" + 
					"                            {" + 
					"                                \"cmd\": \"move\"," + 
					"                                \"args\": [" + 
					"                                    \"confidence\"," + 
					"                                    \"aligner_confidence\"" + 
					"                                ]" + 
					"                            }" + 
					"                        ]" + 
					"                    }" + 
					"                }," +

					"                {" + 
					"                    \"name\": \"face_headpose\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"face_landmarks\"," + 
					"                    \"inputs\": [\"face_landmarks\"]," + 
					"                    \"outputs\": [\"face_headpose\"]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"headpose\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," + 
					"                        \"max_batch_size\": 64," +
					"                        \"model_key\": \"face_headpose\"" +
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," + 
					"                    \"parallel_group\": \"face_landmarks\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_landmarks\"," + 
					"                        \"face_headpose\"," + 
					"                        \"selected_faces\"" + 
					"                    ]," + 
					"                    \"outputs\": [\"face_targets_quality_element\"]" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_quality_update\"," + contextIdString() +
					"                    \"type\": \"FaceQuality\"," + 
					"                    \"parallel_group\": \"face_landmarks\"," + 
					"                    \"inputs\": [\"face_targets_quality_element\"]," + 
					"                    \"outputs\": [\"new_face_tracklets\"]" + 
					"                }," +

					"                {" +
					"                    \"name\": \"face_multidim_quality\"," + contextIdString() +
					"                    \"type\": \"MultidimFaceQuality\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"new_face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [\"new_face_tracklets\"]," +
					"                    \"config\": {" +
					"                        \"quality_model\": {" +
					"                            \"plugin\": \"classifier\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_quality_module") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"multi_face_quality\"" +
					"                         }," +

					"                        \"clear_model\": {" +
					"                            \"plugin\": \"classifier\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_blur_module_scg") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"multi_face_quality_blur\"" +
					"                         }," +
					"                         \"clear_score_threshold\": "       + 0 + "," +
					"                         \"angle_score_threshold\": "       + 0 + "," +
					"                         \"yaw_angle_threshold\": "         + 180 + "," +
					"                         \"pitch_angle_threshold\": "       + 180 + "," +
					"                         \"visible_score_threshold\": "     + 0 + "," +
					"                         \"shine_score_threshold\": "       + 0 +

					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_multidim_quality_filtered_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [\"new_face_tracklets\"]," +
					"                    \"outputs\": [" +
					"                        \"new_face_tracklets\"," +
					"                        \"face_multidim_quality_filtered_targets_lightweight\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_quality_filter\"," + contextIdString() +
					"                    \"type\": \"FaceQualityFilter\"," +
					"                    \"parallel_group\": \"face_landmarks\"," +
					"                    \"inputs\": [\"new_face_tracklets\"]," +
					"                    \"outputs\": [\"selected_faces_with_landmarks\"]," +
					"                    \"config\": {\"quality_threshold\": " + ingrate_quality_thresh + "}" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_quality_filtered_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [\"selected_faces_with_landmarks\"]," +
					"                    \"outputs\": [" +
					"                        \"face_quality_filtered_target_tracklets_normal\"," +
					"                        \"face_quality_filtered_target_tracklets_lightweight\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_feature_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [\"face_quality_filtered_target_tracklets_normal\"]," +
					"                    \"outputs\": [\"face_features\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"feature\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_face_module_scg") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"model_key\": \"face_feature\"" +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_feature_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_features\"," +
					"                        \"face_quality_filtered_target_tracklets_normal\"" +
					"                    ]," +
					"                    \"outputs\": [\"face_tracklets_with_feature\"]" +
					"                }," +

					"                {" +
					"                    \"name\": \"faces_refinement\"," + contextIdString() +
					"                    \"type\": \"Refinement\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets_with_feature\"" +
					"                    ]," +
					"                    \"outputs\": [\"best_faces\"]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_attribute_extraciton\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [\"best_faces\"]," +
					"                    \"outputs\": [\"face_attributes\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"attribute\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"filter\": false," +
					"                        \"model_key\": \"face_attribute\"" +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_targets_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_attributes\"," +
					"                        \"best_faces\"" +
					"                    ]," +
					"                    \"outputs\": [\"analyzed_face_targets\"]" +
					"                }," +


					"                {" +
					"                    \"name\": \"lightweight_targets_concat\"," + contextIdString() +
					"                    \"type\": \"Concat\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"selected_face_tracklets_lightweight\"," +
					"                        \"face_classifier_filtered_targets_lightweight\"," +
					"                        \"face_multidim_quality_filtered_targets_lightweight\"," +
					"                        \"face_quality_filtered_target_tracklets_lightweight\"" +
					"                    ]," +
					"                    \"outputs\": [\"lightweight_targets\"]," +
					"                    \"config\": {" +
					"                        \"concat_items\": [" +
					"                          \"targets\"" +
					"                        ]" +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," + 
					"                    \"parallel_group\": \"output\"," +
					"                    \"inputs\": [" +
					"                        \"analyzed_face_targets\"," +
					"                        \"lightweight_targets\"" +
					"                    ]," +
					"                    \"outputs\": []" + 
					"                }" +
					"            ]" + 
					"        }" +
					"    ]" +
					"}" + 
					"";
		}
	}			
}