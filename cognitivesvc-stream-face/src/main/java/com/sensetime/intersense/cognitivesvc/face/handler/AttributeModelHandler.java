package com.sensetime.intersense.cognitivesvc.face.handler;

import java.util.Map;

import com.sensetime.intersense.cognitivesvc.face.utils.FaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

public class AttributeModelHandler extends AbstractHandler<AttributeModelHandler.Attribute>{

	public AttributeModelHandler() {
		super(false);
		
		this.handlerEntity  = HandlerEntity.builder().build();
		this.pointers = new ModelHolder[] {FaceInitializer.hunter_large_holder, FaceInitializer.aligner_106_holder, FaceInitializer.attribute_class_holder};
	}

	@Override
	protected Attribute[] readModelResult(ModelResult modelResult) {
		PointerByReference keson = modelResult.getResult();
		if(keson == null || keson.getValue() == null)
			return new Attribute[0];
		
		Pointer targets = KestrelApi.keson_get_object_item(keson.getValue(), "targets");
		int arr_size = KestrelApi.keson_array_size(targets);
		
		Attribute[] result = new Attribute[arr_size];
		
		for(int index = 0 ; index < arr_size; index ++) {
			result[index] = new Attribute();
			Pointer target = KestrelApi.keson_get_array_item(targets, index);
			UtilsReader.readHunterData(target, result[index]);
			result[index].setAttribute(UtilsReader.readAttributeData(target));
		}
		
		return result;
	}
	
	@Data
	@EqualsAndHashCode(callSuper=false)
	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Attribute extends AbstractHandler.Detection{
		/** 属性 */
		private Map<String, Object> attribute;
	}
}
