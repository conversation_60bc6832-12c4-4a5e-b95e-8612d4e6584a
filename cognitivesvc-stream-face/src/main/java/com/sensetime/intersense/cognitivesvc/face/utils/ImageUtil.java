package com.sensetime.intersense.cognitivesvc.face.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Base64;

/**
 * @Author: Cirmons
 * @Date: 2019-03-28
 */
@Slf4j
public class ImageUtil {
    
    
    /**
     * zoom in or out image
     *
     * @param img
     * @param height
     * @param width
     * @return
     */
    public static BufferedImage resizeImage(BufferedImage img, int height, int width) {
        Image tmp = img.getScaledInstance(width, height, Image.SCALE_DEFAULT);
        BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resized.createGraphics();
        g2d.drawImage(tmp, 0, 0, null);
        g2d.dispose();
        return resized;
    }
    
    /**
     * zoom in or out image
     *
     * @param img
     * @param width
     * @return
     */
    public static BufferedImage resizeImage(BufferedImage img, int width) {
        double rate = BigDecimal.valueOf(width).divide(BigDecimal.valueOf(img.getWidth()), 2, RoundingMode.HALF_UP).doubleValue();
        int height = (int) Math.round(img.getHeight() * rate);
        return resizeImage(img, height, width);
    }
    
    /**
     * rotate image by 90 degrees in clockwise
     *
     * @param src
     * @return
     */
    public static BufferedImage rotateClockwise90(BufferedImage src) {
        int width = src.getWidth();
        int height = src.getHeight();
        
        BufferedImage dest = new BufferedImage(height, width, src.getType());
        
        Graphics2D graphics2D = dest.createGraphics();
        graphics2D.translate((height - width) / 2, (height - width) / 2);
        graphics2D.rotate(Math.PI / 2, height / 2, width / 2);
        graphics2D.drawRenderedImage(src, null);
        
        return dest;
    }
    
    public static String getFileBase64(File src) throws IOException {
        return Base64.getEncoder().encodeToString(FileUtils.readFileToByteArray(src));
    }
    
    /**
     * transform 2 points into rectangle
     *
     * @param x1
     * @param y1
     * @param x2
     * @param y2
     * @return
     */
    public static Rectangle getRectangleInstance(int x1, int y1, int x2, int y2) {
        return new Rectangle(x1, y1, x2 - x1 - 1, y2 - y1 - 1);
    }
    
    /**
     * resize rectangle by rate
     *
     * @param rect
     * @param rate
     * @return
     */
    public static Rectangle resizeRectangle(Rectangle rect, float rate) {
        int w = Math.round(rect.width * rate);
        int h = Math.round(rect.height * rate);
        
        int x = Math.round(rect.x - (rate * rect.width - rect.width) / 2) > 0 ? Math.round(rect.x - (rate * rect.width - rect.width) / 2) : 0;
        int y = Math.round(rect.y - (rate * rect.height - rect.height) / 2) > 0 ? Math.round(rect.y - (rate * rect.height - rect.height) / 2) : 0;
        return new Rectangle(x, y, w, h);
    }
    
    /**
     * crop image by rectangle boundray
     *
     * @param image
     * @param boundray
     * @return
     */
    public static BufferedImage cropImage(BufferedImage image, Rectangle boundray) {
        BufferedImage result = image;
        
        int x = boundray.x;
        int y = boundray.y;
        int height = boundray.height;
        int width = boundray.width;
        
        int maxHt = image.getHeight();
        int maxWd = image.getWidth();
        
        int x2 = (x + width) > maxWd ? maxWd : (x + width);
        int y2 = (y + height) > maxHt ? maxHt : (y + height);
        width = x2 - x;
        height = y2 - y;
        
        try {
            result = image.getSubimage(x, y, width, height);
        } catch (Exception e) {
            log.error("----->>> cropping failed, and return the original image.", e);
        }
        return result;
    }

    public static BufferedImage convertToIntRGB(BufferedImage originalImage) {
        BufferedImage newImage = new BufferedImage(
                originalImage.getWidth(),
                originalImage.getHeight(),
                BufferedImage.TYPE_INT_RGB
        );
        Graphics2D g2d = newImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();
        return newImage;
    }
    
}
