package com.sensetime.intersense.cognitivesvc.face.utils;

import java.io.File;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.sensetime.intersense.cognitivesvc.face.handler.event.FaceRawEventOutput;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.MediaType;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sensetime.intersense.cognitivesvc.face.handler.FeatureAttributeModelHandler;
import com.sensetime.intersense.cognitivesvc.face.handler.FeatureAttributeModelHandler.FeatureAttribute;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PasserFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PersonFaceObject;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;
import com.sensetime.intersense.cognitivesvc.strangerface.service.StrangerFaceSeekerFacade;
import com.sensetime.intersense.cognitivesvc.strangerface.service.StrangerFaceSeekerFacade.RawMessage;
import com.sensetime.lib.clientlib.response.BaseRes;


import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Configuration("faceKafkaConfiguration")
@ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "yes")
//@EnableBinding({FaceKafkaConfiguration.KafkaHandler.class, FaceKafkaConfiguration.KafkaSender.class})
@PropertySource("classpath:face-stream.properties")
public class FaceKafkaConfiguration {
	
//	public static interface KafkaHandler {
//		String FACE_RAW_IMAGE_INPUT = "face_raw_image_input";
//		@Input(FACE_RAW_IMAGE_INPUT) SubscribableChannel face_raw_image_input();
//	}
//
//	public static interface KafkaSender {
//		String FACE_RAW_EVENT_OUTPUT = "face_raw_event_output";
//		@Output(FACE_RAW_EVENT_OUTPUT) MessageChannel face_raw_event_output();
//	}

	@Bean("face_raw_event_output")
	public FaceRawEventOutput faceRawEventOutput(StreamBridge streamBridgeTemplate){
		return  new FaceRawEventOutput(streamBridgeTemplate);
	}
	
	@Configuration("faceKafkaHandler")
	@ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "yes")
	@Slf4j
	public static class ZFaceRawImageHandler {
		private static final ObjectMapper mapper = new ObjectMapper();

		@Autowired
		private SeekerFaceFacade seekerFacade;
		
		@Autowired
		private StrangerFaceSeekerFacade strangerFacade;
		
		@Autowired
	    private FaceRawEventOutput face_raw_event_output;
		
		@Autowired
		private FeatureAttributeModelHandler featureAttributeModelHandler;
		
		@Value("${intersense.face-raw-image-handler.max-read-img-retry-cnt:3}")
		private Integer maxReadImgRetryCnt;
			
		@Value("${intersense.face-raw-image-handler.read-img-retry-timebreak:200}")
		private Long readImgRetryTimeBreak;

		@SuppressWarnings("unchecked")
		@Bean("face_raw_image_input")
		public Consumer<FaceRawImage> demoConsumer() {
			return s->handleFaceRawEvent(s);
		}

		public void handleFaceRawEvent (FaceRawImage message) {
			log.info("[FaceRawImageHandler] retrieve msg:{}",message);
			Date capturedTime = message._getCapturedTime();
			if(capturedTime == null || System.currentTimeMillis() - capturedTime.getTime() > 10 * 60 * 1000) {
				//十分钟前的图片就不处理了 防止堆积
				log.warn("[FaceRawImageHandler] filter message timeout{}", JSON.toJSONString(message.toString()));
				return;
			}

			String imagePath = message._getFaceInfoImageUrl();
			int read_img_retry_cnt = 0;
			
			while(true){
				if(StringUtils.isBlank(imagePath) || !new File(imagePath).exists()){
					log.warn(">>> [FaceRawImageHandler] failed to read image! img:{}, read image retry count:{}",imagePath,read_img_retry_cnt);
					try { Thread.sleep(readImgRetryTimeBreak.longValue()); } catch (InterruptedException e) { e.printStackTrace(); }
					read_img_retry_cnt +=1;
				}else{
					if(log.isDebugEnabled())
						log.debug(">>> [FaceRawImageHandler] success to load img! img:{}, read image retry count:{}",imagePath,read_img_retry_cnt);
					break;
				}
				
				if(read_img_retry_cnt >= maxReadImgRetryCnt){
					log.error(">>> [FaceRawImageHandler] retried to read image, but file not exist! img:{}, read image retry count:{}",imagePath,read_img_retry_cnt);
					return;
				}
			}
			
			String targetGroup = message._getTargetGroup();
			String privilege = message._getPrivilege();
			
			FeatureAttribute feature_attribute[] = featureAttributeModelHandler.extractByPath(imagePath);

			if(ArrayUtils.isEmpty(feature_attribute))
				return;

			String cameraId = message._getCameraId();
			String cameraTag = StringUtils.isBlank(message._getCameraTag()) ? message._getCameraId() : message._getCameraTag().split(",")[0];

			String sex = "0";
			String age = "0";

			if(MapUtils.isNotEmpty(feature_attribute[0].getAttribute())) {
				message._setAttributes(feature_attribute[0].getAttribute());

				age = ((Map<String, Number>)feature_attribute[0].getAttribute().get("st_age_value")).get("st_age_value").toString();
				Map<String, Number> gender = (Map<String, Number>)feature_attribute[0].getAttribute().get("gender_code");
				sex = gender.get("male").floatValue() > gender.get("female").floatValue() ? "0" : "1";
			}else {
				try {
					List<JSONObject> jsons = JSON.parseArray(message._getAttributes()).stream().map(JSONObject.class::cast).collect(Collectors.toList());
					for(JSONObject json : jsons) {
						if("57".equals(json.get("key"))) {
							age = (json.get("value").toString());
						}else if("5".equals(json.get("key"))) {
							sex = (json.get("value").toString());
						}
					}
				}catch(Exception e) {}
			}

			String personType = message._getPersonType();

			float threshold = message._getThresHold() == null ? Utils.instance.Lv1Threshold : message._getThresHold();


			Map<String, Object> varibles = new HashMap<String, Object>();
			varibles.put("faceRecognitionMode", Utils.instance.faceRecognitionMode);
			varibles.put("strangerRecognition", Utils.instance.strangerRecognition);

			message.setExtra(varibles);

			if(StringUtils.isBlank(personType) || personType.equals("Target")) {
				PersonParam param = PersonParam.builder()
						.threshold(threshold)
						.feature(feature_attribute[0].getFeature())
						.deptIds(StringUtils.split(privilege, ","))
						.personGroups(StringUtils.split(targetGroup, ","))
						.findLocal(true)
						.figureImageUrl(imagePath)
						.count(1)
						.build();

				List<Pair<PersonFaceObject, Float>> persons = seekerFacade.findPerson(param);
				if(!persons.isEmpty()) {
					Pair<PersonFaceObject, Float> person = persons.get(0);
					message.setPersonInfo(person.getRight(), person.getLeft().pid, "Target", cameraTag, person.getLeft().avatar);
					log.info("[FaceRawImageHandler] send msg:{}",message);
					FaceRawEvent faceRawEvent = new FaceRawEvent(message);
					face_raw_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(faceRawEvent)).setHeader(KafkaHeaders.KEY, cameraId.getBytes()).build());
					return ;
				}
			}

			if((StringUtils.isBlank(personType) || personType.equals("Passer"))) {
				PasserParam param = PasserParam.builder()
						.feature(feature_attribute[0].getFeature())
						.count(1)
						.deptIds(StringUtils.split(privilege, ","))
						.threshold(threshold)
						.findLocal(true)
						.build();

				List<Pair<PasserFaceObject, Float>> passers = seekerFacade.findPasser(param);
				if(!passers.isEmpty()) {
					Pair<PasserFaceObject, Float> passer = passers.get(0);
					message.setPersonInfo(passer.getRight(), passer.getLeft().pid, "Passer", cameraTag, passer.getLeft().avatar);
					log.info("[FaceRawImageHandler] send msg:{}",message);
					FaceRawEvent faceRawEvent = new FaceRawEvent(message);
					face_raw_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(faceRawEvent)).setHeader(KafkaHeaders.KEY, cameraId.getBytes()).build());
					return ;
				}
			}

			StrangerFaceObject obj = StrangerFaceObject.builder().id(-1)
					.feature(feature_attribute[0].getFeature())
					.imagePath(imagePath)
					.createTs(capturedTime)
					.infra(VideoStreamInfra.builder().deviceId(cameraId).deviceTag(cameraTag).privilege(privilege).build())
					.sex(sex).age(age)
					.quality((float)feature_attribute[0].getConfidence())
					.build();

			String personUUID = strangerFacade.strangerIsHere(obj);
			if(personUUID == null) {
				message.rebuildStranger();
				FaceRawEvent faceRawEvent = new FaceRawEvent(message);
				log.info("[FaceRawImageHandler] send msg:{}",message);
				face_raw_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(faceRawEvent)).setHeader(KafkaHeaders.KEY, cameraId.getBytes()).build());
			}else {
				message.setPersonInfo(1.0f, personUUID, "Passer", cameraTag, imagePath);
				log.info("[FaceRawImageHandler] send msg:{}",message);
				FaceRawEvent faceRawEvent = new FaceRawEvent(message);
				face_raw_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(faceRawEvent)).setHeader(KafkaHeaders.KEY, cameraId.getBytes()).build());
			}
		};
		@SuppressWarnings("unchecked")
		public static class FaceRawImage extends RawMessage{
			public Date _getCapturedTime() {
				try {
					return Utils.dateFormat.get().parse(data.get("capturedTime").toString());
				} catch (Exception e) {
					return null;
				}
			}
			
			public String _getCapturedTimeString() {
				try {
					return data.get("capturedTime").toString();
				} catch (Exception e) {
					return null;
				}
			}
			
			public String _getAttributes() {
				try {
					return JSON.toJSONString(data.get("attributes"));
				} catch (Exception e) {
					return null;
				}
			}
			
			public void rebuildStranger() {
				try {
					int trackId = Integer.parseInt(((Map<String, Object>)data.get("faceInfo")).get("trackId").toString());
					setPersonInfo(0.0f, "-" + trackId, "Stranger", "", "");
				} catch (Exception e) { }
			}

			@SuppressWarnings("rawtypes")
			public void _setAttributes(Map attribute) {
				data.put("attributes", attribute);
			}
			
			public String _getImageUrl() {
				try {
					return ((Map<String, Object>)data.get("image")).get("url").toString();
				}catch (Exception e) {
					return null;
				}
			}
			
			public String _getFaceInfoImageUrl() {
				try {
					return ((Map<String, Object>)((Map<String, Object>)data.get("faceInfo")).get("image")).get("url").toString();
				}catch (Exception e) {
					return null;
				}
			}
			
			public String _getCameraId() {
				try {
					return ((Map<String, Object>)data.get("camera")).get("referId").toString();
				}catch (Exception e) {
					return null;
				}
			}
			
			public String _getCameraTag() {
				try {
					return ((Map<String, Object>)data.get("camera")).get("deviceTag").toString();
				}catch (Exception e) {
					return null;
				}
			}
			
			public String _getTargetGroup() {
				try {
					return ((Map<String, Object>)data.get("camera")).get("targetGroup").toString();
				}catch (Exception e) {
					return null;
				}
			}
			
			public String _getPrivilege() {
				try {
					return ((Map<String, Object>)data.get("camera")).get("privilege").toString();
				}catch (Exception e) {
					return null;
				}
			}
			
			public Float _getThresHold() {
				try {
					return Float.parseFloat(((Map<String, Object>)data.get("camera")).get("threshold").toString());
				}catch (Exception e) {
					return null;
				}
			}
			
			public String _getPersonType() {
				try {
					Map<String, Object> similar = (Map<String, Object>)((List<Object>)((Map<String, Object>)(((List<Object>)(((Map<String, Object>)(((List<Object>)data.get("detectTaskInfos")).get(0))).get("recognisedInfos"))).get(0))).get("similars")).get(0);
					Map<String, Object> target = (Map<String, Object>)similar.get("target");
					return target.get("personType").toString();
				}catch (Exception e) {
					return null;
				}
			}
			
			public String _getSerial() {
				try {
					Map<String, Object> similar = (Map<String, Object>)((List<Object>)((Map<String, Object>)(((List<Object>)(((Map<String, Object>)(((List<Object>)data.get("detectTaskInfos")).get(0))).get("recognisedInfos"))).get(0))).get("similars")).get(0);
					Map<String, Object> target = (Map<String, Object>)similar.get("target");
					return target.get("serial").toString();
				}catch (Exception e) {
					return null;
				}
			}
			
			public void setPersonInfo(float score, String aliasname, String personType, String personTag, String image) {
				setPersonInfo(score, aliasname, personType, personTag, image, false);
			}
			
			public void setPersonInfo(float score, String aliasname, String personType, String personTag, String image, boolean reissued) {
				try {
					String value = "[{\"recognisedInfos\": "
							         + "[{\"similars\": "
							             + "["
							               + "{"
							               + "\"score\": \"" + score + "\","
							               + "\"target\": "
							                   + "{"
								                   + "\"aliasname\": \"" + aliasname + "\","
								                   + "\"personType\": \"" + personType + "\","
								                   + "\"personTag\": \"" + personTag + "\", "
								                   + "\"url\":\"" + image + "\", "
								                   + "\"reissued\":\"" + reissued + "\""
							                   + "}"
							               + "}"
							               + "]"
							         + "}]"
							     + "}]";
					
					data.put("detectTaskInfos", mapper.readValue(value, ArrayList.class));
				}catch (Exception e) {
					throw new RuntimeException(e);
				}
			}

			public void setExtra(Map<String, Object> extra) {
				data.put("extra", extra);
			}
		}
		
		public static class FaceRawEvent extends FaceRawImage{
			
			public FaceRawEvent() {}
			
			public FaceRawEvent(FaceRawImage message) {
				eventName = message.eventName;
				eventAction = message.eventAction;
				data = message.data;
			}
			
			public FaceRawEvent(String eventName, String eventAction, Map<String, Object> data) {
				this.eventName = eventName;
				this.eventAction = eventAction;
				this.data = data;
			}
		}
		
		@RestController("FaceRawImageProvider")
		@RequestMapping(value = "/cognitive/face/", produces = MediaType.APPLICATION_JSON_VALUE)
		public class FaceProvider extends BaseProvider {
			
			@Autowired
			private ZFaceRawImageHandler handler;
			
			@Operation(summary = "图片流接口, 相当于facerawimage", method = "POST")
			@RequestMapping(value = "/faceRawImage", method = RequestMethod.POST)
			public BaseRes<String> faceRawImage(@RequestBody FaceRawImage message) throws Exception {
				handler.handleFaceRawEvent(message);
				return BaseRes.success("OK");
			}
			
		}
	}
}
