package com.sensetime.intersense.cognitivesvc.face.controller;

import com.sensetime.intersense.cognitivesvc.face.controller.ImageProvider.CutFaceResult.CutFace;
import com.sensetime.intersense.cognitivesvc.face.handler.BlurHeadposeModelHandler;
import com.sensetime.intersense.cognitivesvc.face.handler.BlurHeadposeModelHandler.BlurHeadpose;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler.Detection;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/cognitive/image/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "ImageProvider", description = "image controller")
public class ImageProvider extends BaseProvider {

    @Autowired
    private BlurHeadposeModelHandler blurHeadposeModelHandler;

    @Operation(summary = "从人脸图片路径, cut出所有脸的图的定位框", method = "GET")
    @RequestMapping(value = "/cutFaceByImagePath", method = RequestMethod.GET)
    public BaseRes<CutFaceResult> cutFaceByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
                                                     @Parameter(required = false, name = "type", description = "检测类型") @RequestParam(required = false) Integer type) throws Exception {
        BlurHeadpose[] hunters = blurHeadposeModelHandler.extractByPath(figureImageUrl);
        CutFaceResult result = new CutFaceResult();
        for (BlurHeadpose hunter : hunters)
            result.getResults().add(new CutFace(hunter));

        return BaseRes.success(result);
    }

    @Operation(summary = "从人脸图片base64, cut出所有脸的图的定位框", method = "POST")
    @RequestMapping(value = "/cutFaceByImageBase64", method = RequestMethod.POST)
    public BaseRes<CutFaceResult> cutFaceByImageBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64,
                                                       @Parameter(required = false, name = "type", description = "检测类型") @RequestParam(required = false) Integer type) throws Exception {
        File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

        try {
            return cutFaceByImagePath(tmpImage.getAbsolutePath(), type);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            tmpImage.delete();
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CutFaceResult {

        private List<CutFace> results = new ArrayList<CutFace>();

	    @Data
	    @Accessors(chain = true)
	    public static class CutFace {
	    	private int id;
	    	private int x;
	    	private int y;
	    	private int width;
	    	private int height;
	    	private double confidence;
	    	
			public CutFace(Detection hunter) {
				super();
				this.id = hunter.getId();
				this.x = hunter.getHunter().getLeft();
				this.y = hunter.getHunter().getTop();
				this.width = hunter.getHunter().getWidth();
				this.height = hunter.getHunter().getHeight();
				this.confidence = hunter.getConfidence();
			}
	    }    
	}
}
