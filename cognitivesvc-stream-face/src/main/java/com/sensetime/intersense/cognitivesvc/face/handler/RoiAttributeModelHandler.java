package com.sensetime.intersense.cognitivesvc.face.handler;

import java.util.List;

import com.sensetime.intersense.cognitivesvc.face.utils.FaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sun.jna.Memory;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

public class RoiAttributeModelHandler extends AttributeModelHandler{

	public RoiAttributeModelHandler() {
		this.handlerEntity  = HandlerEntity.builder().build();
		this.pointers = new ModelHolder[] {FaceInitializer.aligner_106_holder, FaceInitializer.attribute_class_holder};
	}
	
	@Override
	protected PointerByReference prepareInput(List<ModelResult> handlingList) {
    	Pointer[] gpuFrames = handlingList.stream().map(item -> item.getRequest().getFrames()[0]).toArray(Pointer[]::new);
        return new PointerByReference(KesonUtils.frameTo<PERSON>eson(gpuFrames, new Memory[gpuFrames.length], null));
    }
}
