package com.sensetime.intersense.cognitivesvc.streamface.handler;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import org.apache.commons.lang3.tuple.Pair;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_imgproc.CvFont;
import org.opencv.core.CvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamFace;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoIconEntity.VideoIcon;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoIconRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamSeen;
import com.sensetime.intersense.cognitivesvc.stream.video.service.RebalanceService;
import com.sensetime.intersense.cognitivesvc.streamface.zutils.FaceTrack;

import lombok.Getter;

@Component("faceVideoIconContainer")
public class VideoIconContainer{
	public static final Pattern p_cn_name = Pattern.compile("\\$\\{person_cn_name\\}");
	public static final Pattern p_en_name = Pattern.compile("\\$\\{person_en_name\\}");
	public static final Pattern p_tag     = Pattern.compile("\\$\\{tag\\}");
	public static final Pattern p_uuid    = Pattern.compile("\\$\\{person_uuid\\}");

	@Autowired
	private VideoIconRepository iconMapper;
	
	@Autowired
	private RebalanceService rebalanceService;
	
	@Autowired
	private FaceTrackerPipeline faceTrackerContainer;

	private Icon defaultPersonIcon;

	private Icon defaultPasserIcon;

	private Icon defaultStrangerIcon;
	
	//-< devceid_persontag, Icon >
	private Map<String, Icon> iconMap = new HashMap<String, Icon>();
	
	@Getter
	private final Map<String, CvScalar> expColor = new HashMap<String, CvScalar>();
	
	public VideoIconContainer() {
		expColor.put("angry",     CvScalar.BLUE);
		expColor.put("sorrow",    CvScalar.GREEN);
		expColor.put("calm",      CvScalar.WHITE);
		expColor.put("scared",    CvScalar.RED);
		expColor.put("yawn",      CvScalar.BLACK);
		expColor.put("surprised", CvScalar.CYAN);
		expColor.put("happy",     CvScalar.YELLOW);
		expColor.put("disgust",   CvScalar.MAGENTA);
	}
	
	@Scheduled(fixedDelay = 120 * 1000)
	@PostConstruct
	public synchronized void reload(){
		Map<String, Pair<VideoStreamInfra, VideoStreamFace>> streamFacePairs = faceTrackerContainer.getStreamPairs();
		boolean hasRtmpOn = streamFacePairs.values().stream().filter(p -> p.getLeft().getSts() == 0 && "0".equals(p.getLeft().getRtmpOn()) && p.getRight() != null).findAny().isPresent();
		if(!hasRtmpOn) {
			iconMap = Map.of();
			return;
		}
		
		List<VideoIcon> icons = iconMapper.findAll();
		Iterator<VideoIcon> its = icons.iterator();
		while(its.hasNext()) {
			VideoIcon icon = its.next();
			
			if("*".equals(icon.getDeviceId())) {
				if ("default_person_icon".equals(icon.getPersonTag())) {
					defaultPersonIcon = Icon.convert(icon);
					its.remove();
				} else if ("default_passer_icon".equals(icon.getPersonTag())) {
					defaultPasserIcon = Icon.convert(icon);
					its.remove();
				} else if ("default_stranger_icon".equals(icon.getPersonTag())) {
					defaultStrangerIcon = Icon.convert(icon);
					its.remove();
				}
			}
		}
		
		Set<String> deviceids = rebalanceService.getCurrentStreamMap().values().stream()
				.filter(stream -> stream instanceof VideoStreamSeen)
				.map(seen -> seen.getDevice().getDeviceId())
				.collect(Collectors.toSet());
		
		iconMap = icons.stream()
				.filter(icon -> deviceids.contains(icon.getDeviceId()))
				.collect(Collectors.toMap(
						icon -> icon.getDeviceId() + "_" + icon.getPersonTag(), 
						icon -> Icon.convert(icon)
				));
	}
	
	public Icon findIcon(FaceTrack track) {
		if(track.getPersonObj() != null) {
			Icon icon = iconMap.get(track.getStreamInfra().getDeviceId() + "_" + track.getPersonObj().tag);
			if(icon == null)
				iconMap.get(track.getStreamInfra().getDeviceId() + "_" + "default_person_icon");
			
			if(icon == null)
				icon = defaultPersonIcon;
			
			return icon;
		}else if(track.getPasserObj() != null) {
			Icon icon = iconMap.get(track.getStreamInfra().getDeviceId() + "_" + "default_passer_icon");
			if(icon == null)
				icon = defaultPasserIcon;
			
			return icon;
		}else {
			Icon icon = iconMap.get(track.getStreamInfra().getDeviceId() + "_" + "default_stranger_icon");
			if(icon == null)
				icon = defaultStrangerIcon;
			
			return icon;
		}
	}
	
	public static boolean isCNChar(String s){
		return s.chars().filter(c -> c > 128).findAny().isPresent();
    }
	
	@Getter
	public static final class Icon{
		private String  person_tag;
		private String  device_id;
		private CvScalar color;
		private CvFont font;
		private String  text;
		private Mat icon;
		private Date updateTs;
	    
	    public static Icon convert(VideoIcon icon) {
	    	Icon cache = new Icon();
	    	cache.person_tag = icon.getPersonTag();
	    	cache.device_id = icon.getDeviceId();
	    	cache.text = icon.getText();
	    	cache.font = opencv_imgproc.cvFont(Objects.requireNonNullElse(icon.getFontSize(), 2), Objects.requireNonNullElse(icon.getFontThick(), 2));
	    	cache.color = opencv_core.CV_RGB(Integer.parseInt(icon.getRgb().split(",")[2]), Integer.parseInt(icon.getRgb().split(",")[1]), Integer.parseInt(icon.getRgb().split(",")[0]));
	    	cache.updateTs = icon.getUpdateTs();
	    	File iconFile = null;
	    	try {
	    		iconFile = ImageUtils.storeImageToMemoryFileSystem(icon.getIconImageBase64());
	    		Mat src = opencv_imgcodecs.imread(iconFile.getAbsolutePath());
	    		if(src.cols() > 0 && src.rows() > 0) {
	    			cache.icon = new Mat(src.rows(), src.cols(), CvType.CV_8UC3);
	    			opencv_imgproc.cvtColor(src, cache.icon, opencv_imgproc.COLOR_RGB2BGR);
	    		}
	    		src.close();
			}catch(Exception e) {
				
			}finally {
				if(iconFile != null)
					iconFile.delete();
			}
	    	return cache;
	    }
	    
	    @Override
	    public void finalize() {
	    	icon.close();
	    }
	}
}
