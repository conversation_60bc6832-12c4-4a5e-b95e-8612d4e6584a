package com.sensetime.intersense.cognitivesvc.face.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.core.env.Environment;

import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;


public class FaceInitializer {
	/** 如果是CPU 并且明确了count 才会赋值 用来提高并行度  */
	public static final int FACE_MODEL_COUNT = Initializer.isDevice() ? 1 : Integer.parseInt(Utils.getProperty("DUP_MODEL_COUNT", "4"));
	
	public static final String optInit = Utils.getProperty("optimizeInit");

	private static boolean initialized = false;

	/** 这些都是真模型holder */
	public static ModelHolder hunter_large_holder;
	public static ModelHolder hunter_small_holder;
	public static ModelHolder hunter_common_holder;
	public static ModelHolder feature_holder;
	public static ModelHolder blur_holder;
	public static ModelHolder headpose_holder;
	public static ModelHolder attribute_class_holder;
	public static ModelHolder attribute_mtnet_holder;
	public static ModelHolder aligner_106_holder;
	
	//列表
	public static List<ModelHolder> model_handlers = new ArrayList<ModelHolder>();
	
	/** 初始化 需要调用
	 */
	public synchronized static void initialize(Environment env) {
		if(initialized)
			return ;

		SeekerFaceInitializer.initialize(env);
		
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "headpose.kep"  , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "blur.kep"      , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "attribute.kep" , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "hunter.kep"    , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "aligner.kep"   , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "feature.kep"   , "");

		hunter_small_holder    = new ModelHolder("hunter"    , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module")       , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		hunter_large_holder    = new ModelHolder("hunter"    , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_large_module")       , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		hunter_common_holder   = new ModelHolder("hunter"    , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_common_module")      , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		
		aligner_106_holder     = new ModelHolder("aligner"   , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module")        , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		attribute_class_holder = new ModelHolder("attribute" , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		attribute_mtnet_holder = new ModelHolder("attribute" , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_mtnet_module")    , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		blur_holder            = new ModelHolder("blur"      , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("blur_module")               , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		headpose_holder        = new ModelHolder("headpose"  , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module")           , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		feature_holder         = new ModelHolder("feature"   , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module")            , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);
		
		model_handlers.add(hunter_small_holder);
		model_handlers.add(hunter_large_holder);
		model_handlers.add(hunter_common_holder);
		model_handlers.add(aligner_106_holder);
		model_handlers.add(attribute_class_holder);
		model_handlers.add(attribute_mtnet_holder);
		model_handlers.add(blur_holder);
		model_handlers.add(headpose_holder);
		model_handlers.add(feature_holder);

    	initialized = true;
	}
	
	private static final boolean judgeInit(boolean isLazy) {
		if(optInit == null)
			return isLazy;
		
		return !Boolean.valueOf(optInit);
	}
}