package com.sensetime.intersense.cognitivesvc.streamface.video.filter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.opencv.core.Rect;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamFace;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawItem;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawMat;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawRect;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawTextCh;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawTextEn;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoSeenFilter;
import com.sensetime.intersense.cognitivesvc.streamface.handler.FaceTrackerPipeline;
import com.sensetime.intersense.cognitivesvc.streamface.handler.FaceTrackerHandler;
import com.sensetime.intersense.cognitivesvc.streamface.handler.VideoIconContainer;
import com.sensetime.intersense.cognitivesvc.streamface.handler.VideoIconContainer.Icon;
import com.sensetime.intersense.cognitivesvc.streamface.zutils.FaceTrack;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Setter;

/** 同步处理提交过来的帧
 */
@Order(100)
public class FaceSeenFilter extends VideoSeenFilter{
	
	private static final Logger log = LoggerFactory.getLogger(FaceSeenFilter.class);

	@Setter
	private static FaceTrackerHandler faceTrackerHandler;
	
	@Setter
	private static FaceTrackerPipeline faceTrackerContainer;
	
	@Setter
	private static VideoIconContainer videoIconContainer;
	
	private final PointerByReference inputKeson = new PointerByReference();
	
	private final PointerByReference outputKeson_s1 = new PointerByReference();
	
	private final PointerByReference outputKeson_s2 = new PointerByReference();
	
	private List<DrawItem> previousDrawItems = List.of();
	
	@SuppressWarnings("unchecked")
	@Override
	protected void handle(VideoFrame videoFrame, VideoStreamInfra device) {		
		VideoStreamFace deviceFace = faceTrackerContainer.getStreamFaces().get(device.getDeviceId());
		if(deviceFace == null || deviceFace.getRunFaceModel() != 0)
			return ;
		
		if(!Boolean.TRUE.equals(context.get("handleCurrentFrame"))) {
			((List<DrawItem>)context.get("draws")).addAll(previousDrawItems);
			return ;
		}
		
		Long sourceId = Utils.keyToContextId(device.getDeviceId());
		Pointer input = KesonUtils.frameToKeson(new Pointer[] { videoFrame.getGpuFrame()}, new Long[] { sourceId});
		inputKeson.setValue(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), input));
		
		synchronized(faceTrackerContainer.pipeline()) {
			int ret = KestrelApi.flock_pipeline_input_and_output(faceTrackerContainer.pipeline(), FaceTrackerPipeline.stageOne, inputKeson.getValue(), outputKeson_s1);
			if(ret != KestrelApi.KESTREL_OK)
				return ;
			
			ret = KestrelApi.flock_pipeline_input_and_output(faceTrackerContainer.pipeline(), FaceTrackerPipeline.stageTwo, outputKeson_s1.getValue(), outputKeson_s2);
			if(ret != KestrelApi.KESTREL_OK)
				return ;
		}
		
		Map<Integer, FaceTrack> contextTrackMap = faceTrackerContainer.getOnGoingTracks().get(sourceId);
		Pointer targetsBson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(outputKeson_s1.getValue(), 0), "targets");
		int targetSize = KestrelApi.keson_array_size(targetsBson);
		
		List<Map<String, Object>> currentTargets = new ArrayList<Map<String, Object>>();
		for(int index = 0; index < targetSize; index ++) {
			Pointer targetBson = KestrelApi.keson_get_array_item(targetsBson, index);
			Map<String, Object> target = (Map<String, Object>)KesonUtils.kesonToJson(targetBson);
			
			Number trackId = (Number)target.get("track_id");
			FaceTrack track = contextTrackMap.get(trackId.intValue());
			
			currentTargets.add(target);
			
			if(track == null) {
				track = FaceTrack.builder().trackId(trackId.intValue()).streamInfra(device).streamFace(deviceFace).build();
				contextTrackMap.put(trackId.intValue(), track);
				
				faceTrackerHandler.handleTargetStart(track, targetBson, target, videoFrame.getCapturedTime());
			}else {
				faceTrackerHandler.handleTargetAgain(track, targetBson, target, videoFrame.getCapturedTime());
			}
			
			track.setFrameIndex(videoFrame.getFrameDecodeCost());
			track.setFramePts(videoFrame.getCapturedTime());
		}
		
		Pointer trackletsBson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(outputKeson_s2.getValue(), 0), "targets");
		int trackletSize = KestrelApi.keson_array_size(trackletsBson);
		
		for(int index = 0; index < trackletSize; index ++) {
			Pointer trackletBson = KestrelApi.keson_get_array_item(trackletsBson, index);
			Map<String, Object> tracklet = (Map<String, Object>)KesonUtils.kesonToJson(trackletBson);
			
			Number trackId = (Number)tracklet.get("track_id");
			FaceTrack track = contextTrackMap.get(trackId.intValue());
			if(track != null) {
				faceTrackerHandler.handleTracklet(track, trackletBson, tracklet, videoFrame.getCapturedTime());

				track.setFrameIndex(videoFrame.getFrameIndex());
				track.setFramePts(videoFrame.getCapturedTime());
			}else
				log.warn("***************why tacklet is first to show up.******************");
		}
		
		Pointer dropTargetsBson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(outputKeson_s1.getValue(), 1), "targets");
		int dropTargetSize = KestrelApi.keson_array_size(dropTargetsBson);
		
		for(int index = 0; index < dropTargetSize; index ++) {
			Pointer dropTarget = KestrelApi.keson_get_array_item(dropTargetsBson, index);
			Map<String, Object> target = (Map<String, Object>)KesonUtils.kesonToJson(dropTarget);
			
			Number trackId = (Number)target.get("dropped_track_id");			
			contextTrackMap.remove(trackId.intValue());
		}

		List<DrawItem> currentDrawItems = (List<DrawItem>)context.get("draws");
		for(int index = 0; index < currentTargets.size(); index ++) {
			Map<String, Object> target = currentTargets.get(index);
			Number trackId = (Number)target.get("track_id");
			drawToFrame(contextTrackMap.get(trackId.intValue()), target);
		}
		
		previousDrawItems = currentDrawItems;
	}	

	@Override
	protected void postHandle(VideoFrame videoFrame, VideoStreamInfra device) {
		KesonUtils.kesonDeepDelete(inputKeson, outputKeson_s1, outputKeson_s2);
	}
	
	@SuppressWarnings("unchecked")
	private void drawToFrame(FaceTrack track, Map<String, Object> target) {
		Map<String, Number> roi = (Map<String, Number>)target.get("roi");
		int top = roi.get("top").intValue();
		int left = roi.get("left").intValue();
		int width = roi.get("width").intValue();
		int height = roi.get("height").intValue();
		
		Rect faceRect = new Rect(left, top, width, height);
		Icon icon = videoIconContainer.findIcon(track);
		
		List<DrawItem> draws = (List<DrawItem>)context.get("draws");
		if(icon == null) {
			 draws.add(new DrawRect(faceRect, 128, 128, 128));
			 return;
		}else
			draws.add(new DrawRect(faceRect, icon.getColor()));
		
		if(StringUtils.isNotBlank(icon.getText())) {
			String text = icon.getText();
			
			if(track.getPersonObj() != null) {
				text = VideoIconContainer.p_cn_name.matcher(text).replaceAll(StringUtils.isNotBlank(track.getPersonObj().cnName) ? track.getPersonObj().cnName : "");
				text = VideoIconContainer.p_en_name.matcher(text).replaceAll(StringUtils.isNotBlank(track.getPersonObj().enName) ? track.getPersonObj().enName : "");
				text = VideoIconContainer.p_tag.matcher(text).replaceAll(StringUtils.isNotBlank(track.getPersonObj().tag) ? track.getPersonObj().tag : "");
				text = VideoIconContainer.p_uuid.matcher(text).replaceAll(StringUtils.isNotBlank(track.getPersonObj().pid) ? track.getPersonObj().pid : "");
			}else if(track.getPasserObj() != null) {
				text = VideoIconContainer.p_uuid.matcher(text).replaceAll(StringUtils.isNotBlank(track.getPasserObj().pid) ? track.getPasserObj().pid : "");
			}
			
			if(Utils.instance.logged)
				text = track.getTrackId() + "_" + text;
			
			if(VideoIconContainer.isCNChar(text)) 
				draws.add(new DrawTextCh(text, icon.getColor(), faceRect));
			else 
				draws.add(new DrawTextEn(text, icon.getFont(), icon.getColor(), faceRect));
		}
		
		if(icon.getIcon() != null)
			draws.add(new DrawMat(faceRect, icon.getIcon()));
		
		if(track.getStreamFace().getRunFaceAttrModel() == 0 && MapUtils.isNotEmpty(track.getAttribute())) {
			try {
				Map<String, Number> stExpression = (Map<String, Number>)track.getAttribute().get("st_expression");
				Entry<String, Number> maxExpression = stExpression.entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
				
				if(Utils.instance.attributeAggressive != null 
					&& maxExpression.getKey().equals("st_calm") 
					&& maxExpression.getValue().floatValue() < Utils.instance.attributeAggressive) {
					
					stExpression = new HashMap<String, Number>((Map<String, Number>)track.getAttribute().get("st_expression"));
					stExpression.remove("st_calm");
					
					maxExpression = stExpression.entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
				}
				
				String expression = maxExpression.getKey().replace("st_", "");
				CvScalar exp_color = videoIconContainer.getExpColor().getOrDefault(expression, CvScalar.MAGENTA);				
				Rect newRect = new Rect(faceRect.x, faceRect.y - 30, faceRect.width, faceRect.height);
				
				draws.add(new DrawTextEn(expression, icon.getFont(), exp_color, newRect));
			}catch(Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	/** 初始化 */
	@Override
	protected synchronized void initialize(VideoStreamInfra device) {		
		VideoStreamFace face = faceTrackerContainer.getStreamFaces().get(device.getDeviceId());
		if(face == null)
			return ;
		
		long contextId = Utils.keyToContextId(device.getDeviceId());
		faceTrackerContainer.initContext(device.getDeviceId(), contextId);
	}
	
	/** 销毁 */
	@Override
	protected synchronized void destroy(VideoStreamInfra device) {
		try { Thread.sleep(2000); } catch (InterruptedException e) { }
		
		long contextId = Utils.keyToContextId(device.getDeviceId());
		faceTrackerContainer.destroyContext(device.getDeviceId(), contextId);
	}
}
