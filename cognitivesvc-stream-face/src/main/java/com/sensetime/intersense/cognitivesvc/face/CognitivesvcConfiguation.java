package com.sensetime.intersense.cognitivesvc.face;

import java.io.IOException;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.ConfigurableEnvironment;

import com.sensetime.intersense.cognitivesvc.face.handler.*;
import com.sensetime.intersense.cognitivesvc.face.utils.FaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.event.AttributeTypeChangedEvent;
import com.sensetime.intersense.cognitivesvc.server.event.HunterTypeChangedEvent;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;

import lombok.extern.slf4j.Slf4j;

@Configuration("faceConfiguation")
@ComponentScan
@Slf4j
@ConditionalOnExpression("${face.enabled:true} && ${seeker.enabled:true} && ${seeker.face.enabled:true}")
@PropertySource("classpath:face.properties")
public class CognitivesvcConfiguation{

	@Autowired
	private ConfigurableEnvironment env;
	
	@Autowired
	private Utils utils;
	
    @PostConstruct
    public void initialize() throws InterruptedException, IOException {    	
		utils.toString();
		
    	FaceInitializer.initialize(env);
    	
		log.warn("\n");
		log.warn("************************************");
		log.warn("**********init kestrel face*********");
		log.warn("****face.enabled=false to disable***");
		log.warn("***seeker.enabled=false to disable**");
		log.warn("seeker.face.enabled=false to disable");
		log.warn("************************************");
		log.warn("\n");
    }
    
    @Bean
    public RoiAttributeModelHandler roiAttributeModelHandler() {
    	FaceInitializer.initialize(env);
    	return new RoiAttributeModelHandler();
    }
    
    @Bean("roiFeatureModelHandler")
    public RoiFeatureModelHandler roiFeatureModelHandler() {
    	FaceInitializer.initialize(env);
    	return new RoiFeatureModelHandler();
    }
    
    @Bean
    public RoiFeatureAttributeModelHandler roiFeatureAttributeModelHandler() {
    	FaceInitializer.initialize(env);
    	return new RoiFeatureAttributeModelHandler();
    }
    
    @Bean
    public FeatureAttributeModelHandler featureAttributeModelHandler() {
    	FaceInitializer.initialize(env);
    	return new FeatureAttributeModelHandler();
    }
    
    @Bean
    public AttributeModelHandler attributeModelHandler() {
    	FaceInitializer.initialize(env);
    	return new AttributeModelHandler();
    }
    
    @Bean("faceFeatureModelHandler")
    public FeatureModelHandler featureModelHandler() {
    	FaceInitializer.initialize(env);
    	return new FeatureModelHandler();
    }
    
    @Bean
    public BlurHeadposeModelHandler blurHeadposeModelHandler() {
    	FaceInitializer.initialize(env);
    	return new BlurHeadposeModelHandler();
    }

	@Bean
	public BlurHeadposeSmallModelHandler blurHeadposeSmallModelHandler() {
		FaceInitializer.initialize(env);
		return new BlurHeadposeSmallModelHandler();
	}

	@Bean
	public BlurHeadposeModelPipeline blurHeadposeModelPipeline() {
		FaceInitializer.initialize(env);
		return new BlurHeadposeModelPipeline();
	}

	@Autowired
	@Lazy
	private FeatureModelHandler featureModelHandler;
	
	@Autowired
	@Lazy
	private AttributeModelHandler attributeModelHandler;
	
	@Autowired
	@Lazy
	private FeatureAttributeModelHandler featureAttributeModelHandler;

	@Autowired
	@Lazy
	private RoiFeatureModelHandler roiFeatureModelHandler;
	
	@Autowired
	@Lazy
	private RoiAttributeModelHandler roiAttributeModelHandler;
	
	@Autowired
	@Lazy
	private RoiFeatureAttributeModelHandler roiFeatureAttributeModelHandler;
	
	@Autowired
	@Lazy
	private BlurHeadposeModelHandler blurHeadposeModelHandler;

	@Autowired
	@Lazy
	private BlurHeadposeSmallModelHandler blurHeadposeSmallModelHandler;
    
    @EventListener({HunterTypeChangedEvent.class})
	public void onHunterTypeChanged(HunterTypeChangedEvent e) {
		ModelHolder newHolder = FaceInitializer.hunter_large_holder;
		switch(e.getNewType()) {
			case 2:
	    		newHolder = FaceInitializer.hunter_common_holder;
				break;
			case 3:
	    		newHolder = FaceInitializer.hunter_small_holder;
				break;
			case 1:
			default:
	    		newHolder = FaceInitializer.hunter_large_holder;
				break;
		}

		featureModelHandler.getPointers()[0] = newHolder;
		attributeModelHandler.getPointers()[0] = newHolder;
		featureAttributeModelHandler.getPointers()[0] = newHolder;
		blurHeadposeModelHandler.getPointers()[0] = newHolder;
		blurHeadposeSmallModelHandler.getPointers()[0] = newHolder;
	}
    
    @EventListener({AttributeTypeChangedEvent.class})
	public void onAttributeTypeChanged(AttributeTypeChangedEvent e) {
		ModelHolder newHolder = FaceInitializer.attribute_class_holder;
		switch(e.getNewType()) {
			case 1:
	    		newHolder = FaceInitializer.attribute_mtnet_holder;
				break;
			case 0:
			default : 
	    		newHolder = FaceInitializer.attribute_class_holder;
				break;
		}
		
		attributeModelHandler.getPointers()[2] = newHolder;
		featureAttributeModelHandler.getPointers()[3] = newHolder;
		
		roiAttributeModelHandler.getPointers()[1] = newHolder;
		roiFeatureAttributeModelHandler.getPointers()[2] = newHolder;
	}
}
