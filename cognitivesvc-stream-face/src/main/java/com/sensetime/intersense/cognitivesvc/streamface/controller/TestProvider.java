package com.sensetime.intersense.cognitivesvc.streamface.controller;

import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoNonSeenFilter.Counter;
import com.sensetime.intersense.cognitivesvc.streamface.handler.VideoIconContainer;

import io.swagger.v3.oas.annotations.Operation;

import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController("faceStreamTestProvider")
@RequestMapping(value = "/test", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="TestProvider",description = "Test")
@ConditionalOnProperty(value = "stream.face.enabled", havingValue = "true", matchIfMissing = true)
public class TestProvider extends BaseProvider {
	@Autowired
	private VideoIconContainer iconUtils;
	
	@Operation(summary = "icon_reload",method = "GET")
	@RequestMapping(value = "/icon_reload", method = RequestMethod.GET)
	public String icon_reload() throws Exception{
		iconUtils.reload();
		return "OK";
	}
	
	@Autowired
	private DiscoveryClient discoveryClient;
	
	@Value("${spring.application.name}")
	private String appName;
	
	@Operation(summary = "nonSeenHandlingItemQueueForall",method = "GET")
	@RequestMapping(value = "/nonSeenHandlingItemQueueForall", method = RequestMethod.GET)
	public Object nonSeenHandlingItemQueueForall() throws Exception{		
		return discoveryClient.getInstances(appName)
			.parallelStream()
			.map(instance -> {
				try {
					return "["+instance.getHost() + ":" + instance.getPort() + "] " + RestUtils.restTemplate4000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/test/nonSeenHandlingItemQueue", String.class);
				}catch(Exception e) {
					return "["+instance.getHost() + ":" + instance.getPort() + "] " + "ERROR";
				}
			})
			.collect(Collectors.toList()); 
	}
	
	private volatile long now = 0;
	
	@Operation(summary = "nonSeenHandlingItemQueue",method = "GET")
	@RequestMapping(value = "/nonSeenHandlingItemQueue", method = RequestMethod.GET)
	public Object nonSeenHandlingItemQueue() throws Exception{
		if(now == 0) {
			now = System.currentTimeMillis();
			Counter.nonSeenHandlingItemQueue.set(0);
			Counter.nonSeenHandlingItemHandled.set(0);
			Counter.nonSeenHandlingSended.set(0);
			return "init";
		}else {
			long expire = System.currentTimeMillis() - now;
			now = System.currentTimeMillis();
			
			String reuslt = "queued : " + Counter.nonSeenHandlingItemQueue.get() + ", handled : " + Counter.nonSeenHandlingItemHandled.get() + ", sended: " + Counter.nonSeenHandlingSended.get() + " , elapse :" + expire;

			Counter.nonSeenHandlingItemQueue.set(0);
			Counter.nonSeenHandlingItemHandled.set(0);
			Counter.nonSeenHandlingSended.set(0);
			
			return reuslt;
		}
	}
}
