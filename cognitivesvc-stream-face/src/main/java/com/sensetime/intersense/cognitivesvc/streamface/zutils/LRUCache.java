package com.sensetime.intersense.cognitivesvc.streamface.zutils;

import java.util.*;

public class LRUCache<K, V> {
    private final int MAX_CACHE_SIZE;
    private final float DEFAULT_LOAD_FACTOR = 0.75f;

    LinkedHashMap<K, CacheEntry<V>> map;

    public LRUCache(int cacheSize) {
        MAX_CACHE_SIZE = cacheSize;
        int capacity = (int) Math.ceil(MAX_CACHE_SIZE / DEFAULT_LOAD_FACTOR) + 1;
        map = new LinkedHashMap<K, CacheEntry<V>>(capacity, DEFAULT_LOAD_FACTOR, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_CACHE_SIZE;
            }
        };
    }

    public synchronized V get(K key) {
        CacheEntry<V> entry = map.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry.value;
        } else {
            return null;
        }
    }

    public synchronized void put(K key, V value) {
        map.put(key, new CacheEntry<V>(value));
        if (map.size() > MAX_CACHE_SIZE) {
            Set<Map.Entry<K, CacheEntry<V>>> entrySet = new HashSet<>(map.entrySet());
            for (Map.Entry<K, CacheEntry<V>> entry : entrySet) {
                if (entry.getValue().isExpired()) {
                    map.remove(entry.getKey());
                }
            }
        }
    }

    public synchronized void remove(K key) {
        map.remove(key);
    }

    public synchronized Set<Map.Entry<K, V>> getAll() {
        Set<Map.Entry<K, V>> result = new HashSet<>();
        Set<Map.Entry<K, CacheEntry<V>>> entrySet = new HashSet<>(map.entrySet());
        for (Map.Entry<K, CacheEntry<V>> entry : entrySet) {
            if (!entry.getValue().isExpired()) {
                result.add(new AbstractMap.SimpleEntry<>(entry.getKey(), entry.getValue().value));
            }
        }
        return result;
    }

    private static class CacheEntry<V> {
        final long timestamp;
        final V value;

        CacheEntry(V value) {
            this.timestamp = System.currentTimeMillis();
            this.value = value;
        }

        boolean isExpired() {
            return System.currentTimeMillis() > timestamp + 10*1000*60; // 10秒过期时间
        }
    }
}

