package com.sensetime.intersense.cognitivesvc.streamface.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamFace;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamFaceRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streamface.handler.FaceTrackerPipeline;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController("deviceFaceProvider")
@RequestMapping(value = "/cognitive/device/face/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "DeviceFaceProvider", description = "device face controller")
@Slf4j
public class DeviceFaceProvider extends BaseProvider{


	@Value("${spring.application.name}")
	private String appName;

	@Autowired
	private Broadcaster broadcastService;

	@Setter
	private static FaceTrackerPipeline faceTrackerContainer;


    @Autowired
    private VideoStreamFaceRepository videoStreamFaceMapper;

    @Operation(summary = "视频流face总数", method = "GET")
    @RequestMapping(value = "/getDeviceFaceCount", method = RequestMethod.GET)
    public BaseRes<Long> getDeviceFaceCount() {
        return BaseRes.success(videoStreamFaceMapper.count());
    }

    @Operation(summary = "查询视频流face配置", method = "GET")
    @RequestMapping(value = "/getDeviceFace", method = RequestMethod.GET)
    public BaseRes<List<VideoStreamFace>> getDeviceX(@RequestParam(required = false) String deviceId) {
        //log.info("[printHttpLog] url: /cognitive/device/face/getDeviceFace , req:{}", deviceId);
        if (StringUtils.isNotBlank(deviceId)) {
            List<VideoStreamFace> videoStreamFaces = videoStreamFaceMapper.findAllById(Lists.newArrayList(deviceId));
            //log.info("[printHttpLog] url: /cognitive/device/face/getDeviceFace , req:{}, res.size:{}", deviceId, videoStreamFaces.size());
            return BaseRes.success(videoStreamFaces);
        } else {
            List<VideoStreamFace> videoStreamFaces = videoStreamFaceMapper.findAll();
            //log.info("[printHttpLog] url: /cognitive/device/face/getDeviceFace , req:{}, res.size:{}", deviceId, videoStreamFaces.size());
            return BaseRes.success(videoStreamFaces);
        }
    }

    @Operation(summary = "添加视频流face配置", method = "POST")
    @RequestMapping(value = "/addOrUpdateDeviceFace", method = RequestMethod.POST)
    public BaseRes<Object> addOrUpdateDeviceFace(@RequestBody VideoStreamFace device) throws Exception {
        log.info("[printHttpLog] url: /cognitive/device/face/addOrUpdateDeviceFace , req:{}", device);
        if (device.getRunFaceModel() == null)
            device.setRunFaceModel(0);

        if (device.getRunFaceAttrModel() == null)
            device.setRunFaceAttrModel(0);

		//存在 有变化 则更新
		List<VideoStreamFace> videoFace = videoStreamFaceMapper.findAllById(Lists.newArrayList(device.getDeviceId()));

		try {
			updateRoi(device, videoFace);
		}catch (Exception e){
			e.printStackTrace();
		}

		videoStreamFaceMapper.saveAndFlush(device);

		try {
			updateThreshold(device, videoFace);
		}catch (Exception e){
			e.printStackTrace();
		}

		log.info("[printHttpLog] url: /cognitive/device/face/addOrUpdateDeviceFace , req:{},res:{}", device,"OK");
		return BaseRes.success("OK");
	}

	private void updateThreshold(VideoStreamFace device, List<VideoStreamFace> videoFace) {


		if(CollectionUtils.isNotEmpty(videoFace) && videoFace.get(0).getProcessFace() !=null){

			log.info("[v2.8.7]change {}", device.getDeviceId());
			//broadcastService.postForObject(appName, "/cognitive/pipelineChange", Map.of("deviceId", device.getDeviceId()), Object.class);

			if(videoFace.get(0).getProcessFace().getLargeDetectThresh() == null &&
					videoFace.get(0).getProcessFace().getSmallDetectThresh() == null
			){
				return;
			}
			if(videoFace.get(0).getProcessFace().getLargeDetectThresh() <=0f &&
					videoFace.get(0).getProcessFace().getSmallDetectThresh()  <=0f
			){
				return;
			}
			boolean allInB = true;

			String dbThreshold = videoFace.get(0).getProcessFace().getLargeDetectThresh() +"_" + videoFace.get(0).getProcessFace().getSmallDetectThresh();
			String requestThreshold =device.getProcessFace().getLargeDetectThresh() +"_" + device.getProcessFace().getSmallDetectThresh();

			if(!checkThresholdBool(requestThreshold, dbThreshold)){
				allInB = false;
			}
			if(Utils.instance.logged)
			   log.info("[v2.8.7]change {},{},{}",requestThreshold, dbThreshold, allInB);
			//第一次创建流不需要更新且有变化才更
			if (!allInB) {

				broadcastService.postForObject(appName, "/cognitive/pipelineChange", Map.of("deviceId", device.getDeviceId()), Object.class);
			}
		}
	}

	public void updateRoi(VideoStreamFace device, List<VideoStreamFace> videoFace){

		String[] rois = JSON.parseObject(device.getRoiIds(), String[].class);

		boolean allInB = true;

		String[] roisDb = new String[0];
		if(!videoFace.isEmpty()  && videoFace.get(0).getRoiIds() !=null) {
			roisDb = JSON.parseObject(videoFace.get(0).getRoiIds(), String[].class);
			//allInB = Arrays.stream(rois).allMatch(Arrays.asList(roisDb)::contains);
		}
		if(rois ==  null && videoFace.get(0).getRoiIds() == null){
            return;
		}
		if(!checkRoiBool(rois, roisDb)){
			allInB = false;
		}
		//第一次创建流不需要更新且有变化才更
		if (!allInB) {
			broadcastService.postForObject(appName, "/cognitive/device/env/pipelineChangeRoi", Map.of("deviceId", device.getDeviceId()), Object.class);
		}
	}
	public boolean checkRoiBool(String[] A, String[] B){
		if (A == null) {
			return B == null;
		} else {
			return Arrays.equals(A, B);
		}
	}
	public boolean checkThresholdBool(String A, String B){
		if (A == null) {
			return B == null;
		} else {
			return StringUtils.equals(A, B);
		}
	}
	@Operation(summary = "删除视频流face配置", method = "POST")
	@RequestMapping(value = "/deleteDeviceFace", method = RequestMethod.POST)
	public BaseRes<Object> deleteDeviceX(@RequestParam String deviceId) {
		log.info("[printHttpLog] url: /cognitive/device/face/deleteDeviceFace , req:{}", deviceId);
		try{
			videoStreamFaceMapper.deleteById(deviceId);
		}catch (Exception e){
			log.warn("deleteDeviceFace error,deviceId:{}, {}",deviceId, e.getMessage());
		}
		return BaseRes.success("OK");
	}

	@Operation(summary = "删除视频流face配置", method = "GET")
	@RequestMapping(value = "/getFaceConfig", method = RequestMethod.GET)
	public BaseRes<String> getFaceConfig() {

		try{
		  return  BaseRes.success(faceTrackerContainer.getFaceConfig());
		}catch (Exception e){
			log.warn("getFaceConfig error {}", e.getMessage());
		}
		return BaseRes.success("please open a face stream");
	}
}
