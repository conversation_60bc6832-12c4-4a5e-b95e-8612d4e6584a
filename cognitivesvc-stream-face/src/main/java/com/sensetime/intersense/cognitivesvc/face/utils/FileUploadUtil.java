package com.sensetime.intersense.cognitivesvc.face.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.opencsv.CSVReader;
import com.sensetime.intersense.cognitivesvc.client.response.entity.ResStatusEnum;
import com.sensetime.lib.weblib.exception.BusinessException;

import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileExistsException;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.io.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * @Author: Cirmons
 * @Date: 2018-07-30
 */
@Slf4j
public class FileUploadUtil {

    public static String getImageFormat(byte[] imageBytes) {
        if (imageBytes == null || imageBytes.length < 8) {
            throw new IllegalArgumentException("Invalid image byte array");
        }

        // PNG file signature
        byte[] pngSignature = new byte[] {(byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A};

        // JPEG file signature
        byte[] jpgSignature = new byte[] {(byte) 0xFF, (byte) 0xD8, (byte) 0xFF};

        // Check if it's a PNG
        if (startsWith(imageBytes, pngSignature)) {
            return "png";
        }

        // Check if it's a JPG
        if (startsWith(imageBytes, jpgSignature)) {
            return "jpg";
        }

        return "Unknown format";
    }

    private static boolean startsWith(byte[] data, byte[] prefix) {
        if (data.length < prefix.length) {
            return false;
        }
        for (int i = 0; i < prefix.length; i++) {
            if (data[i] != prefix[i]) {
                return false;
            }
        }
        return true;
    }



    public static String saveFile(byte[] fbs, String directory, String fileName) {
        try {
            File dir = new File(directory);
            if (!dir.exists())
                FileUtils.forceMkdir(dir);

            // 通过先写入临时文件，再mv的方式，利用操作系统写ceph路径，提高效率
            File tempPath = new File("/dev/shm/" + directory.replace("/", "_") + "_" + fileName);
            FileUtils.writeByteArrayToFile(tempPath, fbs);

            File filePath = new File(directory + "/" + fileName);
            if (!tempPath.renameTo(filePath)) {
                log.warn(">>> [FileUploadUtil] system operation file.renameTo() failed! use copy() in FileUtils!,tempPath:{},filePath:{}", tempPath.getAbsolutePath(), filePath.getAbsolutePath());

                try {
                    FileUtils.moveFile(tempPath, filePath);
                } catch (FileExistsException e) {
                    log.warn(">>> [FileUploadUtil] existed file:{}, will be overwrite!", filePath.getAbsolutePath());
                    FileUtils.forceDelete(filePath);
                    FileUtils.moveFile(tempPath, filePath);
                }
            }
            return filePath.getAbsolutePath();
        } catch (Exception e) {
            log.error("upload files, file save failed", e);
            throw new BusinessException(ResStatusEnum.SAVE_FILE_FAILED.code(), ResStatusEnum.SAVE_FILE_FAILED.msg());
        }
    }

    private static final int BUFFER_SIZE = 2 * 1024;

    public static void unZip(File srcFile, String destDirPath) throws RuntimeException {
        long start = System.currentTimeMillis();
        // 判断源文件是否存在
        if (!srcFile.exists()) {
            throw new RuntimeException(srcFile.getPath() + "所指文件不存在");
        }
        // 开始解压
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(srcFile);
            Enumeration<?> entries = zipFile.entries();
            int fileCount = 0;
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
                log.info("uncompress" + entry.getName());
                fileCount++;
                // 如果是文件夹，就创建个文件夹
                if (entry.isDirectory()) {
                    String dirPath = destDirPath + "/" + entry.getName();
                    File dir = new File(dirPath);
                    dir.mkdirs();
                } else {
                    // 如果是文件，就先创建一个文件，然后用io流把内容copy过去
                    File targetFile = new File(destDirPath + "/" + entry.getName());
                    // 保证这个文件的父文件夹必须要存在
                    if (!targetFile.getParentFile().exists()) {
                        targetFile.getParentFile().mkdirs();
                    }
                    targetFile.createNewFile();
                    // 将压缩文件内容写入到这个文件中
                    InputStream is = zipFile.getInputStream(entry);
                    FileOutputStream fos = new FileOutputStream(targetFile);
                    int len;
                    byte[] buf = new byte[BUFFER_SIZE];
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                    }
                    // 关流顺序，先打开的后关闭
                    fos.close();
                    is.close();
                }
            }
            long end = System.currentTimeMillis();
            log.info("uncompress complete, cost: " + (end - start) + " ms");
//            if (fileCount != 3) {
//                throw new RuntimeException("The number of extracted files is incorrect.");
//            }
        } catch (Exception e) {
            throw new RuntimeException("unzip error from ZipUtils", e);
        } finally {
            if (zipFile != null) {
                try {
                    zipFile.close();
                } catch (IOException e) {
                    log.error("unzip file error:", e);
                }
            }
        }
    }

    //XDYNAMICMODEL
    public static String uploadXDynamicModelZip(String zipPath, String dirPath) {
        unZip(new File(zipPath), dirPath);
        try {
            File zipFile = new File(zipPath);
            zipFile.delete();
        } catch (Exception ex) {
            log.error("@uploadXDynamicModelZip delete zip file error:", ex);
        }
        return dirPath;
    }

    public static String generateNewName(String originalName, byte[] fbs, String... acceptFileType) {
        String extension = getFileExtension(originalName, acceptFileType);

//        String md5 = DigestUtils.md5Hex(fbs);
//        return md5 + extension;
        return IdWorker.get32UUID() + extension;
    }

    public static String getFileExtension(String fileOriginalName, String... acceptFileType) {
        String extension = "";
        try {
            int index = fileOriginalName.lastIndexOf(".");
            extension = fileOriginalName.substring(index).toLowerCase();
        } catch (Exception e) {
            log.error("get file extension failed, fileOriginalName=" + fileOriginalName, e);
            throw new BusinessException(ResStatusEnum.ILLEGAL_FILE_NAME.code(), ResStatusEnum.ILLEGAL_FILE_NAME.msg());
        }

        if (acceptFileType != null) {
            List<String> acceptFileTypeList = Arrays.asList(acceptFileType).stream().map(String::toLowerCase).collect(Collectors.toList());
            if (acceptFileTypeList != null && acceptFileTypeList.size() > 0 && !Arrays.asList(acceptFileType).contains(extension.replace(".", ""))) {
                log.error("file format not valid, fileOriginalName=" + fileOriginalName);
                throw new BusinessException(ResStatusEnum.ILLEGAL_FILE_TYPE.code(), ResStatusEnum.ILLEGAL_FILE_TYPE.msg());
            }
        }
        return extension;
    }




    public static void checkFileSize(byte[] fbs, long size) {
        if (fbs.length > size)
            throw new BusinessException(ResStatusEnum.FILE_OVER_SIZE.code(), ResStatusEnum.FILE_OVER_SIZE.msg());
    }

    public static void checkImageIntegrity(byte[] fbs) {
        try {
            @Cleanup
            InputStream is = new ByteArrayInputStream(fbs);
            ImageIO.read(is).flush();
        } catch (Exception e) {
            throw new BusinessException(ResStatusEnum.CORRUPTTED_FILE.code(), ResStatusEnum.CORRUPTTED_FILE.msg());
        }
    }

//    public static List<HashMap<String,String>> readCsvFileToList(String csvPath) throws IOException {
//        List<String> csvFileList = FileUtils.readLines(new File(csvPath), Charset.forName("UTF-8"));
//        List<HashMap<String, String>> list = new ArrayList<>();
//        if (csvFileList != null && csvFileList.size() > 0) {
//            String headers = csvFileList.get(0);
//            String[] headerArray = headers.split(",");
//            list = csvFileList.stream().skip(1).map(line -> {
//                HashMap<String, String> map = new HashMap<>();
//                String[] lineArray = line.split(",", -1);
//                for (int i = 0; i < headerArray.length; i++) {
//                    String header = headerArray[i];
//                    String headerValue = lineArray[i];
//                    map.put(header, headerValue);
//                }
//                return map;
//            }).collect(Collectors.toList());
//        }
//        return list;
//    }

    public static List<HashMap<String, Object>> readCsvFileToList(String csvPath) throws IOException {
        String charset = "UTF-8";
        List<HashMap<String, Object>> list = new ArrayList<>();
        boolean readHead = false;
        FileInputStream fileInputStream = new FileInputStream(new File(csvPath));
        InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, charset);
        Reader reader = new BufferedReader(inputStreamReader);
        try {
            CSVReader csvReader = new CSVReader(reader);
            Iterator<String[]> iterator = csvReader.iterator();
            String[] head = null;
            while (iterator.hasNext()) {
                HashMap<String, Object> map = new HashMap<>();
                String[] nexts = iterator.next();
                if (readHead == false) {
                    head = nexts;
                    readHead = true;
                } else {
                    for (int i = 0; i < head.length; i++) {
                        map.put(head[i], nexts[i]);
                    }
                    list.add(map);
                }
            }
        } catch (Exception e) {
            log.error("readCsvFileToListCsv error:", e);
        } finally {
            fileInputStream.close();
            inputStreamReader.close();
            reader.close();
        }
        return list;
    }

//    public static List<HashMap<String,String>> readCsvFileToList(String csvPath) throws IOException {
//        String charset = "UTF-8";
//        List<HashMap<String, String>> list = new ArrayList<>();
//        boolean readHead = false;
//        try (CSVReader csvReader = new CSVReaderBuilder(new BufferedReader(new InputStreamReader(new FileInputStream(new File(csvPath)), charset))).build()) {
//            Iterator<String[]> iterator = csvReader.iterator();
//            String[] head = null;
//            while (iterator.hasNext()) {
//                HashMap<String, String> map = new HashMap<>();
//                String[] nexts = iterator.next();
//                if (readHead == false) {
//                    head = nexts;
//                    readHead = true;
//                } else {
//                    for (int i = 0; i < head.length; i++) {
//                        map.put(head[i], nexts[i]);
//                    }
//                    list.add(map);
//                }
//            }
//        } catch (Exception e) {
//           log.error("readCsvFileToListCsv error:", e);
//        }
//        return list;
//    }
}
