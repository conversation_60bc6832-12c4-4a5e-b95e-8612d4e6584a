package com.sensetime.intersense.cognitivesvc.face.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.client.response.entity.QualityRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.ResStatusEnum;
import com.sensetime.intersense.cognitivesvc.face.utils.FileUploadUtil;
import com.sensetime.intersense.cognitivesvc.face.utils.ImageUtil;
import com.sensetime.intersense.cognitivesvc.server.common.config.properties.FileUploadProperty;
import com.sensetime.intersense.cognitivesvc.server.entities.ImagePrehandleConfigEntity;
import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.intersense.cognitivesvc.face.handler.*;
import com.sensetime.intersense.cognitivesvc.face.handler.AttributeModelHandler.Attribute;
import com.sensetime.intersense.cognitivesvc.face.handler.BlurHeadposeModelHandler.BlurHeadpose;
import com.sensetime.intersense.cognitivesvc.face.handler.FeatureAttributeModelHandler.FeatureAttribute;
import com.sensetime.intersense.cognitivesvc.face.handler.FeatureModelHandler.Feature;
import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.PasserFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.*;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sensetime.storage.entity.ImageInfo;
import com.sensetime.storage.service.FileAccessor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.Statement;
import java.util.*;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;
import javax.sql.DataSource;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.bytedeco.opencv.opencv_core.*;
import org.bytedeco.opencv.opencv_core.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;

@RestController
@RequestMapping(value = "/cognitive/face/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "FaceProvider", description = "face controller")
@Slf4j
public class FaceProvider extends BaseProvider {

	@Autowired
	private DataSource dataSource;

	@Autowired
	private PersonFaceFeatureRepository personMapper;

	@Autowired
	private PasserFaceFeatureRepository passerMapper;

	@Autowired
	@Qualifier("faceFeatureModelHandler")
	private FeatureModelHandler featureModelHandler;

	@Autowired
	@Qualifier("roiFeatureModelHandler")
	private RoiFeatureModelHandler roiFeatureModelHandler;

	@Autowired
	private AttributeModelHandler attributeModelHandler;
	
	@Autowired
	private FeatureAttributeModelHandler featureAttributeModelHandler;
	
	@Autowired
	private BlurHeadposeModelHandler blurHeadposeModelHandler;

	@Autowired
	private BlurHeadposeModelPipeline blurHeadposeModelPipeline;

    @Resource
    private BlurHeadposeSmallModelHandler blurHeadposeSmallModelHandler;

    @Autowired
    FileAccessor fileAccessor;

    @Resource
    private FileUploadProperty property;

    @Operation(summary = "从人脸图片路径提取脸角度", method = "POST")
    @RequestMapping(value = "/getHeadposeBlurByImage", method = RequestMethod.POST)
    public BaseRes<Map<String, Object>> getHeadposeByImage(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception {
        BlurHeadpose[] headposes = blurHeadposeModelHandler.extractByPath(figureImageUrl);

        Map<String, Object> data = new HashMap<String, Object>();
        for (int index = 0; index < headposes.length; index++) {
            Map<String, Object> item = new HashMap<String, Object>();
            data.put("face_" + index, item);

			item.put("detect",  toDot(headposes[index].getHunter()));
			
			item.put("blur", headposes[index].getBlur());
			item.put("pitch", headposes[index].getPitch());
			item.put("yaw", headposes[index].getYaw());
			item.put("roll", headposes[index].getRoll());
		}

		if(log.isDebugEnabled())
			log.debug(">>> [getHeadposeBlurByImage] request image is: {}, detail result: {}",figureImageUrl, JSON.toJSONString(headposes));
		
		return BaseRes.success(data);
	}

	@Operation(summary = "从人脸图片base64提取脸角度", method = "POST")
	@RequestMapping(value = "/getHeadposeBlurByBase64", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getHeadposeByBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws Exception {
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
		try {
			return getHeadposeByImage(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}
	
	@SuppressWarnings("unchecked")
	@Operation(summary = "从人脸图片路径提取特征和属性", method= "POST")
	@RequestMapping(value = "/getFeatureAndAttributeByImagePath", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getFeatureAndAttributeByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception {
		FeatureAttribute[] feature_attributes = featureAttributeModelHandler.extractByPath(figureImageUrl);
		Map<String, Object>[] result = new Map[feature_attributes.length];
		for(int index = 0; index < feature_attributes.length; index ++) {
			result[index] = new HashMap<>();
			result[index].put("id", index);
			result[index].put("detect",  toDot(feature_attributes[index].getHunter()));
			result[index].put("feature",   FaissSeeker.featureToString(feature_attributes[index].getFeature()));
			result[index].put("attributes",feature_attributes[index].getAttribute());
		}
		return BaseRes.success(Map.of("face", result));
	}
	
	@Operation(summary = "从人脸图片base64提取特征和属性", method= "POST")
	@RequestMapping(value = "/getFeatureAndAttributeByImageBase64", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getFeatureAndAttributeByImageBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws Exception {
//		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
//
//		try {
//			return getFeatureAndAttributeByImagePath(tmpImage.getAbsolutePath());
//		}finally {
//			tmpImage.delete();
//		}

		byte[] bytes = ImageUtils.base64ToBytes(figureImageBase64);
		FeatureAttribute[] feature_attributes = featureAttributeModelHandler.extractByBytes(bytes);
		Map<String, Object>[] result = new Map[feature_attributes.length];
		for(int index = 0; index < feature_attributes.length; index ++) {
			result[index] = new HashMap<>();
			result[index].put("id", index);
			result[index].put("detect",  toDot(feature_attributes[index].getHunter()));
			result[index].put("feature",   FaissSeeker.featureToString(feature_attributes[index].getFeature()));
			result[index].put("attributes",feature_attributes[index].getAttribute());
		}
		return BaseRes.success(Map.of("face", result));

	}
	
	@Operation(summary = "从人脸图片路径提取大小框", method= "POST")
	@RequestMapping(value = "/getDetectByImagePath", method = RequestMethod.POST)
	public BaseRes<Object> getDetectByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception {

		Map<String, Object> data = new HashMap<String, Object>();
		try {
			BlurHeadpose[] detect = blurHeadposeModelHandler.extractByPath(figureImageUrl);
			for (int index = 0; index < detect.length; index++)
				data.put("face_" + index, toDot(detect[index].getHunter()));
		}catch (Exception e){
			log.error("getDetectByImagePath throw exception{},figureImageUrl:{}", e.getMessage(), figureImageUrl);
		}
		return BaseRes.success(data);
	}
	
	@Operation(summary = "从人脸图片base64提取大小框", method= "POST")
	@RequestMapping(value = "/getDetectByBase64", method = RequestMethod.POST)
	public BaseRes<Object> getDetectByBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws Exception {
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
		
		try {
			return getDetectByImagePath(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}
	
	@SuppressWarnings("unchecked")
	@Operation(summary = "从人脸图片路径提取特征", method= "POST")
	@RequestMapping(value = "/getFeatureByImagePath", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getFeatureByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception {
		Feature[] features = featureModelHandler.extractByPath(figureImageUrl);
		Map<String, Object>[] result = new Map[features.length];
		for(int index = 0; index < features.length; index ++) {
			result[index] = new HashMap<>();
			result[index].put("id", index);
			result[index].put("detect",  toDot(features[index].getHunter()));
			result[index].put("feature", FaissSeeker.featureToString(features[index].getFeature()));
		}
		return BaseRes.success(Map.of("face", result));
	}
	@Operation(summary = "从人脸图片路径提取特征", method= "POST")
	@RequestMapping(value = "/getFeatureByImagePathRoi", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getFeatureByImagePathRoi(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception {
		Feature[] features = roiFeatureModelHandler.extractByPath(figureImageUrl);
		Map<String, Object>[] result = new Map[features.length];
		for(int index = 0; index < features.length; index ++) {
			result[index] = new HashMap<>();
			result[index].put("id", index);
			result[index].put("detect",  toDot(features[index].getHunter()));
			result[index].put("feature", FaissSeeker.featureToString(features[index].getFeature()));
		}
		return BaseRes.success(Map.of("face", result));
	}

	@Operation(summary = "从人脸图片base64提取特征", method= "POST")
	@RequestMapping(value = "/getFeatureByImageBase64", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getFeatureByImageBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws Exception {
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
		
		try {
			return getFeatureByImagePath(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}

	@Operation(summary = "从人脸图片base64提取特征", method= "POST")
	@RequestMapping(value = "/getFeatureByImageBase64s", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getFeatureByImageBase64s(@RequestParam("figureImageBase64") String figureImageBase64 ) throws Exception {
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

		try {
			return getFeatureByImagePath(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}
	@Operation(summary = "从人脸图片base64提取特征", method= "POST")
	@RequestMapping(value = "/getFeatureByImageBase64Roi", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getFeatureByImageBase64Roi(@RequestParam("figureImageBase64") String figureImageBase64 ) throws Exception {
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

		try {
			return getFeatureByImagePathRoi(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}
	@Operation(summary = "从人脸图片base64检验图片质量评分", method= "POST")
	@RequestMapping(value = "/getImageQualityByImageBase64", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getImageQualityByImageBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws Exception {
        // Validate input
        if (StringUtils.isBlank(figureImageBase64)) {
            log.warn("Image base64 input is null or empty");
            return BaseRes.error("3001", "图片base64不能为空");
        }
        // Convert base64 to bytes
        byte[] bytes = ImageUtils.base64ToBytes(figureImageBase64);

        // Apply image prehandling if enabled
        FileUploadProperty.ImagePrehandleConfig config = property.getImagePrehandleConfig();
        List<QualityRes> blurHeadposes = null;
        if (config != null && config.getEnabled()) {
            blurHeadposes = Optional.of(bytes)
                    .map(this::imageQualityUtility)
                    .orElseThrow(() -> new BusinessException("3002", "图片处理失败"));

			// Process results
			Map<String, Object>[] result = new Map[blurHeadposes.size()];
			for (int i = 0; i < blurHeadposes.size(); i++) {
				result[i] = processBlurHeadpose(blurHeadposes.get(i));
			}

			return BaseRes.success(Map.of("face", result));
        }else{
			// Extract face features
			BlurHeadposeModelPipeline.FaceBody[] blurHeadposesFaceBody = blurHeadposeModelPipeline.extractByBytes(bytes);
			Map<String, Object>[] result = new Map[blurHeadposesFaceBody.length];
			for (int i = 0; i < blurHeadposesFaceBody.length; i++) {
				result[i] = processBlurHeadpose(blurHeadposes.get(i));
			}

			return BaseRes.success(Map.of("face", result));
		}
    }
	// Helper method to process individual BlurHeadpose result
	private Map<String, Object> processBlurHeadpose(QualityRes faceBody) {
		Map<String, Object> resultMap = new HashMap<>();
		if (faceBody != null) {
			resultMap.put("id", faceBody.getId());
			resultMap.put("quality", faceBody.getQuality());
			resultMap.put("score", faceBody.getScore());
			resultMap.put("confidence", faceBody.getConfidence());
			resultMap.put("aligner_confidence", faceBody.getAlignerConfidence());
			resultMap.put("detect", faceBody.getDetect());
		}
		return resultMap;
	}

	public List<QualityRes>  imageQualityUtility(byte[] bytes) {
        List<QualityRes> qualityResList;
        try {
            String imagePath = "";

            FileUploadUtil.checkFileSize(bytes, property.getAcceptImageSize());
            FileUploadUtil.checkImageIntegrity(bytes);
            String imageFormat = FileUploadUtil.getImageFormat(bytes);
            log.info("FileUploadUtil.getImageFormat:{}", imageFormat);

            ImagePrehandleConfigEntity im_conf = ImagePrehandleConfigEntity.builder().build();

            AtomicReference<String> extension = new AtomicReference<>("jpg");

            im_conf.setFileExtension(imageFormat);

            Arrays.stream(ImageIO.getWriterFormatNames())
                    .filter(e -> e.equalsIgnoreCase(im_conf.getFileExtension()))
                    .findAny()
                    .ifPresent(e -> extension.set(e.toLowerCase()));

            FileUploadProperty.ImagePrehandleConfig config = property.getImagePrehandleConfig();

            int accessDetectApiCount = 1;
            if (config.getFaceDetectConfig().getRotatedImageDetectEnabled())
                accessDetectApiCount = 4;

            List<Rectangle> faceRects = new ArrayList<>();
            for (int i = 1; i <= accessDetectApiCount; i++) {
                try {
                    log.info(">>> [image prehandle face detect] image path={}, count={}，starting...", 11, i);
                    faceRects = detectMultiFace(bytes);
                    if (faceRects.size() > 0) break;
                    log.info(">>> [image prehandle face detect] no face detected this time. imagePath={} ,count={}", 111, i);
                    BufferedImage bufferedImage = byteArrayToBufferedImage(bytes);
                    BufferedImage rotateImg = ImageUtil.rotateClockwise90(bufferedImage);
                    //                        String detectPath = getDestPath(new File(imagePath), "detect" + i);
                    //                        ImageIO.write(rotateImg, extension.get(), new File(detectPath));
                    bytes = fileAccessor.bufferedImageToByteArray(rotateImg, extension.get());

                } catch (Exception e) {
                    log.error(">>> [image prehandle face detect] face detect failed，imagePath= " + 11, e);
                    throw new BusinessException(ResStatusEnum.DETECT_FACE_FAILED.code(), ResStatusEnum.DETECT_FACE_FAILED.msg());
                }
            }

            if (faceRects.size() == 0) {
                log.info(">>> [image prehandle face detect] no face detected. imagePath={}", imagePath);
                throw new BusinessException(ResStatusEnum.NO_FACE_DETECTED.code(), ResStatusEnum.NO_FACE_DETECTED.msg());
            }

            if (!config.getEnableMultiface() && faceRects.size() > 1) {
                log.info(">>> [image prehandle face detect] multi face detected. imagePath={}", imagePath);
                throw new BusinessException(ResStatusEnum.MULTI_FACE_DETECTED.code(), ResStatusEnum.MULTI_FACE_DETECTED.msg());
            }

            Rectangle faceRect = faceRects.stream().max(Comparator.comparingInt(r -> (int) (r.getWidth() * r.getHeight()))).get();

            try {
                // 1.2 crop face image
                Double faceEnlargeRate = im_conf != null && im_conf.getFaceEnlargeRate() != null ? im_conf.getFaceEnlargeRate() : config.getFaceDetectConfig().getFaceEnlargeRate();
                faceRect = ImageUtil.resizeRectangle(faceRect, faceEnlargeRate.floatValue());

                BufferedImage bimg = byteArrayToBufferedImage(bytes);
                bimg = ImageUtil.cropImage(bimg, faceRect);

                // 1.3 resize image
                if (config.getFaceDetectConfig().getResizeImageEnabled())
                    bimg = ImageUtil.resizeImage(bimg, config.getFaceDetectConfig().getResizeImageWidth());

                try {
                    bytes = fileAccessor.bufferedImageToByteArray(bimg, extension.get());

                } catch (Exception e) {
                    log.error("resize image then write error:{}", e.getMessage());
                }
                if (bytes.length <= 0) {
                    // 发现type=6的存不下来，先转换为1
                    BufferedImage convertedImage = ImageUtil.convertToIntRGB(bimg);
                    try {
                        bytes = fileAccessor.bufferedImageToByteArray(convertedImage, extension.get());
                    } catch (Exception e) {
                        log.error(">>> [image prehandle face detect] cant save cropped Image... use original image");
                    }
                }

            } catch (Exception e) {
                log.error(">>> [image prehandle face detect] face detect, crop face failed...", e);
            }

            if (config.getEnabledSaveImage()) {
                ImageInfo imageInfo = new ImageInfo();
                imageInfo.setImagePath("/tmp");
                imageInfo.setData(bytes);
                String destPath = fileAccessor.writeImage(imageInfo);
                log.info("destPath = {}", destPath);
            }

            // 2. image quality
            Double qualityScore = null;
            qualityResList = new ArrayList<>();
            try {
                log.debug(">>> [image prehandle face quality] image face quality. imagePath={}, starting...", imagePath);
                qualityResList = verifyImageMultiFaceQuality(bytes);
            } catch (Exception e) {
                log.error(">>> [image prehandle face quality] quality dectet failed, imagePath = " + imagePath, e);
                throw new BusinessException(ResStatusEnum.DETECT_FACE_FAILED.code(), ResStatusEnum.DETECT_FACE_FAILED.msg());
            }

            if (qualityResList.size() == 0) {
                log.info(">>> [image prehandle face quality] no face detected. imagePath={}", imagePath);
                throw new BusinessException(ResStatusEnum.NO_FACE_DETECTED.code(), ResStatusEnum.NO_FACE_DETECTED.msg());
            }
            if (!config.getEnableMultiface() && qualityResList.size() > 1) {
                log.info(">>> [image prehandle face quality] multi face detected. imagePath={}", imagePath);
                throw new BusinessException(ResStatusEnum.MULTI_FACE_DETECTED.code(), ResStatusEnum.MULTI_FACE_DETECTED.msg());
            }
            QualityRes qualityRes = qualityResList.stream().max(Comparator.comparingInt(r -> (int) (r.getDetect().getWidth() * r.getDetect().getHeight()))).get();
            qualityScore = (double) qualityRes.getScore();
            if (qualityScore == null || qualityScore.doubleValue() < config.getQualityDetectConfig().getImageApproveScore()) {
                log.info(">>> [image prehandle face quality] image quality not passed. imagePath={},qualityScore:{}", imagePath, qualityScore);
                throw new BusinessException(ResStatusEnum.BAD_IMAGE_QUALITY.code(), ResStatusEnum.BAD_IMAGE_QUALITY.msg());
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("save file failed unknown error,msg:{}", e.getMessage());
            throw new BusinessException(ResStatusEnum.SAVE_FILE_FAILED.code(), ResStatusEnum.SAVE_FILE_FAILED.msg());
        }

        return qualityResList;
    }

	@SuppressWarnings("unchecked")
	@Operation(summary = "从人脸图片路径检验图片质量评分by ppl", method= "GET")
	@RequestMapping(value = "/getImageQualityByPipeline", method = RequestMethod.GET)
	public BaseRes<Map<String, Object>> getImageQualityByPipeline(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) {
		BlurHeadposeModelPipeline.FaceBody[] blurHeadposes = blurHeadposeModelPipeline.extractByPath(figureImageUrl);
		Map<String, Object>[] result = new Map[blurHeadposes.length];
		for(int index = 0; index < blurHeadposes.length; index ++) {
			result[index] = new HashMap<>();
			result[index].put("id", blurHeadposes[index].getId());
			result[index].put("quality", blurHeadposes[index].getQuality());
			result[index].put("score", blurHeadposes[index].getIntegrateQuality());
			result[index].put("confidence", blurHeadposes[index].getConfidence());
			result[index].put("aligner_confidence", blurHeadposes[index].getAlignerConfidence());
			result[index].put("detect", toDot(blurHeadposes[index].getHunter()));
		}
		return BaseRes.success(Map.of("face", result));
	}

	@Operation(summary = "从人脸图片base64检验图片质量评分NO-PPL", method= "POST")
	@RequestMapping(value = "/getImageQualityByImageBase64NoPPL", method = RequestMethod.POST)
	public BaseRes<Map<String, Object>> getImageQualityByImageBase64NoPPL(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws Exception {
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

		try {
			return getImageQualityByImage(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}

	@SuppressWarnings("unchecked")
	@Operation(summary = "从人脸图片路径检验图片质量评分", method= "GET")
	@RequestMapping(value = "/getImageQualityByImage", method = RequestMethod.GET)
	public BaseRes<Map<String, Object>> getImageQualityByImage(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) {
		BlurHeadpose [] blurHeadposes = blurHeadposeModelHandler.extractByPath(figureImageUrl);
		Map<String, Object>[] result = new Map[blurHeadposes.length];
		for(int index = 0; index < blurHeadposes.length; index ++) {
			result[index] = new HashMap<>();
			result[index].put("id", blurHeadposes[index].getId());
			result[index].put("score", blurHeadposes[index].getScore());
			result[index].put("detect", toDot(blurHeadposes[index].getHunter()));
		}
		return BaseRes.success(Map.of("face", result));
	}


	@SuppressWarnings("unchecked")
	@Operation(summary = "从人脸图片路径检验图片质量评分opencv", method= "GET")
	@RequestMapping(value = "/getImageQualityByImageOpenCv", method = RequestMethod.GET)
	public BaseRes<Map<String, Object>> getImageQualityByImageOpenCv(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) {

		BlurHeadposeSmallModelHandler.BlurHeadposeSmall[] blurHeadposes = blurHeadposeSmallModelHandler.extractByPath(figureImageUrl);
		Map<String, Object>[] result = new Map[blurHeadposes.length];

		Mat sourceImage = opencv_imgcodecs.imread(figureImageUrl);

		for(int index = 0; index < blurHeadposes.length; index ++) {

			result[index] = new HashMap<>();
			result[index].put("id", blurHeadposes[index].getId());
			result[index].put("score", blurHeadposes[index].getScore());

			int left = blurHeadposes[index].getHunter().getLeft();
			int top = blurHeadposes[index].getHunter().getTop();
			int width = blurHeadposes[index].getHunter().getWidth();
			int height = blurHeadposes[index].getHunter().getHeight();
			float extendedRate = 0.1f;

			org.bytedeco.opencv.opencv_core.Rect rect = new org.bytedeco.opencv.opencv_core.Rect(Math.max(0, left - (int)(width * extendedRate)), Math.max(0, top - (int)(height * extendedRate)) , width + (int)(width * extendedRate * 2) ,  height + (int)(height * extendedRate * 2));

			org.bytedeco.opencv.opencv_core.Point org1 = new Point(rect.x() , rect.y() );
			org.bytedeco.opencv.opencv_core.Point  org = new Point(rect.x() + rect.width(), rect.y() + rect.height());

			int thickness = 1;
			// 在图片上画出ROI框
			//opencv_imgproc.rectangle(sourceImage , org1,  org ,  new Scalar(0,255,255,0), 2,8,0);
			opencv_imgproc.rectangle(sourceImage, rect,  new Scalar(0,255,255,0), thickness, opencv_imgproc.LINE_AA, 0);

			// 在ROI框内部写入文本
			BigDecimal bd = BigDecimal.valueOf(blurHeadposes[index].getScore());
			bd = bd.setScale(4, RoundingMode.HALF_UP);

			Point org2 = new Point(rect.x() + thickness, rect.y() + thickness + 15);
			// 字体样式、字体大小、字体颜色
			int fontFace =opencv_imgproc.FONT_HERSHEY_DUPLEX;
			double fontScale = 0.8;
			Scalar color = new Scalar(0, 0, 255, 0);
/*			opencv_imgproc.putText(sourceImage, bd.toString(), org2, opencv_imgproc.CV_FONT_HERSHEY_PLAIN, opencv_imgproc.INTERSECT_PARTIAL, color, 1, 8,false);*/
			opencv_imgproc.putText(sourceImage, bd.toString(), org, fontFace, fontScale, color, thickness, opencv_imgproc.LINE_AA, false);
		}
		String fileName = figureImageUrl.substring(figureImageUrl.lastIndexOf('/') + 1);


		File path = new File(Utils.instance.savePath + "/opencv/" + Objects.requireNonNullElse(ImageUtils.dateFormatter_DAY.get().format(new Date()), "/") + "face");
		if(!path.exists())
			path.mkdirs();
		// 保存结果图片
		opencv_imgcodecs.imwrite(path.getAbsolutePath()+ "/" + fileName + "_opencv.jpg", sourceImage);

		return BaseRes.success(Map.of("face", result));
	}
	
	@SuppressWarnings("unchecked")
	@Operation(summary = "从人脸图片路径提取属性", method= "POST")
	@RequestMapping(value = "/getAttributeByImagePath", method = RequestMethod.POST)
	public BaseRes<Object> getAttributeByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception {
		Attribute[] attributes = attributeModelHandler.extractByPath(figureImageUrl);
		Map<String, Object>[] result = new Map[attributes.length];
		for(int index = 0; index < attributes.length; index ++) {
			result[index] = new HashMap<>();
			result[index].put("id", attributes[index].getId());
			result[index].put("attribute", attributes[index].getAttribute());
		}
		return BaseRes.success(Map.of("face", result));
	}
	
	@Operation(summary = "从人脸图片base64提取属性", method = "POST")
	@RequestMapping(value = "/getAttributeByImageBase64", method = RequestMethod.POST)
	public BaseRes<Object> getAttributeByImageBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws Exception {
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);
		
		try {
			return getAttributeByImagePath(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}
	
	@Operation(summary = "对比两个特征值，算出评分", method= "POST")
	@RequestMapping(value = "/compareFeatures", method = RequestMethod.POST)
	public BaseRes<Float> compareFeatures(
			  @Parameter(required = true, name = "feature1", description = "特征_1") @RequestParam String feature1
			, @Parameter(required = true, name = "feature2", description = "特征_2") @RequestParam String feature2) throws Exception {
		
		float[] feature_1 = FaissSeeker.stringToFeature(feature1);
		float[] feature_2 = FaissSeeker.stringToFeature(feature2);
		return BaseRes.success(FaissSeeker.compare_feature_normalize(feature_1, feature_2, SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint));
	}
	
	@Operation(summary = "对比两张图片base64，算出评分", method = "POST")
	@RequestMapping(value = "/compareImageBase64s", method = RequestMethod.POST)
	public BaseRes<Float> compareImageBase64s(
			  @Parameter(required = true, name = "figureImageBase64_1", description = "图片_1") @RequestParam String figureImageBase64_1
			, @Parameter(required = true, name = "figureImageBase64_2", description = "图片_2") @RequestParam String figureImageBase64_2) throws Exception {
		File tmpImage_1 = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64_1);
		File tmpImage_2 = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64_2);
		
		try {
			return compareImagePaths(tmpImage_1.getAbsolutePath(), tmpImage_2.getAbsolutePath());
		}finally {
			tmpImage_1.delete();
			tmpImage_2.delete();
		}
	}
	
	@Operation(summary = "对比两张图片路径，算出评分", method = "POST")
	@RequestMapping(value = "/compareImagePaths", method = RequestMethod.POST)
	public BaseRes<Float> compareImagePaths(
			  @Parameter(required = true, name = "figureImageUrl_1", description = "图片_1") @RequestParam String figureImageUrl_1
			, @Parameter(required = true, name = "figureImageUrl_2", description = "图片_2") @RequestParam String figureImageUrl_2) throws Exception {
		
		Feature[] feature_1 = featureModelHandler.extractByPath(figureImageUrl_1);
		Feature[] feature_2 = featureModelHandler.extractByPath(figureImageUrl_2);
		if(ArrayUtils.isEmpty(feature_1) || ArrayUtils.isEmpty(feature_2)) {
            log.error("image has no face.");
            throw new BusinessException("3001","image has no face.");
        }
		
		return BaseRes.success(FaissSeeker.compare_feature_normalize(feature_1[0].getFeature(), feature_2[0].getFeature(), SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint));
	}
	
	@Operation(summary = "全表重新提取特征", method = "GET")
	@RequestMapping(value = "/reExtractFully", method = RequestMethod.GET)
    public synchronized String reExtractFully() throws Exception {
		log.info("start reExtractFully");
		try(Connection conn = dataSource.getConnection()){
			try(Statement stat = conn.createStatement()){
				stat.executeUpdate("update person_face_feature set image_feature = '', model_version = '';");
				stat.executeUpdate("update passer_face_feature set image_feature = '';");
			}
		}

		int maxCusor = Objects.requireNonNullElse(personMapper.queryMaxId(), 0);
		for(int cursor = 0; cursor < maxCusor; cursor += 10000) {
			List<PersonFaceFeature> pfs = personMapper.querySplit(cursor, cursor + 10000, 1, 0);
			pfs.parallelStream().forEach(person ->{
				try {
					Feature[] features = featureModelHandler.extractByPath(person.getAvatarImageUrl());
					String feature = FaissSeeker.featureToString(features[0].getFeature());
					person.setImageFeature(feature);
					person.setModelVersion(Initializer.modelPathMap.get("feature_module"));
					personMapper.save(person);
				}catch(Exception e) {
					log.warn("person id error : {},{},{}" , person.getPersonUuid(), e.getMessage(), person.getAvatarImageUrl());
				}
			}); 
			
			personMapper.flush();

			if(cursor > 0 && cursor  % 10000  == 0){
				log.info("reExtract for person, Every 1000 prints{}", cursor);
			}
		}
		log.info("reExtractFully SUCCESS person, cursor{}", maxCusor);

    	maxCusor = Objects.requireNonNullElse(passerMapper.queryMaxId(), 0);
		for(int cursor = 0; cursor < maxCusor; cursor += 10000) {
			List<PasserFaceFeature> pfs = passerMapper.querySplit(cursor, cursor + 10000, 1, 0);
			pfs.parallelStream().forEach(passer ->{
				try {
					Feature[] features = featureModelHandler.extractByPath(passer.getAvatarImageUrl());
					String feature = FaissSeeker.featureToString(features[0].getFeature());
					passer.setImageFeature(feature);
					passerMapper.save(passer);
				}catch(Exception e) {
					log.warn("passer id error :{}, {}, {} " , passer.getId(), e.getMessage(), passer.getAvatarImageUrl());
				}
			});
			
			passerMapper.flush();
			if(cursor > 0 && cursor  % 10000  == 0){
				log.info("reExtract for person, Every 1000 prints{}", cursor);
			}
		}
		log.info("reExtractFully success passer, cursor{}", maxCusor);

		return "OK";
	}

	@Operation(summary = "全表重新提取特征到静态库", method = "GET")
	@RequestMapping(value = "/reExtractFullyToSfd", method = RequestMethod.GET)
	public synchronized String reExtractFullyToSfd() throws Exception {
		log.info("start reExtractFully");
		try(Connection conn = dataSource.getConnection()){
			try(Statement stat = conn.createStatement()){
				stat.executeUpdate("update person_face_feature set image_feature = '', model_version = '';");
				stat.executeUpdate("update passer_face_feature set image_feature = '';");
			}
		}

		int maxCusor = Objects.requireNonNullElse(personMapper.queryMaxId(), 0);
		for(int cursor = 0; cursor < maxCusor; cursor += 10000) {
			List<PersonFaceFeature> pfs = personMapper.querySplit(cursor, cursor + 10000, 1, 0);
			pfs.parallelStream().forEach(person ->{
				try {
					Feature[] features = featureModelHandler.extractByPath(person.getAvatarImageUrl());
					String feature = FaissSeeker.featureToString(features[0].getFeature());
					person.setImageFeature(feature);
					person.setModelVersion(Initializer.modelPathMap.get("feature_module"));
					personMapper.save(person);
				}catch(Exception e) {
					log.warn("person id error : {},{},{}" , person.getPersonUuid(), e.getMessage(), person.getAvatarImageUrl());
				}
			});

			personMapper.flush();

			if(cursor > 0 && cursor  % 10000  == 0){
				log.info("reExtract for person, Every 1000 prints{}", cursor);
			}
		}
		log.info("reExtractFully SUCCESS person, cursor{}", maxCusor);

		maxCusor = Objects.requireNonNullElse(passerMapper.queryMaxId(), 0);
		for(int cursor = 0; cursor < maxCusor; cursor += 10000) {
			List<PasserFaceFeature> pfs = passerMapper.querySplit(cursor, cursor + 10000, 1, 0);
			pfs.parallelStream().forEach(passer ->{
				try {
					Feature[] features = featureModelHandler.extractByPath(passer.getAvatarImageUrl());
					String feature = FaissSeeker.featureToString(features[0].getFeature());
					passer.setImageFeature(feature);
					passerMapper.save(passer);
				}catch(Exception e) {
					log.warn("passer id error :{}, {}, {} " , passer.getId(), e.getMessage(), passer.getAvatarImageUrl());
				}
			});

			passerMapper.flush();
			if(cursor > 0 && cursor  % 10000  == 0){
				log.info("reExtract for person, Every 1000 prints{}", cursor);
			}
		}
		log.info("reExtractFully success passer, cursor{}", maxCusor);

		return "OK";
	}

	@Operation(summary = "全表重新提取特征NoHunter", method= "GET")
	@RequestMapping(value = "/reExtractFullyNoHunter", method = RequestMethod.GET)
	public synchronized String reExtractFullyNoHunter() throws Exception {
		try(Connection conn = dataSource.getConnection()){
			try(Statement stat = conn.createStatement()){
				stat.executeUpdate("update person_face_feature set image_feature = '', model_version = '';");
				stat.executeUpdate("update passer_face_feature set image_feature = '';");
			}
		}
		int maxCusor = Objects.requireNonNullElse(personMapper.queryMaxId(), 0);
		for(int cursor = 0; cursor < maxCusor; cursor += 10000) {
			List<PersonFaceFeature> pfs = personMapper.querySplit(cursor, cursor + 10000, 1, 0);
			pfs.parallelStream().forEach(person ->{
				try {
					Feature[] features = roiFeatureModelHandler.extractByPath(person.getAvatarImageUrl());
					String feature = FaissSeeker.featureToString(features[0].getFeature());
					person.setImageFeature(feature);
					person.setModelVersion(Initializer.modelPathMap.get("feature_module"));
					personMapper.save(person);
				}catch(Exception e) {
					log.warn("person id error : " + person.getPersonUuid(), e);
				}
			});

			personMapper.flush();
		}

		maxCusor = Objects.requireNonNullElse(passerMapper.queryMaxId(), 0);
		for(int cursor = 0; cursor < maxCusor; cursor += 10000) {
			List<PasserFaceFeature> pfs = passerMapper.querySplit(cursor, cursor + 10000, 1, 0);
			pfs.parallelStream().forEach(passer ->{
				try {
					Feature[] features = roiFeatureModelHandler.extractByPath(passer.getAvatarImageUrl());
					String feature = FaissSeeker.featureToString(features[0].getFeature());
					passer.setImageFeature(feature);
					passerMapper.save(passer);
				}catch(Exception e) {
					log.warn("person id error : " + passer.getId(), e);
				}
			});

			passerMapper.flush();
		}

		return "OK";
	}

	@Operation(summary = "重新提取personId的特征", method= "GET")
	@RequestMapping(value = "/reExtractByPersonId", method = RequestMethod.GET)
    public synchronized String reExtractByPersonId(@Parameter(required = true, name = "personId", description = "personId") @RequestParam String personId) throws Exception {
		List<PersonFaceFeature> persons = personMapper.findByPersonUuid(personId);
		for(PersonFaceFeature person : persons) {
			Feature[] features = featureModelHandler.extractByPath(person.getAvatarImageUrl());
			String feature = FaissSeeker.featureToString(features[0].getFeature());
			person.setImageFeature(feature);
			person.setModelVersion(Initializer.modelPathMap.get("feature_module"));
			personMapper.save(person);
		}
		
		List<PasserFaceFeature> passers = passerMapper.findByPersonUuid(personId);
		for(PasserFaceFeature passer : passers) {
			Feature[] features = featureModelHandler.extractByPath(passer.getAvatarImageUrl());
			String feature = FaissSeeker.featureToString(features[0].getFeature());
			passer.setImageFeature(feature);
			passerMapper.save(passer);
		}
		
		personMapper.flush();
		return "OK";
    }
	
	@Operation(summary = "检查库内特征与模型不匹配的数量", method= "GET")
	@RequestMapping(value = "/checkFeatureCorrect", method = RequestMethod.GET)
    public synchronized Object checkFeatureCorrect() throws Exception {
		int person = personMapper.countByModelVersionNot(Initializer.modelPathMap.get("feature_module"));
		return Map.of("person" , person);
    }

    public List<Rectangle> detectMultiFace(byte[] imagePath, Integer... retryCnt) {
        List<Rectangle> rectangle = new ArrayList<>();
        BaseRes<Object> detectByImagePath = null;

        int _retryCnt = 0;
        if (retryCnt != null && retryCnt.length > 0)
            _retryCnt = retryCnt[0];

        try {
            detectByImagePath = getDetectByImageByte(imagePath);
        } catch (Exception e) {
            if (_retryCnt >= 3)
                throw new RuntimeException("[cog service] call getDetectByImagePath failed! image path: " + imagePath, e);
            log.error(">>> [cog service] call getDetectByImagePath failed! image path: " + imagePath, e);
            try {
                Thread.sleep(300);
            } catch (InterruptedException ex) {
                log.error(">>> [cog service] thread sleep failed! ", ex);
            }
            _retryCnt = _retryCnt + 1;
            rectangle = detectMultiFace(imagePath, _retryCnt);
        }
        //parse detectByImagePath
        try {
            checkDetectResult(JSON.parseObject(JSON.toJSONString(detectByImagePath)));
        } catch (Exception e) {
            throw new RuntimeException("[cog service] call getDetectByImagePath failed! image path: " + imagePath, e);
        }

        try {
            log.debug(">>> [cog service] response of image face detect: {}", JSON.toJSONString(detectByImagePath));
            JSONObject data = JSON.parseObject(JSON.toJSONString(detectByImagePath)).getJSONObject("data");

            Set<String> detectedKeys = data.keySet();
            if (detectedKeys.size() <= 0)
                return rectangle;

            // return all face
            rectangle = detectedKeys.stream()
                    .map(k -> {
                        JSONArray face = data.getJSONArray(k);
                        JSONArray point01 = face.getJSONArray(0);
                        JSONArray point02 = face.getJSONArray(1);
                        return new Rectangle(point01.getInteger(0), point01.getInteger(1), point02.getInteger(0) - point01.getInteger(0), point02.getInteger(1) - point01.getInteger(1));
                    }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error(">>> [cog service] detectFace parse response failed! image path:" + 11, e);
        }
        return rectangle;

    }

    private void checkDetectResult(JSONObject repjson) throws Exception {
        String code = repjson.getString("code");
        if (!code.equals("0000")) {
            throw new Exception();
        }
    }

    public BaseRes<Object> getDetectByImageByte(byte[] bytes) throws Exception {

        Map<String, Object> data = new HashMap<String, Object>();
        try {
            BlurHeadpose[] detect = blurHeadposeModelHandler.extractByBytes(bytes);
            for (int index = 0; index < detect.length; index++)
                data.put("face_" + index, toDot(detect[index].getHunter()));
        } catch (Exception e) {
            log.error("getDetectByImagePath throw exception{},figureImageUrl:{}", e.getMessage());
        }
        return BaseRes.success(data);
    }
    public BaseRes<List<com.sensetime.intersense.cognitivesvc.server.entities.QualityRes>> verifyImageMultiFaceQualityCog(byte[] bytes) {
        List<com.sensetime.intersense.cognitivesvc.server.entities.QualityRes> result = new ArrayList();
        try {
            BlurHeadposeModelPipeline.FaceBody[] blurHeadposes = blurHeadposeModelPipeline.extractByBytes(bytes);
//            if (ArrayUtils.isEmpty(blurHeadposes) || blurHeadposes.length != 1)
//                throw new RuntimeException("no face or more than one face. face count : [" + blurHeadposes.length + "]");
            if (ArrayUtils.isEmpty(blurHeadposes)) {
                log.warn("verifyImageMultiFaceQuality figureImageUrl {}, has no face", 11);
                return BaseRes.success(new ArrayList<>());
            }
            for (int i = 0; i < blurHeadposes.length; i++) {
                com.sensetime.intersense.cognitivesvc.server.entities.QualityRes qualityRes = com.sensetime.intersense.cognitivesvc.server.entities.QualityRes.builder()
                        .score((float) blurHeadposes[i].getIntegrateQuality())
                        .detect(blurHeadposes[i].getHunter())
						.id(blurHeadposes[i].getId())
						.quality(blurHeadposes[i].getQuality())
						.confidence(blurHeadposes[i].getConfidence())
						.alignerConfidence(blurHeadposes[i].getAlignerConfidence())
                        .build();
                result.add(qualityRes);
            }

        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        }

        return BaseRes.success(result);
    }

    public List<com.sensetime.intersense.cognitivesvc.client.response.entity.QualityRes> verifyImageMultiFaceQuality(byte[] bytes, Integer... retryCnt) throws Exception {
        List<com.sensetime.intersense.cognitivesvc.client.response.entity.QualityRes> result = new ArrayList<>();
        //Double face_score = null;
        Object response = null;

        int _retryCnt = 0;
        if (retryCnt != null && retryCnt.length > 0)
            _retryCnt = retryCnt[0];

        try {
            response = verifyImageMultiFaceQualityCog(bytes);
        } catch (Exception e) {
            if (_retryCnt >= 3)
                throw new Exception("[cog service] call verifyImageQuality failed! image path: " + 1, e);
            try {
                Thread.sleep(300);
            } catch (InterruptedException ex) {
                log.error(">>> [cog service] thread sleep failed!", ex);
            }
            _retryCnt = _retryCnt + 1;
            result = verifyImageMultiFaceQuality(bytes, _retryCnt);
        }
        //parse response
        try {
            checkDetectResult(JSON.parseObject(JSON.toJSONString(response)));
        } catch (Exception e) {
            throw new Exception("[cog service] call verifyImageQuality failed! image path: " + 11, e);
        }
        try {
            String resJsonStr = JSON.toJSONString(response);
            log.debug(">>> [cog service] response of image quality: {}", resJsonStr);
            JSONObject resJson = JSON.parseObject(resJsonStr);
            List<com.sensetime.intersense.cognitivesvc.client.response.entity.QualityRes> imageQuality = JSON.parseArray(resJson.getString("data"), com.sensetime.intersense.cognitivesvc.client.response.entity.QualityRes.class);

            if (imageQuality.size() == 0) {
                log.warn(">>> [cog service] imageQuality no face detect");
            } else {
                result = imageQuality;
            }
        } catch (Exception e) {
            log.error(">>> [cog service] verifyImageQuality parse response failed! image path:" + 11, e);
        }
        return result;
    }

	public BufferedImage byteArrayToBufferedImage(byte[] imageData) {
		try {
			ByteArrayInputStream bais = new ByteArrayInputStream(imageData);
			BufferedImage bufferedImage = ImageIO.read(bais);
			if (bufferedImage != null) {
				return bufferedImage;
			} else {
				log.error(">>> storage io ByteArrayToBufferedImage fail");
				throw new RuntimeException();
			}
		} catch (Exception e) {
			log.error(">>> storage io ByteArrayToBufferedImage fail e: {}", e.getMessage());
			throw new RuntimeException(e);
		}
	}

	public static int[][] toDot(AbstractHandler.Detection.Rect rect) {
		return new int[][]{new int[]{rect.getLeft(), rect.getTop()}, new int[]{rect.getLeft() + rect.getWidth(), rect.getTop() + rect.getHeight()}};
	}
}
