package com.sensetime.intersense.cognitivesvc.streamface.zutils;

import java.awt.Polygon;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_packetLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_packet_t;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PasserFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PersonFaceObject;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamFace;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sun.jna.Pointer;

import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;

@Builder
@Getter
@Slf4j
public class FaceTrack{
	public static enum RegStage{ idle, queued, extracted, identified }
	
	private final VideoStreamInfra streamInfra;
	
	private final VideoStreamFace  streamFace;

	private volatile Image image;
	@Setter
	private volatile Integer trackId;
	@Setter
	private volatile Map<String, Object> target;
	@Setter
	private volatile Map<String, Object> tracklet;
	@Setter
	private volatile long queuedTime;
	@Setter
	@Builder.Default
	private volatile RegStage regFaceStage = RegStage.idle;
	@Setter
	@Builder.Default
	private volatile RegStage regAttributeStage = RegStage.idle;
	@Setter
	@Builder.Default
	private volatile Integer handleTargetAgainFlag = 0;
	@Setter
	private volatile float[] feature;
	@Setter
	private volatile Map<String, Object> attribute;
	@Setter
	private volatile PersonFaceObject personObj;
	@Setter
	private volatile Float regPersonScore;
	@Setter
	private volatile PasserFaceObject passerObj;
	@Setter
	private volatile Float regPasserScore;
	@Setter
	@Builder.Default
	private volatile boolean isNewPasser = false;
	@Setter
	@Builder.Default
	private volatile int regTimes = Utils.instance.unknownRegTimeCount;
	@Setter
	@Builder.Default
	/** 是否发送了unkownMessage*/
	private volatile boolean unkownMessageSended = false;
	@Setter
	@Builder.Default
	/** 是否发送了imageCapture*/
	private volatile boolean imageCaptureSended = false;
	@Setter
	@Builder.Default
	/** 是否发送了patternDetect*/
	private volatile boolean patternDetectSended = false;
	@Setter
	@Builder.Default
	/** 这个track已经处于流的第几帧*/
	private volatile long frameIndex = 0;
	@Setter
	@Builder.Default
	/** 这个track已经处于流的多少pts*/
	private volatile long framePts = 0;
	@Setter
	@Builder.Default
	/** 解帧耗时*/
	private volatile long frameDecodeCost = 0;
	@Setter
	@Builder.Default
	private volatile String reason = "";

//	@Setter
//	private volatile long modeTimeInterValTime;

	/**
	 * 质量过滤
	 * {"aligner_confidence":0.9976553,"confidence":0.96839416,"pitch":16.171875,"quality":0.6217376,"roll":-4.477449,"yaw":2.8368573},
	 *
	 * @param face_target
	 * @return
	 */
	public boolean check(Map<String, Object> face_target) {
		boolean checked = true;
		checked &= ArrayUtils.isNotEmpty(getFaceInRois(face_target));
		checked &= isFaceSizeEnough(face_target);
		checked &= isFaceQualified(face_target);
		return checked;
	}
	public Pair<Boolean, String>  checkTracklet(Map<String, Object> face_target, String deviceID, String fileName) {
		String reasonStep ="";
		boolean checked = true;
//		checked &= ArrayUtils.isNotEmpty(getFaceInRoisTracklet(face_target));
//		if(!ArrayUtils.isNotEmpty(getFaceInRoisTracklet(face_target))){
//			if(Utils.instance.logged) {
//				log.info("checkTracklet1{},{}--,{}", face_target, checked, deviceID);
//			}
//		}
		checked &= isFaceSizeEnoughTracklet(face_target);
		if(!checked){
			reasonStep = "faceSize";
			if(Utils.instance.logged) {
				if(Utils.instance.dropFace == 0) {
					face_target.put("isFaceSizeEnoughTracklet", checked);
					ImageUtils.newFileNameJson("dropFaceJson", fileName, face_target,"face");
				}
				log.info("checkTrackletFaceSizeEnough checkFail{},{},--{}", face_target, checked, deviceID);
			}
		}
		Pair<Boolean, String> pair = isFaceQualifiedTracklet(face_target);

		checked &= pair.getLeft();
		if(!pair.getRight().isEmpty()) {
			reasonStep = pair.getRight();
		}
		if(!checked){
			if(Utils.instance.logged) {
				if(Utils.instance.dropFace == 0) {
					face_target.put("isFaceQualifiedTracklet", checked);
					ImageUtils.newFileNameJson("dropFaceJson", fileName, face_target,"face");
				}
				log.info("checkTrackletFaceQualified checkFail{},{},--{}", face_target, checked, deviceID);
			}
		}
		return new MutablePair<Boolean, String>(checked, reasonStep);
	}

	public ProcessorDrop getProcessFace(){
		if(Utils.instance.dropFaceQuality.isEmpty()){
			return new ProcessorDrop();
		}
		return JSON.parseObject(Utils.instance.dropFaceQuality, ProcessorDrop.class);
	}

	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	@Data
	public static final class ProcessorDrop {
		public static final ProcessorDrop empty = new ProcessorDrop();

		@Getter @Setter
		private Float inteGrateQuality = 0f;

		@Getter @Setter
		private Float quality = 0f;

		@Getter @Setter
		private int faceSizeLimit =0;

		@Getter @Setter
		private Float yaw = 0f;

		@Getter @Setter
		private Float pitch =0f;

		@Getter @Setter
		private Float roll =0f;


	}

	public Pair<Boolean, String>  checkTrackletDrop(Map<String, Object> face_target, String deviceID, String fileName) {

		String reasonStep ="";
		boolean checked = true;

		checked &= isFaceSizeEnoughTrackletDrop(face_target);
		if(!checked){
			reasonStep = "faceSize";
			if(Utils.instance.logged) {
				if(Utils.instance.dropFace == 0) {
					face_target.put("isFaceSizeEnoughTrackletDrop", checked);
					//ImageUtils.newFileNameJson("drop_face_cog_json", fileName, face_target);
				}
				log.info("checkTrackletDrop checkFail{},{},--{},{}", face_target, checked, deviceID,reasonStep);
			}
		}
		Pair<Boolean, String> pair = isFaceQualifiedTrackletDrop(face_target);

		checked &= pair.getLeft();
		if(!pair.getRight().isEmpty()) {
			reasonStep = pair.getRight();
		}
		if(!checked){
			if(Utils.instance.logged) {
				if(Utils.instance.dropFace == 0) {
					face_target.put("isFaceQualifiedTrackletDrop", checked);
					//ImageUtils.newFileNameJson("drop_face_cog_json", fileName, face_target);
				}
				log.info("checkTrackletDrop checkFail{},{},--{},{}", face_target, checked, deviceID, reasonStep);
			}
		}
		return new MutablePair<Boolean, String>(checked, reasonStep);
	}

	@SuppressWarnings("unchecked")
	public boolean isFaceSizeEnough(Map<String, Object> target) {
		int faceSizeLimit = Objects.requireNonNullElse(streamFace.getMinFaceSize(), Utils.instance.faceSizeLimit);		
		
		int width = ((Map<String, Number>)target.get("roi")).get("width").intValue();
		int height = ((Map<String, Number>)target.get("roi")).get("height").intValue();
		return width >= faceSizeLimit && height >= faceSizeLimit;
	}
	@SuppressWarnings("unchecked")
	public boolean isFaceSizeEnoughTracklet(Map<String, Object> target) {
		int faceSizeLimit = Objects.requireNonNullElse(streamFace.getMinFaceSize(), Utils.instance.faceSizeLimit);

		int width = ((Map<String, Number>)target.get("target_image_roi")).get("width").intValue();
		int height = ((Map<String, Number>)target.get("target_image_roi")).get("height").intValue();
		return width >= faceSizeLimit && height >= faceSizeLimit;
	}

	@SuppressWarnings("unchecked")
	public boolean isFaceSizeEnoughTrackletDrop(Map<String, Object> target) {
		int faceSizeLimit = getProcessFace().getFaceSizeLimit();

		int width = ((Map<String, Number>)target.get("target_image_roi")).get("width").intValue();
		int height = ((Map<String, Number>)target.get("target_image_roi")).get("height").intValue();
		return width >= faceSizeLimit && height >= faceSizeLimit;
	}

	public boolean isFaceQualified(Map<String, Object> target) {
		float imageQuality = Objects.requireNonNullElse(streamFace.getBaseImageQuality(), Utils.instance.imageQuality);

		Number quality            = (Number)target.get("quality");
		Number detect_confidence = (Number) target.get("confidence");
		Number aligner_confidence = (Number) target.get("aligner_confidence");
		Number yaw = (Number) target.get("yaw");
		Number pitch = (Number) target.get("pitch");
		Number roll = (Number) target.get("roll");

		boolean confidence = true;

		if (quality != null)
			confidence &= quality.floatValue() > imageQuality;

		//add 質量分
		if (Utils.instance.shieldFaceSwitch == 0) {
			//log.info("detect_confidence{},{}", detect_confidence, aligner_confidence);
			if (detect_confidence != null && aligner_confidence != null) {
				float qualitySum = detect_confidence.floatValue() * aligner_confidence.floatValue();
				confidence &= qualitySum > imageQuality;
			} else {
				if (detect_confidence != null)
					confidence &= detect_confidence.floatValue() > imageQuality;

				if (aligner_confidence != null)
					confidence &= aligner_confidence.floatValue() > imageQuality;
			}
		} else {
			if (detect_confidence != null)
				confidence &= detect_confidence.floatValue() > imageQuality;

			if (aligner_confidence != null)
				confidence &= aligner_confidence.floatValue() > imageQuality;
		}

//		if (detect_confidence != null)
//			confidence &= detect_confidence.floatValue() > imageQuality;
//
//		if (aligner_confidence != null)
//			confidence &= aligner_confidence.floatValue() > imageQuality;

		if (streamFace.getYaw() != null && yaw != null)
			confidence &= Math.abs(yaw.floatValue()) <= streamFace.getYaw();
		
		if(streamFace.getPitch() != null && pitch != null)
			confidence &= Math.abs(pitch.floatValue()) <= streamFace.getPitch();
		
		if(streamFace.getRoll() != null && roll != null)
			confidence &= Math.abs(roll.floatValue()) <= streamFace.getRoll();
		
		return confidence;
	}

	public Pair<Boolean, String> isFaceQualifiedTracklet(Map<String, Object> target) {
		float imageQuality = Objects.requireNonNullElse(streamFace.getBaseImageQuality(), Utils.instance.imageQuality);

		Number quality            = (Number)target.get("pageant_quality");
		Number detect_confidence = (Number) target.get("confidence");
		Number aligner_confidence = (Number) target.get("aligner_confidence");
		Number yaw = (Number) target.get("yaw");
		Number pitch = (Number) target.get("pitch");
		Number roll = (Number) target.get("roll");

		boolean confidence = true;

		if (quality != null) {
			confidence &= quality.floatValue() > imageQuality;
			if(!confidence){
				return new MutablePair<Boolean, String>(confidence, "imageQualityFilter");
			}
		}

		//add 質量分
		if (Utils.instance.shieldFaceSwitch == 0) {
			//log.info("detect_confidence{},{}", detect_confidence, aligner_confidence);
			if (detect_confidence != null && aligner_confidence != null) {
				float qualitySum = detect_confidence.floatValue() * aligner_confidence.floatValue();
				confidence &= qualitySum > imageQuality;
				if(!confidence){
					return new MutablePair<Boolean, String>(confidence, "alignerConfidenceFilter");
				}
			} else {
				if (detect_confidence != null) {
					confidence &= detect_confidence.floatValue() > imageQuality;
					if(!confidence){
						return new MutablePair<Boolean, String>(confidence, "alignerConfidenceFilter");
					}
				}

				if (aligner_confidence != null) {
					confidence &= aligner_confidence.floatValue() > imageQuality;
					if(!confidence){
						return new MutablePair<Boolean, String>(confidence, "alignerConfidenceFilter");
					}
				}
			}
		}

//		if (detect_confidence != null)
//			confidence &= detect_confidence.floatValue() > imageQuality;
//
//		if (aligner_confidence != null)
//			confidence &= aligner_confidence.floatValue() > imageQuality;

		if (streamFace.getYaw() != null && yaw != null) {
			confidence &= Math.abs(yaw.floatValue()) <= streamFace.getYaw();
			if(!confidence){
				return new MutablePair<Boolean, String>(confidence, "yawFilter");
			}
		}

		if(streamFace.getPitch() != null && pitch != null) {
			confidence &= Math.abs(pitch.floatValue()) <= streamFace.getPitch();
			if(!confidence){
				return new MutablePair<Boolean, String>(confidence, "pitchFilter");
			}
		}

		if(streamFace.getRoll() != null && roll != null) {
			confidence &= Math.abs(roll.floatValue()) <= streamFace.getRoll();
			if(!confidence){
				return new MutablePair<Boolean, String>(confidence, "rollFilter");
			}
		}

		return new MutablePair<Boolean, String>(confidence, "");
	}

	public Pair<Boolean, String> isFaceQualifiedTrackletDrop(Map<String, Object> target) {

		float imageQuality = getProcessFace().getQuality();

		Number quality            = (Number)target.get("pageant_quality");
		Number detect_confidence = (Number) target.get("confidence");
		Number aligner_confidence = (Number) target.get("aligner_confidence");
		Number yaw = (Number) target.get("yaw");
		Number pitch = (Number) target.get("pitch");
		Number roll = (Number) target.get("roll");

		boolean confidence = true;

		if (quality != null) {
			confidence &= quality.floatValue() > imageQuality;
			if(!confidence){
				return new MutablePair<Boolean, String>(confidence, "imageQualityFilter");
			}
		}

		//add 質量分
		if (Utils.instance.shieldFaceSwitch == 0) {
			//log.info("detect_confidence{},{}", detect_confidence, aligner_confidence);
			if (detect_confidence != null && aligner_confidence != null) {
				float qualitySum = detect_confidence.floatValue() * aligner_confidence.floatValue();
				confidence &= qualitySum > imageQuality;
				if(!confidence){
					return new MutablePair<Boolean, String>(confidence, "alignerConfidenceFilter");
				}
			} else {
				if (detect_confidence != null) {
					confidence &= detect_confidence.floatValue() > imageQuality;
					if(!confidence){
						return new MutablePair<Boolean, String>(confidence, "alignerConfidenceFilter");
					}
				}

				if (aligner_confidence != null) {
					confidence &= aligner_confidence.floatValue() > imageQuality;
					if(!confidence){
						return new MutablePair<Boolean, String>(confidence, "alignerConfidenceFilter");
					}
				}
			}
		}


		if (getProcessFace().getYaw() > 0 && yaw != null) {
			confidence &= Math.abs(yaw.floatValue()) <= streamFace.getYaw();
			if(!confidence){
				return new MutablePair<Boolean, String>(confidence, "yawFilter");
			}
		}

		if(getProcessFace().getPitch()  > 0  && pitch != null) {
			confidence &= Math.abs(pitch.floatValue()) <= streamFace.getPitch();
			if(!confidence){
				return new MutablePair<Boolean, String>(confidence, "pitchFilter");
			}
		}

		if(getProcessFace().getRoll()  > 0 && roll != null) {
			confidence &= Math.abs(roll.floatValue()) <= streamFace.getRoll();
			if(!confidence){
				return new MutablePair<Boolean, String>(confidence, "rollFilter");
			}
		}

		return new MutablePair<Boolean, String>(confidence, "");
	}

	public boolean isIntegrateQuality(Map<String, Object> target, Float deviceIntegrateQuality) {

		float integrateQuality =  (deviceIntegrateQuality != null) ? deviceIntegrateQuality: Utils.instance.integrateQuality;
		if(Utils.instance.logged) {
			log.info("[v2.8.7]change integrate quality {},{}", deviceIntegrateQuality, integrateQuality);
		}

		Number quality         = (Number)target.get("integrate_quality");

		boolean confidence = true;

		if (quality != null)
			confidence &= quality.floatValue() > integrateQuality;

		return confidence;
	}

	public boolean isIntegrateQualityDrop(Map<String, Object> target) {

		float integrateQuality =  getProcessFace().getInteGrateQuality();

		Number quality         = (Number)target.get("integrate_quality");

		boolean confidence = true;

		if (quality != null)
			confidence &= quality.floatValue() > integrateQuality;

		return confidence;
	}

	@SuppressWarnings("unchecked")
	public int[] getFaceInRois(Map<String, Object> target) {
		Polygon[] rois = streamFace.queryPolygons();
		if(ArrayUtils.isEmpty(rois))
			return new int[] { -1 };
		
		Map<String, Number> roi = (Map<String, Number>)target.get("roi");

		int top    = roi.get("top").intValue();
		int left   = roi.get("left").intValue();
		int width  = roi.get("width").intValue();
		int height = roi.get("height").intValue();
		
		int faceX = left + (width  / 2);
		int faceY = top  + (height / 2);
		
		return IntStream.range(0, rois.length)
						.map(index -> rois[index].contains(faceX, faceY) ? index : -1)
						.filter(index -> index >= 0)
						.toArray();
	}
	@SuppressWarnings("unchecked")
	public int[] getFaceInRoisTracklet(Map<String, Object> target) {
		Polygon[] rois = streamFace.queryPolygons();
		if(ArrayUtils.isEmpty(rois))
			return new int[] { -1 };

		Map<String, Number> roi = (Map<String, Number>)target.get("target_image_roi");

		int top    = roi.get("top").intValue();
		int left   = roi.get("left").intValue();
		int width  = roi.get("width").intValue();
		int height = roi.get("height").intValue();

		int faceX = left + (width  / 2);
		int faceY = top  + (height / 2);

		return IntStream.range(0, rois.length)
				.map(index -> rois[index].contains(faceX, faceY) ? index : -1)
				.filter(index -> index >= 0)
				.toArray();
	}
	
	public List<String> queryFaceInRoiIds(){
    	try {
    		int[] roiIndexArray = getFaceInRois(target);
    		
			if(ArrayUtils.isNotEmpty(roiIndexArray) && StringUtils.isNotBlank(streamFace.getRoiIds())) {
				List<String> ids = JSON.parseArray(streamFace.getRoiIds(), String.class);
				return Arrays.stream(roiIndexArray).filter(index -> index >= 0 && index < ids.size()).mapToObj(index -> ids.get(index)).collect(Collectors.toList());
			}
		}catch(Exception e) {
			e.printStackTrace();
		}
    	
    	return List.of();
    }
	
	public boolean isRegScoreVeryGood() {
		return Objects.requireNonNullElse(regPersonScore, Objects.requireNonNullElse(regPasserScore, -1f)) >= Objects.requireNonNullElse(Utils.instance.Lv3Threshold, streamFace.getBaseThreshold());
	}
	
	public boolean isRecognized() {
		return  personObj != null || passerObj != null || Utils.instance.featureSearchSfd;
	}
	
	public boolean isShouldRegAgain(Map<String, Object> targetJson) {
		float oldQuality = ((Number)target.get("quality")).floatValue();
		float newQuality = ((Number)targetJson.get("quality")).floatValue();
		
		boolean isQualityBetter = newQuality > oldQuality;
		boolean isPersonRegNotGood = personObj != null && regPersonScore <= Objects.requireNonNullElse(streamFace.getBaseThreshold(), Utils.instance.Lv1Threshold);
		boolean isPasserRegNotGood = personObj == null && passerObj != null && regPasserScore <= Objects.requireNonNullElse(streamFace.getBaseThreshold(), Utils.instance.Lv2Threshold);
		
		return !isRecognized() || (isQualityBetter && (isPersonRegNotGood || isPasserRegNotGood));
	}

	public synchronized void setImage(Pointer frame, Map<String, Number> roi) {
		if(image != null)
			image.close();
		
		image = Image.builder()
					 .faceImage(FrameUtils.roi_frame(frame, roi, 0.1f))
					 .sceneImage(FrameUtils.ref_frame(frame))
					 .track(this)
					 .build();
	}
	
	public synchronized void setImage(Pointer face, Pointer scene) {
		if(image != null)
			image.close();
		
		image = Image.builder()
					 .faceImage(FrameUtils.ref_frame(face))
					 .sceneImage(FrameUtils.ref_frame(scene))
					 .track(this)
					 .build();
	}
	public synchronized void setImage(Pointer face,byte[] imageByte ) {
		if(image != null)
			image.close();

		image = Image.builder()
				.faceImage(FrameUtils.ref_frame(face))
				.sceneImagePacket(imageByte)
				.track(this)
				.build();
	}

	public synchronized void setImage(Pointer face ) {
		if(image != null)
			image.close();

		image = Image.builder()
				.faceImage(FrameUtils.ref_frame(face))
				.track(this)
				.build();
	}
	public synchronized void setImage(Pointer face,kestrel_packet_t kestrelPacket ) {
		if(image != null)
			image.close();

		image = Image.builder()
				.faceImage(FrameUtils.ref_frame(face))
				.sceneImageKestrelPacket(kestrelPacket)
				.track(this)
				.build();
	}

	public synchronized void closeImage(){
		if(image != null)
			image.close();
	}
	
	@Builder
	@Getter
	public static class Image{
		private final FaceTrack track;
		
		private Pointer faceImage;
		
		private Pointer sceneImage;

		private byte[] sceneImagePacket;
		private final kestrel_packet_t sceneImageKestrelPacket;
		
		@Setter
		private String savedFacePath;

		@Setter
		private String savedScenePath;
		
		public void save() {
			synchronized(track) {
				if(savedFacePath == null && faceImage != null)
					savedFacePath  = FrameUtils.save_image_as_jpg(faceImage, ImageUtils.newFile("tmp_face"), 0);
				
				if(savedScenePath == null && sceneImage != null)
					savedScenePath = FrameUtils.save_image_as_jpg(sceneImage, ImageUtils.newFile("tmp_face"), Boolean.TRUE.equals(Utils.instance.imageSceneSave)? 0 : 1);

				if(savedScenePath == null && sceneImagePacket !=null && sceneImagePacket.length >0 && Boolean.TRUE.equals(Utils.instance.imageSceneSave))
					savedScenePath = FrameUtils.save_image_packet_byte(sceneImagePacket, ImageUtils.newFile("tmp_face"));

				if(savedScenePath == null && sceneImageKestrelPacket !=null  && Boolean.TRUE.equals(Utils.instance.imageSceneSave))
					savedScenePath = FrameUtils.save_image_packet_(sceneImageKestrelPacket, ImageUtils.newFile("tmp_face"));
			}
		}
		
		public void close(){
			synchronized(track) {
				FrameUtils.batch_free_frame(faceImage, sceneImage);
				//FrameUtils.free_packet(sceneImageKestrelPacket);
				if(sceneImageKestrelPacket !=null) sceneImageKestrelPacket.clear();
				faceImage = null;
				sceneImage = null;
				sceneImagePacket = null;
			}
		}
	}
}
