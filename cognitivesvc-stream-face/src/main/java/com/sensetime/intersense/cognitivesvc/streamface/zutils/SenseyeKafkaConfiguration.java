package com.sensetime.intersense.cognitivesvc.streamface.zutils;

import com.sensetime.intersense.cognitivesvc.stream.configuration.SenseyesRawEventOutput;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
//@EnableBinding({SenseyeKafkaConfiguration.KafkaSender.class})
@ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "yes")
public class SenseyeKafkaConfiguration {

//	public static interface KafkaSender {
//		String Senseye_RAW_EVENT_OUTPUT = "senseye_raw_event_output";
//		@Output(Senseye_RAW_EVENT_OUTPUT) MessageChannel senseye_raw_event_output();
//	}

    @Bean
    public SenseyesRawEventOutput senseyeRawEventOutput(StreamBridge streamBridgeTemplate) {
        return new SenseyesRawEventOutput(streamBridgeTemplate);
    }
}
