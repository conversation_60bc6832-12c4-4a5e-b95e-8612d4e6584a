spring.cloud.function.definition=face_raw_image_input
spring.cloud.stream.bindings.face_raw_image_input-in-0.destination=FaceRawImage
spring.cloud.stream.bindings.face_raw_image_input-in-0.binder=_kafka
spring.cloud.stream.bindings.face_raw_image_input-in-0.group=congnitive
spring.cloud.stream.bindings.face_raw_image_input-in-0.consumer.headerMode=none
spring.cloud.stream.bindings.face_raw_image_input-in-0.consumer.concurrency=256

spring.cloud.stream.bindings.face_raw_event_output-out-0.destination=SenseyeRawEvent
spring.cloud.stream.bindings.face_raw_event_output-out-0.binder=_kafka
spring.cloud.stream.bindings.face_raw_event_output-out-0.producer.headerMode=none

spring.cloud.stream.bindings.senseye_raw_event_output-out-0.destination=SenseyeRawEvent
spring.cloud.stream.bindings.senseye_raw_event_output-out-0.binder=_kafka
spring.cloud.stream.bindings.senseye_raw_event_output-out-0.producer.headerMode=none