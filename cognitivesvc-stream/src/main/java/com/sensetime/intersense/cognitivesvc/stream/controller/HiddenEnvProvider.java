package com.sensetime.intersense.cognitivesvc.stream.controller;

import com.google.common.collect.Maps;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamFaceRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.HostUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamNonSeen;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamSeen;
import com.sensetime.intersense.cognitivesvc.stream.video.service.RebalanceService;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController("hiddenEnvProvider")
@RequestMapping(value = "/cognitive/device/env/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "HiddenEnvProvider",description = "device infra env controller")
@Slf4j
public class HiddenEnvProvider extends BaseProvider{
	
	@Autowired
	private VideoStreamInfraRepository deviceMapper;

	@Value("${spring.application.name}")
	private String appName;
	
	@Value("${server.port}")
	private int port;

	@Autowired
	private RebalanceService rebalanceService;

	@Autowired
	private VideoStreamFaceRepository videoStreamFaceMapper;


    @Operation(summary = "更新roi", method = "POST")
    @RequestMapping(value = "/pipelineChangeRoi", method = RequestMethod.POST)
    public BaseRes<Integer> pipelineChangeRoi(@RequestBody Map<String, Object> param) {

		String deviceId = (String) param.getOrDefault("deviceId","");
		if(!deviceId.isBlank()) {
			System.out.println(param);
		}
		Map<String, Object> goings = rebalanceService.getOngoingStream();
		List<VideoStreamNonSeen> nonseens = (List<VideoStreamNonSeen>) goings.get("nonseen");
		if(nonseens.isEmpty()){
			log.info("nothing doing bec non ongoing stream");
		}
		log.info("pipelineChangeRoi{},{}", param, nonseens);

		for (VideoStreamNonSeen nonseen : nonseens) {
			log.info("pipelineChangeRoi device{},{},{}", nonseen.getDevice().getDeviceId(), deviceId, nonseen.getDevice().getDeviceId().equals(deviceId));
            if(nonseen.getDevice().getDeviceId().equals(deviceId)) {
            	log.info("pipelineChangeRoi update{}", deviceId);
				nonseen.changeStart();
			}
		}
		return BaseRes.success(1);
	}

    @Operation(summary = "device监控", method = "GET", hidden = true)
    @RequestMapping(value = "/queryMonitor", method = RequestMethod.GET)
    public CognitiveEntity.QuerySwitcherResult getMonitorList() {

		CognitiveEntity.QuerySwitcherResult result = new CognitiveEntity.QuerySwitcherResult();
		result.setIncharge(Utils.incharge);
		result.setVersion(Utils.VERSION);
		result.setDeviceType(Initializer.deviceType.toString() + Initializer.cudaType.toString());

		if (Initializer.isGpu()) {
			result.setIdentity(HostUtils.UUID());
			result.setIdentityFull(HostUtils.UUIDFull());
			result.setGpuId(Initializer.deviceId);
		} else {
			result.setIdentity(CognitiveEntity.QueryWorkerResult.NaN);
			result.setIdentityFull(CognitiveEntity.QueryWorkerResult.NaN);
			result.setGpuId(CognitiveEntity.QueryWorkerResult.NaN);
		}

		Map<String, Object> goings = rebalanceService.getOngoingStream();
		List<VideoStreamSeen> seens = (List<VideoStreamSeen>) goings.get("seen");
		List<VideoStreamNonSeen> nonseens = (List<VideoStreamNonSeen>) goings.get("nonseen");


		Map<String, Map<String, Object>> monitorDevice = new HashMap<>();
		result.setLowMonitorDevices(monitorDevice);

		for (VideoStreamNonSeen nonseen : nonseens) {
			VideoStreamNonSeen.Monitor monitorMap = VideoStreamNonSeen.getMonitorMap().get(nonseen.getDevice().getDeviceId());
			if (monitorMap != null) {

				Map<String, Object> monitorMapWorker = Maps.newLinkedHashMap();
				monitorMapWorker.put("handled_in_minite", monitorMap.getHandledCount());
				monitorMapWorker.put("unhandled_in_minite", monitorMap.getUnhandledCount());
				monitorMapWorker.put("send_message_in_minite", monitorMap.getSendedCount());
				monitorMapWorker.put("send_message_total", monitorMap.sendedTotal.get());
				monitorMapWorker.put("handled_total", monitorMap.handledTotal.get());
				monitorMapWorker.put("unhandled_total", monitorMap.unhandledTotal.get());
				monitorMapWorker.put("video_status", monitorMap.getVideoStatus());
				monitorMapWorker.put("video_status_detail", monitorMap.getVideoStatusDetail());
				if(monitorMap.getLastErrorCheckTime() != null){
					monitorMapWorker.put("last_error_check_time",monitorMap.getLastErrorCheckTime());
				}
				monitorMapWorker.put("frame_handled_total", monitorMap.handledTotal.get());
				monitorMapWorker.put("frame_unhandled_total", monitorMap.unhandledTotal.get());
				monitorMapWorker.put("frame_un_offer_total", monitorMap.unOfferTotal.get());

				monitorMapWorker.put("deviceId", nonseen.getDevice().getDeviceId());

				monitorDevice.put(nonseen.getDevice().getDeviceId(), monitorMapWorker);
			}
		}

		//log.info("[query instance info] queryMonitor :{}", JSON.toJSONString(result));
		return result;

	}

}
