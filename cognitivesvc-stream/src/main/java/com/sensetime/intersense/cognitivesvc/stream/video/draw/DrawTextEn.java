package com.sensetime.intersense.cognitivesvc.stream.video.draw;

import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.IplImage;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_imgproc.CvFont;
import org.opencv.core.Rect;

public class DrawTextEn implements DrawItem {
	private String text;
	private CvFont font;
	private CvScalar color;
	private Rect rect;
	private int x;
	private int y;
	
	public DrawTextEn(String text, CvFont font, CvScalar color, Rect rect) {
		this.text = text;
		this.font = font;
		this.color = color;
		this.rect = rect;
	}

	public DrawTextEn(String text, CvFont font, CvScalar color, int x, int y) {
		this.text = text;
		this.font = font;
		this.color = color;
		this.x = x;
		this.y = y;
	}

	@Override
	public boolean drawTo(Frame frame, IplImage ipl_frame, Mat mat_frame) {
		try {
			if(rect != null)
				opencv_imgproc.cvPutText(ipl_frame, text, opencv_core.cvPoint(rect.x + rect.width / 2 - text.length() * 19 / 2, rect.y), font , color);
			else
				opencv_imgproc.cvPutText(ipl_frame, text, opencv_core.cvPoint(x, y), font , color);
		}catch(Exception e) {
			return false;
		}
		
		return true;
	}
}
