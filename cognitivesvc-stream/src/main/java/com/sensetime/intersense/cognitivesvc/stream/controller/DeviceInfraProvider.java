package com.sensetime.intersense.cognitivesvc.stream.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.netflix.appinfo.InstanceInfo;
import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.*;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.*;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.stream.video.service.RebalanceService;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.hibernate.annotations.NaturalId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.netflix.eureka.EurekaServiceInstance;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RestController("deviceInfraProvider")
@RequestMapping(value = "/cognitive/device/infra/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "DeviceInfraProvider", description = "device infra controller")
@Slf4j
public class DeviceInfraProvider extends BaseProvider{
	
	@Autowired
	private VideoStreamInfraRepository deviceMapper;

	@Autowired
	private RebalanceService rebalanceService;

	@Autowired
	private FetchFrameVideo fetchFrameVideo;

	@Value("${spring.application.name}")
	private String appName;
	
	@Value("${server.port}")
	private int port;

	@Autowired
	private DiscoveryClient discoveryClient;

    @Operation(summary = "视频流总数", method = "GET")
    @RequestMapping(value = "/getDeviceInfraCount", method = RequestMethod.GET)
    public BaseRes<Long> getDeviceInfraCount() {
        return BaseRes.success(deviceMapper.count());
    }

    @Operation(summary = "直接视频流-分页", method = "GET")
    @RequestMapping(value = "/getDeviceInfra", method = RequestMethod.GET)
    public BaseRes<List<VideoStreamInfra>> getDeviceInfra(@RequestParam(required = false) String deviceId) {
        //log.info("[printHttpLog] url: /cognitive/device/infra/getDeviceInfra , req:{}", deviceId);
        if (StringUtils.isNotBlank(deviceId)) {
            List<VideoStreamInfra> videoStreamInfras = deviceMapper.findAllById(Lists.newArrayList(deviceId));
            log.info("[printHttpLog] url: /cognitive/device/infra/getDeviceInfra , req:{},res.size:{}", deviceId, videoStreamInfras.size());
            return BaseRes.success(videoStreamInfras);
        } else {
            List<VideoStreamInfra> videoStreamInfras = deviceMapper.findAll();
           // log.info("[printHttpLog] url: /cognitive/device/infra/getDeviceInfra , req:{},res.size:{}", deviceId, videoStreamInfras.size());
            return BaseRes.success(videoStreamInfras);
        }
    }

    @Operation(summary = "检查视频流", method = "POST")
    @RequestMapping(value = "/ajustDeviceInfra", method = RequestMethod.POST)
    public BaseRes<VideoStreamInfra> ajustDeviceInfra(@RequestBody String rtsp_source) throws Exception {
        log.info("[printHttpLog] url: /cognitive/device/infra/ajustDeviceInfra-Start , req:{}", rtsp_source);
        VideoStreamInfra device = new VideoStreamInfra();
        device.setRtspSource(rtsp_source);
        VideoStreamInfra videoStreamInfra = ajustVideo(device);
        log.info("[printHttpLog] url: /cognitive/device/infra/ajustDeviceInfra-End , req:{}, res.size:{}", rtsp_source, videoStreamInfra);
        return BaseRes.success(videoStreamInfra);
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "添加直接视频流", method = "POST")
    @RequestMapping(value = "/addOrUpdateDeviceInfra", method = RequestMethod.POST)
    public BaseRes<Integer> addOrUpdateDeviceInfra(@RequestBody VideoStreamInfra device) throws Exception {
        log.info("[printHttpLog] url: /cognitive/device/infra/addOrUpdateDeviceInfra-Start , req:{}", device);
        device.setSeed(VideoStreamInfra.initSeed);
        device.setKeepAlive(new Date());

        if (StringUtils.isBlank(device.getRtspSource())) {
            log.error("device rtspSource should not be blank.");
			throw new BusinessException("3005","device rtspSource should not be blank.");
		}

		if(device.getRtspSource().endsWith("/")) {
			device.setRtspSource(device.getRtspSource().substring(0, device.getRtspSource().length() - 1));
		}

		if(StringUtils.isNotBlank(device.getRtmpDestination())) {
			List<VideoStreamInfra> sameRtmpDevices = deviceMapper.findByRtmpDestination(device.getRtmpDestination());
			if(CollectionUtils.isNotEmpty(sameRtmpDevices))
				if(!device.getDeviceId().equals(sameRtmpDevices.get(0).getDeviceId()))
					return BaseRes.error("-1", "rtmp url is duplicate, please check.");
		}

		if(VideoStream.isStreamRemote(device.getRtspSource())) {
			//ajustVideo(device);
			if(device.getVideoRate()!=null){
				if(Math.abs(device.getVideoRate()) > 100)
					return BaseRes.error("-1", "video rate is [" + device.getVideoRate() + "], should be between 0 ~ 100.");
			}else{
				device.setVideoRate(-1);
			}
			if(device.getRtspHeight()!=null && device.getRtspWidth()!=null){
				if(device.getRtspWidth() * device.getRtspHeight() > 7680 * 4320)
					return BaseRes.error("-1", "video size is [" + device.getRtspWidth() + " * " + device.getRtspHeight() + "], should be less than 7680 * 4320.");
			}else{
				device.setRtspWidth(-1);
				device.setRtspHeight(-1);
			}
		}else{
			if(device.getVideoRate()==null){
				device.setVideoRate(-1);
			}
			if(device.getRtspHeight()!=null && device.getRtspWidth()==null){
				device.setRtspWidth(-1);
				device.setRtspHeight(-1);
			}
		}

		if(device.getRtmpOption() == null)
			device.setRtmpOption("tune:zerolatency,preset:ultrafast");

		if(device.getFrameSkip() == null)
			device.setFrameSkip(Utils.instance.frameSkip);

		if(device.getSts() == null)
			device.setSts(0);

		device.setUpdateTs(new Date());

		List<VideoStreamInfra> exists = deviceMapper.findByDeviceIdIn(List.of(device.getDeviceId()));
		if(CollectionUtils.isNotEmpty(exists)) {
			device.setRtspMappedSource(exists.get(0).getRtspMappedSource());
			device.setSeed(exists.get(0).getSeed());
		}

		deviceMapper.saveAndFlush(device);
		log.info("[printHttpLog] url: /cognitive/device/infra/addOrUpdateDeviceInfra-END , req:{},res:{}", device,1);
		return BaseRes.success(1);
	}

	@Operation(summary = "删除直接视频流", method = "POST")
	@RequestMapping(value = "/deleteDeviceInfra", method = RequestMethod.POST)
	public BaseRes<Integer> deleteDeviceInfra(@RequestParam String deviceId) {
		log.info("[printHttpLog] url: /cognitive/device/face/deleteDeviceInfra , req:{}", deviceId);
		try{
			deviceMapper.deleteById(deviceId);
		}catch (Exception e){
			log.warn("deleteDeviceInfra error,deviceId:{}, {}",deviceId, e.getMessage());
		}
		return BaseRes.success(1);
	}

	@Operation(summary = "保持存活渲染视频流keepalive", method = "POST")
	@RequestMapping(value = "/keepAliveDeviceInfra", method = RequestMethod.POST)
	public BaseRes<Integer> keepAliveDeviceInfra(@RequestParam String deviceId, @RequestParam(required = false) Date expire) {
		VideoStreamInfra current = deviceMapper.findById(deviceId).orElse(null);
		if(current == null)
			return BaseRes.success(0);

		if(current.getKeepAlive() == null) {
			current.setKeepAlive(expire == null ? new Date() : expire);
		}else {
			if(expire == null && current.getKeepAlive().getTime() <= System.currentTimeMillis()) {
				current.setKeepAlive(new Date());
			}

			if(expire != null && current.getKeepAlive().getTime() <= expire.getTime()){
				current.setKeepAlive(expire);
			}
		}

		deviceMapper.saveAndFlush(current);
		return BaseRes.success(1);
	}
	//todo 加参数
	@Operation(description = "获取流的一帧", method = "POST")
	@RequestMapping(value = "/fetchFrame", method = RequestMethod.POST)
	public BaseRes<Map<String,String>> fetchFrame(@RequestBody String rtsp_source) throws Exception {

		VideoFrame videoFrame = null;
		Map<String,String> map = new HashMap<String,String>();
		try {

			videoFrame = FrameUtils.fetch_up_down_load_frame_path(rtsp_source);
			String path = FrameUtils.save_image_as_jpg(videoFrame.getFrame(), ImageUtils.newFileWithMkdir("fetchFrame"),0);
			log.info("fetchFrame image:{}", path);
			if(path.equals(FrameUtils.NOIMAGE)){
				return  BaseRes.success(map);
			}
			map.put("path",path);
			map.put("rFrameRate",videoFrame.getRFrameRate());
			return BaseRes.success(map);
		}catch (BusinessException e){
			e.printStackTrace();
			throw e;
		}catch (Exception ex){
			ex.printStackTrace();
			throw new BusinessException("1001", ex.getMessage());
		}finally {
			FrameUtils.batch_free_frame(videoFrame);
		}
		//return BaseRes.success("");
	}

    @Deprecated
    @Operation(summary = "获取流的一帧,需要清理", method = "POST",hidden = true)
    @RequestMapping(value = "/fetchFrameToClean", method = RequestMethod.POST)
    public BaseRes<String> fetchFrameToClean(@RequestBody String rtsp_source) throws Exception {

		VideoFrame videoFrame = null;
		try {
			videoFrame = FrameUtils.fetch_up_down_load_frame_path(rtsp_source);
			String path = FrameUtils.save_image_as_jpg(videoFrame.getFrame(), ImageUtils.newFileWithMkdir("fetchFrameToClean"), 0);
			if(path.equals(FrameUtils.NOIMAGE)){
				return  BaseRes.success("");
			}
			return BaseRes.success(path);
		}catch (Exception e){
			e.printStackTrace();
		}finally {
			FrameUtils.batch_free_frame(videoFrame);
		}
		return BaseRes.success("");
	}


    @Operation(summary = "获取video的all frame", method = "POST",hidden = true)
    @RequestMapping(value = "/fetchFrameByVideo", method = RequestMethod.POST)
	@Deprecated(forRemoval = true)
    public BaseRes<String> fetchFrameByVideo(@RequestBody FrameParamReq frameParamReq) throws Exception {
        String restpSource = frameParamReq.getRtspSource();
        VideoFrame videoFrame = FrameUtils.fetch_up_down_load_video_path(frameParamReq.getRtspSource(), frameParamReq.getSaveDir(), frameParamReq.getFrameSkip(), frameParamReq.getRange());

		return BaseRes.success("");
	}

	@Operation(summary = "获取video的all frame离线视频", method = "POST",hidden = true)
	@RequestMapping(value = "/fetchVideoFrame", method = RequestMethod.POST)
	public BaseRes<String> fetchVideoFrame(@RequestBody FrameParamReq frameParamReq) throws Exception {
		String restpSource = frameParamReq.getRtspSource();
		//Check if the resource（gpu or memory, others） release meets the requirements
		File videoFile = new File(frameParamReq.getRtspSource());
		if(!videoFile.exists()) {
			log.error("image_path [" + frameParamReq.getRtspSource() + "] file not exist.");
			throw new BusinessException("3003","image_path [" + frameParamReq.getRtspSource() + "] file not exist.");
		}
		//	("${video.max.file.size:1073741824}")  // default 1GB (1024*1024*1024)
		//	private long maxFileSize;
		// Check file size
		// SHARDING_SIZE_LIMIT
		// 获取 SHARDING_SIZE_LIMIT 环境变量并转换为字节数
		String shardingSizeLimitStr = System.getenv("SHARDING_SIZE_LIMIT");
		long shardingSizeLimit = 0;

		if (shardingSizeLimitStr != null) {
			try {
				// 将环境变量的值转为 long，并转换为字节（以 MB 为单位）
				shardingSizeLimit = Long.parseLong(shardingSizeLimitStr) * 1024 * 1024; // M转为字节
			} catch (NumberFormatException e) {
				log.error("Invalid SHARDING_SIZE_LIMIT value: " + shardingSizeLimitStr, e);
				throw new BusinessException("4002", "Invalid sharding size limit");
			}
		}
		// 检查视频文件大小
		if (shardingSizeLimit > 0) {
			// 如果环境变量存在并且有效，使用其值进行比较
			if (videoFile.length() > shardingSizeLimit) {
				log.error("File size exceeds limit: " + videoFile.length() + " bytes");
				throw new BusinessException("4001", "param error, File size exceeds the limit");
			}
		} else {
			// 环境变量不存在时，使用默认值进行比较
			if (videoFile.length() > Utils.instance.offlineVideoLimit) {
				log.error("File size exceeds 1GB limit: " + videoFile.length() + " bytes");
				throw new BusinessException("4001", "param error, File size exceeds 1GB limit");
			}
		}
		log.info("fetchVideoFrameReq:{},{},{}", frameParamReq.getVideoId(),frameParamReq.getRtspSource(), JSON.toJSONString(frameParamReq));

		float memRate = HostUtils.getGpuMemRate();
		if(memRate > Utils.instance.offlineVideoGpuMemLimit){
			throw new BusinessException("3013","overflow offlineVideoGpuMemLimit:" + Utils.instance.offlineVideoGpuMemLimit);
		}
		try {
			fetchFrameVideo.fetch_up_down_load_video_path(frameParamReq);
			log.info("fetchFrameVideo stop,device:{}", frameParamReq.getRtspSource());
		}catch  (Exception e){
			e.printStackTrace();
			throw new BusinessException("9900","error:" + e.getMessage());
		}
		return BaseRes.success("");
	}

	@Operation(summary = "获取video的all frame离线视频-Sync", method = "POST",hidden = true)
	@RequestMapping(value = "/fetchVideoFrameSync", method = RequestMethod.POST)
	@Deprecated(forRemoval = true)
	public BaseRes<String> fetchVideoFrameSync(@RequestBody FrameParamReq frameParamReq) throws Exception {
		String restpSource = frameParamReq.getRtspSource();
		//Check if the resource（gpu or memory, others） release meets the requirements
		//Check if the resource（gpu or memory, others） release meets the requirements
		File videoFile = new File(frameParamReq.getRtspSource());
		if(!videoFile.exists()) {
			log.error("image_path [" + frameParamReq.getRtspSource() + "] file not exist.");
			throw new BusinessException("3003","image_path [" + frameParamReq.getRtspSource() + "] file not exist.");
		}
		//	("${video.max.file.size:1073741824}")  // default 1GB (1024*1024*1024)
		//	private long maxFileSize;
		// Check file size
		// SHARDING_SIZE_LIMIT
		// 获取 SHARDING_SIZE_LIMIT 环境变量并转换为字节数
		String shardingSizeLimitStr = System.getenv("SHARDING_SIZE_LIMIT");
		long shardingSizeLimit = 0;

		if (shardingSizeLimitStr != null) {
			try {
				// 将环境变量的值转为 long，并转换为字节（以 MB 为单位）
				shardingSizeLimit = Long.parseLong(shardingSizeLimitStr) * 1024 * 1024; // M转为字节
			} catch (NumberFormatException e) {
				log.error("Invalid SHARDING_SIZE_LIMIT value: " + shardingSizeLimitStr, e);
				throw new BusinessException("4002", "Invalid sharding size limit");
			}
		}
		// 检查视频文件大小
		if (shardingSizeLimit > 0) {
			// 如果环境变量存在并且有效，使用其值进行比较
			if (videoFile.length() > shardingSizeLimit) {
				log.error("File size exceeds limit: " + videoFile.length() + " bytes");
				throw new BusinessException("4001", "param error, File size exceeds the limit");
			}
		} else {
			// 环境变量不存在时，使用默认值进行比较
			if (videoFile.length() > Utils.instance.offlineVideoLimit) {
				log.error("File size exceeds 1GB limit: " + videoFile.length() + " bytes");
				throw new BusinessException("4001", "param error, File size exceeds 1GB limit");
			}
		}
		float memRate = HostUtils.getGpuMemRate();
		if(memRate > Utils.instance.offlineVideoGpuMemLimit){
			throw new BusinessException("3013","overflow offlineVideoGpuMemLimit:" + Utils.instance.offlineVideoGpuMemLimit);
		}

		try {
			CompletableFuture<VideoFrame> videoFrame = fetchFrameVideo.fetch_up_down_load_video_path_sync(frameParamReq);

			// 添加回调处理
			videoFrame.thenAccept(frame -> {
				// 处理成功的回调
				log.info("Video frame processing completed for source: {}", restpSource);
			}).exceptionally(throwable -> {
				// 处理异常的回调
				log.error("Error processing video frame for source: {}, error: {}",
					restpSource, throwable.getMessage(), throwable);
				return null;
			});

			return BaseRes.success("");
		} catch (BusinessException e) {
			log.error("Business error while processing video frame: {}", e.getMessage(), e);
			return BaseRes.error("3013", e.getMessage());
		} catch (Exception e) {
			log.error("Unexpected error while processing video frame: {}", e.getMessage(), e);
			return BaseRes.error("9900", "System error: " + e.getMessage());
		}
	}

	public VideoStreamInfra ajustVideo(VideoStreamInfra device) {
		FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(device.getRtspSource());
		grabber.setOption("rtsp_transport","tcp");
		grabber.setOption("stimeout", String.valueOf(Utils.instance.streamOpenTimeout * 1000));
		grabber.setOption("timeout", String.valueOf((Utils.instance.streamOpenTimeout -1000) * 1000));
		try {
			grabber.start();
			log.info("inputVideo  [" + device.getDeviceId() + "] RtspWidth:[" + device.getRtspWidth() + "], RtspHeight[" + device.getRtspHeight() + "], VideoRate[" + device.getVideoRate() + "]");
			log.info("detectVideo [" + device.getDeviceId() + "] RtspWidth:[" + grabber.getImageWidth() + "], RtspHeight[" + grabber.getImageHeight() + "], VideoRate[" + (int)grabber.getVideoFrameRate() + "]");

			if(device.getRtspWidth() == null)
				device.setRtspWidth(grabber.getImageWidth());

			if(device.getRtspHeight() == null)
				device.setRtspHeight(grabber.getImageHeight());

			if(device.getVideoRate() == null)
				device.setVideoRate((int)grabber.getVideoFrameRate());

			log.info("finalVideo [" + device.getDeviceId() + "] RtspWidth:[" + device.getRtspWidth() + "], RtspHeight[" + device.getRtspHeight() + "], VideoRate[" + device.getVideoRate() + "]");
		} catch (Exception e) {
            log.error("can not open stream [" + device.getRtspSource() + "].",e);
			throw new BusinessException("3006","can not open stream [" + device.getRtspSource() + "].");
		}finally {
			try { grabber.close(); } catch (Exception e) { }
		}

		return device;
	}

    @Operation(summary = "face监控", method = "GET")
    @RequestMapping(value = "/getDeviceMonitorInfo", method = RequestMethod.GET)
    public BaseRes<Map<String, CognitiveEntity.QuerySwitcherResult>> getDeviceMonitorInfo() {

		List<Pair<ServiceInstance, CognitiveEntity.QuerySwitcherResult>> instanceSwitcherList = discoveryClient.getServices()
				.stream()
				.map(discoveryClient::getInstances)
				.flatMap(List::stream)
				.filter(instance -> "true".equals(instance.getMetadata().get("isFaceStream")))
				.sorted((l, r) -> l.getInstanceId().compareTo(r.getInstanceId()))
				.parallel()
				.filter(instance -> {
					if(!(instance instanceof EurekaServiceInstance))
						return true;

					EurekaServiceInstance eurekaInstance = (EurekaServiceInstance)instance;
					return eurekaInstance.getInstanceInfo().getStatus() == InstanceInfo.InstanceStatus.UP;
				})
				.map(instance -> {
					try {
						CognitiveEntity.QuerySwitcherResult query = RestUtils.restTemplate60000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/device/env/queryMonitor", CognitiveEntity.QuerySwitcherResult.class);
						return new ImmutablePair<ServiceInstance, CognitiveEntity.QuerySwitcherResult>(instance, query);
					}catch(Exception e) {
						return new ImmutablePair<ServiceInstance, CognitiveEntity.QuerySwitcherResult>(instance, CognitiveEntity.QuerySwitcherResult.ERROR);
					}
				})
				.collect(Collectors.toList());


		Map<String, CognitiveEntity.QuerySwitcherResult> resultSwitchers = new HashMap<String, CognitiveEntity.QuerySwitcherResult>();
		for(Pair<ServiceInstance, CognitiveEntity.QuerySwitcherResult> info : instanceSwitcherList)
			resultSwitchers.put(info.getLeft().getInstanceId(), info.getRight());


		return BaseRes.success(resultSwitchers);
	}


    @Operation(summary = "face监控", method = "GET")
    @RequestMapping(value = "/monitor/device/info", method = RequestMethod.GET)
    public BaseRes<List<InstanceDeviceInfoItem>> getDeviceInfo() {
        BaseRes<Map<String, CognitiveEntity.QuerySwitcherResult>> baseRes = getDeviceMonitorInfo();
        if (!baseRes.isSuccess())
            return BaseRes.error("9900", "server error");
        List<InstanceDeviceInfoItem> res = new ArrayList<>();
        Map<String, CognitiveEntity.QuerySwitcherResult> resultSwitchers = baseRes.getData();
        resultSwitchers.forEach((k, v) -> {
            Map<String, Map<String, Object>> devices = v.getLowMonitorDevices();
            for (String deviceId : devices.keySet()) {
                InstanceDeviceInfoItem instanceDeviceInfoItem = new InstanceDeviceInfoItem();
                Map<String, Object> deviceInfo = devices.get(deviceId);

				instanceDeviceInfoItem.setDeviceId(deviceId);
				//instanceDeviceInfoItem.setType("low-frequency");

				instanceDeviceInfoItem.setAnnotatorName("Face");
				instanceDeviceInfoItem.setRunningPod(k);
				instanceDeviceInfoItem.setVideoStatus(deviceInfo.get("video_status").toString());
				instanceDeviceInfoItem.setHandleTotal(deviceInfo.get("handled_total").toString());
				instanceDeviceInfoItem.setHandleInMinute(deviceInfo.get("handled_in_minite").toString());
				instanceDeviceInfoItem.setSendMsgInMinute(deviceInfo.get("send_message_in_minite").toString());
				instanceDeviceInfoItem.setSendMsgTotal(deviceInfo.get("send_message_total").toString());
				instanceDeviceInfoItem.setUnhandledTotal(deviceInfo.get("unhandled_total").toString());
				instanceDeviceInfoItem.setUnhandledInMinute(deviceInfo.get("unhandled_in_minite").toString());

				res.add(instanceDeviceInfoItem);
			}
		});
		return BaseRes.success(res);
		//return BaseRes.success(resultSwitchers);
	}


    @Operation(summary = "face监控", method = "GET")
    @RequestMapping(value = "/monitor/model/info", method = RequestMethod.GET)
    public BaseRes<Map<String, List<InstanceModelInfoItem>>> getModelInfo() {
        BaseRes<Map<String, CognitiveEntity.QuerySwitcherResult>> baseRes = getDeviceMonitorInfo();
        if (!baseRes.isSuccess())
            return BaseRes.error("9900", "server error");
        Map<String, List<InstanceModelInfoItem>> result = new HashMap<>();
        List<InstanceModelInfoItem> instanceModelInfoItems = new ArrayList<>();
        result.put("Face", instanceModelInfoItems);
        Map<String, CognitiveEntity.QuerySwitcherResult> resultSwitchers = baseRes.getData();
        resultSwitchers.forEach((k, v) -> {
            Map<String, Map<String, Object>> devices = v.getLowMonitorDevices();
            InstanceModelInfoItem instanceModelInfoItem = new InstanceModelInfoItem();
            instanceModelInfoItem.setRunningDeviceIdList(devices.keySet().stream().collect(Collectors.toList()));
            instanceModelInfoItem.setStatus("running");
            instanceModelInfoItem.setRunningPod(k);
            instanceModelInfoItems.add(instanceModelInfoItem);
        });
        // 低频不写device 不在低频解析，只是解流，可以看device
        return BaseRes.success(result);
    }


    @Operation(summary = "gpu监控", method = "GET")
    @RequestMapping(value = "/gpu/info", method = RequestMethod.GET)
    public BaseRes<List<GpuInfo>> getGpuInfo() {

		List<Pair<ServiceInstance, GpuInfo>> instanceGpuList = discoveryClient.getServices()
				.stream()
				.map(discoveryClient::getInstances)
				.flatMap(List::stream)
				.filter(instance -> ("true".equals(instance.getMetadata().get("isFaceStream")) || "true".equals(instance.getMetadata().get("isXSwitcher"))))
				.sorted((l, r) -> l.getInstanceId().compareTo(r.getInstanceId()))
				.parallel()
				.filter(instance -> {
					if(!(instance instanceof EurekaServiceInstance))
						return true;

					EurekaServiceInstance eurekaInstance = (EurekaServiceInstance)instance;
					return eurekaInstance.getInstanceInfo().getStatus() == InstanceInfo.InstanceStatus.UP;
				})
				.map(instance -> {
					try {
						GpuInfo query = RestUtils.restTemplate60000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/host/gpu/info", GpuInfo.class);
						//if(BaseRes.success().isSuccess()){
							//query = queryBase.getData();
						//}
						return new ImmutablePair<ServiceInstance, GpuInfo>(instance, query);
					}catch(Exception e) {
						return new ImmutablePair<ServiceInstance, GpuInfo>(instance, null);
					}
				})
				.collect(Collectors.toList());
		// <gpuid, gpuInfo>
		Map<String, GpuInfo> gpuMap = new HashMap<>();
		instanceGpuList.forEach(pair ->{
			String instanceId = pair.getLeft().getInstanceId();
			String gpuId = pair.getRight().getIdentity();
			if(StringUtils.isBlank(gpuId)) return;
			if(gpuMap.containsKey(gpuId)){
				GpuInfo gpuInfo = gpuMap.get(gpuId);
				gpuInfo.getRunningPod().add(instanceId);
			}else{
				pair.getRight().getRunningPod().add(instanceId);
				gpuMap.put(gpuId,pair.getRight());
			}


		} );
		return BaseRes.success(new ArrayList<>(gpuMap.values()));
	}

    @Operation(summary = "face流监控", method = "GET")
    @RequestMapping(value = "/videostatus", method = RequestMethod.GET)
    public BaseRes<List<VideoStatusRes>> getVideoStatus() {
        //todo 这些广播调用接口的逻辑，例如info、refresh等 在节点多的时候可能会有性能问题
        // 后续考虑使用缓存做法
        List<Pair<ServiceInstance, CognitiveEntity.QuerySwitcherResult>> instanceSwitcherList = discoveryClient.getServices()
                .stream()
                .map(discoveryClient::getInstances)
                .flatMap(List::stream)
                .filter(instance -> "true".equals(instance.getMetadata().get("isFaceStream")))
                .sorted((l, r) -> l.getInstanceId().compareTo(r.getInstanceId()))
                .parallel()
                .filter(instance -> {
                    if (!(instance instanceof EurekaServiceInstance))
                        return true;

					EurekaServiceInstance eurekaInstance = (EurekaServiceInstance)instance;
					return eurekaInstance.getInstanceInfo().getStatus() == InstanceInfo.InstanceStatus.UP;
				})
				.map(instance -> {
					try {
						CognitiveEntity.QuerySwitcherResult query = RestUtils.restTemplate60000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/device/env/queryMonitor", CognitiveEntity.QuerySwitcherResult.class);
						return new ImmutablePair<ServiceInstance, CognitiveEntity.QuerySwitcherResult>(instance, query);
					}catch(Exception e) {
						return new ImmutablePair<ServiceInstance, CognitiveEntity.QuerySwitcherResult>(instance, CognitiveEntity.QuerySwitcherResult.ERROR);
					}
				})
				.collect(Collectors.toList());


		List<VideoStatusRes> videoStatusResList = new ArrayList<>();
		if(instanceSwitcherList == null) return BaseRes.success(videoStatusResList);
		for(Pair<ServiceInstance, CognitiveEntity.QuerySwitcherResult> info : instanceSwitcherList){
			if(info.getRight().getLowMonitorDevices() !=null) {
				info.getRight().getLowMonitorDevices().forEach((s, stringObjectMap) -> {
					VideoStatusRes videoStatusRes = new VideoStatusRes();
					videoStatusRes.setVideoStatus((String) stringObjectMap.getOrDefault("video_status", "OK"));
					videoStatusRes.setDeviceId(s);
					videoStatusRes.setDesc((String) stringObjectMap.get("video_status_detail"));
					videoStatusRes.setLastErrorCheckTs(stringObjectMap.containsKey("last_error_check_time") ? (String) stringObjectMap.get("last_error_check_time") : "");
					videoStatusResList.add(videoStatusRes);
				});
			}
		}


		return BaseRes.success(videoStatusResList);
	}

	@Operation(summary = "停止解析离线视频", method = "POST")
	@RequestMapping(value = "/stopVideoFrame", method = RequestMethod.POST)
	public BaseRes<String> stopVideoProcessing(@RequestBody FrameParamReq frameParamReq) {
		try {
			fetchFrameVideo.stopVideoProcessing(frameParamReq.getVideoId());
			log.info("Stopped video frame processing for videoId: {}", frameParamReq.getVideoId());
			return BaseRes.success("Video frame processing stopped successfully for videoId: " + frameParamReq.getVideoId());
		} catch (Exception e) {
			log.error("Error stopping video frame processing for videoId: {}", frameParamReq.getVideoId(), e);
			return BaseRes.error("9900", "Error stopping video frame processing: " + e.getMessage());
		}
	}
}
