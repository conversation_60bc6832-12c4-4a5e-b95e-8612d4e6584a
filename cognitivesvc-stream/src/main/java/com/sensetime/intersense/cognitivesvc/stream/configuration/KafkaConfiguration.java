package com.sensetime.intersense.cognitivesvc.stream.configuration;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.cloud.stream.annotation.EnableBinding;
//import org.springframework.cloud.stream.annotation.Output;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.MessageChannel;

@Configuration
@ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "yes")
public class KafkaConfiguration {

    //  public static interface KafkaSenderDeviceStatus {
    //        String DEVICE_STATUS_EVENT_OUTPUT = "device_status_event_output";
    //        @Output(DEVICE_STATUS_EVENT_OUTPUT)
    //        MessageChannel device_status_event_output();
    //    }
    @Bean("device_status_event_output")
    public DeviceStatusEventOutput deviceStatusEventOutput(StreamBridge streamBridgeTemplate){
        return  new DeviceStatusEventOutput(streamBridgeTemplate);
    }

    @Bean("senseyes_raw_event_output")
    public SenseyesRawEventOutput senseyesRawEventOutput(StreamBridge streamBridgeTemplate){
        return  new SenseyesRawEventOutput(streamBridgeTemplate);
    }

}
