package com.sensetime.intersense.cognitivesvc.stream;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import jakarta.annotation.PostConstruct;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.stream.controller.HiddenEnvProvider;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamNonSeen;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawTextCh;
import com.sensetime.intersense.cognitivesvc.stream.video.service.FontService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration("streamConfiguation")
@EnableScheduling
@EnableAsync
@ComponentScan
@ConditionalOnProperty(value = "stream.enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
@PropertySource("classpath:stream.properties")
public class CognitivesvcConfiguation{

	@Autowired
	private FontService fontService;

	@Autowired
	private ConfigurableEnvironment env;

	@Autowired
	HiddenEnvProvider hiddenEnvProvider;

	@Autowired
	private Utils utils;
	
	@PostConstruct
	public void initialize() throws Exception{
		utils.toString();

		Initializer.initialize(env);

		DrawTextCh.fontService = fontService;
		log.warn("\n");
		log.warn("*******************************");
		log.warn("**********init stream**********");
		log.warn("stream.enabled=false to disable");
		log.warn("*******************************");
		log.warn("\n");

		log.info("get property seeker.enabled : {}", System.getProperty("seeker.enabled"));
	}
	@Scheduled(fixedDelay = 5000)
	public void resetModelMonitor() throws Exception {

		for(VideoStreamNonSeen.Monitor worker : VideoStreamNonSeen.getMonitorMap().values()){
			worker.reset();
		}
	}

//	@Scheduled(cron = "${face.gueryDevice.cron: 0 0/2 * * * ?}")
//	public void queryDeviceForLog() throws Exception {
//		CognitiveEntity.QuerySwitcherResult querySwitcherResult = hiddenEnvProvider.getMonitorList();
//		log.info("[query instance info] queryMonitor :{}", JSON.toJSONString(querySwitcherResult));
//
//	}
}
