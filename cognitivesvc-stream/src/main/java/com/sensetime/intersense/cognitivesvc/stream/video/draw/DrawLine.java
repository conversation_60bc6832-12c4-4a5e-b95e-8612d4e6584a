package com.sensetime.intersense.cognitivesvc.stream.video.draw;

import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.IplImage;
import org.bytedeco.opencv.opencv_core.Mat;

public class DrawLine implements DrawItem {
	private int[] from;
	private int[] to;
	private CvScalar color;
	
	public DrawLine(int[] from, int[] to, CvScalar color) {
		this.from = from;
		this.to = to;
		this.color = color;
	}

	@Override
	public boolean drawTo(Frame frame, IplImage ipl_frame, Mat mat_frame) {
		try {
			opencv_imgproc.cvLine(ipl_frame, from, to, color);
		}catch(Exception e) {
			return false;
		}
		
		return true;
	}
}
