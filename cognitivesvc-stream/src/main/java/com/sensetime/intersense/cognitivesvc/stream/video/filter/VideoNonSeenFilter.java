package com.sensetime.intersense.cognitivesvc.stream.video.filter;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import org.springframework.core.annotation.Order;

import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;

/** 非可视化视频流处理 所有视频流共享唯一的filter
 * <AUTHOR>
 *
 */
public abstract class VideoNonSeenFilter{
	
	/** 初始化上下文*/
	protected Map<String, Object> initContext = new HashMap<String, Object>();
	
	/**下一个filter */
	protected VideoNonSeenFilter next;

	/** 停止内部的线程标记位 */
	protected volatile boolean stoped;
	
	/** 上下文变量 */
	protected final ThreadLocal<Map<String, Object>> context = new ThreadLocal<Map<String, Object>>();
	
	/** 执行这个filter */
	public final void doFilterOne(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		context.set(new HashMap<String, Object>());
		
		try {
			handleStageOne(videoFrames, devices);
		}catch(Exception e) {
			e.printStackTrace();
		}
		
		if(next != null)
			next.doFilterOne(videoFrames, devices);
		
		try {
			postHandleStageOne(videoFrames, devices);
		}catch(Exception e) {
			e.printStackTrace();
		}
	}
	
	/** 执行这个filter */
	public final void doFilterTwo(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {		
		try {
			handleStageTwo(videoFrames, devices);
		}catch(Exception e) {
			e.printStackTrace();
		}
		
		if(next != null)
			next.doFilterTwo(videoFrames, devices);
		
		try {
			postHandleStageTwo(videoFrames, devices);
		}catch(Exception e) {
			e.printStackTrace();
		}
	}
	
	/** 执行这个filter */
	public final void doFilterThree(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		try {
			handleStageThree(videoFrames, devices);
		}catch(Exception e) {
			e.printStackTrace();
		}
		
		if(next != null) 
			next.doFilterThree(videoFrames, devices);
		
		try {
			postHandleStageThree(videoFrames, devices);
		}catch(Exception e) {
			e.printStackTrace();
		}
		
		context.remove();
	}
	
	/**
	 * 初始化
	 * @param device
	 */
	public final void doInitialize(VideoStreamInfra device, boolean changeFlag) {
		try {
			initialize(device, changeFlag);
		}catch(Exception e) {
			throw new RuntimeException(e);
		}
		
		if(next != null) {
			next.initContext.putAll(initContext);
			try {
				next.doInitialize(device, changeFlag);
			}catch(Exception e) {
				destroy(device);
				initContext.clear();
				throw new RuntimeException(e);
			}
		}
	}
	/**
	 * 初始化
	 * @param device
	 */
	public final void doReInitialize(VideoStreamInfra device, boolean changeFlag) {
		try {
			initialize(device, changeFlag);
		}catch(Exception e) {
			throw new RuntimeException(e);
		}
		if(next != null) {
			try {
				next.initialize(device, changeFlag);
			}catch(Exception e) {
				throw new RuntimeException(e);
			}
		}
	}
	
	/**
	 * 初始化
	 * @param device
	 */
	public final void doDestroy(VideoStreamInfra device) {
		try {
			destroy(device);
		}catch(Exception e) {
			e.printStackTrace();
		}
		
		if(next != null)
			next.doDestroy(device);
		
		initContext.clear();
	}

	/** 处理这个gpuframe  */
	protected void handleStageOne(VideoFrame[] videoFrames, VideoStreamInfra[] devices)  throws Exception {}
	
	/** 后处理这个gpuframe */
	protected void postHandleStageOne(VideoFrame[] videoFrames, VideoStreamInfra[] devices) throws Exception {}
	
	/** 处理这个gpuframe  */
	protected void handleStageTwo(VideoFrame[] videoFrames, VideoStreamInfra[] devices)  throws Exception {}
	
	/** 后处理这个gpuframe */
	protected void postHandleStageTwo(VideoFrame[] videoFrames, VideoStreamInfra[] devices) throws Exception {}

	/** 处理这个gpuframe  */
	protected void handleStageThree(VideoFrame[] videoFrames, VideoStreamInfra[] devices)  throws Exception {}
	
	/** 后处理这个gpuframe */
	protected void postHandleStageThree(VideoFrame[] videoFrames, VideoStreamInfra[] devices) throws Exception {}
	
	/** 初始化 */
	protected void initialize(VideoStreamInfra device, boolean changeFlag) {}
	
	/** 销毁 */
	protected void destroy(VideoStreamInfra device) {}
	
	/** 全部filter */
	public static final List<Class<? extends VideoNonSeenFilter>> existingFilter = Collections.synchronizedList(Lists.newArrayList());
	
	/** 获取有多少个filter */
	public static VideoNonSeenFilter newInstances(){
		List<VideoNonSeenFilter> result = VideoNonSeenFilter.existingFilter
					.stream()
					.sorted((l, r)->{
						Order lo = l.getDeclaredAnnotation(Order.class);
						Order ro = r.getDeclaredAnnotation(Order.class);
						return (lo == null ? 0 : lo.value()) - (ro == null ? 0 : ro.value());
					})
					.map(clazz -> {
						try {
							return (VideoNonSeenFilter) clazz.getDeclaredConstructor().newInstance();
						} catch (Exception e) {
							throw new RuntimeException(e);
						}
					})
					.filter(Objects::nonNull)
					.collect(Collectors.toList());
		
		result.add(0, new DestroyNonSeenFilter());
		
		for(int index = 0 ; index < result.size() - 1; index ++) 
			result.get(index).next = result.get(index + 1);
		
		return result.get(0);
	}
	
	public static class Counter {
		public static final AtomicLong nonSeenHandlingItemQueue   = new AtomicLong();
		public static final AtomicLong nonSeenHandlingItemHandled = new AtomicLong();
		public static final AtomicLong nonSeenHandlingSended      = new AtomicLong();
	}
}
