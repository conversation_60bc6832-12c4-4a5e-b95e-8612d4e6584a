package com.sensetime.intersense.cognitivesvc.stream.video.service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.netflix.appinfo.ApplicationInfoManager;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.event.RebalancedEvent;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.DarwinUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream;

import io.swagger.v3.oas.annotations.Operation;

@Component
public class RemappedSourceService {

	@Autowired
	private VideoStreamInfraRepository deviceMapper;

	@Autowired
	private Broadcaster broadcaster;

	@Autowired
	private List<VideoFrameFilter> counters = List.of();

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Order(value = RebalancedEvent.RemappedSource)
	@EventListener(classes = {RebalancedEvent.class})
	public void rebalance(RebalancedEvent event) throws Exception {
		if(!event.isKeepGoing() || !event.isMaster())
			return;

		boolean ret = Utils.instance.tryLock("RemappingRtspSource", 60);
		if(!ret)
			return ;

		List<VideoStreamInfra> infras = deviceMapper.findByStsOrderByDeviceIdAsc(0);

		Map<String, List<VideoStreamInfra>> infraMap = infras.stream()
				.filter(infra -> VideoStream.isStreamRemote(infra.getRtspSource()))
				.collect(Collectors.toMap(
					infra -> infra.getRtspSource(),
					infra -> List.of(infra),
					ListUtils::union
				));

		List<Pair<ServiceInstance, List>> mappedRtsps = broadcaster.getForPair("", "/cognitive/darwin/getMappedRtsp", Map.of(), List.class, 2);

		//检查是否同一个url开了两次
		Set<String> adds = new HashSet<String>();
		Set<String> deletes = new HashSet<String>();
		mappedRtsps.stream()
				   .map(pair -> (List<Map<String, Object>>)pair.getRight())
				   .flatMap(List::stream)
				   .collect(Collectors.toMap(
						   obj -> obj.get("source").toString(),
						   obj -> List.of(obj),
						   ListUtils::union))
				   .forEach((k, v) -> {
					   if(v.size() > 1)
						   deletes.add(k);
				   });

		if(!deletes.isEmpty()) {
			broadcaster.postForObject("", "/cognitive/darwin/deleteMappedRtsp", deletes, String.class);
			mappedRtsps = broadcaster.getForPair("", "/cognitive/darwin/getMappedRtsp", Map.of(), List.class, 2);
			deletes.clear();
		}

		//检查是否有需要删除的
		Map<String, String> currentRemappedSources = mappedRtsps.stream()
				.map(pair -> {
						List<Map<String, Object>> objs = (List<Map<String, Object>>)pair.getRight();
						return objs.stream()
								   .map(obj -> new MutablePair<String, String>(obj.get("source").toString(), "rtsp://" + pair.getLeft().getHost() + ":" + DarwinUtils.rtspPort(pair.getLeft().getPort()) + obj.get("path").toString()))
								   .collect(Collectors.toList());
				})
				.flatMap(List::stream)
				.collect(Collectors.toMap(p -> p.getLeft(), p -> p.getRight()));

		Set<String> currentOngoingSources = infraMap.entrySet()
				.stream()
				.filter(entry -> {
					int extraCount = entry.getValue().stream().mapToInt(infra -> counters.stream().mapToInt(count -> count.extraRtspCount(infra)).sum()).sum();
					return extraCount + entry.getValue().size() >= Utils.instance.remappedMinNum;
				})
				.map(entry -> entry.getKey())
				.collect(Collectors.toSet());

		deletes.addAll(currentRemappedSources.keySet());
		deletes.removeAll(currentOngoingSources);

		if(!deletes.isEmpty()) {
			broadcaster.postForObject("", "/cognitive/darwin/deleteMappedRtsp", deletes, String.class);
			mappedRtsps = broadcaster.getForPair("", "/cognitive/darwin/getMappedRtsp", Map.of(), List.class, 2);
			deletes.clear();
		}

		//检查是否有新增的
		adds.addAll(currentOngoingSources);
		adds.removeAll(currentRemappedSources.keySet());

		if(!adds.isEmpty() && !mappedRtsps.isEmpty()) {
			for(String rtspSource : adds) {
				Collections.sort(mappedRtsps, (l, r) -> Integer.compare(l.getValue().size(), r.getValue().size()));

				Pair<ServiceInstance, List> pair = mappedRtsps.get(0);
				pair.getRight().add(StringUtils.EMPTY);//增加一个凑数 下个循环排序时候能放在后面

				broadcaster.postForObject(List.of(pair.getLeft()), "/cognitive/darwin/addMappedRtsp", rtspSource, String.class);
			}

			mappedRtsps = broadcaster.getForPair("", "/cognitive/darwin/getMappedRtsp", Map.of(), List.class, 2);
			adds.clear();
		}

		currentRemappedSources = mappedRtsps.stream()
				.map(pair -> {
						List<Map<String, Object>> objs = (List<Map<String, Object>>)pair.getRight();
						return objs.stream()
								   .map(obj -> new MutablePair<String, String>(obj.get("source").toString(), "rtsp://" + pair.getLeft().getHost() + ":" + DarwinUtils.rtspPort(pair.getLeft().getPort()) + obj.get("path").toString()))
								   .collect(Collectors.toList());
				})
				.flatMap(List::stream)
				.collect(Collectors.toMap(p -> p.getLeft(), p -> p.getRight()));

		for(VideoStreamInfra infra : infras) {
			String mappedRtsp = currentRemappedSources.get(infra.getRtspSource());

			if(!StringUtils.equals(mappedRtsp, infra.getRtspMappedSource())) {
				infra.setRtspMappedSource(mappedRtsp);
				deviceMapper.saveAndFlush(infra);
			}
		}

		Utils.instance.releaseLock("RemappingRtspSource");
	}

	@RestController("darwinProvider")
	@Tag(name="DarwinProvider",description = "DarwinProvider")
	@RequestMapping(value = "/cognitive/darwin/", produces = MediaType.APPLICATION_JSON_VALUE)
	public static class DarwinProvider extends BaseProvider{

		@Autowired
		private ApplicationInfoManager infoManager;

		@Operation(summary = "查询映射的rtsp流",method = "GET", hidden = true)
		@RequestMapping(value = "/getMappedRtsp", method = RequestMethod.GET)
		public Object getMappedRtsp() {
			String ip = infoManager.getEurekaInstanceConfig().getIpAddress();

			return DarwinUtils.getMappedRtsp(ip);
		}

		@Operation(summary = "add映射的rtsp流", method = "POST", hidden = true)
		@RequestMapping(value = "/addMappedRtsp", method = RequestMethod.POST)
		public Object addMappedRtsp(@RequestBody String rtspSource) {
			String ip = infoManager.getEurekaInstanceConfig().getIpAddress();

			JSONObject obj = (JSONObject)DarwinUtils.getMappedRtsp(ip).stream().filter(item -> StringUtils.equals(rtspSource, ((JSONObject)item).getString("source"))).findAny().orElse(null);
			if(obj == null)
				return DarwinUtils.addMappedRtsp(ip, rtspSource, RandomStringUtils.randomAlphabetic(10));
			else
				return "Already exist.";
		}

		@Operation(summary = "add映射的rtsp流", method = "POST", hidden = true)
		@RequestMapping(value = "/deleteMappedRtsp", method = RequestMethod.POST)
		public Object deleteMappedRtsp(@RequestBody List<String> rtspSources) {
			String ip = infoManager.getEurekaInstanceConfig().getIpAddress();

			List<JSONObject> objs = (List<JSONObject>)DarwinUtils.getMappedRtsp(ip).stream().map(item -> (JSONObject)item).filter(item -> rtspSources.contains(item.getString("source"))).collect(Collectors.toList());
			for(JSONObject obj : objs)
				DarwinUtils.deleteMappedRtsp(ip, obj.getString("id"));

			return "OK";
		}
	}


	/**
	 {
		"path": "/COG",
		"transType": "TCP",
		"outBytes": 0,
		"inBytes": 778447,
		"id": "9Ou5aXCngz",
		"source": "rtsp://************/face",
		"onlines": 0,
		"startAt": "2022-06-17 20:45:18",
		"url": "rtsp://************:33554/COG"
	}
	rtsp://************:33554/COG
	*/
}
