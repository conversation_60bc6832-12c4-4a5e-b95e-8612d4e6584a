package com.sensetime.intersense.cognitivesvc.stream.video;

import java.lang.Thread.State;
import java.nio.Buffer;
import java.util.ArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.bytedeco.javacv.Frame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawItem;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoSeenFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.service.VideoFrameFilter.VideoChecked;

/**
 * 需要显示到rtmp的视频流，需要独立的tracker，占用显存多
 */
public class VideoStreamSeen extends VideoStream{
	private static final Logger log = LoggerFactory.getLogger(VideoStreamSeen.class);

	private VideoSeenFilter seenFilter = VideoSeenFilter.newInstances();
	
	private CountDownLatch latch = new CountDownLatch(1);
	private boolean needHostFrame = true;
	private boolean needDeviceFrame = true;
	
	public VideoStreamSeen(VideoStreamInfra device, boolean useGpuDecoder, VideoChecked checked) {
		super(device);
		this.useDeviceDecoder = useGpuDecoder && Initializer.isGpu();
		this.needHostFrame = checked.isNeedHostFrame();
		this.needDeviceFrame = checked.isNeedDeviceFrame();
	}

	private Thread daemonThread = new Thread(Utils.cogGroup, new Runnable() {
		private long lastExpire = 0;
		
		@Override
		public void run() {
			Initializer.bindDeviceOrNot();
			
			try{
				boolean keepRunning = true;

				long now = 0;
				now = System.currentTimeMillis();
				while(!stoped && keepRunning && frameIndex < maxLimitFrameIndex) {
					keepRunning = handleNextFrame();

					boolean logged = Utils.instance.watchFrameTiktokLevel == -797;

					if (logged){
						if(frameIndex % 100 == 0) {
							log.info("[VideoHandleLog] VideoStreamSeen whole [" + device.getDeviceId() + "] frameindex[" + frameIndex + "] cost [" + (System.currentTimeMillis() - now) + "]");
							now = System.currentTimeMillis();
						}
					}
				}
			}finally {
				latch.countDown();
				close();
				videoStatus = VideoStatus.EOF;
			}
		}
		
		private final boolean handleNextFrame() {
			long now = System.currentTimeMillis();
			boolean handleCurrentFrame = frameIndex % (Math.abs(device.getFrameSkip()) + 1) == 0;
			
			try {					
				VideoFrame videoFrame = grabberNextFrame(needHostFrame, needDeviceFrame);
				if(!isStreamRemote(device.getRtspSource())) {
					long sleep = 1000 / device.getVideoRate() - lastExpire;
					if(sleep > 0)
						Thread.sleep(sleep);
				}
				
				seenFilter.addAttribute("handleCurrentFrame", handleCurrentFrame);
				
				Frame frame = new Frame();
				frame.imageWidth  = KestrelApi.kestrel_frame_video_width(videoFrame.getCpuFrame());
				frame.imageHeight = KestrelApi.kestrel_frame_video_height(videoFrame.getCpuFrame());
				frame.imageStride = KestrelApi.kestrel_frame_video_stride(videoFrame.getCpuFrame(), 0);
				frame.image = new Buffer[] {KestrelApi.kestrel_frame_plane(videoFrame.getCpuFrame(), 0).getByteBuffer(0, frame.imageWidth * frame.imageHeight * 3)};
				frame.imageChannels = 3;
				frame.imageDepth = 8;
				
				seenFilter.addAttribute("frame", frame);
				seenFilter.addAttribute("draws", new ArrayList<DrawItem>());

				seenFilter.doFilter(videoFrame, device);
			}catch(Throwable e) {
				VideoStreamSeen.this.frameIndex ++;
				
				if("KPLUGIN_E_EOF".equals(e.getMessage())) {
					if(!isStreamEndless(device.getRtspSource())) {
						VideoStreamSeen.this.videoStatus = VideoStatus.EOF;
						return false;
					}else {						
						try {
							close();

							open();
							
							VideoStreamSeen.this.frameIndex = 0;
							log.debug("video succeed to reopen [" + device.getDeviceId() + "].");
						} catch (Throwable e1) {
							try { Thread.sleep(3000); } catch (InterruptedException e2) { }
							log.warn("video fail to reopen [" + device.getDeviceId() + "].");
						}
					}
				}else if(StringUtils.startsWith(e.getMessage(), "SOURCE_CHANGED") || errorTime % Utils.instance.faceStreamReopenErrorTime == 0){
					try {
						close();
						open();
						
						VideoStreamSeen.this.frameIndex = 0;
						log.debug("video succeed to reopen [" + device.getDeviceId() + "].");
					} catch (Throwable e1) {
						try { Thread.sleep(3000); } catch (InterruptedException e2) { }
						log.warn("video fail to reopen [" + device.getDeviceId() + "].");
					}
				}
			}finally {
				lastExpire = System.currentTimeMillis() - now;
			}
			
			return true;
		}
	});
	
	@Override 
	public synchronized void start() {
		if(daemonThread.getState() != State.NEW) 
			return ;
		
		log.info("Seen video is trying starting deviceId[" + device.getDeviceId() + "].");
		
		Initializer.bindDeviceOrNot();
		
		open();
		
		seenFilter.doInitialize(device);
		
		daemonThread.setName("[" + device.getDeviceId() + "] seen-daemonThread");
		daemonThread.setDaemon(true);
		daemonThread.start();
		
		log.info("Seen video started deviceId[" + device.getDeviceId() + "].");
	}
	
	@Override 
	public synchronized void stop() {
		log.info("Seen video is closing deviceId[" + device.getDeviceId() + "].");
		
		Initializer.bindDeviceOrNot();
		
		stoped = true;
		
		try { 
			boolean ret = latch.await(30, TimeUnit.SECONDS); 
			if(!ret) {
				throw new RuntimeException("closing seen steam[" + device.getDeviceId() + "] cost more than 30 second, please check or ingore.");
			}
		} 
		catch (InterruptedException e) { 
			
		}finally {
			seenFilter.doDestroy(device);
		}
		
		try { Thread.sleep(1000); } catch (InterruptedException e) { }
		
		close();
	}
	
	@Override
	public void setDevice(VideoStreamInfra device) {
		super.setDevice(device);
		
		VideoSeenFilter filter = seenFilter;
		
		while(filter != null) {
			filter.setDevice(device);
			filter = filter.getNext();
		}
	}
}
