package com.sensetime.intersense.cognitivesvc.stream.video.filter;

import java.lang.Thread.State;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.ffmpeg.global.avutil;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.opencv.opencv_core.IplImage;
import org.bytedeco.opencv.opencv_core.Mat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawItem;

import lombok.AllArgsConstructor;

/**
         渲染过程最后一步，推流，并销毁cpu和gpu的frame
 */
@SuppressWarnings("unchecked")
public class RecordAndDestroySeenFilter extends VideoSeenFilter{
	private static final Logger log = LoggerFactory.getLogger(RecordAndDestroySeenFilter.class);

	private FFmpegFrameRecorder recorder;
	
	private VideoStreamInfra device;

	/** 后处理这个gpuframe */
	@Override
	protected void postHandle(VideoFrame videoFrame, VideoStreamInfra device) {
		Frame frame = (Frame)context.get("frame");
		List<DrawItem> draw_item = (List<DrawItem>)context.get("draws");
		
		try { 
			boolean ret = frameToRecord.offer(new RecordEntity(frame, draw_item, videoFrame), 1, TimeUnit.SECONDS); 
			if(!ret) {
				FrameUtils.batch_free_frame(videoFrame);
			}
		} catch (InterruptedException e) { }
	}
	
	@Override
	protected void initialize(VideoStreamInfra device) {
		if(recorder != null) 
			return ;
		
		this.device = device;
		
		if(recordThread.getState() == State.NEW) {
	    	recordThread.setDaemon(true);
	    	recordThread.setName("[" + device.getDeviceId() + "] seen-recordThread");
	    	recordThread.start();
	    }
	}
	
	@Override
	protected void destroy(VideoStreamInfra device) {
		stoped = true;
	}
	
	private LinkedBlockingQueue<RecordEntity> frameToRecord = new LinkedBlockingQueue<RecordEntity>(5);
	
	/** 推流 并释放frame
	 */
	private Thread recordThread = new Thread(Utils.cogGroup, ()->{
		Initializer.bindDeviceOrNot();
		
		tryOpenRtmp();

		boolean logged = Utils.instance.watchFrameTiktokLevel == -909;
		
		while(!stoped || !frameToRecord.isEmpty()) {
			RecordEntity entity = null;
			
			try { entity = frameToRecord.poll(100, TimeUnit.MILLISECONDS); } catch (Exception e) { }
			
			if(entity == null) 
				continue;
			
			OpenCVFrameConverter.ToMat tomat = new OpenCVFrameConverter.ToMat();
			OpenCVFrameConverter.ToIplImage toipl = new OpenCVFrameConverter.ToIplImage();
			
			try {
				Mat imgSrc      = tomat.convert(entity.frame);
				IplImage picSrc = toipl.convert(entity.frame);
				
				if(CollectionUtils.isNotEmpty(entity.draw_item))
					for(DrawItem item : entity.draw_item) {
						if(logged){
							log.info("recordThread calss:{}, size:{}",item.getClass(), entity.draw_item.size());
						}
						boolean ret = item.drawTo(entity.frame, picSrc, imgSrc);
						if(!ret){
							//something
						}
					}
				
				recorder.record(entity.frame, avutil.AV_PIX_FMT_RGB24);
				
				imgSrc.close();
				picSrc.close();
			} catch (Exception e) {
				try {
					try {
						Thread.sleep(3000);
						this.recorder.close();
						this.recorder = null;
					} catch (Exception ee) { }
					
					tryOpenRtmp();
				}catch(Exception e1) {
					e.printStackTrace();
					e1.printStackTrace();
				}
			}finally {
				FrameUtils.batch_free_frame(entity.videoFrame);
			}
		}
		
		if(this.recorder == null) {
			return ;
		}
		
		FFmpegFrameRecorder recorder = this.recorder;
		this.recorder = null;
		
		try {
			Thread.sleep(100);
			recorder.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		recorder = null;
		
		log.info("[" + device.getDeviceId() + "] recordThread terminated.");
	});
	
	private void tryOpenRtmp() {
		int frameRate = device.getVideoRate() == null ? 25 : device.getVideoRate();

	    while(!stoped) {
			FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(device.getRtmpDestination(), device.getRtspWidth(), device.getRtspHeight(), 0);
			recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
		    recorder.setFormat("flv");
		    recorder.setFrameRate(frameRate);
		    recorder.setInterleaved(true);
		    recorder.setVideoOption("crf", Integer.toString(frameRate));
		    recorder.setPixelFormat(0);
		    recorder.setGopSize(-1);
		    
		    if(StringUtils.isNotBlank(device.getRtmpOption()))
			    for(String option : device.getRtmpOption().split(",")) {
			    	String[] param = option.split(":");
				    recorder.setVideoOption(param[0], param[1]);
			    }
		    
			try {
			    recorder.start();
			    this.recorder = recorder;
			    break;
			} catch (Exception e) {
				log.warn("can not open deviceid is [" + device.getDeviceId() + "] rtmp [" + device.getRtmpDestination() + "], please check.");
				try {  Thread.sleep(1000); recorder.close(); } catch (Exception e1) { }
			}
		}
	}
	
	@AllArgsConstructor
	private static class RecordEntity{
		private Frame frame;
		private List<DrawItem> draw_item;
		private VideoFrame videoFrame;
	}
}
