package com.sensetime.intersense.cognitivesvc.stream.video.service;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.DeviceInfoEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.DeviceStatusEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.netflix.eureka.EurekaServiceInstance;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.appinfo.InstanceInfo.InstanceStatus;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.event.GpuMemLimitEvent;
import com.sensetime.intersense.cognitivesvc.server.event.RebalancedEvent;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.HostUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoStatus;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamNonSeen;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamSeen;
import com.sensetime.intersense.cognitivesvc.stream.video.service.VideoFrameFilter.VideoChecked;


import io.swagger.v3.oas.annotations.Operation;
import lombok.Getter;

@Component
public class RebalanceService {
	private static final Logger log = LoggerFactory.getLogger(RebalanceService.class);
	
	@Value("${spring.application.name}")
	private String appName;
	
	@Value("${cognitive.num:-1}")
	private int cogNum;
	
	@Getter
	private final Map<String, VideoStream> currentStreamMap = new HashMap<String, VideoStream>();
	
	@Autowired
	private ApplicationContext applicationContext;

	@Autowired
	BaseOutput device_status_event_output;

	@Autowired
	private BaseOutput senseyes_raw_event_output;

	@Autowired(required = false)
	private List<VideoFrameFilter> videoFrameFilters = new ArrayList<VideoFrameFilter>();
	
	@Autowired
	private VideoStreamInfraRepository deviceMapper;
	
	@Autowired
	private DiscoveryClient discoveryClient;

	@Autowired
	private Broadcaster broadcastService;

	@Autowired
	private XDynamicModelRepository dynamicModelMapper;

	@Autowired(required = false)
	private InstancesWatchHolder instancesWatchHolder;
	
	@Order(value = RebalancedEvent.ReabalanceStream)
	@EventListener(classes = {RebalancedEvent.class})
	public void rebalance(RebalancedEvent event) throws Exception {
		if(!event.isKeepGoing() || !event.isMaster()) 
			return;

		boolean flagChange = false;
		String deviceId =  "";
		if(event.getParameter() != null && !event.getParameter().isEmpty()) {
			 flagChange = (boolean) event.getParameter().get("flagChange");
			 deviceId = (event.getParameter().containsKey("deviceId")) ? event.getParameter().get("deviceId").toString() : "";
		}
		Optional<Boolean> isChanged = broadcastService.postForObject(appName, "/cognitive/rebalance/execute", Map.of("flagChange", flagChange, "deviceId", deviceId ), Boolean.class)
				.stream()
				.filter(b -> Boolean.TRUE.equals(b))
				.findAny();
		
		if(isChanged.isPresent())
			event.setKeepGoing(false);
	}
	
	@RestController("streamRebalanceProvider")
	@RequestMapping(value = "/cognitive/rebalance/", produces = MediaType.APPLICATION_JSON_VALUE)
	@Tag(name = "StreamRebalanceProvider", description = "rebalance controller")
	public static class StreamRebalanceProvider extends BaseProvider {
		
		private final Semaphore semaphore = new Semaphore(1);
		
		@Autowired
		private RebalanceService rebalanceService;
		
		@Operation(summary = "rebalance", method = "POST", hidden = true)
		@RequestMapping(value = "/execute", method = RequestMethod.POST)
		public Boolean execute(@RequestBody Map<String, Object> event) throws Exception {
			boolean isChanged = false;

			boolean flagChange = false;
			String deviceId =  "";
			if(event != null && !event.isEmpty()){
				 flagChange = (boolean) event.get("flagChange");
				 deviceId = (event.containsKey("deviceId")) ? event.get("deviceId").toString() : "";
			}
			try {
				if(semaphore.tryAcquire()) {
					try {
						//log.info(">> StreamRebalanceProvider rebalance");
						isChanged = rebalanceService.rebalance(flagChange, deviceId);
					}finally {
						semaphore.release();
					}
				}
			}catch(Exception e) {
				
			}
			
			return isChanged;
		}
	}
	
	private boolean rebalance(boolean flagChange, String updateDeviceId) throws Exception {
		Initializer.bindDeviceOrNot();
		
		long currentTimeMillis = System.currentTimeMillis();
		int cogNum = this.cogNum;
		if(cogNum <= 0)
			cogNum = discoveryClient.getInstances(appName).stream()
									.filter(item -> !(item instanceof EurekaServiceInstance) || ((EurekaServiceInstance)item).getInstanceInfo().getStatus() == InstanceStatus.UP)
									.mapToInt(e -> 1).sum();

		//检查所有流是否达到最大帧数或者流已经EOF
		for(VideoStream stream : currentStreamMap.values()){
			Integer max = stream.getDevice().getFrameMax();
			if(stream.getVideoStatus() == VideoStatus.EOF || (max != null && stream.getFrameIndex() >= max))
				deviceMapper.updateStsAndSeedByDeviceId(1, VideoStreamInfra.initSeed, new Date(currentTimeMillis), stream.getDevice().getDeviceId());
		}
		
		List<VideoStreamInfra> infras = deviceMapper.findAll();
		List<VideoStreamInfra> infrasOn = infras.stream()
				.filter(infra -> infra.getSts() == 0)
				.sorted((l, r) -> StringUtils.compare(l.getDeviceId(), r.getDeviceId()))
				.collect(Collectors.toList());

		//检查所有渲染流视频的是否超时了
		for(VideoStreamInfra device : infras) {
			if(Utils.instance.keepAliveExpire > 0 && judgeRtmpOn(device)
					&& (device.getKeepAlive() == null || currentTimeMillis - device.getKeepAlive().getTime() > Utils.instance.keepAliveExpire * 1000)) {
				device.setRtmpOn("1");
				deviceMapper.updateRtmpOn("1", device.getDeviceId());
			}
		}


		Map<String, String> allGroupModels = new HashMap<>();
		for (XDynamicModel model : dynamicModelMapper.findAll()) {
			if(model.getMonopolizedGroup() !=null && !model.getMonopolizedGroup().isEmpty() ) {
				allGroupModels.put(model.getAnnotatorName(), model.getMonopolizedGroup());
			}
		}
		
		Map<String, VideoChecked> frameFilterMap = infrasOn.stream()
				.map(infra -> {
					List<VideoChecked> values = videoFrameFilters.stream().map(filter -> filter.check(infra, allGroupModels)).filter(Objects::nonNull).collect(Collectors.toList());
					return new ImmutablePair<String, VideoChecked>(infra.getDeviceId(), VideoChecked.merge(values));
				})
				.filter(entry -> entry.getValue().isNeedHostFrame() || entry.getValue().isNeedDeviceFrame())
				.collect(Collectors.toMap(
						entry -> entry.getKey(), 
						entry -> entry.getValue()
				));
		
		Map<String, VideoStreamInfra> streamInfraMap = infrasOn.stream()
				.filter(infra -> frameFilterMap.containsKey(infra.getDeviceId()) 
						&& (VideoStream.isStreamRemote(infra.getRtspSource()) || new File(infra.getRtspSource()).exists()))
				.collect(Collectors.toMap(VideoStreamInfra::getDeviceId, Function.identity()));

		//add模式变化
		if(flagChange) {
			log.info("[v2.8.7] flagChange{},{}", flagChange, updateDeviceId);
			for (Entry<String, VideoStream> streamEntry : currentStreamMap.entrySet()) {
				VideoStream stream = streamEntry.getValue();
				String deviceIdStream = streamEntry.getKey();
				if(StringUtils.isNotBlank(updateDeviceId) && deviceIdStream.equals(updateDeviceId)){
					Thread.sleep(500);
					stream.changeStart();
					return true;
				}
				//stream.stop();
				//stream.start();
				stream.changeStart();
				Thread.sleep(1000);
			}
			return true;
		}
		//add Low frequency does not run at high frequency nodes
		if(Utils.instance.stabilityFlag && instancesWatchHolder !=null){
			Pair<Boolean, Integer> congNumPair = 	instancesWatchHolder.getSwitchWatchHolder(allGroupModels);
            int congNumRe = congNumPair.getRight();
            if(congNumPair.getLeft()){
				cogNum = congNumRe;
			}else {
				if (congNumRe > 0) {
					cogNum = (cogNum - congNumRe) > 0 ? cogNum - congNumRe : cogNum;
				}
			}
			log.info("[VideoHandleLog] [lowStream] cogNumReSwitchWatchHolder{},cogNum {}", congNumRe, cogNum);
		}
		boolean logged = Utils.instance.watchFrameTiktokLevel == -796;

		//log.info("[VideoHandleLog] [lowStream] frameFilterMap{}------{}", frameFilterMap.size(), streamInfraMap.size());
		for(Iterator<Entry<String, VideoStream>> it = currentStreamMap.entrySet().iterator(); it.hasNext(); ) {
			Entry<String, VideoStream> streamEntry = it.next();
			VideoStream stream = streamEntry.getValue();
			VideoStreamInfra deviceIndb = streamInfraMap.get(stream.getDevice().getDeviceId());
			
			if(deviceIndb == null || !Objects.equals(Utils.instance.getSeed(), deviceIndb.getSeed())) {
				try {
					log.info("[videoHandleLog] [stream] currentStreamMap stream stop,deviceIndb == null {} or seed {}", deviceIndb == null,Utils.instance.getSeed());
					stream.stop();
				}catch(Exception e) {
					e.printStackTrace();
				}
				
				it.remove();
				
				if(VideoStream.isStreamEndless(stream.getDevice().getRtspSource())) 
					deviceMapper.updateSeed(VideoStreamInfra.initSeed, new Date(currentTimeMillis), stream.getDevice().getDeviceId(), stream.getDevice().getSeed());
				else 
					deviceMapper.updateSeed(VideoStreamInfra.nonRtspDoneSeed, new Date(currentTimeMillis), stream.getDevice().getDeviceId(), stream.getDevice().getSeed());
			}else {
				stream = checkRtmpSwitch(stream.getDevice(), deviceIndb, streamEntry, frameFilterMap);
				
				try {
					stream.start();
				}catch(Exception e) {
					e.printStackTrace();
				}
				
				deviceMapper.updateSeed(Utils.instance.getSeed(), new Date(currentTimeMillis), stream.getDevice().getDeviceId(), Utils.instance.getSeed());
				
				if(stream.getVideoStatus() == VideoStatus.UNKNOWN)
					log.warn("Video[" + stream.getDevice().getDeviceId() + "] , the video stream may not be running for 10s, please check if this message show up many times.");
				else if(stream.getVideoStatus() == VideoStatus.OK)
					stream.setVideoStatus(VideoStatus.UNKNOWN);
			}
		}

		boolean isChanged = false;
		if(cogNum <= 0 || streamInfraMap.size() <= 0)
			return isChanged;
		
		List<VideoStreamInfra> handleNonSeenDevices = streamInfraMap.values().stream().filter(device -> !judgeRtmpOn(device)).collect(Collectors.toList());
        if(logged) {
			log.info("[VideoHandleLog] [lowStream] handleNonSeenDeviceSize {}, cogNum {}", handleNonSeenDevices.size(), cogNum);
		}
		if(!handleNonSeenDevices.isEmpty()){
			// 根据开关决定是否使用镜头算力功能
			if (Utils.instance.cameraDispatch) {
				// 计算总算力需求，而不是简单的设备数量
				float totalComputePower = handleNonSeenDevices.stream()
						.map(VideoStreamInfra::getComputePower)
						.reduce(0.0f, Float::sum);
				
				// 计算需要多少节点来处理这些流
				int needNonseenCount = (int) Math.ceil(totalComputePower / cogNum);
				
				// 节点数量受nonSeenMaxCount限制
				if(needNonseenCount > Utils.instance.nonSeenMaxCount) {
					log.warn("need to open Nonseen count[" + needNonseenCount + "], but nonSeenMaxCount is [" + Utils.instance.nonSeenMaxCount + "], please ignore or modify it to open more stream.");
					needNonseenCount = Utils.instance.nonSeenMaxCount;
				}
				
				// 目标算力总和也受nonSeenMaxCount限制
				float targetComputePower = Math.min(totalComputePower, Utils.instance.nonSeenMaxCount);
				
				// 计算当前正在处理的流的总算力
				float ongoingComputePower = currentStreamMap.values().stream()
						.filter(stream -> stream instanceof VideoStreamNonSeen)
						.map(stream -> stream.getDevice().getComputePower())
						.reduce(0.0f, Float::sum);
						
				if(logged) {
					log.info("[VideoHandleLog] [lowStream] totalComputePower {}, needNonseenCount {}, targetComputePower {}, ongoingComputePower {}, currentStreamMap {}", 
							totalComputePower, needNonseenCount, targetComputePower, ongoingComputePower, currentStreamMap.size());
				}
				
				if(targetComputePower > ongoingComputePower) {
					steals((long)Math.ceil(targetComputePower - ongoingComputePower), handleNonSeenDevices, currentTimeMillis, frameFilterMap);
					isChanged = true;
				}else if(Utils.instance.rebalanceOn && targetComputePower < ongoingComputePower) 
					returns((long)Math.ceil(ongoingComputePower - targetComputePower), handleNonSeenDevices, currentTimeMillis);
			} else {
				// 使用原来的逻辑
				int needNonseenCount = (int) Math.ceil(handleNonSeenDevices.size() / (float)cogNum);
				if(needNonseenCount > Utils.instance.nonSeenMaxCount) {
					log.warn("need to open Nonseen count[" + needNonseenCount + "], but nonSeenMaxCount is [" + Utils.instance.nonSeenMaxCount + "], please ignore or modify it to open more stream.");
					needNonseenCount = Utils.instance.nonSeenMaxCount;
				}
				
				long ongoingNonseenCount = currentStreamMap.values().stream().filter(stream -> stream instanceof VideoStreamNonSeen).count();
				
				if(logged) {
					log.info("[VideoHandleLog] [lowStream] needNonseenCount {}, ongoingNonseenCount {}, currentStreamMap {}", 
							needNonseenCount, ongoingNonseenCount, currentStreamMap.size());
				}
				
				if(needNonseenCount > ongoingNonseenCount) {
					steals((long)Math.ceil(needNonseenCount - ongoingNonseenCount), handleNonSeenDevices, currentTimeMillis, frameFilterMap);
					isChanged = true;
				}else if(Utils.instance.rebalanceOn && needNonseenCount < ongoingNonseenCount) 
					returns((long)Math.ceil(ongoingNonseenCount - needNonseenCount), handleNonSeenDevices, currentTimeMillis);
			}
		}
		
		List<VideoStreamInfra> handleSeenDevices = streamInfraMap.values().stream().filter(device -> judgeRtmpOn(device)).collect(Collectors.toList());
		if(!handleSeenDevices.isEmpty()) {
			// 根据开关决定是否使用镜头算力功能
			if (Utils.instance.cameraDispatch) {
				// 计算可见流的总算力需求
				float totalComputePower = handleSeenDevices.stream()
						.map(VideoStreamInfra::getComputePower)
						.reduce(0.0f, Float::sum);
						
				// 计算需要多少节点来处理这些流
				int needSeenCount = (int) Math.ceil(totalComputePower / cogNum);
				
				if(needSeenCount > Utils.instance.seenMaxCount) {
					log.warn("need to open Seen count[" + needSeenCount + "], but seenMaxCount is [" + Utils.instance.seenMaxCount + "], please ignore or modify it to open more stream.");
					needSeenCount = Utils.instance.seenMaxCount;
				}
				
				// 目标算力总和也受seenMaxCount限制
				float targetComputePower = Math.min(totalComputePower, Utils.instance.seenMaxCount);
				
				// 计算当前正在处理的流的总算力
				float ongoingComputePower = currentStreamMap.values().stream()
						.filter(stream -> stream instanceof VideoStreamSeen)
						.map(stream -> stream.getDevice().getComputePower())
						.reduce(0.0f, Float::sum);
						
				if(logged) {
					log.info("[VideoHandleLog] [lowStream] totalComputePower {}, needSeenCount {}, targetComputePower {}, ongoingComputePower {}, currentStreamMap {}", 
							totalComputePower, needSeenCount, targetComputePower, ongoingComputePower, currentStreamMap.size());
				}
				
				if(targetComputePower > ongoingComputePower) {
					steals((long)Math.ceil(targetComputePower - ongoingComputePower), handleSeenDevices, currentTimeMillis, frameFilterMap);
					isChanged = true;
				}else if(Utils.instance.rebalanceOn && targetComputePower < ongoingComputePower)
					returns((long)Math.ceil(ongoingComputePower - targetComputePower), handleSeenDevices, currentTimeMillis);
			} else {
				// 使用原来的逻辑
				int needSeenCount = (int) Math.ceil(handleSeenDevices.size() / (float)cogNum);
				if(needSeenCount > Utils.instance.seenMaxCount) {
					log.warn("need to open Seen count[" + needSeenCount + "], but seenMaxCount is [" + Utils.instance.seenMaxCount + "], please ignore or modify it to open more stream.");
					needSeenCount = Utils.instance.seenMaxCount;
				}
				
				long ongoingSeenCount = currentStreamMap.values().stream().filter(stream -> stream instanceof VideoStreamSeen).count();
				
				if(needSeenCount > ongoingSeenCount) {
					steals((long)Math.ceil(needSeenCount - ongoingSeenCount), handleSeenDevices, currentTimeMillis, frameFilterMap);
					isChanged = true;
				}else if(Utils.instance.rebalanceOn && needSeenCount < ongoingSeenCount)
					returns((long)Math.ceil(ongoingSeenCount - needSeenCount), handleSeenDevices, currentTimeMillis);
			}
		}
		
		return isChanged;
	}

	private boolean steals(long needCount, List<VideoStreamInfra> devices, long currentTimeMillis, Map<String, VideoChecked> frameFilterMap) {
		boolean logged = false, changed = false;
		log.debug("stealing[" + needCount +"].");

		int timeoutExpire;
		if (devices.size() < Utils.instance.lockVideoDevicesNum) {
			timeoutExpire = Utils.instance.lockVideoExpire;
		} else {
			timeoutExpire = Utils.instance.lockVideoExpire + ((devices.size() / Utils.instance.lockVideoDevicesNum) * Utils.instance.lockVideoExpire);
		}
		
		// 根据开关决定排序方式
		if (Utils.instance.cameraDispatch) {
			// 首先按优先级排序，然后按算力倍数排序（算力小的优先）
			devices.sort((d1, d2) -> {
				// 先按优先级排序
				Integer p1 = d1.getPriority() == null ? Integer.MAX_VALUE : d1.getPriority();
				Integer p2 = d2.getPriority() == null ? Integer.MAX_VALUE : d2.getPriority();
				int priorityCompare = p1.compareTo(p2);
				
				// 如果优先级相同，再按算力排序
				if (priorityCompare == 0) {
					return Float.compare(d1.getComputePower(), d2.getComputePower());
				}
				return priorityCompare;
			});
		} else {
			// 原来的排序逻辑：只按优先级排序
			devices.sort((d1, d2) -> {
				Integer p1 = d1.getPriority() == null ? Integer.MAX_VALUE : d1.getPriority();
				Integer p2 = d2.getPriority() == null ? Integer.MAX_VALUE : d2.getPriority();
				return p1.compareTo(p2);
			});
		}

		// 记录已分配的算力
		float allocatedPower = 0;
		
		// 计算当前正在处理的相同类型流的总算力
		boolean isSeenType = devices.stream().anyMatch(d -> judgeRtmpOn(d));
		float currentPower = currentStreamMap.values().stream()
				.filter(stream -> (isSeenType && stream instanceof VideoStreamSeen) || 
								(!isSeenType && stream instanceof VideoStreamNonSeen))
				.map(stream -> stream.getDevice().getComputePower())
				.reduce(0.0f, Float::sum);
		
		// 确定最大允许的算力总量
		float maxAllowedPower = isSeenType ? Utils.instance.seenMaxCount : Utils.instance.nonSeenMaxCount;

		for(int index = 0; index < devices.size() && (Utils.instance.cameraDispatch ? allocatedPower < needCount : index < needCount); index ++) {
			VideoStreamInfra device = devices.get(index);
			if(currentStreamMap.containsKey(device.getDeviceId()))
				continue;
			
			if(!VideoStreamInfra.initSeed.equals(device.getSeed()))
				if(device.getUpdateTs() != null && currentTimeMillis - device.getUpdateTs().getTime() <= timeoutExpire * 1000L)
					continue;

			if (Utils.instance.cameraDispatch) {
				// 获取当前设备需要的算力
				float devicePower = device.getComputePower();
				
				// 检查是否会超出需要分配的算力
				if (allocatedPower + devicePower > needCount) {
					// 如果只剩最后一点算力，且这个设备算力太大，可以选择跳过
					if (needCount - allocatedPower < 0.5f) {
						break;
					}
					// 否则尝试找更合适的设备
					continue;
				}
				
				// 检查是否会超出最大允许流数限制
				if (currentPower + allocatedPower + devicePower > maxAllowedPower) {
					log.info("设备[{}]分配失败，算力为{}，当前已分配算力{}，新增算力{}，总算力{}将超过最大限制{}",
							device.getDeviceId(), devicePower, currentPower, allocatedPower, 
							(currentPower + allocatedPower + devicePower), maxAllowedPower);
					continue;
				}
			}

			if(index > 0)
				try { Thread.sleep(ThreadLocalRandom.current().nextInt(20, 100)); } catch (InterruptedException e) { }
			
			float memRate = HostUtils.getGpuMemRate();
			if(judgeRtmpOn(device)) {
				/** 渲染流打开时 看一下当前显存 */
				if(memRate > Utils.instance.seenGpuMemLimit) {
					if(!logged)
						log.warn("need to open new seen stream, but gpu mem is above " + memRate + " , more than seen limit [" + Utils.instance.seenGpuMemLimit + "], not enougn, ignore it.");
					applicationContext.publishEvent(new GpuMemLimitEvent(memRate));
					logged = true;
					break ;
				}
			}else {
				/** 后台流打开时 看一下当前显存 */
				if(memRate > Utils.instance.nonSeenGpuMemLimit) {
					if(!logged)
						log.warn("need to open new stream, but gpu mem is above [" + memRate + "], more than limit [" + Utils.instance.nonSeenGpuMemLimit + "], not enougn, ignore it.");
					logged = true;
					applicationContext.publishEvent(new GpuMemLimitEvent(memRate));
					break ;
				}
			}

			VideoChecked checked = frameFilterMap.get(device.getDeviceId());
			boolean useCpuDecoder = Utils.instance.decoderCpuCount > currentStreamMap.values().stream().filter(stream -> !stream.isUseDeviceDecoder()).count();
			
			VideoStream video = judgeRtmpOn(device) ?
					new VideoStreamSeen(device, !useCpuDecoder, checked)
				  : new VideoStreamNonSeen(device, !useCpuDecoder, checked, device_status_event_output, senseyes_raw_event_output);
					
			try {				
				int ret = -1;
				if(device.getSeed() == null) /** 更新时 只能更新没有NULL的 与取的时候相同 **/
					ret = deviceMapper.updateNullSeed(Utils.instance.getSeed(), new Date(currentTimeMillis), device.getDeviceId());
				else /** 更新时 只能更新没有更改的 即device.getSeed() 与取的时候相同 **/
					ret = deviceMapper.updateSeed(Utils.instance.getSeed(), new Date(currentTimeMillis), device.getDeviceId(), device.getSeed());
				
				if(ret > 0) {
					device.setSeed(Utils.instance.getSeed());
					currentStreamMap.put(device.getDeviceId(), video);
					video.start();
					
					// 更新实时视频流参数
					JSONObject codecPar = (JSONObject)VideoStream.chosenStreamInfoMap.get(device.getDeviceId()).get("codecpar");
					int frameRate = (Integer) VideoStream.chosenStreamInfoMap.get(device.getDeviceId()).get("frameRate");
					int width = (Integer)  codecPar.get("width");
					if(width!=device.getRtspWidth() || frameRate != device.getVideoRate()){
						int height = (Integer)  codecPar.get("height");
						device.setVideoRate(frameRate);
						device.setRtspWidth(width);
						device.setRtspHeight(height);
						deviceMapper.saveAndFlush(device);
						List<DeviceInfoEntity.DeviceInfo> deviceInfoList = new ArrayList<>();
						DeviceInfoEntity.DeviceInfo deviceInfo = DeviceInfoEntity.DeviceInfo.builder()
								.did(device.getDeviceId())
								.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
								.rtspWidth(String.valueOf(width))
								.rtspHeight(String.valueOf(height))
								.videoRate(String.valueOf(frameRate))
								.build();
						deviceInfoList.add(deviceInfo);
						DeviceInfoEntity deviceInfoEntity = DeviceInfoEntity.builder()
								.eventAction("DeviceInfoUpdate")
								.eventName("cognitive")
								.data(deviceInfoList)
								.build();
						try{
							device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceInfoEntity)).build());
							log.info("device_info_event_output send output {}",deviceInfoEntity);
						}catch (Exception ex){
							log.warn("device_info_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceInfoEntity);
						}
					}
					video.setDevice(device);

					if (Utils.instance.cameraDispatch) {
						// 累加已分配的算力
						allocatedPower += device.getComputePower();
						log.info("Allocated device {} with compute power {}, total allocated: {}/{}",
								device.getDeviceId(), device.getComputePower(), allocatedPower, needCount);
					}
				}
			}catch(Exception e) {
				log.warn("[videoHandleLog] [stream] steals exception:{}",e.getMessage());
				
				try {
					video.stop();
				}catch(Exception ee) {
					ee.printStackTrace();
				}
				
				currentStreamMap.remove(device.getDeviceId());
			}
		}
		
		return changed;
	}
	
	private void returns(long returnCount, List<VideoStreamInfra> devices, long currentTimeMillis) throws Exception {
		log.info("[videoHandleLog] [stream] returning streams [" + returnCount +"].");
		Map<String, VideoStreamInfra> deviceMap = devices.stream().collect(Collectors.toMap(VideoStreamInfra::getDeviceId, item -> item));
		
		// 按算力从大到小排序，这样优先释放算力大的设备
		List<Map.Entry<String, VideoStream>> streamsToCheck = new ArrayList<>(currentStreamMap.entrySet());
		if (Utils.instance.cameraDispatch) {
			streamsToCheck.sort(Comparator.<Map.Entry<String, VideoStream>>comparingDouble(
				entry -> -entry.getValue().getDevice().getComputePower()) // 负号使其从大到小排序
			);
		}
		
		float powerToReturn = returnCount;
		float returnedPower = 0;
		
		for (Entry<String, VideoStream> entry : streamsToCheck) {
			if (Utils.instance.cameraDispatch && returnedPower >= powerToReturn) {
				break;
			}
			
			VideoStream stream = entry.getValue();
			String deviceId = entry.getKey();
			
			// 确保该流与我们正在处理的设备类型匹配（seen或nonseen）
			boolean isSeenType = stream instanceof VideoStreamSeen;
			boolean isMatchingType = (devices.stream().anyMatch(d -> judgeRtmpOn(d)) && isSeenType) ||
									 (devices.stream().anyMatch(d -> !judgeRtmpOn(d)) && !isSeenType);
									 
			if (!isMatchingType) {
				continue;
			}
			
			if (Utils.instance.cameraDispatch) {
				float devicePower = stream.getDevice().getComputePower();
				
				// 如果释放这个设备后会导致过量释放，且差距太大，跳过它
				if (returnedPower + devicePower > powerToReturn * 1.2) {
					continue;
				}
			}
			
			// 停止流并释放资源
			stream.getDevice().setUpdateTs(null);
			log.info("[videoHandleLog] [stream] returns device {} with compute power {}", 
					  deviceId, Utils.instance.cameraDispatch ? stream.getDevice().getComputePower() : 1.0f);
			stream.stop();
			
			if (Utils.instance.cameraDispatch) {
				returnedPower += stream.getDevice().getComputePower();
			} else {
				returnedPower += 1.0f;
			}
			currentStreamMap.remove(deviceId);
			
			VideoStreamInfra device = deviceMap.get(deviceId);
			if (device != null) {
				deviceMapper.updateSeed(VideoStreamInfra.initSeed, new Date(currentTimeMillis), device.getDeviceId(), device.getSeed());
			}
		}
		
		log.info("[videoHandleLog] [stream] returned power: {}/{}", returnedPower, powerToReturn);
	}
	
	private VideoStream checkRtmpSwitch(VideoStreamInfra deviceInMem, VideoStreamInfra deviceIndb, Entry<String, VideoStream> streamEntry, Map<String, VideoChecked> frameFilterMap) {
		VideoChecked checked = frameFilterMap.get(deviceIndb.getDeviceId());
		
		if(judgeRtmpOn(deviceInMem) && !judgeRtmpOn(deviceIndb)) {
			VideoStreamSeen seen = (VideoStreamSeen)streamEntry.getValue();
			log.info("[videoHandleLog] [stream] seen.stop device{}", deviceIndb.getDeviceId());
			seen.stop();
			
			VideoStreamNonSeen nonSeen = new VideoStreamNonSeen(deviceIndb, seen.isUseDeviceDecoder(), checked,
					device_status_event_output, senseyes_raw_event_output);
			nonSeen.start();
			
			streamEntry.setValue(nonSeen);
		}else if(!judgeRtmpOn(deviceInMem) && judgeRtmpOn(deviceIndb)) {
			VideoStreamNonSeen nonSeen = (VideoStreamNonSeen)streamEntry.getValue();
			log.info("[videoHandleLog] [stream] nonSeen.stop device{}", deviceIndb.getDeviceId());
			nonSeen.stop();
			
			VideoStreamSeen seen = new VideoStreamSeen(deviceIndb, nonSeen.isUseDeviceDecoder(), checked);
			seen.start();

			streamEntry.setValue(seen);
		}
		
		VideoStream stream = streamEntry.getValue();
		stream.setDevice(deviceIndb);
		
		return stream;
	}
	
	public Map<String, Object> getOngoingStream() {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("seen", currentStreamMap.values().stream().filter(stream -> stream instanceof VideoStreamSeen).collect(Collectors.toList()));
		result.put("nonseen", currentStreamMap.values().stream().filter(stream -> stream instanceof VideoStreamNonSeen).collect(Collectors.toList()));
		return result;
	}

	private boolean judgeRtmpOn(VideoStreamInfra device) {
		return StringUtils.isNotBlank(device.getRtmpDestination())&& (Boolean.valueOf(device.getRtmpOn()) || "0".equals(device.getRtmpOn()));
	}
}
