package com.sensetime.intersense.cognitivesvc.stream.video.draw;

import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.IplImage;
import org.bytedeco.opencv.opencv_core.Mat;
import org.opencv.core.Rect;

public class DrawRect implements DrawItem {
	private Rect rect;
	private CvScalar color;
	private int thick = 2;
	
	public DrawRect(Rect rect, CvScalar color) {
		this.rect = rect;
		this.color = color;
	}
	
	public DrawRect(Rect rect, double r, double g, double b) {
		this.rect = rect;
		this.color = opencv_core.CV_RGB(r, g, b);
	}
	
	public DrawRect(Rect rect, CvScalar color, int thick) {
		this.rect = rect;
		this.color = color;
		this.thick = thick;
	}

	@Override
	public boolean drawTo(Frame frame, IplImage ipl_frame, Mat mat_frame) {
		try {
			opencv_imgproc.cvRectangle(ipl_frame, opencv_core.cvPoint(rect.x, rect.y), opencv_core.cvPoint(rect.x + rect.width, rect.y + rect.height), color, thick, 8 ,0);
		}catch(Exception e) {
			return false;
		}
		
		return true;
	}
}
