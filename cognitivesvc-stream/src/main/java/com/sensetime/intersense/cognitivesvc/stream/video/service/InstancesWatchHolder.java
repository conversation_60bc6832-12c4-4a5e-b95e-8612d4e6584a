package com.sensetime.intersense.cognitivesvc.stream.video.service;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

@FunctionalInterface
public interface InstancesWatchHolder {
	
	public Pair<Boolean, Integer> getSwitchWatchHolder(Map<String, String> modelsGroup);
	
}
