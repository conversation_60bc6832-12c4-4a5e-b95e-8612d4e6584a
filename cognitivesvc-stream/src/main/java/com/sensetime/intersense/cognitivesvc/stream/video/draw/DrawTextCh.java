package com.sensetime.intersense.cognitivesvc.stream.video.draw;

import java.awt.Color;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.IplImage;
import org.bytedeco.opencv.opencv_core.Mat;
import org.opencv.core.Rect;

import com.sensetime.intersense.cognitivesvc.stream.video.service.FontService;
import com.sensetime.intersense.cognitivesvc.stream.video.service.FontService.TextMat;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DrawTextCh implements DrawItem {
	public static FontService fontService;
	
	private String text;
	private Color color;
	private Rect rect;
	
	public DrawTextCh(String text, CvScalar color, Rect rect) {
		this.text = text;
		this.color = new Color((int)color.red(), (int)color.green(), (int)color.blue());
		this.rect = rect;
	}

	@Override
	public boolean drawTo(Frame frame, IplImage ipl_frame, Mat mat_frame) {
		if(fontService == null)
			return true;
		
		int x = 0, y = 0, width = 0, height = 0;
		try {
			Future<TextMat> future = fontService.getFontMat(text, color);
			TextMat fontMat = future.get(2, TimeUnit.MILLISECONDS);
			
			x = Math.max(rect.x + rect.width / 2 - fontMat.textWidth / 2, 0);
			y = Math.max(rect.y - fontMat.textHeight, 0);
			width = Math.min(mat_frame.cols() - x, fontMat.text.cols());
			height = Math.min(mat_frame.rows() - y, fontMat.text.rows());
			
			Mat roi = mat_frame.apply(new org.bytedeco.opencv.opencv_core.Rect(x, y, width, height));
			if(fontMat.mask != null) 
				fontMat.text.copyTo(roi, fontMat.mask);
			else 
				fontMat.text.copyTo(roi);
			roi.close();
		}catch(TimeoutException ee) {
			
		}catch(Exception e) {
			log.warn("src.width = " + mat_frame.cols() + ", src.height = " + mat_frame.rows() + ", x = " + x + ", y = " + y + ", width = " + width + ", height = " + height ,e);
			return false;
		}
		
		return true;
	}
}
