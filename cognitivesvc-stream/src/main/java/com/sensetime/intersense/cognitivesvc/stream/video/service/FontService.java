package com.sensetime.intersense.cognitivesvc.stream.video.service;

import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.javacv.OpenCVFrameConverter.ToMat;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.opencv_core.Mat;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class FontService{
	
	private final Pair<TextMat, Long> empty = new MutablePair<TextMat, Long>();
	
	private final ConcurrentHashMap<String, Pair<TextMat, Long>> textMatMap = new ConcurrentHashMap<String, Pair<TextMat, Long>>();
	
	@Async("cogThreadPool")
	public Future<TextMat> getFontMat(String text, Color color){
		String key = text + "_" + color.getRGB();
		TextMat textMat = textMatMap.getOrDefault(key, empty).getLeft();
		if(textMat != null)
			return new AsyncResult<TextMat>(textMat);
					
		BufferedImage bi = new BufferedImage(1, 1, BufferedImage.TYPE_3BYTE_BGR);
		Font font = new Font("微软雅黑", Font.BOLD, 20);
		FontMetrics fm = bi.getGraphics().getFontMetrics(font);
		int width = fm.stringWidth(text);
		int height = fm.getHeight();
		
		BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_3BYTE_BGR);
		Graphics2D graphics = bufferedImage.createGraphics();
		
		ToMat toMat = new OpenCVFrameConverter.ToMat();
		Java2DFrameConverter convert = new Java2DFrameConverter();
		
		if(Color.BLACK.equals(color)) {
			graphics.setBackground(Color.BLACK);
			graphics.clearRect(0, 0, width, height);
			graphics.setColor(Color.WHITE);
			graphics.setFont(font);
			
			graphics.drawString(text, 0, height / 4 * 3);
			graphics.dispose();
			
			org.bytedeco.javacv.Frame frame = convert.getFrame(bufferedImage);
			Mat result = toMat.convert(frame);
			
			textMat = new TextMat(new Mat(height, width, opencv_core.CV_8UC3), result, width, height);
		}else {
			graphics.setBackground(Color.BLACK);
			graphics.clearRect(0, 0, width, height);
			graphics.setColor(color);
			graphics.setFont(font);
			
			graphics.drawString(text, 0, height / 4 * 3);
			graphics.dispose();
			
			Mat result = toMat.convert(convert.getFrame(bufferedImage));
			
			graphics.setColor(Color.WHITE);
			graphics.drawString(text, 0, height / 4 * 3);
			graphics.dispose();
			
			Mat mask = toMat.convert(convert.getFrame(bufferedImage));
			textMat = new TextMat(result, mask, width, height);
		}
		
		textMatMap.put(key, new MutablePair<TextMat, Long>(textMat, System.currentTimeMillis()));
		
		return new AsyncResult<TextMat>(textMat);
	}
	
	@Scheduled(fixedDelay = 120 * 1000)
	public void cleanCache() {
		Iterator<Pair<TextMat, Long>> its = textMatMap.values().iterator();
		while(its.hasNext()) {
			Pair<TextMat, Long> pair = its.next();
			if(System.currentTimeMillis() - pair.getRight() >= 120 * 1000)
				its.remove();
		}
	}
	
	public static class TextMat implements AutoCloseable{
		public Mat text;
		public Mat mask;
		public int textWidth;
		public int textHeight;
		
		public TextMat(Mat text, Mat mask, int textWidth, int textHeight) {
			this.text = text;
			this.mask = mask;
			this.textWidth = textWidth;
			this.textHeight = textHeight;
		}
		
		@Override
		public void finalize() {
			try { close(); } catch (Exception e) {  }
		}

		@Override
		public void close() throws Exception {
			text.close();
			mask.close();
		}
	}
}
