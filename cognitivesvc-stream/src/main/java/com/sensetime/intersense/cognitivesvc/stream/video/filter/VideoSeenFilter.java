package com.sensetime.intersense.cognitivesvc.stream.video.filter;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.core.annotation.Order;

import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;

import lombok.Getter;
import lombok.Setter;

/**  可视化的视频流处理  每路视频独立一个filter
 * <AUTHOR>
 *
 */
public abstract class VideoSeenFilter{

	@Getter
	@Setter
	protected VideoStreamInfra device;

	/** 初始化上下文*/
	protected Map<String, Object> initContext = new HashMap<String, Object>();
	
	/** 执行上下文 */
	protected Map<String, Object> context = new HashMap<String, Object>();
	
	/**下一个filter */
	@Getter
	protected VideoSeenFilter next;

	/** 停止内部的线程标记位 */
	protected volatile boolean stoped;
	
	/**  是否初始化过 */
	protected volatile boolean inited;
	
	/**
	 * 执行这个filter
	 * @param gpu_frame
	 * @param cpu_frame
	 * @param device
	 */
	public final void doFilter(VideoFrame videoFrame, VideoStreamInfra device) {
		try {
			handle(videoFrame, device);
		}catch(Exception e) {
			e.printStackTrace();
		}
		
		if(next != null) {
			next.context.putAll(context);
			next.doFilter(videoFrame, device);
		}
		
		try {
			postHandle(videoFrame, device);
		}catch(Exception e) {
			e.printStackTrace();
		}
		context.clear();
	}
	
	/**
	 * 初始化
	 * @param device
	 */
	public final void doInitialize(VideoStreamInfra device) {
		if(inited)
			return ;
		
		try {
			initialize(device);
		}catch(Exception e) {
			throw new RuntimeException(e);
		}
		
		if(next != null) {
			next.initContext.putAll(initContext);
			try {
				next.doInitialize(device);
			}catch(Exception e) {
				destroy(device);
				initContext.clear();
				throw new RuntimeException(e);
			}
		}
		
		inited = true;
	}
	
	/**
	 * 销毁
	 * @param device
	 */
	public final void doDestroy(VideoStreamInfra device) {
		try {
			destroy(device);
		}catch(Exception e) {
			e.printStackTrace();
		}
		
		if(next != null)
			next.doDestroy(device);

		initContext.clear();
	}
	
	/**设置上下文 */
	public void addAttribute(String string, Object value) { context.put(string, value); }

	/** 处理这个gpuframe */
	protected void handle(VideoFrame videoFrame, VideoStreamInfra device) {};
	
	/** 后处理这个gpuframe */
	protected void postHandle(VideoFrame videoFrame, VideoStreamInfra device) {}
	
	/** 初始化 */
	protected void initialize(VideoStreamInfra device) {}
	
	/** 销毁 */
	protected void destroy(VideoStreamInfra device) {}

	
	/** 全部filter */
	public static final List<Class<? extends VideoSeenFilter>> existingFilter = Collections.synchronizedList(Lists.newArrayList());
	
	/** 获取有多少个filter */
	public static VideoSeenFilter newInstances(){
		List<VideoSeenFilter> result = VideoSeenFilter.existingFilter
				.stream()
				.sorted((l, r)->{
					Order lo = l.getDeclaredAnnotation(Order.class);
					Order ro = r.getDeclaredAnnotation(Order.class);
					return (lo == null ? 0 : lo.value()) - (ro == null ? 0 : ro.value());
				})
				.map(clazz -> {
					try {
						return (VideoSeenFilter) clazz.getDeclaredConstructor().newInstance();
					} catch (Exception e) {
						throw new RuntimeException(e);
					}
				})
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
	
		result.add(0, new RecordAndDestroySeenFilter());
			
		for(int index = 0 ; index < result.size() - 1; index ++) 
			result.get(index).next = result.get(index + 1);
		
		return result.get(0);
	}
}
