package com.sensetime.intersense.cognitivesvc.stream.video.service;

import java.util.List;
import java.util.Map;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;

import lombok.Builder;
import lombok.Getter;

@FunctionalInterface
public interface VideoFrameFilter{
	
	public VideoChecked check(VideoStreamInfra infra, Map<String, String> modelGroup);
	
	public default int extraRtspCount(VideoStreamInfra infra) { return 0; };
	 
	@Getter
	@Builder
	public static class VideoChecked{
		private boolean needDeviceFrame;
		private boolean needHostFrame;
		
		@Builder.Default
		private boolean canMultiplex = false;//此路流可以尝试多路复用
		
		public static VideoChecked merge(List<VideoChecked> checkeds) {
			VideoChecked result = VideoChecked.builder().build();
			
			for(VideoChecked checked : checkeds) {
				result.needHostFrame |= checked.needHostFrame;
				result.needDeviceFrame |= checked.needDeviceFrame;
				result.canMultiplex &= checked.canMultiplex;
			}
			
			return result;
		}
		
		public static final VideoChecked NOT = VideoChecked.builder().needHostFrame(false).needDeviceFrame(false).build();
		public static final VideoChecked CPU = VideoChecked.builder().needHostFrame(true).needDeviceFrame(false).build();
		public static final VideoChecked GPU = VideoChecked.builder().needHostFrame(false).needDeviceFrame(true).build();
		public static final VideoChecked ALL = VideoChecked.builder().needHostFrame(true).needDeviceFrame(true).build();
		
		public static final VideoChecked GPUMultiplex = VideoChecked.builder().needHostFrame(false).needDeviceFrame(true).canMultiplex(true).build();
	}
	
}
