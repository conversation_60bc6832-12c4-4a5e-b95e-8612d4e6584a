package com.sensetime.intersense.cognitivesvc.stream.video.draw;

import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.opencv_core.IplImage;
import org.bytedeco.opencv.opencv_core.Mat;
import org.opencv.core.Rect;

public class DrawMat implements DrawItem {
	private Rect rect;
	private Mat mat;
	
	public DrawMat(Rect rect, Mat mat) {
		this.rect = rect;
		this.mat = mat;
	}

	@Override
	public boolean drawTo(Frame frame, IplImage ipl_frame, Mat mat_frame) {
		try {
			Mat ROI = mat_frame.apply(new org.bytedeco.opencv.opencv_core.Rect(rect.x + rect.width / 2 - mat.cols() / 2, rect.y + rect.height, mat.cols(), mat.rows()));
			mat.copyTo(ROI);
		}catch(Exception e) {
			return false;
		}
		
		return true;
	}
}
