package com.sensetime.intersense.cognitivesvc.stream.video;

import java.lang.Thread.State;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.DeviceStatusEntity;
import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import com.sensetime.intersense.cognitivesvc.stream.configuration.DeviceStatusEventOutput;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.bytedeco.librealsense.device;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.service.VideoFrameFilter.VideoChecked;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;

/** 
	不需要显示在rtmp上面的视频流，所以共享tracker，占用显存少 
 */
public class VideoStreamNonSeen extends VideoStream{
	private static final Logger log = LoggerFactory.getLogger(VideoStreamNonSeen.class);

	private static final AtomicInteger streamingCount = new AtomicInteger();

	@Setter
	private BaseOutput device_status_event_output;

	@Setter
	private BaseOutput senseyes_raw_event_output;

	private static final LinkedBlockingQueue<BatchItem> frameQueue = new LinkedBlockingQueue<BatchItem>();
	
	private static final ConcurrentHashMap<String, Queue<BatchItem>> frameMapQueue = new ConcurrentHashMap<String, Queue<BatchItem>>();
	
	private static final VideoNonSeenFilter filter = VideoNonSeenFilter.newInstances();

	private CountDownLatch latch = new CountDownLatch(1);

	@Getter @Setter
	public static volatile   ConcurrentHashMap<String, Monitor> MonitorMap = new ConcurrentHashMap<String, Monitor>();


	public volatile long decodeFrameCount;
	public volatile long handledFrameCount;
	public volatile long lostFrameCount;
	
	private boolean needHostFrame = true;
	private boolean needDeviceFrame = true;
	
	public VideoStreamNonSeen(VideoStreamInfra device, boolean useGpuDecoder, VideoChecked checked,
							  BaseOutput device_status_event_output, BaseOutput senseyes_raw_event_output) {
		super(device);
		this.useDeviceDecoder = useGpuDecoder && Initializer.isGpu();
		this.needHostFrame = checked.isNeedHostFrame();
		this.needDeviceFrame = checked.isNeedDeviceFrame();
		this.useMultiplexDecoder = checked.isCanMultiplex();
		this.device_status_event_output = device_status_event_output;
		this.senseyes_raw_event_output = senseyes_raw_event_output;

		MonitorMap.put(device.getDeviceId(), new Monitor());
	}
	
	private Thread daemonThread = new Thread(Utils.cogGroup, new Runnable() {
		private long lastExpire = 0;
		
		@Override
		public void run() {
			Initializer.bindDeviceOrNot();
			
			try{
				long now = System.currentTimeMillis();


				boolean keepRunning = true;
				while(!stoped && keepRunning && frameIndex < maxLimitFrameIndex) {
					boolean logged = Utils.instance.watchFrameTiktokLevel == -797;

					keepRunning = handleNextFrame();

					if(logged){
						if(frameIndex % 100 == 0) {
							log.info("[VideoHandleLog] VideoStreamNonSeen whole [" + device.getDeviceId() + "] frameindex[" + frameIndex + "] decodeFrameCount[" + decodeFrameCount +"]"  + "handledFrameCount[" + handledFrameCount + "] lostFrameCount	[" + lostFrameCount + "] cost[" + (System.currentTimeMillis() - now) + "]");
							now = System.currentTimeMillis();
						}
					}
				}


			}finally {
				latch.countDown();
				close();
				videoStatus = VideoStatus.EOF;
			}
		}
		
		private final boolean handleNextFrame() {

			long now = System.currentTimeMillis();
			boolean handleCurrentFrame = frameIndex % (Math.abs(device.getFrameSkip()) + 1) == 0;

			VideoGrabFrameStatusOutput oldVideoStatus = videoStatusOutput;

			VideoFrame videoFrame = null;
			try {
				  videoFrame = grabberNextFrame(needHostFrame & handleCurrentFrame, needDeviceFrame & handleCurrentFrame);
				//				// todo 流状态更新为OK
				videoStatusOutput = VideoGrabFrameStatusOutput.OK;
				videoStatusDetail = "";
				lastErrorCheckTime = null;


				MonitorMap.get(device.getDeviceId()).setVideoStatus(videoStatusOutput);
				MonitorMap.get(device.getDeviceId()).setVideoStatusDetail(videoStatusDetail);
				MonitorMap.get(device.getDeviceId()).setLastErrorCheckTime(lastErrorCheckTime);

				if(!videoStatusOutput.equals(oldVideoStatus)){
					List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
					DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
							.did(device.getDeviceId())
							.sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
							.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
							.processSts("0")
							.build();
					deviceStatusesList.add(deviceStatus);
					DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
							.eventAction("DeviceStatusSync")
							.eventName("cognitive")
							.data(deviceStatusesList)
							.build();
					try{
						device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
						log.info("device_status_event_output send output {}",deviceStatusEntity);
					}catch (Exception ex){
						log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
					}
				}

				decodeFrameCount ++;

                //人脸精度测试可以打开
				if (!isStreamRemote(device.getRtspSource()) && Utils.instance.faceIsQueue) {
					long sleep = Math.max(3000 / device.getVideoRate() - lastExpire, 80);
					Thread.sleep(sleep);
					//log.info("frameIndxConst:{},sleep:{}", frameIndex, sleep);
				}else {
					if (!isStreamRemote(device.getRtspSource())) {
						long sleep = 1000 / device.getVideoRate() - lastExpire;
						if (sleep > 0)
							Thread.sleep(sleep);
					}
				}
				if(videoFrame.getFrame() == null){
					handleCurrentFrame = false;
				}

				if(handleCurrentFrame) {
					boolean ret = true;
					videoFrame.setFrameDecodeCost(System.currentTimeMillis() - now);
					BatchItem batchItem = new BatchItem(videoFrame, device);

					if(Utils.instance.faceIsQueue && !isStreamEndless(device.getRtspSource())) {
						VideoFrame[] videoFrames = new VideoFrame[1];
						VideoStreamInfra[] devices = new VideoStreamInfra[1];
						try {
							enqueueSemaphores[0].acquireUninterruptibly();
							for (int index = 0; index < 1; index++) {

								videoFrames[index] = videoFrame;
								devices[index] = device;
							}
							enqueueSemaphores[1].acquireUninterruptibly();
							enqueueSemaphores[0].release();

							filter.doFilterOne(videoFrames, devices);
						} catch (Exception e) {
							e.printStackTrace();
						} finally {

							enqueueSemaphores[2].acquireUninterruptibly();

							enqueueSemaphores[1].release();
						}

						try {
							filter.doFilterTwo(videoFrames, devices);
						} catch (Exception e) {
							e.printStackTrace();
						} finally {
							enqueueSemaphores[3].acquireUninterruptibly();
							enqueueSemaphores[2].release();
						}

						try {
							filter.doFilterThree(videoFrames, devices);
						} catch (Exception e) {
							e.printStackTrace();
						} finally {
							enqueueSemaphores[3].release();
						}

					}else {

						if (Utils.instance.nonSeenQueueType == 0)
							ret = frameMapQueue.get(device.getDeviceId()).offer(batchItem);
						else {
							ret = frameQueue.offer(batchItem, 15, TimeUnit.MILLISECONDS);
						}

						if (!ret) {
							videoFrame.close();
							lostFrameCount++;
							MonitorMap.get(device.getDeviceId()).getAndIncrementUnHandled();
							log.error("[VideoHandleLog] MonitorMap_noSeen_unhandle deviceId {},ret: {} offer to handle queue fail", device.getDeviceId(), ret);
						} else {
							handledFrameCount++;
							MonitorMap.get(device.getDeviceId()).getAndIncrementHandled();
						}

					}
				}else{
					videoFrame.close();
				}
			}catch(Throwable e) {
				if(videoFrame != null) {
					videoFrame.close();
				}
				MonitorMap.get(device.getDeviceId()).getAndIncrementUnHandled();
				VideoStreamNonSeen.this.frameIndex ++;
				log.warn("[VideoHandleLog] grabberNextFrameErr deviceId {},e {},errorTime:{}", VideoStreamNonSeen.this.getDevice().getDeviceId(), e.getMessage(),errorTime);
				// todo errorTime达到多少的时候判断流是否出问题 更新流状态
				if(errorEofTime >= Utils.instance.videoStatusErrorTime){
					log.warn("[VideoStatusChange] grabberNextFrameErr change videoStatus to error, msg: {},deviceId:{},{}", e.getMessage(), device.getDeviceId(),errorTime);
					videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
					videoStatusDetail = e.getMessage();
					lastErrorCheckTime = new Date();
				}

				if(errorAgainTime >= Utils.instance.videoStatusErrorKestrelAgainTime){
					log.warn("[VideoStatusChange] grabberNextFrameErr change videoStatus to error, msg: {},deviceId:{},{}", e.getMessage(), device.getDeviceId(),errorAgainTime);
					videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
					videoStatusDetail = e.getMessage();
					lastErrorCheckTime = new Date();
				}

				if(errorInternalTime >= Utils.instance.videoStatusErrorInternalTime){
					log.warn("[VideoStatusChange] grabberNextFrameErr change videoStatus to error, msg: {},deviceId:{},{}", e.getMessage(), device.getDeviceId(),errorInternalTime);
					videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
					videoStatusDetail = e.getMessage();
					lastErrorCheckTime = new Date();
				}


				MonitorMap.get(device.getDeviceId()).setVideoStatus(videoStatusOutput);
				MonitorMap.get(device.getDeviceId()).setVideoStatusDetail(videoStatusDetail);
				MonitorMap.get(device.getDeviceId()).setLastErrorCheckTime(lastErrorCheckTime);

				if(!videoStatusOutput.equals(oldVideoStatus)){
					List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
					DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
							.did(device.getDeviceId())
							.sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
							.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
							.processSts("0")
							.build();
					deviceStatusesList.add(deviceStatus);
					DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
							.eventAction("DeviceStatusSync")
							.eventName("cognitive")
							.data(deviceStatusesList)
							.build();
					try{
						device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
						log.info("device_status_event_output send output {}",deviceStatusEntity);
					}catch (Exception ex){
						log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
					}
				}

				if("KPLUGIN_E_EOF".equals(e.getMessage())){
					try { Thread.sleep(3000); } catch (InterruptedException ex) { }
				}
				if("KPLUGIN_E_INTERNAL".equals(e.getMessage())){
					try { Thread.sleep(1000); } catch (InterruptedException ex) { }
				}
				if("KPLUGIN_E_UNKNOWN".equals(e.getMessage())){
					try { Thread.sleep(1000); } catch (InterruptedException ex) { }
				}
				if("KPLUGIN_E_AGAIN".equals(e.getMessage())){
					try { Thread.sleep(1000); } catch (InterruptedException ex) { }
				}

				if("KPLUGIN_E_EOF".equals(e.getMessage())) {
					if(!isStreamEndless(device.getRtspSource())) {
						//sendEOFMsg();
						try { Thread.sleep(50000); } catch (InterruptedException ex) { }
						VideoStreamNonSeen.this.videoStatus = VideoStatus.EOF;
						return false;
					}else {						
						try {
							close();
							try {
								log.info("[VideoHandleLog] video reopen wait time {} s", Utils.instance.streamReopenSleepTime );
								Thread.sleep(Utils.instance.streamReopenSleepTime * 1000);
							} catch (InterruptedException e2) {
							}
							open();
							
							VideoStreamNonSeen.this.frameIndex = 0;
							log.info("[VideoHandleLog] video succeed to reopen [" + device.getDeviceId() + "].");
						} catch (Throwable e1) {
							try { Thread.sleep(3000); } catch (InterruptedException e2) { }
							log.error("[VideoHandleLog] video fail to reopen [" + device.getDeviceId() + "].");
						}
					}
				}else if(StringUtils.startsWith(e.getMessage(), "SOURCE_CHANGED") || errorTime % Utils.instance.faceStreamReopenErrorTime == 0){
					try {
						close();
						try {
							log.info("[VideoHandleLog] video reopen wait time {} s", Utils.instance.streamReopenSleepTime );
							Thread.sleep(Utils.instance.streamReopenSleepTime * 1000);
						} catch (InterruptedException e2) {
						}
						open();
						
						VideoStreamNonSeen.this.frameIndex = 0;
						log.info("[VideoHandleLog] video succeed to reopen [" + device.getDeviceId() + "].");
					} catch (Throwable e1) {
						try { Thread.sleep(3000); } catch (InterruptedException e2) { }
						log.error("[VideoHandleLog] video fail to reopen [" + device.getDeviceId() + "].");
					}
				}
			}finally {
				lastExpire = System.currentTimeMillis() - now;
				//todo MonitorMap.get(device.getDeviceId()).setVideoStatus(videoStatus);
			}
			return true;
		}
	});

	private void sendEOFMsg() {

		try {
			String message =
					"{" +
							"	\"eventName\": \"" + "sendEOFMsg" + "\"," +
							"	\"eventAction\": \"" + "pullFrameEof" + "\"," +
							"	\"data\": {" +
							"		\"camera\": {" +
							"			\"referId\": \"" + device.getDeviceId() + "\"," +
							"			\"type\": 1," +
							"			\"tag\": \"" + device.getDeviceTag() + "\"" +
							"		}," +
							"		\"capturedTime\": \"" + System.currentTimeMillis() + "\"" +
							"		} }";

			senseyes_raw_event_output.send(MessageBuilder.withPayload(message).setHeader(KafkaHeaders.KEY, device.getDeviceId().getBytes()).build());
			log.info("senseyes_raw_event_output_eof:{}", device.getDeviceId());
		} catch (Exception ex) {
			log.warn("sendEOFMsg senseye send status failed:{},{}", ex.getMessage(),device.getDeviceId());
		}
	}

	@Override 
	public synchronized void start() {
		try{
			if(daemonThread.getState() != State.NEW)
				return ;

			log.info("[VideoHandleLog] NonSeen video is starting deviceId[" + device.getDeviceId() + "].");

			Initializer.bindDeviceOrNot();

			frameMapQueue.putIfAbsent(device.getDeviceId(), new LinkedBlockingQueue<BatchItem>(Utils.instance.nonSeenReusedFrameCount));

			open();

			streamingCount.incrementAndGet();
			filter.doInitialize(device, false);

			daemonThread.setName("[" + device.getDeviceId() + "] nonSeen-daemonThread");
			daemonThread.setDaemon(true);
			daemonThread.start();
			// 打开之后才会发送ok, 否则不发。
			videoStatusOutput = VideoGrabFrameStatusOutput.OK;
			List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
			DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
					.did(device.getDeviceId())
					.sts("0")
					.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
					.processSts("0")
					.build();
			deviceStatusesList.add(deviceStatus);
			DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
					.eventAction("DeviceStatusSync")
					.eventName("cognitive")
					.data(deviceStatusesList)
					.build();
			try{
				device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
				log.info("device_status_event_output send output {}",deviceStatusEntity);
			}catch (Exception ex){
				log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
			}


			log.info("[VideoHandleLog] NonSeen video started deviceId[" + device.getDeviceId() + "].");
		}catch (Exception e){
			videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
			List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
			DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
					.did(device.getDeviceId())
					.sts("1")
					.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
					.processSts("0")
					.build();
			deviceStatusesList.add(deviceStatus);
			DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
					.eventAction("DeviceStatusSync")
					.eventName("cognitive")
					.data(deviceStatusesList)
					.build();
			try{
				device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
				log.info("device_status_event_output send output {}",deviceStatusEntity);
			}catch (Exception ex){
				log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
			}
			throw e;
		}

	}
	@Override
	public synchronized void changeStart() {

		//Initializer.bindDeviceOrNot();

		filter.doReInitialize(device, true);

		log.info("[VideoHandleLog] changeStart NonSeen video started deviceId[" + device.getDeviceId() + "].");

		List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
		DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
				.did(device.getDeviceId())
				.sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
				.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
				.processSts("0")
				.build();
		deviceStatusesList.add(deviceStatus);
		DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
				.eventAction("DeviceStatusSync")
				.eventName("cognitive")
				.data(deviceStatusesList)
				.build();
		try{
			device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
			log.info("device_status_event_output send output {}",deviceStatusEntity);
		}catch (Exception ex){
			log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
		}

	}
	
	@Override 
	public synchronized void stop() {
		try{
			log.info("[VideoHandleLog] NonSeen video is closing deviceId[" + device.getDeviceId() + "].");

			Initializer.bindDeviceOrNot();

			stoped = true;

			try {
				if (!isStreamEndless(device.getRtspSource())) {
					sendEOFMsg();
				}
				boolean ret = latch.await(30, TimeUnit.SECONDS);
				if(!ret) {
					throw new RuntimeException("closing non seen steam[" + device.getDeviceId() + "] cost more than 30 second, please check or ingore.");
				}
			} catch (InterruptedException e) {

			}finally {
				filter.doDestroy(device);
				streamingCount.decrementAndGet();

				Queue<BatchItem> removings = frameMapQueue.remove(device.getDeviceId());
				if(CollectionUtils.isNotEmpty(removings))
					for(BatchItem removing : removings)
						removing.close();
			}


			try { Thread.sleep(1000); } catch (InterruptedException e) { }

			close();
			List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
			DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
					.did(device.getDeviceId())
					.sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
					.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
					.processSts("1")
					.build();
			deviceStatusesList.add(deviceStatus);
			DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
					.eventAction("DeviceStatusSync")
					.eventName("cognitive")
					.data(deviceStatusesList)
					.build();
			try{
				device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
				log.info("device_status_event_output send output {}",deviceStatusEntity);
			}catch (Exception ex){
				log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
			}

		}catch (Exception e){

			videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
			List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
			DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
					.did(device.getDeviceId())
					.sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
					.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
					.processSts("1")
					.build();
			deviceStatusesList.add(deviceStatus);
			DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
					.eventAction("DeviceStatusSync")
					.eventName("cognitive")
					.data(deviceStatusesList)
					.build();
			try{
				device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
				log.info("device_status_event_output send output {}",deviceStatusEntity);
			}catch (Exception ex){
				log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
			}
			if (!isStreamEndless(device.getRtspSource()) && !stoped) {
				sendEOFMsg();
			}
			throw e;
		}
	}
	
	private static final Thread[] enqueueThread = new Thread[4];
	
	private static final Semaphore[] enqueueSemaphores = new Semaphore[4];
	
	private static final Runnable nonSeenRunner = () -> {
		Initializer.bindDeviceOrNot();
		
		while (true) {
			List<BatchItem> handlingList;
			
			try {
				enqueueSemaphores[0].acquireUninterruptibly();

				if(Utils.instance.nonSeenQueueType == 0)			
					handlingList = Utils.drainFromMapQueue(frameMapQueue, 4, 15);
				else 
					handlingList = Utils.drainFromQueue(frameQueue, Initializer.batchSize, 4, 5);
			}catch(Exception e) {
				handlingList = List.of();
				e.printStackTrace();
			}finally {
				enqueueSemaphores[1].acquireUninterruptibly();
				enqueueSemaphores[0].release();
			}

//			if(handlingList.size() <= 0) {
//				enqueueSemaphores[1].release();
//				try { Thread.sleep(20); } catch (InterruptedException e) { }
//				continue;
//			}

			boolean logged = Utils.instance.watchFrameTiktokLevel == 654 || Utils.instance.printLog;

			long now = 0;
            if(logged)
            	now = System.currentTimeMillis();

			VideoFrame[] videoFrames = new VideoFrame[handlingList.size()];
			VideoStreamInfra[] devices = new VideoStreamInfra[handlingList.size()];

			try {
				if(handlingList.size() <= 0) {
					try { Thread.sleep(20); } catch (InterruptedException e) { }
					continue;
				}

				for(int index = 0; index < handlingList.size(); index ++) {
					BatchItem item = handlingList.get(index);
					videoFrames[index] = item.getVideoFrame();
					devices[index] = item.getDeviceInfra();
				}

				filter.doFilterOne(videoFrames, devices);
			}catch(Exception e) {
				e.printStackTrace();
			}finally {
				if(handlingList.size() > 0)
					enqueueSemaphores[2].acquireUninterruptibly();

				enqueueSemaphores[1].release();
			}

			try {
				filter.doFilterTwo(videoFrames, devices);
			}catch(Exception e) {
				e.printStackTrace();
			}finally {
				enqueueSemaphores[3].acquireUninterruptibly();
				enqueueSemaphores[2].release();
			}

			try {
				filter.doFilterThree(videoFrames, devices);
			}catch(Exception e) {
				e.printStackTrace();
			}finally {
				enqueueSemaphores[3].release();
			}


			for(int index = 0; index < handlingList.size(); index ++) {
				BatchItem item = handlingList.get(index);
				videoFrames[index] = item.getVideoFrame();
				videoFrames[index].close();
			}
//			for (VideoStreamInfra device : devices)
//				if(MonitorMap.get(device.getDeviceId()) !=null)
//			       MonitorMap.get(device.getDeviceId()).getAndIncrementSended();
			
			if(logged) {
				int queueSize = frameMapQueue.values().stream().map(q -> q.size()).reduce(Integer::sum).get();
				log.info("[VideoHandleLog] NonSeen doFilter [" + handlingList.size() + "] cost [" + (System.currentTimeMillis() - now) + "] queueSize[" + queueSize + "] stream_num[" + streamingCount.get() + "]");
			}
		}
	};

	static {
		for(int index = 0; index < enqueueThread.length; index ++) {
			enqueueThread[index] = new Thread(nonSeenRunner);
			enqueueThread[index].setName("NonSeenRunner[" + index + "]");
			enqueueThread[index].setDaemon(true);
			enqueueThread[index].setPriority(Thread.MAX_PRIORITY);
			enqueueThread[index].start();
		}
		
		for(int index = 0; index < 4; index ++)
			enqueueSemaphores[index] = new Semaphore(1);
	}
	
	@Getter
	@AllArgsConstructor
	private static class BatchItem implements AutoCloseable{
		private VideoFrame       videoFrame;
		private VideoStreamInfra deviceInfra;

		@Override
		public void close(){
			if(videoFrame != null)
				videoFrame.close();
		}
	}
	public static class Monitor{
		public ConcurrentLinkedDeque<AtomicLong> expired;

		public ConcurrentLinkedDeque<AtomicInteger> handled;
		public ConcurrentLinkedDeque<AtomicInteger> unhandled;
		public ConcurrentLinkedDeque<AtomicInteger> sended;
		public AtomicInteger handledTotal;
		public AtomicInteger unhandledTotal;
		public AtomicInteger sendedTotal;
		public VideoGrabFrameStatusOutput videoStatus;
		public AtomicInteger unOfferTotal;

		public String getVideoStatusDetail() {
			return videoStatusDetail;
		}

		public void setVideoStatusDetail(String videoStatusDetail) {
			this.videoStatusDetail = videoStatusDetail;
		}

		public String videoStatusDetail;

		public Date getLastErrorCheckTime() {
			return lastErrorCheckTime;
		}

		public void setLastErrorCheckTime(Date lastErrorCheckTime) {
			this.lastErrorCheckTime = lastErrorCheckTime;
		}

		public Date lastErrorCheckTime;



		public Monitor(){
			handled   = new ConcurrentLinkedDeque<AtomicInteger>();
			unhandled = new ConcurrentLinkedDeque<AtomicInteger>();
			expired   = new ConcurrentLinkedDeque<AtomicLong>();
			sended   = new ConcurrentLinkedDeque<AtomicInteger>();
			videoStatus = VideoGrabFrameStatusOutput.OK;

			for(int index = 0; index < 12; index ++) {
				handled.offer(new AtomicInteger());
				unhandled.offer(new AtomicInteger());
				sended.offer(new AtomicInteger());
				expired.offer(new AtomicLong());
			}

			handledTotal   = new AtomicInteger();
			unhandledTotal = new AtomicInteger();
			sendedTotal    = new AtomicInteger();
			unOfferTotal    = new AtomicInteger();
		}

		public void setVideoStatus(VideoGrabFrameStatusOutput videoStatus) {
			this.videoStatus = videoStatus;
		}

		public VideoGrabFrameStatusOutput getVideoStatus() {
			return this.videoStatus;
		}




		public int getAndIncrementHandled() {
			handledTotal.getAndIncrement();
			return handled.peekFirst().getAndIncrement();
		}


		public int getAndIncrementUnHandled() {
			unhandledTotal.getAndIncrement();
			return unhandled.peekFirst().getAndIncrement();
		}

		public int getAndIncrementSended() {
			sendedTotal.getAndIncrement();
			return sended.peekFirst().getAndIncrement();
		}
		public void getAndIncrementUnOffer() {
			unOfferTotal.getAndIncrement();
		}

		public int getHandledCount() {
			return handled.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
		}

		public int getUnhandledCount() {
			return unhandled.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
		}

		public int getSendedCount() {
			return sended.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
		}

		public long getExpired() {
			return expired.stream().map(AtomicLong::get).reduce(Long::sum).orElse(0L);
		}

		public void reset() {
			handled.pollLast();
			handled.offerFirst(new AtomicInteger());

			unhandled.pollLast();
			unhandled.offerFirst(new AtomicInteger());

			sended.pollLast();
			sended.offerFirst(new AtomicInteger());

			expired.pollLast();
			expired.offerFirst(new AtomicLong());
		}
	}
}
