package com.sensetime.intersense.cognitivesvc.stream.video.filter;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;

/**
         非渲染视频流的最后一步，将gpuframe都销毁
 */
public class DestroyNonSeenFilter extends VideoNonSeenFilter{

	@Override
	protected void postHandleStageThree(VideoFrame[] videoFrames, VideoStreamInfra[] devices) throws Exception {
		FrameUtils.batch_free_frame(videoFrames);
	}
}
