package com.sensetime.intersense.cognitivesvc.stream.controller;

import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.stream.video.service.RebalanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuppressWarnings("unused")
@RestController("streamTestProvider")
@RequestMapping(value = "/test", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "TestProvider", description = "Test")
@ConditionalOnProperty(value = "stream.enabled", havingValue = "true", matchIfMissing = true)
public class TestProvider extends BaseProvider {

    @Operation(summary = "get_ongoing_stream_device", method = "GET")
    @RequestMapping(value = "/get_ongoing_stream_device", method = RequestMethod.GET)
    public Object get_ongoing_stream_device() throws Exception {
        return rebalanceService.getOngoingStream();
    }

    @Autowired
    private RebalanceService rebalanceService;

    @Operation(summary = "test_bufferedImage", method = "GET")
    @RequestMapping(value = "/test_bufferedImage", method = RequestMethod.GET)
    public String test_bufferedImage(@RequestParam String source) throws Exception {
        BufferedImage bufferedImage = new BufferedImage(100, 100, 4);
        Graphics2D graphics = bufferedImage.createGraphics();
        graphics.setColor(Color.white);
        graphics.setFont(new Font(source, Font.BOLD, 128));
        graphics.drawString("我", 0, 0);
        graphics.dispose();
        return "OK";
    }

    @Value("${spring.application.name}")
    private String name;

    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private Broadcaster broadcaster;

    @Operation(summary = "get_lost_frame_count_forall", method = "GET")
    @RequestMapping(value = "/get_lost_frame_count_forall", method = RequestMethod.GET)
    public String[][] get_lost_frame_count_forall() throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String[][] result = discoveryClient.getInstances(name)
                .parallelStream()
                .map(instance -> {
                    try {
                        return new String[]{instance.getHost() + ":" + instance.getPort(), RestUtils.restTemplate4000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/test/get_lost_frame_count", String.class)};
                    } catch (Exception e) {
                        return new String[]{instance.getHost() + ":" + instance.getPort(), e.getMessage()};
                    }
                })
                .toArray(String[][]::new);

        return result;
    }

    @Operation(summary = "get_each_device_forall", method = "GET")
    @RequestMapping(value = "/get_each_device_forall", method = RequestMethod.GET)
    public String[][] get_each_device_forall() throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String[][] result = discoveryClient.getInstances(name)
                .parallelStream()
                .map(instance -> {
                    try {
                        return new String[]{instance.getHost() + ":" + instance.getPort(), RestUtils.restTemplate4000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/test/get_each_device", String.class)};
                    } catch (Exception e) {
                        return new String[]{instance.getHost() + ":" + instance.getPort(), e.getMessage()};
                    }
                })
                .toArray(String[][]::new);

        return result;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Operation(summary = "test_darwin", method = "GET")
    @RequestMapping(value = "/test_darwin", method = RequestMethod.GET)
    public Object test_darwin() throws Exception {
        List<Pair<ServiceInstance, List>> mappedRtsps = broadcaster.getForPair("", "/cognitive/darwin/getMappedRtsp", Map.of(), List.class, 2);
        return mappedRtsps.stream().map(pair -> new MutablePair(pair.getLeft().getInstanceId(), pair.getRight())).collect(Collectors.toList());
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Operation(summary = "test_stream_rate", method = "GET")
    @RequestMapping(value = "/test_stream_rate", method = RequestMethod.GET)
    public Object test_stream_rate(@RequestParam String source) throws Exception {

        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(source);
        grabber.setOption("rtsp_transport", "tcp");
        grabber.setOption("stimeout", "3000000");
        grabber.setOption("timeout", "2000000");
        try {
            grabber.start();
            Map map = new HashMap();
            map.put("getAudioBitrate", grabber.getAudioBitrate());
            map.put("getAudioFrameRate", grabber.getAudioFrameRate());
            map.put("getFrameRate", grabber.getFrameRate());
            map.put("getSampleRate", grabber.getSampleRate());
            map.put("getVideoBitrate", grabber.getVideoBitrate());
            map.put("getVideoFrameRate", grabber.getVideoFrameRate());
            return map;
        } catch (Exception e) {
            throw new RuntimeException("can not open stream [" + source + "].", e);
        } finally {
            try {
                grabber.close();
            } catch (Exception e) {
            }
        }
    }
}
