package com.sensetime.intersense.cognitivesvc.stream.video.draw;

import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.IplImage;
import org.bytedeco.opencv.opencv_core.Mat;

public class DrawPoint implements DrawItem {
	private int[] point;
	private CvScalar color;
	
	public DrawPoint(int[] point, CvScalar color) {
		this.point = point;
		this.color = color;
	}

	@Override
	public boolean drawTo(Frame frame, IplImage ipl_frame, Mat mat_frame) {
		try {
			opencv_imgproc.cvLine(ipl_frame, point, point, color);
		}catch(Exception e) {
			return false;
		}
		
		return true;
	}
}
