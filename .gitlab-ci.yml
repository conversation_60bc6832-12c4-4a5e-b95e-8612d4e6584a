# stages:
# - code_scan

# #job_code_scan1:
# #  image: registry.sensetime.com/security/codescan:latest
# #  stage: code_scan
# #  script:
# #  - /opt/code_scan/sonar_scan.sh
# #  tags:
# #  - k8s

# job_code_scan2:
#   image: registry.sensetime.com/security/codescan:latest
#   stage: code_scan
#   script:
#   - /opt/code_scan/sonar_full_scan.sh
#   tags:
#   - k8s
#   only:
#   - master
---
include:
  - project: 'DevOps/cicd-template'
    file: 'studio/studio-cog-template.yaml'


variables:
  ALIAS_NAME: "cognitivesvc-kestrel"
  PROJECT_VERSION: ""
  TEAM: "studio"



