package com.sensetime.intersense.cognitivesvc.xswitcher.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.netflix.appinfo.InstanceInfo;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QuerySwitcherResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.entities.InstanceDeviceInfoItem;
import com.sensetime.intersense.cognitivesvc.server.entities.InstanceModelInfoItem;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStatusRes;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.HostUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamNonSeen;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamSeen;
import com.sensetime.intersense.cognitivesvc.stream.video.service.RebalanceService;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.netflix.eureka.EurekaServiceInstance;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController("xSwitcherEnvProvider")
@RequestMapping(value = "/cognitive/xswitcher/env/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "XSwitcherEnvProvider", description = "device xswitcher env controller")
@Slf4j
public class XSwitcherEnvProvider extends BaseProvider{
	
	@Autowired
	private XModelWatcher watcher;
	
	@Autowired
	private RebalanceService rebalanceService;
	
	@Autowired
	private XDynamicModelRepository mapper;


	@Autowired
	private DiscoveryClient discoveryClient;


    @Operation(summary = "获取当前环境拥有的能力", method = "GET")
    @RequestMapping(value = "/available/models", method = RequestMethod.GET)
    public BaseRes<Set<String>> availableModels() {
        return BaseRes.success(watcher.getHolder().getModelDynamicInstanceMap().keySet());
    }

    @Operation(summary = "获取当前环境预备的能力", method = "GET")
    @RequestMapping(value = "/standby/models", method = RequestMethod.GET)
    public BaseRes<Set<String>> standbyModels() {
        return BaseRes.success(mapper.findAll().stream().map(m -> m.getAnnotatorName()).filter(name -> !watcher.getHolder().getModelDynamicInstanceMap().containsKey(name)).collect(Collectors.toSet()));
    }

    @Operation(summary = "获取当前环境状况", method = "GET")
    @RequestMapping(value = "/instances/info", method = RequestMethod.GET)
    public BaseRes<Map<String, Object>> instancesInfo(@RequestParam(required = false) String type) {

        Holder infoHolder = watcher.getHolder();
        Map<String, Double> scoreMap = infoHolder.getScoreMap().entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey().getInstanceId(), entry -> entry.getValue()));

		List<Pair<ServiceInstance, QueryWorkerResult>> instanceModelList = infoHolder.getInstanceWorkerList();
		Map<String, QueryWorkerResult> resultWorkers = new HashMap<String, QueryWorkerResult>();
		for(Pair<ServiceInstance, QueryWorkerResult> info : instanceModelList)
			resultWorkers.put(info.getLeft().getInstanceId(), info.getRight());
		
		List<Pair<ServiceInstance, QuerySwitcherResult>> instanceSwitcherList = infoHolder.getInstanceSwitcherList();
		Map<String, QuerySwitcherResult> resultSwitchers = new HashMap<String, QuerySwitcherResult>();
		for(Pair<ServiceInstance, QuerySwitcherResult> info : instanceSwitcherList)
			resultSwitchers.put(info.getLeft().getInstanceId(), info.getRight());
		
		if("switcher".equals(type)) {
			return BaseRes.success(Map.of("switchers", resultSwitchers));
		}else if("worker".equals(type)) {
			return BaseRes.success(Map.of("workers", resultWorkers));
		}else
			return BaseRes.success(Map.of("switchers", resultSwitchers, "workers", resultWorkers, "scores", scoreMap));
	}

    @Operation(summary = "获取当前环境状况设备维度", method = "GET")
    @RequestMapping(value = "/instances/device/info", method = RequestMethod.GET)
    public BaseRes<List<InstanceDeviceInfoItem>> instancesDeviceInfo(@RequestParam(required = false) String type) {

		Holder infoHolder = watcher.getHolder();
		//Map<String, Double> scoreMap = infoHolder.getScoreMap().entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey().getInstanceId(), entry -> entry.getValue()));
		List<InstanceDeviceInfoItem> result = new ArrayList<>();
		List<Pair<ServiceInstance, QueryWorkerResult>> instanceModelList = infoHolder.getInstanceWorkerList();
		//Map<String, QueryWorkerResult> resultWorkers = new HashMap<String, QueryWorkerResult>();
		for(Pair<ServiceInstance, QueryWorkerResult> info : instanceModelList){
			// 高频device
			String runningPod = info.getLeft().getInstanceId();
			//for (int i = 0; i < info.getRight().getDynamicModels().size(); i++) {
			List<QueryWorkerResult.Model> models = info.getRight().getDynamicModels();
			for (int j = 0; j < models.size() ; j++) {
				QueryWorkerResult.Model model = models.get(j);
				String annotatorName = model.getAnnotatorName();
				Map<String, Map<String, Object>> deviceMap =  model.getMonitorDevice();
				for (Map.Entry<String, Map<String, Object>> entry : deviceMap.entrySet()) {
					InstanceDeviceInfoItem instanceDeviceInfoItem = new InstanceDeviceInfoItem();
					String deviceId = entry.getKey();
					Map<String,Object> deviceInfo = entry.getValue();

					instanceDeviceInfoItem.setDeviceId(deviceId);
					instanceDeviceInfoItem.setType("high-frequency");
					instanceDeviceInfoItem.setAnnotatorName(annotatorName);
					instanceDeviceInfoItem.setRunningPod(runningPod);

					instanceDeviceInfoItem.setVideoStatus(deviceInfo.get("video_status") != null ? deviceInfo.get("video_status").toString(): "");
					instanceDeviceInfoItem.setHandleTotal(deviceInfo.get("handled_total") != null ? deviceInfo.get("handled_total").toString(): "");
					instanceDeviceInfoItem.setHandleInMinute(deviceInfo.get("handled_in_minite") != null ? deviceInfo.get("handled_in_minite").toString(): "");
					instanceDeviceInfoItem.setSendMsgInMinute(deviceInfo.get("send_message_in_minite") != null ? deviceInfo.get("send_message_in_minite").toString(): "");
					instanceDeviceInfoItem.setSendMsgTotal(deviceInfo.get("send_message_total") != null ? deviceInfo.get("send_message_total").toString(): "");
					instanceDeviceInfoItem.setUnhandledTotal(deviceInfo.get("unhandled_total") != null ? deviceInfo.get("unhandled_total").toString(): "");
					instanceDeviceInfoItem.setUnhandledInMinute(deviceInfo.get("unhandled_in_minite") != null ? deviceInfo.get("unhandled_in_minite").toString():"");

					result.add(instanceDeviceInfoItem);
				}
			}

			//}
		}
			//resultWorkers.put(info.getLeft().getInstanceId(), info.getRight());

		List<Pair<ServiceInstance, QuerySwitcherResult>> instanceSwitcherList = infoHolder.getInstanceSwitcherList();
		Map<String, QuerySwitcherResult> resultSwitchers = new HashMap<String, QuerySwitcherResult>();
		for(Pair<ServiceInstance, QuerySwitcherResult> info : instanceSwitcherList){
			String runningPod = info.getLeft().getInstanceId();
			Map<String, Map<String, Object> > deviceMap = info.getRight().getLowMonitorDevices();
				for (Map.Entry<String, Map<String, Object>> entry : deviceMap.entrySet()) {
					InstanceDeviceInfoItem instanceDeviceInfoItem = new InstanceDeviceInfoItem();
					String deviceId = entry.getKey();
					Map<String,Object> deviceInfo = entry.getValue();

					instanceDeviceInfoItem.setDeviceId(deviceId);
					instanceDeviceInfoItem.setType("low-frequency");
					//JSONArray jsonArray = JSONArray.parseArray();

					instanceDeviceInfoItem.setAnnotatorName(deviceInfo.get("annotatorName").toString());
					instanceDeviceInfoItem.setRunningPod(runningPod);
//					instanceDeviceInfoItem.setVideoStatus(deviceInfo.get("video_status").toString());
//					instanceDeviceInfoItem.setHandleTotal(deviceInfo.get("handled_total").toString());
//					instanceDeviceInfoItem.setHandleInMinute(deviceInfo.get("handled_in_minite").toString());
//					instanceDeviceInfoItem.setSendMsgInMinute(deviceInfo.get("send_message_in_minite").toString());
//					instanceDeviceInfoItem.setSendMsgTotal(deviceInfo.get("send_message_total").toString());
//					instanceDeviceInfoItem.setUnhandledTotal(deviceInfo.get("unhandled_total").toString());
//					instanceDeviceInfoItem.setUnhandledInMinute(deviceInfo.get("unhandled_in_minite").toString());

					instanceDeviceInfoItem.setVideoStatus(deviceInfo.get("video_status") != null ? deviceInfo.get("video_status").toString(): "");
					instanceDeviceInfoItem.setHandleTotal(deviceInfo.get("handled_total") != null ? deviceInfo.get("handled_total").toString(): "");
					instanceDeviceInfoItem.setHandleInMinute(deviceInfo.get("handled_in_minite") != null ? deviceInfo.get("handled_in_minite").toString(): "");
					instanceDeviceInfoItem.setSendMsgInMinute(deviceInfo.get("send_message_in_minite") != null ? deviceInfo.get("send_message_in_minite").toString(): "");
					instanceDeviceInfoItem.setSendMsgTotal(deviceInfo.get("send_message_total") != null ? deviceInfo.get("send_message_total").toString(): "");
					instanceDeviceInfoItem.setUnhandledTotal(deviceInfo.get("unhandled_total") != null ? deviceInfo.get("unhandled_total").toString(): "");
					instanceDeviceInfoItem.setUnhandledInMinute(deviceInfo.get("unhandled_in_minite") != null ? deviceInfo.get("unhandled_in_minite").toString():"");


					result.add(instanceDeviceInfoItem);
				}
			}

		return BaseRes.success(result);


		}


    @Operation(summary = "获取当前环境状况模型维度", method = "GET")
    @RequestMapping(value = "/instances/model/info", method = RequestMethod.GET)
    public BaseRes<Map<String, List<InstanceModelInfoItem>>> instancesModelInfo(@RequestParam(required = false) String type) {

		Holder infoHolder = watcher.getHolder();
		//Map<String, Double> scoreMap = infoHolder.getScoreMap().entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey().getInstanceId(), entry -> entry.getValue()));
		Map<String,List<InstanceModelInfoItem>> result = new HashMap<>();
		List<Pair<ServiceInstance, QueryWorkerResult>> instanceModelList = infoHolder.getInstanceWorkerList();
		//Map<String, QueryWorkerResult> resultWorkers = new HashMap<String, QueryWorkerResult>();
		for(Pair<ServiceInstance, QueryWorkerResult> info : instanceModelList){
			// 高频device
			String runningPod = info.getLeft().getInstanceId();
			//for (int i = 0; i < info.getRight().getDynamicModels().size(); i++) {
			List<QueryWorkerResult.Model> models = info.getRight().getDynamicModels();
			for (int j = 0; j < models.size() ; j++) {
				QueryWorkerResult.Model model = models.get(j);
				String annotatorName = model.getAnnotatorName();
				InstanceModelInfoItem instanceModelInfoItem = new InstanceModelInfoItem();
				instanceModelInfoItem.setStatus(model.getStatus());
				instanceModelInfoItem.setRunningPod(runningPod);
				instanceModelInfoItem.setRunningDeviceIdList(model.getHigtRateDevices());
				instanceModelInfoItem.setMonitor(model.getMonitor());

				if(result.containsKey(annotatorName)){
					List<InstanceModelInfoItem> instanceModelInfoItems = result.get(annotatorName);
					instanceModelInfoItems.add(instanceModelInfoItem);
				}else{
					List<InstanceModelInfoItem> instanceModelInfoItems = new ArrayList<>();
					instanceModelInfoItems.add(instanceModelInfoItem);
					result.put(annotatorName, instanceModelInfoItems);
				}
			}
			//}
		}

		// 低频不写device 不在低频解析，只是解流，可以看device
		return BaseRes.success(result);
	}


	//即时接口 不依赖watcher
	//todo
//	@ApiOperation(value = "获取当前环境流状况", httpMethod = "GET")
//	@RequestMapping(value = "/instances/videostatus/local", method = RequestMethod.GET)
//	public BaseRes<List<VideoStatusRes>> videostatus(){
//
//		return null;
//	}


    @Operation(summary = "获取当前环境流状况", method = "GET")
    @RequestMapping(value = "/instances/videostatus", method = RequestMethod.GET)
    public BaseRes<List<VideoStatusRes>> videostatus() {

		List<Pair<ServiceInstance, BaseRes<List<VideoStatusRes>>>> switherVideoStatus = discoveryClient.getServices()
				.stream()
				.map(discoveryClient::getInstances)
				.flatMap(List::stream)
				.filter(instance -> "true".equals(instance.getMetadata().get("isXSwitcher")))
				.sorted((l, r) -> l.getInstanceId().compareTo(r.getInstanceId()))
				.parallel()
				.filter(instance -> {
					if(!(instance instanceof EurekaServiceInstance))
						return true;

					EurekaServiceInstance eurekaInstance = (EurekaServiceInstance)instance;
					return eurekaInstance.getInstanceInfo().getStatus() == InstanceInfo.InstanceStatus.UP;
				})
				.map(instance -> {
					try {
						BaseRes<List<VideoStatusRes>> videostatus = RestUtils.restTemplate60000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xswitcher/env/query/switcher/videostatus", BaseRes.class);
						return new ImmutablePair<ServiceInstance, BaseRes<List<VideoStatusRes>>>(instance, videostatus);
					}catch(Exception e) {
						log.warn("call isXWorker failed, instanceId:{},{}, e:{}",instance.getHost(), instance.getPort(), e.getMessage());
						return new ImmutablePair<ServiceInstance, BaseRes<List<VideoStatusRes>>>(instance, new BaseRes<>());
					}
				})
				.collect(Collectors.toList());


		List<Pair<ServiceInstance, BaseRes<List<VideoStatusRes>>>> workerVideoStatus = discoveryClient.getServices()
				.stream()
				.map(discoveryClient::getInstances)
				.flatMap(List::stream)
				.filter(instance -> "true".equals(instance.getMetadata().get("isXWorker")))
				.sorted((l, r) -> l.getInstanceId().compareTo(r.getInstanceId()))
				.parallel()
				.filter(instance -> {
					if(!(instance instanceof EurekaServiceInstance))
						return true;

					EurekaServiceInstance eurekaInstance = (EurekaServiceInstance)instance;
					return eurekaInstance.getInstanceInfo().getStatus() == InstanceInfo.InstanceStatus.UP;
				})
				.map(instance -> {
					try {
						BaseRes<List<VideoStatusRes>> videostatus = RestUtils.restTemplate60000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/heart/beat/query/videostatus", BaseRes.class);
						return new ImmutablePair<ServiceInstance, BaseRes<List<VideoStatusRes>>>(instance, videostatus);
					}catch(Exception e) {
						log.warn("call isXWorker failed, instanceId:{},{}, e:{}",instance.getHost(), instance.getPort(), e.getMessage());
						return new ImmutablePair<ServiceInstance, BaseRes<List<VideoStatusRes>>>(instance, BaseRes.success());
					}
				})
				.collect(Collectors.toList());

		List<VideoStatusRes> videoStatusRes = new ArrayList<>();
		if(switherVideoStatus != null){
			switherVideoStatus.forEach(pair ->{
				if(pair.getRight().isSuccess())
					videoStatusRes.addAll(pair.getRight().getData());
			});
		}
		if(workerVideoStatus != null){
			workerVideoStatus.forEach(pair ->{
				if(pair.getRight().isSuccess())
					videoStatusRes.addAll(pair.getRight().getData());
			});
		}

		return BaseRes.success(videoStatusRes);
	}

	// 低频流和noseen实时输出
	@Scheduled(cron = "${xswitcher.queryDevice.cron: 0 0/2 * * * ?}")
	public void queryDeviceForLog() throws Exception {
		CognitiveEntity.QuerySwitcherResult result = this.querySwitcherHandler();
		log.info("[query instance info] queryMonitor :{}", JSON.toJSONString(result));
	}

    @SuppressWarnings("unchecked")
    @Operation(summary = "获取swticher的信息", method = "GET", hidden = true)
    @RequestMapping(value = "/query/switcher", method = RequestMethod.GET)
    public QuerySwitcherResult querySwitcherHandler() {
        QuerySwitcherResult result = new QuerySwitcherResult();

		result.setIncharge(Utils.incharge);
		result.setVersion(Utils.VERSION);
		result.setDeviceType(Initializer.deviceType.toString() + Initializer.cudaType.toString());

		if (Initializer.isGpu()) {
			result.setIdentity(HostUtils.UUID());
			result.setIdentityFull(HostUtils.UUIDFull());
			result.setGpuId(Initializer.deviceId);
		} else {
			result.setIdentity(QueryWorkerResult.NaN);
			result.setIdentityFull(QueryWorkerResult.NaN);
			result.setGpuId(QueryWorkerResult.NaN);
		}

		Map<String, Object> goings = rebalanceService.getOngoingStream();
		List<VideoStreamSeen> seens = (List<VideoStreamSeen>) goings.get("seen");
		List<VideoStreamNonSeen> nonseens = (List<VideoStreamNonSeen>) goings.get("nonseen");
		Set<String> switcherIds = watcher.getHolder().getSwitcherLowRateMap().keySet();

		List<Map<String, Object>> devices = new ArrayList<Map<String, Object>>();
		result.setLowRateDevices(devices);

		Map<String, Map<String, Object>> monitorDevice = new HashMap<>();
		result.setLowMonitorDevices(monitorDevice);

		for (VideoStreamSeen seen : seens)
			if (switcherIds.contains(seen.getDevice().getDeviceId())) {
				devices.add(Map.of("deviceId", seen.getDevice().getDeviceId(), "type", "seen"));
			}


		for (VideoStreamNonSeen nonseen : nonseens)
			if (switcherIds.contains(nonseen.getDevice().getDeviceId())) {
				devices.add(Map.of("deviceId", nonseen.getDevice().getDeviceId(), "type", "nonSeen", "decodeCount", nonseen.decodeFrameCount, "handledCount", nonseen.handledFrameCount, "lostCount", nonseen.lostFrameCount));

				VideoStreamNonSeen.Monitor monitorMap = VideoStreamNonSeen.getMonitorMap().get(nonseen.getDevice().getDeviceId());
				if (monitorMap != null) {

					Map<String, Object> monitorMapWorker = Maps.newLinkedHashMap();
					monitorMapWorker.put("handled_in_minite", monitorMap.getHandledCount());
					monitorMapWorker.put("unhandled_in_minite", monitorMap.getUnhandledCount());
					monitorMapWorker.put("send_message_in_minite", monitorMap.getSendedCount());
					monitorMapWorker.put("send_message_total", monitorMap.sendedTotal.get());
					monitorMapWorker.put("handled_total", monitorMap.handledTotal.get());
					monitorMapWorker.put("unhandled_total", monitorMap.unhandledTotal.get());
					monitorMapWorker.put("deviceId", nonseen.getDevice().getDeviceId());
					monitorMapWorker.put("annotatorName", watcher.getHolder().getSwitcherLowRateMap().get(nonseen.getDevice().getDeviceId()).getProcessors().keySet());
					monitorMapWorker.put("video_status", monitorMap.getVideoStatus());
					monitorMapWorker.put("video_status_detail", monitorMap.getVideoStatusDetail());
					if(monitorMap.getLastErrorCheckTime() != null){
						monitorMapWorker.put("last_error_check_time",monitorMap.getLastErrorCheckTime());
					}
					monitorMapWorker.put("frame_handled_total", monitorMap.handledTotal.get());
					monitorMapWorker.put("frame_unhandled_total", monitorMap.unhandledTotal.get());
					monitorMapWorker.put("frame_un_offer_total", monitorMap.unOfferTotal.get());
					monitorDevice.put(nonseen.getDevice().getDeviceId(), monitorMapWorker);
				}

			}

		//log.info("[query instance info] query switcher info :{}", JSON.toJSONString(result));
		return result;
	}


    @Operation(summary = "获取swticher的信息-流状态", method = "GET", hidden = true)
    @RequestMapping(value = "/query/switcher/videostatus", method = RequestMethod.GET)
    public BaseRes<List<VideoStatusRes>> querySwitcherHandlerVideoStatus() {

		List<VideoStatusRes> videoStatusResList = new ArrayList<>();

		Map<String, Object> goings = rebalanceService.getOngoingStream();
		List<VideoStreamNonSeen> nonseens = (List<VideoStreamNonSeen>) goings.get("nonseen");
		Set<String> switcherIds = watcher.getHolder().getSwitcherLowRateMap().keySet();// switcher 表数据


		for (VideoStreamNonSeen nonseen : nonseens) {
			if (switcherIds.contains(nonseen.getDevice().getDeviceId())) {
				VideoStreamNonSeen.Monitor monitorMap = VideoStreamNonSeen.getMonitorMap().get(nonseen.getDevice().getDeviceId());

				if (monitorMap != null) {
					VideoStatusRes videoStatusRes = new VideoStatusRes();
					videoStatusRes.setVideoStatus(monitorMap.getVideoStatus().toString());
					videoStatusRes.setDeviceId(nonseen.getDevice().getDeviceId());
					videoStatusRes.setDesc(monitorMap.getVideoStatusDetail());
					if (monitorMap.getLastErrorCheckTime() != null) {
						videoStatusRes.setLastErrorCheckTs(String.valueOf(monitorMap.getLastErrorCheckTime()));
					}
					videoStatusResList.add(videoStatusRes);
				}
			}
		}
		//	}
		return BaseRes.success(videoStatusResList);
	}


	@Operation(summary = "更新pipeline", method = "POST")
	@RequestMapping(value = "/pipelineChange", method = RequestMethod.POST)
	public BaseRes<Integer> pipelineChange(@RequestBody Map<String, Object> param) {

		// Convert the map to a Holder object
		ObjectMapper objectMapper = new ObjectMapper();
		Holder deviceId = objectMapper.convertValue(param.get("deviceId"), Holder.class);

		log.info("pipelineChange {}", deviceId.getAnnotatorMap());
		watcher.setHolder(deviceId);
		return BaseRes.success(1);
	}


}
