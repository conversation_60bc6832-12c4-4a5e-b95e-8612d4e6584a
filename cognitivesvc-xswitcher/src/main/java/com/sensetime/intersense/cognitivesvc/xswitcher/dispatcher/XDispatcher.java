package com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.event.RebalancedEvent;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamXswitcherRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sensetime.lib.clientlib.response.BaseRes;

import lombok.Getter;
import lombok.Setter;

@Component
@Slf4j
public class XDispatcher{
	
	@Autowired
	private XDispatcherMonopolized dispatcherMonopolized;
	
	@Autowired
	private XDispatcherCpuModel dynamicCpuDispatcher;
	
	@Autowired
	private XDispatcherGpuModel dynamicGpuDispatcher;
	
	@Autowired
	private XDispatcherCpuStream  streamCpuDispatcher;
	
	@Autowired
	private XDispatcherGpuStream  streamGpuDispatcher;
	
	@Autowired
	private XDispatcherSwitcherOnWorker  switcherOnWorker;
	
	@Autowired
	private XModelWatcher watcher;
	
	@Autowired
	private VideoStreamInfraRepository videoInfraMapper;
	
	@Autowired
	private XDynamicModelRepository dynamicModelMapper;
	
	@Autowired
	private VideoStreamXswitcherRepository switcherMapper;
	
	@Order(value = RebalancedEvent.DispatchToWorker)
	@EventListener(classes = {RebalancedEvent.class})
	public void dispatch(RebalancedEvent event) throws Exception{
		watcher.refresh(event.isMaster());
		log.info("[VideoHandleLog] [dispatch] dispatch master start...{},{}", event.isKeepGoing(), event.isMaster());
		if (!event.isKeepGoing() || !event.isMaster())
			return;
		boolean isChanged = dispatchMaster(event);
		if (isChanged)
			event.setKeepGoing(false);
	}
	
	private boolean dispatchMaster(RebalancedEvent event){
		Boolean loggedForDispatch = (Utils.instance.watchFrameTiktokLevel == -177);
		List<XDynamicModel> allModels = dynamicModelMapper.findAll();
		List<XDynamicModel> currentModels = allModels.stream().filter(model -> model.getSts() == 0 && model.getEstimateCount() > 0).collect(Collectors.toList());
		
		List<XDynamicModel> allCpuModels     = allModels.stream().filter(model -> Objects.requireNonNullElse(model.getCpuModelDup(), 0) > 0).collect(Collectors.toList());
		List<XDynamicModel> currentCpuModels = currentModels.stream().filter(model -> Objects.requireNonNullElse(model.getCpuModelDup(), 0) > 0).collect(Collectors.toList());
		
		List<XDynamicModel> allGpuModels     = allModels.stream().filter(model -> Objects.requireNonNullElse(model.getCpuModelDup(), 0) == 0).collect(Collectors.toList());
		List<XDynamicModel> currentGpuModels = currentModels.stream().filter(model -> Objects.requireNonNullElse(model.getCpuModelDup(), 0) == 0).collect(Collectors.toList());
		
		boolean isChanged = false;
		
		try {
			dispatcherMonopolized.setCurrentModels(allGpuModels);
			dynamicCpuDispatcher.setAllModels(allCpuModels);
			dynamicCpuDispatcher.setCurrentModels(currentCpuModels);
			dynamicGpuDispatcher.setAllModels(allGpuModels);
			dynamicGpuDispatcher.setCurrentModels(currentGpuModels);
			
			dispatcherMonopolized.dispatchMaster();
			
			isChanged |= dynamicGpuDispatcher.dispatchMaster();
			isChanged |= dynamicCpuDispatcher.dispatchMaster();
			
			Map<String, List<String>> ongoingModels = mergeOngoingModels(dynamicCpuDispatcher.getOngoingModels(), dynamicGpuDispatcher.getOngoingModels());

			if(loggedForDispatch){
				log.info("[VideoHandleLog] [dispatch] model dispatch to ongoingModels:{}",  JSON.toJSONString(ongoingModels));
			}
			ongoingModels.entrySet().parallelStream().forEach(entry -> {
			     ServiceInstance instance = watcher.getHolder().getInstanceIdMap().get(entry.getKey()).getLeft();
				 
				 try {
					 if(loggedForDispatch)
						 log.info("[VideoHandleLog] [dispatch] model dispatch to instance {},model:{}", instance.getHost(), JSON.toJSONString(entry.getValue()));
					BaseRes res = RestUtils.restTemplate4000ms.postForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/heart/beat/model", new HttpEntity<List<String>>(entry.getValue(), RestUtils.headers), BaseRes.class);
					 if(!res.isSuccess()){
						 log.error("[VideoHandleLog] [dispatch]  model dispatch heart beat  to instance {} call, url: /cognitive/xworker/heart/beat/model not success :{}", instance.getHost(), res);
					 }
				 }catch(Exception e) {
					 log.error("[VideoHandleLog] [dispatch] dispatch heart beat to instance {}, exception : {}",instance.getHost(),e.getMessage());
					 e.printStackTrace();
				 }
			 });
			
			if(isChanged) {
				dynamicModelMapper.updateRuntime("");
				
				for(XDynamicModel model : currentModels) {
					List<String> instances = new ArrayList<String>();
					
					for(Entry<String, List<String>> entry : ongoingModels.entrySet()) 
						if(entry.getValue().contains(model.getAnnotatorName())) 
							instances.add(entry.getKey());

					dynamicModelMapper.updateRuntimeByAnnotatorName(instances.stream().collect(Collectors.joining("],[", "[", "]")), model.getAnnotatorName());
				}
			}
		}catch(Exception e) {
			log.error("[VideoHandleLog] [dispatch] dispatch models error:{}",e.getMessage());
			e.printStackTrace();
		}

		if(isChanged)
			return isChanged;
		
		try {
			Holder infoHolder = watcher.getHolder();
			
			streamCpuDispatcher.setCurrentModels(currentCpuModels);
			streamGpuDispatcher.setCurrentModels(currentGpuModels);
			
			isChanged |= streamGpuDispatcher.dispatchMaster();
			isChanged |= streamCpuDispatcher.dispatchMaster();
			
			Map<String, Map<String, List<String>>> ongoingStreams = mergeOngoingStreams(streamCpuDispatcher.getOngoingStreams(), streamGpuDispatcher.getOngoingStreams());
			ongoingStreams.entrySet().parallelStream().forEach(entry -> {
			    ServiceInstance instance = infoHolder.getInstanceIdMap().get(entry.getKey()).getLeft();
			 
			    try {
					//todo 加判断打log 打在别的地方
					if(loggedForDispatch)
						log.info("[VideoHandleLog] [dispatch] stream dispatch to instance {},streams:{}", instance.getHost(), JSON.toJSONString(entry.getValue()));
				    BaseRes res = RestUtils.restTemplate4000ms.postForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/heart/beat/stream", new HttpEntity<Map<String, List<String>>>(entry.getValue(), RestUtils.headers), BaseRes.class);
					if(!res.isSuccess()){
						log.error("[VideoHandleLog] [dispatch] stream dispatch heart beat to instance {} call, url: /cognitive/xworker/heart/beat/stream not success :{}", instance.getHost(), res);
					}
				}catch(Exception e) {
					log.error("[VideoHandleLog] [dispatch] stream dispatch heart beat to instance {},exception: {}", instance.getHost(), e.getMessage());
				    e.printStackTrace();
				}
			});
			
			if(isChanged) {
				switcherMapper.updateDispatchDesc("");
				
				for(String deviceId : infoHolder.getSwitcherHighRateMap().keySet()) {
					StringBuilder desc = new StringBuilder();
					
					for(Entry<String, Map<String, List<String>>> entry : ongoingStreams.entrySet()) {
						List<String> processors = entry.getValue().get(deviceId);
						if(CollectionUtils.isNotEmpty(processors)) 
							desc.append(entry.getKey() + ":[" + processors.toString() + "] ");
					}
					
					switcherMapper.updateDispatchDescByDeviceId(desc.toString(), deviceId);
				}			
			}
		}catch(Exception e) {
			log.error("[VideoHandleLog] [dispatch] stream Exception :{}", e.getMessage() );
			e.printStackTrace();
		}
		
		if(isChanged)
			return isChanged;
		
		try {
			Holder infoHolder = watcher.getHolder();
			switcherOnWorker.setVideoStreamInfraMap(videoInfraMapper.findByStsOrderByDeviceIdAsc(0).stream().collect(Collectors.toMap(v -> v.getDeviceId(), v -> v)));
			switcherOnWorker.setOngoingStreams(mergeOngoingStreams(streamCpuDispatcher.getOngoingStreams(), streamGpuDispatcher.getOngoingStreams()).entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue().keySet())));
			
			isChanged |= switcherOnWorker.dispatchMaster();
			
			Map<String, List<String>> ongoingSwitcherOnWorkers = switcherOnWorker.getOngoingSwitcherOnWorkers();
			if(isChanged && loggedForDispatch) {
				log.info("[VideoHandleLog] [dispatch] switcherOnWorker heart beat changed:{},info:{}", JSON.toJSONString(ongoingSwitcherOnWorkers));
			}
			infoHolder.getInstanceIdMap().entrySet().parallelStream().forEach(entry -> {
				List<String> deviceIds = ongoingSwitcherOnWorkers.getOrDefault(entry.getKey(), List.of());
				ServiceInstance instance = entry.getValue().getLeft();
				
				try {
					if(loggedForDispatch)
						log.info("[VideoHandleLog] [dispatch] switcherOnWorker dispatch to instance {},model:{}", instance.getHost(), JSON.toJSONString(entry.getValue()));
					BaseRes res = RestUtils.restTemplate4000ms.postForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/heart/beat/switcher/on/worker", new HttpEntity<List<String>>(deviceIds, RestUtils.headers), BaseRes.class);
					if(!res.isSuccess()){
						log.error("[VideoHandleLog] [dispatch] switcherOnWorker dispatch to instance {} call, url: /cognitive/xworker/heart/beat/switcher/on/worker not success :{}", instance.getHost(), res);
					}
				}catch(Exception e) {
					log.error("[VideoHandleLog] [dispatch] switcherOnWorker dispatch to instance {} exception:{}", instance.getHost(), e.getMessage());
				    e.printStackTrace();
				}
			});
		}catch(Exception e) {
			log.error("[VideoHandleLog] [dispatch] switcherOnWorker Exception :{}", e.getMessage() );
			e.printStackTrace();
		}
		
		if(isChanged)
			return isChanged;
		
		if(Utils.instance.senseyexVideoCreateAffinity && event.getCounting() % 5 == 0) {
			try {
				streamGpuDispatcher.setCurrentModels(currentGpuModels);
				streamGpuDispatcher.judgeAffinity();
				streamCpuDispatcher.judgeAffinity();
			}catch(Exception e) {
				log.error("[VideoHandleLog] [dispatch] streamGpuDispatcher judgeAffinity Exception :{}", e.getMessage() );
				e.printStackTrace();
			}
		}
		
		return isChanged;
	}
	
	private Map<String, List<String>> mergeOngoingModels(Map<String, List<String>> ongoingCpuModels, Map<String, List<String>> ongoingGpuModels){
		Map<String, List<String>> ongoingModels = new HashMap<String, List<String>>(ongoingCpuModels);
		for(Entry<String, List<String>> entry : ongoingGpuModels.entrySet()) {
			List<String> annotators = ongoingModels.getOrDefault(entry.getKey(), new ArrayList<String>());
			annotators.addAll(entry.getValue());
			ongoingModels.put(entry.getKey(), annotators);
		}
		return ongoingModels;
	}
	
	private Map<String, Map<String, List<String>>> mergeOngoingStreams(Map<String, Map<String, List<String>>> ongoingCpuStreams, Map<String, Map<String, List<String>>> ongoingGpuStreams){
		Map<String, Map<String, List<String>>> ongoingStreams = new HashMap<String, Map<String, List<String>>>(ongoingCpuStreams);
		for(Entry<String, Map<String, List<String>>> entry : ongoingGpuStreams.entrySet()) {
			Map<String, List<String>> instanceOngoingStreams = ongoingStreams.getOrDefault(entry.getKey(), new HashMap<String, List<String>>());
			for(Entry<String, List<String>> entryInstance : entry.getValue().entrySet()) {
				List<String> instanceDeviceOngoingStreams = instanceOngoingStreams.getOrDefault(entryInstance.getKey(), new ArrayList<String>());
				instanceDeviceOngoingStreams.addAll(entryInstance.getValue());
				instanceOngoingStreams.put(entryInstance.getKey(), instanceDeviceOngoingStreams);
			}
			ongoingStreams.put(entry.getKey(), instanceOngoingStreams);
		}
		return ongoingStreams;
	}
	
	public static interface Dispatcher{
		/** 作为主节点 进行dispatch */
		public default boolean dispatchMaster() { return false;}
	}
	
	public static abstract class ModelDispatcher implements Dispatcher{
		/** <instanceId, List<annotatorName>>*/
		@Getter
		protected Map<String, List<String>> ongoingModels = new HashMap<String, List<String>>();

		@Autowired
		protected XModelWatcher watcher;
		
		@Autowired
		protected XDynamicModelRepository mapper;

		@Setter
		protected List<XDynamicModel> allModels;
		
		@Setter
		protected List<XDynamicModel> currentModels;
	}
	
	public static abstract class StreamDispatcher implements Dispatcher{
		/** <instanceId, <deviceId, List<annotatorName>>>*/
		@Getter
		protected Map<String, Map<String, List<String>>> ongoingStreams = new HashMap<String, Map<String, List<String>>>();
		
		@Autowired
		protected XModelWatcher watcher;
		
		@Autowired
		protected XDynamicModelRepository mapper;
		
		@Setter
		protected List<XDynamicModel> currentModels;
	}
}
