package com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.stereotype.Component;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.ModelHandlerEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult.Model;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher.XDispatcher.ModelDispatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class XDispatcherGpuModel extends ModelDispatcher{	
	private int rebalanceSemaphore;
	
	private int rebalanceNoChangeCount;
	
	@Override
	public synchronized boolean dispatchMaster(){
		Set<String> gpuModelNames = allModels.stream().map(model -> model.getAnnotatorName()).collect(Collectors.toSet());
		
		ongoingModels = watcher.getHolder().getInstanceWorkerList()
							   .stream()
							   .filter(instance -> instance.getRight().gpuInstance())
							   .collect(Collectors.toMap(
									pair -> pair.getLeft().getInstanceId(), 
									pair -> pair.getRight().getDynamicModels().stream().filter(model -> gpuModelNames.contains(model.getAnnotatorName())).map(Model::getAnnotatorName).collect(Collectors.toList())
							   ));
		
		Holder holder = watcher.getHolder();
		
		/**[instanceId, score] */
		Map<ServiceInstance, Double> scoreMap = holder.getScoreMap();
		//[<instance, instanceEnv>]
		List<Pair<ServiceInstance, QueryWorkerResult>> instanceList = holder.getInstanceWorkerList().stream().filter(instance -> instance.getRight().gpuInstance() && instance.getRight() != QueryWorkerResult.ERROR).collect(Collectors.toList());
		
		//[instanceId, <instance, instanceEnv>]
		Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap = new HashMap<String, Pair<ServiceInstance, QueryWorkerResult>>();

		//[annotatorName, <instance, instanceEnv>]
		Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap = new HashMap<String, List<Pair<ServiceInstance, QueryWorkerResult>>>();
		
		for(Pair<ServiceInstance, QueryWorkerResult> pair : instanceList) {
			instanceIdMap.put(pair.getLeft().getInstanceId(), pair);
			
			for(Model model : pair.getRight().getDynamicModels()) {
				if(!gpuModelNames.contains(model.getAnnotatorName()))
					continue;
				
				List<Pair<ServiceInstance, QueryWorkerResult>> list = annotatorNameMap.get(model.getAnnotatorName());
				if(list == null) {
					list = new ArrayList<Pair<ServiceInstance, QueryWorkerResult>>();
					annotatorNameMap.put(model.getAnnotatorName(), list);
				}
				list.add(pair);
			}
		}
		
		boolean isChanged = false;
		
		List<String> monopolizedIdentities = currentModels.stream()
				.map(model -> model.getMonopolizedIdentity())
				.filter(StringUtils::isNotBlank)
				.map(identity -> identity.split(","))
				.flatMap(Arrays::stream)
				.distinct()
				.collect(Collectors.toList());
		
		Map<String, ModelHandlerEntity> currentModelEntityMap = currentModels.stream()
				.collect(Collectors.toMap(
						XDynamicModel::getAnnotatorName, 
						model -> {
							ModelHandlerEntity entity = ModelHandlerEntity.ofSwitcherGpuEntity(model, monopolizedIdentities, holder.getIdentityMap().keySet());
							if(ArrayUtils.isEmpty(entity.getPreferredIdentity()))
								return entity;
							
							String[] preferredIdentity = Arrays.stream(entity.getPreferredIdentity())
									.filter(identity -> {												
										if(ArrayUtils.isNotEmpty(entity.getRequiredIdentity()))
											if(!ArrayUtils.contains(entity.getRequiredIdentity(), identity))
												return false;
										
										if(ArrayUtils.isNotEmpty(entity.getRejectedIdentity()))
											if(ArrayUtils.contains(entity.getRejectedIdentity(), identity))
												return false;
										
										return true;
									})
									.toArray(String[]::new);
							
							entity.setPreferredIdentity(preferredIdentity);
							return entity;
						}
				));
		
		List<ModelHandlerEntity> currentEntities = new ArrayList<ModelHandlerEntity>(currentModelEntityMap.values());
		
		//遍历一遍所有节点 看看有没有不在列表内的模型在跑 , 并且修正map
		isChanged |= dispatchRemove(annotatorNameMap, currentModelEntityMap);
		
		if(CollectionUtils.isEmpty(currentEntities))//数据库里面没有模型了，那就没必要继续下去了
			return isChanged;
		
		//看一下是否启动了足够多的实例或者超标量的实例 多退少补
		isChanged |= dispatchSufficient(annotatorNameMap, instanceIdMap, instanceList, currentModelEntityMap, scoreMap);
		
		//刚开始排序一下 把有标识和亲和性的模型那到前面来调度
		Collections.sort(currentEntities, (l, r) -> {
			if(!ArrayUtils.isEmpty(l.getRejectedIdentity()) && !ArrayUtils.isEmpty(r.getRejectedIdentity())) {
				return Integer.compare(l.getRejectedIdentity().length, r.getRejectedIdentity().length);
			}else if(!ArrayUtils.isEmpty(l.getRejectedIdentity())) {
				return -1;
			}else if(!ArrayUtils.isEmpty(r.getRejectedIdentity())) {
				return 1;
			}

			if(!ArrayUtils.isEmpty(l.getRequiredIdentity()) && !ArrayUtils.isEmpty(r.getRequiredIdentity())) {
				return Integer.compare(l.getRequiredIdentity().length, r.getRequiredIdentity().length);
			}else if(!ArrayUtils.isEmpty(l.getRequiredIdentity())) {
				return -1;
			}else if(!ArrayUtils.isEmpty(r.getRequiredIdentity())) {
				return 1;
			}

			if(!ArrayUtils.isEmpty(l.getPreferredIdentity()) && !ArrayUtils.isEmpty(r.getPreferredIdentity())) {
				return Integer.compare(l.getPreferredIdentity().length, r.getPreferredIdentity().length);
			}else if(!ArrayUtils.isEmpty(l.getPreferredIdentity())) {
				return -1;
			}else if(!ArrayUtils.isEmpty(r.getPreferredIdentity())) {
				return 1;
			}
			
			if(StringUtils.isNotBlank(l.getAffinityGroup()) && StringUtils.isNotBlank(r.getAffinityGroup())) {
				return l.getAffinityGroup().compareTo(r.getAffinityGroup());
			}else if(StringUtils.isNotBlank(l.getAffinityGroup())) {
				return -1;
			}else if(StringUtils.isNotBlank(r.getAffinityGroup())) {
				return 1;
			}
			
			return 0;
		});
		
		for(int index = 0, count = 0; index < currentEntities.size() && count < 2; index ++) {
			ModelHandlerEntity currentEntity = currentEntities.get(index);
			
			List<Pair<ServiceInstance, QueryWorkerResult>> annotatorInstanceList = annotatorNameMap.get(currentEntity.getAnnotatorName());
			
			if(CollectionUtils.isEmpty(annotatorInstanceList)) {
				boolean ret = dispatchCreate(annotatorNameMap, instanceList, currentEntity, currentModelEntityMap, scoreMap);
				if(ret) {
					isChanged = true;
					count ++;
				}
			}
		}

		if(!isChanged)
			isChanged |= dispatchMandatoryIdentityMatch(instanceList, currentModelEntityMap);
		
		if(!isChanged)
			isChanged |= dispatchOptionalIdentityMatch(annotatorNameMap, instanceList, currentModelEntityMap, scoreMap);
		
		if(!isChanged)
			isChanged |= dispatchAffinityMatch(annotatorNameMap, instanceIdMap, currentModelEntityMap);
		

		/**必须在最后一步来做*/
		if(isChanged) {
			rebalanceSemaphore = 0;
			rebalanceNoChangeCount = 0;
			log.info("[VideoHandleLog] [dispatch] dispatch models...changed ...");
		}else {
			if(Utils.instance.rebalanceOn) {
				rebalanceSemaphore ++;
				if(rebalanceSemaphore >= 3) {
					rebalanceSemaphore = 0;
					isChanged |= dispatchBalance(instanceList, currentModelEntityMap, scoreMap);
					if(isChanged) {
						rebalanceNoChangeCount = 0;
					}else{
						rebalanceNoChangeCount ++;
						
						if(rebalanceNoChangeCount < 10)
							log.info("[VideoHandleLog] [dispatch] Nothing changed in this rebalance round.");
						else if(rebalanceNoChangeCount == 10)
							log.info("[VideoHandleLog] [dispatch] More than 10 times no rebalance changes. Stop logging.");
					}
				}else {
					if(rebalanceNoChangeCount < 10)
						log.info("[VideoHandleLog] [dispatch] Nothing changed in this dispatch round. another [" + (3 - rebalanceSemaphore) + "] more rounds to rebalance.");
				}
			}else {
				if(rebalanceNoChangeCount < 10)
					log.info("[VideoHandleLog] [dispatch] Nothing changed in this dispatch round. rebalance is disabled.");
			}
		}
		
		return isChanged;
	}
	
	/** 查一下当前环境里不需要跑的模型都在哪 全部杀掉 */
	private boolean dispatchRemove(Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap, Map<String, ModelHandlerEntity> currentModelEntityMap) {
		List<String> removeAnnotatorNames = annotatorNameMap.keySet().stream().filter(annotatorName -> !currentModelEntityMap.containsKey(annotatorName)).collect(Collectors.toList());
		if(removeAnnotatorNames.isEmpty()) 
			return false;
		
		for(String removeAnnotatorName : removeAnnotatorNames) {
			List<Pair<ServiceInstance, QueryWorkerResult>> removeingInstances = annotatorNameMap.remove(removeAnnotatorName);
			for(Pair<ServiceInstance, QueryWorkerResult> pair : removeingInstances) {
				log.info("[VideoHandleLog] [dispatch] remove model[" + removeAnnotatorName + "] from (" + pair.getLeft().getInstanceId() + ")");
				ModelHandlerEntity fake = new ModelHandlerEntity();
				fake.setAnnotatorName(removeAnnotatorName);
				fake.setEstimateGpuMemory(1000);
				removeModelToInstance(pair.getLeft(), pair.getRight(), fake);
			}
		}
		
		return true;
	}
	
	/** 看一下是否启动了足够多的副本数 或者杀掉超量的副本数 多退少补 */
	private boolean dispatchSufficient(Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap, Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap, List<Pair<ServiceInstance, QueryWorkerResult>> instanceList, Map<String, ModelHandlerEntity> currentModelEntityMap, Map<ServiceInstance, Double> scoreMap) {
		boolean isChanged = false;
		int insufficientTime = 0;
		for(Entry<String, List<Pair<ServiceInstance, QueryWorkerResult>>> entry : annotatorNameMap.entrySet()) {
			if(insufficientTime >= 2)
				break;
			
			ModelHandlerEntity entity = currentModelEntityMap.get(entry.getKey());
			if(entity == null)
				continue;
			
			int targetCount = entity.getEstimateCount();
			List<Pair<ServiceInstance, QueryWorkerResult>> modelInstanceList = entry.getValue();
			if(modelInstanceList.size() == targetCount) //数量相同 不干活
				continue;
			
			insufficientTime ++;
			
			Map<ServiceInstance, Map<String, Long>> instanceIdAffinityMap = instanceIdAffinityMap(instanceList, currentModelEntityMap);
			
			if(modelInstanceList.size() < targetCount) {//数量太少 增加几个
				int grow = targetCount - modelInstanceList.size();
				
				List<Pair<ServiceInstance, QueryWorkerResult>> optionalInstances = instanceList.stream()
						.filter(p -> !modelInstanceList.contains(p) && !p.getRight().hasInitializing())
						.collect(Collectors.toList());
				
				List<Pair<ServiceInstance, QueryWorkerResult>> growInstances = optionalInstances.stream()
					.filter(pair -> pair.getRight().getAvailableGpuMemory() > Math.max(1000, entity.getEstimateGpuMemory()))
					.filter(pair -> ArrayUtils.isEmpty(entity.getRequiredIdentity()) ||  ArrayUtils.contains(entity.getRequiredIdentity(), pair.getRight().getIdentity()))
					.filter(pair -> ArrayUtils.isEmpty(entity.getRejectedIdentity()) || !ArrayUtils.contains(entity.getRejectedIdentity(), pair.getRight().getIdentity()))
					.sorted((l ,r) -> {
						if(ArrayUtils.isNotEmpty(entity.getPreferredIdentity())) {
							if(ArrayUtils.contains(entity.getPreferredIdentity(), l.getRight().getIdentity())) {
								return -1;
							}else if(ArrayUtils.contains(entity.getPreferredIdentity(), r.getRight().getIdentity())) {
								return 1;
							}
						}
						
						if(StringUtils.isNotBlank(entity.getAffinityGroup())) {
							Long lAffinity = instanceIdAffinityMap.get(l.getKey()).get(entity.getAffinityGroup());
							Long rAffinity = instanceIdAffinityMap.get(r.getKey()).get(entity.getAffinityGroup());
							
							if(lAffinity != null && rAffinity != null && lAffinity != rAffinity) {
								return - Long.compare(lAffinity, rAffinity);
							}else if(lAffinity != null) {
								return -1;
							}else if(rAffinity != null) {
								return 1;
							}
						}
						
						return - Double.compare(scoreMap.get(l.getLeft()), scoreMap.get(r.getLeft()));
					})
					.limit(grow)
					.collect(Collectors.toList());
				
				modelInstanceList.addAll(growInstances);
				
				for(Pair<ServiceInstance, QueryWorkerResult> pair : growInstances) {
					log.info("[VideoHandleLog] [dispatch] sufficient matching add model[" + entity.getAnnotatorName() + "] to (" + pair.getLeft().getInstanceId() + ")");
					isChanged = true;
					addModelToInstance(pair.getLeft(), pair.getRight(), entity);
				}
			}else if(modelInstanceList.size() > targetCount) {//数量太多 砍掉几个
				int shrink = modelInstanceList.size() - targetCount;
				
				List<Pair<ServiceInstance, QueryWorkerResult>> shrinkInstances = modelInstanceList
						.stream()
						.sorted((l ,r) -> {
							if(ArrayUtils.isNotEmpty(entity.getRejectedIdentity())) {
								if(ArrayUtils.contains(entity.getRejectedIdentity(), l.getRight().getIdentity())) {
									return -1;
								}else if(ArrayUtils.contains(entity.getRejectedIdentity(), r.getRight().getIdentity())) {
									return 1;
								}
							}
							
							if(ArrayUtils.isNotEmpty(entity.getRequiredIdentity())) {
								if(ArrayUtils.contains(entity.getRequiredIdentity(), l.getRight().getIdentity())) {
									return 1;
								}else if(ArrayUtils.contains(entity.getRequiredIdentity(), r.getRight().getIdentity())) {
									return -1;
								}
							}
							
							if(ArrayUtils.isNotEmpty(entity.getPreferredIdentity())) {
								if(ArrayUtils.contains(entity.getPreferredIdentity(), l.getRight().getIdentity())) {
									return 1;
								}else if(ArrayUtils.contains(entity.getPreferredIdentity(), r.getRight().getIdentity())) {
									return -1;
								}
							}
							
							if(StringUtils.isNotBlank(entity.getAffinityGroup())) {
								Long lAffinity = instanceIdAffinityMap.get(l.getKey()).get(entity.getAffinityGroup());
								Long rAffinity = instanceIdAffinityMap.get(r.getKey()).get(entity.getAffinityGroup());
								
								if(lAffinity != null && rAffinity != null && lAffinity != rAffinity) {
									return Long.compare(lAffinity, rAffinity);
								}else if(lAffinity != null) {
									return 1;
								}else if(rAffinity != null) {
									return -1;
								}
							}
							
							return Double.compare(scoreMap.get(l.getLeft()), scoreMap.get(r.getLeft()));
						})
						.limit(shrink)
						.collect(Collectors.toList());
				
				modelInstanceList.removeAll(shrinkInstances);
				for(Pair<ServiceInstance, QueryWorkerResult> pair : shrinkInstances) {
					log.info("[VideoHandleLog] [dispatch] sufficient matching remove model[" + entity.getAnnotatorName() + "] from (" + pair.getLeft().getInstanceId() + ")");
					isChanged = true;
					removeModelToInstance(pair.getLeft(), pair.getRight(), entity);
				}
			}
		}
		
		return isChanged;
	}
	
	/** 创建新的模型 */
	private boolean dispatchCreate(Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap, List<Pair<ServiceInstance, QueryWorkerResult>> instanceList, ModelHandlerEntity modelToCreate, Map<String, ModelHandlerEntity> currentModelEntityMap, Map<ServiceInstance, Double> scoreMap) {
		boolean isChanged = false;
		
		int targetCount = modelToCreate.getEstimateCount();
		
		Map<ServiceInstance, Map<String, Long>> instanceIdAffinityMap = instanceIdAffinityMap(instanceList, currentModelEntityMap);
		
		List<Pair<ServiceInstance, QueryWorkerResult>> growInstances = instanceList.stream()
				.filter(pair -> pair.getRight().getAvailableGpuMemory() > Math.max(1000, modelToCreate.getEstimateGpuMemory()))
				.filter(pair -> ArrayUtils.isEmpty(modelToCreate.getRequiredIdentity()) ||  ArrayUtils.contains(modelToCreate.getRequiredIdentity(), pair.getRight().getIdentity()))
				.filter(pair -> ArrayUtils.isEmpty(modelToCreate.getRejectedIdentity()) || !ArrayUtils.contains(modelToCreate.getRejectedIdentity(), pair.getRight().getIdentity()))
				.filter(pair -> !pair.getRight().hasInitializing())
				.sorted((l ,r) -> {
					if(ArrayUtils.isNotEmpty(modelToCreate.getPreferredIdentity())) {
						if(ArrayUtils.contains(modelToCreate.getPreferredIdentity(), l.getRight().getIdentity())) {
							return -1;
						}else if(ArrayUtils.contains(modelToCreate.getPreferredIdentity(), r.getRight().getIdentity())) {
							return 1;
						}
					}
					
					if(StringUtils.isNotBlank(modelToCreate.getAffinityGroup())) {
						Long lAffinity = instanceIdAffinityMap.get(l.getKey()).get(modelToCreate.getAffinityGroup());
						Long rAffinity = instanceIdAffinityMap.get(r.getKey()).get(modelToCreate.getAffinityGroup());
						
						if(lAffinity != null && rAffinity != null && lAffinity != rAffinity) {
							return - Long.compare(lAffinity, rAffinity);
						}else if(lAffinity != null) {
							return -1;
						}else if(rAffinity != null) {
							return 1;
						}
					}
					
					return - Double.compare(scoreMap.get(l.getLeft()), scoreMap.get(r.getLeft()));
				})
				.limit(targetCount)
				.collect(Collectors.toList());
		
		annotatorNameMap.put(modelToCreate.getAnnotatorName(), growInstances);
		
		for(Pair<ServiceInstance, QueryWorkerResult> pair : growInstances) {
			log.info("[VideoHandleLog] [dispatch] create matching add model[" + modelToCreate.getAnnotatorName() + "] to (" + pair.getLeft().getInstanceId() + ")");
			isChanged = true;
			addModelToInstance(pair.getLeft(), pair.getRight(), modelToCreate);
		}
		
		return isChanged;
	}

	/** 空闲时候 查看一下当前环境上 每个模型跑在的位置的标识 是否和需要的一致 不一致的话 尝试调度一下  */
	private boolean dispatchMandatoryIdentityMatch(List<Pair<ServiceInstance, QueryWorkerResult>> instanceList, Map<String, ModelHandlerEntity> currentModelEntityMap) {
		boolean isChanged = false;
		
		/** requiredIdentity rejectedIdentity 检测 不合适位置的删除*/
		for(Pair<ServiceInstance, QueryWorkerResult> pair : instanceList) {
			List<String> needToRemoveAnnotators = pair.getRight().getDynamicModels()
						.stream()
						.filter(model -> {
							ModelHandlerEntity entity = currentModelEntityMap.get(model.getAnnotatorName());
							if(entity == null)
								return false;
							
							if(ArrayUtils.isNotEmpty(entity.getRequiredIdentity()))
								if(!ArrayUtils.contains(entity.getRequiredIdentity(), pair.getRight().getIdentity()))
									return true;
							
							if(ArrayUtils.isNotEmpty(entity.getRejectedIdentity()))
								if(ArrayUtils.contains(entity.getRejectedIdentity(), pair.getRight().getIdentity()))
									return true;
							
							return false;
						})
						.map(model -> model.getAnnotatorName())
						.collect(Collectors.toList());
			
			if(needToRemoveAnnotators.isEmpty()) 
				continue;
			
			isChanged = true;
			
			for(String removing : needToRemoveAnnotators){
				ModelHandlerEntity entity = currentModelEntityMap.get(removing);
				log.info("[VideoHandleLog] [dispatch] identity matching remove model[" + entity.getAnnotatorName() + "] from (" + pair.getLeft().getInstanceId() + ")");
				removeModelToInstance(pair.getLeft(), pair.getRight(), entity);
			}
		}
		
		return isChanged;
	}
	
	/** 空闲时候 查看一下当前环境上 每个模型跑在的位置的标识 是否和需要的一致 不一致的话 尝试调度一下  */
	private boolean dispatchOptionalIdentityMatch(Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap, List<Pair<ServiceInstance, QueryWorkerResult>> instanceList, Map<String, ModelHandlerEntity> currentModelEntityMap, Map<ServiceInstance, Double> scoreMap) {
		boolean isChanged = false;
		
		List<Triple<Pair<ServiceInstance, QueryWorkerResult>, Pair<ServiceInstance, QueryWorkerResult>, String>> movings = new ArrayList<Triple<Pair<ServiceInstance, QueryWorkerResult>, Pair<ServiceInstance, QueryWorkerResult>, String>>();
		
		for(Entry<String, List<Pair<ServiceInstance, QueryWorkerResult>>> entry : annotatorNameMap.entrySet()) {
			ModelHandlerEntity entity = currentModelEntityMap.get(entry.getKey());
			if(entity == null || ArrayUtils.isEmpty(entity.getPreferredIdentity()))
				continue;
			
			List<Pair<ServiceInstance, QueryWorkerResult>> alreadyInstances = entry.getValue();
			
			Set<String> alreadyIndentities = alreadyInstances.stream().map(pair -> pair.getRight().getIdentity()).collect(Collectors.toSet());
			Set<String> wantedIndentities = Arrays.stream(entity.getPreferredIdentity()).filter(id -> !alreadyIndentities.contains(id)).collect(Collectors.toSet());
			
			if(wantedIndentities.isEmpty())
				continue;

			List<Pair<ServiceInstance, QueryWorkerResult>> optionalInstances = instanceList.stream()
					.filter(p -> !alreadyInstances.contains(p) && !p.getRight().hasInitializing())
					.collect(Collectors.toList());
			
			Map<ServiceInstance, Map<String, Long>> instanceIdAffinityMap = instanceIdAffinityMap(instanceList, currentModelEntityMap);
			
			List<Pair<ServiceInstance, QueryWorkerResult>> targetOptionalInstances = optionalInstances.stream()
					.filter(pair -> wantedIndentities.contains(pair.getRight().getIdentity()))
					.filter(pair -> pair.getRight().getAvailableGpuMemory() > Math.max(1000, entity.getEstimateGpuMemory()))
					.sorted((l ,r) -> {
						if(StringUtils.isNotBlank(entity.getAffinityGroup())) {
							Long lAffinity = instanceIdAffinityMap.get(l.getKey()).get(entity.getAffinityGroup());
							Long rAffinity = instanceIdAffinityMap.get(r.getKey()).get(entity.getAffinityGroup());
							
							if(lAffinity != null && rAffinity != null && lAffinity != rAffinity) {
								return - Long.compare(lAffinity, rAffinity);
							}else if(lAffinity != null) {
								return -1;
							}else if(rAffinity != null) {
								return 1;
							}
						}
						
						return - Double.compare(scoreMap.get(l.getLeft()), scoreMap.get(r.getLeft()));
					})
					.collect(Collectors.toList());
			
			if(targetOptionalInstances.isEmpty())
				continue;
			
			List<Pair<ServiceInstance, QueryWorkerResult>> removeOptionalInstances = alreadyInstances.stream()
					.filter(pair -> !ArrayUtils.contains(entity.getPreferredIdentity(), pair.getRight().getIdentity()))
					.sorted((l ,r) -> {						
						if(StringUtils.isNotBlank(entity.getAffinityGroup())) {
							Long lAffinity = instanceIdAffinityMap.get(l.getKey()).get(entity.getAffinityGroup());
							Long rAffinity = instanceIdAffinityMap.get(r.getKey()).get(entity.getAffinityGroup());
							
							if(lAffinity != null && rAffinity != null && lAffinity != rAffinity) {
								return Long.compare(lAffinity, rAffinity);
							}else if(lAffinity != null) {
								return 1;
							}else if(rAffinity != null) {
								return -1;
							}
						}
						
						return Double.compare(scoreMap.get(l.getLeft()), scoreMap.get(r.getLeft()));
					})
					.collect(Collectors.toList());
			
			if(removeOptionalInstances.isEmpty())
				continue;
			
			for(int index = 0; index < Math.min(targetOptionalInstances.size(), removeOptionalInstances.size()); index ++) {
				Pair<ServiceInstance, QueryWorkerResult> targetPair = targetOptionalInstances.remove(index);
				Pair<ServiceInstance, QueryWorkerResult> removePair = removeOptionalInstances.remove(index);
				
				movings.add(new ImmutableTriple<Pair<ServiceInstance, QueryWorkerResult>, Pair<ServiceInstance, QueryWorkerResult>, String>(removePair, targetPair, entry.getKey()));
			}
		}

		for(Triple<Pair<ServiceInstance, QueryWorkerResult>, Pair<ServiceInstance, QueryWorkerResult>, String> change : movings){
			isChanged = true;
			
			ModelHandlerEntity entity = currentModelEntityMap.get(change.getRight());
			
			ServiceInstance from = change.getLeft().getLeft();
			QueryWorkerResult fromEnv = change.getLeft().getRight();
			
			removeModelToInstance(from, fromEnv, entity);
			
			ServiceInstance to   = change.getMiddle().getLeft();
			QueryWorkerResult toEnv = change.getMiddle().getRight();
			
			log.info("[VideoHandleLog] [dispatch] identity matching move model[" + entity.getAnnotatorName() + "] from (" + from.getInstanceId() + ") to (" + to.getInstanceId() + ")");
			addModelToInstance(to, toEnv, entity);
		}
		
		return isChanged;
	}
	
	/** 空闲时查看一下当前环境上模型的亲和性是否合适 如果有不合适的尝试调度一下 */
	private boolean dispatchAffinityMatch(Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap, Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap, Map<String, ModelHandlerEntity> currentModelEntityMap) {
		Map<String, List<ModelHandlerEntity>> affinityModelMap = currentModelEntityMap.values()
				.stream()
				.filter(dynamic -> StringUtils.isNotBlank(dynamic.getAffinityGroup()))
				.collect(Collectors.toMap(
						model -> model.getAffinityGroup(), 
						Lists::newArrayList, 
						(l, r) -> (ArrayList<ModelHandlerEntity>)ListUtils.union(l, r))
				)
				.entrySet()
				.stream()
				.filter(entry -> entry.getValue().size() >= 2)
				.collect(Collectors.toMap(
						entry -> entry.getKey(), 
						entry -> entry.getValue()
				));
		if(affinityModelMap.size() <= 0) 
			return false;
		
		//<frominstance, model, toinstance>
		List<Triple<ServiceInstance, String, ServiceInstance>> changes = new ArrayList<Triple<ServiceInstance, String, ServiceInstance>>();
		 
		for(Entry<String, List<ModelHandlerEntity>> entry : affinityModelMap.entrySet()) {			
			Map<String, Integer> groupAnnotatorSizeMap = entry.getValue().stream()
								.map(model -> model.getAnnotatorName())
								.collect(Collectors.toMap(
									Function.identity(), 
									annotatorName -> annotatorNameMap.get(annotatorName).size()
								));
			
			/** 已存在在卡上模型名 按照由少到多排序*/
			List<Pair<ServiceInstance, List<String>>> instancePartialList = entry.getValue().stream()
					.map(model -> annotatorNameMap.get(model.getAnnotatorName()))
					.flatMap(List::stream)
					.distinct()
					.map(pair -> {
						return new ImmutablePair<ServiceInstance, List<String>>(
										pair.getLeft(), 
										pair.getRight().getDynamicModels()
														.stream()
														.map(model -> model.getAnnotatorName())
														.filter(name -> groupAnnotatorSizeMap.containsKey(name))
														.collect(Collectors.toList()));
					})
					.filter(pair -> pair.getRight().size() > 0)
					.sorted((l, r) -> {
						if(l.getRight().size() != r.getRight().size())
							return Integer.compare(l.getRight().size(), r.getRight().size());
						else
							return l.getLeft().getInstanceId().compareTo(r.getLeft().getInstanceId());
					})
					.collect(Collectors.toList());
			
			for(boolean nextIterate = true; nextIterate;) {
				nextIterate = false;
				
				for(Iterator<Pair<ServiceInstance, List<String>>> its = instancePartialList.iterator(); its.hasNext();) {
					Pair<ServiceInstance, List<String>> pair = its.next();
					//判断一下 这个卡上已经拥有该亲和性组的所有模型了 不需要在搞了
					if(pair.getRight().size() >= groupAnnotatorSizeMap.size()) {
						its.remove();
						Iterator<Entry<String, Integer>> it = groupAnnotatorSizeMap.entrySet().iterator();
						while(it.hasNext()) {
							Entry<String, Integer> subEntry = it.next();
							if(subEntry.getValue() <= 1) {
								it.remove();
								nextIterate = true;
							}else 
								subEntry.setValue(subEntry.getValue() - 1);
						}
					}
				}
			}
			
			if(groupAnnotatorSizeMap.size() <= 1)//大于一个 是表达在环境上 还有亲和组内至少两个模型飘在外面 可以进行调度
				continue;

			List<Triple<ServiceInstance, String, ServiceInstance>> thisGroupMaxChanges = new ArrayList<Triple<ServiceInstance, String, ServiceInstance>>();
			int thisGroupMaxSize = 0;

			for(int index = instancePartialList.size() - 1; index >= 0 ;index --) {
				Pair<ServiceInstance, List<String>> targetPair = instancePartialList.get(index);
				
				List<String> readys = new ArrayList<String>();
				List<String> wanteds = groupAnnotatorSizeMap.keySet()
						.stream()
						.filter(annotator -> !targetPair.getRight().contains(annotator))
						.collect(Collectors.toList());
				
				List<Triple<ServiceInstance, String, ServiceInstance>> thisGroupChanges = new ArrayList<Triple<ServiceInstance, String, ServiceInstance>>();
				
				for(int jndex = 0; jndex < instancePartialList.size(); jndex ++) {
					if(index == jndex) 
						continue;
					
					Pair<ServiceInstance, List<String>> fromPair = instancePartialList.get(jndex);
					
					List<String> fromProvideds = ListUtils.intersection(fromPair.getRight(), wanteds);					
					for(String wanted : fromProvideds) {
						ModelHandlerEntity entity = currentModelEntityMap.get(wanted);
						if(entity == null)
							continue;
						
						String fromIdendity = instanceIdMap.get(fromPair.getLeft().getInstanceId()).getRight().getIdentity();
						String targetIdendity = instanceIdMap.get(targetPair.getLeft().getInstanceId()).getRight().getIdentity();
						
						if(ArrayUtils.isNotEmpty(entity.getRequiredIdentity()))
							if(!ArrayUtils.contains(entity.getRequiredIdentity(), targetIdendity))
								continue;
						
						if(ArrayUtils.isNotEmpty(entity.getRejectedIdentity()))
							if(ArrayUtils.contains(entity.getRejectedIdentity(), targetIdendity))
								continue;
						
						if(ArrayUtils.isNotEmpty(entity.getPreferredIdentity())) {
							boolean fromPreferred = ArrayUtils.contains(entity.getPreferredIdentity(), fromIdendity);
							boolean targetPreferred = ArrayUtils.contains(entity.getPreferredIdentity(), targetIdendity);
							
							if(fromPreferred && !targetPreferred)
								continue;
						}
						
						thisGroupChanges.add(new ImmutableTriple<ServiceInstance, String, ServiceInstance>(fromPair.getLeft(), wanted, targetPair.getLeft()));
						wanteds.remove(wanted);
						readys.add(wanted);
					}
				}
				
				int thisGroupSize = thisGroupChanges.size() + targetPair.getRight().size();
				//如果这次移动模型数量比上次
				if(thisGroupSize > thisGroupMaxSize && thisGroupChanges.size() > 0) {
					Pair<ServiceInstance, QueryWorkerResult> toEnv = instanceIdMap.get(targetPair.getLeft().getInstanceId());
					int needGpuMem = readys.stream().map(currentModelEntityMap::get).filter(Objects::nonNull).map(model -> model.getEstimateGpuMemory()).reduce(Integer::sum).get();
					
					if(toEnv.getRight().getAvailableGpuMemory() > Math.max(1000, needGpuMem)) {
						thisGroupMaxChanges = thisGroupChanges;
						thisGroupMaxSize = thisGroupSize;
					}
				}
			}
			
			changes.addAll(thisGroupMaxChanges);
		}
		
		if(changes.isEmpty())
			return false;
		
		for(Triple<ServiceInstance, String, ServiceInstance> change : changes){
			log.info("[VideoHandleLog] [dispatch] affinitiy matching move model[" + change.getMiddle() + "] from (" + change.getLeft().getInstanceId() + ") to (" + change.getRight().getInstanceId() + ")");
			
			ModelHandlerEntity entity = currentModelEntityMap.get(change.getMiddle());
			if(entity == null)
				continue;
			
			Pair<ServiceInstance, QueryWorkerResult> fromEnv = instanceIdMap.get(change.getLeft().getInstanceId());
			removeModelToInstance(change.getLeft(), fromEnv.getRight(), entity);
			
			Pair<ServiceInstance, QueryWorkerResult> toEnv = instanceIdMap.get(change.getRight().getInstanceId());
			addModelToInstance(change.getRight(), toEnv.getRight(), entity);
		}
		
		return true;
	}
	
	/** 环境上没有任何变化的情况下 才会去检查均衡 每次修改只操作一个模型 然后就返回了 均衡检查不破坏标志匹配*/
	private boolean dispatchBalance(List<Pair<ServiceInstance, QueryWorkerResult>> instanceList, Map<String, ModelHandlerEntity> currentModelEntityMap, Map<ServiceInstance, Double> scoreMap) {
		Boolean loggedForDispatch = (Utils.instance.watchFrameTiktokLevel == -177);
		if(loggedForDispatch)
			log.info("[VideoHandleLog] [dispatch] start dispatch balance...");
		if(instanceList.size() <= 1 || currentModelEntityMap.isEmpty())
			return false;
		
		Map<ServiceInstance, List<Model>> removableMap = instanceList.parallelStream()
				.collect(Collectors.toMap(
						Pair::getLeft, 
						pair -> pair.getRight().getDynamicModels().stream().collect(Collectors.toList()),
						ListUtils::union
				));
		
		boolean isRemovealbe = removableMap.values().stream().filter(list -> !list.isEmpty()).findAny().isPresent();
		if(!isRemovealbe) 
			return false;
		
		//显卡使用率从小到大排序
		removableMap.values().forEach(list -> Collections.sort(list, (l, r) -> Long.compare(l.getExpire(), r.getExpire())));
		
		//评分由小到大排序
		List<Pair<ServiceInstance, QueryWorkerResult>> sortedInstanceList = instanceList.stream()
				.filter(pair -> pair.getRight() != QueryWorkerResult.ERROR && !pair.getRight().hasInitializing())
				.sorted((l, r) -> Double.compare(scoreMap.get(l.getLeft()), scoreMap.get(r.getLeft())))
				.collect(Collectors.toList());
		
		List<Pair<Pair<ServiceInstance, QueryWorkerResult>, List<Pair<ServiceInstance, QueryWorkerResult>>>> iteratorPairs = Stream.iterate(0, i -> i + 1)
					.limit(sortedInstanceList.size())
					.map(index -> {
						Pair<ServiceInstance, QueryWorkerResult> hostPair = sortedInstanceList.get(index);
						List<Pair<ServiceInstance, QueryWorkerResult>> fromPairs = sortedInstanceList.stream()
						            /*写了这么多就为了这个filter*/
									.filter(fromPair -> scoreMap.get(hostPair.getLeft()) - scoreMap.get(fromPair.getLeft()) > 25)
									.collect(Collectors.toList());
						
						if(fromPairs.isEmpty()) 
							return null;
						
						return new ImmutablePair<Pair<ServiceInstance, QueryWorkerResult>, List<Pair<ServiceInstance, QueryWorkerResult>>>(hostPair, fromPairs);
					})
					.filter(Objects::nonNull)
					.collect(Collectors.toList());
		
		if(iteratorPairs.isEmpty()) 
			return false;

		Map<ServiceInstance, Map<String, Long>> instanceIdAffinityMap = instanceIdAffinityMap(sortedInstanceList, currentModelEntityMap);
		
		Pair<ServiceInstance, Model> fromTarget = null;
		Pair<ServiceInstance, ModelHandlerEntity> toTarget = null;
		
		for(int index = iteratorPairs.size() - 1; index >= 0; index --) {
			Pair<ServiceInstance, QueryWorkerResult> toPair = iteratorPairs.get(index).getLeft();
			List<Pair<ServiceInstance, QueryWorkerResult>> fromPairs = iteratorPairs.get(index).getRight();
			
			Set<String> toExistsModels = toPair.getRight().getDynamicModels().stream().map(m -> m.getAnnotatorName()).collect(Collectors.toSet());
			
			for(int jndex = 0; jndex < fromPairs.size(); jndex ++) {
				Pair<ServiceInstance, QueryWorkerResult> fromPair = fromPairs.get(jndex);
				
				ServiceInstance fromInstance = fromPair.getLeft();
				QueryWorkerResult fromModelResult = fromPair.getRight();
				
				List<Model> removableModels = removableMap.get(fromInstance);
				if(removableModels.isEmpty() || fromModelResult.getDynamicModels().size() < 2)
					continue;
				
				Map<String, Long> affinityMap = instanceIdAffinityMap.get(fromInstance);
				
				removableModels = removableModels.stream()
						.filter(model -> {
							ModelHandlerEntity entity = currentModelEntityMap.get(model.getAnnotatorName());
							if(entity == null)
								return false;
							
							String fromIdendity = fromPair.getRight().getIdentity();
							String targetIdendity = toPair.getRight().getIdentity();
							
							if(ArrayUtils.isNotEmpty(entity.getRequiredIdentity()))
								if(!ArrayUtils.contains(entity.getRequiredIdentity(), targetIdendity))
									return false;
							
							if(ArrayUtils.isNotEmpty(entity.getRejectedIdentity()))
								if(ArrayUtils.contains(entity.getRejectedIdentity(), targetIdendity))
									return false;
							
							if(ArrayUtils.isNotEmpty(entity.getPreferredIdentity())) {
								boolean fromPreferred = ArrayUtils.contains(entity.getPreferredIdentity(), fromIdendity);
								boolean targetPreferred = ArrayUtils.contains(entity.getPreferredIdentity(), targetIdendity);
								
								if(fromPreferred && !targetPreferred)
									return false;
							}
							
							return !ArrayUtils.contains(entity.getRejectedIdentity(), toPair.getRight().getIdentity());
						})
						.filter(model -> {
							String affinityGroup = currentModelEntityMap.get(model.getAnnotatorName()).getAffinityGroup();
							return StringUtils.isBlank(affinityGroup) ? true : affinityMap.get(affinityGroup) <= 1;
						})
						.filter(model -> !toExistsModels.contains(model.getAnnotatorName()))
						.collect(Collectors.toList());
				
				if(removableModels.isEmpty())
					continue;

				Model targetModel = removableModels.stream()
						.max((l, r) -> {
							ModelHandlerEntity le = currentModelEntityMap.get(l.getAnnotatorName());
							ModelHandlerEntity re = currentModelEntityMap.get(r.getAnnotatorName());
							
							return Integer.compare(le.getEstimateGpuMemory(), re.getEstimateGpuMemory());
						})
						.get();
				
				fromTarget = new ImmutablePair<ServiceInstance, Model>(fromInstance, targetModel);
				toTarget   = new ImmutablePair<ServiceInstance, ModelHandlerEntity>(toPair.getLeft(), currentModelEntityMap.get(targetModel.getAnnotatorName()));
				break;
			}
			
			//每次rebalance只会move一个模型，慢是慢了点，但是相对稳定
			if(fromTarget != null && toTarget != null)
				break;
		}
		
		if(fromTarget == null || toTarget == null)
			return false;
		
		log.info("[VideoHandleLog] [dispatch] rebalance move model[" + fromTarget.getRight().getAnnotatorName() + "] from [" + scoreMap.get(fromTarget.getLeft()) + "](" + fromTarget.getLeft().getInstanceId() + ") to [" + scoreMap.get(toTarget.getLeft()) + "](" + toTarget.getLeft().getInstanceId() + ")");

		String annotatorName = fromTarget.getRight().getAnnotatorName();
		ModelHandlerEntity entity = currentModelEntityMap.get(annotatorName);
		
		removeModelToInstance(fromTarget.getLeft(), null, entity);
		addModelToInstance(toTarget.getLeft(), null, entity);
		return true;
	}
	
	/** 返回这个instanse上面的模型的affinity   <instance, <affinity, count>> */
	private Map<ServiceInstance, Map<String, Long>> instanceIdAffinityMap(List<Pair<ServiceInstance, QueryWorkerResult>> instanceList, Map<String, ModelHandlerEntity> currentModelEntityMap){
		return instanceList.stream()
				.collect(Collectors.toMap(
						pair -> pair.getLeft(), 
						pair -> {							
							Map<String, Long> affinitys = pair.getRight().getDynamicModels()
										.stream()
										.map(Model::getAnnotatorName)
										.map(currentModelEntityMap::get)
										.filter(Objects::nonNull)
										.map(ModelHandlerEntity::getAffinityGroup)
										.filter(StringUtils::isNotBlank)
										.collect(Collectors.toMap(
												Function.identity(), 
												group -> 1L, 
												Long::sum));
							
							return affinitys;
						}
				));
	}
	
	/** 增加模型*/
	private void addModelToInstance(ServiceInstance instance, QueryWorkerResult env, ModelHandlerEntity entity) {
		ongoingModels.get(instance.getInstanceId()).add(entity.getAnnotatorName());
		if(env != null) {
			env.setAvailableGpuMemory(env.getAvailableGpuMemory() - entity.getEstimateGpuMemory());
			env.addDynamicModel(entity.getAnnotatorName());
		}
	}
	
	/** 删除模型*/
	private void removeModelToInstance(ServiceInstance instance, QueryWorkerResult env, ModelHandlerEntity entity) {
		ongoingModels.get(instance.getInstanceId()).remove(entity.getAnnotatorName());
		if(env != null) {
			env.setAvailableGpuMemory(env.getAvailableGpuMemory() + entity.getEstimateGpuMemory());
			env.removeDynamicModel(entity.getAnnotatorName());
		}
	}
}
