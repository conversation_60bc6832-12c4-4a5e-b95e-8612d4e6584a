package com.sensetime.intersense.cognitivesvc.xswitcher.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel.XDynamicModelBuilder;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher;
import com.sensetime.lib.clientlib.response.BaseRes;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.io.File;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController("xSwitcherDynamicProvider")
@RequestMapping(value = "/cognitive/xswitcher/dynamic/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="XSwitcherDynamicProvider", description = "device xswitcher dynamic controller")
@Slf4j
public class XSwitcherDynamicProvider extends BaseProvider{
    
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH:mm:ss");
	
	@Autowired
	private XDynamicModelRepository mapper;
	
	@Autowired
	private XModelWatcher watcher;
	
	@Operation(summary = "动态模型总数",method = "GET")
	@RequestMapping(value = "/getXDynamicModelCount", method = RequestMethod.GET)
	public BaseRes<Long> getXDynamicModelCount() {
		return BaseRes.success(mapper.count());
	}
    
	@Operation(summary = "查询动态模型",method = "GET")
	@RequestMapping(value = "/getXDynamicModel", method = RequestMethod.GET)
	public BaseRes<List<XDynamicModel>> getXDynamicModel(@RequestParam(required = false) String annotatorName) {
		if(StringUtils.isNotBlank(annotatorName)) {
			return BaseRes.success(mapper.findAllById(Lists.newArrayList(annotatorName)));
		}else {
			return BaseRes.success(mapper.findAll());
		}
	}
	
    @Operation(summary = "删除动态模型处理器", method = "POST")
    @RequestMapping(value = "/deleteXDynamicModel", method = RequestMethod.POST)
    public BaseRes<Integer> deleteXDynamicModel(@RequestParam String annotatorName) throws Exception {
		if(!mapper.existsById(annotatorName)) 
			return BaseRes.success(0);
    	
    	mapper.deleteById(annotatorName);
    	
		return BaseRes.success(1);
    }
    
    @Operation(summary = "更改动态模型处理器", method = "POST")
    @RequestMapping(value = "/addXDynamicModel", method = RequestMethod.POST)
    public BaseRes<Integer> addXDynamicModel(@RequestBody XDynamicModel entity) throws Exception {
		if(!ArrayUtils.isSameLength(entity.getAnnotatorPaths().split(","), entity.getPluginPaths().split(","))) 
			throw new RuntimeException("annotatorPaths and pluginPaths are not same length, please check.");
		
		if(mapper.existsById(entity.getAnnotatorName())) 
			throw new RuntimeException(entity.getAnnotatorName() + " exist, please check.");
		
		if(entity.getCpuModelDup() == null)
			entity.setCpuModelDup(0);
		
		entity.setUpdateTs(new Date());
		mapper.saveAndFlush(entity);
		
		return BaseRes.success(1);
    }
    
    @Operation(summary = "更改动态模型处理器", method = "POST")
    @RequestMapping(value = "/updateXDynamicModel", method = RequestMethod.POST)
    public BaseRes<Integer> updateXDynamicModel(@RequestBody XDynamicModel entity) throws Exception {
		if(entity.getModelSource()!=1){
			if(!ArrayUtils.isSameLength(entity.getAnnotatorPaths().split(","), entity.getPluginPaths().split(",")))
			throw new RuntimeException("annotatorPaths and pluginPaths are not same length, please check.");
		}


		if(!mapper.existsById(entity.getAnnotatorName())) 
			throw new RuntimeException(entity.getAnnotatorName() + " does not exist, please check.");

		if(entity.getCpuModelDup() == null)
			entity.setCpuModelDup(0);
		
		entity.setUpdateTs(new Date());
		entity.setFmdPaths("");
		mapper.saveAndFlush(entity);
		
		return BaseRes.success(1);
    }
    
    @Operation(summary = "开关动态模型", method = "POST")
    @RequestMapping(value = "/onoffXDynamicModel", method = RequestMethod.POST)
    public BaseRes<Integer> onoffXDynamicModel(@RequestParam String annotatorName, @RequestParam boolean onoff) throws Exception {
		if(!mapper.existsById(annotatorName)) {
            log.error("no annotator [" + annotatorName + "] exist.");
            throw new BusinessException("3002","no annotator [" + annotatorName + "] exist.");
        }
    	
    	if(onoff)
    		return BaseRes.success(mapper.updatests(0, annotatorName));
    	else
    		return BaseRes.success(mapper.updatests(1, annotatorName));
    }

	@Operation(summary = "开关动态模型存图功能", method = "POST")
	@RequestMapping(value = "/onoffXDynamicModelImgSaveTag", method = RequestMethod.POST)
	public BaseRes<Integer> onoffXDynamicModelImgSaveTag(@RequestParam String annotatorName, @RequestParam int onoff) throws Exception {
		if(!mapper.existsById(annotatorName)) {
			log.error("no annotator [" + annotatorName + "] exist.");
			throw new BusinessException("3002","no annotator [" + annotatorName + "] exist.");
		}

		if(onoff == 0)
			return BaseRes.success(mapper.updateImgSaveTag(0, annotatorName));
		else
			return BaseRes.success(mapper.updateImgSaveTag(1, annotatorName));
	}


	@Operation(summary = "开关动态模型存图功能", method = "POST")
	@RequestMapping(value = "/getXDynamicModelImgSaveTag", method = RequestMethod.POST)
	public BaseRes<Integer> onoffXDynamicModelImgSaveTag(@RequestParam String annotatorName) throws Exception {
		if(!mapper.existsById(annotatorName)) {
			log.error("no annotator [" + annotatorName + "] exist.");
			throw new BusinessException("3002","no annotator [" + annotatorName + "] exist.");
		}
		Optional<XDynamicModel> xDynamicModel = mapper.findById(annotatorName);
		return BaseRes.success(xDynamicModel.get().getImgSaveTag());
	}
    
    @Operation(summary = "上传动态模型包", method = "POST")
    @RequestMapping(value = "/uploadXDynamicModel", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseRes<String> uploadDynamicModel(@RequestParam("modelFile") MultipartFile modelFile) throws Exception {
    	if(!modelFile.getOriginalFilename().endsWith(".zip"))
    		throw new RuntimeException("should be file.zip");
    	
    	File dir = new File("/kestre/kestrel_dynamic/_BOOT_INITIAL/") ;
    	if(!dir.exists() || !dir.isDirectory())
    		dir.mkdirs();
    	
    	modelFile.transferTo(Path.of("/kestrel/kestrel_dynamic/_BOOT_INITIAL/" + modelFile.getOriginalFilename()));
    	
		return BaseRes.success("OK");
    }
    
    @SuppressWarnings("unchecked")
	@Operation(summary = "编译动态代码", method = "POST")
    @RequestMapping(value = "/compile/dynamic/code", method = RequestMethod.POST)
    public BaseRes<String> compileDynamicCode(@RequestBody Map<String, String> javaCode){
    	List<ServiceInstance> instances = watcher.getHolder().getInstances();
    	if(instances.isEmpty()) 
    		throw new RuntimeException("no worker in this environment.");
    	
    	ServiceInstance instance = instances.get(ThreadLocalRandom.current().nextInt(instances.size()));
    	return RestUtils.restTemplate60000ms.postForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/compile/dynamic/code", new HttpEntity<Map<String, String>>(javaCode, RestUtils.headers), BaseRes.class);
    }
    
    @Scheduled(fixedDelay = 15000)
    synchronized void checkNewModel() {
    	File directory = new File("/kestrel/kestrel_dynamic/_BOOT_INITIAL/");
    	if(!directory.exists() || directory.listFiles().length <= 0)
    		return;
    	
		boolean ret = lock();
		if(!ret) {
			log.debug("other model is uploading, checkNewModel task waiting.");
			return;
		}
		
    	for(File modelFile : directory.listFiles()) {
    		lock();
    		
    		if(StringUtils.endsWithIgnoreCase(modelFile.getName(), ".zip")) {
    			try(ZipFile zipfile = new ZipFile(modelFile.getAbsolutePath())){
        			
        		}catch (Exception e) {
        			if(e.getMessage().equals("zip END header not found") || e.getMessage().equals("zip file is empty")) {
            			log.warn("zipfile[" + modelFile.getAbsolutePath() + "] is not completed or empty, wait next time.");
            			continue;
        			}else {
        				e.printStackTrace();
        				modelFile.delete();
        				continue;
        			}
    			}
        		
        		try {
        			log.info("load dynamic model [" + modelFile.getAbsolutePath() + "]");
        			handleZipFile(modelFile.getAbsolutePath());
        		}catch(Exception e) {
        	    	unlock();
        			throw new RuntimeException(e);
        		}finally {
        			modelFile.delete();
        		}
    		}else if(StringUtils.startsWithIgnoreCase(modelFile.getName(), "KM_") && StringUtils.endsWithIgnoreCase(modelFile.getName(), ".model")) {
    			long sizes[] = new long[3];
    			for(int index = 0; index < sizes.length; index ++) {
    				sizes[index] = modelFile.length();
    				try { Thread.sleep(500); } catch (InterruptedException e) { }
    			}
    			
    			long num = Arrays.stream(sizes).distinct().count();
    			if(num != 1) {
    				log.warn("modelfile[" + modelFile.getAbsolutePath() + "] is not completed, wait next time.");
    				continue;
    			}
    			
    			try {
        			log.info("load dynamic model [" + modelFile.getAbsolutePath() + "]");
        			handleModelFile(modelFile.getAbsolutePath(),"","");
        		}catch(Exception e) {
        	    	unlock();
        			throw new RuntimeException(e);
        		}finally {
        			modelFile.delete();
        		}
    		}else {
    			modelFile.delete();
        		log.warn(modelFile.getAbsolutePath() + " should be file.zip or km_file.model, deleted.");
    		}
    	}
    	
    	unlock();
    }

	@Transactional
    protected void handleModelFile(String modelFilePath, String version, String commitId) throws Exception {
    	File modelFile = new File(modelFilePath);
    	String[] parameters = modelFile.getName().split("\\.")[0].split("_");
    	
    	if(parameters.length < 6)
    		throw new RuntimeException("parameter not enougn in file name." + Arrays.toString(parameters));
    	
    	if(!"KM".equalsIgnoreCase(parameters[0]))
    		throw new RuntimeException("should be KM_ model.");
    	
    	String kepName = parameters[1];
    	String annotatorName = parameters[2];
    	int batchSize = Integer.parseInt(parameters[3]);
    	int model_count = Integer.parseInt(parameters[4]);
    	int cpu_dup = Integer.parseInt(parameters[5]);
    	int gpu_memory = Integer.parseInt(parameters[6]);
		int max_stream_count = 0;
		int img_save_tag = 0;
		// check 模型版本和环境是否符合
		if(!checkModelPlatform(Arrays.asList(modelFile.getName())))
			throw new RuntimeException("model platform not match");

    	File kepFile = new File("/usr/cognitivesvc/" + kepName + ".kep");
    	if(!kepFile.exists()) {
			 kepFile = new File(Initializer.zipLibPath +"/" + kepName + ".kep");
			if(!kepFile.exists()) {
				throw new RuntimeException("default kep[" + kepName + "] not exist.");
			}
		}
    	
    	Integer originSts = 1;
    	List<XDynamicModel> exists = getXDynamicModel(annotatorName).getData();
    	if(!exists.isEmpty()) {
    		originSts = exists.get(0).getSts();
    		//deleteXDynamicModel(annotatorName);
			model_count = exists.get(0).getEstimateCount();
			max_stream_count = exists.get(0).getMaxStreamCount();
			img_save_tag = exists.get(0).getImgSaveTag();
    	}
    	
    	File storeDir  = new File("/kestrel/kestrel_dynamic/" + annotatorName + "/" + dateTimeFormatter.format(LocalDateTime.now()) + "/");
    	FileUtils.moveToDirectory(modelFile, storeDir, true);
    	
    	XDynamicModelBuilder dynamicModelBuilder = XDynamicModel.builder().updateTs(new Date()).annotatorName(annotatorName)
				.batchSize(batchSize).estimateCount(model_count).estimateGpuMemory(gpu_memory)
				.maxStreamCount(max_stream_count).cpuModelDup(cpu_dup).sts(originSts).imgSaveTag(img_save_tag);
    	if(StringUtils.equalsIgnoreCase(kepName, "classifier"))
    		dynamicModelBuilder = dynamicModelBuilder.additional("{\"fillRoiMap\": {\"0\": 1}}");
    	else if(StringUtils.equalsIgnoreCase(kepName, "psyche"))
    		dynamicModelBuilder = dynamicModelBuilder.javaClassName("PsycheProcess");
    	
    	dynamicModelBuilder.pluginPaths(kepFile.getAbsolutePath());
    	dynamicModelBuilder.annotatorPaths(storeDir.getAbsolutePath() + "/" + modelFile.getName());

		if(StringUtils.isNotEmpty(version)){
			dynamicModelBuilder.version(version);
		}

		if(StringUtils.isNotEmpty(commitId)){
			dynamicModelBuilder.commitId(commitId);
		}
		if(!exists.isEmpty()) {
			deleteXDynamicModel(annotatorName);
		}
		dynamicModelBuilder.fmdPaths("");
		dynamicModelBuilder.imgSaveTag(0);
    	mapper.saveAndFlush(dynamicModelBuilder.build());
    }

	private boolean checkModelPlatform(List<String> modelNames){
		Initializer.DeviceType currentDeviceType = Initializer.deviceType;
		// ModelDescribe-trt-fp-T4/A2_XXX.model
		for (int i = 0; i < modelNames.size(); i++) {
			String modelName = modelNames.get(i);
			if(modelName.toUpperCase().contains("CPU")) continue;
			String[] parameters = modelName.split("-");
			if(parameters.length < 4){
				log.error("checkModelPlatform model name not standard {}",modelName);
				return false;
			}
			String platform = parameters[3];
			if(!platform.toUpperCase().startsWith(currentDeviceType.toString())){
				log.error("checkModelPlatform model name not match {}",modelName);
				return false;
			}
		}
		return true;
	}

    @Transactional
    protected void handleZipFile(String zipFilePath) throws Exception {
    	try(ZipFile zipfile = new ZipFile(zipFilePath)){

			// 获取version信息
			String version = "";
			String commitId = "";
			ZipEntry versionFile = zipfile.getEntry("version.txt");
			if(versionFile == null)
				log.warn("{} dont have version info",zipFilePath);
			else{
				String[] versionInfo = new String(FileCopyUtils.copyToByteArray(zipfile.getInputStream(versionFile))).split("\n");
				for (int i = 0; i < versionInfo.length; i++) {
					String text = versionInfo[i];
					String[] info = text.split(":");
					if(info.length == 2)
						if(info[0].toLowerCase().equals("version")){
							version = info[1];
						}
					if(info[0].toLowerCase().equals("commitid")){
						commitId = info[1];

					}
				}
			}

			// 比较是不是单个模型文件
			ZipEntry paramJsonFile = zipfile.getEntry("param.json");
			if(paramJsonFile == null){
				ZipEntry modelNameFile = null;
				int modelCount = 0;
				Iterator iterator = zipfile.entries().asIterator();
				for (int i = 0; i < zipfile.size(); i++) {
					ZipEntry zipEntry = (ZipEntry) iterator.next();
					if(zipEntry.getName().endsWith(".model")){
						modelNameFile = zipEntry;
						modelCount +=1;
					}
				}

				if(modelCount == 1){
					String tempPath = "/kestrel/kestrel_dynamic/temp/" + dateTimeFormatter.format(LocalDateTime.now()) + "/";
					File file = new File(tempPath);
					try{
						if(!file.exists()) {
							file.mkdirs();
						}
						FileUtils.copyInputStreamToFile(zipfile.getInputStream(modelNameFile), new File(tempPath + modelNameFile.getName()));
						handleModelFile(tempPath + modelNameFile.getName(), version,commitId);
					}finally {
						file.deleteOnExit();
					}
				}else{
					throw new RuntimeException("no param.json in this zip file.");
				}
			}
			else{

				String paramJson = new String(FileCopyUtils.copyToByteArray(zipfile.getInputStream(paramJsonFile)));

				XDynamicModel dynamicModel = JSON.parseObject(paramJson, XDynamicModel.class);
				Integer originSts = null;
				Integer originEstimateCount = null;
				Integer originMaxStreamCount = null;
				int saveImgTag = 0;
				List<XDynamicModel> exists = getXDynamicModel(dynamicModel.getAnnotatorName()).getData();
				if(!exists.isEmpty()) {
					originSts = exists.get(0).getSts();
					originEstimateCount = exists.get(0).getEstimateCount();
					originMaxStreamCount =  exists.get(0).getMaxStreamCount();
					saveImgTag = exists.get(0).getImgSaveTag();
					//deleteXDynamicModel(dynamicModel.getAnnotatorName());
				}
				String storeBasePath = "/kestrel/kestrel_dynamic/" + dynamicModel.getAnnotatorName() + "/" + dateTimeFormatter.format(LocalDateTime.now()) + "/";

				String[] pluginFiles = new String[0];
				if(StringUtils.isNotBlank(dynamicModel.getPluginPaths()))
					pluginFiles = dynamicModel.getPluginPaths().split(",");

				String[] annotatorFiles = new String[0];
				if(StringUtils.isNotBlank(dynamicModel.getAnnotatorPaths()))
					annotatorFiles = dynamicModel.getAnnotatorPaths().split(",");

				// pluginFiles  & annotatorFiles 去重
				try {
					int beforeLength = pluginFiles.length; // pluginFiles & annotatorFiles 数量和顺序严格匹配
					if(beforeLength > 2) {
						Set<Pair<String, String>> set = new LinkedHashSet<>(); // 保持数组顺序
						for (int i = 0; i < beforeLength; i++) {
							Pair<String, String> pair = Pair.create(pluginFiles[i], annotatorFiles[i]);
							set.add(pair);
						}

						Pair<String, String>[] pairArr = set.toArray(new Pair[0]);
						String[] pluginPathsArr_after = new String[pairArr.length];
						String[] annotatorPathsArr_after = new String[pairArr.length];
						for (int i = 0; i < pairArr.length; i++) {
							pluginPathsArr_after[i] = pairArr[i].getFirst();
							annotatorPathsArr_after[i] = pairArr[i].getSecond();
						}
						pluginFiles = pluginPathsArr_after;
						annotatorFiles = annotatorPathsArr_after;
					}
				} catch (Exception e) {
					log.warn(">>> [handle param.json] pluginFiles & annotatorFiles deduplicate failed!",e);
				}


				// check 模型版本和环境是否符合
				if(!checkModelPlatform(Arrays.asList(annotatorFiles)))
					throw new RuntimeException("model platform not match");


				if(pluginFiles.length != annotatorFiles.length)
					throw new RuntimeException("param.json pluginFiles and annotatorFiles length is not even, please check.");

				List<ZipEntry> modelEntrys = Stream.of(pluginFiles, annotatorFiles)
						.flatMap(Arrays::stream)
						.map(zipfile::getEntry)
						.collect(Collectors.toList());

				if(CollectionUtils.exists(modelEntrys, Objects::isNull))
					throw new RuntimeException("some of the pluginPaths or annotatorPaths do not exist, please check.");

				if(StringUtils.isNotBlank(dynamicModel.getJavaCode())) {
					ZipEntry codeEntry = zipfile.getEntry(dynamicModel.getJavaCode());
					if(codeEntry == null)
						throw new RuntimeException("JavaCode file do not exist, please check.");
				}

//				List<ZipEntry> fmdEntrys = Arrays.stream(dynamicModel.getFmdPaths().split(","))
//						.map(zipfile::getEntry)
//						.collect(Collectors.toList());
//
//				if(CollectionUtils.exists(fmdEntrys, Objects::isNull))
//					throw new RuntimeException("some of the fmd do not exist, please check.");

				if(StringUtils.isNotBlank(dynamicModel.getAttached())) {
					List<ZipEntry> attachedEntrys = Arrays.stream(dynamicModel.getAttached().split(","))
							.map(zipfile::getEntry)
							.collect(Collectors.toList());

					if(CollectionUtils.exists(attachedEntrys, Objects::isNull))
						throw new RuntimeException("some of the attached do not exist, please check.");
				}

				if(StringUtils.isNotBlank(dynamicModel.getFlockConfig())) {
					ZipEntry codeEntry = zipfile.getEntry(dynamicModel.getFlockConfig());
					if(codeEntry == null)
						throw new RuntimeException("FlockConfig file do not exist, please check.");
				}

				if(StringUtils.isNotBlank(dynamicModel.getFlockConfig())) {
					ZipEntry codeEntry = zipfile.getEntry(dynamicModel.getFlockConfig());
					String flockConfig = new String(FileCopyUtils.copyToByteArray(zipfile.getInputStream(codeEntry)));
					dynamicModel.setFlockConfig(flockConfig);
				}

				//添加统一check
				if (dynamicModel.getCheckForce() == 1) {
					String error = checkDynamicModel(dynamicModel, paramJson);
					if (StringUtils.isNotBlank(error))
						throw new RuntimeException("check force dynamic error:" + error);
				}

				try {
					/**
					 * 文件检查正确，开始copy文件到ceph上
					 */
					FileUtils.copyInputStreamToFile(zipfile.getInputStream(paramJsonFile), new File(storeBasePath + "param.json"));

					for(ZipEntry entry : modelEntrys)
						FileUtils.copyInputStreamToFile(zipfile.getInputStream(entry), new File(storeBasePath + entry.getName()));

					if(StringUtils.isNotBlank(dynamicModel.getAttached())) {
						String attacheds[] = dynamicModel.getAttached().split(",");

						for(String attached : attacheds)
							FileUtils.copyInputStreamToFile(zipfile.getInputStream(zipfile.getEntry(attached)), new File(storeBasePath + attached));

						dynamicModel.setAttached(Arrays.stream(attacheds).map(item -> storeBasePath + item).collect(Collectors.joining(",")));
					}

                    
                    // 读取动态fmd 目录，已导入的fmd就跳过
                    Set<String> exsitedFmdFilenames = FileUtils.listFiles(new File(Initializer.zipLibPath), new String[]{"fmd"}, false)
                            .stream().map(File::getName).collect(Collectors.toSet());
                    
                    if(StringUtils.isNotBlank(dynamicModel.getFmdPaths())) {
						String[] fmdFiles = dynamicModel.getFmdPaths().split(",");

						List<ZipEntry> pluginEntrys = Stream.of(pluginFiles, fmdFiles)
								.flatMap(Arrays::stream)
								.map(zipfile::getEntry)
								.collect(Collectors.toList());

						for (ZipEntry entry : pluginEntrys) {
                            if(entry == null)
                                continue;
                            
                            if(exsitedFmdFilenames.contains(entry.getName())){
                                log.warn(">>> [dynamic model import] exsited fmd file in zipLib, skipped! ziplib path: {} , fmd: {}",Initializer.zipLibPath,entry.getName());
                                continue;
                            }
							
                            log.info("pluginEntrysPath {}", Initializer.zipLibPath + "/" + entry.getName());
                            FileUtils.copyInputStreamToFile(zipfile.getInputStream(entry),
                                    new File(Initializer.zipLibPath + "/" + entry.getName()));
							
						}
					}

					dynamicModel.setFmdPaths("");


					/**
					 * copy文件成功，开始构建对象存数据库
					 */
					dynamicModel.setPluginPaths(Arrays.stream(pluginFiles).map(item -> storeBasePath + item).collect(Collectors.joining(",")));
					dynamicModel.setAnnotatorPaths(Arrays.stream(annotatorFiles).map(item -> storeBasePath + item).collect(Collectors.joining(",")));

					if(StringUtils.isNotBlank(dynamicModel.getJavaCode())) {
						ZipEntry codeEntry = zipfile.getEntry(dynamicModel.getJavaCode());
						dynamicModel.setJavaClassName(codeEntry.getName().split("\\.")[0]);
						String javaCode = new String(FileCopyUtils.copyToByteArray(zipfile.getInputStream(codeEntry)));
						dynamicModel.setJavaCode(javaCode);
					}



					dynamicModel.setUpdateTs(new Date());

					if(originSts != null)
						dynamicModel.setSts(originSts);
					else if(dynamicModel.getSts() == null)
						dynamicModel.setSts(1);

					if(originEstimateCount != null){
						dynamicModel.setEstimateCount(originEstimateCount);
					}else if(dynamicModel.getEstimateCount() == null)
						dynamicModel.setEstimateCount(1);

					if(originMaxStreamCount != null){
						dynamicModel.setMaxStreamCount(originMaxStreamCount);
					}
					else
						dynamicModel.setMaxStreamCount(0);

					if(dynamicModel.getEstimateGpuMemory() == null)
						dynamicModel.setEstimateGpuMemory(1000);

					if(dynamicModel.getBatchSize() == null)
						dynamicModel.setBatchSize(0);

					if(dynamicModel.getCpuModelDup() == null)
						dynamicModel.setCpuModelDup(0);


					dynamicModel.setVersion(version);
					dynamicModel.setCommitId(commitId);

					//spring 模型又policy 外部触发修改
					dynamicModel.setModelType(0);
					dynamicModel.setImgSaveTag(saveImgTag);

					if(!exists.isEmpty()) {
						deleteXDynamicModel(dynamicModel.getAnnotatorName());
					}
//
//					if(dynamicModel.getImgSaveTag() == null){
//						dynamicModel.setImgSaveTag(0);
//					}
					mapper.saveAndFlush(dynamicModel);



				}catch(Exception e){
					File deletes = new File(storeBasePath);
					Arrays.stream(deletes.listFiles()).forEach(File::delete);
					deletes.delete();

					throw new RuntimeException(e);
				}
    		}
		}
    }

	private String checkDynamicModel(XDynamicModel dynamicModel, String paramJson) {

		ArrayList<String> fmdFiles = new ArrayList<>();
		ArrayList<String> modelFiles = new ArrayList<>();

		extractFileNames(dynamicModel.getFlockConfig(), "\\b\\w+\\.fmd\\b", fmdFiles);
		extractFileNames(dynamicModel.getFlockConfig(), "\\bKM_[\\w.-]+\\.model\\b", modelFiles);
		fmdFiles.remove("plugin.fmd");
		return checkFilesExistenceAndInParamJson(fmdFiles, paramJson, "fmd")
				+ checkFilesExistenceAndInParamJson(modelFiles, paramJson, "model");
	}

	private void extractFileNames(String text, String regex, ArrayList<String> fileNames) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(text);
		while (matcher.find()) {
			fileNames.add(matcher.group());
		}
	}

	private String checkFilesExistenceAndInParamJson(ArrayList<String> fileNames, String paramJson, String fileType) {
		for (String fileName : fileNames) {
			if (!paramJson.contains(fileName.trim())) {
				return fileName + " " + fileType + " not exists in the current directory";
			}
//			if (!Files.exists(Paths.get(fileName.trim()))) {
//				return fileName + " file not exists in the current directory";
//			}
		}
		return "";
	}

	private boolean lock() {
    	return Utils.instance.tryLock("DynamicModelUploading", 180);
    }
    
    private void unlock() {
    	Utils.instance.releaseLock("DynamicModelUploading");
    }
}
