package com.sensetime.intersense.cognitivesvc.xswitcher.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.VideoStreamXswitcherInput;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamXswitcher;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamXswitcherRepository;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XBusinessExecuter;
import com.sensetime.lib.clientlib.response.BaseRes;

import io.swagger.v3.oas.annotations.Operation;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController("xSwitcherProvider")
@RequestMapping(value = "/cognitive/xswitcher/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="XSwitcherProvider",description = "device xswitcher controller")
@Slf4j
public class XSwitcherProvider extends BaseProvider{

	@Autowired
	private VideoStreamXswitcherRepository videoStreamXMapper;

	@Autowired
	private XBusinessExecuter xBusinessExecuter;

	@Operation(summary = "视频流X总数",method = "GET")
	@RequestMapping(value = "/getXSwitcherCount", method = RequestMethod.GET)
	public BaseRes<Number> getXSwitcherCount() {
		return BaseRes.success(videoStreamXMapper.count());
	}
	
	@Operation(summary = "查询视频流X配置",method = "GET")
	@RequestMapping(value = "/getXSwitcher", method = RequestMethod.GET)
	public BaseRes<List<VideoStreamXswitcher>> getXSwitcher(@RequestParam(required = false) String deviceId) {
		//log.info("[printHttpLog] url: /cognitive/device/face/getXSwitcher , req:{}", deviceId);
		if(StringUtils.isNotBlank(deviceId)) {
			return BaseRes.success(videoStreamXMapper.findByDeviceIdIn(Lists.newArrayList(deviceId)));
		}else {
			return BaseRes.success(videoStreamXMapper.findAll());
		}
	}
	
	@Operation(summary = "添加视频流X配置", method = "POST")
	@RequestMapping(value = "/addOrUpdateXSwitcher", method = RequestMethod.POST)
	public BaseRes<Object> addOrUpdateXSwitcher(@RequestBody VideoStreamXswitcherInput device) throws Exception {
		log.info("[printHttpLog] url: /cognitive/xswitcher/addOrUpdateXSwitcher , req:{}", device);
		Set<String> processorSet = device.getProcessors().stream().map(p -> p.get("processor").toString()).filter(Objects::nonNull).collect(Collectors.toSet());
		
		List<VideoStreamXswitcher> exists = videoStreamXMapper.findByDeviceIdIn(Lists.newArrayList(device.getDeviceId()));

		device.getProcessors().forEach(x->{xBusinessExecuter.addProcessor(x);});


		List<VideoStreamXswitcher> savings = Lists.newArrayList(device.toVideoStreamXswitcher());
		List<Integer> deletings = new ArrayList<Integer>();
		
		for(VideoStreamXswitcher switcher : exists) {
			JSONArray array = JSON.parseArray(switcher.getProcessors());
			
			boolean changed = false;
			
			Iterator<Object> its = array.iterator();
			while(its.hasNext()) {
				JSONObject obj = (JSONObject)its.next();
				if(processorSet.contains(obj.get("processor"))) {
					its.remove();
					changed = true;
				}
			}
			
			if(changed) {
				switcher.setProcessors(JSON.toJSONString(array));
				savings.add(switcher);
				
				if(array.isEmpty())
					deletings.add(switcher.getId());
			}
		}
		
		videoStreamXMapper.saveAll(savings);
		for(int delete : deletings) 
			videoStreamXMapper.deleteById(delete);
		
		videoStreamXMapper.flush();
		log.info("[printHttpLog] url: /cognitive/xswitcher/addOrUpdateXSwitcher , req:{},res:{}", device,savings.size());
		
		return BaseRes.success(savings.size());
	}
	
	@Operation(summary = "删除视频流X配置", method = "POST")
	@RequestMapping(value = "/deleteXSwitcher", method = RequestMethod.POST)
	public BaseRes<Object> deleteXSwitcher(@RequestParam String deviceId, @RequestParam(required = false) String annotatorName) {
		log.info("[printHttpLog] url: /cognitive/xswitcher/deleteXSwitcher , req:{},{}", deviceId, annotatorName);
		if(StringUtils.isBlank(annotatorName)) {
			try{
				videoStreamXMapper.deleteByDeviceIdIn(List.of(deviceId));
			}catch (Exception e){
				log.warn("deleteDeviceFace error,deviceId:{}, {}",deviceId, e.getMessage());
			}
			return BaseRes.success("OK");
		}
		
		List<VideoStreamXswitcher> exists = videoStreamXMapper.findByDeviceIdIn(Lists.newArrayList(deviceId));
		for(VideoStreamXswitcher switcher : exists) {
			JSONArray array = JSON.parseArray(switcher.getProcessors());
			boolean changed = false;
			
			Iterator<Object> its = array.iterator();
			while(its.hasNext()) {
				JSONObject obj = (JSONObject)its.next();
				if(annotatorName.equals(obj.get("processor"))) {
					its.remove();
					changed = true;
				}
			}
			
			if(changed) {
				switcher.setProcessors(JSON.toJSONString(array));
				videoStreamXMapper.saveAndFlush(switcher);
			}
		}
		return BaseRes.success("OK");
	}
}
