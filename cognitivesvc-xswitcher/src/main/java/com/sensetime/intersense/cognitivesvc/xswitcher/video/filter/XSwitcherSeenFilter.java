package com.sensetime.intersense.cognitivesvc.xswitcher.video.filter;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_imgproc.CvFont;
import org.opencv.core.Rect;
import org.springframework.core.annotation.Order;
import org.springframework.util.concurrent.ListenableFuture;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.google.common.collect.Maps;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawItem;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawRect;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawTextEn;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoSeenFilter;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelExecuter;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sun.jna.Pointer;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.XSwitcher;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;

import lombok.Setter;

@Order(150)
public class XSwitcherSeenFilter extends VideoSeenFilter{
	
	private final ConcurrentHashMap<String, Long> ticker = new ConcurrentHashMap<String, Long>();
	
	private List<DrawItem> currentRects = new ArrayList<DrawItem>();
	
	@Setter
	private static XModelWatcher watcher;
	
	@Setter
	private static XModelExecuter executor;


	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	protected void handle(VideoFrame videoFrame, VideoStreamInfra device) {
		Holder infoHolder = watcher.getHolder();
		
		XSwitcher entity = infoHolder.getSwitcherLowRateMap().get(device.getDeviceId());
		if(entity == null) 
			return;
		
		Map<String, Processor> processorMap = entity.getProcessors();
		if(MapUtils.isEmpty(processorMap)) 
			return;
		
		List<DrawItem> draw_item = (List<DrawItem>)context.get("draws");
		
		Iterator<Processor> its = processorMap.values().iterator();
		for(int index = 0; its.hasNext(); index ++) {
			String processor = its.next().getProcessor();
			CvScalar color = colors.get(processor);
			if(color == null) {
				colors.put(processor, opencv_core.CV_RGB(ThreadLocalRandom.current().nextInt(0, 256), ThreadLocalRandom.current().nextInt(0, 256), ThreadLocalRandom.current().nextInt(0, 256)));
				color = colors.get(processor);
			}
			
			String text = infoHolder.getModelDynamicInstanceMap().containsKey(processor) ? processor : processor + "[not exist]";
			draw_item.add(new DrawTextEn(text, font2, color, 0, (index + 1) * 27));
		}
		
		long now = videoFrame.getCapturedTime();
		
		Map<String, Processor> processors = processorMap.values().stream()
					.filter(processor -> {
						if(!infoHolder.getModelDynamicInstanceMap().containsKey(processor.getProcessor()))
							return false;
						
						long previousTime = ticker.getOrDefault(processor.getProcessor(), 0L);
						
						boolean pass = now - previousTime > Objects.requireNonNullElse(processor.getInterval(), 1) * 1000;
						if(pass)
							ticker.put(processor.getProcessor(), now);
						
						return pass;
					})
					.collect(Collectors.toMap(
							processor -> processor.getProcessor(), 
							Function.identity()
					));
		
		if(MapUtils.isEmpty(processors)) {
			draw_item.addAll(currentRects);
			return;
		}else{
			currentRects.clear();
		}

		SenseyexRawImage event = SenseyexRawImage.builder()
				.deviceId(device.getDeviceId())
				.processors(processors.values().stream().map(p -> p.getProcessor()).toArray(String[]::new))
				.capturedTime(now)
				.extra(Maps.newHashMap(Map.of("response", true, "frame", FrameUtils.ref_frame(videoFrame.getGpuFrame()))))
				.build();
		Map<String, List<Map<String, Object>>> data = Map.of();
		
		try {
			ListenableFuture<Map<String, List<Map<String, Object>>>> future = executor.sendSenseyeXRawImage(event);
			try { data = future.get(); } catch (Exception e) { }
		}catch(Exception e) {
			Pointer frame = (Pointer)event.getExtra().remove("frame");
			if(frame != null)
				FrameUtils.batch_free_frame(frame);
		}
		
		for(Entry<String, List<Map<String, Object>>> entry : data.entrySet()) {
			List<Map<String, Object>> modelResult = entry.getValue();
			if(CollectionUtils.isEmpty(modelResult))
				continue;
			
			for(Map<String, Object> target : modelResult) {
				Map roi = (Map)target.get("detect");
				if(roi == null) 
					continue;
				
				int top = (Integer)roi.get("top");
				int left = (Integer)roi.get("left");
				int width = (Integer)roi.get("width");
				int height = (Integer)roi.get("height");
				
				DrawRect drawRect = new DrawRect(new Rect(left, top, width, height), colors.get(entry.getKey()));
				draw_item.add(drawRect);
				currentRects.add(drawRect);

				String message = "";
				try {
					List<Map> modelAttributes = (List<Map>)target.get("attributes");
					if(CollectionUtils.isEmpty(modelAttributes))
						continue;
					
					String value = (String)modelAttributes.get(0).get("key");
					Number confidence = (Number)modelAttributes.get(0).get("confidence");
					
					message = value + ":" + (int)(confidence.floatValue() * 100);
				}catch(Exception e) { 
					Object confidence = target.get("confidence");
					if(confidence instanceof Number)
						message = String.valueOf((int)(((Number)confidence).floatValue() * 100));
				}
				
				DrawTextEn drawEn = new DrawTextEn(message, font1, colors.get(entry.getKey()), left, top);
				draw_item.add(drawEn);
				currentRects.add(drawEn);
			}
		}
	}

	private static final CvFont font1 = opencv_imgproc.cvFont(1.5, 2);
	private static final CvFont font2 = opencv_imgproc.cvFont(2, 2);
	private static final ConcurrentHashMap<String, CvScalar> colors = new ConcurrentHashMap<String, CvScalar>();
}