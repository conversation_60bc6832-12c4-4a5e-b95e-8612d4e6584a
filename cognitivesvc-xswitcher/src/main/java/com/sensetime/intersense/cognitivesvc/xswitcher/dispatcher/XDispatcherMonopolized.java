package com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher.XDispatcher.Dispatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/** 调度独占显卡 
 */
@Component
@Slf4j
public class XDispatcherMonopolized implements Dispatcher{
	
	@Setter
	private List<XDynamicModel> currentModels;

	@Autowired
	private XModelWatcher watcher;
	
	@Autowired
	private XDynamicModelRepository mapper;
	
	@Override
	public synchronized boolean dispatchMaster(){
		boolean changed = false;
		List<String> steps = new ArrayList<>();
		
		for(XDynamicModel model : currentModels) {
			if(StringUtils.isBlank(model.getMonopolizedGroup())) 
				model.setMonopolizedGroup(null);
			
			if(StringUtils.isBlank(model.getMonopolizedIdentity())) 
				model.setMonopolizedIdentity(null);
			else {
				String distinct = Arrays.stream(model.getMonopolizedIdentity().split(",")).distinct().collect(Collectors.joining(","));
				if(!Objects.equals(distinct, model.getMonopolizedIdentity())) {
					model.setMonopolizedIdentity(distinct);
					changed = true;
					steps.add("Removing duplicates from monopolized identity");
				}
			}
		}

		Holder holder = watcher.getHolder();
		
		/** 删除已经掉线的显卡独占信息 等待重新安排新的独占信息*/
		for(XDynamicModel model : currentModels) {
			if(StringUtils.isBlank(model.getMonopolizedIdentity()))
				continue;
			
			String[] currentIdentities = model.getMonopolizedIdentity().split(",");
			String[] existedIndentities = Arrays.stream(currentIdentities).filter(indentity -> holder.getIdentityMap().containsKey(indentity)).toArray(String[]::new);
			
			if(existedIndentities.length != currentIdentities.length) {
				model.setMonopolizedIdentity(StringUtils.join(existedIndentities, ","));
				changed = true;
				steps.add("Cleaning offline monopolized identities");
			}
		}
		
		/** 删除 已关闭并且独占信息不为空的*/
		for(XDynamicModel model : currentModels) {
			boolean isClosed = (model.getSts() != 0 || StringUtils.isBlank(model.getMonopolizedGroup())) && StringUtils.isNotBlank(model.getMonopolizedIdentity()) ;
			if(!isClosed)
				continue;
			
			model.setMonopolizedIdentity(null);
			changed = true;
			steps.add("Clearing monopolized identity for closed model");
		}
		
		Map<String, List<XDynamicModel>> currentGroupMap = currentModels.stream()
				.filter(model -> 0 == model.getSts() && StringUtils.isNotBlank(model.getMonopolizedGroup()))
				.filter(model -> StringUtils.isNotBlank(model.getMonopolizedGroup()))
				.collect(Collectors.toMap(
						model -> model.getMonopolizedGroup(), 
						model -> List.of(model),
						ListUtils::union
				));
		
		/** 检查组内的所有模型的独占信息是否一致 如果不一致就抹掉*/
		for(Entry<String, List<XDynamicModel>> entry : currentGroupMap.entrySet()){
			List<XDynamicModel> groupModels = entry.getValue();
			
			boolean equals = true;
			
			for(int index = 0; index < groupModels.size(); index ++)
				for(int jndex = index + 1; jndex < groupModels.size(); jndex ++) {
					XDynamicModel left = groupModels.get(index);
					XDynamicModel right = groupModels.get(jndex);
					
					if(!StringUtils.equals(left.getMonopolizedIdentity(), right.getMonopolizedIdentity())) {
						equals = false;
						index = Integer.MAX_VALUE - 1000;
						jndex = Integer.MAX_VALUE - 1000;
					}
				}
			
			if(!equals) {
				changed = true;
				steps.add("Clearing inconsistent monopolized identities in the group");
				
				for(XDynamicModel model : groupModels) 
					model.setMonopolizedIdentity(null);
			}
		}
		
		/** 检查组之间的所有模型的独占信息是否有重复 如果有删掉其中一个*/
		for(Entry<String, List<XDynamicModel>> entry_l : currentGroupMap.entrySet()){
			String monopolizedIdentity_l = entry_l.getValue().get(0).getMonopolizedIdentity();
			if(StringUtils.isBlank(monopolizedIdentity_l))
				continue;
			
			String[] monopolizeds = monopolizedIdentity_l.split(",");
			List<String> equals = new ArrayList<String>();
			
			for(Entry<String, List<XDynamicModel>> entry_r : currentGroupMap.entrySet()){
				if(Objects.equals(entry_l.getKey(), entry_r.getKey())) 
					continue;
				
				String monopolizedIdentity_r = entry_r.getValue().get(0).getMonopolizedIdentity();
				
				for(String monopolized : monopolizeds)
					if(StringUtils.INDEX_NOT_FOUND != StringUtils.indexOf(monopolizedIdentity_r, monopolized)) 
						equals.add(monopolized);
			}
			
			if(!equals.isEmpty()) {
				String newMonopolized = Arrays.stream(monopolizeds).filter(item -> !equals.contains(item)).collect(Collectors.joining(","));
				for(XDynamicModel model : entry_l.getValue()) 
					model.setMonopolizedIdentity(newMonopolized);
			}
		}

		Set<String> monopolizedIds = currentModels.stream().map(model -> model.getMonopolizedIdentity()).filter(StringUtils::isNotBlank).map(id -> id.split(",")).flatMap(Arrays::stream).collect(Collectors.toSet());
		List<String> availableIdentities = holder.getIdentityMap().entrySet().stream().filter(entry -> !monopolizedIds.contains(entry.getKey())).sorted((l, r) -> -l.getValue().compareTo(r.getValue())).map(entry -> entry.getKey()).collect(Collectors.toList());
		
		Set<String> gpuSwitcherIdentities = holder.getInstanceSwitcherList().stream().map(query -> query.getRight().getIdentity()).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
		/** 检查组独占的卡数是与组内最多的模型数量一致 如果不一致就降低*/
		for(Entry<String, List<XDynamicModel>> entry : currentGroupMap.entrySet()){
			int maxCount = entry.getValue().stream().mapToInt(XDynamicModel::getEstimateCount).max().getAsInt();
			
			String groupIdentity = entry.getValue().get(0).getMonopolizedIdentity();
			List<String> groupIdentities = StringUtils.isBlank(groupIdentity) ? Lists.newArrayList() : Lists.newArrayList(groupIdentity.split(","));
			
			if(groupIdentities.size() > maxCount) {
				List<String> shrinkList = shrink(groupIdentities.size() - maxCount, groupIdentities, gpuSwitcherIdentities);
				availableIdentities.addAll(shrinkList);
				groupIdentities.removeAll(shrinkList);
				
				String newGroupIdentity = StringUtils.join(groupIdentities, ",");
				for(XDynamicModel model : entry.getValue())
					model.setMonopolizedIdentity(newGroupIdentity);
				
				changed = true;
				steps.add("Removing duplicate identities between groups 0");
			}else if(groupIdentities.size() < maxCount) {
				List<String> growList = grow(maxCount - groupIdentities.size(), availableIdentities, gpuSwitcherIdentities);
				if(growList.isEmpty())
					continue;
				
				groupIdentities.addAll(growList);
				availableIdentities.removeAll(growList);
				
				String newGroupIdentity = StringUtils.join(groupIdentities, ",");
				for(XDynamicModel model : entry.getValue())
					model.setMonopolizedIdentity(newGroupIdentity);
				
				changed = true;
				steps.add("Removing duplicate identities between groups1 and groups2");
			}
		}
		
		List<String> availableNonGpuSwitcherIdentities = ListUtils.removeAll(availableIdentities, gpuSwitcherIdentities);
		/** 检查独占组所持有的卡上是否其他硬解码 如果有用无其他硬解码的卡来替换*/
		for(Entry<String, List<XDynamicModel>> entry : currentGroupMap.entrySet()){
			String[] monopolizeds = StringUtils.split(entry.getValue().get(0).getMonopolizedIdentity(), ",");
			if(ArrayUtils.isEmpty(monopolizeds) || CollectionUtils.isEmpty(availableNonGpuSwitcherIdentities))
				continue;
			
			List<String> currentAvailableNonGpuSwitcherIdentities = new ArrayList<String>(availableNonGpuSwitcherIdentities);
			currentAvailableNonGpuSwitcherIdentities.removeAll(List.of(monopolizeds));
			
			if(CollectionUtils.isEmpty(currentAvailableNonGpuSwitcherIdentities))
				continue;
			
			for(String monopolized : monopolizeds) {
				if(!gpuSwitcherIdentities.contains(monopolized) || CollectionUtils.isEmpty(currentAvailableNonGpuSwitcherIdentities)) 
					continue;
				
				String nonGpuSwitcherIdentity = currentAvailableNonGpuSwitcherIdentities.remove(0);
				availableNonGpuSwitcherIdentities.remove(nonGpuSwitcherIdentity);
				
				for(XDynamicModel model : entry.getValue())
					model.setMonopolizedIdentity(model.getMonopolizedIdentity().replaceAll(monopolized, nonGpuSwitcherIdentity));
				
				changed = true;
				steps.add("Removing duplicate identities between groups");
			}
		}
		
		if(changed) {
			mapper.saveAll(currentModels);
			mapper.flush();
			log.info("[VideoHandleLog] [dispatch] Monopolization has something changed. Steps: {}", String.join("; ", steps));
		}
		
		return changed;
	}
	
	private List<String> shrink(int count, List<String> groupIdentities, Set<String> gpuSwitcherIdentities) {
		List<String> shrinks = new ArrayList<String>();
		
		for(int index = 0; index < groupIdentities.size() && count > 0; index ++) {
			String id = groupIdentities.get(index);
			if(gpuSwitcherIdentities.contains(id) && !shrinks.contains(id)) {
				shrinks.add(id);
				count --;
			}
		}
		
		for(int index = 0; index < groupIdentities.size() && count > 0; index ++) {
			String id = groupIdentities.get(index);
			if(!shrinks.contains(id)) {
				shrinks.add(id);
				count --;
			}
		}
		
		return shrinks;
	}
	
	private List<String> grow(int count, List<String> availableIdentities, Set<String> gpuSwitcherIdentities) {
		List<String> grows = new ArrayList<String>();
		
		for(int index = 0; index < availableIdentities.size() && count > 0; index ++) {
			String id = availableIdentities.get(index);
			if(!gpuSwitcherIdentities.contains(id) && !grows.contains(id)) {
				grows.add(id);
				count --;
			}
		}
		
		for(int index = 0; index < availableIdentities.size() && count > 0; index ++) {
			String id = availableIdentities.get(index);
			if(!grows.contains(id)) {
				grows.add(id);
				count --;
			}
		}
		
		return grows;
	}
}
