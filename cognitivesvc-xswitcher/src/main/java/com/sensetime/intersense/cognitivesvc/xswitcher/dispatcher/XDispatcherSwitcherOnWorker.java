package com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher.XDispatcher.Dispatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/** 检查是否有高频和低频同时在相同的一路流上
 */
@Component
@Slf4j
public class XDispatcherSwitcherOnWorker implements Dispatcher{
	
	@Autowired
	private XModelWatcher watcher;
	
	/** <instanceId, Set<deviceId>>*/ /** 高频流 **/
	@Setter
	private Map<String, Set<String>> ongoingStreams;
	
	@Setter
	private Map<String, VideoStreamInfra> videoStreamInfraMap;
	/** <instanceId, List<deviceId>>*/ /** 目前跑的低频流 **/
	@Getter
	private Map<String, List<String>> ongoingSwitcherOnWorkers = new HashMap<String, List<String>>();
	
	@Override
	public synchronized boolean dispatchMaster(){
		boolean changed = false;
		
		if(!Utils.instance.senseyexVideoSwitcherOnWorker) {
			changed = !ongoingSwitcherOnWorkers.isEmpty();
			ongoingSwitcherOnWorkers.clear();
			return changed;
		}
		
		Holder infoHolder = watcher.getHolder();
        /** 高频流 **/
		Set<String> ongoingDeviceIds = ongoingStreams.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
		/** 高低频交集 **/
		List<String> wantedDeviceIds = (List<String>)CollectionUtils.intersection(infoHolder.getSwitcherLowRateMap().keySet(), infoHolder.getSwitcherHighRateMap().keySet());
		Iterator<String> its = wantedDeviceIds.iterator();
		while(its.hasNext()) { 
			String deviceId = its.next();
			
			if(!ongoingDeviceIds.contains(deviceId)) {
				its.remove();
				continue;
			}
			
			VideoStreamInfra infra = videoStreamInfraMap.get(deviceId);
			if(infra == null || "0".equals(infra.getRtmpOn())) {
				its.remove();
				continue;
			}
		}
		
		//查找需要删除的
		Iterator<Entry<String, List<String>>> itMap = ongoingSwitcherOnWorkers.entrySet().iterator();
		while(itMap.hasNext()) {
			Entry<String, List<String>> entry = itMap.next();
			if(!ongoingStreams.containsKey(entry.getKey())) {
				itMap.remove();
				log.info("[VideoHandleLog] [dispatch] switcher on worker, remove instance[" + entry.getKey() + "] due to lost.");
				changed = true;
				continue;
			}
			
			Iterator<String> itList = entry.getValue().iterator();
			while(itList.hasNext()) {
				String deviceId = itList.next();
				
				if(!wantedDeviceIds.contains(deviceId)) {
					changed = true;
					log.info("[VideoHandleLog] [dispatch] switcher on worker, remove device[" + deviceId + "] from worker[" + entry.getKey() + "].");
					itList.remove();
				}else
					wantedDeviceIds.remove(deviceId);
			}
			
			if(CollectionUtils.isEmpty(entry.getValue()))
				itMap.remove();
		}
		
		//查找需要新建的
		for(String wantedDeviceId : wantedDeviceIds) {
			Entry<String, Set<String>> targetEntry = ongoingStreams.entrySet().stream()
					.filter(entry -> entry.getValue().contains(wantedDeviceId))
					.min((l, r) -> Integer.compare(l.getValue().size(), r.getValue().size()))
					.get();
			
			List<String> switcherOnWorkers = ongoingSwitcherOnWorkers.get(targetEntry.getKey());
			if(switcherOnWorkers == null) {
				switcherOnWorkers = new ArrayList<String>();
				ongoingSwitcherOnWorkers.put(targetEntry.getKey(), switcherOnWorkers);
			}
			
			switcherOnWorkers.add(wantedDeviceId);
			log.info("[VideoHandleLog] [dispatch] switcher on worker, add device[" + wantedDeviceId + "] into worker[" + targetEntry.getKey() + "].");
			changed = true;
		}
		
		return changed;
	}
}
