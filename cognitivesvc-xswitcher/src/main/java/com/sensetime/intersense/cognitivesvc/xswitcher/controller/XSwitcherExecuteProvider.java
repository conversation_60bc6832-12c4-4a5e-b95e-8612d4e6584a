package com.sensetime.intersense.cognitivesvc.xswitcher.controller;

import com.google.common.collect.Maps;
import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawVideo;
import com.sensetime.intersense.cognitivesvc.server.entities.ExecModelResultEntity;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelExecuter;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController("xSwitcherExecuteProvider")
@RequestMapping(value = "/cognitive/xswitcher/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "XSwitcherExecuteProvider", description = "device xswitcher execute controller")
@SuppressWarnings("rawtypes")
@CrossOrigin(origins = "*")
@Slf4j
public class XSwitcherExecuteProvider extends BaseProvider {
    
    @Value("${intersense.executeModel.batchSizeLimit:10}")
    private Integer execModelBatchSizeLimit;
    
    @Autowired
    private XModelExecuter xExecuter;
    
    
    /**
     * 单图跑模型接口；
     *
     * @param message
     * @return
     * @throws Exception
     */
    @Operation(summary = "跑模型 - 仅支持单图", method = "POST")
    @RequestMapping(value = "/execute/model", method = RequestMethod.POST)
    public BaseRes executeModelImage(@RequestBody SenseyexRawImage message) throws Exception {
        if (ArrayUtils.isEmpty(message.getProcessors()))
            throw new RuntimeException("processors should not be empty.");
        
        String images[] = message.getImages();
        if (ArrayUtils.isEmpty(images))
            throw new RuntimeException("image is empty.");
        
        if (message.getCapturedTime() == null)
            message.setCapturedTime(System.currentTimeMillis());
        
        if (message.getExtra() == null)
            message.setExtra(Maps.newHashMap());
        message.getExtra().put("response", true);
        
        ListenableFuture<Map<String, List<Map<String, Object>>>> future = xExecuter.sendSenseyeXRawImage(message);
        return BaseRes.success(future.get());
    }
    
    
    /**
     * 多图跑模型接口
     * <p>
     * 【历史背景】
     * 原来的单图跑模型接口，实际上只支持传入一个图片。 原因是 底层执行模型的实现，把传入入参的图片数组维度和执行层面的 batchsize 的概念混淆了，设计实现有问题；
     * <p>
     * 综合以下因素：
     * - 底层改动影响范围太大
     * - 原有的API 第三方有调用
     * - 当前迭代周期有限
     * <p>
     * 暂不考虑重构底层，复制一份新的API 以满足需求
     *
     * @param message
     * @return
     * @throws Exception
     */
    @Operation(summary = "跑模型 - 支持传入多图", method = "POST")
    @RequestMapping(value = "/execute/model/batch", method = RequestMethod.POST)
    public BaseRes executeModelImageBatch(@RequestBody SenseyexRawImage message
            , @RequestParam(required = false, defaultValue = "false") Boolean ignoreLimit) {
        if (ArrayUtils.isEmpty(message.getProcessors()))
            throw new BusinessException("4001", "param error");
        
        String images[] = message.getImages();
        if (ArrayUtils.isEmpty(images))
            throw new BusinessException("3004", "length should not be 0");
        if (!ignoreLimit && images.length > execModelBatchSizeLimit)
            throw new BusinessException("3011", "length over limit");
        
        if (message.getCapturedTime() == null)
            message.setCapturedTime(System.currentTimeMillis());
        
        if (message.getExtra() == null)
            message.setExtra(Maps.newHashMap());
        message.getExtra().put("response", true);
        
        
        // 多个图片消息转换为 单个图片的多组消息
        List<SenseyexRawImage> messageList = new ArrayList<>();
        for (int i = 0; i < images.length; i++) {
            SenseyexRawImage singleImageMessage = new SenseyexRawImage();
            BeanUtils.copyProperties(message, singleImageMessage);
            singleImageMessage.setImages(new String[]{images[i]});
            messageList.add(singleImageMessage);
        }
        
        // 每个消息单独发送，得到所有图片处理的结果
        List<ExecModelResultEntity> allImageModelResult = new ArrayList<>();
        messageList.parallelStream().map(m -> {
            ListenableFuture<Map<String, List<Map<String, Object>>>> future = xExecuter.sendSenseyeXRawImage(m);
            List<ExecModelResultEntity> singleImageModelResult = new ArrayList<>();
            
            try {
                singleImageModelResult = future.get().entrySet()
                        .stream()
                        .map(e -> ExecModelResultEntity.builder()
                                .image(m.getImages()[0])
                                .processor(e.getKey())
                                .result(e.getValue())
                                .build()
                        ).collect(Collectors.toList());
            } catch (Exception e) {
                log.warn(">>> [batch exec model] single image result convert failed! img=" + m.getImages()[0]);
            }
            return singleImageModelResult;
        }).forEach(l -> allImageModelResult.addAll(l));
        
        // 返回结果处理：所有图片处理结果，按processor分类，以图片为单位返回
        Map<String, List<ExecModelResultEntity>> result = allImageModelResult.stream()
                .collect(Collectors.groupingBy(ExecModelResultEntity::getProcessor));
        
        return BaseRes.success(result);
    }


    @Operation(summary = "发图片消息", method = "POST")
    @RequestMapping(value = "/send/senseyex/raw/image/input", method = RequestMethod.POST)
    public void sendSenseyexRawImage(@RequestBody SenseyexRawImage message) throws Exception {
        String images[] = message.getImages();
        if (ArrayUtils.isEmpty(images) || StringUtils.isBlank(message.getDeviceId()))
            throw new RuntimeException("deviceId or image is empty.");
        
        if (message.getCapturedTime() == null)
            message.setCapturedTime(System.currentTimeMillis());
        
        if (message.getExtra() == null)
            message.setExtra(Maps.newHashMap());
        
        xExecuter.sendSenseyeXRawImage(message);
    }

    @Operation(summary = "发视频消息", method = "POST")
    @RequestMapping(value = "/send/senseyex/raw/video/input", method = RequestMethod.POST)
    public void sendSenseyexRawVideo(@RequestBody @Payload SenseyexRawVideo message) throws Exception {
        if (StringUtils.isBlank(message.getDeviceId()))
            throw new RuntimeException("deviceId is empty.");
        
        File file = new File(message.getVideo());
        if (!file.exists())
            throw new RuntimeException("video[" + message.getVideo() + "] does not exist.");
        else if (file.length() > 8 * 1024 * 1024 * 1024L)
            throw new RuntimeException("video[" + message.getVideo() + "] is too big, should not be larger than 8192MB.");
        
        if (message.getCapturedTime() == null)
            message.setCapturedTime(System.currentTimeMillis());
        
        xExecuter.sendSenseyeXRawVideo(message);
    }
}
