package com.sensetime.intersense.cognitivesvc.xswitcher.executer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult.Model;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawVideo;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.XSwitcher;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamXswitcher;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.feign.PolicyMonitorFeign;
import com.sensetime.intersense.cognitivesvc.server.feign.StrangerFeign;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.stream.video.service.RebalanceService;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sun.jna.Pointer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@SuppressWarnings("rawtypes")
public class XBusinessExecuter {

	@Autowired
	private XDynamicModelRepository xDynamicModelRepository;

	@Autowired
	private ApplicationContext context;

	private static final Logger log = LoggerFactory.getLogger(XBusinessExecuter.class);

	public Map<String, Object>  addProcessor(Map<String, Object> originProcess){
		String processors = (String)originProcess.get("processor");
		List<XDynamicModel> modelList = xDynamicModelRepository.findAllById(Lists.newArrayList(processors));
		if(modelList!=null && modelList.size()>0){
			Integer modelSource = modelList.get(0).getModelSource();
			if(modelSource!=null){
				Integer modelType = modelList.get(0).getModelType();
				originProcess.put("modelSource",modelSource);
				originProcess.put("modelType",modelType);
			}

		}
		PolicyMonitorFeign.TargetDefinitionQueryReq req = new PolicyMonitorFeign.TargetDefinitionQueryReq();
		req.setModelSource(Arrays.asList(1));
		req.setTargetTypeList(Arrays.asList("city"));
		try {
			BaseRes<List<PolicyMonitorFeign.TagetListRes>>  policyBaseRes =  context.getBean(PolicyMonitorFeign.class).queryListOfDictionary(req);
			List<PolicyMonitorFeign.TagetListRes> definitionList = JSON.parseArray(JSON.toJSON(policyBaseRes.getData()).toString(),PolicyMonitorFeign.TagetListRes.class);
			definitionList.forEach(definition->{
				definition.getTagetDictionarys().forEach(dictionary->{
					String targetName = dictionary.getTargetName();
					if(targetName.endsWith(processors)){
						originProcess.put("targetOptions",dictionary.getTargetOptions());
					}
				});
			});
		}catch (Exception e){
			log.error(e.getMessage());
		}

		return originProcess;
	}

}
