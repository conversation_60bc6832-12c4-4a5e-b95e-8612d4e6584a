package com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult.Model;
import com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher.XDispatcher.ModelDispatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class XDispatcherCpuModel extends ModelDispatcher{
	
	@Override
	public synchronized boolean dispatchMaster(){
		Set<String> cpuModelNames = allModels.stream().map(model -> model.getAnnotatorName()).collect(Collectors.toSet());
		
		ongoingModels = watcher.getHolder().getInstanceWorkerList()
							   .stream()
							   .collect(Collectors.toMap(
									pair -> pair.getLeft().getInstanceId(), 
									pair -> pair.getRight().getDynamicModels().stream().filter(model -> cpuModelNames.contains(model.getAnnotatorName())).map(Model::getAnnotatorName).collect(Collectors.toList())
							   ));
		
		Holder holder = watcher.getHolder();
		
		//[<instance, instanceEnv>]
		List<Pair<ServiceInstance, QueryWorkerResult>> instanceList = holder.getInstanceWorkerList()
					   .stream()
					   .filter(instance -> instance.getRight() != QueryWorkerResult.ERROR)
					   .collect(Collectors.toList());
		
		//[instanceId, <instance, instanceEnv>]
		Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap = new HashMap<String, Pair<ServiceInstance, QueryWorkerResult>>();

		//[annotatorName, <instance, instanceEnv>]
		Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap = new HashMap<String, List<Pair<ServiceInstance, QueryWorkerResult>>>();
		
		for(Pair<ServiceInstance, QueryWorkerResult> pair : instanceList) {
			instanceIdMap.put(pair.getLeft().getInstanceId(), pair);
			
			for(Model model : pair.getRight().getDynamicModels()) {
				if(!cpuModelNames.contains(model.getAnnotatorName()))
					continue;
				
				List<Pair<ServiceInstance, QueryWorkerResult>> list = annotatorNameMap.get(model.getAnnotatorName());
				if(list == null) {
					list = new ArrayList<Pair<ServiceInstance, QueryWorkerResult>>();
					annotatorNameMap.put(model.getAnnotatorName(), list);
				}
				list.add(pair);
			}
		}

		boolean isChanged = false;
		
		Map<String, XDynamicModel> currentXDynamicModelMap = currentModels.stream().collect(Collectors.toMap(XDynamicModel::getAnnotatorName, model -> model));
		
		//遍历一遍所有节点 看看有没有不在列表内的模型在跑 , 并且修正map
		isChanged |= dispatchRemove(annotatorNameMap, currentXDynamicModelMap);
		
		if(currentXDynamicModelMap.isEmpty())
			return isChanged;
		
		//看一下是否启动了足够多的实例或者超标量的实例 多退少补
		isChanged |= dispatchSufficient(annotatorNameMap, instanceList, currentXDynamicModelMap);
		
		if(isChanged)
			return isChanged;
		
		int createCount = 0;
		for(Entry<String, XDynamicModel> entry : currentXDynamicModelMap.entrySet()) {
			List<Pair<ServiceInstance, QueryWorkerResult>> annotatorInstanceList = annotatorNameMap.get(entry.getValue().getAnnotatorName());
			if(CollectionUtils.isNotEmpty(annotatorInstanceList))
				continue;
			
			if(++ createCount > 2)
				break;
			
			isChanged |= dispatchCreate(annotatorNameMap, instanceList, entry.getValue());
		}
		
		return isChanged;
	}
	
	/** 查一下当前环境里不需要跑的模型都在哪 全部杀掉 */
	private boolean dispatchRemove(Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap, Map<String, XDynamicModel> currentModelEntityMap) {
		List<String> removeAnnotatorNames = annotatorNameMap.keySet().stream().filter(annotatorName -> !currentModelEntityMap.containsKey(annotatorName)).collect(Collectors.toList());
		if(removeAnnotatorNames.isEmpty()) 
			return false;
		
		for(String removeAnnotatorName : removeAnnotatorNames) {
			List<Pair<ServiceInstance, QueryWorkerResult>> removeingInstances = annotatorNameMap.remove(removeAnnotatorName);
			for(Pair<ServiceInstance, QueryWorkerResult> pair : removeingInstances) {
				log.info("[VideoHandleLog] [dispatch] remove model[" + removeAnnotatorName + "] from (" + pair.getLeft().getInstanceId() + ")");
				ongoingModels.get(pair.getLeft().getInstanceId()).remove(removeAnnotatorName);
			}
		}
		
		return true;
	}
	
	/** 看一下是否启动了足够多的副本数 或者杀掉超量的副本数 多退少补 */
	private boolean dispatchSufficient(Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap, List<Pair<ServiceInstance, QueryWorkerResult>> instanceList, Map<String, XDynamicModel> currentModelEntityMap) {
		boolean isChanged = false;
		
		for(Entry<String, List<Pair<ServiceInstance, QueryWorkerResult>>> entry : annotatorNameMap.entrySet()) {
			XDynamicModel entity = currentModelEntityMap.get(entry.getKey());
			int targetCount = entity.getEstimateCount();
			
			List<Pair<ServiceInstance, QueryWorkerResult>> modelInstanceList = entry.getValue();
			if(modelInstanceList.size() == targetCount) //数量相同 不干活
				continue;
			
			if(modelInstanceList.size() < targetCount) {//数量太少 增加几个
				int grow = targetCount - modelInstanceList.size();
				
				List<Pair<ServiceInstance, QueryWorkerResult>> optionInstances = new ArrayList<Pair<ServiceInstance, QueryWorkerResult>>(instanceList);
				optionInstances.removeAll(modelInstanceList);
				
				sortInstanceByModelCount(optionInstances);
				
				List<Pair<ServiceInstance, QueryWorkerResult>> growInstances = optionInstances.stream().limit(grow).collect(Collectors.toList());
				modelInstanceList.addAll(growInstances);
				
				for(Pair<ServiceInstance, QueryWorkerResult> pair : growInstances) {
					log.info("[VideoHandleLog] [dispatch] sufficient matching add model[" + entity.getAnnotatorName() + "] to (" + pair.getLeft().getInstanceId() + ")");
					isChanged = true;
					ongoingModels.get(pair.getLeft().getInstanceId()).add(entity.getAnnotatorName());
				}
			}else if(modelInstanceList.size() > targetCount) {//数量太多 砍掉几个
				int shrink = modelInstanceList.size() - targetCount;

				List<Pair<ServiceInstance, QueryWorkerResult>> shrinkInstances = new ArrayList<Pair<ServiceInstance, QueryWorkerResult>>(modelInstanceList);
				sortInstanceByModelCount(shrinkInstances);
				
				shrinkInstances = shrinkInstances.subList(shrinkInstances.size() - shrink, shrinkInstances.size());
				modelInstanceList.removeAll(shrinkInstances);
				
				for(Pair<ServiceInstance, QueryWorkerResult> pair : shrinkInstances) {
					log.info("[VideoHandleLog] [dispatch] sufficient matching remove model[" + entity.getAnnotatorName() + "] from (" + pair.getLeft().getInstanceId() + ")");
					isChanged = true;
					ongoingModels.get(pair.getLeft().getInstanceId()).remove(entity.getAnnotatorName());
				}
			}
		}
		
		return isChanged;
	}
	
	/** 创建新的模型 */
	private boolean dispatchCreate(Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorNameMap, List<Pair<ServiceInstance, QueryWorkerResult>> instanceList, XDynamicModel modelToCreate) {
		boolean isChanged = false;

		sortInstanceByModelCount(instanceList);
		
		List<Pair<ServiceInstance, QueryWorkerResult>> growInstances = instanceList.stream()
				.limit(modelToCreate.getEstimateCount())
				.collect(Collectors.toList());
		
		annotatorNameMap.put(modelToCreate.getAnnotatorName(), growInstances);
		
		for(Pair<ServiceInstance, QueryWorkerResult> pair : growInstances) {
			log.info("[VideoHandleLog] [dispatch] create matching add model[" + modelToCreate.getAnnotatorName() + "] to (" + pair.getLeft().getInstanceId() + ")");
			isChanged = true;
			ongoingModels.get(pair.getLeft().getInstanceId()).add(modelToCreate.getAnnotatorName());
		}
		
		return isChanged;
	}
	
	private void sortInstanceByModelCount(List<Pair<ServiceInstance, QueryWorkerResult>> instances) {
		Collections.sort(instances, (l, r) -> {
			if(l.getRight().gpuInstance() && !r.getRight().gpuInstance())
				return 1;
			else if(!l.getRight().gpuInstance() && r.getRight().gpuInstance()) 
				return -1;
			
			return Integer.compare(l.getRight().getDynamicModels().size(), r.getRight().getDynamicModels().size());
		});
	}
}
