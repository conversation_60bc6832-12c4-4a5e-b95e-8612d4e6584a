package com.sensetime.intersense.cognitivesvc.xswitcher.executer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.lib.clientlib.response.BaseRes;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.netflix.eureka.EurekaServiceInstance;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.netflix.appinfo.InstanceInfo.InstanceStatus;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamXswitcher;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult.Model;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamXswitcherRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QuerySwitcherResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.XSwitcher;

import lombok.Builder;
import lombok.Getter;

@Slf4j
@Component
public class XModelWatcher {
	
	/** <instanceId, errorCount> */
	private final Map<String, Integer> instanceErrorCountMap = new ConcurrentHashMap<String, Integer>();
	
	/** <instanceId, errorCount> */
	private final Map<String, Integer> switcherErrorCountMap = new ConcurrentHashMap<String, Integer>();
	
	@Autowired
	private DiscoveryClient discoveryClient;

	@Autowired
	private VideoStreamXswitcherRepository videoStreamXswitcherRepository;
	
	@Autowired
	private VideoStreamInfraRepository videoStreamInfraRepository;

	@Autowired
	private XDynamicModelRepository xDynamicModelRepository;

	@Getter
	@Setter
	private volatile Holder holder = Holder.builder().build();


	@Value("${spring.application.name}")
	private String appName;

	@Autowired
	private Broadcaster broadcastService;
	
	public synchronized void refresh(boolean isMaster) {
		List<Pair<ServiceInstance, QuerySwitcherResult>> instanceSwitcherList = discoveryClient.getServices()
				   .stream()
				   .map(discoveryClient::getInstances)
				   .flatMap(List::stream)
				   .filter(instance -> "true".equals(instance.getMetadata().get("isXSwitcher")))
				   .sorted((l, r) -> l.getInstanceId().compareTo(r.getInstanceId()))
				   .parallel()
				   .filter(instance -> {
						if(!(instance instanceof EurekaServiceInstance)) 
							return true;
						
						EurekaServiceInstance eurekaInstance = (EurekaServiceInstance)instance;
						return eurekaInstance.getInstanceInfo().getStatus() == InstanceStatus.UP;
					})
					.map(instance -> {
						 try {
						 	QuerySwitcherResult query = null;
						 	if(Utils.instance.instanceLocalSwitch==1){
								 query = RestUtils.restTemplate60000ms.getForObject("http://" + "localhost" + ":" + instance.getPort() + "/cognitive/xswitcher/env/query/switcher", QuerySwitcherResult.class);
							}else{
								 query = RestUtils.restTemplate60000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xswitcher/env/query/switcher", QuerySwitcherResult.class);
							}

							 return new ImmutablePair<ServiceInstance, QuerySwitcherResult>(instance, query);
						 }catch(Exception e) {
							 Integer count = switcherErrorCountMap.getOrDefault(instance.getInstanceId(), 0);
							 
							 if(count >= 10) {
								 switcherErrorCountMap.remove(instance.getInstanceId());
								 return new ImmutablePair<ServiceInstance, QuerySwitcherResult>(instance, QuerySwitcherResult.ERROR);
							 }else {
								 switcherErrorCountMap.put(instance.getInstanceId(), ++count);
								 
								 Pair<ServiceInstance, QuerySwitcherResult> lastRoundPair = holder.getInstanceSwitcherList()
										 .stream()
										 .filter(pair -> pair.getLeft().getInstanceId().equals(instance.getInstanceId()))
										 .findAny()
										 .orElse(null);
								 
								 if(lastRoundPair == null)
									 return new ImmutablePair<ServiceInstance, QuerySwitcherResult>(instance, QuerySwitcherResult.ERROR);
								 else
									 return new ImmutablePair<ServiceInstance, QuerySwitcherResult>(instance, lastRoundPair.getRight());
							 }
						 }
					})
					.collect(Collectors.toList());
		
		List<ServiceInstance> instances = discoveryClient.getServices()
				   .stream()
				   .map(discoveryClient::getInstances)
				   .flatMap(List::stream)
				   .filter(instance -> "true".equals(instance.getMetadata().get("isXWorker")))
				   .sorted((l, r) -> l.getInstanceId().compareTo(r.getInstanceId()))
				   .collect(Collectors.toList());

		log.info("isMasterSwitcher={}", isMaster);
		List<Pair<ServiceInstance, QueryWorkerResult>> instanceWorkerList = instances.parallelStream()
				.filter(instance -> {
					if(!(instance instanceof EurekaServiceInstance)) 
						return true;
					
					EurekaServiceInstance eurekaInstance = (EurekaServiceInstance)instance;
					return eurekaInstance.getInstanceInfo().getStatus() == InstanceStatus.UP;
				})
				.map(instance -> {
					 try {
						 QueryWorkerResult query = RestUtils.restTemplate60000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/heart/beat/query?isMaster=" + isMaster, QueryWorkerResult.class);
						 return new ImmutablePair<ServiceInstance, QueryWorkerResult>(instance, query);
					 }catch(Exception e) {
						 Integer count = instanceErrorCountMap.getOrDefault(instance.getInstanceId(), 0);

						 log.warn("[xModelWatcher] query error={}, count={}, instance:{}", e.getMessage(), count, instance.getInstanceId());
						 if(count >= 10) {
							 instanceErrorCountMap.remove(instance.getInstanceId());
							 return new ImmutablePair<ServiceInstance, QueryWorkerResult>(instance, QueryWorkerResult.ERROR);
						 }else {
							 instanceErrorCountMap.put(instance.getInstanceId(), ++count);
							 
							 Pair<ServiceInstance, QueryWorkerResult> lastRoundPair = holder.getInstanceWorkerList()
									 .stream()
									 .filter(pair -> pair.getLeft().getInstanceId().equals(instance.getInstanceId()))
									 .findAny()
									 .orElse(null);
							 
							 if(lastRoundPair == null)
								 return new ImmutablePair<ServiceInstance, QueryWorkerResult>(instance, QueryWorkerResult.ERROR);
							 else
								 return new ImmutablePair<ServiceInstance, QueryWorkerResult>(instance, lastRoundPair.getRight());
						 }
					 }
				})
				.collect(Collectors.toList());
		
		Map<String, Model> annotatorModelMap = new HashMap<String, Model>();
		for(Pair<ServiceInstance, QueryWorkerResult> instanceModel : instanceWorkerList)
			for(Model model : instanceModel.getRight().getDynamicModels())
				annotatorModelMap.put(model.getAnnotatorName(), model);
		
		Map<String, List<ServiceInstance>> modelDynamicInstanceMap = new HashMap<String ,List<ServiceInstance>>();
		for(Pair<ServiceInstance, QueryWorkerResult> instanceModel : instanceWorkerList) {
			for(Model model : instanceModel.getRight().getDynamicModels()) {
				List<ServiceInstance> list = modelDynamicInstanceMap.get(model.getAnnotatorName());
				if(list == null) {
					list = new ArrayList<ServiceInstance>();
					modelDynamicInstanceMap.put(model.getAnnotatorName(), list);
				}
				
				list.add(instanceModel.getLeft());
			}
		}
		
		modelDynamicInstanceMap.values().forEach(instanceList -> Collections.sort(instanceList, (l, r) -> l.getInstanceId().compareTo(r.getInstanceId())));
		
		Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorMap = new HashMap<String, List<Pair<ServiceInstance, QueryWorkerResult>>>();
		for(Pair<ServiceInstance, QueryWorkerResult> pair : instanceWorkerList) {
			for(Model model : pair.getRight().getDynamicModels()) {
				List<Pair<ServiceInstance, QueryWorkerResult>> list = annotatorMap.get(model.getAnnotatorName());
				if(list == null) {
					list = new ArrayList<Pair<ServiceInstance, QueryWorkerResult>>();
					annotatorMap.put(model.getAnnotatorName(), list);
				}
				list.add(pair);
			}
		}
		
		Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap = instanceWorkerList.stream().collect(Collectors.toMap(pair -> pair.getLeft().getInstanceId(), pair -> pair));
		List<Pair<ServiceInstance, QueryWorkerResult>> instanceAvailableModelList = instanceWorkerList.stream().filter(pair -> pair.getRight() != QueryWorkerResult.ERROR).collect(Collectors.toList());

		
		MutableTriple<Double, Double, Double> env = new MutableTriple<Double, Double, Double>(Double.MIN_VALUE, Double.MAX_VALUE, 0d);//<numMax, numMin, numAvg>
		for(Pair<ServiceInstance, QueryWorkerResult> instance : instanceAvailableModelList) {
			if(instance.getRight() == QueryWorkerResult.ERROR || !instance.getRight().gpuInstance()) 
				continue;
			
			double num = instance.getRight().getDynamicModels().size();
			
			env.setRight(env.getRight() + num / instanceAvailableModelList.size());
			
			if(num > env.getLeft())
				env.setLeft(num);
			
			if(num < env.getMiddle())
				env.setMiddle(num);
		}
		
		Map<ServiceInstance, Double> scoreMap = instanceAvailableModelList.stream().filter(pair -> pair.getRight().gpuInstance()).collect(Collectors.toMap(Pair::getLeft, pair -> score(pair.getRight(), env)));
		
		Map<ServiceInstance, Integer> avgGpuMap = instanceAvailableModelList.stream().filter(pair -> pair.getRight().gpuInstance()).collect(Collectors.toMap(Pair::getLeft, pair -> pair.getRight().getAvgGpuRate()));
		
		Map<String, String> identityMap = instanceWorkerList.stream().map(model -> model.getRight()).filter(model -> model.gpuInstance()).collect(Collectors.toMap(model -> model.getIdentity(), model -> model.getGpuId()));

		Map<String,Integer> processorSaveImgMap = xDynamicModelRepository.findAll().stream().
				collect(Collectors.toMap(
						XDynamicModel::getAnnotatorName,
						XDynamicModel::getImgSaveTag
				));

		Map<String, List<XSwitcher>> switcherMap = videoStreamXswitcherRepository.findAll().stream()
				.collect(Collectors.toMap(
					VideoStreamXswitcher::getDeviceId, 
					switcher -> Lists.newArrayList(new XSwitcher(switcher,processorSaveImgMap)),
					ListUtils::union
				));
		
		Map<String, XSwitcher> switcherLowRateMap = new HashMap<String, XSwitcher>();
		for(Entry<String, List<XSwitcher>> entry : switcherMap.entrySet()) {
			List<XSwitcher> lowSwitchers  = entry.getValue().stream().filter(switcher -> !switcher.isHighRate()).collect(Collectors.toList());
			if(CollectionUtils.isEmpty(lowSwitchers)) //device表里面没有的  也要加入到这里 因为可能有外部消息来执行
				continue;
			
			boolean isMultiplex = !lowSwitchers.stream().filter(s -> !s.isMultiplex()).findAny().isPresent();
			XSwitcher item = new XSwitcher(false, isMultiplex, new HashMap<String, Processor>(), "");
			for(XSwitcher swticher : lowSwitchers)
				item.getProcessors().putAll(swticher.getProcessors());
			
			switcherLowRateMap.put(entry.getKey(), item);
		}

		Set<String> deviceIds = videoStreamInfraRepository.queryDeviceidsBySts(0);
		Map<String, XSwitcher> switcherHighRateMap = new HashMap<String, XSwitcher>();
		for(Entry<String, List<XSwitcher>> entry : switcherMap.entrySet()) {
			if(!deviceIds.contains(entry.getKey()))//device表里面没有的  就不add到高频里面了
				continue;
			
			List<XSwitcher> highSwitchers = entry.getValue().stream().filter(switcher -> switcher.isHighRate()).collect(Collectors.toList());
			if(CollectionUtils.isEmpty(highSwitchers))
				continue;
			
			boolean isMultiplex = !highSwitchers.stream().filter(s -> !s.isMultiplex()).findAny().isPresent();
			XSwitcher item = new XSwitcher(true, isMultiplex, new HashMap<String, Processor>(), "");
			for(XSwitcher swticher : highSwitchers)
				item.getProcessors().putAll(swticher.getProcessors());
			
			switcherHighRateMap.put(entry.getKey(), item);
		}
		
		Map<String, List<XSwitcher>> switcherMapNew = new HashMap<>();
		for(String deviceId : switcherHighRateMap.keySet()) {
			List<XSwitcher> list = new ArrayList<>();
			list.add(switcherHighRateMap.get(deviceId));
			switcherMapNew.put(deviceId, list);
		}

		// 计算所有设备的分辨率倍数
		// TODO 注意性能问题
		Map<String, Float> deviceResolutionMultiplierMap = new HashMap<>();
		videoStreamInfraRepository.findAll().forEach(device -> {
            // 使用getStreamMultiplier()方法从processors JSON字段中解析出stream_multiplier值
            Float multiplier = device.getStreamMultiplier();

            // 大于0时直接使用JSON中配置的倍数
            if (multiplier != null && multiplier > 0) {
                deviceResolutionMultiplierMap.put(device.getDeviceId(), multiplier);
                log.info("设备[{}]从processors JSON中解析到倍数为{}",
                    device.getDeviceId(), multiplier);
            } else {
                // 否则根据分辨率计算倍数
                // 基准分辨率 1920*1080，计算相对倍数
				// 要按保留1位小数向上取整  如果是780* 800
                Float resolutionMultiplier = 1.0f;
                if (device.getRtspWidth() != null && device.getRtspHeight() != null &&
                    device.getRtspWidth() > 0 && device.getRtspHeight() > 0) {
                    double pixelRatio = (double)(device.getRtspWidth() * device.getRtspHeight()) / (1920.0 * 1080.0);
                    resolutionMultiplier = (float)(Math.ceil(pixelRatio * 10) / 10.0);
					
                    if (resolutionMultiplier <= 0.0f) {
                        resolutionMultiplier = 1.0f;
                    }
                    // 记录根据分辨率计算的倍数（非1的情况）
                    if (resolutionMultiplier != 1.0f) {
                        log.info("设备[{}]按分辨率计算倍数为{}，分辨率：{}x{}",
                            device.getDeviceId(), resolutionMultiplier, device.getRtspWidth(), device.getRtspHeight());
                    }
                }
                deviceResolutionMultiplierMap.put(device.getDeviceId(), resolutionMultiplier);
            }
        });

		holder =  Holder.builder()
						.instances(Collections.unmodifiableList(instances))
						.instanceWorkerList(Collections.unmodifiableList(instanceWorkerList))
						.instanceSwitcherList(Collections.unmodifiableList(instanceSwitcherList))
						.modelDynamicInstanceMap(Collections.unmodifiableMap(modelDynamicInstanceMap))
						.annotatorMap(Collections.unmodifiableMap(annotatorMap))
						.instanceIdMap(Collections.unmodifiableMap(instanceIdMap))
						.scoreMap(Collections.unmodifiableMap(scoreMap))
						.avgGpuMap(Collections.unmodifiableMap(avgGpuMap))
						.identityMap(Collections.unmodifiableMap(identityMap))
						.switcherLowRateMap(Collections.unmodifiableMap(switcherLowRateMap))
						.switcherHighRateMap(Collections.unmodifiableMap(switcherHighRateMap))
						.switcherMap(Collections.unmodifiableMap(switcherMap))
						.annotatorModelMap(Collections.unmodifiableMap(annotatorModelMap))
						.deviceResolutionMultiplierMap(Collections.unmodifiableMap(deviceResolutionMultiplierMap))
						.build();


		log.info("broadcastService");
		//broadcastService.postForObject(appName, "/cognitive/xswitcher/env/pipelineChange", Map.of("deviceId", holder), Object.class);
	}

	/** 计算该节点的评分 数字越大代表越空闲*/
	private static double score(QueryWorkerResult modelResult, Triple<Double, Double, Double>/*<numMax, numMin, numAvg>*/ env) {
		if(modelResult == QueryWorkerResult.ERROR) 
			return -1;
		
		double rate = modelResult.getAvgGpuRate();
		double mem = modelResult.getAvailableGpuMemory();
		double num = modelResult.getDynamicModels().size();
		
		double rateScore = 100;
		if(rate >= 90)
			rateScore = 0;
		else if(rate > 40)
			rateScore -= 100 * (1 - Math.pow((90 - rate) / 50, 0.8));
		
		double memScore = 100;
		if(mem <= 1024) 
			memScore = 0;
		else if(mem < 8192)
			memScore -= 100 * Math.pow((8192 - mem) / 7168, 1.2);
		
		double numScore = 100;
		if(env.getLeft() != env.getRight() && env.getMiddle() != env.getRight() && env.getLeft() - env.getMiddle() > 1)
			if(num > env.getRight())
				numScore -= 40 + 60 * (num - env.getRight()) / (env.getLeft() - env.getRight());
			else
				numScore -= 40 * (num - env.getMiddle()) / (env.getRight() - env.getMiddle());
		
		return numScore * 0.25 + memScore * 0.25 + rateScore * 0.5;
	}
	
	@Getter
	@Builder
	public static class Holder{
		/** <instances> */
		@Builder.Default
		private List<ServiceInstance> instances = new ArrayList<ServiceInstance>();
		/**[instance, SwitcherEnv] */
		@Builder.Default
		private List<Pair<ServiceInstance, QuerySwitcherResult>> instanceSwitcherList = new ArrayList<Pair<ServiceInstance, QuerySwitcherResult>>();
		/**<instance, WorkerEnv> */
		@Builder.Default
		private List<Pair<ServiceInstance, QueryWorkerResult>> instanceWorkerList = new ArrayList<Pair<ServiceInstance, QueryWorkerResult>>();
		/** <annotatorName, ServiceId> */
		@Builder.Default
		private Map<String, List<ServiceInstance>> modelDynamicInstanceMap = new HashMap<String, List<ServiceInstance>>();
		/** [annotatorName, <instance, instanceEnv>]*/
		@Builder.Default
		private Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> annotatorMap = new HashMap<String, List<Pair<ServiceInstance, QueryWorkerResult>>>();
		/**[instanceId, <instance, instanceEnv>] */
		@Builder.Default
		private Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap = new HashMap<String, Pair<ServiceInstance, QueryWorkerResult>>();
		/**[instanceId, avgGpu] */
		@Builder.Default
		private Map<ServiceInstance, Integer> avgGpuMap = new HashMap<ServiceInstance, Integer>();
		/**[instanceId, score] */
		@Builder.Default
		private Map<ServiceInstance, Double> scoreMap = new HashMap<ServiceInstance, Double>();
		/**[GPU_Identity, GPU_INDEX] */
		@Builder.Default
		private Map<String, String> identityMap = new HashMap<String, String>();
		/**[deviceId, low_switcher] */
		@Builder.Default
		private Map<String, XSwitcher> switcherLowRateMap = new HashMap<String, XSwitcher>();
		/**[deviceId, high_switcher] */
		@Builder.Default
		private Map<String, XSwitcher> switcherHighRateMap = new HashMap<String, XSwitcher>();
		/**[deviceId, high_switcher] */
		@Builder.Default
		private Map<String, List<XSwitcher>> switcherMap = new HashMap<String, List<XSwitcher>>();
		/**[annotatorName, annotatorModel] */
		@Builder.Default
		private Map<String, Model> annotatorModelMap = new HashMap<String, Model>();
		/**[deviceId, 镜头算力倍数] */
		@Builder.Default
		private Map<String, Float> deviceResolutionMultiplierMap = new HashMap<String, Float>();

		public ServiceInstance chooseDynamicInstance(String processor) {	
			List<ServiceInstance> optionInstances = modelDynamicInstanceMap.getOrDefault(processor, List.of());
			if(ThreadLocalRandom.current().nextDouble() >= 0.75) {
				int gpuLimit = ThreadLocalRandom.current().nextInt(100);
				optionInstances = optionInstances.stream().filter(instance -> avgGpuMap.getOrDefault(instance, 0) < gpuLimit).collect(Collectors.toList());
			}
			
			if(CollectionUtils.isEmpty(optionInstances)) 
				optionInstances = modelDynamicInstanceMap.getOrDefault(processor, List.of());
			
			return optionInstances.get(ThreadLocalRandom.current().nextInt(optionInstances.size()));
		}
		
		public ServiceInstance chooseDynamicInstance(String processor, String key) {			
			if(StringUtils.isNotBlank(key)) {
				List<ServiceInstance> optionInstances = modelDynamicInstanceMap.getOrDefault(processor, List.of());
				return optionInstances.get(Math.abs(key.hashCode()) % optionInstances.size());
			}else {
				return chooseDynamicInstance(processor);
			}
		}
		
		public ServiceInstance chooseDynamicVideo(String processor) {
			List<Pair<ServiceInstance, QueryWorkerResult>> optionInstances = annotatorMap.getOrDefault(processor, List.of());
			
			return optionInstances.stream()
					.min((l, r) -> {
						Model lm = l.getValue().getDynamicModels().stream().filter(m -> Objects.equals(processor, m.getAnnotatorName())).findAny().get(); 
						Model rm = r.getValue().getDynamicModels().stream().filter(m -> Objects.equals(processor, m.getAnnotatorName())).findAny().get(); 
						
						return Integer.compare(lm.getTempHigtRateDevices().size(), rm.getTempHigtRateDevices().size());
					})
					.get()
					.getLeft();
		}
	}
}
