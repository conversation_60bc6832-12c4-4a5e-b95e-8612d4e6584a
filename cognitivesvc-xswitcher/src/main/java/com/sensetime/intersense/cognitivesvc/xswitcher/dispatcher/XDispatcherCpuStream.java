package com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult.Model;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher.XDispatcher.StreamDispatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.XSwitcher;
import lombok.extern.slf4j.Slf4j;

/** 艹你大爷 没想到高频流调度这么麻烦 后悔坐着玩意了 才用递归贪心算法 实现简单 效率低就低吧 不管了 
 */
@Component
@Slf4j
public class XDispatcherCpuStream extends StreamDispatcher{
	
	@Override
	public synchronized boolean dispatchMaster(){
		/** 首先同步一下当前环境*/
		Set<String> cpuModelNames = currentModels.stream().map(model -> model.getAnnotatorName()).collect(Collectors.toSet());

		ongoingStreams = watcher.getHolder().getInstanceWorkerList()
			   .stream()
			   .collect(Collectors.toMap(
					   pair -> pair.getLeft().getInstanceId(), 
					   pair -> {
						   Map<String, List<String>> result = pair.getRight().getDynamicModels()
								     .stream()
								     .filter(model -> cpuModelNames.contains(model.getAnnotatorName()))
							   		 .map(model -> model.getHigtRateDevices()
							   				            .stream()
										   				.map(deviceId -> new ImmutablePair<String, String>(deviceId, model.getAnnotatorName()))
										   				.collect(Collectors.toList())
							   		 )
							   		 .flatMap(List::stream)
							   		 .collect(Collectors.toMap(
							   				 item -> item.getLeft(), 
							   				 item -> Lists.newArrayList(item.getRight()),
							   				 ListUtils::union
							   		 ));
						   
						   return result;
					   }
			   ));

		Holder infoHolder = watcher.getHolder();
		
		Map<String, XSwitcher> highRateCpuStreams = infoHolder.getSwitcherHighRateMap()
				.entrySet()
				.stream()
				.collect(Collectors.toMap(
						entry -> entry.getKey(), 
						entry -> {
							XSwitcher xSwitcher = entry.getValue().clone();
							xSwitcher.setProcessors(xSwitcher.getProcessors().entrySet().stream().filter(e -> cpuModelNames.contains(e.getKey())).collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue())));
							return xSwitcher;
						}
				));

		Iterator<Entry<String, XSwitcher>> its = highRateCpuStreams.entrySet().iterator();
		while(its.hasNext())
			if(MapUtils.isEmpty(its.next().getValue().getProcessors()))
				its.remove();
		
		/**<deviceId_annotatorName>*/
		Set<String> targetStreamSet = new HashSet<String>();
		for(Entry<String, XSwitcher> entry : highRateCpuStreams.entrySet())
			for(String processor : entry.getValue().getProcessors().keySet()) 
				targetStreamSet.add(entry.getKey() + PREFIX + processor);
		
		/**<deviceId_annotatorName, instanceId>*/
		Map<String, String> existingStreamMap = new HashMap<String, String>();
		for(Entry<String, Map<String, List<String>>> entry : ongoingStreams.entrySet()) 
			for(Entry<String, List<String>> subEntry : entry.getValue().entrySet()) 
				for(String annotatorName : subEntry.getValue()) 
					existingStreamMap.put(subEntry.getKey() + PREFIX + annotatorName, entry.getKey());
		
		/**<instanceId, List<deviceId>>*/
		Map<String, Set<String>> deviceStreamMap = new HashMap<String, Set<String>>();
		for(Entry<String, Map<String, List<String>>> entry : ongoingStreams.entrySet()) 
			deviceStreamMap.put(entry.getKey(), entry.getValue().keySet());
		
		boolean changed = false;
		/**<deviceId_annotatorName>*/
		List<String> creating = ListUtils.removeAll(targetStreamSet, existingStreamMap.keySet());
		List<String> removing = ListUtils.removeAll(existingStreamMap.keySet(), targetStreamSet);

		//删除如果有超过配置的参数的
		if(!changed)
			changed |= dispatchOverflow(deviceStreamMap);

		//删除不存在的
		if(!changed && CollectionUtils.isNotEmpty(removing))
			changed |= dispatchRemove(removing, existingStreamMap);
		
		//创建新流和模型
		if(!changed && CollectionUtils.isNotEmpty(creating))
			changed |= dispatchCreate(creating, existingStreamMap.keySet(), infoHolder);
		
		//没有新建和删除的情况下 检测是否有更优解
		if(!changed && !highRateCpuStreams.isEmpty()) 
			changed |= dispatchBest(highRateCpuStreams.keySet(), infoHolder);
		
		return changed;
	}

	private boolean dispatchOverflow(Map<String, Set<String>> deviceStreamMap) {
		boolean isChanged = false;

		for(Entry<String, Set<String>> entry : deviceStreamMap.entrySet()) {
			if(entry.getValue().size() <= Utils.instance.xWorkerHighRateMaxCount)
				continue;

			Map<String, List<String>> instanceMap = ongoingStreams.get(entry.getKey());
			List<String> removes = instanceMap.entrySet().stream()
					.sorted((l ,r) -> Integer.compare(l.getValue().size(), r.getValue().size()))
					.map(e -> e.getKey())
					.limit(entry.getValue().size() - Utils.instance.xWorkerHighRateMaxCount)
					.collect(Collectors.toList());

			for(String remove : removes)
				instanceMap.remove(remove);

			isChanged = true;
		}

		return isChanged;
	}

	/** 删除停掉的视频流和模型*/
	private boolean dispatchRemove(List<String> removing, Map<String, String> existingStreamMap) {
		boolean isChanged = true;
		
		/** 循环一下 删除环境中已经不存在设备模型流*/
		for(String id : removing) {
			String instanceId = existingStreamMap.remove(id);
			String[] info = id.split(PREFIX);
			
			String deviceId = info[0];
			String annotator = info[1];
			
			Map<String, List<String>> instanceInfo = ongoingStreams.get(instanceId);
			List<String> annotators = instanceInfo.get(deviceId);
			annotators.remove(annotator);
			
			if(annotators.isEmpty()) {
				instanceInfo.remove(deviceId);
				log.info("[VideoHandleLog] [dispatch] closing stream[" + deviceId + "] from instanceId[" + instanceId + "].");
			}else{
				log.info("[VideoHandleLog] [dispatch] removing annotator[" + annotator + "] from stream[" + deviceId + "] and instanceId[" + instanceId + "]");
			}
		}
		
		return isChanged;
	}
	
	/** 新增视频流和模型  */
	private boolean dispatchCreate(List<String> creating, Set<String> existingSet, Holder infoHolder) {
		boolean isChanged = false;
		
		/** 开始查看需要创建的 <deviceId, list<annotatorName>*/
		Map<String, List<String>> createMap = new HashMap<String, List<String>>();
		Map<String, List<String>> existMap  = new HashMap<String, List<String>>();
		Map<String, List<String>> errorMap  = new HashMap<String, List<String>>();
		
		for(String id : creating) {
			String[] info = id.split(PREFIX);
			
			String deviceId = info[0];
			String annotator = info[1];
			
			Map<String, List<String>> target = infoHolder.getAnnotatorMap().containsKey(annotator) ? createMap : errorMap;
			
			List<String> annotators = target.get(deviceId);
			if(annotators == null) {
				annotators = new ArrayList<String>();
				target.put(deviceId, annotators);
			}
			
			annotators.add(annotator);
		}
		
		for(String id : existingSet) {
			String[] info = id.split(PREFIX);
			
			String deviceId = info[0];
			String annotator = info[1];
			
			Map<String, List<String>> target = infoHolder.getAnnotatorMap().containsKey(annotator) ? existMap : errorMap;
			
			List<String> annotators = target.get(deviceId);
			if(annotators == null) {
				annotators = new ArrayList<String>();
				target.put(deviceId, annotators);
			}
			
			annotators.add(annotator);
		}
		
		int streamToAdd = 16;
		
		for(Entry<String, List<String>> entry : createMap.entrySet()) {
			String deviceId = entry.getKey();
			List<String> creatingAnnotators = entry.getValue();
			
			//删除存在的流的模型 合并到CREATE
			List<String> existingAnnotators = existMap.get(deviceId);
			if(!CollectionUtils.isEmpty(existingAnnotators)) {
				creatingAnnotators.addAll(existingAnnotators);
				
				for(String existingAnnotator : existingAnnotators) {
					for(Map<String, List<String>> instanceInfo : ongoingStreams.values()) {
						List<String> ongoingAnnotators = instanceInfo.get(deviceId);
						if(!CollectionUtils.isEmpty(ongoingAnnotators))
							ongoingAnnotators.remove(existingAnnotator);
					}
				}
			}
			
			//将新建不在环境存在的模型删掉
			for(Iterator<String> it = creatingAnnotators.iterator(); it.hasNext();)
				if(!infoHolder.getAnnotatorMap().containsKey(it.next()))
					it.remove();
			
			//找到一个最优解安排并塞进去
			List<Pair<ServiceInstance, List<String>>> bests = findBest(creatingAnnotators, infoHolder);
			for(Pair<ServiceInstance, List<String>> best : bests) {
				Map<String, List<String>> instanceInfo = ongoingStreams.get(best.getLeft().getInstanceId());
				
				if(instanceInfo.size() < Utils.instance.xWorkerHighRateMaxCount) {
					instanceInfo.put(deviceId, best.getRight());
					isChanged = true;
					log.info("[VideoHandleLog] [dispatch] opening stream[" + deviceId + "] to instanceId[" + best.getLeft().getInstanceId() + "] using annotators[" + StringUtils.join(best.getRight(), ",") + "]");
				}
			}
			
			if(-- streamToAdd <= 0)
				break;
		}
		
		if(!errorMap.isEmpty())
			log.info("[VideoHandleLog] [dispatch] some annotator is missing in this enviroment, can not to create model stream [" + errorMap.toString() + "].");
		
		return isChanged;
	}

	/** 随机选一个device  查一下这个device是否是best*/
	private boolean dispatchBest(Set<String> deviceIds, Holder infoHolder) {
		String targetDeviceId = deviceIds.toArray(String[]::new)[ThreadLocalRandom.current().nextInt(deviceIds.size())];

		long ongoingCount = ongoingStreams.values().stream().map(map -> map.get(targetDeviceId)).filter(Objects::nonNull).count();
		
		List<String> ongoingAnnotators = ongoingStreams.values().stream()
				.map(map -> map.get(targetDeviceId))
				.filter(Objects::nonNull)
				.flatMap(List::stream)
				.collect(Collectors.toList());
		
		List<Pair<ServiceInstance, List<String>>> bestTargets = findBest(ongoingAnnotators, infoHolder);
		
		/** 如果算出来的最优解比当前解流数要小  那么删掉旧的 添加新的*/
		if(!bestTargets.isEmpty() && bestTargets.size() < ongoingCount) {
			ongoingStreams.values().forEach(map -> map.remove(targetDeviceId));
			
			for(Pair<ServiceInstance, List<String>> best : bestTargets) {
				Map<String, List<String>> instanceInfo = ongoingStreams.get(best.getLeft().getInstanceId());
				instanceInfo.put(targetDeviceId, best.getRight());
			}
			
			String resultLog = bestTargets.stream().map(pair -> "{instanceId[" + pair.getLeft().getInstanceId() + "], annotator[" + StringUtils.join(pair.getValue(), ",") + "]}").collect(Collectors.joining(","));
			log.info("[VideoHandleLog] [dispatch] rearrange stream[" + targetDeviceId + "] to " + resultLog);
			return true;
		}else {
			return false;
		}
	}
	
	/** 利用算法导论里面最简单的 贪心算法加递归 效率差实现简单 给定一套模型 找出最少节点列表能满足所有模型的  */
	private List<Pair<ServiceInstance, List<String>>> findBest(List<String> annotators, Holder infoHolder){
		Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap = infoHolder.getInstanceIdMap().entrySet()
				.stream()
				.filter(entry -> {
					QueryWorkerResult qmr = entry.getValue().getRight();
					
					boolean empty = qmr.getDynamicModels().stream().filter(model -> annotators.contains(model.getAnnotatorName())).findAny().isEmpty();
					if(empty || qmr.getAvailableGpuMemory() < 1024)
						return false;
					
					Set<String> ongoings = qmr.getDynamicModels().stream()
							.map(Model::getHigtRateDevices)
							.flatMap(List::stream)
							.collect(Collectors.toSet());
					
					if(ongoings.size() >= Utils.instance.xWorkerHighRateMaxCount)
						return false;
					
					return true;
				})
				.collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue()));
		
		List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> result= findBest_0(2, annotators, instanceIdMap);
		return result.stream()
					 .map(triple -> new ImmutablePair<ServiceInstance, List<String>>(triple.getLeft(), triple.getRight()))
					 .collect(Collectors.toList());
	}
	
	/** 没必要进行全解覆盖 贪心时只贪前几轮*/
	@SuppressWarnings({ "unchecked", "rawtypes" })
	private List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> findBest_0(int round, List<String> annotators, Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap){
		if(annotators.isEmpty() || instanceIdMap.isEmpty())
			return Lists.newArrayList();
		
		List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> sortedInstanceList = instanceIdMap.values().stream()
				.map(pair -> {
					List intersection = ListUtils.intersection(annotators, pair.getRight().getDynamicModels().stream().map(model -> model.getAnnotatorName()).collect(Collectors.toList()));
					return new ImmutableTriple<ServiceInstance, QueryWorkerResult, List<String>>(pair.getLeft(), pair.getRight(), intersection);
				})
				.sorted((l, r) -> {
					//由大到小
					return - Integer.compare(l.getRight().size(), r.getRight().size());
				})
				.collect(Collectors.toList());
		
		Triple<ServiceInstance, QueryWorkerResult, List<String>> thisRoundTarget[] = new Triple[Math.min(round, sortedInstanceList.size())];
		List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> thisRoundSubTarget[] = new List[Math.min(round, sortedInstanceList.size())];
		
		for(int index = 0; index < round && index < sortedInstanceList.size(); index ++) {
			thisRoundTarget[index] = sortedInstanceList.get(index);
			
			List<String> subAnnotators = ListUtils.removeAll(annotators, thisRoundTarget[index].getMiddle().getDynamicModels().stream().map(model -> model.getAnnotatorName()).collect(Collectors.toList()));
			Map<String, Pair<ServiceInstance, QueryWorkerResult>> subInstanceIdMap = new HashMap<String, Pair<ServiceInstance, QueryWorkerResult>>(instanceIdMap);
			subInstanceIdMap.remove(thisRoundTarget[index].getLeft().getInstanceId());
			
			//删除掉本次选中的最佳值之后 递归进入下一轮贪心
			thisRoundSubTarget[index] = findBest_0(round, subAnnotators, subInstanceIdMap);
		}
		
		return Stream.iterate(0, i -> i + 1)
					 .limit(Math.min(round, sortedInstanceList.size()))
					 .map(index -> ListUtils.union(Lists.newArrayList(thisRoundTarget[index]), thisRoundSubTarget[index]))
					 .max((l, r) -> {
						 if(l.size() != r.size())
							 return - Integer.compare(l.size(), r.size());
						 
						 int lmem = l.stream().map(item -> item.getMiddle().getAvailableGpuMemory()).reduce(Integer::sum).get();
						 int rmem = r.stream().map(item -> item.getMiddle().getAvailableGpuMemory()).reduce(Integer::sum).get();
						 //显存大的在前面
						 return Integer.compare(lmem, rmem);
					 })
					 .get();
	}
	/** 只寻找视频最多的那一套模型 添加亲和性*/
	public synchronized void judgeAffinity() {
		Holder infoHolder = watcher.getHolder();

		Map<String, XSwitcher> highRateStreams = infoHolder.getSwitcherHighRateMap();

		//<annotatorName, affinityName>
		Map<String, String> normalAffinityMap = new HashMap<String, String>();//人工设置的affinity
		Map<String, String> videoAffinityMap = new HashMap<String, String>();//自动设置的affinity
		Set<String> noAffinitySet = new HashSet<String>();//无affinity的

		for(XDynamicModel model : currentModels) {
			if(StringUtils.isBlank(model.getAffinityGroup())) {
				noAffinitySet.add(model.getAnnotatorName());
			}else if(model.getAffinityGroup().startsWith(SUGGEST)) {
				videoAffinityMap.put(model.getAnnotatorName(), model.getAffinityGroup());
			}else {
				normalAffinityMap.put(model.getAnnotatorName(), model.getAffinityGroup());
			}
		}

		//如果有人工设置亲和性组,那么删除所有自动设置的亲和性组, 因为已经人工设置过, 说明有明白人, 那就不要自动了
		if(!normalAffinityMap.isEmpty()) {
			for(XDynamicModel model : currentModels) {
				if(videoAffinityMap.containsKey(model.getAnnotatorName())) {
					model.setAffinityGroup("");
					mapper.saveAndFlush(model);
				}
			}
			return ;
		}

		List<List<String>> existingProcessorGroups = highRateStreams.entrySet().stream()
				.map(entry -> {
					List<String> processors = new ArrayList<String>(entry.getValue().getProcessors().keySet());
					return new ImmutablePair<String, List<String>>(entry.getKey(), processors);
				})
				.filter(pair -> pair.getRight().size() > 1)
				.collect(Collectors.toMap(
						entry -> entry.getRight(),
						entry -> 1,
						Integer::sum
				))
				.entrySet()
				.stream()
				.sorted((l, r) -> {
					List<String> lm = l.getKey();
					List<String> rm = r.getKey();

					int lNum = lm.size() * l.getValue() - l.getValue();
					int rNum = rm.size() * r.getValue() - r.getValue();

					if(lNum != rNum)
						return Integer.compare(lNum, rNum);

					if(lm.size() != rm.size())
						return - Integer.compare(lm.size(), rm.size());

					for(int index = 0; index < lm.size(); index ++) {
						if(!lm.get(index).equals(rm.get(index))) {
							return lm.get(index).compareTo(rm.get(index));
						}
					}

					throw new RuntimeException("should not be here.");
				})
				.map(entry -> entry.getKey())
				.collect(Collectors.toList());

		if(existingProcessorGroups.size() <= 0) {
			for(XDynamicModel model : currentModels) {
				if(videoAffinityMap.containsKey(model.getAnnotatorName())) {
					model.setAffinityGroup("");
					mapper.saveAndFlush(model);
				}
			}
			return;
		}

		Map<String, String> targetAffinityMap = new HashMap<String, String>();
		for(List<String> existingProcessorGroup : existingProcessorGroups) {
			String affinity = SUGGEST + RandomStringUtils.randomAlphabetic(8);
			for(String annotator : existingProcessorGroup) {
				targetAffinityMap.put(annotator, affinity);
			}
		}

		for(XDynamicModel model : currentModels) {
			String affinity = targetAffinityMap.get(model.getAnnotatorName());
			if(StringUtils.isNotBlank(affinity)) {
				model.setAffinityGroup(affinity);
				mapper.saveAndFlush(model);
			}
		}
	}

	private static final String PREFIX = ";;;";
	private static final String SUGGEST = "VIDEO_AUTO_";
}
