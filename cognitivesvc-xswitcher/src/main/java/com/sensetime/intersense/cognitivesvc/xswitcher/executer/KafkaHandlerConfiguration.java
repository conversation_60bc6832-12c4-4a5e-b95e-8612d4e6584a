package com.sensetime.intersense.cognitivesvc.xswitcher.executer;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawVideo;
import com.sensetime.intersense.cognitivesvc.xswitcher.controller.XSwitcherExecuteProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.function.Consumer;

@Configuration("xswitcherHandlerKafkaConfiguration")
@ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "yes")
//@EnableBinding({KafkaHandlerConfiguration.SenseyexRawImageHandler.class, KafkaHandlerConfiguration.SenseyexRawVideoHandler.class})
@PropertySource("classpath:xswitcher-stream.properties")
@Slf4j
public class KafkaHandlerConfiguration {

    //	public static interface SenseyexRawImageHandler {
//    String SENSEYEX_RAW_IMAGE_INPUT = "senseyex_raw_image_input";
    //		@Input(SENSEYEX_RAW_IMAGE_INPUT) SubscribableChannel senseyex_raw_image_input();
//	}
//
//	public static interface SenseyexRawVideoHandler {
//    String SENSEYEX_RAW_VIDEO_INPUT = "senseyex_raw_video_input";
//		@Input(SENSEYEX_RAW_VIDEO_INPUT) SubscribableChannel senseyex_raw_video_input();
//	}

    @Configuration("senseyexRawImageHandler")
    @ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "yes")
    public static class ZSenseyexRawImageHandler {

        @Autowired
        private XSwitcherExecuteProvider switcherExecuteProvider;

        @Bean("senseyex_raw_image_input")
        public Consumer<SenseyexRawImage> senseyexRawImageInput() {
            return message -> {
                try {
                    switcherExecuteProvider.sendSenseyexRawImage(message);
                } catch (Exception e) {
                    log.error("senseyexRawImageInput exception msg:{} ", e.getMessage());
                }
            };
        }

        //		@StreamListener(KafkaHandlerConfiguration.SenseyexRawImageHandler.SENSEYEX_RAW_IMAGE_INPUT)
//	    public void sendSenseyexRawImage(@RequestBody @Payload SenseyexRawImage message) throws Exception {
//			switcherExecuteProvider.sendSenseyexRawImage(message);
//		}
//
        @Bean("senseyex_raw_video_input")
        public Consumer<SenseyexRawVideo> senseyexRawVideoInput() {
            return message -> {
                try {
                    switcherExecuteProvider.sendSenseyexRawVideo(message);
                } catch (Exception e) {
                    log.error("senseyexRawVideoInput exception msg:{} ", e.getMessage());
                }
            };
        }

//        @StreamListener(KafkaHandlerConfiguration.SenseyexRawVideoHandler.SENSEYEX_RAW_VIDEO_INPUT)
//        public void sendSenseyexRawVideo(@RequestBody @Payload SenseyexRawVideo message) throws Exception {
//            switcherExecuteProvider.sendSenseyexRawVideo(message);
//        }
    }
}
