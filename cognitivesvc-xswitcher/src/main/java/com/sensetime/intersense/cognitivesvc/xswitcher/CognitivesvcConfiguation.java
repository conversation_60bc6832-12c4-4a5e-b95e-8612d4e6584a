package com.sensetime.intersense.cognitivesvc.xswitcher;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.XSwitcher;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer.DeviceType;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils.FrameDefaultRequired;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoSeenFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.service.InstancesWatchHolder;
import com.sensetime.intersense.cognitivesvc.stream.video.service.VideoFrameFilter;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelExecuter;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sensetime.intersense.cognitivesvc.xswitcher.video.filter.XSwitcherNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.xswitcher.video.filter.XSwitcherSeenFilter;
import com.sensetime.storage.autoconfigure.StorageProperties;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sensetime.storage.factory.FileStorageType.OSG;

@Configuration("streamXswitcherConfiguation")
@EnableScheduling
@ComponentScan
@ConditionalOnExpression("${stream.xswitcher.enabled:true} && ${stream.enabled:true}")
@Slf4j
@PropertySource("classpath:xswitcher.properties")
public class CognitivesvcConfiguation{
	
	@Autowired
	private XModelWatcher watcher;
	
	@Autowired
	private XModelExecuter executor;

	@Autowired
	StorageProperties storageProperties;

	@Autowired
	private XDynamicModelRepository dynamicModelMapper;

	@PostConstruct
	public synchronized void initialize() {

		if(VideoNonSeenFilter.existingFilter.contains(XSwitcherSeenFilter.class))
			return;
		
		VideoSeenFilter.existingFilter.add(XSwitcherSeenFilter.class);
		VideoNonSeenFilter.existingFilter.add(XSwitcherNonSeenFilter.class);

		log.warn("\n");
		log.warn("*****************************************");
		log.warn("**********init stream xswitcher**********");
		log.warn("*****stream.enabled=false to disable*****");
		log.warn("stream.xswitcher.enabled=false to disable");
		log.warn("*****************************************");
		log.warn("\n");

		XSwitcherSeenFilter.setWatcher(watcher);
		XSwitcherSeenFilter.setExecutor(executor);
		
		XSwitcherNonSeenFilter.setWatcher(watcher);
		XSwitcherNonSeenFilter.setExecutor(executor);

		if(storageProperties.getFileStorageType().equals(OSG)){
			ImageUtils.preMkDirPostContructCompleted = true;
			log.info("******* use osg, no need preMkdirs");
			return;
		}

		ImageUtils.preMkDirPostContructCompleted = true;
		log.info("[preMakeDirsCron] preMakeDir Completed");
	}
    
    @Bean
    public FrameDefaultRequired switcherFrameDefaultRequired() {
    	return new FrameDefaultRequired() {
    		
			@Override
			public int requiredNonSeenReusedFrameCount(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
					case T4: 
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 8;
				}
			}

			@Override
			public int requiredNonSeenQueueSize(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
					case T4: 
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 64;
				}
			}

			@Override
			public int requiredNonSeenMaxCount(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
						return 32;
					case T4: 
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 48;
				}
			}
		};
    }


	@Bean
	public InstancesWatchHolder switcherWatchHolder(XModelWatcher xModelWatcher) {
		return new InstancesWatchHolder() {
			@Override
			public Pair<Boolean, Integer> getSwitchWatchHolder(Map<String, String> modelsGroup) {

				int checkGroupInstances = 0;
				boolean checkModelLowGroup = false;
				try {
					InetAddress address = InetAddress.getLocalHost();
					String currentHost = address.getHostAddress();

					Map<String, Pair<ServiceInstance, QueryWorkerResult>>   instanceIdMap = watcher.getHolder().getInstanceIdMap();
					Map<String, XSwitcher>  switcherLowRateMap = watcher.getHolder().getSwitcherLowRateMap();

					Map<String, String> modelsLowGroup = new HashMap<>();
					for (Map.Entry<String,XSwitcher> entry : switcherLowRateMap.entrySet()) {
						String deviceId = entry.getKey();
						XSwitcher switcher = entry.getValue();
						for (String processor : switcher.getProcessors().keySet()) {
							modelsLowGroup.put(processor, deviceId);
						}
					}
					for (Map.Entry<String, Pair<ServiceInstance, QueryWorkerResult>> entry : instanceIdMap.entrySet()) {
						ServiceInstance serviceInstance = entry.getValue().getLeft();
						QueryWorkerResult queryWorkerResult = entry.getValue().getRight();
						// 遍历查询工作结果中的动态模型列表
						boolean checkModelGroupStep1 = false;

						if (serviceInstance.getHost().equals(currentHost)) {
							for (QueryWorkerResult.Model models : queryWorkerResult.getDynamicModels()) {
								String annotatorName = models.getAnnotatorName();
								String monopolizedGroup = modelsGroup.getOrDefault(annotatorName, "");
								String modelsLow = modelsLowGroup.getOrDefault(annotatorName, "");
								//log.info("switcherVideoFrameFilterMid2{},{},{},{},{}",currentHost,models.getAnnotatorName(),models.getMonopolizedGroup() ,!lowSwitcher.getProcessors().containsKey(annotatorName), monopolizedGroup);
								//获取低频独占模型
								if ( monopolizedGroup != null && !monopolizedGroup.isEmpty() && StringUtils.isNotBlank(modelsLow)) {
									checkModelLowGroup   = true;
									checkModelGroupStep1 = true;
								}
							}
						}

						if(checkModelGroupStep1){
							checkGroupInstances ++;
						}
					}
					if(!checkModelLowGroup){

						for (Map.Entry<String, Pair<ServiceInstance, QueryWorkerResult>> entry : instanceIdMap.entrySet()) {

								QueryWorkerResult queryWorkerResult = entry.getValue().getRight();
								// 遍历查询工作结果中的动态模型列表
								boolean checkModelGroup = false;
								for (QueryWorkerResult.Model models : queryWorkerResult.getDynamicModels()) {
									String annotatorName = models.getAnnotatorName();
									// 节点其他模型是否是独占
									String monopolizedGroup = modelsGroup.getOrDefault(annotatorName, "");
									//log.info("switcherVideoFrameFilterMid2{},{},{},{},{}",currentHost,models.getAnnotatorName(),models.getMonopolizedGroup() ,!lowSwitcher.getProcessors().containsKey(annotatorName), monopolizedGroup);
									//获取非低频独占模型
									if ( monopolizedGroup != null && !monopolizedGroup.isEmpty()) {
										checkModelGroup =true;
									}
								}
								if(checkModelGroup){
									checkGroupInstances ++;
								}
						}

					}


				}catch (Exception e){
					e.printStackTrace();
				}
				return new MutablePair<>(checkModelLowGroup, checkGroupInstances);
			}

		};
	}
	@Bean
	public VideoFrameFilter switcherVideoFrameFilter(XModelWatcher xModelWatcher){
		return new VideoFrameFilter() {

			@Override
			public VideoChecked check(VideoStreamInfra infra,Map<String, String> modelsGroup) {
				Holder infoHolder = watcher.getHolder();
				
				XSwitcher lowSwitcher = infoHolder.getSwitcherLowRateMap().get(infra.getDeviceId());
				if(lowSwitcher == null || lowSwitcher.isHighRate())
					return null;

				Map<String, List<Pair<ServiceInstance, QueryWorkerResult>>> existingAnnotators = watcher.getHolder().getAnnotatorMap();

				boolean exist = CollectionUtils.containsAny(existingAnnotators.keySet(), lowSwitcher.getProcessors().keySet());
				if (Utils.instance.logged) {
                    log.info("switcherVideoFrameFilters {},{},{},{}", infra.getDeviceId(), existingAnnotators.keySet(), lowSwitcher.getProcessors().keySet(),exist);
                }
				if(!exist)
					return null;

				if(Utils.instance.stabilityFlag) {
					try {
						InetAddress address = InetAddress.getLocalHost();
						String currentHost = address.getHostAddress();

						boolean checkFilter = false;
						boolean selfCheckFilter = false;
						// 判断自己 对应的模型 是否独占
						for (Map.Entry<String, List<Pair<ServiceInstance, QueryWorkerResult>>> entry : existingAnnotators.entrySet()) {
							String modelNameExit = entry.getKey();
							List<Pair<ServiceInstance, QueryWorkerResult>> modelValueList = entry.getValue();
							// 检查模型名称是否存在于低频的处理器中
							if (lowSwitcher.getProcessors().containsKey(modelNameExit)) {
								//当前节点独占模型对应的流，应该返回true
								checkFilter = processModelValueListSelf(modelValueList, currentHost, lowSwitcher, modelsGroup, infra.getDeviceId());
                                if (Utils.instance.logged) {
                                    log.info("switcherVideoFrameFilterStart {},{},{},{},{},{},{}",infra.getDeviceId(), modelNameExit, currentHost, checkFilter, lowSwitcher, existingAnnotators.keySet(), checkFilter);
                                }
								if(checkFilter){
									selfCheckFilter = true;
								}
							}
						}
                        if(!selfCheckFilter) {
							// 当前节点没有低频独占，判断 该节点其他模型是否是独占
							for (Map.Entry<String, List<Pair<ServiceInstance, QueryWorkerResult>>> entry : existingAnnotators.entrySet()) {
								List<Pair<ServiceInstance, QueryWorkerResult>> modelValueList = entry.getValue();
								checkFilter = processModelValueList(modelValueList, currentHost, lowSwitcher, modelsGroup, infra.getDeviceId());
								if (checkFilter) {
									return null;
								}
							}
							if (Utils.instance.logged) {
								log.info("switcherVideoFrameFilterEnd {},{},{},{}", infra.getDeviceId(),currentHost, checkFilter, existingAnnotators.keySet());
							}
						}
					} catch (Exception e) {
						log.error("UnknownHostException{}", e.getMessage());
					}
				}
				if("0".equals(infra.getRtmpOn()))
					return VideoChecked.ALL;

				XSwitcher highSwitcher = infoHolder.getSwitcherHighRateMap().get(infra.getDeviceId());
				if(Utils.instance.senseyexVideoSwitcherOnWorker && highSwitcher != null)
					return null;
				else {
					if(lowSwitcher.isMultiplex())
						return VideoChecked.GPUMultiplex;
					else
						return VideoChecked.GPU;
				}
			}

			@Override
			public int extraRtspCount(VideoStreamInfra infra) {
				XSwitcher high = xModelWatcher.getHolder().getSwitcherHighRateMap().get(infra.getDeviceId());

				if(high == null || high.getProcessors().isEmpty())
					return 0;
				else
					return high.getProcessors().size() - 1;
			};
		};
	}



	// 处理模型值列表的方法
	private boolean processModelValueList(List<Pair<ServiceInstance, QueryWorkerResult>> modelValueList, String currentHost, XSwitcher lowSwitcher, Map<String, String> allModels, String deviceId) {
		// 遍历模型对应的值列表
		boolean currentNoModel = false;
		for (Pair<ServiceInstance, QueryWorkerResult> value : modelValueList) {
			ServiceInstance serviceInstance = value.getLeft();
			QueryWorkerResult queryWorkerResult = value.getRight();
			//log.info("switcherVideoFrameFilterMid1 {},{},{}", deviceId, serviceInstance.getHost(), currentHost);
			// 检查服务实例的主机是否为当前主机
			if (serviceInstance.getHost().equals(currentHost)) {
				// 遍历查询工作结果中的动态模型列表
				if(queryWorkerResult.getDynamicModels().isEmpty()){
					currentNoModel = true;
				}
				for (QueryWorkerResult.Model models : queryWorkerResult.getDynamicModels()) {
					String annotatorName = models.getAnnotatorName();
					// 节点其他模型是否是独占
					String monopolizedGroup = allModels.getOrDefault(annotatorName, "");
					//log.info("switcherVideoFrameFilterMid2 {}, {},{},{},{},{}",deviceId, currentHost,models.getAnnotatorName(),serviceInstance.getHost(),!lowSwitcher.getProcessors().containsKey(annotatorName), monopolizedGroup);
					if (!lowSwitcher.getProcessors().containsKey(annotatorName) && monopolizedGroup != null && !monopolizedGroup.isEmpty()) {
						return true;
					}
				}
			}else{
				currentNoModel = true;
			}
		}
		return currentNoModel;
	}
	// 处理模型值列表的方法
	private boolean processModelValueListSelf(List<Pair<ServiceInstance, QueryWorkerResult>> modelValueList, String currentHost, XSwitcher lowSwitcher, Map<String, String> allModels, String deviceId) {
		// 遍历模型对应的值列表
		for (Pair<ServiceInstance, QueryWorkerResult> value : modelValueList) {
			ServiceInstance serviceInstance = value.getLeft();
			QueryWorkerResult queryWorkerResult = value.getRight();
			//log.info("switcherVideoFrameFilterMid1{},{}", serviceInstance.getHost(), currentHost);
			// 检查服务实例的主机是否为当前主机
			//log.info("switcherVideoFrameFilter each{},{},{},{},{}", deviceId, queryWorkerResult.getDynamicModels(), currentHost, serviceInstance.getHost(), lowSwitcher);
			if (serviceInstance.getHost().equals(currentHost)) {
				// 遍历查询工作结果中的动态模型列表
				String modelCurrent = "";
				for (QueryWorkerResult.Model models : queryWorkerResult.getDynamicModels()) {
					String annotatorName = models.getAnnotatorName();
					// 节点其他模型是否是独占
					String monopolizedGroup = allModels.getOrDefault(annotatorName, "");
					//log.info("switcherVideoFrameFilterMid2{},{},{},{},{}",currentHost,models.getAnnotatorName(),models.getMonopolizedGroup() ,!lowSwitcher.getProcessors().containsKey(annotatorName), monopolizedGroup);
					if ( monopolizedGroup != null && !monopolizedGroup.isEmpty()) {
						modelCurrent = monopolizedGroup;
					}
				}
				//当前节点独占模型
				if(lowSwitcher.getProcessors().containsKey(modelCurrent)){
					return  true;
				}
			}
		}
		return false;
	}
}
