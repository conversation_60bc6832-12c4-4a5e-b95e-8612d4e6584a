package com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult.Model;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xswitcher.dispatcher.XDispatcher.StreamDispatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.XSwitcher;
import lombok.extern.slf4j.Slf4j;

/** 艹你大爷 没想到高频流调度这么麻烦 后悔坐着玩意了 才用递归贪心算法 实现简单 效率低就低吧 不管了 
 */
@Component
@Slf4j
public class XDispatcherGpuStream extends StreamDispatcher{

	@Value("${cognitivesvcx.availableGpuMemory:1024}")
	private int availableGpuMemory;

	@Override
	public synchronized boolean dispatchMaster(){

		Boolean loggedForDispath = (Utils.instance.watchFrameTiktokLevel == -177);
		/** 首先同步一下当前环境*/
		Set<String> gpuModelNames = currentModels.stream().map(model -> model.getAnnotatorName()).collect(Collectors.toSet());
		/** 目前在跑的 **/
		ongoingStreams = watcher.getHolder().getInstanceWorkerList()
			   .stream()
			   .filter(instance -> instance.getRight().gpuInstance())
			   .collect(Collectors.toMap(
					   pair -> pair.getLeft().getInstanceId(), 
					   pair -> {
						   Map<String, List<String>> result = pair.getRight().getDynamicModels()
								     .stream()
								     .filter(model -> gpuModelNames.contains(model.getAnnotatorName()))
							   		 .map(model -> model.getHigtRateDevices()
							   				            .stream()
										   				.map(deviceId -> new ImmutablePair<String, String>(deviceId, model.getAnnotatorName()))
										   				.collect(Collectors.toList())
							   		 )
							   		 .flatMap(List::stream)
							   		 .collect(Collectors.toMap(
							   				 item -> item.getLeft(), 
							   				 item -> Lists.newArrayList(item.getRight()),
							   				 ListUtils::union
							   		 ));
						   
						   return result;
					   }
			   ));

		Holder infoHolder = watcher.getHolder();
		/** 需要跑的 **/
		Map<String, XSwitcher> highRateGpuStreams = infoHolder.getSwitcherHighRateMap()
				.entrySet()
				.stream()
				.collect(Collectors.toMap(
						entry -> entry.getKey(), 
						entry -> {
							XSwitcher xSwitcher = entry.getValue().clone();
							xSwitcher.setProcessors(xSwitcher.getProcessors().entrySet().stream().filter(e -> gpuModelNames.contains(e.getKey())).collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue())));
							return xSwitcher;
						}
				));
		
		Iterator<Entry<String, XSwitcher>> its = highRateGpuStreams.entrySet().iterator();
		while(its.hasNext())
			if(MapUtils.isEmpty(its.next().getValue().getProcessors()))
				its.remove();
		
		/**<deviceId_annotatorName>*/
		Set<String> targetStreamSet = new HashSet<String>();
		for(Entry<String, XSwitcher> entry : highRateGpuStreams.entrySet())
			for(String processor : entry.getValue().getProcessors().keySet()) 
				targetStreamSet.add(entry.getKey() + PREFIX + processor);
		
		/**<deviceId_annotatorName, instanceId>*/
		Map<String, String> existingStreamMap = new HashMap<String, String>();
		for(Entry<String, Map<String, List<String>>> entry : ongoingStreams.entrySet()) 
			for(Entry<String, List<String>> subEntry : entry.getValue().entrySet()) 
				for(String annotatorName : subEntry.getValue()) 
					existingStreamMap.put(subEntry.getKey() + PREFIX + annotatorName, entry.getKey());
		
		/**<instanceId, List<deviceId>>*/
		Map<String, Set<String>> deviceStreamMap = new HashMap<String, Set<String>>();
		for(Entry<String, Map<String, List<String>>> entry : ongoingStreams.entrySet()) 
			deviceStreamMap.put(entry.getKey(), entry.getValue().keySet());


		Set<String> dispatchDescStream = new HashSet<String>();
		for(Entry<String, List<XSwitcher>> entry : infoHolder.getSwitcherMap().entrySet()) {
			for(XSwitcher switcher : entry.getValue()){
			    if(switcher !=null && switcher.getDispatchDesc() !=null && !switcher.getDispatchDesc().isEmpty()) {
					dispatchDescStream.add(entry.getKey() + PREFIX + switcher.getDispatchDesc());
				}
			}
		}

		boolean changed = false;
		/**<deviceId_annotatorName>*/
		List<String> creating = ListUtils.removeAll(targetStreamSet, existingStreamMap.keySet());
		List<String> removing = ListUtils.removeAll(existingStreamMap.keySet(), targetStreamSet);

		if(loggedForDispath)
			log.info("[VideoHandleLog] [dispatch] dispatchCreate createMapOrRemove : creating:{} removing: {},existingStreamMap:{},dispatchDescStream:{} ", JSON.toJSONString(creating),  JSON.toJSONString(removing), JSON.toJSONString(existingStreamMap), JSON.toJSONString(dispatchDescStream));

		//删除如果有超过配置的参数的
		if(!changed)
			changed |= dispatchOverflow(deviceStreamMap);
		
		//删除不存在的
		if(!changed && CollectionUtils.isNotEmpty(removing))
			changed |= dispatchRemove(removing, existingStreamMap);
		
		//创建新流和模型
		if(!changed && CollectionUtils.isNotEmpty(creating))
			changed |= dispatchCreate(creating, existingStreamMap.keySet(), infoHolder);
		
		//没有新建和删除的情况下 检测是否有更优解
		if(!changed && !highRateGpuStreams.isEmpty()) 
			changed |= dispatchBest(highRateGpuStreams.keySet(), infoHolder);		

		//check dispatch desc
		if(!changed && dispatchDescStream.size() != existingStreamMap.size()){
			changed |= true;
		}
		return changed;
	}
	
	private boolean dispatchOverflow(Map<String, Set<String>> deviceStreamMap) {
		boolean isChanged = false;
		Boolean loggedForDispath = (Utils.instance.watchFrameTiktokLevel == -177 || Utils.instance.printLog);

		if (Utils.instance.cameraDispatch) {

			for (Entry<String, Set<String>> entry : deviceStreamMap.entrySet()) {

				Map<String, List<String>> instanceInfo = ongoingStreams.get(entry.getKey());

				// 使用镜头算力逻辑处理模型流数限制
				Map<String, Float> modelWeightedStreams = new HashMap<>(); // 每个模型的加权流数
				Map<String, Map<String, Float>> modelDeviceWeights = new HashMap<>(); // 每个模型对应的设备及其权重

				// 计算每个模型的加权流数
				for (Entry<String, List<String>> deviceEntry : instanceInfo.entrySet()) {
					String deviceId = deviceEntry.getKey();
					List<String> models = deviceEntry.getValue();

					// 获取设备的分辨率倍数
					Float deviceMultiplier = watcher.getHolder().getDeviceResolutionMultiplierMap().get(deviceId);
					if (deviceMultiplier == null || deviceMultiplier <= 0) {
						deviceMultiplier = 1.0f; // 默认为1倍
					}

					// 更新每个模型的加权流数
					for (String model : models) {
						// 累加模型的加权流数
						float currentWeight = modelWeightedStreams.getOrDefault(model, 0.0f);
						modelWeightedStreams.put(model, currentWeight + deviceMultiplier);

						// 记录模型对应的设备及其权重
						Map<String, Float> deviceWeights = modelDeviceWeights.computeIfAbsent(model, k -> new HashMap<>());
						deviceWeights.put(deviceId, deviceMultiplier);
					}
				}
				if(loggedForDispath){
					log.info("[VideoHandleLog] [dispatch] dispatchOverflowCheck deviceWeights:{},modelWeightedStreams{}",
							JSONUtils.toJSONString(modelDeviceWeights), JSONUtils.toJSONString(modelWeightedStreams));
				}

				// 计算所有模型的总权重，检查是否需要应用全局限制
				float totalWeightAcrossAllModels = 0;
				for (Float weight : modelWeightedStreams.values()) {
					totalWeightAcrossAllModels += weight;
				}
				boolean needToApplyGlobalLimit = false;
				// 如果总权重超过全局限制，则需要应用全局限制
				if (Utils.instance.xWorkerHighRateMaxCount > 0 && totalWeightAcrossAllModels > Utils.instance.xWorkerHighRateMaxCount) {
					needToApplyGlobalLimit = true;
					if (loggedForDispath) {
						log.info("[VideoHandleLog] [dispatch] 全局加权流数超限: 总权重={}, 全局限制={}", 
							totalWeightAcrossAllModels, Utils.instance.xWorkerHighRateMaxCount);
					}
				}

				// 检查每个模型是否超过限制
				List<String> devicesToRemove = new ArrayList<>();
				boolean anyModelOverLimit = false;

				// 第一轮处理：检查单个模型是否超限
				for (XDynamicModel model : currentModels) {
					String annotatorName = model.getAnnotatorName();
					Float totalWeight = modelWeightedStreams.get(annotatorName);
					if(loggedForDispath) {
						log.info("[VideoHandleLog] [dispatch] dispatchOverflowCheck annotatorName:{}, totalWeight:{},getMaxStreamCount{}",
								annotatorName, totalWeight, model.getMaxStreamCount());
					}
					
					// 检查模型是否需要限流：有特定限制的模型超限或者无特定限制但需要应用全局限制
					if (totalWeight != null && (model.getMaxStreamCount() > 0 && totalWeight > model.getMaxStreamCount()) ) {
						anyModelOverLimit = true;
						
						// 确定使用的最大流数限制（模型自身限制或全局限制）
						float effectiveMaxStreamCount = model.getMaxStreamCount() > 0 ? 
										 model.getMaxStreamCount() : 
										 Utils.instance.xWorkerHighRateMaxCount;
						// 需要移除的加权流数
						float excessWeight = totalWeight - effectiveMaxStreamCount;
						if(loggedForDispath) {
							log.info("[VideoHandleLog] [dispatch] dispatchOverflowCheck annotatorName:{}, excessWeight:{},getMaxStreamCount{}",
									annotatorName, excessWeight, model.getMaxStreamCount());
						}

						// 获取该模型对应的所有设备及其权重
						Map<String, Float> deviceWeights = modelDeviceWeights.get(annotatorName);
						if (deviceWeights != null) {
							// 按设备权重从大到小排序
							List<Entry<String, Float>> sortedDevices = new ArrayList<>(deviceWeights.entrySet());
							sortedDevices.sort((e1, e2) -> Float.compare(e2.getValue(), e1.getValue()));

							// 先找出可以直接移除的设备（权重 <= excessWeight）
							boolean removedAny = false;
							for (Entry<String, Float> deviceEntry : sortedDevices) {
								if (excessWeight <= 0) break;

								String deviceId = deviceEntry.getKey();
								float weight = deviceEntry.getValue();

								// 如果移除这个设备可以解决问题
								if (weight <= excessWeight) {
									devicesToRemove.add(deviceId);
									excessWeight -= weight;
									removedAny = true;
									log.info("[VideoHandleLog] [dispatch] 移除超出限制的设备[{}]，权重为{}，模型[{}]最大流数为{}",
											deviceId, weight, annotatorName, model.getMaxStreamCount());
								}
							}
							
							// 如果excessWeight仍然大于0，但没有找到可以移除的设备（所有设备权重都大于excessWeight）
							// 则按权重从小到大排序，移除权重最小的设备，直到解决问题
							if (excessWeight > 0 && !removedAny && !sortedDevices.isEmpty()) {
								// 重新按权重从小到大排序
								sortedDevices.sort((e1, e2) -> Float.compare(e1.getValue(), e2.getValue()));
								
								// 逐个移除权重最小的设备，直到解决问题
								while (excessWeight > 0 && !sortedDevices.isEmpty()) {
									Entry<String, Float> smallestDevice = sortedDevices.remove(0);
									String deviceId = smallestDevice.getKey();
									float weight = smallestDevice.getValue();
									
									devicesToRemove.add(deviceId);
									excessWeight -= weight;
									log.info("[VideoHandleLog] [dispatch] 强制移除权重最小的设备[{}]，权重为{}，模型[{}]最大流数为{}，剩余超额权重={}",
											deviceId, weight, annotatorName, model.getMaxStreamCount(), excessWeight);
								}
							}	
						}
					}
				}

				// 第二轮处理：如果没有单个模型超限，但总体权重超过全局限制
				if (!anyModelOverLimit && needToApplyGlobalLimit && Utils.instance.xWorkerHighRateMaxCount > 0) {
					// 收集所有模型的所有设备信息
					Map<String, Float> allDeviceWeights = new HashMap<>();
					for (Map<String, Float> deviceWeights : modelDeviceWeights.values()) {
						for (Map.Entry<String, Float> deviceWeightPair : deviceWeights.entrySet()) {
							String deviceId = deviceWeightPair.getKey();
							Float weight = deviceWeightPair.getValue();
							// 如果设备出现在多个模型中，选择最大权重值
							if (allDeviceWeights.containsKey(deviceId)) {
								if (weight > allDeviceWeights.get(deviceId)) {
									allDeviceWeights.put(deviceId, weight);
								}
							} else {
								allDeviceWeights.put(deviceId, weight);
							}
						}
					}
					
					// 计算需要移除的超出权重
					float globalExcessWeight = totalWeightAcrossAllModels - Utils.instance.xWorkerHighRateMaxCount;
					if (loggedForDispath) {
						log.info("[VideoHandleLog] [dispatch] 全局权重超出，没有单个模型超限，需要移除权重: {}", globalExcessWeight);
					}
					
					// 按权重从大到小排序
					List<Map.Entry<String, Float>> sortedAllDevices = new ArrayList<>(allDeviceWeights.entrySet());
					sortedAllDevices.sort((e1, e2) -> Float.compare(e2.getValue(), e1.getValue()));
					
					// 从权重最大的设备开始移除
					for (Map.Entry<String, Float> deviceWeightEntry : sortedAllDevices) {
						if (globalExcessWeight <= 0) break;
						
						String deviceId = deviceWeightEntry.getKey();
						float weight = deviceWeightEntry.getValue();
						
						// 如果该设备权重小于等于超出权重，直接移除
						if (weight <= globalExcessWeight) {
							devicesToRemove.add(deviceId);
							globalExcessWeight -= weight;
							log.info("[VideoHandleLog] [dispatch] 移除全局超限的设备[{}]，权重为{}，剩余超额权重={}", 
								deviceId, weight, globalExcessWeight);
						} else if (sortedAllDevices.size() > 0 && globalExcessWeight > 0) {
							// 如果所有设备权重都大于超出权重，移除权重最小的设备
							devicesToRemove.add(deviceId);
							globalExcessWeight -= weight;
							log.info("[VideoHandleLog] [dispatch] 强制移除全局超限的最小权重设备[{}]，权重为{}，剩余超额权重={}", 
								deviceId, weight, globalExcessWeight);
							break; // 只移除一个设备就停止，避免移除过多
						}
					}
				}
				
				// 执行设备移除
				for (String deviceId : devicesToRemove) {
					if (instanceInfo.remove(deviceId) != null) {
						isChanged = true;
					}
				}

				if (devicesToRemove.size() > 0) {
					if (loggedForDispath)
						log.info("[VideoHandleLog] [dispatch] remove OverflowModel stream instance:{}, remove streams:{},size:{}",
								entry.getKey(), devicesToRemove, devicesToRemove.size());
				}	
			}
		} else {
			// 使用原始逻辑处理模型流数限制
			//model
			if (Utils.instance.stabilityFlag) {
				for (Entry<String, Set<String>> entry : deviceStreamMap.entrySet()) {

					Map<String, List<String>> instanceInfo = ongoingStreams.get(entry.getKey());

					Map<String, Integer> modelDeviceCount = new HashMap<>();
					Map<String, Integer> modelDeviceMaxCount = new HashMap<>();
					for (List<String> models : instanceInfo.values()) {
						for (String model : models) {
							int count = modelDeviceCount.getOrDefault(model, 0);
							modelDeviceCount.put(model, count + 1);
						}
					}
					for (XDynamicModel models : currentModels) {
						String annotatorName = models.getAnnotatorName();
						Integer deviceCount = modelDeviceCount.get(annotatorName);
						if (deviceCount != null && models.getMaxStreamCount() > 0) {
							if (deviceCount > models.getMaxStreamCount()) {
								modelDeviceMaxCount.put(annotatorName, deviceCount - models.getMaxStreamCount());
							}
						}
					}

					Map<String, Integer> removesModelMap = new HashMap<>();
					List<String> removesModel = new ArrayList<>();
					// Remove excess models
					instanceInfo.forEach((deviceId, modelList) -> {
						modelList.forEach(model -> {
							int modelCount = modelDeviceMaxCount.getOrDefault(model, 0);
							int removedCount = removesModelMap.getOrDefault(model, 0);
							if (modelCount > 0 && removedCount < modelCount) {
								removesModelMap.put(model, removedCount + 1);
								removesModel.add(deviceId);
							}
						});
					});

					for (String remove : removesModel)
						instanceInfo.remove(remove);

					if (removesModel.size() > 0) {
						if (loggedForDispath)
							log.info("[VideoHandleLog] [dispatch] remove OverflowModel stream instance:{}, remove streams:{},size:{}", entry.getKey(), removesModel, removesModel.size());
						isChanged = true;
					}

				}
			}
		}

		
		// 处理全局worker节点的流数限制
		for(Entry<String, Set<String>> entry : deviceStreamMap.entrySet()) {
			String instanceId = entry.getKey();
			Map<String, List<String>> instanceMap = ongoingStreams.get(instanceId);
			
			if (Utils.instance.cameraDispatch) {
				// 计算当前实例的所有设备的加权流数总和
				float totalWeightedStreams = 0.0f;
				Map<String, Float> deviceWeights = new HashMap<>(); // 存储每个设备及其权重
				
				// 计算每个设备的权重并累加总权重
				for (String deviceId : entry.getValue()) {
					// 获取设备的分辨率倍数
					Float deviceMultiplier = watcher.getHolder().getDeviceResolutionMultiplierMap().get(deviceId);
					if (deviceMultiplier == null || deviceMultiplier <= 0) {
						deviceMultiplier = 1.0f; // 默认为1倍
					}
					
					// 记录设备权重
					deviceWeights.put(deviceId, deviceMultiplier);
					// 累加总权重
					totalWeightedStreams += deviceMultiplier;
				}
				
				// 检查总权重是否超过限制
				if (totalWeightedStreams <= Utils.instance.xWorkerHighRateMaxCount) {
					continue; // 未超过限制，无需处理
				}
				
				// 超过限制，需要移除一些设备
				float excessWeight = totalWeightedStreams - Utils.instance.xWorkerHighRateMaxCount;
				float weightToRemove = excessWeight;
				
				if(loggedForDispath) {
					log.info("[VideoHandleLog] [dispatch] 实例[{}]总加权流数[{}]超过限制[{}]，需移除[{}]权重", 
						instanceId, totalWeightedStreams, Utils.instance.xWorkerHighRateMaxCount, excessWeight);
				}
				
				// 按设备的模型数量（优先移除模型数量少的设备）和权重（优先移除权重大的设备）排序
				List<Entry<String, Float>> sortedDevices = new ArrayList<>(deviceWeights.entrySet());
				sortedDevices.sort((e1, e2) -> {
					// 先按模型数量升序排序
					int sizeCompare = Integer.compare(
						instanceMap.getOrDefault(e1.getKey(), Collections.emptyList()).size(), 
						instanceMap.getOrDefault(e2.getKey(), Collections.emptyList()).size()
					);
					
					if (sizeCompare != 0) {
						return sizeCompare;
					}
					
					// 如果模型数量相同，则按权重降序排序（优先移除高权重的）
					return Float.compare(e2.getValue(), e1.getValue());
				});
				
				// 确定要移除的设备
				List<String> devicesToRemove = new ArrayList<>();
				for (Entry<String, Float> device : sortedDevices) {
					if (weightToRemove <= 0) {
						break;
					}
					
					String deviceId = device.getKey();
					Float weight = device.getValue();
					
					// 添加要移除的设备
					devicesToRemove.add(deviceId);
					weightToRemove -= weight;
					
					if(loggedForDispath) {
						log.info("[VideoHandleLog] [dispatch] 准备移除设备[{}]，权重[{}]，剩余需移除权重[{}]", 
							deviceId, weight, weightToRemove);
					}
				}
				
				// 执行移除操作
				for (String deviceId : devicesToRemove) {
					instanceMap.remove(deviceId);
				}
				
				if (devicesToRemove.size() > 0) {
					if (loggedForDispath) {
						log.info("[VideoHandleLog] [dispatch] remove Overflow weighted stream instance:{}, remove streams:{}, size:{}", 
							instanceId, devicesToRemove, devicesToRemove.size());
					}
					isChanged = true;
				}
			} else {
				// 原始逻辑，不考虑镜头算力
				if(entry.getValue().size() <= Utils.instance.xWorkerHighRateMaxCount) 
					continue;
				
				List<String> removes = instanceMap.entrySet().stream()
						.sorted((l, r) -> Integer.compare(l.getValue().size(), r.getValue().size()))
						.map(e -> e.getKey())
						.limit(entry.getValue().size() - Utils.instance.xWorkerHighRateMaxCount)
						.collect(Collectors.toList());
				
				for(String remove : removes)
					instanceMap.remove(remove);

				if(removes.size() > 0){
					if(loggedForDispath)
						log.info("[VideoHandleLog] [dispatch] remove Overflow stream instance:{}, remove streams:{}, size:{}", 
							entry.getKey(), removes, removes.size());
					
					isChanged = true;
				}
			}
		}
		
		return isChanged;
	}

	/** 删除停掉的视频流和模型*/
	private boolean dispatchRemove(List<String> removing, Map<String, String> existingStreamMap) {
		boolean isChanged = true;
		Boolean loggedForDispath = (Utils.instance.watchFrameTiktokLevel == -177);
		/** 循环一下 删除环境中已经不存在设备模型流*/
		for(String id : removing) {
			String instanceId = existingStreamMap.remove(id);
			String[] info = id.split(PREFIX);
			
			String deviceId = info[0];
			String annotator = info[1];
			
			Map<String, List<String>> instanceInfo = ongoingStreams.get(instanceId);
			List<String> annotators = instanceInfo.get(deviceId);
			annotators.remove(annotator);
			
			if(annotators.isEmpty()) {
				instanceInfo.remove(deviceId);
				log.info("[VideoHandleLog] [dispatch] closing stream[" + deviceId + "] from instanceId[" + instanceId + "].");
			}else{
				log.info("[VideoHandleLog] [dispatch] removing annotator[" + annotator + "] from stream[" + deviceId + "] and instanceId[" + instanceId + "]");
			}
		}
		
		return isChanged;
	}

	private int getMinPriority(String deviceId, Holder infoHolder) {
		AtomicInteger minPriority = new AtomicInteger(Integer.MAX_VALUE);

		XSwitcher xSwitcher = infoHolder.getSwitcherHighRateMap().get(deviceId);
		if (xSwitcher != null && xSwitcher.getProcessors() != null) {
			xSwitcher.getProcessors().forEach((k, v) -> {
				Integer priority = v.getPriority(); // priority 是 Integer 类型
				if (priority != null) {
					minPriority.set(Math.min(minPriority.get(), priority));
				}
			});
		}

		return minPriority.get();
	}
	
	/** 新增视频流和模型  */
	private boolean dispatchCreate(List<String> creating, Set<String> existingSet, Holder infoHolder) {
		boolean isChanged = false;
		Boolean logForDispatch = (Utils.instance.watchFrameTiktokLevel == -177);
		
		/** 开始查看需要创建的 <deviceId, list<annotatorName>*/
		Map<String, List<String>> createMap = new HashMap<String, List<String>>();
		Map<String, List<String>> existMap  = new HashMap<String, List<String>>();
		Map<String, List<String>> errorMap  = new HashMap<String, List<String>>();
		
		for(String id : creating) {
			String[] info = id.split(PREFIX);
			
			String deviceId = info[0];
			String annotator = info[1];
			
			Map<String, List<String>> target = infoHolder.getAnnotatorMap().containsKey(annotator) ? createMap : errorMap;
			
			List<String> annotators = target.get(deviceId);
			if(annotators == null) {
				annotators = new ArrayList<String>();
				target.put(deviceId, annotators);
			}
			
			annotators.add(annotator);
		}
		
		for(String id : existingSet) {
			String[] info = id.split(PREFIX);
			
			String deviceId = info[0];
			String annotator = info[1];
			
			Map<String, List<String>> target = infoHolder.getAnnotatorMap().containsKey(annotator) ? existMap : errorMap;
			
			List<String> annotators = target.get(deviceId);
			if(annotators == null) {
				annotators = new ArrayList<String>();
				target.put(deviceId, annotators);
			}
			
			annotators.add(annotator);
		}
		
		int streamToAdd = 16;
		if(logForDispatch)
			log.info("[VideoHandleLog] [dispatch] dispatchCreate createMap : {} streamToAdd: 16 ", JSON.toJSONString(createMap));
		//@Alert 优化 随机取流 而不是每次都顺序 haspmap key遍历也许会重合 取出的流超过限制，下次还是取那几个，会一直卡住。

		List<String>  keys = new ArrayList(createMap.keySet());
		
		// 根据开关决定排序方式
		if(Utils.instance.cameraDispatch) {
			// 首先按优先级排序，然后按算力倍数排序（算力小的优先）
			keys.sort((k1, k2) -> {
				// 先按优先级排序
				int p1 = getMinPriority(k1, infoHolder);
				int p2 = getMinPriority(k2, infoHolder);
				int priorityCompare = Integer.compare(p1, p2);
				
				// 如果优先级相同，再按算力排序
				if(priorityCompare == 0) {
					Float m1 = infoHolder.getDeviceResolutionMultiplierMap().getOrDefault(k1, 1.0f);
					Float m2 = infoHolder.getDeviceResolutionMultiplierMap().getOrDefault(k2, 1.0f);
					return Float.compare(m1, m2); // 算力小的优先
				}
				return priorityCompare;
			});
		} else {
			// 原来的排序逻辑：只按优先级排序
			keys.sort((k1, k2) -> {
				int p1 = getMinPriority(k1, infoHolder);
				int p2 = getMinPriority(k2, infoHolder);
				return Integer.compare(p1, p2);
			});
		}
		
		Map<String, List<String>> shuffleMap = new LinkedHashMap<>();
		keys.forEach(k->shuffleMap.put(k, createMap.get(k)));


		for(Entry<String, List<String>> entry : shuffleMap.entrySet()) {
			String deviceId = entry.getKey();
			log.info("[VideoHandleLog] [dispatch] create stream id : {}", deviceId);
			List<String> creatingAnnotators = entry.getValue();
			
			//删除存在的流的模型 合并到create里面
			//目前一个流只有一个模型,这个合并 不是所有流的合并
			List<String> existingAnnotators = existMap.get(deviceId);
			if(!CollectionUtils.isEmpty(existingAnnotators)) {
				creatingAnnotators.addAll(existingAnnotators);
				
				for(String existingAnnotator : existingAnnotators) {
					for(Map<String, List<String>> instanceInfo : ongoingStreams.values()) {
						List<String> ongoingAnnotators = instanceInfo.get(deviceId);
						if(!CollectionUtils.isEmpty(ongoingAnnotators))
							ongoingAnnotators.remove(existingAnnotator);
					}
				}
			}
			
			//将create里面不在环境存在的模型删掉
			for(Iterator<String> it = creatingAnnotators.iterator(); it.hasNext();)
				if(!infoHolder.getAnnotatorMap().containsKey(it.next()))
					it.remove();

			//找到一个最优解安排并塞进去
			// List<Pair<instance, annotatorsList> 一个device会跑不同的annotator

			List<Pair<ServiceInstance, List<String>>> bests = findBest(creatingAnnotators, infoHolder);
			if(logForDispatch)
				log.info("[VideoHandleLog] [dispatch] dispatchCreate bests creatingAnnotators : {} bests: {} ", JSON.toJSONString(creatingAnnotators),  JSON.toJSONString(bests));
			for(Pair<ServiceInstance, List<String>> best : bests) {
				Map<String, List<String>> instanceInfo = ongoingStreams.get(best.getLeft().getInstanceId());

				// @Alert镜头算力
				// 根据开关决定是否使用镜头算力功能
				if (Utils.instance.cameraDispatch) {
					// 计算当前设备和已分配设备的算力权重
					// 1. 从video_stream_infra表的videoStreamInfraRepository获取设备分辨率
					float streamMultiplier = 1.0f; // 默认当前设备的权重为1

					// 从Holder中获取设备分辨率倍数
					Float cachedMultiplier = infoHolder.getDeviceResolutionMultiplierMap().get(deviceId);
					if (cachedMultiplier != null && cachedMultiplier > 0) {
						streamMultiplier = cachedMultiplier;
					}

					// 计算已分配设备的加权流数量
					float totalWeightedStreams = 0.0f;
					for (Map.Entry<String, List<String>> deviceEntry : instanceInfo.entrySet()) {
						String existingDeviceId = deviceEntry.getKey();
						// 获取已分配设备的倍数
						Float deviceMultiplier = infoHolder.getDeviceResolutionMultiplierMap().get(existingDeviceId);
						if (deviceMultiplier != null && deviceMultiplier > 0) {
							totalWeightedStreams += deviceMultiplier;
						} else {
							// 如果没找到，按1倍计算
							totalWeightedStreams += 1.0f;
						}
					}
					
					// 获取当前请求相关的模型名称
					Set<String> requestModelNames = new HashSet<>(best.getRight());
					
					// 检查每个模型的流数限制
					boolean canAllocate = true;
					for (XDynamicModel model : currentModels) {
						if (requestModelNames.contains(model.getAnnotatorName()) && model.getMaxStreamCount() > 0) {
							// 计算当前模型已分配的流数（考虑设备分辨率倍数）
							float currentModelWeightedStreams = 0.0f;
							for (Map.Entry<String, List<String>> deviceEntry : instanceInfo.entrySet()) {
								String existingDeviceId = deviceEntry.getKey();
								List<String> deviceModels = deviceEntry.getValue();
								
								if (deviceModels.contains(model.getAnnotatorName())) {
									// 获取设备的分辨率倍数
									Float deviceMultiplier = infoHolder.getDeviceResolutionMultiplierMap().get(existingDeviceId);
									if (deviceMultiplier != null && deviceMultiplier > 0) {
										currentModelWeightedStreams += deviceMultiplier;
									} else {
										// 如果没找到，按1倍计算
										currentModelWeightedStreams += 1.0f;
									}
								}
							}
							
							// 如果当前模型已分配的加权流数加上新设备的加权流数超过限制，则不能分配
							if (currentModelWeightedStreams + streamMultiplier > model.getMaxStreamCount()) {
								canAllocate = false;
								log.warn("设备[{}]分配失败，模型[{}]当前加权流数{}，加上设备倍数{}后超过最大允许流数{}",
										deviceId, model.getAnnotatorName(), currentModelWeightedStreams, streamMultiplier, model.getMaxStreamCount());
								break;
							}
						}
					}

					// 判断是否可以分配，满足worker最大流数和模型最大流数两个条件
					float workerMaxCount = Utils.instance.xWorkerHighRateMaxCount;
					if (canAllocate && (totalWeightedStreams + streamMultiplier) <= workerMaxCount + 0.00001f) {
						log.info("设备[{}]的流倍数为{}，当前总加权流数为{}，worker允许最大流数{}",
								deviceId, streamMultiplier, totalWeightedStreams, workerMaxCount);
						// 分配成功，更新设备列表
						instanceInfo.put(deviceId, best.getRight());
						isChanged = true;
					} else {
						// 分配失败，总流数超过限制
						log.warn("设备[{}]分配失败，当前总加权流数为{}，设备的流倍数为:{},worker允许最大流数{}",
								deviceId, totalWeightedStreams, streamMultiplier,workerMaxCount);
					}
				} else {
					// 使用原来的逻辑，不考虑镜头算力
					// 获取模型的最大流数量，只考虑当前请求相关的模型

					// @Alert 获取正在跑的模型对应的流数量
					//currentModels 是所有模型 ongoingStreams是所有流
					////根据模型配置流数量 currentModels //获取当前节点 模型对应流的数量 instanceInfo
					// 计算每个模型对应的设备数量
					Map<String, Integer> modelDeviceCount = new HashMap<>();
					boolean checkBest = false;
					List<String> bestRight = new ArrayList<>();

					if(Utils.instance.stabilityFlag) {
						for (List<String> models : instanceInfo.values()) {
							for (String model : models) {
								int count = modelDeviceCount.getOrDefault(model, 0);
								modelDeviceCount.put(model, count + 1);
							}
						}

						for (String bestStr : best.getRight()) {
							for (XDynamicModel models : currentModels) {
								String annotatorName = models.getAnnotatorName();
								Integer deviceCount = modelDeviceCount.get(annotatorName);
								if (bestStr.equals(annotatorName) && deviceCount != null && models.getMaxStreamCount() > 0) {
									checkBest = true;
									if (deviceCount < models.getMaxStreamCount()) {
										bestRight.add(annotatorName);
									}
								}
							}
						}
						log.info("[VideoHandleLog] [dispatch] bestRight stream[" + deviceId + "] to instanceId[" + best.getLeft().getInstanceId() + "] using annotators[" + StringUtils.join(bestRight, ",") + "], modelDeviceCount:" + modelDeviceCount
								+ ",checkBest:"+ checkBest + ",currentModels:" + currentModels.stream().map(XDynamicModel::getAnnotatorName).collect(Collectors.toSet()) + ",instanceInfo:" + instanceInfo.values());
					}

					if(instanceInfo.size() < Utils.instance.xWorkerHighRateMaxCount) {
						List<String> result = (!modelDeviceCount.isEmpty() && checkBest ) ? bestRight : best.getRight();
						if(result.size() > 0) {
							instanceInfo.put(deviceId, result);
							isChanged = true;
						}
						log.info("[VideoHandleLog] [dispatch] opening stream[" + deviceId + "] to instanceId[" + best.getLeft().getInstanceId() + "] using annotators[" + result + "],isChanged:" + isChanged + ",check:" + checkBest + ",modelDeviceCountEmpty:" + (!modelDeviceCount.isEmpty()) );
					}else{
						log.warn("[VideoHandleLog] [dispatch] create stream deviceId {} failed instance size {} overload , find next worker...",deviceId, instanceInfo.size());
					}

				}
			}
			
			if(-- streamToAdd <= 0)
				break;
		}
		
		if(!errorMap.isEmpty())
			log.info("[VideoHandleLog] [dispatch] some annotator is missing in this enviroment, can not to create model stream [" + errorMap.toString() + "],annotatorMap:[" + infoHolder.getAnnotatorMap().toString() + "].");

		
		return isChanged;
	}

	/** 随机选一个device  查一下这个device是否是best*/
	private boolean dispatchBest(Set<String> deviceIds, Holder infoHolder) {
		String targetDeviceId = deviceIds.toArray(String[]::new)[ThreadLocalRandom.current().nextInt(deviceIds.size())];

		long ongoingCount = ongoingStreams.values().stream().map(map -> map.get(targetDeviceId)).filter(Objects::nonNull).count();
		
		List<String> ongoingAnnotators = ongoingStreams.values().stream()
				.map(map -> map.get(targetDeviceId))
				.filter(Objects::nonNull)
				.flatMap(List::stream)
				.collect(Collectors.toList());
		
		List<Pair<ServiceInstance, List<String>>> bestTargets = findBest(ongoingAnnotators, infoHolder);
		
		/** 如果算出来的最优解比当前解流数要小  那么删掉旧的 添加新的*/
		if(!bestTargets.isEmpty() && bestTargets.size() < ongoingCount) {
			ongoingStreams.values().forEach(map -> map.remove(targetDeviceId));
			
			for(Pair<ServiceInstance, List<String>> best : bestTargets) {
				Map<String, List<String>> instanceInfo = ongoingStreams.get(best.getLeft().getInstanceId());
				instanceInfo.put(targetDeviceId, best.getRight());
			}
			
			String resultLog = bestTargets.stream().map(pair -> "{instanceId[" + pair.getLeft().getInstanceId() + "], annotator[" + StringUtils.join(pair.getValue(), ",") + "]}").collect(Collectors.joining(","));
			log.info("[VideoHandleLog] [dispatch] rearrange stream[" + targetDeviceId + "] to " + resultLog);
			return true;
		}else {
			return false;
		}
	}
	
	/** 利用算法导论里面最简单的 贪心算法加递归 效率差实现简单 给定一套模型 找出最少节点列表能满足所有模型的  */
	private List<Pair<ServiceInstance, List<String>>> findBest(List<String> annotators, Holder infoHolder){
		Boolean loggedForDispatch = (Utils.instance.watchFrameTiktokLevel == -177);
		Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap = infoHolder.getInstanceIdMap().entrySet()
				.stream()
				.filter(entry -> {
					QueryWorkerResult qmr = entry.getValue().getRight();
					
					boolean empty = qmr.getDynamicModels().stream().filter(model -> annotators.contains(model.getAnnotatorName())).findAny().isEmpty();
					if(empty || qmr.getAvailableGpuMemory() < ((availableGpuMemory > 0) ? availableGpuMemory : 1024))
						return false;

					if(Utils.instance.xGpuMemRateLimit > 0 &&  qmr.getCurrentGpuRate() >= Utils.instance.xGpuMemRateLimit){
						log.error("[VideoHandleLog] [dispatch] dispatchError overflow gpu memory,nonSeenGpuMemLimit:{},getCurrentGpuRate:{},{},{}", Utils.instance.nonSeenGpuMemLimit, qmr.getCurrentGpuRate(),entry.getKey(), entry.getValue().getLeft().getInstanceId());
						return false;
					}
					
					// 如果启用了镜头算力计算
					if (Utils.instance.cameraDispatch) {
						// 计算已分配设备的加权流数量，考虑镜头算力
						float totalWeightedStreams = 0.0f;
						
						for (String deviceId : qmr.getDynamicModels().stream()
								.map(Model::getHigtRateDevices)
								.flatMap(List::stream)
								.collect(Collectors.toSet())) {
							// 获取设备的分辨率倍数
							Float deviceMultiplier = infoHolder.getDeviceResolutionMultiplierMap().get(deviceId);
							if (deviceMultiplier != null && deviceMultiplier > 0) {
								totalWeightedStreams += deviceMultiplier;
							} else {
								// 如果没找到，按1倍计算
								totalWeightedStreams += 1.0f;
							}
						}
						
						if (totalWeightedStreams >= Utils.instance.xWorkerHighRateMaxCount) {
							log.debug("Worker总加权流数[{}]超过或等于最大限制[{}]，不可分配", 
								totalWeightedStreams, Utils.instance.xWorkerHighRateMaxCount);
							return false;
						}
					} else {
						// 原有逻辑，不考虑镜头算力
						Set<String> ongoings = qmr.getDynamicModels().stream()
								.map(Model::getHigtRateDevices)
								.flatMap(List::stream)
								.collect(Collectors.toSet());
						
						if(ongoings.size() >= Utils.instance.xWorkerHighRateMaxCount)
							return false;
					}

					// 检查每个模型的流数限制
					int checkAllMax = 0;
					
					// 定义模型相关变量，便于后续在日志中使用
					Map<String, Float> modelWeightedStreams = null;
					Map<String, Integer> modelDeviceCount = null;
					
					if (Utils.instance.cameraDispatch) {
						// 使用镜头算力计算模型的加权流数
						modelWeightedStreams = new HashMap<>();
						
						// 首先初始化每个模型的加权流数为0
						for (Model model : qmr.getDynamicModels()) {
							modelWeightedStreams.put(model.getAnnotatorName(), 0.0f);
						}
						
						// 计算每个模型的当前加权流数
						for (Model model : qmr.getDynamicModels()) {
							String annotatorName = model.getAnnotatorName();
							for (String deviceId : model.getHigtRateDevices()) {
								// 获取设备的分辨率倍数
								Float deviceMultiplier = infoHolder.getDeviceResolutionMultiplierMap().get(deviceId);
								if (deviceMultiplier != null && deviceMultiplier > 0) {
									modelWeightedStreams.put(annotatorName, 
										modelWeightedStreams.get(annotatorName) + deviceMultiplier);
								} else {
									// 如果没找到，按1倍计算
									modelWeightedStreams.put(annotatorName, 
										modelWeightedStreams.get(annotatorName) + 1.0f);
								}
							}
						}
						
						// 检查每个模型的加权流数是否超过限制
						for (XDynamicModel model : currentModels) {
							String annotatorName = model.getAnnotatorName();
							Float weightedStreams = modelWeightedStreams.get(annotatorName);
							
							if (weightedStreams != null && model.getMaxStreamCount() > 0) {
								if (weightedStreams >= model.getMaxStreamCount()) {
									log.debug("模型[{}]当前加权流数[{}]超过或等于最大限制[{}]", 
										annotatorName, weightedStreams, model.getMaxStreamCount());
									checkAllMax++;
								}
							}
						}
						
						if (loggedForDispatch) {
							log.info("[VideoHandleLog] [dispatch] findBest 加权流数: " + JSON.toJSONString(modelWeightedStreams));
						}
					} else {
						// 原有逻辑，不考虑镜头算力
						modelDeviceCount = new HashMap<>();

						for (Model models : qmr.getDynamicModels()) {
							modelDeviceCount.put(models.getAnnotatorName(), models.getHigtRateDevices().size());
						}
						
						for (XDynamicModel models : currentModels) {
							String annotatorName = models.getAnnotatorName();
							Integer deviceCount = modelDeviceCount.get(annotatorName);
							if (deviceCount != null && models.getMaxStreamCount() > 0) {
								if (deviceCount >= models.getMaxStreamCount()) {
									checkAllMax++;
								}
							}
						}
					}
					if(loggedForDispatch) {
						if (Utils.instance.cameraDispatch) {
							// 当启用镜头算力时，使用modelWeightedStreams
							log.info("[VideoHandleLog] [dispatch] findBest stream instance:["+ entry.getKey() +"], weighted mode, to: " + checkAllMax + ":" + qmr.getDynamicModels().size());
						} else {
							// 使用已定义的modelDeviceCount
							log.info("[VideoHandleLog] [dispatch] findBest stream instance:["+ entry.getKey() +"],[" + JSON.toJSONString(modelDeviceCount) + "] to: " + checkAllMax + ":" + qmr.getDynamicModels().size());
						}
					}
					
					if(checkAllMax > 0 && qmr.getDynamicModels().size() == checkAllMax) {
						if (Utils.instance.cameraDispatch) {
							log.info("[VideoHandleLog] [dispatch] findBest stream false instance:["+ entry.getKey() +"], weighted mode, to: " + checkAllMax + ":" + qmr.getDynamicModels().size());
						} else {
							log.info("[VideoHandleLog] [dispatch] findBest stream false instance:["+ entry.getKey() +"],[" + JSON.toJSONString(modelDeviceCount) + "] to: " + checkAllMax + ":" + qmr.getDynamicModels().size());
						}
						return false;
					}

					return true;
				})
				.collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue()));
		
		List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> result= findBest_0(2, annotators, instanceIdMap);
		if(loggedForDispatch)
			log.info("[VideoHandleLog] [dispatch] findBest result[" + JSON.toJSONString(result) + "]");
		return result.stream()
					 .map(triple -> new ImmutablePair<ServiceInstance, List<String>>(triple.getLeft(), triple.getRight()))
					 .collect(Collectors.toList());
	}
	
	/** 没必要进行全解覆盖 贪心时只贪前几轮*/
	@SuppressWarnings({ "unchecked", "rawtypes" })
	private List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> findBest_0(int round, List<String> annotators, Map<String, Pair<ServiceInstance, QueryWorkerResult>> instanceIdMap){
		if(annotators.isEmpty() || instanceIdMap.isEmpty())
			return Lists.newArrayList();
		//<instance,instance运行情况, instance上运行的能力与需要运行的能力交集>
		Boolean loggedForDispatch = (Utils.instance.watchFrameTiktokLevel == -177);
		
		List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> sortedInstanceList = instanceIdMap.values().stream()
				.map(pair -> {
					log.info("[VideoHandleLog] [dispatch] findBest stream result instance:[" + pair.getLeft() + "], getRight: " + pair.getRight().getDynamicModels().stream().map(model -> model.getAnnotatorName()).collect(Collectors.toList()) + ",annotators:[" + annotators + "]");
					List intersection = ListUtils.intersection(annotators, pair.getRight().getDynamicModels().stream().map(model -> model.getAnnotatorName()).collect(Collectors.toList()));
					return new ImmutableTriple<ServiceInstance, QueryWorkerResult, List<String>>(pair.getLeft(), pair.getRight(), intersection);
				})
				.sorted((l, r) -> {
					//由大到小
					return - Integer.compare(l.getRight().size(), r.getRight().size());
				})
				.collect(Collectors.toList());

		//round = sortedInstanceList.size();
		if(loggedForDispatch)
			log.info("[VideoHandleLog] [dispatch] findBest stream sortedInstanceList :["+ sortedInstanceList + "]");

		Triple<ServiceInstance, QueryWorkerResult, List<String>> thisRoundTarget[] = new Triple[Math.min(round, sortedInstanceList.size())];
		List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> thisRoundSubTarget[] = new List[Math.min(round, sortedInstanceList.size())];
		
		for(int index = 0; index < round && index < sortedInstanceList.size(); index ++) {
			thisRoundTarget[index] = sortedInstanceList.get(index);
			// 用交集剩下的能力再去做贪心
			List<String> subAnnotators = ListUtils.removeAll(annotators, thisRoundTarget[index].getMiddle().getDynamicModels().stream().map(model -> model.getAnnotatorName()).collect(Collectors.toList()));
			Map<String, Pair<ServiceInstance, QueryWorkerResult>> subInstanceIdMap = new HashMap<String, Pair<ServiceInstance, QueryWorkerResult>>(instanceIdMap);
			subInstanceIdMap.remove(thisRoundTarget[index].getLeft().getInstanceId());
			
			//删除掉本次选中的最佳值之后 递归进入下一轮贪心
			thisRoundSubTarget[index] = findBest_0(round, subAnnotators, subInstanceIdMap);
		}

		// 在返回之前打印合并后的结果
		for (int index = 0; index < round && index < sortedInstanceList.size(); index++) {
			// 获取合并的目标
			List<Triple<ServiceInstance, QueryWorkerResult, List<String>>> combinedTargets =
					ListUtils.union(Lists.newArrayList(thisRoundTarget[index]), thisRoundSubTarget[index]);
			// 打印合并后的列表内容
			int finalIndex = index;
			combinedTargets.forEach(target -> {
				log.info("gpuStream combined Targets for index"+ finalIndex + "- Target: " + target.getLeft() + ", Result: " + target.getMiddle() + ", Annotations: " + target.getRight() + ",GOU:" + target.getMiddle().getAvailableGpuMemory());
			});
		}

		
		return Stream.iterate(0, i -> i + 1)
					 .limit(Math.min(round, sortedInstanceList.size()))
					 .map(index -> ListUtils.union(Lists.newArrayList(thisRoundTarget[index]), thisRoundSubTarget[index]))
					 .max((l, r) -> {
						 if(l.size() != r.size())
							 return - Integer.compare(l.size(), r.size());
						 
						 int lmem = l.stream().map(item -> item.getMiddle().getAvailableGpuMemory()).reduce(Integer::sum).get();
						 int rmem = r.stream().map(item -> item.getMiddle().getAvailableGpuMemory()).reduce(Integer::sum).get();
						 //显存大的在前面
						 return Integer.compare(lmem, rmem);
					 })
					 .get();
	}
	
	/** 只寻找视频最多的那一套模型 添加亲和性*/
	public synchronized void judgeAffinity() {
		Holder infoHolder = watcher.getHolder();
		
		Map<String, XSwitcher> highRateStreams = infoHolder.getSwitcherHighRateMap();
		
		//<annotatorName, affinityName>
		Map<String, String> normalAffinityMap = new HashMap<String, String>();//人工设置的affinity
		Map<String, String> videoAffinityMap = new HashMap<String, String>();//自动设置的affinity
		Set<String> noAffinitySet = new HashSet<String>();//无affinity的
		
		for(XDynamicModel model : currentModels) {
			if(StringUtils.isBlank(model.getAffinityGroup())) {
				noAffinitySet.add(model.getAnnotatorName());
			}else if(model.getAffinityGroup().startsWith(SUGGEST)) {
				videoAffinityMap.put(model.getAnnotatorName(), model.getAffinityGroup());
			}else {
				normalAffinityMap.put(model.getAnnotatorName(), model.getAffinityGroup());
			}
		}
		
		//如果有人工设置亲和性组,那么删除所有自动设置的亲和性组, 因为已经人工设置过, 说明有明白人, 那就不要自动了
		if(!normalAffinityMap.isEmpty()) {
			for(XDynamicModel model : currentModels) {
				if(videoAffinityMap.containsKey(model.getAnnotatorName())) {
					model.setAffinityGroup("");
					mapper.saveAndFlush(model);
				}
			}
			return ;
		}
		
		List<List<String>> existingProcessorGroups = highRateStreams.entrySet().stream()
				.map(entry -> {
					List<String> processors = new ArrayList<String>(entry.getValue().getProcessors().keySet());
					return new ImmutablePair<String, List<String>>(entry.getKey(), processors);
				})
				.filter(pair -> pair.getRight().size() > 1)
				.collect(Collectors.toMap(
						entry -> entry.getRight(), 
						entry -> 1,
						Integer::sum
				))
				.entrySet()
				.stream()
				.sorted((l, r) -> {
					List<String> lm = l.getKey();
					List<String> rm = r.getKey();
					
					int lNum = lm.size() * l.getValue() - l.getValue();
					int rNum = rm.size() * r.getValue() - r.getValue();
					
					if(lNum != rNum)
						return Integer.compare(lNum, rNum);
					
					if(lm.size() != rm.size())
						return - Integer.compare(lm.size(), rm.size());
					
					for(int index = 0; index < lm.size(); index ++) {
						if(!lm.get(index).equals(rm.get(index))) {
							return lm.get(index).compareTo(rm.get(index));
						}
					}
					
					throw new RuntimeException("should not be here.");
				})
				.map(entry -> entry.getKey())
				.collect(Collectors.toList());
		
		if(existingProcessorGroups.size() <= 0) {
			for(XDynamicModel model : currentModels) {
				if(videoAffinityMap.containsKey(model.getAnnotatorName())) {
					model.setAffinityGroup("");
					mapper.saveAndFlush(model);
				}
			}
			return;
		}
		
		Map<String, String> targetAffinityMap = new HashMap<String, String>();
		for(List<String> existingProcessorGroup : existingProcessorGroups) {
			String affinity = SUGGEST + RandomStringUtils.randomAlphabetic(8);
			for(String annotator : existingProcessorGroup) {
				targetAffinityMap.put(annotator, affinity);
			}
		}
		
		for(XDynamicModel model : currentModels) {
			String affinity = targetAffinityMap.get(model.getAnnotatorName());
			if(StringUtils.isNotBlank(affinity)) {
				model.setAffinityGroup(affinity);
				mapper.saveAndFlush(model);
			}
		}
	}
	
	private static final String PREFIX = ";;;";
	private static final String SUGGEST = "VIDEO_AUTO_";
}
