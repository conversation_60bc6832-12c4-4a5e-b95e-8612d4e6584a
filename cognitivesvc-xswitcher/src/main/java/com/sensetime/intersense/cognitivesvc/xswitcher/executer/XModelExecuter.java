package com.sensetime.intersense.cognitivesvc.xswitcher.executer;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.http.HttpEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult.Model;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawVideo;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.XSwitcher;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.google.common.collect.Lists;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sun.jna.Pointer;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.client.RestTemplate;

@Component
@SuppressWarnings("rawtypes")
@Slf4j
public class XModelExecuter {
	
	@Autowired
	private XModelWatcher watcher;

	/** switcher解码视频流之后发送SenseyeXRawImage */
	@SuppressWarnings("unchecked")
	@Async("cogThreadPool")
	public ListenableFuture<Map<String, List<Map<String, Object>>>> sendSenseyeXRawImage(SenseyexRawImage senseyexRawImage) {
		Pointer frame = (Pointer)senseyexRawImage.getExtra().remove("frame");
		if(frame != null) {
			senseyexRawImage.setImages(new String[] {ImageUtils.bytesToBase64(FrameUtils.encode_image_as_jpg(frame))});
			FrameUtils.batch_free_frame(frame);
		}
		
		Holder infoHolder = watcher.getHolder();
		String deviceId = StringUtils.isBlank(senseyexRawImage.getDeviceId()) ? RandomStringUtils.randomAlphabetic(10) : senseyexRawImage.getDeviceId();
		XSwitcher switcher = infoHolder.getSwitcherLowRateMap().get(deviceId);
		
		Map<ServiceInstance, List<String>> targetInstanceMap = Map.of();
		if(switcher == null)
			targetInstanceMap = Arrays.stream(senseyexRawImage.getProcessors())
				.filter(p -> infoHolder.getAnnotatorMap().containsKey(p))
				.map(processor -> {
					ServiceInstance instance = infoHolder.chooseDynamicInstance(processor);
					return new ImmutablePair<ServiceInstance, String>(instance, processor);
				})
				.collect(Collectors.toMap(
						pair -> pair.getLeft(), 
						pair -> Lists.newArrayList(pair.getRight()), 
						ListUtils::union
				));
		else
			targetInstanceMap = switcher.getProcessors().values().stream()
				.filter(processor -> ArrayUtils.isEmpty(senseyexRawImage.getProcessors()) || ArrayUtils.contains(senseyexRawImage.getProcessors(), processor.getProcessor()))
				.map(processor -> {
					boolean buffered  = processor.fetchBufferMap() != null;				
					boolean contexted = Boolean.TRUE.equals(infoHolder.getAnnotatorModelMap().getOrDefault(processor.getProcessor(), Model.builder().build()).getConfig().get("needContext"));
					
					ServiceInstance instance = buffered || contexted ? infoHolder.chooseDynamicInstance(processor.getProcessor(), deviceId) : infoHolder.chooseDynamicInstance(processor.getProcessor());
					return new ImmutablePair<ServiceInstance, String>(instance, processor.getProcessor());
				})
				.collect(Collectors.toMap(
						pair -> pair.getLeft(), 
						pair -> Lists.newArrayList(pair.getRight()), 
						ListUtils::union
				));
		
		Boolean response = MapUtils.getBoolean(senseyexRawImage.getExtra(), "response");
		if(Boolean.TRUE.equals(response)) {
			Function<Entry<ServiceInstance, List<String>>, List<Pair<String, Object>>> mapper = entry -> {
				String[] processors = entry.getValue().stream().toArray(String[]::new);
				SenseyexRawImage event = SenseyexRawImage.builder()
						.deviceId(deviceId)
						.processors(processors)
						.images(senseyexRawImage.getImages())
						.capturedTime(senseyexRawImage.getCapturedTime())
						.extra(senseyexRawImage.getExtra())
						.build();

				boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;
				long start = 0L;
				if(loggedForCost){
					start = new Date().getTime();
				}
				if(loggedForCost) {
					log.info("[VideoHandleLog] [Cost] [handleLowRateEvent] step -4 intoHandle deviceId :{}, cost,{} ms, reqHost:{}, port: {}", deviceId, start - senseyexRawImage.getCapturedTime(), entry.getKey().getHost(), entry.getKey().getPort());
				}
				try {
					ServiceInstance instance = entry.getKey();
					List<Object> datas = (List<Object>)RestUtils.get(5000, 500, 100).postForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/execute/event", new HttpEntity<SenseyexRawImage>(event, RestUtils.headers), BaseRes.class).getData();
					
					List<Pair<String, Object>> result = new ArrayList<Pair<String, Object>>();
					for(int index = 0; index < processors.length; index ++)
						result.add(new ImmutablePair<String, Object>(processors[index], datas.get(index)));
					
					return result;
				}catch(Exception e) {
					log.error("call worker event cost {}", new Date().getTime() - senseyexRawImage.getCapturedTime());
					e.printStackTrace();
					return null;
				}
			};
			
			Map<String, List<Map<String, Object>>> data = targetInstanceMap.entrySet()
				  .parallelStream()
				  .map(mapper)
				  .flatMap(List::stream)
				  .filter(p -> p != null && p.getRight() != null)
				  .collect(Collectors.toMap(
					  p -> p.getLeft(), 
					  p -> {
						  if(p.getRight() instanceof List)
							  return (List<Map<String,Object>>)p.getRight();
						  else
							  return List.of((Map<String,Object>)p.getRight());
					  }
				  ));
			
			return new AsyncResult(data);
		}else {
			Consumer<Entry<ServiceInstance, List<String>>> action = entry -> {
				String[] processors = entry.getValue().stream().toArray(String[]::new);
				SenseyexRawImage event = SenseyexRawImage.builder()
						.deviceId(deviceId)
						.processors(processors)
						.images(senseyexRawImage.getImages())
						.capturedTime(senseyexRawImage.getCapturedTime())
						.extra(senseyexRawImage.getExtra())		
						.build();

				try {
					ServiceInstance instance = entry.getKey();
					BaseRes res = RestUtils.restTemplate1000ms.postForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/execute/event", new HttpEntity<SenseyexRawImage>(event, RestUtils.headers), BaseRes.class);
					if(!Boolean.valueOf(res.getData().toString()))
						throw new RuntimeException("worker[" + instance.getHost() + ":" + instance.getPort() + "] image queue is full, send failed.");
				}catch(Exception e) {
					e.printStackTrace();
				}
			};
			
			targetInstanceMap.entrySet().parallelStream().forEach(action);
			
			return new AsyncResult(new Object());
		}
	}
	
	/** switcher发送SenseyeXRawVideo */
	@Async("cogThreadPool")
	public void sendSenseyeXRawVideo(SenseyexRawVideo senseyexRawVideo) {
		watcher.refresh(false);
		
		Holder infoHolder = watcher.getHolder();
		String deviceId = senseyexRawVideo.getDeviceId();
		
		List<XSwitcher> switcherMap = infoHolder.getSwitcherMap().get(deviceId);		
		Stream<Processor> processorStream = CollectionUtils.isNotEmpty(switcherMap) 
				? switcherMap.stream()
						     .map(s -> s.getProcessors().values())
						     .flatMap(Collection::stream)
							 .filter(processor -> ArrayUtils.isEmpty(senseyexRawVideo.getProcessors()) || ArrayUtils.contains(senseyexRawVideo.getProcessors(), processor.getProcessor()))
				: Arrays.stream(senseyexRawVideo.getProcessors())
						.filter(p -> infoHolder.getAnnotatorMap().containsKey(p))
						.map(p -> Processor.builder().processor(p).build());
		
		Map<ServiceInstance, List<Processor>> targetInstanceMap = processorStream
			.map(processor -> {
				ServiceInstance instance = infoHolder.chooseDynamicVideo(processor.getProcessor());
				return new ImmutablePair<ServiceInstance, Processor>(instance, processor);
			})
			.collect(Collectors.toMap(
					pair -> pair.getLeft(), 
					pair -> Lists.newArrayList(pair.getRight()), 
					ListUtils::union
			));
		
		Consumer<Entry<ServiceInstance, List<Processor>>> action = entry -> {
			SenseyexRawVideo event = SenseyexRawVideo.builder()
					.deviceId(deviceId)
					.processors(entry.getValue().stream().map(p -> p.getProcessor()).toArray(String[]::new))
					.video(senseyexRawVideo.getVideo())
					.capturedTime(senseyexRawVideo.getCapturedTime())
					.extra(senseyexRawVideo.getExtra())
					.rtmpDestination(senseyexRawVideo.getRtmpDestination())
					.rtmpOn(senseyexRawVideo.getRtmpOn())
					.build();

			try {
				ServiceInstance instance = entry.getKey();
				BaseRes res = RestUtils.restTemplate1000ms.postForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xworker/execute/video", new HttpEntity<SenseyexRawVideo>(event, RestUtils.headers), BaseRes.class);
				if(!Boolean.valueOf(res.getData().toString()))
					throw new RuntimeException("worker[" + instance.getHost() + ":" + instance.getPort() + "] video queue is full, send failed.");
			}catch(Exception e) {
				e.printStackTrace();
			}
		};
		
		targetInstanceMap.entrySet().parallelStream().forEach(action);
	}
}
