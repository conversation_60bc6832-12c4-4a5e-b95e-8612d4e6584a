package com.sensetime.intersense.cognitivesvc.xswitcher.video.filter;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.core.annotation.Order;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelExecuter;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher;
import com.sensetime.intersense.cognitivesvc.xswitcher.executer.XModelWatcher.Holder;
import com.sun.jna.Pointer;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.google.common.collect.Maps;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.XSwitcher;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;

import lombok.Setter;

@Order(150)
public class XSwitcherNonSeenFilter extends VideoNonSeenFilter{
	
	private final ConcurrentHashMap<String, Long> ticker = new ConcurrentHashMap<String, Long>();
	
	@Setter
	private static XModelWatcher watcher;
	
	@Setter
	private static XModelExecuter executor;
	
	@Override
	protected void handleStageTwo(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		Holder infoHolder = watcher.getHolder();
		
		XSwitcher[] entities = Arrays.stream(devices)
				.map(device -> infoHolder.getSwitcherLowRateMap().get(device.getDeviceId()))
				.toArray(XSwitcher[]::new);
		
		List<List<String>> targetProcessors = new ArrayList<List<String>>();
		for(int index = 0; index < devices.length; index ++){
			long now = videoFrames[index].getCapturedTime();
			
			VideoStreamInfra device = devices[index];
			XSwitcher entity = entities[index];
			if(entity == null) 
				continue;
			
			List<String> processors = new ArrayList<String>();
			for(Processor processor : entity.getProcessors().values()) {
				if(!infoHolder.getModelDynamicInstanceMap().containsKey(processor.getProcessor()))
					continue;
				
				String key = device.getDeviceId() + "_" + processor.getProcessor();
				long previousTime = ticker.getOrDefault(key, 0L);
				
				boolean pass = now - previousTime > Objects.requireNonNullElse(processor.getInterval(), 1) * 1000;
				if(pass)
					ticker.put(key, now);
				else 
					continue;
				
				processors.add(processor.getProcessor());
			}
			
			targetProcessors.add(processors);
		}
		
		context.get().put("processors", targetProcessors);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	protected void handleStageThree(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		List<List<String>> targetProcessors = (List<List<String>>)context.get().remove("processors");
		if(targetProcessors == null)
			return ;

		if(targetProcessors.isEmpty())
			return ;
		
		for(int index = 0; index < devices.length; index++) {
			List<String> processors = targetProcessors.get(index);		
			if(processors.isEmpty())
				continue;

			HashMap<String, Object> extraMap = Maps.newHashMap(Map.of("frame", FrameUtils.ref_frame(videoFrames[index].getGpuFrame())));
			extraMap.put("frameDecodeCost", videoFrames[index].getFrameDecodeCost());
			SenseyexRawImage event = SenseyexRawImage.builder()
					.processors(processors.toArray(String[]::new))
					.deviceId(devices[index].getDeviceId())
					.capturedTime(videoFrames[index].getCapturedTime())
					.extra(extraMap)
					.build();
			
			try {
				executor.sendSenseyeXRawImage(event);
			}catch(Exception e) {
				FrameUtils.batch_free_frame((Pointer)event.getExtra().remove("frame"));	
			}
		}
	}
}
