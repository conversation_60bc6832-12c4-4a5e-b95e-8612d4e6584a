目前发现 整个解码 +  跑pipeline的流程 有 gpu显存泄漏，
流程是：
1 com.sensetime.intersense.cognitivesvc.server.utils.VideoStream#grabberNextFrame
这个方法解码，

2  帧塞到队列
XSenseyexEventHandler.highRateHandleMap.get(device.getDeviceId()).offer(entity);


3 从队列从队列取出
com.sensetime.intersense.cognitivesvc.xworker.handler.XSenseyexEventHandler#handleSenseyexRawFrame

4 高频流直接处理消息

com.sensetime.intersense.cognitivesvc.xworker.handler.XSenseyexEventHandler.handleHighRateEvent


5 处理帧

com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.handle

6  送到pipeline处理队列

extractQueue(modelRequest.getParameter()).offer(batchItem)

7 从 pipeline队列取出  调用pipeline

handlingList = Utils.drainFromMapQueue(extractQueueMap, getDrainPollCount(), getDrainTimeout());


调用pipeline 方法 batch_extract_xmodel_asap


这个类 会复写 batch_extract_xmodel_asap方法
com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code.RoiIntrudeStay.batch_extract_xmodel_asap


splitFlockKesonAndDestroy
