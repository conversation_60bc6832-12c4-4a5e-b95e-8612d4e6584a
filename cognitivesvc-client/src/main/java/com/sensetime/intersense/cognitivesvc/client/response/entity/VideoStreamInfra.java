package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(title = "直接视频流设备", description = "直接视频流设备")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamInfra {

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "设备tag")
    private String deviceTag;

    @Schema(description = "视频rtsp源")
    private String rtspSource;

    @Schema(description = "视频width")
    private Integer rtspWidth;

    @Schema(description = "视频height")
    private Integer rtspHeight;

    @Schema(description = "rtmp推源")
    private String rtmpDestination;

    @Schema(description = "是否播放rtmp")
    private String rtmpOn;

    @Schema(description = "ffmpeg的option参数")
    private String rtmpOption;

    @Schema(description = "解码类型(optional)")
    private String decoderFormat;

    @Schema(description = "该路视频最大检测帧数，达到数值后关闭流")
    private Integer frameMax;

    @Schema(description = "检测1帧跳N帧")
    private Integer frameSkip;

    @Schema(description = "预留帧池大小")
    private Integer frameBuffer;

    @Schema(description = "预留帧池策略")
    private String frameBufferStrategy;

    @Schema(description = "设备状态")
    private Integer sts;

    @Schema(description = "种子")
    private String seed;

    @Schema(description = "最后活跃时间")
    private Date updateTs;

    @Schema(description = "是否存活继续播放")
    private Date keepAlive;

    @Schema(description = "速率")
    private Integer videoRate;

    @Schema(description = "权限组")
    private String privilege;

    public static final String initSeed = "waiting to steal";

    public static final String nonRtspDoneSeed = "done";
}
