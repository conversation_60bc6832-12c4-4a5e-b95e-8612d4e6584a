package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.VideoIcon;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(path = "/cognitive/icon/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface IconFeign {

    @Operation(summary = "显示视频配置总数", method = "GET")
    @RequestMapping(value = "/getRenderConfigCount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Long> getRenderConfigCount();

    @Operation(summary = "显示视频的配置-分页", method = "GET")
    @RequestMapping(value = "/getRenderConfig", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Object> getRenderConfig(@RequestParam(required = false) String person_tag, @RequestParam(required = false) String device_id, @RequestParam int current, @RequestParam int size);

    @Operation(summary = "设置显示视频的配置", method = "POST")
    @RequestMapping(value = "/addOrUpdateRenderConfig", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<String> addOrUpdateRenderConfig(@RequestBody VideoIcon icon);

    @Operation(summary = "删除显示视频的配置", method = "POST")
    @RequestMapping(value = "/deleteRenderConfig", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Integer> deleteRenderConfig(@RequestParam(required = false) String person_tag, @RequestParam(required = false) String device_id);
}
