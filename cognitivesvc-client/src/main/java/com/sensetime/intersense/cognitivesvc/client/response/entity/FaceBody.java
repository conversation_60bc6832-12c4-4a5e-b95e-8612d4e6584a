package com.sensetime.intersense.cognitivesvc.client.response.entity;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FaceBody {
	
	/** 脸索引 */
	private int id;
	/** 图片索引 */
	private int imageId;
	/** 特征 */
	private Rect hunter;
	/** 置信度*/
	private double confidence;
	/** 标签 */
	private int label;
	/** 属性 */
	private Map<String, Object> attribute;
	/** 特征 */
	private Object feature;
	/** 匹配id */
	private Integer matchedId;
	/** 匹配实体 */
	private FaceBody matchedFaceBody;
	
	@Data
	@Accessors(chain = true)
	@NoArgsConstructor
	@Builder
	@AllArgsConstructor
	public static class Rect{
		private int left;
		private int top;
		private int width;
		private int height;
	}
}
