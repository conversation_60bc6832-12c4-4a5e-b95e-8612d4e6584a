package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(title = "X动态模型", description = "X动态模型")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XDynamicModel {

    @Schema(description = "模型类名")
    private String annotatorName;

    @Schema(description = "组件路径,逗号分隔")
    private String pluginPaths;

    @Schema(description = "模型路径,逗号分隔")
    private String annotatorPaths;

    @Schema(description = "最后更新时间")
    private Date updateTs;

    @Schema(description = "状态，0开启，非0关闭")
    private Integer sts;

    @Schema(description = "动态编译类名(optional)")
    private String javaClassName;

    @Schema(description = "动态代码(optional)")
    private String javaCode;

    @Schema(description = "额外文件(optional)")
    private String attached;

    @Schema(description = "预估模型占用显存(optional)")
    private Integer estimateGpuMemory;

    @Schema(description = "预估模型份数(optional)")
    private Integer estimateCount;

    @Schema(description = "批量个数(optional)")
    private Integer batchSize;

    @Schema(description = "亲和性组，相同组名的模型优先部署相同显卡(非强制)")
    private String affinityGroup;

    @Schema(description = "希望模型部署在哪些pod上(非强制)")
    private String preferredIdentity;

    @Schema(description = "不可以部署在哪些pod上(强制)")
    private String requiredIdentity;

    @Schema(description = "不可以部署在哪些pod上(强制)")
    private String rejectedIdentity;

    @Schema(description = "相同group的模型独占一张卡")
    private String monopolizedGroup;

    @Schema(description = "独占信息")
    private String monopolizedIdentity;

    @Schema(description = "大于零表示该能力是cpu且有该值个副本")
    private Integer cpuModelDup;

    @Schema(description = "一些可选的额外信息用来描述算法行为")
    private String additional;

    @Schema(description = "模型运行状况描述")
    private String runtime;

    @Schema(description = "模型版本")
    private String version;

    @Schema(description = "模型版本")
    private String commitId;


    @Schema(description = "模型来源")
    private Integer modelSource;

    @Schema(description = "是否gpu model")
    private Integer gpuModel;

    @Schema(description = "gpu 显存")
    private Integer gpuMemUsage;

    @Schema(description = "模型中文名字")
    private String modelCnName;

    @Schema(description = "模型类型")
    private String modelType;
}
