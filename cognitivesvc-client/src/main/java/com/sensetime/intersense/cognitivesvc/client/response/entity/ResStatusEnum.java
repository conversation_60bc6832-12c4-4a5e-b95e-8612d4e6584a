package com.sensetime.intersense.cognitivesvc.client.response.entity;

public enum ResStatusEnum {
  
  SUCCESS("0000","success"),
  SERVER_ERROR("9900", "server error"),
  PARAM_MISSING("4001", "param error"),
  DETECT_FACE_FAILED("2109","DETECT FACE FAILED"),
  MULTI_FACE_DETECTED("2112","MULTI FACE DETECTED"),
  NO_FACE_DETECTED("2110","NO FACE DETECTED"),
  BAD_IMAGE_QUALITY("2102", "BAD IMAGE QUALITY!"),
  SAVE_FILE_FAILED("2103", "SAVE FILE FAILED!"),
  ILLEGAL_IMAGE_TYPE("2104","ILLEGAL IMAGE TYPE"),
  ILLEGAL_FILE_NAME("2105","ILLEGAL FILE NAME"),
  ILLEGAL_FILE_TYPE("2106","ILLEGAL FILE TYPE"),
  FILE_OVER_SIZE("2107","FILE OVER SIZE"),
  CORRUPTTED_FILE("2108","FILE IS BROKEN"),

  NO_FEATURE("0001", "no freature");

  
  private String code;
  private String msg;
  ResStatusEnum(String code, String msg){
    this.code = code;
    this.msg = msg;
  }
  
  public String code() {
    return code;
  }
  
  public String msg() {
    return msg;
  }
  
}
