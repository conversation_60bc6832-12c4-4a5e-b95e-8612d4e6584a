package com.sensetime.intersense.cognitivesvc.client.response.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class FacePedestrianCluster{
	
    private Integer id;

	private String facePersonId;

	private Integer facePersonType;

	private String pedestrianPersonId;

	private Integer pedestrianPersonType;
}
