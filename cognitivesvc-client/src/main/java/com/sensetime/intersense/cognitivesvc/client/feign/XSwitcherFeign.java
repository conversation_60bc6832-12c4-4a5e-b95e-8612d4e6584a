package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.CognitiveEntity.VideoStreamXswitcherInput;
import com.sensetime.intersense.cognitivesvc.client.response.entity.VideoStreamXswitcher;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(path = "/cognitive/xswitcher/", name = "${feign.cognitivesvc.name:cognitivexswitcher}", url = "${feign.cognitivesvc.url:}")
public interface XSwitcherFeign {

    @Operation(summary = "视频流X总数", method = "GET")
    @RequestMapping(value = "/getXSwitcherCount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Number> getXSwitcherCount();

    @Operation(summary = "查询视频流X配置", method = "GET")
    @RequestMapping(value = "/getXSwitcher", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<VideoStreamXswitcher>> getXSwitcher(@RequestParam(required = false) String deviceId);

    @Operation(summary = "添加视频流X配置", method = "POST")
    @RequestMapping(value = "/addOrUpdateXSwitcher", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Object> addOrUpdateXSwitcher(@RequestBody VideoStreamXswitcherInput device);

    @Operation(summary = "删除视频流X配置", method = "POST")
    @RequestMapping(value = "/deleteXSwitcher", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Object> deleteXSwitcher(@RequestParam String deviceId, @RequestParam(required = false) String annotatorName);
}
