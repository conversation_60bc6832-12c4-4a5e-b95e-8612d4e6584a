package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.FrameParamReq;
import com.sensetime.intersense.cognitivesvc.client.response.entity.VideoStatusRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.VideoStreamInfra;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

@FeignClient(path = "/cognitive/device/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface DeviceFeign{
	
	@Operation(summary = "视频流总数", method = "GET")
	@RequestMapping(value = "/getDeviceInfraCount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Long> getDeviceInfraCount();
	
	@Operation(summary = "直接视频流-分页", method = "GET")
	@RequestMapping(value = "/getDeviceInfra", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<List<VideoStreamInfra>> getDeviceInfra(@RequestParam(required = false) String deviceId);
	
	@Operation(summary = "检查视频流", method = "POST")
	@RequestMapping(value = "/ajustDeviceInfra", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<VideoStreamInfra> ajustDeviceInfra(@RequestBody String rtsp_source);
	
	@Operation(summary = "添加直接视频流", method = "POST")
	@RequestMapping(value = "/addOrUpdateDeviceInfra", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Integer> addOrUpdateDeviceInfra(@RequestBody VideoStreamInfra device);
	
	@Operation(summary = "删除直接视频流", method = "POST")
	@RequestMapping(value = "/deleteDeviceInfra", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Integer> deleteDeviceInfra(@RequestParam String deviceId);
	
	@Operation(summary = "保持存活渲染视频流keepalive", method = "POST")
	@RequestMapping(value = "/keepAliveDeviceInfra", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Integer> keepAliveDeviceInfra(@RequestParam String deviceId, @RequestParam(required = false) Date expire);
	
	@Operation(summary = "获取流的一帧", method = "POST")
	@RequestMapping(value = "/fetchFrame", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<String> fetchFrame(@RequestBody String rtsp_source);

	@Operation(summary = "获取流的一帧,需要清理", method = "POST")
	@RequestMapping(value = "/fetchFrameToClean", method = RequestMethod.POST)
	public BaseRes<String> fetchFrameToClean(@RequestBody String rtsp_source);

	@Operation(summary = "face流监控", method = "GET")
	@RequestMapping(value = "/infra/videostatus", method = RequestMethod.GET)
	public BaseRes<List<VideoStatusRes>> getVideoStatus();


	@Operation(summary = "获取video的all frame离线视频", method = "POST")
	@RequestMapping(value = "/infra/fetchVideoFrame", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<String> fetchVideoFrame(@RequestBody FrameParamReq frameParamReq);

	@Operation(summary = "获取video的all frame离线视频-sync", method = "POST")
	@RequestMapping(value = "/infra/fetchVideoFrameSync", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<String> fetchVideoFrameSync(@RequestBody FrameParamReq frameParamReq);

	@Operation(summary = "stop video的all frame离线视频", method = "POST")
	@RequestMapping(value = "/infra/stopVideoFrame", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<String> stopVideoProcessing(@RequestBody FrameParamReq frameParamReq);
}
