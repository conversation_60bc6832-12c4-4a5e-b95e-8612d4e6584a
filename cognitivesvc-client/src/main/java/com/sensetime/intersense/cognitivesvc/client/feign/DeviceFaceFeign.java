package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.VideoStreamFace;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(path = "/cognitive/device/face/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface DeviceFaceFeign {

    @Operation(summary = "视频流face总数", method = "GET")
    @RequestMapping(value = "/getDeviceFaceCount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Long> getDeviceFaceCount();

    @Operation(summary = "查询视频流face配置", method = "GET")
    @RequestMapping(value = "/getDeviceFace", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<VideoStreamFace>> getDeviceFace(@RequestParam(required = false) String deviceId);

    @Operation(summary = "添加视频流face配置", method = "POST")
    @RequestMapping(value = "/addOrUpdateDeviceFace", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Object> addOrUpdateDeviceFace(@RequestBody VideoStreamFace device);

    @Operation(summary = "删除视频流face配置", method = "POST")
    @RequestMapping(value = "/deleteDeviceFace", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Object> deleteDeviceFace(@RequestParam String deviceId);
}
