package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "直接视频流设备", description = "直接视频流设备")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamFace {

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "该设备是否走face模型")
    private Integer runFaceModel;

    @Schema(description = "该设备是否走faceAttr模型")
    private Integer runFaceAttrModel;

    @Schema(description = "基础阈值")
    private Float baseThreshold;

    @Schema(description = "基础质量阈值")
    private Float baseImageQuality;

    @Schema(description = "yaw")
    private Float yaw;

    @Schema(description = "pitch")
    private Float pitch;

    @Schema(description = "roll")
    private Float roll;

    @Schema(description = "最小脸size")
    private Integer minFaceSize;

    @Schema(description = "是否存大图")
    private Boolean storeScene;

    @Schema(description = "是否存陌生人")
    private Boolean storePasser;

    @Schema(description = "目标组")
    private String targetGroup;

    @Schema(description = "热区")
    private String roi;

    @Schema(description = "热区的外部id")
    private String roiIds;
}
