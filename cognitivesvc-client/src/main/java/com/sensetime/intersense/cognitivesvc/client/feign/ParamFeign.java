package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(path = "/cognitive/param/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface ParamFeign {
    @Operation(summary = "获取参数", method = "GET")
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public BaseRes<Map<String, String>> get();

    @Operation(summary = "设置参数", method = "POST")
    @RequestMapping(value = "/set", method = RequestMethod.POST)
    public BaseRes<Integer> set(@RequestParam String key, @RequestParam String value);
}
