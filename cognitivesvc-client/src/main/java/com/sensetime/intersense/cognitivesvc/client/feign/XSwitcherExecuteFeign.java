package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.client.response.entity.CognitiveEntity.SenseyexRawVideo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(path = "/cognitive/xswitcher/", name = "${feign.cognitivesvc.name:cognitivexswitcher}", url = "${feign.cognitivesvc.url:}")
public interface XSwitcherExecuteFeign {

    @Operation(summary = "跑模型", method = "POST")
    @RequestMapping(value = "/execute/model", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Object> executeModel(@RequestBody SenseyexRawImage message);

    @Operation(summary = "发消息", method = "POST")
    @RequestMapping(value = "/send/senseyex/raw/image/input", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public void sendSenseyexRawImage(@RequestBody SenseyexRawImage message);

    @Operation(summary = "发视频消息", method = "POST")
    @RequestMapping(value = "/send/senseyex/raw/video/input", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public void sendSenseyexRawVideo(@RequestBody SenseyexRawVideo message);
}
