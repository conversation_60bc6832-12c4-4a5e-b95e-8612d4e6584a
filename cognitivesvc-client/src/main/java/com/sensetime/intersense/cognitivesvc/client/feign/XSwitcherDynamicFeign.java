package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.XDynamicModel;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@FeignClient(path = "/cognitive/xswitcher/dynamic/", name = "${feign.cognitivesvc.name:cognitivexswitcher}", url = "${feign.cognitivesvc.url:}")
public interface XSwitcherDynamicFeign {

    @Operation(summary = "动态模型总数", method = "GET")
    @RequestMapping(value = "/getXDynamicModelCount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Long> getXDynamicModelCount();

    @Operation(summary = "查询动态模型", method = "GET")
    @RequestMapping(value = "/getXDynamicModel", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<XDynamicModel>> getXDynamicModel(@RequestParam(required = false) String annotatorName);

    @Operation(summary = "删除动态模型处理器", method = "POST")
    @RequestMapping(value = "/deleteXDynamicModel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Integer> deleteXDynamicModel(@RequestParam String annotatorName);

    @Operation(summary = "添加动态模型处理器", method = "POST")
    @RequestMapping(value = "/addXDynamicModel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Integer> addXDynamicModel(@RequestBody XDynamicModel entity);

    @Operation(summary = "更改动态模型处理器", method = "POST")
    @RequestMapping(value = "/updateXDynamicModel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Integer> updateXDynamicModel(@RequestBody XDynamicModel entity);

    @Operation(summary = "上传动态模型包", method = "POST")
    @RequestMapping(value = "/uploadXDynamicModel", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<String> uploadDynamicModel(@RequestParam("modelFile") MultipartFile modelFile);

    @Operation(summary = "编译动态代码", method = "POST")
    @RequestMapping(value = "/compile/dynamic/code", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<String> compileDynamicCode(@RequestBody Map<String, String> javaCode);

    @Operation(summary = "开关动态模型存图功能", method = "POST")
    @RequestMapping(value = "/onoffXDynamicModelImgSaveTag", method = RequestMethod.POST)
    public BaseRes<Integer> onoffXDynamicModelImgSaveTag(@RequestParam String annotatorName, @RequestParam int onoff);

}
