package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.FaceBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(path = "/cognitive/pedestrian/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface PedestrianFeign {

    @Operation(summary = "用图片和库内Id特征比对", method = "POST")
    @RequestMapping(value = "/compareFeatureByImagePathAndId", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Float> compareImagePaths(
            @Parameter(required = true, name = "figureImageUrl", description = "图片_1") @RequestParam String figureImageUrl
            , @Parameter(required = true, name = "personId", description = "personId") @RequestParam String personId
            , @Parameter(required = false, name = "type", description = "比人脸(0)还是比身体(1)") @RequestParam(required = false) Integer type
            , @Parameter(required = false, name = "personType", description = "比库内(0)还是比路人(1)") @RequestParam(required = false) Integer personType);

    @Operation(summary = "对比两张图片路径，算出评分", method = "POST")
    @RequestMapping(value = "/compareFeatureByImagePaths", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Float> compareFeatureByImagePaths(
            @Parameter(required = true, name = "figureImageUrl_1", description = "图片_1") @RequestParam String figureImageUrl_1
            , @Parameter(required = true, name = "figureImageUrl_2", description = "图片_2") @RequestParam String figureImageUrl_2
            , @Parameter(required = false, name = "type", description = "比人脸(0)还是比身体(1)") @RequestParam(required = false) Integer type);

    @Operation(summary = "从图片base64提取人脸人体特征", method = "POST")
    @RequestMapping(value = "/getPedestrianFeatureByBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<FaceBody> getPedestrianFeatureByBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64);

    @Operation(summary = "从图片路径提取人脸人体特征", method = "POST")
    @RequestMapping(value = "/getPedestrianFeatureByImagePath", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<FaceBody> getPedestrianFeatureByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl);

    @Operation(summary = "从图片base64提取人脸人体特征", method = "POST")
    @RequestMapping(value = "/getPedestrianFeatureAttributeByBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<FaceBody> getPedestrianFeatureAttributeByBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64);

    @Operation(summary = "从图片路径提取人脸人体特征", method = "POST")
    @RequestMapping(value = "/getPedestrianFeatureAttributeByImagePath", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<FaceBody> getPedestrianFeatureAttributeByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl);

    @Operation(summary = "全表重新提取特征", method = "GET")
    @RequestMapping(value = "/reExtractFully", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<String> reExtractFully();

    @Operation(summary = "检查库内特征与模型不匹配的数量", method = "GET")
    @RequestMapping(value = "/checkFeatureCorrect", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Integer> checkFeatureCorrect();
}
