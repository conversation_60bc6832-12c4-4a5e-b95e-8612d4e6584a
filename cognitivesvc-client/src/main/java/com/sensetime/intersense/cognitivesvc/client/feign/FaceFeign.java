package com.sensetime.intersense.cognitivesvc.client.feign;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.MediaType;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;

@FeignClient(path = "/cognitive/face/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface FaceFeign {
	
	@Operation(summary = "从人脸图片路径提取脸角度", method = "POST")
	@RequestMapping(value = "/getHeadposeByImage", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Map<String, Object>> getHeadposeByImage(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception ;
	
	@Operation(summary = "从人脸图片base64提取脸角度", method = "POST")
	@RequestMapping(value = "/getHeadposeByBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Map<String, Object>> getHeadposeByBase64(@RequestBody MultiValueMap<String, Object> queryString) throws Exception ;
	
	@Operation(summary = "从人脸图片路径提取特征和属性", method = "POST")
	@RequestMapping(value = "/getFeatureAndAttributeByImagePath", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Map<String, Object>> getFeatureAndAttributeByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception ;
	
	@Operation(summary = "从人脸图片base64提取特征和属性", method = "POST")
	@RequestMapping(value = "/getFeatureAndAttributeByImageBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Map<String, Object>> getFeatureAndAttributeByImageBase64(@RequestBody MultiValueMap<String, Object> queryString) throws Exception ;
	
	@Operation(summary = "从人脸图片路径提取大小框", method = "POST")
	@RequestMapping(value = "/getDetectByImagePath", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Object> getDetectByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception ;
	
	@Operation(summary = "从人脸图片base64提取大小框", method = "POST")
	@RequestMapping(value = "/getDetectByBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Object> getDetectByBase64(@RequestBody MultiValueMap<String, Object> queryString) throws Exception ;

	@Operation(summary = "从人脸图片路径提取特征", method = "POST")
	@RequestMapping(value = "/getFeatureByImagePath", method = RequestMethod.POST)
	public  BaseRes<Map<String, Object>>  getFeatureByImagePath(
			@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) throws Exception ;
	
//	@Operation(summary = "从人脸图片base64提取特征", method = "POST")
//	@RequestMapping(value = "/getFeatureByImageBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
//	public BaseRes<String> getFeatureByImageBase64(@RequestBody MultiValueMap<String, Object> queryString) throws Exception ;
	
	@Operation(summary = "从人脸图片base64检验图片质量评分", method = "POST")
	@RequestMapping(value = "/getImageQualityByImageBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Float> getImageQualityByImageBase64(@RequestBody MultiValueMap<String, Object> queryString) throws Exception ;

	@Operation(summary = "从人脸图片路径检验图片质量评分", method = "GET")
	@RequestMapping(value = "/getImageQualityByImage", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Float> getImageQualityByImage(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl) ;
	
	@Operation(summary = "对比两个特征值，算出评分", method = "POST")
	@RequestMapping(value = "/compareFeatures", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Float> compareFeatures(@RequestBody MultiValueMap<String, Object> queryString) throws Exception ;
	
	@Operation(summary = "对比两张图片base64，算出评分", method = "POST")
	@RequestMapping(value = "/compareImageBase64s", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Float> compareImageBase64s(@RequestBody MultiValueMap<String, Object> queryString) throws Exception ;
	
	@Operation(summary = "对比两张图片路径，算出评分", method = "POST")
	@RequestMapping(value = "/compareImagePaths", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Float> compareImagePaths(@Parameter(required = true, name = "figureImageUrl_1", description = "图片_1") @RequestParam String figureImageUrl_1
			, @Parameter(required = true, name = "figureImageUrl_2", description = "图片_2") @RequestParam String figureImageUrl_2) throws Exception ;
	
	@Operation(summary = "重现扫描表。并提取特征", method = "GET")
	@RequestMapping(value = "/reExtractFully", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String reExtractFully();
	
	@Operation(summary = "重新提取personId的特征", method = "GET")
	@RequestMapping(value = "/reExtractByPersonId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String reExtractByPersonId(@Parameter(required = true, name = "personId", description = "personId") @RequestParam String personId);
}