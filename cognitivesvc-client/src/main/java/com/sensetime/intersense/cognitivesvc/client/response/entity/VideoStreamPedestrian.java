package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "直接视频流设备", description = "直接视频流设备")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamPedestrian {

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "人脸人体追踪质量阈值")
    private Float qualityThreshold;

    @Schema(description = "人脸特征比对阈值")
    private Float faceFeatureThreshold;

    @Schema(description = "人体特征比对阈值")
    private Float bodyFeatureThreshold;

    @Schema(description = "搜索数量")
    private Integer seekNum;

    @Schema(description = "是否全追踪特征融合")
    private Boolean refinement;

    @Schema(description = "yaw")
    private Float yaw;

    @Schema(description = "pitch")
    private Float pitch;

    @Schema(description = "roll")
    private Float roll;

    @Schema(description = "最小人脸size")
    private Integer minFaceSize;

    @Schema(description = "最小人体size")
    private Integer minBodySize;

    @Schema(description = "是否存大图")
    private Boolean storeScene;

    @Schema(description = "是否存陌生人")
    private Boolean storePasser;

    @Schema(description = "目标组")
    private String targetGroup;

    @Schema(description = "热区")
    private String roi;

    @Schema(description = "热区的外部id")
    private String roiIds;

    @Schema(description = "快速人脸识别响应时间")
    private Integer quickResponseTime;

    @Schema(description = "选帧触发的时间间隔")
    private Integer timeInterval;

    @Schema(description = "目标最大的跟踪时长")
    private Integer maxTrackTime;
}
