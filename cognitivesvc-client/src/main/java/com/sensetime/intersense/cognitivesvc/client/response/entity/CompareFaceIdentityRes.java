package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompareFaceIdentityRes {
    @Schema(description = "人员ID", name = "personId")
    private String personID;

    @Schema(description = "相似指数", name = "score")
    private Double score;

    @Schema(description = "人员类型", name = "targetType")
    private String targetType;
}
