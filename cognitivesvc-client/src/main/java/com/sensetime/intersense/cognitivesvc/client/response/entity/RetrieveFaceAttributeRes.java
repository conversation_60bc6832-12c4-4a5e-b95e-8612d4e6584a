package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RetrieveFaceAttributeRes {
    @Schema(description = "脸部属性", name = "attributeListStr")
    private String attributeListStr;
}
