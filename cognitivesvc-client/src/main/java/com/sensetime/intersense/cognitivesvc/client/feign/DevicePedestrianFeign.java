package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.VideoStreamPedestrian;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(path = "/cognitive/device/pedestrian/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface DevicePedestrianFeign {

    @Operation(summary = "视频流pedestrian总数", method = "GET")
    @RequestMapping(value = "/getDevicePedestrianCount", method = RequestMethod.GET)
    public BaseRes<Long> getDevicePedestrianCount();

    @Operation(summary = "查询视频流pedestrian配置", method = "GET")
    @RequestMapping(value = "/getDevicePedestrian", method = RequestMethod.GET)
    public BaseRes<List<VideoStreamPedestrian>> getDevicePedestrian(@RequestParam(required = false) String deviceId);

    @Operation(summary = "添加视频流pedestrian配置", method = "POST")
    @RequestMapping(value = "/addOrUpdateDevicePedestrian", method = RequestMethod.POST)
    public BaseRes<Object> addOrUpdateDevicePedestrian(@RequestBody VideoStreamPedestrian device) throws Exception;

    @Operation(summary = "删除视频流pedestrian配置", method = "POST")
    @RequestMapping(value = "/deleteDevicePedestrian", method = RequestMethod.POST)
    public BaseRes<Object> deleteDevicePedestrian(@RequestParam String deviceId);
}
