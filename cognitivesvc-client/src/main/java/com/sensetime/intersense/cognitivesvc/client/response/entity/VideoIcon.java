package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2018-07-07
 */
@Data
@Accessors(chain = true)
@Schema(title = "视频渲染配置", description = "视频渲染配置")
public class VideoIcon {

    @Schema(description = "人员tag")
    private String person_tag;
    @Schema(description = "rgb色")
    private String rgb;
    @Schema(description = "文字(英文)")
    private String text;
    @Schema(description = "图标base64")
    private String icon_image_base64;
}
