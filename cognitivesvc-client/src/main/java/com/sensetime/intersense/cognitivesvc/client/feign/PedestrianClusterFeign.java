package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.FacePedestrianCluster;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(path = "/cognitive/pedestrian/cluster/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface PedestrianClusterFeign {

    @Operation(summary = "人脸ID与人体ID绑定查询", method = "GET")
    @RequestMapping(value = "/find", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<FacePedestrianCluster>> find(
            @Parameter(required = false, name = "facePersonId", description = "facePersonId") @RequestParam(required = false) String facePersonId
            , @Parameter(required = false, name = "bodyPersonId", description = "bodyPersonId") @RequestParam(required = false) String bodyPersonId);

    @Operation(summary = "人脸ID与人体ID绑定设置", method = "POST")
    @RequestMapping(value = "/set", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<String> set(
            @Parameter(name = "facePersonId", description = "facePersonId") @RequestParam String facePersonId
            , @Parameter(name = "bodyPersonId", description = "bodyPersonId") @RequestParam String bodyPersonId);
}
