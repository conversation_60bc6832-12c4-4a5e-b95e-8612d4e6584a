package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RetrieveFaceFeatureRes {
    @Schema(description = "脸部特征", name = "feature")
    private String feature;

    @Schema(description = "模组版号", name = "modelVersion")
    private String modelVersion;
}
