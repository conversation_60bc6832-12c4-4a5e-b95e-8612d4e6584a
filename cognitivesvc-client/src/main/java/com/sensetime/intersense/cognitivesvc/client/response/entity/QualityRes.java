package com.sensetime.intersense.cognitivesvc.client.response.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QualityRes {

    private float score;

    private Rect detect;

    private float id;


    private double quality;

    private double confidence;

    private double alignerConfidence;


    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @Builder
    @AllArgsConstructor
    public static class Rect{
        private int left;
        private int top;
        private int width;
        private int height;
    }
}
