package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;
import com.sensetime.intersense.cognitivesvc.client.response.entity.CognitiveEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(path = "/cognitive/pedestrian/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
public interface PedestrianMidfaceFeign {

    @SuppressWarnings("rawtypes")
    @Operation(summary = "从人体图片提取特征", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/retrievePedestrianFeature", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Map> retrieveFaceFeature(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = true, name = "personId", description = "人员UUID") @RequestParam String personId,
            @Parameter(required = true, name = "personCnName", description = "人员中文名争") @RequestParam String personCnName,
            @Parameter(required = false, name = "personEnName", description = "人员英文名争") @RequestParam(value = "personEnName", required = false) String personEnName,
            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag);

    @Operation(summary = "以图搜人，直接用图片base64", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/comparePedestrianBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Object> comparePedestrianBase64(
            @Parameter(required = true, name = "figureImageBase64", description = "图片") @RequestParam("figureImageBase64") String figureImageBase64,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold);

    @Operation(summary = "以图搜人", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/comparePedestrianIdentity", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Object> comparePedestrianIdentity(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag,逗号分隔") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold);

    @Operation(summary = "批量提特 - 支持人脸人体", method = "POST")
    @RequestMapping(value = "/retrieveBatchFeature", method = RequestMethod.POST)
    public BaseRes<Map<Integer, List<Map<String, Object>>>> retrieveBatchFeature(@RequestBody CognitiveEntity.SenseyexRawFeature message,
                                                                                 @RequestParam(required = false, defaultValue = "false") Boolean ignoreLimit);

}
