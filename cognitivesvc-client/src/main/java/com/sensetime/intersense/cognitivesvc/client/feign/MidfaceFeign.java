package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

@FeignClient(path = "/cognitive/face/", name = "${feign.cognitivesvc.name:cognitivesvc}", url = "${feign.cognitivesvc.url:}")
@SuppressWarnings({"rawtypes"})
public interface MidfaceFeign {

    @Operation(summary = "保留多少天的数据", method = "GET")
    @RequestMapping(value = "/deleteParsserByDays", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes deleteParsserByDays(@Parameter(required = true, name = "days", description = "days") @RequestParam int days);

    @Operation(summary = "保留多少天的数据", method = "POST")
    @RequestMapping(value = "/deleteParsserByDays", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes deleteParsserByDaysPost(@Parameter(required = true, name = "days", description = "days") @RequestParam int days);

    @Operation(summary = "删除人员特征值", method = "GET")
    @RequestMapping(value = "deleteByPId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes deleteByPId(@Parameter(required = false, name = "pids", description = "pid") @RequestParam(required = false) List<String> pids
            , @Parameter(required = false, name = "startTime", description = "startTime") @RequestParam(required = false) Date startTime
            , @Parameter(required = false, name = "endTime", description = "endTime") @RequestParam(required = false) Date endTime);

    @Operation(summary = "获取人员特征值", method = "POST")
    @RequestMapping(value = "retrieveAllPersonByIds", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes retrieveAllPersonByIds(@Parameter(required = false, name = "pids", description = "pid") @RequestParam(required = false) List<String> pids);


    @Operation(summary = "删除人员特征值", method = "POST")
    @RequestMapping(value = "deleteByPId", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes deleteByPIdPost(@Parameter(required = false, name = "pids", description = "pid") @RequestParam(required = false) List<String> pids
            , @Parameter(required = false, name = "startTime", description = "startTime") @RequestParam(required = false) String startTime
            , @Parameter(required = false, name = "endTime", description = "endTime") @RequestParam(required = false) String endTime);

    @Operation(summary = "人组关系数据增量同步到cog应用缓存", method = "POST")
    @RequestMapping(value = "/updateMemberIdCache", method = RequestMethod.POST)
    public BaseRes<Object> updateMemberIdCache(
            @Parameter(examples = @ExampleObject(value = " {\"groupId\": \"123\", \"personIds\": [\"1\",\"2\",\"3\",\"4\"],\"opsType\": \"add or del\"}"
            )
            )
            @RequestBody Map<String, Object> parameters,
            @RequestParam(required = false) Boolean isBroadcast);

    @Operation(summary = "检验人脸图片质量", method = "GET")
    @RequestMapping(value = "verifyImageQuality", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<VerifyImageQualityRes> verifyImageQuality(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl);
    @Operation(summary = "检验人脸图片质量-多人脸", method = "GET")
    @RequestMapping(value = "/verifyImageMultiFaceQuality", method = RequestMethod.GET)
    public BaseRes<List<QualityRes>> verifyImageMultiFaceQuality(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl);

    @Operation(summary = "从人脸图片提取特征", method = "GET")
    @RequestMapping(value = "retrieveFaceFeature", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<Map> retrieveFaceFeature(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
                                            @Parameter(required = true, name = "personId", description = "人员UUID") @RequestParam String personId,
                                            @Parameter(required = true, name = "personCnName", description = "人员中文名争") @RequestParam String personCnName,
                                            @Parameter(required = false, name = "personEnName", description = "人员英文名争") @RequestParam(value = "personEnName", required = false) String personEnName,
                                            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag,
                                            @Parameter(required = false, name = "needFeature", description = "是否返回特征字符串") @RequestParam(value = "needFeature", required = false) Boolean needFeature);

    @Operation(summary = "从人脸图片提取特征", method = "GET")
    @RequestMapping(value = "getFeatureByImage", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<RetrieveFaceFeatureRes> retrieveFaceFeature(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl);

    @Operation(summary = "提取人脸属性", method = "GET")
    @RequestMapping(value = "retrieveFaceAttribute", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<RetrieveFaceAttributeRes> retrieveFaceAttribute(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl);

    @RequestMapping(value = "retrieveAllPersonIds", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<String>> retrieveAllPersonIds();

    @RequestMapping(value = "retrieveAllPersonIdsUuid", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<String>> retrieveAllPersonIdsUuid();

    @Operation(summary = "以图搜人", method = "GET")
    @RequestMapping(value = "compareFaceIdentity", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<CompareFaceIdentityRes>> compareFaceIdentity(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold);

    @Operation(summary = "多人员组中以图搜人，直接用图片base64", method = "POST")
    @RequestMapping(value = "/compareFaceByMultiGroupByBase64", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<CompareFaceIdentityRes>> compareFaceByMultiGroupByBase64(@RequestBody MultiValueMap<String, Object> queryString);

    @Operation(summary = "从人脸图片base64提取特征", method = "POST")
    @RequestMapping(value = "/getFeatureByImageBase64", method = RequestMethod.POST)
    public BaseRes<Map<String, Object>> getFeatureByImageBase64(@RequestParam("figureImageBase64") String figureImageBase64);

    @Operation(summary = "多人员组中以图搜人", method = "GET")
    @RequestMapping(value = "/compareFaceByMultiGroup", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseRes<List<CompareFaceIdentityRes>> compareFaceByMultiGroup(
            @Parameter(name = "figureImageUrl", description = "图片路径") @RequestParam(value = "figureImageUrl") String figureImageUrl,
            @Parameter(name = "groups", description = "用户群组") @RequestParam(value = "groups") List<String> groups,
            @Parameter(name = "thresholds", description = "得分阈值") @RequestParam(value = "thresholds") List<Float> thresholds,
            @Parameter(name = "count", description = "最多搜寻数量") @RequestParam(value = "count") int count
    );


    @Operation(summary = "批量提特 - 支持人脸人体", method = "POST")
    @RequestMapping(value = "/retrieveBatchFeature", method = RequestMethod.POST)
    public BaseRes retrieveBatchFeature(@RequestBody CognitiveEntity.SenseyexRawFeature message,
                                        @RequestParam(required = false, defaultValue = "false") Boolean ignoreLimit
    );
}
