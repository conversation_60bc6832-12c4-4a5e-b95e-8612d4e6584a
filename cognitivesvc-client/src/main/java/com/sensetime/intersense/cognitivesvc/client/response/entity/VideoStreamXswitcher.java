package com.sensetime.intersense.cognitivesvc.client.response.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2018-07-07
 */
@Data
@Accessors(chain = true)
@Schema(title = "X配置", description = "X配置")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamXswitcher {
    @Schema(description = "id")
    private Integer id;

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "流类型, 0低频, 1高频")
    private Integer type;

    @Schema(description = "跑哪些模型")
    private String processors;

    @Schema(description = "cogx高频流分配信息")
    private String dispatchDesc;
}
