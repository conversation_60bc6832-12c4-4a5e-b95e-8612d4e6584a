package com.sensetime.intersense.cognitivesvc.client.response.entity;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

public class CognitiveEntity {
	
	@Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class SenseyexRawImage {
        private String              deviceId;
        private String[]            images;
        private String[]            processors;//可以为null 就是全部都跑
        private Long                capturedTime;
    	private Map<String, Object> extra;
    }
	
	@Data
	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static final class VideoStreamXswitcherInput{
		private Integer id;
		private String  deviceId;
		private Integer type;
		private List<Map<String, Object>>  processors;
	}
	
	@Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class SenseyexRawVideo {
        private String              deviceId;
        private String            	video;
        private String[]            processors;//可以为null 就是全部都跑
        private Long                capturedTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class SenseyexRawFeature {

        private Integer[]           processors;//可以为null 就是全部都跑
        private String[]            images;
        private String[]            bodyImages;
        private Boolean             isEncoder;

    }
}
