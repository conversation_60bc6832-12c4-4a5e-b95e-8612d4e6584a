package com.sensetime.intersense.cognitivesvc.client.response.entity;

import lombok.Data;

@Data
@SuppressWarnings("rawtypes")
public class BaseRes<T> {
  private String code;
  private String msg;
  private T data;
  
  public BaseRes(){}
  
  
  public BaseRes(String code, String msg, T data) {
    this.code = code;
    this.msg = msg;
    this.data = data;
  }
  
  
public static BaseRes error(ResStatusEnum resEnum){
    BaseRes baseRes = new BaseRes();
    baseRes.setCode(resEnum.code().toString());
    baseRes.setMsg(resEnum.msg());
    return baseRes;
  }
  @SuppressWarnings("unchecked")
public  BaseRes success(T data){
    BaseRes baseRes = new BaseRes();
    baseRes.setCode(ResStatusEnum.SUCCESS.code().toString());
    baseRes.setMsg(ResStatusEnum.SUCCESS.msg());
    baseRes.setData(data);
    return baseRes;
  }
  
}

