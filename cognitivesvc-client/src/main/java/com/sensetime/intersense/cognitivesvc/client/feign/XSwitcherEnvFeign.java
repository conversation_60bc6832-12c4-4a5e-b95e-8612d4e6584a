package com.sensetime.intersense.cognitivesvc.client.feign;

import com.sensetime.intersense.cognitivesvc.client.response.entity.VideoStatusRes;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.sensetime.intersense.cognitivesvc.client.response.entity.BaseRes;

@FeignClient(path="/cognitive/xswitcher/env/", name = "${feign.cognitivesvc.name:cognitivexswitcher}", url = "${feign.cognitivesvc.url:}")
public interface XSwitcherEnvFeign{
	
	@Operation(summary = "获取当前环境拥有的能力", method = "GET")
	@RequestMapping(value = "/available/models", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Set<String>> availableModels() ;
	
	@Operation(summary = "获取当前环境状况", method = "GET")
	@RequestMapping(value = "/instances/info", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public BaseRes<Map<String, Object>> instancesInfo() ;

	@Operation(summary = "获取当前环境流状况", method = "GET")
	@RequestMapping(value = "/instances/videostatus", method = RequestMethod.GET)
	public BaseRes<List<VideoStatusRes>> videostatus();
}
