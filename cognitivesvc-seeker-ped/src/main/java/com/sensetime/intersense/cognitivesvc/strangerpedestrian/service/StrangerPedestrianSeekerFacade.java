package com.sensetime.intersense.cognitivesvc.strangerpedestrian.service;

import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.sensetime.intersense.cognitivesvc.strangerpedestrian.entity.StrangerPedestrianObject;

public class StrangerPedestrianSeekerFacade{
	
	@Autowired
	private Map<String, AbstractPedestrianStrangerSeeker> seekers;
	
	@Value("${spring.application.name}")
	private String name;
	
	public String strangerIsHere(StrangerPedestrianObject obj) {
		return seekers.get("justPedestrianStranger").strangerIsHere(obj);
	};
}
