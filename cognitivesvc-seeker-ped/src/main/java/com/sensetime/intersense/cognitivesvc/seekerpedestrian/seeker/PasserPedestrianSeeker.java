package com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PasserBodyObject;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.service.utils.SeekerPedestrianInitializer;
import com.sensetime.intersense.cognitivesvc.server.entities.PasserPedestrianFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SeekParam;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserPedestrianFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;

@EnableScheduling
@Component
public class PasserPedestrianSeeker extends FaissSeeker<PasserPedestrianFeature, PasserBodyObject>{
	
	@Autowired
	private PasserPedestrianFeatureRepository mapper;

	@Override
	protected Stream<Pair<PasserBodyObject, Float>> find(Stream<Pair<PasserBodyObject, Float>> baseStream, SeekParam param) {
		PasserParam passerParam = ((PasserParam)param);
		
		String groupId = passerParam.groupId;
		
		boolean noCheckPrivilege = ArrayUtils.isEmpty(passerParam.deptIds) || ArrayUtils.contains(passerParam.deptIds, "0") || ArrayUtils.contains(passerParam.deptIds, "*");
		
		return baseStream.filter(pair -> {
			if(StringUtils.isNotBlank(groupId) && !StringUtils.equals(groupId, pair.getLeft().groupId))
				return false;
			
			if(!noCheckPrivilege) {
				String[] pairPrivilege = pair.getLeft().privilege();
				return Arrays.stream(passerParam.deptIds).filter(deptId -> ArrayUtils.contains(pairPrivilege, deptId)).findAny().isPresent();	
			}
			
			return true;
		});
	}

	@Override
	protected PasserBodyObject convert(PasserPedestrianFeature sp) {
		try {
			PasserBodyObject obj = new PasserBodyObject();
			obj.id        = sp.getId();
			obj.pid       = sp.getPersonUuid();
			obj.groupId   = sp.getGroupId();
			obj.avatar    = sp.getAvatarImageUrl();
			obj.privilege = sp.getPrivilege();
			obj.feature   = stringToFeature(sp.getImageFeature());
			return obj;
		}catch(Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	@Override
	protected int dims() { return SeekerPedestrianInitializer.dims; }
	
	@Override
	protected float normalize_feature_score(float score) { return normalize_feature_score(score, SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint); }
	
	@Override
	protected boolean gpuFaiss() { return Faiss.faissType == FaissType.GPU; }

	@Override
	protected Integer queryMaxId() { return mapper.queryMaxId(); }

	@Override
	protected List<PasserPedestrianFeature> querySplit(int start, int end, int totalSplitNum, int currentSplitNum) { return mapper.querySplit(start, end, totalSplitNum, currentSplitNum); }

	@Override
	protected long countSplit(int totalSplitNum, int currentSplitNum) { return mapper.countSplit(totalSplitNum, currentSplitNum); }
}
