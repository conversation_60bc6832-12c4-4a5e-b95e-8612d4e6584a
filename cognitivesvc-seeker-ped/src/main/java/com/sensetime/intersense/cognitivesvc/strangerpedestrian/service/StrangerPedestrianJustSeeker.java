package com.sensetime.intersense.cognitivesvc.strangerpedestrian.service;

import java.io.File;
import java.io.IOException;
import java.util.*;

import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.storage.entity.ImageInfo;
import com.sensetime.storage.service.FileAccessor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;

import com.sensetime.intersense.cognitivesvc.server.entities.PasserPedestrianFeature;
import com.sensetime.intersense.cognitivesvc.server.feign.StrangerFeign;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserPedestrianFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerpedestrian.entity.StrangerPedestrianObject;
import com.sensetime.lib.clientlib.response.BaseRes;

import lombok.extern.slf4j.Slf4j;

/**发现了陌生人 就立即入库。。。。。
 */
@Slf4j
public class StrangerPedestrianJustSeeker extends AbstractPedestrianStrangerSeeker{
	
	@Autowired
	private PasserPedestrianFeatureRepository passerMapper;
	
    @Autowired
	private ApplicationContext context;

	@Autowired
	FileAccessor fileAccessor;

	@Value("${preMakeDirs}")
	private String preMakeDirs;
	
	@Override
	public String strangerIsHere(StrangerPedestrianObject obj) {

		String finalPath = obj.imagePath;
		// copy
		if(!obj.imagePath.equals(FrameUtils.NOIMAGE)){
			if(!obj.imagePath.contains("passer_ped")){
				try {
//					byte[] data = fileAccessor.readImage(obj.imagePath);
//					ImageInfo imageInfo = ImageInfo
//							.builder()
//							.imagePath("passer_ped")
//							.data(data)
//							.build();
//
//					finalPath = fileAccessor.writeImage(imageInfo);
					finalPath = fileAccessor.cpImage(obj.imagePath,"passer_ped");
				} catch (Exception e) {
					e.printStackTrace();
				}

			}
		}

//		File imagePath = new File(obj.imagePath);
//		File renameFile = imagePath;
//
//		if(!obj.imagePath.equals(FrameUtils.NOIMAGE)){
//			List<String> preMakeDirList = Arrays.asList(preMakeDirs.split(","));
//			 renameFile = ImageUtils.newFileWithMkdir("passer_ped");
//			try {
//				FileUtils.copyFile(imagePath, renameFile);
//			} catch (IOException e) {
//				e.printStackTrace();
//			}
//		}


		StrangerFeign.CreateStrangerReq req = new StrangerFeign.CreateStrangerReq();
		req.setImageURI(finalPath);
		req.setDesc(toDesc(List.of(new MutablePair<StrangerPedestrianObject, Float>(obj, 1.0f))));
		req.setPersonType("passer");
		req.setPersonTag(obj.getInfra().getDeviceTag());
		req.setPersonAge(obj.age);
		req.setPersonSex(obj.sex);
		req.setDeviceSource(obj.getInfra().getDeviceId());
		
		String trackId = obj.getTrackId() + "_" + Utils.keyToContextId(obj.getInfra().getDeviceId()) + "_" + Utils.instance.getSeed().substring(Utils.instance.getSeed().length() - 4, Utils.instance.getSeed().length());
		req.setRemark("{\"label\":221488, \"trackId\":\"" + trackId + "\", \"privilege\":\"" + obj.getInfra().getPrivilege() + "\"}");	
		
		PasserPedestrianFeature item = new PasserPedestrianFeature();
		item.setAvatarImageUrl(finalPath);
		item.setImageFeature(FaissSeeker.featureToString(obj.feature));
		item.setGroupId(Objects.requireNonNullElse(obj.getInfra().getDeviceTag(), ""));
		item.setSts(0);
		item.setTrackId(trackId);
		item.setCreateTs(new Date());
		item.setPrivilege(obj.getInfra().getPrivilege());
		
		try {
			BaseRes<StrangerFeign.CreateRes> res = context.getBean(StrangerFeign.class).createStranger(req);
			if(res.isSuccess() && res.getData() != null) {
				item.setPersonUuid(res.getData().getUuid().toString());
				item.setPrivilege(Objects.requireNonNullElse(res.getData().getPrivilege(), item.getPrivilege()));
			}
		}catch(Exception e) {
			log.warn(e.getLocalizedMessage());
		}
		
		if(StringUtils.isEmpty(item.getPersonUuid())) {
			item.setPersonUuid("unknown-" + UUID.randomUUID().toString().substring(1, 6));
			item.setPrivilege(item.getPrivilege());
		}
		
		passerMapper.saveAndFlush(item);
		
		return item.getPersonUuid();
	}
}
