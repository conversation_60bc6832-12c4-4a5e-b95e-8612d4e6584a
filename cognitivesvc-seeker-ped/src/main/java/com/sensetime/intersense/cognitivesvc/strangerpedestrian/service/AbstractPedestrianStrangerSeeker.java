package com.sensetime.intersense.cognitivesvc.strangerpedestrian.service;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.Pair;

import com.sensetime.intersense.cognitivesvc.server.entities.StrangerFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerpedestrian.entity.StrangerPedestrianObject;

public abstract class AbstractPedestrianStrangerSeeker{
	
	/**
	 * 来了一个陌生人。。。
	 */
	public abstract String strangerIsHere(StrangerPedestrianObject obj);
	
	public static String toDesc(List<Pair<StrangerPedestrianObject,Float>> newMatches) {
		return newMatches.stream().sorted((l, r) -> r.getLeft().createTs.compareTo(l.getLeft().createTs))
				.map(item -> {
					StrangerPedestrianObject obj = item.getLeft();
					StringBuilder sb = new StringBuilder();
					sb.append("{\"image\":\"");
					sb.append(obj.imagePath);
					sb.append("\",\"deviceId\":\"");
					sb.append(obj.getInfra().getDeviceId());
					sb.append("\",\"captureTime\":\"");
					sb.append(Utils.dateFormat.get().format(obj.createTs));
					sb.append("\"}");
					return sb.toString();
				}).collect(Collectors.joining(",", "[", "]"));
	}
	
	public static String toDescNoPair(List<StrangerPedestrianObject> newMatches){
		return newMatches.stream().sorted((l, r) -> r.createTs.compareTo(l.createTs))
				.map(item -> {
					StringBuilder sb = new StringBuilder();
					sb.append("{\"image\":\"");
					sb.append(item.imagePath);
					sb.append("\",\"deviceId\":\"");
					sb.append(item.getInfra().getDeviceId());
					sb.append("\",\"captureTime\":\"");
					sb.append(Utils.dateFormat.get().format(item.createTs));
					sb.append("\"}");
					return sb.toString();
				})
				.collect(Collectors.joining(",", "[", "]"));
	}
	
    public static StrangerFaceFeature builtStrangerFaceFeature(StrangerPedestrianObject object) {
    	StrangerFaceFeature feature = new StrangerFaceFeature();
    	feature.avatarImageUrl = object.imagePath;
    	feature.imageFeature = FaissSeeker.featureToString(object.feature);
    	feature.createTs = object.createTs;
    	feature.groupKey = object.getInfra().getDeviceTag();
    	feature.deviceId = object.getInfra().getDeviceId();
    	feature.attribute = object.age + "," + object.sex;
    	
    	return feature;
    }
}
