package com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.sensetime.intersense.cognitivesvc.server.entities.PasserFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.DP;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.SeekerSpliter;
import com.sensetime.intersense.cognitivesvc.server.utils.SeekerSpliter.SplitEntity;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** 选择性不在本地比对特征 远程的比对服务进行比对 如果没有配置则在本地对比 认为远程的服务拥有所有人员的特征 单节点就能查询到所有*/
@EnableScheduling
@Component
public class SeekerPedestrianFacade {
	@Value("${spring.application.name}")
	private String appName;
	
	@Autowired
	private SeekerSpliter seekerUtils;
	
	@Autowired
	@LoadBalanced
	private RestTemplate restTemplate;
	
	@Autowired
	private Broadcaster broadcastService;
	
	@Autowired
	private PersonPedestrianSeeker personPedestrianSeeker;
	
	@Autowired
	private PasserPedestrianSeeker passerPedestrianSeeker;
	
	public List<Pair<PersonBodyObject, Float>> findPerson(PersonParam param){		
		SplitEntity splitEntity = seekerUtils.getSplitEntity();
		if(splitEntity.getTotalSplitNum() <= 0)
			return findPersonLocal(param);
		
		SeekResponse finalResponse = mergeSplitResult(broadcastService.postForObject(seekerUtils.getInstanceTargets(), "/cognitive/hidden/pedestrian/local", new SeekRequest(param), SeekResponse.class));
		
		List<Pair<PersonBodyObject, Float>> result = Stream.iterate(0, i -> i + 1)
			     	 .limit(finalResponse.personObjs.size())
			     	 .map(index -> new MutablePair<PersonBodyObject, Float>(finalResponse.personObjs.get(index), finalResponse.personScores.get(index)))
			     	 .collect(Collectors.toList());
		
		if(splitEntity.getTotalSplitNum() > 0)
			Collections.sort(result, (l, r) -> Float.compare(r.getRight(), l.getRight()));
		
		if(param.count == null)
			param.count = 1;
		
		if(result.size() > param.count)
			return result.subList(0, param.count);
		else 
			return result;
	}
	
	public List<Pair<PasserBodyObject, Float>> findPasser(PasserParam param){
		SplitEntity splitEntity = seekerUtils.getSplitEntity();
		if(splitEntity.getTotalSplitNum() <= 0)
			return findPasserLocal(param);

		SeekResponse finalResponse = mergeSplitResult(broadcastService.postForObject(seekerUtils.getInstanceTargets(), "/cognitive/hidden/pedestrian/local", new SeekRequest(param), SeekResponse.class));
		
		List<Pair<PasserBodyObject, Float>> result = Stream.iterate(0, i -> i + 1)
				     .limit(finalResponse.passerObjs.size())
				     .map(index -> new MutablePair<PasserBodyObject, Float>(finalResponse.passerObjs.get(index), finalResponse.passerScores.get(index)))
				     .collect(Collectors.toList());
		
		if(splitEntity.getTotalSplitNum() > 0)
			Collections.sort(result, (l, r) -> Float.compare(r.getRight(), l.getRight()));

		if(param.count == null)
			param.count = 1;
		
		if(result.size() > param.count)
			return result.subList(0, param.count);
		else 
			return result;
	}

	public void reFetchData() {
		personPedestrianSeeker.reFetchData();
		passerPedestrianSeeker.reFetchData();
	}
	
	public List<Pair<PersonBodyObject, Float>> findPersonLocal(PersonParam param){
		int count = Objects.requireNonNullElse(param.count, 1);
		
		if(ArrayUtils.isNotEmpty(param.personGroups) || ArrayUtils.isNotEmpty(param.tags) || ArrayUtils.isNotEmpty(param.deptIds))
			param.count = Math.max(param.count, 32);
		
		return personPedestrianSeeker.find(param).stream().limit(count).collect(Collectors.toList());
	}
	
	public List<Pair<PasserBodyObject, Float>> findPasserLocal(PasserParam param){

		int count = Objects.requireNonNullElse(param.count, 1);

		if (ArrayUtils.isNotEmpty(param.deptIds))
			param.count = Math.max(param.count, 32);

		return passerPedestrianSeeker.find(param).stream().limit(count).collect(Collectors.toList());
	}

	public void deletePersons(List<Integer> ids, boolean refetch) {
		personPedestrianSeeker.deleteTargets(ids, refetch);
	}

	public void deletePassers(List<Integer> ids, boolean refetch) {
		passerPedestrianSeeker.deleteTargets(ids, refetch);
	}
	
	private static SeekResponse mergeSplitResult(List<SeekResponse> result) {
		SeekResponse response = new SeekResponse();
		
		for(SeekResponse item : result) {
			response.personScores.addAll(item.personScores);
			response.personObjs.addAll(item.personObjs);
			response.passerScores.addAll(item.passerScores);
			response.passerObjs.addAll(item.passerObjs);
		}
		
		return response;
	}
	
	@Schema(title = "用特征搜索库", description = "用特征搜索库")
	public static class SeekRequest{
		@Schema(description = "搜索人员库")
		public PersonParam personParam;

		@Schema(description = "搜索归档库")
		public PasserParam passerParam;

		public SeekRequest() {}
		
		public SeekRequest(PersonParam personParam, PasserParam passerParam) {
			this.personParam = personParam;
			this.passerParam = passerParam;
		}

		public SeekRequest(PersonParam personParam) {
			this.personParam = personParam;
		}

		public SeekRequest(PasserParam passerParam) {
			this.passerParam = passerParam;
		}
	}
	
	@Schema(title = "搜索结果List", description = "搜索结果List")
	public static class SeekResponse{
		@Schema(description = "person列表")
		public List<PersonBodyObject> personObjs = new ArrayList<>();
		@Schema(description = "person的比对分")
		public List<Float> personScores = new ArrayList<>();
		
		@Schema(description = "passer列表")
		public List<PasserBodyObject> passerObjs = new ArrayList<>();
		@Schema(description = "passer的比对分")
		public List<Float> passerScores = new ArrayList<>();
		
		public void addPerson(PersonBodyObject obj, float score) {
			PersonBodyObject clone = obj.clone();
			clone.feature = null;
			
			personObjs.add(clone);
			personScores.add(score);
		}
		
		public void addPasser(PasserBodyObject obj, float score) {
			PasserBodyObject clone = obj.clone();
			clone.feature = null;
			
			passerObjs.add(clone);
			passerScores.add(score);
		}
	}

	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	@Getter
	@Setter
	public static class PersonBodyObject implements Cloneable, DP{
		public Integer id;
		public String  pid;
		public float[] feature;
		public String  tag;
		public String  cnName;
		public String  enName;
		public String  avatar;
		public String  privilege;
		
		@Override
		public PersonBodyObject clone() {
			try {
				return getClass().cast(super.clone());
			} catch (CloneNotSupportedException e) {
				throw new RuntimeException(e);
			}
		}
		
		public static PersonBodyObject convert(PersonFaceFeature pf) {
			try {
				PersonBodyObject obj = new PersonBodyObject();
				obj.feature = FaissSeeker.stringToFeature(pf.getImageFeature());
				obj.id = pf.getId();
				obj.pid = pf.getPersonUuid();
				obj.tag = pf.getTag();
				obj.avatar = pf.getAvatarImageUrl();
				obj.cnName = pf.getPersonCnName();
				obj.enName = pf.getPersonEnName();
				obj.privilege = pf.getPrivilege();
				return obj;
			}catch(Exception e) {
				e.printStackTrace();
				return null;
			}
		}

		private transient String[] privilege_0;
		
		public String[] privilege() {
			if(privilege_0 != null)
				return privilege_0;
			
			privilege_0 = StringUtils.split(privilege, ",");
			
			return privilege_0;
		}
	}

	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	@Getter
	@Setter
	public static class PasserBodyObject implements Cloneable, DP{
		public Integer id;
		public String  pid;
		public float[] feature;
		public String  groupId;
		public String  avatar;
		public String  privilege;
		
		public static PasserBodyObject convert(PasserFaceFeature pf) {
			try {
				PasserBodyObject obj = new PasserBodyObject();
				obj.id = pf.getId();
				obj.feature = FaissSeeker.stringToFeature(pf.getImageFeature());
				obj.pid = pf.getPersonUuid();
				obj.groupId = pf.getGroupId();
				obj.avatar = pf.getAvatarImageUrl();
				obj.privilege = pf.getPrivilege();
				return obj;
			}catch(Exception e) {
				e.printStackTrace();
				return null;
			}
		}
		
		@Override
		public PasserBodyObject clone() {
			try {
				return getClass().cast(super.clone());
			} catch (CloneNotSupportedException e) {
				throw new RuntimeException(e);
			}
		}

		private transient String[] privilege_0;
		
		public String[] privilege() {
			if(privilege_0 != null)
				return privilege_0;
			
			privilege_0 = StringUtils.split(privilege, ",");
			
			return privilege_0;
		}
	}
}
