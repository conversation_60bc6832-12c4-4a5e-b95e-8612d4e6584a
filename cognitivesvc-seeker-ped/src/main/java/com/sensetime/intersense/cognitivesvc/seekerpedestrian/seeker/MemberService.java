package com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.google.common.collect.Sets;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PersonBodyObject;
import com.sensetime.intersense.cognitivesvc.server.feign.MemberFeign;
import com.sensetime.lib.clientlib.response.BaseRes;

@Component("pedMemberService")
@EnableScheduling
public class MemberService {
	
    private Map<String, Set<String>> memberIdCache = new ConcurrentHashMap<String, Set<String>>();
    
    private Map<String, List<PersonBodyObject>> memberObjCache = new ConcurrentHashMap<String, List<PersonBodyObject>>();

    @Autowired
	private ApplicationContext context;
	
	public Set<String> getGroupMemberId(String personGroup){	
		Set<String> result = memberIdCache.get(personGroup);
		if(result != null) 
			return result;
		
		synchronized(memberIdCache) {
			result = memberIdCache.get(personGroup);
			if(result == null)
				memberIdCache.put(personGroup, getGroupMember_0(personGroup));
		}
		
		result = memberIdCache.get(personGroup);
		return result;
	}
	
	public List<PersonBodyObject> getGroupMemberObj(List<PersonBodyObject> cachedPerson, String personGroup){		
		List<PersonBodyObject> result = memberObjCache.get(personGroup);
		if(result != null) 
			return result;
		
		synchronized(memberObjCache) {
			result = memberObjCache.get(personGroup);
			if(result == null) {
				Set<String> memberIds = getGroupMemberId(personGroup);
				result = cachedPerson.stream().filter(item -> memberIds.contains(item.pid)).collect(Collectors.toList());
			}
		}
		
		return result;
	}
	
	@Scheduled(fixedDelay = 60 * 1000)
    void fetchData() {
		memberIdCache.clear();
		memberObjCache.clear();
		
		context.getBean(MemberFeign.class);
	}
	
	private Set<String> getGroupMember_0(String personGroup){
		try {
			MemberFeign.GroupMemberIdsReq req = new MemberFeign.GroupMemberIdsReq();
			req.setGroupId(personGroup);
			
			BaseRes<List<String>> list = context.getBean(MemberFeign.class).getMemberIdsFromGroup("*", "*", req);
			return list.getData().parallelStream().collect(Collectors.toSet());
		}catch(Exception e) {
			e.printStackTrace();
			return Sets.newHashSet();
		}
	}
}
