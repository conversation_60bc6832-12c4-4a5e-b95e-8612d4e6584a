package com.sensetime.intersense.cognitivesvc.seekerpedestrian;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.ConfigurableEnvironment;

import com.sensetime.intersense.cognitivesvc.seekerpedestrian.service.utils.SeekerPedestrianInitializer;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;

import lombok.extern.slf4j.Slf4j;

@Configuration("seekerPedestrianConfiguation")
@ComponentScan
@ConditionalOnExpression("${seeker.enabled:true} && ${seeker.pedestrian.enabled:true}")
@Slf4j
@PropertySource("classpath:seekerpedestrian.properties")
public class CognitivesvcConfiguation {
	

	@Autowired
	private ConfigurableEnvironment env;
	
	@Autowired
	private Utils utils;
	
	@PostConstruct
	public void initialize(){
		utils.toString();
		
		SeekerPedestrianInitializer.initialize(env);
		
		log.warn("\n");
		log.warn("*****************************************");
		log.warn("*********init seeker pedestrian***********");
		log.warn("*****seeker.enabled=false to disable*****");
		log.warn("seeker.pedestrian.enabled=false to disable");
		log.warn("*****************************************");
		log.warn("\n");
	}
}
