package com.sensetime.intersense.cognitivesvc.strangerpedestrian;

import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.PostConstruct;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.sensetime.intersense.cognitivesvc.strangerpedestrian.service.AbstractPedestrianStrangerSeeker;
import com.sensetime.intersense.cognitivesvc.strangerpedestrian.service.StrangerPedestrianJustSeeker;
import com.sensetime.intersense.cognitivesvc.strangerpedestrian.service.StrangerPedestrianSeekerFacade;

@Configuration("strangerPedestrianConfiguation")
@ComponentScan
@ConditionalOnExpression("${stranger.enabled:true} && ${stranger.pedestrian.enabled:true} && ${seeker.enabled:true} && ${seeker.pedestrian.enabled:true}")
@Slf4j
public class CognitivesvcConfiguation{

	@PostConstruct
	public void initialize(){
		log.warn("\n");
		log.warn("********************************************");
		log.warn("**********init stranger*********************");
		log.warn("*****stranger.enabled=false to disable******");
		log.warn("stranger.pedestrian.enabled=false to disable");
		log.warn("*****seeker.enabled=false to disable********");
		log.warn("*seeker.pedestrian.enabled=false to disable*");
		log.warn("********************************************");
		log.warn("\n");
	}
	
	@Bean
	public StrangerPedestrianSeekerFacade facadeStrangerPedestrian() {
		return new StrangerPedestrianSeekerFacade();
	}
	
	@Bean
	public AbstractPedestrianStrangerSeeker justPedestrianStranger() {
		return new StrangerPedestrianJustSeeker();
	}
}
