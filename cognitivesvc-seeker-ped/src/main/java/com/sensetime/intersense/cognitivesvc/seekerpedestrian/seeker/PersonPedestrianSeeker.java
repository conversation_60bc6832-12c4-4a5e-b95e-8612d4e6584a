package com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PersonBodyObject;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.service.utils.SeekerPedestrianInitializer;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonPedestrianFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SeekParam;

import com.sensetime.intersense.cognitivesvc.server.mapper.PersonPedestrianFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;

@EnableScheduling
@Component
public class PersonPedestrianSeeker extends FaissSeeker<PersonPedestrianFeature, PersonBodyObject>{

	@Autowired
	private PersonPedestrianFeatureRepository mapper;
	
	@Autowired
	private MemberService memberService;

	@Override
	protected Stream<Pair<PersonBodyObject, Float>> find(Stream<Pair<PersonBodyObject, Float>> baseStream, SeekParam param) {
		PersonParam personParam = ((PersonParam)param);
		
		String[] tags = personParam.getTags();
		String[] personGroups = personParam.getPersonGroups();
		boolean noCheckPrivilege = ArrayUtils.isEmpty(personParam.deptIds) || ArrayUtils.contains(personParam.deptIds, "0") || ArrayUtils.contains(personParam.deptIds, "*");

		if(ArrayUtils.isNotEmpty(personGroups)) {
			List<Set<String>> memberSets = Arrays.stream(personGroups)
					.map(personGroup -> memberService.getGroupMemberId(personGroup))
					.collect(Collectors.toList());
			
			baseStream = baseStream.filter(pair -> {
				for(Set<String> memberSet : memberSets)
					if(memberSet.contains(pair.getLeft().pid))
						return true;
				
				return false;
			});
		}
		
		if(ArrayUtils.isNotEmpty(tags)) {
			baseStream = baseStream.filter(pair -> {
				if(StringUtils.isBlank(pair.getLeft().tag))
					return false;
				
				for(String tag : tags) 
					if(pair.getLeft().tag.startsWith(tag)) 
						return true;
					
				return false;
			});
		}
		
		if(!noCheckPrivilege) {
			baseStream = baseStream.filter(pair -> {
				String[] pairPrivilege = pair.getLeft().privilege();
				return Arrays.stream(personParam.deptIds).filter(deptId -> ArrayUtils.contains(pairPrivilege, deptId)).findAny().isPresent();
			});
		}
		
		return baseStream;
	}
	
	@Override
	protected PersonBodyObject convert(PersonPedestrianFeature sp) {
		try {
			PersonBodyObject obj = new PersonBodyObject();
			obj.id        = sp.getId();
			obj.pid       = sp.getPersonUuid();
			obj.tag       = sp.getTag();
			obj.avatar    = sp.getAvatarImageUrl();
			obj.cnName    = sp.getPersonCnName();
			obj.enName    = sp.getPersonEnName();
			obj.privilege = sp.getPrivilege();
			obj.feature   = stringToFeature(sp.getImageFeature());
			return obj;
		}catch(Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	@Override
	protected int dims() { return SeekerPedestrianInitializer.dims; }
	
	@Override
	protected float normalize_feature_score(float score) { return normalize_feature_score(score, SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint); }
	
	@Override
	protected boolean gpuFaiss() { return Faiss.faissType == FaissType.GPU; }

	@Override
	protected Integer queryMaxId() { return mapper.queryMaxId(); }

	@Override
	protected List<PersonPedestrianFeature> querySplit(int start, int end, int totalSplitNum, int currentSplitNum) { return mapper.querySplit(start, end, totalSplitNum, currentSplitNum); }

	@Override
	protected long countSplit(int totalSplitNum, int currentSplitNum) { return mapper.countSplit(totalSplitNum, currentSplitNum); }
}
