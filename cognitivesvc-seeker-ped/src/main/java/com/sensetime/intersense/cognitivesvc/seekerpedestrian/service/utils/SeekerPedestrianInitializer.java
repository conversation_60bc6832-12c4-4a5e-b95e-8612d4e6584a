package com.sensetime.intersense.cognitivesvc.seekerpedestrian.service.utils;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sun.jna.Memory;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SeekerPedestrianInitializer {
	
	private static boolean initialized = false;
	
	//启动时塞入当前模型的人脸特征的维度
	public static int dims = Integer.parseInt(Utils.getProperty("seeker.pedestrian.dims", "256"));
	
	public static float[] pSrcPoint;
	public static float[] pDstPoint;
	
	/**
	 * 初始化，需要调用，某些参数可以为null，需要什么功能，就传什么model进去
	 */
	public synchronized static void initialize(Environment env) {
		if(initialized)
			return ;
		
		Initializer.initialize(env);
		
		String k_src = Utils.getProperty("P_SRC_POINT");
		String k_dst = Utils.getProperty("P_DST_POINT");
		
		if(StringUtils.isNotBlank(k_src) && StringUtils.isNotBlank(k_dst)){
			String srcs[] = k_src.split(",");
			String dsts[] = k_dst.split(",");
			
			if(srcs.length != dsts.length) 
				throw new RuntimeException("P_SRC_POINT and P_DST_POINT length not even.");
			
			pSrcPoint = new float[srcs.length];
			pDstPoint = new float[dsts.length];
			
			for(int index = 0; index < srcs.length; index ++) {
				pSrcPoint[index] = Float.parseFloat(srcs[index]);
				pDstPoint[index] = Float.parseFloat(dsts[index]);
			}
			log.info("****************************************** pedestrian score normalize using : [env] ******************************************************");
		}
		
		if(pSrcPoint == null || pDstPoint == null) {
			try {
				loadThres();
			}catch(Exception e) {
				log.info("reading score_norm_param.json error, using default.");
			}
		}
		
		if(pSrcPoint == null || pDstPoint == null) {
//			log.info("*****************************pedestrian score normalize using default.*****************************");
//			log.info("********** src : {-1.0f, -0.8f, -0.6f, -0.4f, -0.2f, 0.0f, 0.2f, 0.4f, 0.6f, 0.8f, 1.0f }**********");
//			log.info("********** dst : { 0.0f,  0.1f,  0.2f,  0.3f,  0.4f, 0.5f, 0.6f, 0.7f, 0.8f, 0.9f, 1.0f }**********");
//			log.info("************************default model normalize is [KM_Senu_1.5.1]*********************************");

			//https://ones.ainewera.com/wiki/#/team/JNwe8qUX/space/GebSt74Y/page/N8c1wRKC
			//1.56
			pSrcPoint = new float[] {-1.0f, 0.0f, 0.3f, 0.353f, 0.438f, 0.513f, 0.580f, 0.631f, 1.0f };
			pDstPoint = new float[] { 0.0f, 0.0f, 0.1f, 0.6f,  0.7f, 0.8f, 0.85f, 0.95f, 1.0f };


			log.info("****************************************** pedestrian score normalize using : [default] ******************************************************");

		}

		log.info("****************************************** src : {} *********************************************",JSON.toJSONString(pSrcPoint));
		log.info("****************************************** dst : {} *********************************************",JSON.toJSONString(pDstPoint));
		log.info("****************************************** pedestrian model is: {} *******", Initializer.modelPathMap.get("senu_feature_module"));
    	initialized = true;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void loadThres() {
		PointerByReference feature_model = new PointerByReference();
		KestrelApi.kestrel_model_load("/usr/cognitivesvc/" + Initializer.modelPathMap.get("senu_feature_module"), feature_model);
		LongByReference file_size = new LongByReference();
		file_size.setValue(KestrelApi.kestrel_model_file_size(feature_model.getValue(), "score_norm_param.json"));
		Memory buf = new Memory(file_size.getValue());
		KestrelApi.kestrel_model_get_file(feature_model.getValue(), "score_norm_param.json", buf, file_size);
		KestrelApi.kestrel_model_unload(feature_model);
		
		String json = buf.getString(0).substring(0, (int)file_size.getValue());
		log.info("reading score_norm_param.json : \n" + json + "\n");
		
		Map<String, JSONArray> points = (Map)JSON.parseObject(json);
		Float[] src_points = points.get("src_points").stream().map(Number.class::cast).map(Number::floatValue).toArray(Float[]::new);
		Float[] dst_points = points.get("dst_points").stream().map(Number.class::cast).map(Number::floatValue).toArray(Float[]::new);
		if(dst_points.length != src_points.length)
			throw new RuntimeException("length not even.");
		
		pSrcPoint = new float[src_points.length];
		pDstPoint = new float[dst_points.length];
		
		for(int index = 0; index < src_points.length; index ++) {
			pSrcPoint[index] = src_points[index];
			pDstPoint[index] = dst_points[index];
		}
	}
}
