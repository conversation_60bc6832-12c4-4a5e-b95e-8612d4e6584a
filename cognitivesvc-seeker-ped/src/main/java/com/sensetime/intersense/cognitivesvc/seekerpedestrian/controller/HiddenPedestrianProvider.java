package com.sensetime.intersense.cognitivesvc.seekerpedestrian.controller;

import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PasserBodyObject;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PersonBodyObject;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@Tag(name = "HiddenPedestrianProvider")
@RequestMapping(value = "/cognitive/hidden/pedestrian/", produces = MediaType.APPLICATION_JSON_VALUE)
public class HiddenPedestrianProvider extends BaseProvider {
	
	@Value("${spring.application.name}")
	private String appName;

    @Autowired
    private SeekerPedestrianFacade seekerFacade;

    @SuppressWarnings("unchecked")
    @Operation(summary = "删除人员特征值(缓存中)", method = "POST", hidden = true)
    @RequestMapping(value = "/deleteByPid", method = RequestMethod.POST)
    public BaseRes<?> deleteByPid(@RequestBody Map<String, Object> param) {
        List<Integer> ids = (List<Integer>) param.getOrDefault("ids", List.of());

		seekerFacade.deletePersons(ids, false);
		seekerFacade.deletePassers(ids, param.containsKey("startTime") && param.containsKey("endTime"));
		
		return BaseRes.success();
	}

    @Operation(summary = "用特征搜人Local", method = "POST", hidden = true)
    @RequestMapping(value = "/local", method = RequestMethod.POST)
    @ResponseBody
    public SeekResponse seekLocal(@RequestBody SeekRequest request) {
        SeekResponse result = new SeekResponse();

        if (request.personParam != null)
            for (Pair<PersonBodyObject, Float> pair : seekerFacade.findPersonLocal(request.personParam))
                result.addPerson(pair.getLeft(), pair.getRight());

        if (request.passerParam != null)
            for (Pair<PasserBodyObject, Float> pair : seekerFacade.findPasserLocal(request.passerParam))
                result.addPasser(pair.getLeft(), pair.getRight());

        return result;
    }

    @Schema(title = "用特征搜索库", description = "用特征搜索库")
    public static class SeekRequest {
        @Schema(description = "搜索人员库")
        public PersonParam personParam;

        @Schema(description = "搜索归档库")
        public PasserParam passerParam;

		public SeekRequest() {}
		
		public SeekRequest(PersonParam personParam, PasserParam passerParam) {
			this.personParam = personParam;
			this.passerParam = passerParam;
		}

		public SeekRequest(PersonParam personParam) {
			this.personParam = personParam;
		}

        public SeekRequest(PasserParam passerParam) {
            this.passerParam = passerParam;
        }
    }

    @Schema(title = "搜索结果List", description = "搜索结果List")
    public static class SeekResponse {
        @Schema(description = "person列表")
        public List<PersonBodyObject> personObjs = new ArrayList<>();
        @Schema(description = "person的比对分")
        public List<Float> personScores = new ArrayList<>();

        @Schema(description = "passer列表")
        public List<PasserBodyObject> passerObjs = new ArrayList<>();
        @Schema(description = "passer的比对分")
        public List<Float> passerScores = new ArrayList<>();

        public void addPerson(PersonBodyObject obj, float score) {
            PersonBodyObject clone = obj.clone();
            clone.feature = null;

            personObjs.add(clone);
            personScores.add(score);
        }

        public void addPasser(PasserBodyObject obj, float score) {
            PasserBodyObject clone = obj.clone();
            clone.feature = null;

            passerObjs.add(clone);
            passerScores.add(score);
        }
    }
}
