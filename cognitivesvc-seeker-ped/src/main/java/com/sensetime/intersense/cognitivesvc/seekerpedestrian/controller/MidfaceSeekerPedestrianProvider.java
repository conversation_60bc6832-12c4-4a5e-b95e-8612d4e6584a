package com.sensetime.intersense.cognitivesvc.seekerpedestrian.controller;

import com.sensetime.lib.weblib.utils.Baggages;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonPedestrianFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.CompareFeatureParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.mapper.FacePedestrianClusterRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserPedestrianFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonPedestrianFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.lib.clientlib.response.BaseRes;

import io.swagger.v3.oas.annotations.Operation;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping(value = "/cognitive/pedestrian/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="MidfaceSeekerPedestrianProvider",description = "face controller")
@SuppressWarnings("rawtypes")
public class MidfaceSeekerPedestrianProvider extends BaseProvider {
	
	@Autowired
	private MidfaceSeekerPedestrianProvider midfaceSeekerPedestrianProvider;

	@Autowired
	private Broadcaster broadcastService;
	
	@Autowired
	private PersonPedestrianFeatureRepository personMapper;
	
	@Autowired
	private PasserPedestrianFeatureRepository passerMapper;
	
	@Autowired
	private FacePedestrianClusterRepository facePedestrianClusterRepository;
	
	@Autowired
	private SeekerPedestrianFacade seekerFacade;
	
	@Value("${spring.application.name}")
	private String appName;
	
	@Operation(summary = "保留多少天的数据",method = "GET")
	@RequestMapping(value = "/deleteParsserByDays", method = {RequestMethod.GET, RequestMethod.POST})
	public BaseRes<Integer> deleteParsserByDays(@Parameter(required = true, name = "days", description = "days") @RequestParam int days) {
		return deleteByPid(null, new Date(0), new Date(System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L)));
	}
	
	@Operation(summary = "删除人员特征值", method = "POST")
	@RequestMapping(value = "/deleteByPId", method = {RequestMethod.GET, RequestMethod.POST})
	public BaseRes<Integer> deleteByPid( @Parameter(required = false, name = "pids", description = "pid") @RequestParam(required = false) List<String> pids
			,@Parameter(required = false, name = "startTime", description = "startTime") @RequestParam(required = false) Date startTime
			,@Parameter(required = false, name = "endTime", description = "endTime") @RequestParam(required = false) Date endTime) {
		int personDeleted = 0, passerDeleted = 0;
		List<Integer> ids = new ArrayList<Integer>();
		
		if(!CollectionUtils.isEmpty(pids)) {
			List<Integer> personIds = personMapper.queryIdsByPersonUuids(pids);
			List<Integer> passerIds = passerMapper.queryIdsByPersonUuids(pids);

			if(!CollectionUtils.isEmpty(personIds)) {
				personDeleted += personMapper.deleteByPersonUuidIn(pids);
				ids.addAll(personIds);
			}
			
			if(!CollectionUtils.isEmpty(passerIds)) {
				passerDeleted += passerMapper.deleteByPersonUuidIn(pids);
				ids.addAll(passerIds);
			}
			
			if(personDeleted > 0 || passerDeleted > 0)
				facePedestrianClusterRepository.deleteByFacePersonIdIn(pids);
		}
		
		if(startTime != null && endTime != null)
			passerDeleted += passerMapper.deleteByCreateTsBetween(startTime, endTime);
		
		if(personDeleted > 0 || passerDeleted > 0) {
			Map<String, Object> varibles = new HashMap<String, Object>();
			varibles.put("ids", ids);
			
			if(startTime != null)
				varibles.put("startTime", startTime);
			
			if(endTime != null)
				varibles.put("endTime", endTime);
			
			broadcastService.postForObject(appName, "/cognitive/hidden/pedestrian/deleteByPid", varibles, String.class);
		}
		
		return BaseRes.success(personDeleted + passerDeleted);
	}
	
	@SuppressWarnings({ "unchecked" })
	@Operation(summary = "用特征搜人", method = "POST")
	@RequestMapping(value = "/comparePedestrianFeature", method = RequestMethod.POST)
	@ResponseBody
	 @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
	public BaseRes<List<Map>> comparePedestrianFeature(@RequestBody CompareFeatureParam param) {
		
		if (param.getCount() != null && param.getCount() <= 0)
			param.setCount(null);
		
		float[] feature = null;
		
		if(param.getFeatureFloatArray() != null)
			feature = param.getFeatureFloatArray();
		else if(param.getFeatureBase64() != null) 
			feature = FaissSeeker.stringToFeature(param.getFeatureBase64());
		
		if(feature == null)
			return BaseRes.success();
		
		List<Map> result = new ArrayList<Map>();

		String[] deptids = Baggages.getAllDeptIds();

//		if(ArrayUtils.contains(deptids, "*"))
//			deptids = null;
		
		if ("Target".equals(param.getPersonType())) {
			seekerFacade.findPerson(PersonParam.builder()
					.feature(feature)
					.count(param.getCount())
					.personGroups(StringUtils.isBlank(param.getPersonGroup()) ? null : param.getPersonGroup().split(","))
					.tags(StringUtils.isBlank(param.getPersonTag()) ? null : param.getPersonTag().split(","))
					.threshold(param.getThreshold())
					.deptIds(deptids)
					.build())
				.stream()
				.map(item -> {
					Map map = new HashMap();
					map.put("personID", item.getLeft().pid);
					map.put("targetType", "Target");
					map.put("score", item.getValue());
					return map;
				})
				.forEach(result::add);
		} else if ("Passer".equals(param.getPersonType())) {
			seekerFacade.findPasser(PasserParam.builder()
					.feature(feature)
					.count(param.getCount())
					.deptIds(deptids)
					.threshold(param.getThreshold())
					.build())
				.stream()
				.map(item -> {
					Map map = new HashMap();
					map.put("personID", item.getLeft().pid);
					map.put("targetType", "Passer");
					map.put("score", item.getValue());
					return map;
				})
				.forEach(result::add);
		}else {
			seekerFacade.findPerson(PersonParam.builder()
					.feature(feature)
					.count(param.getCount())
					.personGroups(StringUtils.isBlank(param.getPersonGroup()) ? null : param.getPersonGroup().split(","))
					.tags(StringUtils.isBlank(param.getPersonTag()) ? null : param.getPersonTag().split(","))
					.threshold(param.getThreshold())
					.deptIds(deptids)
					.build())
				.stream()
				.map(item -> {
					Map map = new HashMap();
					map.put("personID", item.getLeft().pid);
					map.put("targetType", "Target");
					map.put("score", item.getValue());
					return map;
				})
				.forEach(result::add);
			
			if(result.isEmpty()) {
				seekerFacade.findPasser(PasserParam.builder()
						.feature(feature)
						.count(param.getCount())
						.deptIds(deptids)
						.threshold(param.getThreshold())
						.build())
					.stream()
					.map(item -> {
						Map map = new HashMap();
						map.put("personID", item.getLeft().pid);
						map.put("targetType", "Passer");
						map.put("score", item.getValue());
						return map;
					})
					.forEach(result::add);
			}
		}
		
		return BaseRes.success(result);
	}
	
	@Operation(summary = "用一条person的特征", method = "POST")
	@RequestMapping(value = "/retrieveFeature", method = RequestMethod.POST)
	public BaseRes<Integer> retrieveFeature(@RequestBody Map<String, String> parameters) {
		List<PersonPedestrianFeature> list = personMapper.findByPersonUuid(parameters.get("personId"));
		if(list.size() > 0)
			midfaceSeekerPedestrianProvider.deleteByPid(List.of(parameters.get("personId")), null, null);
		
		String deptid = Baggages.getDeptId();
		PersonPedestrianFeature pff = new PersonPedestrianFeature();
		pff.setAvatarImageUrl(parameters.get("figureImageUrl"));
		pff.setCreateUser(CREATE_MODIFY_USER);
		pff.setPrivilege("*".equals(deptid) ? "0" : deptid);
		pff.setPersonCnName(parameters.get("personCnName"));
		pff.setLastModTs(new Date());
		pff.setImageFeature(parameters.get("figureFeature"));
		pff.setPersonUuid(parameters.get("personId"));
		pff.setSts(STS_VALID);
		pff.setPersonEnName(parameters.get("personEnName"));
		pff.setTag(parameters.get("tag"));
		pff.setModelVersion(parameters.get("modelVersion"));
		pff.setCreateTs(new Date());
		
		personMapper.saveAndFlush(pff);
		
		return BaseRes.success(1);
	}
	
	@Operation(summary = "人脸库和数据库同步一下",method = "GET")
	@RequestMapping(value = "/refetch", method = RequestMethod.GET)
	public void refetch(@RequestParam(required = false) Boolean isBroadcast) {
		if(Boolean.TRUE.equals(isBroadcast)) {
			broadcastService.getForObject(appName, "/cognitive/pedestrian/refetch", Map.of(), String.class);
		}else {
			seekerFacade.reFetchData();
		}
	}
	
	@Operation(summary = "清空表", method = "POST")
	 @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
	@RequestMapping(value = "/scrub/feature", method = RequestMethod.POST)
	public BaseRes<Object> scrubFeature(@Parameter(name = "type", description = "类型") @RequestParam(value = "type") String type){
		if("person".equals(type)) {
			personMapper.deleteAll();
			facePedestrianClusterRepository.deleteByPedestrianPersonType(0);
		}
		
		if("passer".equals(type)) {
			passerMapper.deleteAll();
			facePedestrianClusterRepository.deleteByPedestrianPersonType(1);
		}
		
		refetch(true);
		
		return BaseRes.success("OK");
	}
}
