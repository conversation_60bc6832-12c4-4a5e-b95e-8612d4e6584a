package com.sensetime.intersense.cognitivesvc.seekerpedestrian.controller;

import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;

import io.swagger.v3.oas.annotations.Operation;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController("pedestrianSeekerTestProvider")
@RequestMapping(value = "/test", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="TestProvider",description = "Test")
public class TestProvider extends BaseProvider {
	
	@Operation(summary = "test",method = "GET")
	@RequestMapping(value = "/test", method = RequestMethod.GET)
	public String test(){
		
		return "OK";
	}
}
