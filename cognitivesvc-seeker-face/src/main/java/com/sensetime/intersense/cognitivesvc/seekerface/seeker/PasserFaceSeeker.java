package com.sensetime.intersense.cognitivesvc.seekerface.seeker;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PasserFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.entities.PasserFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SeekParam;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;

@EnableScheduling
@Component
public class PasserFaceSeeker extends FaissSeeker<PasserFaceFeature, PasserFaceObject>{
	
	@Autowired
	private PasserFaceFeatureRepository mapper;

	@Override
	protected Stream<Pair<PasserFaceObject, Float>> find(Stream<Pair<PasserFaceObject, Float>> baseStream, SeekParam param) {
		PasserParam passerParam = ((PasserParam)param);
		
		String groupId = passerParam.groupId;
		
		boolean noCheckPrivilege = ArrayUtils.isEmpty(passerParam.deptIds) || ArrayUtils.contains(passerParam.deptIds, "0") || ArrayUtils.contains(passerParam.deptIds, "*");
		
		return baseStream.filter(pair -> {
			if(StringUtils.isNotBlank(groupId) && !StringUtils.equals(groupId, pair.getLeft().groupId))
				return false;
			
			if(!noCheckPrivilege) {
				String[] pairPrivilege = pair.getLeft().privilege();
				return Arrays.stream(passerParam.deptIds).filter(deptId -> ArrayUtils.contains(pairPrivilege, deptId)).findAny().isPresent();	
			}
			
			return true;
		});
	}

	@Override
	protected PasserFaceObject convert(PasserFaceFeature sp) {
		try {
			PasserFaceObject obj = new PasserFaceObject();
			obj.id        = sp.getId();
			obj.pid       = sp.getPersonUuid();
			obj.groupId   = sp.getGroupId();
			obj.avatar    = sp.getAvatarImageUrl();
			obj.privilege = sp.getPrivilege();
			obj.feature   = stringToFeature(sp.getImageFeature());
			return obj;
		}catch(Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	@Override
	protected int dims() { return SeekerFaceInitializer.dims; }
	
	@Override
	protected float normalize_feature_score(float score) { return normalize_feature_score(score, SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint); }
	
	@Override
	protected boolean gpuFaiss() { return Faiss.faissType == FaissType.GPU; }

	@Override
	protected Integer queryMaxId() { return mapper.queryMaxId(); }

	@Override
	protected long countSplit(int totalSplitNum, int currentSplitNum) { return mapper.countSplit(totalSplitNum, currentSplitNum); }

	@Override
	protected List<PasserFaceFeature> querySplit(int start, int end, int totalSplitNum, int currentSplitNum) { return mapper.querySplit(start, end, totalSplitNum, currentSplitNum); }
}
