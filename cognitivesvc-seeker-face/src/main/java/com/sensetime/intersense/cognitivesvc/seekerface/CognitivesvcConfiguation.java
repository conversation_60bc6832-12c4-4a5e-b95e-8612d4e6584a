package com.sensetime.intersense.cognitivesvc.seekerface;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.ConfigurableEnvironment;

import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;

import lombok.extern.slf4j.Slf4j;

@Configuration("seekerFaceConfiguation")
@ComponentScan
@ConditionalOnExpression("${seeker.enabled:true} && ${seeker.face.enabled:true}")
@Slf4j
@PropertySource("classpath:seekerface.properties")
public class CognitivesvcConfiguation {
	

	@Autowired
	private ConfigurableEnvironment env;
	
	@Autowired
	private Utils utils;
	
	@PostConstruct
	public void initialize(){
		utils.toString();
		
		SeekerFaceInitializer.initialize(env);
		
		log.warn("\n");
		log.warn("************************************");
		log.warn("**********init seeker face**********");
		log.warn("***seeker.enabled=false to disable**");
		log.warn("seeker.face.enabled=false to disable");
		log.warn("************************************");
		log.warn("\n");
	}
}
