package com.sensetime.intersense.cognitivesvc.seekerface.seeker;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PersonFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SeekParam;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@EnableScheduling
@Component
@Slf4j
public class PersonFaceSeeker extends FaissSeeker<PersonFaceFeature, PersonFaceObject> {
    
    @Autowired
    private PersonFaceFeatureRepository mapper;
    
    @Autowired
    private MemberService memberService;
    
    
    @Override
    protected Stream<Pair<PersonFaceObject, Float>> find(Stream<Pair<PersonFaceObject, Float>> baseStream, SeekParam param) {
        PersonParam personParam = ((PersonParam) param);
        
        String[] tags = personParam.getTags();
        String[] personGroups = personParam.getPersonGroups();
        boolean noCheckPrivilege = ArrayUtils.isEmpty(personParam.deptIds) || ArrayUtils.contains(personParam.deptIds, "0") || ArrayUtils.contains(personParam.deptIds, "*");
        
        // 搜索人组
        long t_groupcheck_start = System.currentTimeMillis();
        if (ArrayUtils.isNotEmpty(personGroups)) {
            
            if (personParam.getUseGroupCache() != null && personParam.getUseGroupCache()) {
                List<Set<String>> memberSets = Arrays.stream(personGroups)
                        .map(personGroup -> memberService.getGroupMemberId(personGroup))
                        .collect(Collectors.toList());
                
                baseStream = baseStream.filter(pair -> {
                    for (Set<String> memberSet : memberSets)
                        if (memberSet.contains(pair.getLeft().pid))
                            return true;
                    
                    return false;
                });
            } else {
                List<Pair<PersonFaceObject, Float>> stream2list = baseStream.collect(Collectors.toList()); // 流需要用到两次，先转为数组
                
                List<String> foundPersonIds = stream2list.stream().map(p -> p.getLeft().pid).collect(Collectors.toList());
                Map<String, List<String>> uidGroupIdsMap = memberService.queryGroupIdsByPersonId(foundPersonIds);
                
                List<String> paramPersonGroups = new ArrayList<>(Arrays.asList(personGroups));
                baseStream = stream2list.stream()
                        .filter(pair -> {
                            List<String> groupIdsByPersonId = uidGroupIdsMap.get(pair.getLeft().pid) == null ? new ArrayList<>() : new ArrayList<>(uidGroupIdsMap.get(pair.getLeft().pid));
                            List<String> _paramPersonGroups = new ArrayList<>(paramPersonGroups);
                            _paramPersonGroups.retainAll(groupIdsByPersonId); // 取交集
                            if (_paramPersonGroups.size() > 0)
                                return true;
                            return false;
                        });
            }
            long t_groupcheck_end = System.currentTimeMillis();
            
            if (log.isDebugEnabled()) {
                SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
                filter.getExcludes().add("feature");
                log.debug(">>> [PersonFaceSeeker] find person - groupId filter. featureMd5={}, param={}, ts= {}ms",
                        Utils.computeMD5(param.feature), JSON.toJSONString(param, filter), t_groupcheck_end - t_groupcheck_start);
            }
        }
        
        // 搜索tags
        if (ArrayUtils.isNotEmpty(tags)) {
            baseStream = baseStream.filter(pair -> {
                if (StringUtils.isBlank(pair.getLeft().tag))
                    return false;
                
                for (String tag : tags)
                    if (pair.getLeft().tag.startsWith(tag))
                        return true;
                
                return false;
            });
        }
        
        if (!noCheckPrivilege) {
            baseStream = baseStream.filter(pair -> {
                String[] pairPrivilege = pair.getLeft().privilege();
                return Arrays.stream(personParam.deptIds).filter(deptId -> ArrayUtils.contains(pairPrivilege, deptId)).findAny().isPresent();
            });
        }
        
        return baseStream;
    }
    
    @Override
    protected PersonFaceObject convert(PersonFaceFeature sp) {
        try {
            PersonFaceObject obj = new PersonFaceObject();
            obj.id = sp.getId();
            obj.pid = sp.getPersonUuid();
            obj.tag = sp.getTag();
            obj.avatar = sp.getAvatarImageUrl();
            obj.cnName = sp.getPersonCnName();
            obj.enName = sp.getPersonEnName();
            obj.privilege = sp.getPrivilege();
            obj.feature = stringToFeature(sp.getImageFeature());
            return obj;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    @Override
    protected int dims() {
        return SeekerFaceInitializer.dims;
    }
    
    @Override
    protected float normalize_feature_score(float score) {
        return normalize_feature_score(score, SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint);
    }
    
    @Override
    protected boolean gpuFaiss() {
        return Faiss.faissType == FaissType.GPU;
    }
    
    @Override
    protected Integer queryMaxId() {
        return mapper.queryMaxId();
    }
    
    @Override
    protected List<PersonFaceFeature> querySplit(int start, int end, int totalSplitNum, int currentSplitNum) {
        return mapper.querySplit(start, end, totalSplitNum, currentSplitNum);
    }
    
    @Override
    protected long countSplit(int totalSplitNum, int currentSplitNum) {
        return mapper.countSplit(totalSplitNum, currentSplitNum);
    }
}
