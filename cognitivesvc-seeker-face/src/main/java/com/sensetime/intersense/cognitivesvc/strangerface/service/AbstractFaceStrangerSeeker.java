package com.sensetime.intersense.cognitivesvc.strangerface.service;

import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;

import com.sensetime.intersense.cognitivesvc.server.entities.StrangerFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;

public interface AbstractFaceStrangerSeeker{

	/**
	 * 来了一个陌生人。。。
	 */
	public abstract String strangerIsHere(StrangerFaceObject obj);

	public abstract void aggregateStranger();
	public abstract void enroleRollingWindow();
}
