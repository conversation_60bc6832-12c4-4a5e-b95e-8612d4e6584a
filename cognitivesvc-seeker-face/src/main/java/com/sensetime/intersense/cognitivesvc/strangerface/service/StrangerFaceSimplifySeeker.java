package com.sensetime.intersense.cognitivesvc.strangerface.service;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.entities.PasserFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.StrangerFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.feign.StrangerFeign;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.StrangerFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject.StrangerFaceObjectBuilder;
import com.sensetime.lib.clientlib.response.BaseRes;

import com.sensetime.storage.entity.ImageInfo;
import com.sensetime.storage.service.FileAccessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.stream.Collectors;

@Slf4j
@EnableScheduling
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableAsync(proxyTargetClass = true)
public class StrangerFaceSimplifySeeker implements  ApplicationListener<ContextRefreshedEvent>,AbstractFaceStrangerSeeker {

    @Autowired
    private ApplicationContext context;

    @Autowired
    private PasserFaceFeatureRepository passerMapper;

    @Autowired
    FileAccessor fileAccessor;

    @Autowired
    private StrangerFaceFeatureRepository strangerMapper;

    @Autowired
    StrangerSimplifySeekSync strangerSimplifySeekSync;

    @Value("${preMakeDirs}")
    private String preMakeDirs;


    @Value("${intersense.stranger.simplify-seeker.rolling-window-count:30}")
    private int rollingWindowCount;

    @Value("${intersense.stranger.simplify-seeker.rolling-window-length:60}")
    private long rollingWindowLength;       // 单位秒，可取范围： 10、15、20、30、60

    @Value("${intersense.stranger.simplify-seeker.tsgap-condition:3000}")
    private long tsGapCondition;            // 同一时间窗口，两张相似照片小于该时间间隔认为是同一次抓拍

    @Value("${intersense.stranger.simplify-seeker.period-showtimes-condition:2}")
    private long periodShowTimesCondition;

    @Value("${intersense.stranger.simplify-seeker.device-showtimes-condition:1}")
    private long deviceShowTimesCondition;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private volatile ConcurrentSkipListMap<Long, Queue<StrangerFaceObject>> rollingWindowMap = new ConcurrentSkipListMap<>();

    @Override
    public String strangerIsHere(StrangerFaceObject obj) {

        Long key = this.getRollingWindowMapKey(obj.createTs);
        if (log.isDebugEnabled())
            log.debug(">>> windowKey:{}，obj: {}, start deal strangerObject", key, obj.createTs.getTime());

        Queue<StrangerFaceObject> strangerObjects = rollingWindowMap.get(key);

        Long firstKey = (this.getRollingWindowMapKey(new Date()) - rollingWindowCount * rollingWindowLength); // rollingWindow中最早的窗口key
        if (strangerObjects != null) {
            for (StrangerFaceObject so : strangerObjects) {
                if (Objects.equals(so.getInfra().getDeviceTag(), obj.getInfra().getDeviceTag())
                        && Objects.equals(so.getInfra().getDeviceId(), obj.getInfra().getDeviceId())
                        && Objects.equals(so, obj)) {
                    float score = FaissSeeker.compare_feature_normalize(so.feature, obj.feature, SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint);
                    if (score > Utils.instance.Lv2Threshold) {
                        if (log.isDebugEnabled())
                            log.debug(">>> current rollingwindow exit similiar object, current obj:{} ,exit Obj: {}", obj.createTs.getTime(), so.createTs.getTime());
                        return null;
                    }
                }
            }
        } else if (key > firstKey) {
            strangerObjects = new ConcurrentLinkedQueue<>();
            rollingWindowMap.put(key, strangerObjects);
            while (rollingWindowMap.size() > rollingWindowCount)
                rollingWindowMap.remove(rollingWindowMap.firstKey());
        } else {
            if (log.isDebugEnabled())
                log.debug(">>> image not in process ts:{}...", obj.createTs.getTime());
            return null;
        }
        if (log.isDebugEnabled())
            log.debug(">>> windowKey:{},obj：{},add StrangerObject to cuccent window", key, obj.createTs.getTime());
        strangerObjects.add(obj);
        strangerMapper.saveAndFlush(builtStrangerFaceFeature(obj));
        return null;
    }


    @Scheduled(cron = "${intersense.stranger.simplify-seeker.enrole-window-cron:0 * * * * ?}") // todo ： 改为配置
    public synchronized void enroleRollingWindow() {
        if (log.isDebugEnabled())
            log.debug(">>> new rolling window start init...");
        Calendar c = Calendar.getInstance();
        c.clear(Calendar.SECOND);
        Long currentWindowKey = c.getTimeInMillis() / 1000;

        Queue<StrangerFaceObject> currentWindowObjects = rollingWindowMap.get(currentWindowKey);
        if (CollectionUtils.isEmpty(currentWindowObjects))
            rollingWindowMap.put(currentWindowKey, new ConcurrentLinkedQueue<>());

        while (rollingWindowMap.size() > rollingWindowCount)
            rollingWindowMap.remove(rollingWindowMap.firstKey());

        if (log.isDebugEnabled())
            log.debug(">>> new rollingwindow start，init！");
    }

    @Scheduled(cron = "${intersense.stranger.simplify-seeker.aggregate-stranger-cron:59 * * * * ?}") // todo ： 改为配置
    public synchronized void aggregateStranger() {
        Long current = System.currentTimeMillis();
        Long currentWindowKey = this.getRollingWindowMapKey(new Date(current));
        if (log.isDebugEnabled())
            log.debug(">>> time：{}，start new round aggregateStranger ,key:{}...", current, currentWindowKey);

        Queue<StrangerFaceObject> currentWindowObjects = rollingWindowMap.get(currentWindowKey);

        if (CollectionUtils.isEmpty(currentWindowObjects)) {
            /*if (currentWindowObjects == null) {
                rollingWindowMap.put(currentWindowKey, new ConcurrentLinkedQueue<>());
                while (rollingWindowMap.size() > rollingWindowCount)
                    rollingWindowMap.remove(rollingWindowMap.firstKey());
            }*/
            if (log.isDebugEnabled())
                log.debug(">>> windowKey:{},current window no image, skip", currentWindowKey);
            return;
        }


        List<Pair<String, String>> similarPairs = new ArrayList<>(); // 保存相似的两个StrangerObject的索引（格式: mapkey_listidx）

        // 比较1:当前窗口比较
        StrangerFaceObject[] currentWindowObjectsArr = currentWindowObjects.toArray(new StrangerFaceObject[0]);
        for (int i = 0; i < currentWindowObjectsArr.length; i++) {
            for (int j = i + 1; j < currentWindowObjectsArr.length; j++) {
                StrangerFaceObject objA = currentWindowObjectsArr[i];
                StrangerFaceObject objB = currentWindowObjectsArr[j];
                float score = FaissSeeker.compare_feature_normalize(objA.feature, objB.feature, SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint);
                long tsGap = Math.abs(objA.createTs.getTime() - objB.createTs.getTime());
                if (score > Utils.instance.Lv2Threshold && tsGap > tsGapCondition)
                    similarPairs.add(new MutablePair<>(currentWindowKey + "_" + i, currentWindowKey + "_" + j));
                else if (score > Utils.instance.Lv2Threshold && tsGap < tsGapCondition) {
                    StrangerFaceObject removedObj = objA.createTs.getTime() - objA.createTs.getTime() <= 0 ? objA : objB;
                    currentWindowObjects.remove(removedObj);
                }
            }
        }

        // 比较2:当前窗口与其他窗口比较
        for (Long windowKey : rollingWindowMap.keySet()) {
            if (windowKey.longValue() == currentWindowKey.longValue())
                continue;

            Queue<StrangerFaceObject> otherWindowObjects = rollingWindowMap.get(windowKey);
            StrangerFaceObject[] otherWindowObjectsArr = otherWindowObjects.toArray(new StrangerFaceObject[0]);
            for (int i = 0; i < otherWindowObjectsArr.length; i++) {
                for (int j = 0; j < currentWindowObjectsArr.length; j++) {

                    float score = FaissSeeker.compare_feature_normalize(otherWindowObjectsArr[i].feature, currentWindowObjectsArr[j].feature, SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint);
                    if (score > Utils.instance.Lv2Threshold)
                        similarPairs.add(new MutablePair<>(windowKey + "_" + i, currentWindowKey + "_" + j));
                }
            }
        }

        // 聚合相似的StrangerObject的索引
        List<Set<String>> similarIndexAggregation = new ArrayList<>();
        for (Pair<String, String> similarPair : similarPairs) {

            boolean existInAggr = false;
            for (Set<String> aggregation : similarIndexAggregation) {
                if (aggregation.contains(similarPair.getLeft()) || aggregation.contains(similarPair.getRight())) {
                    aggregation.add(similarPair.getLeft());
                    aggregation.add(similarPair.getRight());
                    existInAggr = true;
                    break;
                }
            }
            if (!existInAggr) {
                Set<String> aggr = new HashSet<>();
                aggr.add(similarPair.getLeft());
                aggr.add(similarPair.getRight());
                similarIndexAggregation.add(aggr);
            }
        }

        // 索引换取对象
        List<Set<Pair<Long, StrangerFaceObject>>> aggregationList = new ArrayList<>(); // map 中left为 rollingwindow的时间key值，
        for (Set<String> similarIndexs : similarIndexAggregation) {

            Set<Pair<Long, StrangerFaceObject>> similarGroup = new HashSet<>();
            for (String similarIndex : similarIndexs) {
                String[] index = similarIndex.split("_");
                StrangerFaceObject[] objectsArr = rollingWindowMap.get(Long.parseLong(index[0])).toArray(new StrangerFaceObject[0]);
                StrangerFaceObject strangerObject = objectsArr[Integer.parseInt(index[1])];
                similarGroup.add(new MutablePair<>(Long.parseLong(index[0]), strangerObject));
            }
            aggregationList.add(similarGroup);
        }


        // 归档条件计算
        for (Set<Pair<Long, StrangerFaceObject>> similars : aggregationList) {
            // todo：聚合中排除差的
            similars.forEach(e -> {
            });

            // 计算是否满足归档条件
            long periodShowTimes = similars.stream().map(s -> s.getLeft()).distinct().count();
            long deviceShowTimes = similars.stream().map(s -> s.getRight().getInfra().getDeviceId()).distinct().count();
            if (periodShowTimes < periodShowTimesCondition || deviceShowTimes < deviceShowTimesCondition)
                continue;

            // 满足条件入库
            // todo : 找出质量最好的，插入数据库
            Pair<Long, StrangerFaceObject> highestObj = similars.stream().findAny().get();

            String personUuid = null;
            String privilege = null;

            //File imagePath = new File(highestObj.getRight().imagePath);
            String finalPath = highestObj.getRight().imagePath;
            if(!highestObj.getRight().imagePath.equals(FrameUtils.NOIMAGE)){
                if(!highestObj.getRight().imagePath.contains("passer_face")){
                    try {
//                        byte[] data = fileAccessor.readImage(highestObj.getRight().imagePath);
//                        ImageInfo imageInfo = ImageInfo
//                                .builder()
//                                .imagePath("passer_face")
//                                .data(data)
//                                .build();
//
//                        finalPath = fileAccessor.writeImage(imageInfo);
                        finalPath = fileAccessor.cpImage(highestObj.getRight().imagePath,"passer_face" );
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            }


//            List<String> preMakeDirList = Arrays.asList(preMakeDirs.split(","));
//            File renameFile = ImageUtils.newFileWithMkdir("passer_face", preMakeDirList.contains("passer_face"));
//
//            try {
//                FileUtils.copyFile(imagePath, renameFile);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }

            PasserFaceFeature item = new PasserFaceFeature();
            item.setPersonUuid(personUuid);
            item.setPrivilege(privilege);
            item.setAvatarImageUrl(finalPath);
            item.setImageFeature(FaissSeeker.featureToString(highestObj.getRight().feature));
            item.setGroupId(highestObj.getRight().getInfra().getDeviceTag());
            item.setSts(0);
            item.setCreateTs(new Date());
            item.setPrivilege(highestObj.getRight().getInfra().getPrivilege());

            try {
                StrangerFeign.CreateStrangerReq req = new StrangerFeign.CreateStrangerReq();
                req.setImageURI(finalPath);
                req.setDesc(toDescNoPair(similars.stream().map(s -> s.getRight()).collect(Collectors.toList())));
                req.setPersonType("passer");
                req.setPersonTag(highestObj.getRight().getInfra().getDeviceTag());
                req.setPersonAge(highestObj.getRight().age);
                req.setPersonSex(highestObj.getRight().sex);
                req.setDeviceSource(highestObj.getRight().getInfra().getDeviceId());

                String trackId = highestObj.getRight().getTrackId() + "_" + Utils.keyToContextId(highestObj.getRight().getInfra().getDeviceId()) + "_" + Utils.instance.getSeed().substring(Utils.instance.getSeed().length() - 4, Utils.instance.getSeed().length());
                req.setRemark("{\"label\":37017, \"trackId\":\"" + trackId + "\", \"privilege\":\"" + highestObj.getRight().getInfra().getPrivilege() + "\"}");

                String captureTime = sdf.format(highestObj.getRight().createTs);
                req.setCaptureTime(captureTime);
                if (log.isDebugEnabled())
                    log.debug(">>> [person] call createStranger, req={} ", JSON.toJSONString(req));
                BaseRes<StrangerFeign.CreateRes> res = context.getBean(StrangerFeign.class).createStranger(req);
                if (log.isDebugEnabled())
                    log.debug(">>> [person] call createStranger, res={} ", JSON.toJSONString(res));

                if (res.isSuccess() && res.getData() != null) {
                    item.setPersonUuid(res.getData().getUuid().toString());
                    item.setPrivilege(Objects.requireNonNullElse(res.getData().getPrivilege(), item.getPrivilege()));
                }
            } catch (Exception e) {
                log.warn(e.getLocalizedMessage());
            }

            if (StringUtils.isEmpty(item.getPersonUuid())) {
                item.setPersonUuid("unknown-" + UUID.randomUUID().toString().substring(1, 6));
                item.setPrivilege(item.getPrivilege());
            }

            passerMapper.saveAndFlush(item);

            // 从缓存中移除这组归档数据
            similars.forEach(p -> rollingWindowMap.get(p.getLeft()).remove(p.getRight()));

        }

        if (log.isDebugEnabled())
            log.debug(">>> windowKey:{},curent aggregation end, cost: {} ms ...", currentWindowKey, System.currentTimeMillis() - current);
    }

    private Long getRollingWindowMapKey(Date time) {
        Calendar c = Calendar.getInstance();
        c.setTime(time);
        int second = c.get(Calendar.SECOND);
        c.clear(Calendar.SECOND);

        Long key = c.getTimeInMillis() / 1000 + (second / rollingWindowLength) * rollingWindowLength;
        return key;
    }

    /**
     * 每天一次 上一天的数据没有用了
     */
    @Scheduled(cron = "10 0 0 * * ?")
    public synchronized void renaming() {
        strangerMapper.deleteAll();
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        strangerSimplifySeekSync.asyncMethod(rollingWindowMap);
    }
//
//
//    @Async("cogThreadPool")
//    @Override
//    public void onApplicationEvent(ContextRefreshedEvent event) {
//        // init rolling window map
//        Calendar c = Calendar.getInstance();
//        Date endTime = c.getTime();
//        c.add(Calendar.MINUTE, (int) (rollingWindowCount * rollingWindowLength * -1));
//        Date startTime = c.getTime();
//
//        strangerMapper.findByCreateTsBetweenOrderByCreateTsDesc(startTime, endTime)
//        		.parallelStream()
//                .map(item -> {
//                	StrangerFaceObjectBuilder builder = StrangerFaceObject.builder()
//                			.id(item.getId())
//                			.imagePath(item.getAvatarImageUrl())
//                			.createTs(item.getCreateTs())
//                			.feature(FaissSeeker.stringToFeature(item.getImageFeature()))
//                			.infra(VideoStreamInfra.builder().deviceId(item.getDeviceId()).deviceTag(item.getGroupKey()).build());
//
//                	if(StringUtils.isNotBlank(item.getAttribute())) {
//                		String[] attributes = item.getAttribute().split(",");
//                		builder.age(attributes[0]).sex(attributes[1]);
//                	}
//
//                	return builder.build();
//                })
//                .forEach(s -> {
//                    Long key = this.getRollingWindowMapKey(s.createTs);
//                    Queue<StrangerFaceObject> rollingWindow = rollingWindowMap.get(key);
//                    if (rollingWindow == null) {
//                        rollingWindow = new ConcurrentLinkedQueue<>();
//                        rollingWindowMap.put(key, rollingWindow);
//                    }
//                    rollingWindow.add(s);
//                });
//
//        while (rollingWindowMap.size() > rollingWindowCount)
//            rollingWindowMap.remove(rollingWindowMap.firstKey());
//
//    }

    public static String toDesc(List<Pair<StrangerFaceObject,Float>> newMatches) {
        return newMatches.stream().sorted((l, r) -> r.getLeft().createTs.compareTo(l.getLeft().createTs))
                .map(item -> {
                    StrangerFaceObject obj = item.getLeft();
                    StringBuilder sb = new StringBuilder();
                    sb.append("{\"image\":\"");
                    sb.append(obj.imagePath);
                    sb.append("\",\"deviceId\":\"");
                    sb.append(obj.getInfra().getDeviceId());
                    sb.append("\",\"captureTime\":\"");
                    sb.append(Utils.dateFormat.get().format(obj.createTs));
                    sb.append("\"}");
                    return sb.toString();
                }).collect(Collectors.joining(",", "[", "]"));
    }

    public static String toDescNoPair(List<StrangerFaceObject> newMatches){
        return newMatches.stream().sorted((l, r) -> r.createTs.compareTo(l.createTs))
                .map(item -> {
                    StringBuilder sb = new StringBuilder();
                    sb.append("{\"image\":\"");
                    sb.append(item.imagePath);
                    sb.append("\",\"deviceId\":\"");
                    sb.append(item.getInfra().getDeviceId());
                    sb.append("\",\"captureTime\":\"");
                    sb.append(Utils.dateFormat.get().format(item.createTs));
                    sb.append("\"}");
                    return sb.toString();
                })
                .collect(Collectors.joining(",", "[", "]"));
    }

    public static StrangerFaceFeature builtStrangerFaceFeature(StrangerFaceObject object) {
        StrangerFaceFeature feature = new StrangerFaceFeature();
        feature.avatarImageUrl = object.imagePath;
        feature.imageFeature = FaissSeeker.featureToString(object.feature);
        feature.createTs = object.createTs;
        feature.groupKey = object.getInfra().getDeviceTag();
        feature.deviceId = object.getInfra().getDeviceId();
        feature.attribute = object.age + "," + object.sex;

        return feature;
    }

}
