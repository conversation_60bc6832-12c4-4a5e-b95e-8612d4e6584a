package com.sensetime.intersense.cognitivesvc.seekerface.controller;

import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.PasserFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonFaceFeatureRepository;


import io.swagger.v3.oas.annotations.Operation;

import java.sql.Timestamp;
import java.util.List;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController("faceSeekerTestProvider")
@RequestMapping(value = "/test", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "TestProvider",description = "Test")
public class TestProvider extends BaseProvider {
	
	@Autowired
	private PersonFaceFeatureRepository personMapper;
	
	@Autowired
	private PasserFaceFeatureRepository passerMapper;
	
	@Operation(summary = "save_passer_to_person",method = "GET")
	@RequestMapping(value = "/save_passer_to_person", method = RequestMethod.GET)
	public String save_passer_to_person(){
		List<PasserFaceFeature> passers = passerMapper.findAll();
		for(PasserFaceFeature passer : passers) {
			PersonFaceFeature person = new PersonFaceFeature();
			person.setAvatarImageUrl(passer.getAvatarImageUrl());
			person.setImageFeature(passer.getImageFeature());
			person.setModelVersion("COG");
			person.setPersonUuid(passer.getPersonUuid());
			person.setPersonCnName("COG");
			person.setSts(0);
			person.setCreateUser("COG");
			person.setCreateTs(new Timestamp(System.currentTimeMillis()));
			personMapper.saveAndFlush(person);
		}
		
		return "OK";
	}
}
