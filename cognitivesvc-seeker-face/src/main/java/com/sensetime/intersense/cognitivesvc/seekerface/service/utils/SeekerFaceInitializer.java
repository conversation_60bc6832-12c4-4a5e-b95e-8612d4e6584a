package com.sensetime.intersense.cognitivesvc.seekerface.service.utils;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sun.jna.Memory;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SeekerFaceInitializer {
	
	private static boolean initialized = false;
	
	//启动时塞入当前模型的人脸特征的维度
	public static int dims = Integer.parseInt(Utils.getProperty("seeker.face.dims", "256"));
	
	public static float[] kSrcPoint;
	public static float[] kDstPoint;
	
	/**
	 * 初始化，需要调用，某些参数可以为null，需要什么功能，就传什么model进去
	 */
	public synchronized static void initialize(Environment env) {
		if(initialized)
			return ;
		
		Initializer.initialize(env);
		
		String k_src = Utils.getProperty("K_SRC_POINT");
		String k_dst = Utils.getProperty("K_DST_POINT");
		
		if(StringUtils.isNotBlank(k_src) && StringUtils.isNotBlank(k_dst)){
			String srcs[] = k_src.split(",");
			String dsts[] = k_dst.split(",");
			
			if(srcs.length != dsts.length) 
				throw new RuntimeException("K_SRC_POINT and K_DST_POINT length not match.");
			
			kSrcPoint = new float[srcs.length];
			kDstPoint = new float[dsts.length];
			
			for(int index = 0; index < srcs.length; index ++) {
				kSrcPoint[index] = Float.parseFloat(srcs[index]);
				kDstPoint[index] = Float.parseFloat(dsts[index]);
			}

			log.info("****************************************** face score normalize using : [env] ******************************************************");
		}
		
//		if(kSrcPoint == null || kDstPoint == null) {
//			try {
//				loadThres();
//			}catch(Exception e) {
//				log.info("reading score_norm_param.json error, using default.");
//			}
//		}

		String featureModule = Initializer.modelPathMap.get("feature_module");

		if (kSrcPoint == null || kDstPoint == null) {

			//https://ones.ainewera.com/wiki/#/team/JNwe8qUX/space/GebSt74Y/page/5VHE55mg

			//2.54
			kSrcPoint = new float[]{-1.0f, 0.1f, 0.320f, 0.352f, 0.388f, 0.420f, 0.470f, 1.0f};
			kDstPoint = new float[]{0.001f, 0.1f, 0.6f, 0.7f, 0.8f, 0.85f, 0.95f, 0.9999f};

			//2.53
			if (StringUtils.isNotBlank(featureModule) && featureModule.indexOf("2.53") > 0) {
				kSrcPoint = new float[]{-1.0f, 0.1f, 0.314f, 0.346f, 0.382f, 0.415f, 0.463f, 1.0f};
				kDstPoint = new float[]{0.001f, 0.1f, 0.6f, 0.7f, 0.8f, 0.85f, 0.95f, 0.9999f};
			}

			//2.52
			else if (StringUtils.isNotBlank(featureModule) && featureModule.indexOf("2.52") > 0) {
				kSrcPoint = new float[]{-1.0f, 0.0f, 0.3f, 0.316f, 0.347f, 0.383f, 0.418f, 0.468f, 1.0f};
				kDstPoint = new float[]{0.0f, 0.0f, 0.1f, 0.6f, 0.7f, 0.8f, 0.85f, 0.95f, 1.0f};
			}

			log.info("****************************************** face score normalize using : [default] ******************************************************");
		}

		log.info("****************************************** src : {} *********************************************",JSON.toJSONString(kSrcPoint));
		log.info("****************************************** dst : {} *********************************************",JSON.toJSONString(kDstPoint));
		log.info("****************************************** feature model is: {} *******", featureModule);
    	initialized = true;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void loadThres() {
		PointerByReference feature_model = new PointerByReference();
		KestrelApi.kestrel_model_load("/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module"), feature_model);
		LongByReference file_size = new LongByReference();
		file_size.setValue(KestrelApi.kestrel_model_file_size(feature_model.getValue(), "score_norm_param.json"));
		Memory buf = new Memory(file_size.getValue());
		KestrelApi.kestrel_model_get_file(feature_model.getValue(), "score_norm_param.json", buf, file_size);
		KestrelApi.kestrel_model_unload(feature_model);
		
		String json = buf.getString(0).substring(0, (int)file_size.getValue());
		log.info("reading score_norm_param.json : \n" + json + "\n");
		
		Map<String, JSONArray> points = (Map)JSON.parseObject(json);
		Float[] src_points = points.get("src_points").stream().map(Number.class::cast).map(Number::floatValue).toArray(Float[]::new);
		Float[] dst_points = points.get("dst_points").stream().map(Number.class::cast).map(Number::floatValue).toArray(Float[]::new);
		if(dst_points.length != src_points.length)
			throw new RuntimeException("length not even.");
		
		kSrcPoint = new float[src_points.length];
		kDstPoint = new float[dst_points.length];
		
		for(int index = 0; index < src_points.length; index  ++) {
			kSrcPoint[index] = src_points[index];
			kDstPoint[index] = dst_points[index];
		}
	}
}
