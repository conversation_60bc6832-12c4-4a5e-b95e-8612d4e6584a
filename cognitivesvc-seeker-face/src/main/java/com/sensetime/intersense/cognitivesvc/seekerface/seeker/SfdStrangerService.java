package com.sensetime.intersense.cognitivesvc.seekerface.seeker;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.sfd.FaceRecognitionMultiDbResponse;
import com.sensetime.intersense.cognitivesvc.server.entities.sfd.FaceRecognitionResponse;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PasserFaceObject;
import com.sensetime.intersense.cognitivesvc.server.services.SfdBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SFD陌生人搜索服务
 * 实现陌生人1:N搜索
 */
@Slf4j
@Service
public class SfdStrangerService extends SfdBaseService<PasserParam, PasserFaceObject, List<Pair<PasserFaceObject, Float>>> {
    

    @Override
    protected float[] getFeature(PasserParam param) {
        return param.getFeature();
    }

    @Override
    protected String getFeatureType() {
        return "face";
    }

    @Override
    protected Integer getFeatureVersion() {
        return sfdProperties.getDb().getFeatureVersion();
    }

    @Override
    protected String getDbName() {
        return sfdProperties.getDb().getStrangerName();
    }

    @Override
    protected String getSfdSearchPath() {
        return sfdProperties.getFace1N();
    }

    @Override
    protected int getTopK(PasserParam param) {
        // 默认返回32个结果，如果设置了count则返回count个
        return Objects.requireNonNullElse(param.getCount(), 32);
    }

    @Override
    protected float getMinScore(PasserParam param) {
        // 使用参数中的阈值，如果没有设置则默认为0.6
        return Objects.requireNonNullElse(param.getThreshold(), 0.6f);
    }

    @Override
    protected List<Pair<PasserFaceObject, Float>> mergeResults(PasserParam param, List<String> groups) {
        Map<String, Pair<PasserFaceObject, Float>> resultMap = new HashMap<>();
        
        for (String group : groups) {
            List<Pair<PasserFaceObject, Float>> result = super.searchInDb(param, group);
            
            // 处理每个结果
            for (Pair<PasserFaceObject, Float> pair : result) {
                PasserFaceObject passer = pair.getLeft();
                Float score = pair.getRight();
                
                // 如果已存在相同的pid，比较分数，保留分数较高的
                if (resultMap.containsKey(passer.pid)) {
                    if (score > resultMap.get(passer.pid).getRight()) {
                        log.info("Found duplicate pid {} with higher score {} (old score {})", 
                                passer.pid, score, resultMap.get(passer.pid).getRight());
                        resultMap.put(passer.pid, pair);
                    } else {
                        log.info("Found duplicate pid {} with lower score {} (keeping old score {})", 
                                passer.pid, score, resultMap.get(passer.pid).getRight());
                    }
                } else {
                    resultMap.put(passer.pid, pair);
                }
            }
        }
        
        // 将结果转换为列表并按分数排序
        List<Pair<PasserFaceObject, Float>> mergedResults = resultMap.values().stream()
                .sorted((a, b) -> Float.compare(b.getRight(), a.getRight()))
                .collect(Collectors.toList());
        
        // 如果设置了数量限制，截取指定数量
        if (param.getCount() != null && mergedResults.size() > param.getCount()) {
            mergedResults = mergedResults.subList(0, param.getCount());
        }
        
        return mergedResults;
    }
    
    @Override
    protected List<Pair<PasserFaceObject, Float>> processResponse(FaceRecognitionResponse response, PasserParam param) {
        List<Pair<PasserFaceObject, Float>> result = new ArrayList<>();

        if (response.getFeature_results() != null &&
                response.getFeature_results().get(0) != null &&
                CollectionUtils.isNotEmpty(response.getFeature_results().get(0).getResults())) {

            List<FaceRecognitionResponse.BaselineData> baselineDataList = response.getFeature_results().get(0).getResults()
                    .stream()
                    .sorted(Comparator.comparing(FaceRecognitionResponse.BaselineData::getScore).reversed())
                    .collect(Collectors.toList());

            for (FaceRecognitionResponse.BaselineData baselineData : baselineDataList) {
                if (baselineData.getScore() < getMinScore(param)) {
                    continue;
                }

                PasserFaceObject passerFaceObject = new PasserFaceObject();
                passerFaceObject.pid = baselineData.getItem().getKey();

                // 从info中提取附加信息
                if (baselineData.getItem().getExtra_info() != null) {
                    try {
                        JSONObject info = JSONObject.parseObject(baselineData.getItem().getExtra_info());
                        if (info.containsKey("avatar")) {
                            passerFaceObject.avatar = info.getString("avatar");
                        }
                        if (info.containsKey("groupId")) {
                            passerFaceObject.groupId = info.getString("groupId");
                        }
                    } catch (Exception e) {
                        log.warn("Failed to parse info: {}", e.getMessage());
                    }
                }

                result.add(new ImmutablePair<>(passerFaceObject,  ((Number)baselineData.getScore()).floatValue()));
            }
        }

        // 如果设置了count，限制返回数量
        if (param.getCount() != null && result.size() > param.getCount()) {
            result = result.subList(0, param.getCount());
        }

        return result;
    }

    @Override
    protected List<Pair<PasserFaceObject, Float>> getEmptyResult() {
        return new ArrayList<>();
    }
    
    /**
     * 处理多数据库搜索的结果
     * 与单数据库搜索保持一致的返回格式
     */
    @Override
    protected List<Pair<PasserFaceObject, Float>> processMultiDbResponse(FaceRecognitionMultiDbResponse response, PasserParam param) {
        List<Pair<PasserFaceObject, Float>> result = new ArrayList<>();
        Map<String, Pair<PasserFaceObject, Float>> resultMap = new HashMap<>();
        
        if (response.getSearch_results() != null) {
            // 遍历每个数据库的结果
            for (FaceRecognitionMultiDbResponse.SearchResult searchResult : response.getSearch_results()) {
                // 处理每个数据库内的搜索结果
                if (searchResult.getSimilar_results() != null) {
                    for (FaceRecognitionMultiDbResponse.SimilarResult similarResult : searchResult.getSimilar_results()) {
                        if (similarResult.getScore() < getMinScore(param)) {
                            continue;
                        }
                        
                        FaceRecognitionMultiDbResponse.Item item = similarResult.getItem();
                        float score = similarResult.getScore().floatValue();
                        
                        PasserFaceObject passerFaceObject = new PasserFaceObject();
                        passerFaceObject.pid = item.getKey(); // 使用key作为pid
                        
                        // 从extra_info中提取附加信息
                        if (item.getExtra_info() != null) {
                            try {
                                JSONObject info = JSONObject.parseObject(item.getExtra_info());
                                if (info.containsKey("avatar")) {
                                    passerFaceObject.avatar = info.getString("avatar");
                                }
                                if (info.containsKey("groupId")) {
                                    passerFaceObject.groupId = info.getString("groupId");
                                }
                            } catch (Exception e) {
                                log.warn("Failed to parse info: {}", e.getMessage());
                            }
                        }
                        
                        // 如果存在相同pid，保留得分高的
                        if (resultMap.containsKey(passerFaceObject.pid)) {
                            if (score > resultMap.get(passerFaceObject.pid).getRight()) {
                                log.info("Found duplicate pid {} with higher score {} (old score {})", 
                                       passerFaceObject.pid, score, resultMap.get(passerFaceObject.pid).getRight());
                                resultMap.put(passerFaceObject.pid, new ImmutablePair<>(passerFaceObject, score));
                            } else {
                                log.info("Found duplicate pid {} with lower score {} (keeping old score {})", 
                                       passerFaceObject.pid, score, resultMap.get(passerFaceObject.pid).getRight());
                            }
                        } else {
                            resultMap.put(passerFaceObject.pid, new ImmutablePair<>(passerFaceObject, score));
                        }
                    }
                }
            }
            
            // 将结果转换为列表并按分数排序
            result = resultMap.values().stream()
                    .sorted((a, b) -> Float.compare(b.getRight(), a.getRight()))
                    .collect(Collectors.toList());
            
            // 如果设置了数量限制，截取指定数量
            if (param.getCount() != null && result.size() > param.getCount()) {
                result = result.subList(0, param.getCount());
            }
        }
        
        return result;
    }
} 