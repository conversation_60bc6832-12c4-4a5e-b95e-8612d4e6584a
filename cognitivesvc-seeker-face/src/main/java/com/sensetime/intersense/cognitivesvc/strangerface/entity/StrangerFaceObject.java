package com.sensetime.intersense.cognitivesvc.strangerface.entity;

import java.util.Date;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class StrangerFaceObject {
	public int     id;
	public String  personId;
	public float[] feature;
	public float   quality;
	public String  imagePath;
	public int     trackId;
	public VideoStreamInfra infra;

	@Builder.Default
	public Date createTs = new Date();
	
	@Builder.Default
	public String sex = "-1";

	@Builder.Default
	public String age = "-1";
}
