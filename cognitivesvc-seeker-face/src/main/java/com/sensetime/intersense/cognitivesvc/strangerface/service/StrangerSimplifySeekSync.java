package com.sensetime.intersense.cognitivesvc.strangerface.service;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.mapper.StrangerFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentSkipListMap;

@Component
public class StrangerSimplifySeekSync {

    @Autowired
    private StrangerFaceFeatureRepository strangerMapper;

    @Value("${intersense.stranger.simplify-seeker.rolling-window-count:30}")
    private int rollingWindowCount;

    @Value("${intersense.stranger.simplify-seeker.rolling-window-length:60}")
    private long rollingWindowLength;       // 单位秒，可取范围： 10、15、20、30、60

    @Value("${intersense.stranger.simplify-seeker.tsgap-condition:3000}")
    private long tsGapCondition;            // 同一时间窗口，两张相似照片小于该时间间隔认为是同一次抓拍

    @Value("${intersense.stranger.simplify-seeker.period-showtimes-condition:2}")
    private long periodShowTimesCondition;

    @Value("${intersense.stranger.simplify-seeker.device-showtimes-condition:1}")
    private long deviceShowTimesCondition;

    @Async("cogThreadPool")
    public void asyncMethod(ConcurrentSkipListMap<Long, Queue<StrangerFaceObject>> rollingWindowMap) {
        // init rolling window map
        Calendar c = Calendar.getInstance();
        Date endTime = c.getTime();
        c.add(Calendar.MINUTE, (int) (rollingWindowCount * rollingWindowLength * -1));
        Date startTime = c.getTime();

        strangerMapper.findByCreateTsBetweenOrderByCreateTsDesc(startTime, endTime)
                .parallelStream()
                .map(item -> {
                    StrangerFaceObject.StrangerFaceObjectBuilder builder = StrangerFaceObject.builder()
                            .id(item.getId())
                            .imagePath(item.getAvatarImageUrl())
                            .createTs(item.getCreateTs())
                            .feature(FaissSeeker.stringToFeature(item.getImageFeature()))
                            .infra(VideoStreamInfra.builder().deviceId(item.getDeviceId()).deviceTag(item.getGroupKey()).build());

                    if(StringUtils.isNotBlank(item.getAttribute())) {
                        String[] attributes = item.getAttribute().split(",");
                        builder.age(attributes[0]).sex(attributes[1]);
                    }

                    return builder.build();
                })
                .forEach(s -> {
                    Long key = this.getRollingWindowMapKey(s.createTs);
                    Queue<StrangerFaceObject> rollingWindow = rollingWindowMap.get(key);
                    if (rollingWindow == null) {
                        rollingWindow = new ConcurrentLinkedQueue<>();
                        rollingWindowMap.put(key, rollingWindow);
                    }
                    rollingWindow.add(s);
                });

        while (rollingWindowMap.size() > rollingWindowCount)
            rollingWindowMap.remove(rollingWindowMap.firstKey());
    }
    private Long getRollingWindowMapKey(Date time) {
        Calendar c = Calendar.getInstance();
        c.setTime(time);
        int second = c.get(Calendar.SECOND);
        c.clear(Calendar.SECOND);

        Long key = c.getTimeInMillis() / 1000 + (second / rollingWindowLength) * rollingWindowLength;
        return key;
    }
}
