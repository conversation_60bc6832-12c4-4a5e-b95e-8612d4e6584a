package com.sensetime.intersense.cognitivesvc.strangerface;

import com.sensetime.intersense.cognitivesvc.strangerface.service.*;

import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.PostConstruct;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@Configuration("strangerFaceConfiguation")
@ComponentScan
@ConditionalOnExpression("${stranger.enabled:true} && ${stranger.face.enabled:true} && ${seeker.enabled:true} && ${seeker.face.enabled:true}")
@Slf4j
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class CognitivesvcConfiguation{

	@PostConstruct
	public void initialize(){
		log.warn("\n");
		log.warn("**************************************");
		log.warn("**********init stranger***************");
		log.warn("**stranger.enabled=false to disable***");
		log.warn("stranger.face.enabled=false to disable");
		log.warn("***seeker.enabled=false to disable****");
		log.warn("*seeker.face.enabled=false to disable*");
		log.warn("**************************************");
		log.warn("\n");
	}
	
	@Bean
	public StrangerFaceSeekerFacade facade() {
		return new StrangerFaceSeekerFacade();
	}

	@Bean
	public AbstractFaceStrangerSeeker just() {
		return new StrangerFaceJustSeeker();
	}
	
	@Bean
	public AbstractFaceStrangerSeeker simplify() {
		return new StrangerFaceSimplifySeeker();
	}
}
