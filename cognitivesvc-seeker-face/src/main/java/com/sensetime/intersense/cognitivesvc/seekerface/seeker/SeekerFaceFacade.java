package com.sensetime.intersense.cognitivesvc.seekerface.seeker;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveParam;
import com.sensetime.intersense.cognitivesvc.server.mapper.CognitiveParamRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.sensetime.intersense.cognitivesvc.server.entities.PasserFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.DP;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.SeekerSpliter;
import com.sensetime.intersense.cognitivesvc.server.utils.SeekerSpliter.SplitEntity;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * 选择性不在本地比对特征 远程的比对服务进行比对 如果没有配置则在本地对比 认为远程的服务拥有所有人员的特征 单节点就能查询到所有
 */
@Slf4j
@EnableScheduling
@Component
public class SeekerFaceFacade {
    
    @Value("${spring.application.name}")
    private String appName;
    
    @Autowired
    private SeekerSpliter seekerSpliter;
    
    @Autowired
    @LoadBalanced
    private RestTemplate restTemplate;
    
    @Autowired
    private Broadcaster broadcastService;
    
    @Autowired
    private PersonFaceSeeker personFaceSeeker;
    
    @Autowired
    private PasserFaceSeeker passerFaceSeeker;
    
    @Autowired
    private CognitiveParamRepository mapper;


    @Autowired
    private SfdPersonService sfdPersonService;

    @Value("${intersense.faissSeeker.reindex.timeout: 6}")
    private Integer reindexTimeout;
    @Value("${intersense.faissSeeker.reindex.forceRebuildHours: 12}")
    private Integer forceRebuildHours;
    @Autowired
    private SfdStrangerService sfdStrangerService;

    public List<Pair<PersonFaceObject, Float>> findPerson(PersonParam param) {

        if(Utils.instance.featureSearchSfd){
            // 调用SFD服务进行人脸识别
            long startTime = System.currentTimeMillis();
            List<Pair<PersonFaceObject, Float>> result;

            try {
                result = sfdPersonService.searchMultiDb(param, param.getPersonGroups(), param.getCount(), param.getThreshold());

                if (log.isDebugEnabled()) {
                    SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
                    filter.getExcludes().add("feature");
                    log.debug(">>> [findPerson] SFD search, featureMd5={}, param={}, result={}, cost={}ms",
                            Utils.computeMD5(param.getFeature()),
                            JSON.toJSONString(param, filter),
                            JSON.toJSONString(result, filter),
                            System.currentTimeMillis() - startTime);
                }
            } catch (Exception e) {
                log.error("Error calling SFD service: {}", e.getMessage(), e);
                result = new ArrayList<>();
            }

            return result;
        }
        
        SplitEntity splitEntity = seekerSpliter.getSplitEntity();
        if (splitEntity.getTotalSplitNum() <= 0 || param.getFindLocal())
            return findPersonLocal(param);
        
        SeekResponse finalResponse = mergeSplitResult(broadcastService.postForObject(seekerSpliter.getInstanceTargets(), "/cognitive/hidden/face/local", new SeekRequest(param), SeekResponse.class));
        
        List<Pair<PersonFaceObject, Float>> result = Stream.iterate(0, i -> i + 1)
                .limit(finalResponse.personObjs.size())
                .map(index -> new MutablePair<PersonFaceObject, Float>(finalResponse.personObjs.get(index), finalResponse.personScores.get(index)))
                .collect(Collectors.toList());
        
        if (splitEntity.getTotalSplitNum() > 0)
            Collections.sort(result, (l, r) -> Float.compare(r.getRight(), l.getRight()));
        
        if (param.count == null)
            param.count = 1;
        
        if (result.size() > param.count)
            result = result.subList(0, param.count);
        
        if (log.isDebugEnabled()) {
            SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
            filter.getExcludes().add("feature");
            log.debug(">>> [findPerson] featureMd5={}, param={}, result={}", Utils.computeMD5(param.feature), JSON.toJSONString(param, filter), JSON.toJSONString(result, filter));
        }
        return result;
    }
    
    public List<Pair<PasserFaceObject, Float>> findPasser(PasserParam param) {

        if(Utils.instance.featureSearchSfd){
            // 调用SFD服务进行陌生人识别
            long startTime = System.currentTimeMillis();
            List<Pair<PasserFaceObject, Float>> result;

            try {
                // 使用新的SFD统一服务进行搜索
                result = sfdStrangerService.search(param);

                if (log.isDebugEnabled()) {
                    SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
                    filter.getExcludes().add("feature");
                    log.debug(">>> [findPasser] SFD search, featureMd5={}, param={}, result={}, cost={}ms",
                            Utils.computeMD5(param.getFeature()),
                            JSON.toJSONString(param, filter),
                            JSON.toJSONString(result, filter),
                            System.currentTimeMillis() - startTime);
                }
            } catch (Exception e) {
                log.error("Error calling SFD service for passer: {}", e.getMessage(), e);
                result = new ArrayList<>();
            }

            return result;
        }
        SplitEntity splitEntity = seekerSpliter.getSplitEntity();
        if (splitEntity.getTotalSplitNum() <= 0 || param.getFindLocal())
            return findPasserLocal(param);
        
        SeekResponse finalResponse = mergeSplitResult(broadcastService.postForObject(seekerSpliter.getInstanceTargets(), "/cognitive/hidden/face/local", new SeekRequest(param), SeekResponse.class));
        
        List<Pair<PasserFaceObject, Float>> result = Stream.iterate(0, i -> i + 1)
                .limit(finalResponse.passerObjs.size())
                .map(index -> new MutablePair<PasserFaceObject, Float>(finalResponse.passerObjs.get(index), finalResponse.passerScores.get(index)))
                .collect(Collectors.toList());
        
        if (splitEntity.getTotalSplitNum() > 0)
            Collections.sort(result, (l, r) -> Float.compare(r.getRight(), l.getRight()));
        
        if (param.count == null)
            param.count = 1;
        
        if (result.size() > param.count)
            return result.subList(0, param.count);
        else
            return result;
    }
    
    public void reFetchData() {
        personFaceSeeker.reFetchData();
        passerFaceSeeker.reFetchData();
    }
    
    /**
     * 检查是否可以获取锁
     * 每隔随机时间查询一次，如果连续3次都是空闲状态，则认为可以获取锁
     * @param reindexIdentifier 锁标识
     * @return 是否可以获取锁
     */
    private boolean canAcquireLock(CognitiveParam reindexIdentifier) {
        final int query_count_limit = 5;
        final int REQUIRED_CONSECUTIVE_EMPTY = 3; // 需要连续空闲次数
        int consecutiveEmptyCount = 0;  // 连续空闲次数计数
        int checkCount = 0;  // 当前检查次数

        while (consecutiveEmptyCount < REQUIRED_CONSECUTIVE_EMPTY) {
            try {
                checkCount++;
                reindexIdentifier = mapper.findById("reindexIdentifier").get();
                log.debug(">>> [reindexPerson] Lock check count: {}: status={}",
                    checkCount,
                    reindexIdentifier.getSValue());
                
                if (reindexIdentifier == null || reindexIdentifier.getSValue().isEmpty()) {
                    consecutiveEmptyCount++;
                } else {
                    // 如果不是空闲状态，重置连续计数
                    consecutiveEmptyCount = 0;
                }
                
                // 如果还没有达到所需的连续次数，继续等待
                if (consecutiveEmptyCount < REQUIRED_CONSECUTIVE_EMPTY) {
                    Thread.sleep((int)(Math.random() * 8 + 3) * 500);  // 等待1500-5500毫秒的随机时间
                }
                
                // 如果已经检查了最大次数了，本轮检查结束
                if(checkCount > query_count_limit){
                    return false;
                }
            } catch (InterruptedException e) {
                log.error(">>> [reindexPerson] Interrupted during lock check", e);
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        return true;
    }

    /**
     * 重建人脸特征索引
     * 该方法实现了一个分布式锁机制，确保在集群环境下只有一个节点执行重建索引操作
     * 执行步骤：
     * 1. 检查上次重建时间，如果超过12小时则强制重建
     * 2. 获取数据库锁并等待其他节点完成
     * 3. 获得锁后更新当前节点信息
     * 4. 执行索引重建
     * 5. 完成后更新重建时间
     */
    public void reindexPersonWithOnebyOne() {
        log.info(">>> [reindexPerson] Start rebuilding face feature index");
        
        // 检查上次重建完成时间
        final long FORCE_REBUILD_HOURS = forceRebuildHours;
        CognitiveParam reindexFinishTime = mapper.findById("reindexFinishTime").get();
        if (reindexFinishTime != null && !reindexFinishTime.getSValue().isEmpty()) {
            long lastFinishTime = Long.parseLong(reindexFinishTime.getSValue());
            long hoursSinceLastRebuild = (System.currentTimeMillis() - lastFinishTime) / (60 * 60 * 1000);
            
            if (hoursSinceLastRebuild > FORCE_REBUILD_HOURS) {
                log.warn(">>> [reindexPerson] Last rebuild was {} hours ago, forcing new rebuild", hoursSinceLastRebuild);
                // 强制清理锁
                CognitiveParam reindexIdentifier = mapper.findById("reindexIdentifier").get();
                reindexIdentifier.setSValue("");
                mapper.saveAndFlush(reindexIdentifier);
            }
        }

        // 查数据库的字段锁
        CognitiveParam reindexIdentifier = mapper.findById("reindexIdentifier").get();

        // 等待其他节点完成重建，设置6小时超时时间
        final long TIMEOUT_HOURS = reindexTimeout;
        final long startTime = System.currentTimeMillis();
        final long timeoutMillis = TIMEOUT_HOURS * 60 * 60 * 1000; // 转换为毫秒

        while(true) {
            try {
                // 检查是否超时
                if (System.currentTimeMillis() - startTime > timeoutMillis) {
                    log.error(">>> [reindexPerson] Timeout after {} hours waiting for lock, task cancelled", TIMEOUT_HOURS);
                    return;
                }

                Thread.sleep(5000);
                
                // 使用新的锁检查逻辑
                if (canAcquireLock(reindexIdentifier)) {
                    break;
                }
                
                log.debug(">>> [reindexPerson] Still waiting for lock, time elapsed: {} seconds",
                    (System.currentTimeMillis() - startTime) / ( 1000));
                
            } catch (InterruptedException e) {
                log.error(">>> [reindexPerson] Interrupted while waiting for lock", e);
                Thread.currentThread().interrupt();
                return;
            }
        }

        // 获取锁并开始执行重建索引
        String containerHostname = System.getenv("HOSTNAME");
        String hostIdentifier = containerHostname != null ? containerHostname : System.currentTimeMillis() + "";
        reindexIdentifier.setSValue(hostIdentifier);
        mapper.saveAndFlush(reindexIdentifier);
        log.info(">>> [reindexPerson] Acquired lock, current node identifier: {}", hostIdentifier);

        try {
            log.info(">>> [reindexPerson] Starting to clean delete queue");
            personFaceSeeker.reFetchDataDelQueue();
            log.info(">>> [reindexPerson] Starting to refresh feature data");
            personFaceSeeker.reFetchData();
            log.info(">>> [reindexPerson] Starting to rebuild index");
            personFaceSeeker.fetchData();
            
            // 更新完成时间
            reindexIdentifier.setSValue("");
            mapper.saveAndFlush(reindexIdentifier);
            reindexFinishTime = mapper.findById("reindexFinishTime").get();
            reindexFinishTime.setSValue(System.currentTimeMillis()+"");
            mapper.saveAndFlush(reindexFinishTime);
            log.info(">>> [reindexPerson] Index rebuild completed, timestamp updated: {}", reindexFinishTime.getSValue());
        } catch (Exception e) {
            log.error(">>> [reindexPerson] Exception occurred during index rebuild", e);
            // 发生异常时也要释放锁
            reindexIdentifier.setSValue("");
            mapper.saveAndFlush(reindexIdentifier);
            throw e;
        }
    }
    
    public List<Pair<PersonFaceObject, Float>> findPersonLocal(PersonParam param) {
        int count = Objects.requireNonNullElse(param.count, 1);
        
        if (ArrayUtils.isNotEmpty(param.personGroups) || ArrayUtils.isNotEmpty(param.tags) || ArrayUtils.isNotEmpty(param.deptIds))
            param.count = Math.max(param.count, 32);
        
        return personFaceSeeker.find(param).stream().limit(count).collect(Collectors.toList());
    }
    
    public List<Pair<PasserFaceObject, Float>> findPasserLocal(PasserParam param) {

        int count = Objects.requireNonNullElse(param.count, 1);

        if (ArrayUtils.isNotEmpty(param.deptIds))
            param.count = Math.max(param.count, 32);
        return passerFaceSeeker.find(param).stream().limit(count).collect(Collectors.toList());
    }
    
    public void deletePersons(List<Integer> ids, boolean refetch) {
        personFaceSeeker.deleteTargets(ids, refetch);
    }
    
    public void deletePassers(List<Integer> ids, boolean refetch) {
        passerFaceSeeker.deleteTargets(ids, refetch);
    }
    
    private static SeekResponse mergeSplitResult(List<SeekResponse> result) {
        SeekResponse response = new SeekResponse();
        
        for (SeekResponse item : result) {
            response.personScores.addAll(item.personScores);
            response.personObjs.addAll(item.personObjs);
            response.passerScores.addAll(item.passerScores);
            response.passerObjs.addAll(item.passerObjs);
        }
        
        return response;
    }
    
    @Schema(title = "用特征搜索库", description = "用特征搜索库")
    public static class SeekRequest {
        @Schema(description = "搜索人员库")
        public PersonParam personParam;
        
        @Schema(description = "搜索归档库")
        public PasserParam passerParam;
        
        public SeekRequest() {
        }
        
        public SeekRequest(PersonParam personParam, PasserParam passerParam) {
            this.personParam = personParam;
            this.passerParam = passerParam;
        }
        
        public SeekRequest(PersonParam personParam) {
            this.personParam = personParam;
        }
        
        public SeekRequest(PasserParam passerParam) {
            this.passerParam = passerParam;
        }
    }
    
    @Schema(title = "搜索结果List", description = "搜索结果List")
    public static class SeekResponse {
        @Schema(description = "person列表")
        public List<PersonFaceObject> personObjs = new ArrayList<>();
        @Schema(description = "person的比对分")
        public List<Float> personScores = new ArrayList<>();
        
        @Schema(description = "passer列表")
        public List<PasserFaceObject> passerObjs = new ArrayList<>();
        @Schema(description = "passer的比对分")
        public List<Float> passerScores = new ArrayList<>();
        
        public void addPerson(PersonFaceObject obj, float score) {
            PersonFaceObject clone = obj.clone();
            clone.feature = null;
            
            personObjs.add(clone);
            personScores.add(score);
        }
        
        public void addPasser(PasserFaceObject obj, float score) {
            PasserFaceObject clone = obj.clone();
            clone.feature = null;
            
            passerObjs.add(clone);
            passerScores.add(score);
        }
    }
    
    @Getter
    @Setter
    public static class PersonFaceObject implements Cloneable, DP {
        public Integer id;
        public String pid;
        public float[] feature;
        public String tag;
        public String cnName;
        public String enName;
        public String avatar;
        public String privilege;
        
        @Override
        public PersonFaceObject clone() {
            try {
                return getClass().cast(super.clone());
            } catch (CloneNotSupportedException e) {
                throw new RuntimeException(e);
            }
        }
        
        public static PersonFaceObject convert(PersonFaceFeature pf) {
            try {
                PersonFaceObject obj = new PersonFaceObject();
                obj.feature = FaissSeeker.stringToFeature(pf.getImageFeature());
                obj.id = pf.getId();
                obj.pid = pf.getPersonUuid();
                obj.tag = pf.getTag();
                obj.avatar = pf.getAvatarImageUrl();
                obj.cnName = pf.getPersonCnName();
                obj.enName = pf.getPersonEnName();
                obj.privilege = pf.getPrivilege();
                return obj;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        
        private transient String[] privilege_0;
        
        public String[] privilege() {
            if (privilege_0 != null)
                return privilege_0;
            
            privilege_0 = StringUtils.split(privilege, ",");
            
            return privilege_0;
        }
    }
    
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class PasserFaceObject implements Cloneable, DP {
        public Integer id;
        public String pid;
        public float[] feature;
        public String groupId;
        public String avatar;
        public String privilege;
        
        public static PasserFaceObject convert(PasserFaceFeature pf) {
            try {
                PasserFaceObject obj = new PasserFaceObject();
                obj.id = pf.getId();
                obj.feature = FaissSeeker.stringToFeature(pf.getImageFeature());
                obj.pid = pf.getPersonUuid();
                obj.groupId = pf.getGroupId();
                obj.avatar = pf.getAvatarImageUrl();
                obj.privilege = pf.getPrivilege();
                return obj;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        
        @Override
        public PasserFaceObject clone() {
            try {
                return getClass().cast(super.clone());
            } catch (CloneNotSupportedException e) {
                throw new RuntimeException(e);
            }
        }
        
        private transient String[] privilege_0;
        
        public String[] privilege() {
            if (privilege_0 != null)
                return privilege_0;
            
            privilege_0 = StringUtils.split(privilege, ",");
            
            return privilege_0;
        }
    }
}
