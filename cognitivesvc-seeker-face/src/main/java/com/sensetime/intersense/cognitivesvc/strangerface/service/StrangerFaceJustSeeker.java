package com.sensetime.intersense.cognitivesvc.strangerface.service;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.sensetime.intersense.cognitivesvc.server.entities.StrangerFaceFeature;
import com.sensetime.storage.autoconfigure.StorageProperties;
import com.sensetime.storage.entity.ImageInfo;
import com.sensetime.storage.factory.FileStorageType;
import com.sensetime.storage.service.FileAccessor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;

import com.sensetime.intersense.cognitivesvc.server.entities.PasserFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.feign.StrangerFeign;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;
import com.sensetime.lib.clientlib.response.BaseRes;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**发现了陌生人 就立即入库。。。。。
 */
@Slf4j
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class StrangerFaceJustSeeker implements AbstractFaceStrangerSeeker{

	@Autowired
	private PasserFaceFeatureRepository passerMapper;
	
    @Autowired
	private ApplicationContext context;

	@Value("${preMakeDirs}")
	private String preMakeDirs;

	@Autowired
	FileAccessor fileAccessor;

	@Autowired
	StorageProperties storageProperties;

	@Override
	public String strangerIsHere(StrangerFaceObject obj) {
		String finalPath = obj.imagePath;
		// todo copy
		if(!obj.imagePath.contains("passer_face")){
            try {
//                byte[] data = fileAccessor.readImage(obj.imagePath);
//				ImageInfo imageInfo = ImageInfo
//						.builder()
//						.imagePath("passer_face")
//						.data(data)
//						.build();
//
//				finalPath = fileAccessor.writeImage(imageInfo);
				finalPath = fileAccessor.cpImage(obj.imagePath,"passer_face" );
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
		StrangerFeign.CreateStrangerReq req = new StrangerFeign.CreateStrangerReq();
		req.setImageURI(finalPath);
		// desc里面不塞/dev/shm的图片
		req.setDesc(toDesc(List.of(new MutablePair<StrangerFaceObject, Float>(obj, 1.0f))));
		req.setPersonType("passer");
		req.setPersonTag(obj.getInfra().getDeviceTag());
		req.setPersonAge(obj.age);
		req.setPersonSex(obj.sex);
		req.setDeviceSource(obj.getInfra().getDeviceId());
		String trackId = obj.getTrackId() + "_" + Utils.keyToContextId(obj.getInfra().getDeviceId()) + "_" + Utils.instance.getSeed().substring(Utils.instance.getSeed().length() - 4, Utils.instance.getSeed().length());
		req.setRemark("{\"label\":37017, \"trackId\":\"" + trackId + "\", \"privilege\":\"" + obj.getInfra().getPrivilege() + "\"}");	
		
		PasserFaceFeature item = new PasserFaceFeature();
		item.setAvatarImageUrl(finalPath);
		item.setImageFeature(FaissSeeker.featureToString(obj.feature));
		item.setGroupId(Objects.requireNonNullElse(obj.getInfra().getDeviceTag(), ""));
		item.setSts(0);
		item.setTrackId(trackId);
		item.setCreateTs(new Date());
		item.setPrivilege(obj.getInfra().getPrivilege());
		item.setImageQuality(obj.getQuality());
		
		try {
			BaseRes<StrangerFeign.CreateRes> res = context.getBean(StrangerFeign.class).createStranger(req);
			if(res.isSuccess() && res.getData() != null) {
				item.setPersonUuid(res.getData().getUuid().toString());
				item.setPrivilege(Objects.requireNonNullElse(res.getData().getPrivilege(), item.getPrivilege()));
			}
		}catch(Exception e) {
			log.warn(e.getLocalizedMessage());
		}
		
		if(StringUtils.isEmpty(item.getPersonUuid())) {
			item.setPersonUuid("unknown-" + UUID.randomUUID().toString().substring(1, 6));
			item.setPrivilege(item.getPrivilege());
		}
		
		passerMapper.saveAndFlush(item);
		
		return item.getPersonUuid();
	}

	@Override
	public void aggregateStranger() {

	}

	@Override
	public void enroleRollingWindow() {

	}


	public static String toDesc(List<Pair<StrangerFaceObject,Float>> newMatches) {
		return newMatches.stream().sorted((l, r) -> r.getLeft().createTs.compareTo(l.getLeft().createTs))
				.map(item -> {
					StrangerFaceObject obj = item.getLeft();
					StringBuilder sb = new StringBuilder();
					sb.append("{\"image\":\"");
					sb.append(obj.imagePath);
					sb.append("\",\"deviceId\":\"");
					sb.append(obj.getInfra().getDeviceId());
					sb.append("\",\"captureTime\":\"");
					sb.append(Utils.dateFormat.get().format(obj.createTs));
					sb.append("\"}");
					return sb.toString();
				}).collect(Collectors.joining(",", "[", "]"));
	}

	public static String toDescNoPair(List<StrangerFaceObject> newMatches){
		return newMatches.stream().sorted((l, r) -> r.createTs.compareTo(l.createTs))
				.map(item -> {
					StringBuilder sb = new StringBuilder();
					sb.append("{\"image\":\"");
					sb.append(item.imagePath);
					sb.append("\",\"deviceId\":\"");
					sb.append(item.getInfra().getDeviceId());
					sb.append("\",\"captureTime\":\"");
					sb.append(Utils.dateFormat.get().format(item.createTs));
					sb.append("\"}");
					return sb.toString();
				})
				.collect(Collectors.joining(",", "[", "]"));
	}

	public static StrangerFaceFeature builtStrangerFaceFeature(StrangerFaceObject object) {
		StrangerFaceFeature feature = new StrangerFaceFeature();
		feature.avatarImageUrl = object.imagePath;
		feature.imageFeature = FaissSeeker.featureToString(object.feature);
		feature.createTs = object.createTs;
		feature.groupKey = object.getInfra().getDeviceTag();
		feature.deviceId = object.getInfra().getDeviceId();
		feature.attribute = object.age + "," + object.sex;

		return feature;
	}

}
