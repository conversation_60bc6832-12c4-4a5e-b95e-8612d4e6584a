package com.sensetime.intersense.cognitivesvc.seekerface.seeker;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PersonFaceObject;
import com.sensetime.intersense.cognitivesvc.server.feign.MemberFeign;
import com.sensetime.lib.clientlib.response.BaseRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Component("faceMemberService")
@EnableScheduling
public class MemberService {
    
    private final Map<String, Set<String>> memberIdCache = new ConcurrentHashMap<String, Set<String>>();
    
    private final Map<String, List<PersonFaceObject>> memberObjCache = new ConcurrentHashMap<String, List<PersonFaceObject>>();
    
    @Autowired
    private ApplicationContext context;
    
    public Set<String> getGroupMemberId(String personGroup) {
        Set<String> result = memberIdCache.get(personGroup);
        if (result != null) return result;
        
        synchronized (memberIdCache) {
            result = memberIdCache.get(personGroup);
            if (result == null) memberIdCache.put(personGroup, getGroupMember_0(personGroup));
        }
        
        result = memberIdCache.get(personGroup);
        return result;
    }
    
    public List<PersonFaceObject> getGroupMemberObj(List<PersonFaceObject> cachedPerson, String personGroup) {
        List<PersonFaceObject> result = memberObjCache.get(personGroup);
        if (result != null) return result;
        
        synchronized (memberObjCache) {
            result = memberObjCache.get(personGroup);
            if (result == null) {
                Set<String> memberIds = getGroupMemberId(personGroup);
                result = cachedPerson.stream().filter(item -> memberIds.contains(item.pid)).collect(Collectors.toList());
            }
        }
        
        return result;
    }
    
    /**
     * 更新本地group-personIds缓存
     *
     * @param groupId
     * @param personIds
     * @param opsType
     */
    public void updateMemberIdCache(String groupId, Set<String> personIds, OpsType opsType) {
        
        synchronized (memberIdCache) {
            Set<String> result = memberIdCache.get(groupId);
            switch (opsType) {
                case ADD:
                    if (result == null) memberIdCache.put(groupId, personIds);
                    else {
                        result.addAll(personIds);
                        memberIdCache.put(groupId, result);
                    }
                    break;
                case DEL:
                    if (result != null) {
                        result.removeAll(personIds);
                        memberIdCache.put(groupId, result);
                    }
                    break;
            }
        }
        if (log.isDebugEnabled())
            log.debug(">>> [update memberIdCache] done! groupId:{},personIds:{},ops:{}", groupId, JSON.toJSONString(personIds), opsType.name());
    }
    
    @Scheduled(fixedDelayString = "${intersense.midface.memberIdCacheClearFixedDelayMillis:60000}")
//	@Scheduled(fixedDelay = 60 * 1000)
    void fetchData() {
        
        memberIdCache.clear();
        memberObjCache.clear();
        
        context.getBean(MemberFeign.class);
        if (log.isInfoEnabled()) log.info(">>> [faceMemberService] clear memerIdCache. done!");
    }
    
    private Set<String> getGroupMember_0(String personGroup) {
        try {
            MemberFeign.GroupMemberIdsReq req = new MemberFeign.GroupMemberIdsReq();
            req.setGroupId(personGroup);
            
            BaseRes<List<String>> list = context.getBean(MemberFeign.class).getMemberIdsFromGroup("*", "*", req);
            if (log.isDebugEnabled())
                log.debug(">>>[getMemberIdsFromGroup] personGroup={}, result={}", personGroup, JSON.toJSONString(list));
            
            return list.getData().parallelStream().collect(Collectors.toSet());
        } catch (Exception e) {
            log.error(">>> [MemberService] get groupMemberIds failed! groupid=" + personGroup, e);
            return Sets.newHashSet();
        }
    }
    
    /**
     * 通过 personId 查询所在的 groupId列表，@since studio 1.x
     *
     * @param personId
     * @return
     */
    private List<String> getGroupIdsByPersonId(String personId) {
        try {
            long start = System.currentTimeMillis();
            BaseRes<MemberFeign.GroupIdsRes> result = context.getBean(MemberFeign.class).queryPersonGroupIds("*", "*", personId);
            long end = System.currentTimeMillis();
            if (log.isDebugEnabled())
                log.debug(">>>[queryPersonGroupIds] personId={}, result={}, ts={}ms", personId, JSON.toJSONString(result),end -start);
            
            return result.getData().getPersonGroupIds();
        } catch (Exception e) {
            log.error(">>> [MemberService] get getGroupIdsByPersonId failed! personId=" + personId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 批量查询，通过 personId 查询所在的 groupId列表，@since studio 2.8.x
     *
     * @param personIds
     * @return
     */
    private Map<String, List<String>> batchGetGroupIdsByPersonId(List<String> personIds) {
        try {
            long start = System.currentTimeMillis();
            MemberFeign.PersonIdListReq req = new MemberFeign.PersonIdListReq();
            req.setUidList(personIds);
            BaseRes<Map<String, List<String>>> result = context.getBean(MemberFeign.class).batchQueryPersonGroupId("*", "*", req);
            long end = System.currentTimeMillis();
            if (log.isDebugEnabled())
                log.debug(">>>[batchGetGroupIdsByPersonId] personIds={}, result={}, ts={}ms", JSON.toJSONString(personIds), JSON.toJSONString(result),end -start);
            
            return result.getData();
        } catch (Exception e) {
            log.error(">>> [MemberService] batchGetGroupIdsByPersonId failed! personIds=" + JSON.toJSONString(personIds), e);
            throw e;
        }
    }
    
    /**
     * 批量查询，通过 personId 查询所在的 groupId列表，
     * 优先使用 /person/batchQueryPersonGroupIds ，查询报错则使用 /person/queryPersonGroupIds
     *
     * @param personIds
     * @return
     */
    public Map<String, List<String>> queryGroupIdsByPersonId(List<String> personIds) {
        Map<String, List<String>> result = new HashMap<>();
        if (personIds == null || personIds.size() <= 0) {
            if (log.isDebugEnabled())
                log.debug(">>>[queryGroupIdsByPersonId] query param personIds is empty!");
            return result;
        }
        try {
            result = batchGetGroupIdsByPersonId(personIds);
        } catch (Exception e) {
            log.error("queryGroupIdsByPersonId error:{}", e.getMessage());
            result = personIds.parallelStream().collect(Collectors.toMap(id -> id, id -> getGroupIdsByPersonId(id)));
        }
        return result;
    }
    
    
    public enum OpsType {ADD, DEL}
    
}
