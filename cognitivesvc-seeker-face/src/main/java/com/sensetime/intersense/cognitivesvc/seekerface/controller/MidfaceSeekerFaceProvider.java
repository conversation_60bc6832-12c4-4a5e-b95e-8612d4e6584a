package com.sensetime.intersense.cognitivesvc.seekerface.controller;

import com.sensetime.lib.weblib.utils.Baggages;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.MemberService;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.CompareFeatureParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.mapper.FacePedestrianClusterRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Broadcaster;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController("midfaceSeekerFaceProvider")
@RequestMapping(value = "/cognitive/face/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "MidfaceSeekerFaceProvider", description = "face controller")
@SuppressWarnings("rawtypes")
public class MidfaceSeekerFaceProvider extends BaseProvider {
    
//    @Autowired
//    private MidfaceSeekerFaceProvider midfaceSeekerFaceProvider;
    
    @Autowired
    private Broadcaster broadcastService;
    
    @Autowired
    private PersonFaceFeatureRepository personMapper;
    
    @Autowired
    private PasserFaceFeatureRepository passerMapper;
    
    @Autowired
    private FacePedestrianClusterRepository facePedestrianClusterRepository;
    
    @Autowired
    private SeekerFaceFacade seekerFacade;
    
    @Autowired
    private MemberService memberService;
    
    @Value("${spring.application.name}")
    private String appName;

    @Operation(summary = "保留多少天的数据", method = "GET")
    @RequestMapping(value = "/deleteParsserByDays", method = {RequestMethod.GET, RequestMethod.POST})
    public BaseRes<Integer> deleteParsserByDays(@Parameter(required = true, name = "days", description = "days") @RequestParam int days) {
        return deleteByPid(null, new Date(0), new Date(System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L)));
    }

    @Operation(summary = "删除人员特征值", method = "POST")
    @RequestMapping(value = "/deleteByPId", method = {RequestMethod.GET, RequestMethod.POST})
    public BaseRes<Integer> deleteByPid(@Parameter(required = false, name = "pids", description = "pid") @RequestParam(required = false) List<String> pids
            , @Parameter(required = false, name = "startTime", description = "startTime") @RequestParam(required = false) Date startTime
            , @Parameter(required = false, name = "endTime", description = "endTime") @RequestParam(required = false) Date endTime) {

        int personDeleted = 0, passerDeleted = 0;
        List<Integer> ids = new ArrayList<Integer>();
        
        if (!CollectionUtils.isEmpty(pids)) {
            List<Integer> personIds = personMapper.queryIdsByPersonUuids(pids);
            List<Integer> passerIds = passerMapper.queryIdsByPersonUuids(pids);
            
            if (!CollectionUtils.isEmpty(personIds)) {
                personDeleted += personMapper.deleteByPersonUuidIn(pids);
                ids.addAll(personIds);
                if(pids.size() !=  personDeleted){
                    log.info("[deleteByPid][Err] ForPerson{},{}", pids.size(), personDeleted);
                }
            }
            
            if (!CollectionUtils.isEmpty(passerIds)) {
                passerDeleted += passerMapper.deleteByPersonUuidIn(pids);
                ids.addAll(passerIds);

                if(pids.size() !=  passerDeleted){
                    log.info("[deleteByPid][Err] ForPasser{},{}", pids.size(), passerDeleted);
                }
            }
            
            if (personDeleted > 0 || passerDeleted > 0)
                facePedestrianClusterRepository.deleteByFacePersonIdIn(pids);
        }
        
        if (startTime != null && endTime != null)
            passerDeleted += passerMapper.deleteByCreateTsBetween(startTime, endTime);
        
        if (personDeleted > 0 || passerDeleted > 0) {
            Map<String, Object> varibles = new HashMap<String, Object>();
            varibles.put("ids", ids);
            
            if (startTime != null)
                varibles.put("startTime", startTime);
            
            if (endTime != null)
                varibles.put("endTime", endTime);
            
            broadcastService.postForObject(appName, "/cognitive/hidden/face/deleteByPid", varibles, String.class);
        }
        if (ids.isEmpty()) {
            return BaseRes.success(-1);
        }
        
        return BaseRes.success(personDeleted + passerDeleted);
    }
    
    @SuppressWarnings({"unchecked"})
    @Operation(summary = "用特征搜人", method = "POST")
    @RequestMapping(value = "/compareFaceFeature", method = RequestMethod.POST)
    @ResponseBody
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    public BaseRes<List<Map>> compareFaceFeature(@RequestBody CompareFeatureParam param) {
        if (param.getCount() != null && param.getCount() <= 0)
            param.setCount(null);
        
        float[] feature = null;
        
        if (param.getFeatureFloatArray() != null)
            feature = param.getFeatureFloatArray();
        else if (param.getFeatureBase64() != null)
            feature = FaissSeeker.stringToFeature(param.getFeatureBase64());
        
        if (feature == null)
            return BaseRes.success();
        
        List<Map> result = new ArrayList<Map>();
        
        String[] deptids = Baggages.getAllDeptIds();
        
//        if (ArrayUtils.contains(deptids, "*"))
//            deptids = null;
        
        if ("Target".equals(param.getPersonType())) {
            seekerFacade.findPerson(PersonParam.builder()
                            .feature(feature)
                            .count(param.getCount())
                            .personGroups(StringUtils.isBlank(param.getPersonGroup()) ? null : param.getPersonGroup().split(","))
                            .tags(StringUtils.isBlank(param.getPersonTag()) ? null : param.getPersonTag().split(","))
                            .threshold(param.getThreshold())
                            .deptIds(deptids)
                            .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("targetType", "Target");
                        map.put("score", item.getValue());
                        return map;
                    })
                    .forEach(result::add);
        } else if ("Passer".equals(param.getPersonType())) {
            seekerFacade.findPasser(PasserParam.builder()
                            .feature(feature)
                            .count(param.getCount())
                            .deptIds(deptids)
                            .threshold(param.getThreshold())
                            .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("targetType", "Passer");
                        map.put("score", item.getValue());
                        return map;
                    })
                    .forEach(result::add);
        } else {
            seekerFacade.findPerson(PersonParam.builder()
                            .feature(feature)
                            .count(param.getCount())
                            .personGroups(StringUtils.isBlank(param.getPersonGroup()) ? null : param.getPersonGroup().split(","))
                            .tags(StringUtils.isBlank(param.getPersonTag()) ? null : param.getPersonTag().split(","))
                            .threshold(param.getThreshold())
                            .deptIds(deptids)
                            .build())
                    .stream()
                    .map(item -> {
                        Map map = new HashMap();
                        map.put("personID", item.getLeft().pid);
                        map.put("targetType", "Target");
                        map.put("score", item.getValue());
                        return map;
                    })
                    .forEach(result::add);
            
            if (result.isEmpty()) {
                seekerFacade.findPasser(PasserParam.builder()
                                .feature(feature)
                                .count(param.getCount())
                                .deptIds(deptids)
                                .threshold(param.getThreshold())
                                .build())
                        .stream()
                        .map(item -> {
                            Map map = new HashMap();
                            map.put("personID", item.getLeft().pid);
                            map.put("targetType", "Passer");
                            map.put("score", item.getValue());
                            return map;
                        })
                        .forEach(result::add);
            }
        }
        
        return BaseRes.success(result);
    }

    @Operation(summary = "用一条person的特征", method = "POST")
    @RequestMapping(value = "/retrieveFeature", method = RequestMethod.POST)
    public BaseRes<Integer> retrieveFeature(@RequestBody Map<String, String> parameters) {
        List<PersonFaceFeature> list = personMapper.findByPersonUuid(parameters.get("personId"));
        if (list.size() > 0)
            deleteByPid(List.of(parameters.get("personId")), null, null);
        
        String deptid = Baggages.getDeptId();
        PersonFaceFeature pff = new PersonFaceFeature();
        pff.setAvatarImageUrl(parameters.get("figureImageUrl"));
        pff.setCreateUser(CREATE_MODIFY_USER);
        pff.setPrivilege("*".equals(deptid) ? "0" : deptid);
        pff.setPersonCnName(parameters.get("personCnName"));
        pff.setLastModTs(new Date());
        pff.setImageFeature(parameters.get("figureFeature"));
        pff.setPersonUuid(parameters.get("personId"));
        pff.setSts(STS_VALID);
        pff.setPersonEnName(parameters.get("personEnName"));
        pff.setTag(parameters.get("tag"));
        pff.setModelVersion(parameters.get("modelVersion"));
        pff.setCreateTs(new Date());
        
        personMapper.saveAndFlush(pff);
        
        return BaseRes.success(1);
    }


    @Operation(summary = "人组关系数据增量同步到cog应用缓存", method = "POST")
    @RequestMapping(value = "/updateMemberIdCache", method = RequestMethod.POST)
    public BaseRes<Object> updateMemberIdCache(
            @Parameter(examples = @ExampleObject(" {\"groupId\": \"123\", \"personIds\": [\"1\",\"2\",\"3\",\"4\"],\"opsType\": \"add or del\"}"
            )
            )
            @RequestBody Map<String, Object> parameters,
            @RequestParam(required = false) Boolean isBroadcast) {
        
        String groupId = (String) parameters.get("groupId");
        List<String> personIds = (List<String>) parameters.get("personIds");
        String opsTypeStr = (String) parameters.get("opsType");
        MemberService.OpsType opsType = MemberService.OpsType.valueOf(opsTypeStr.toUpperCase());
        
        // 更新本地缓存
        memberService.updateMemberIdCache(groupId, personIds.stream().collect(Collectors.toSet()), opsType);
        
        // 如果请求不是广播而来，广播给其他节点
        if (isBroadcast == null || Boolean.TRUE.equals(isBroadcast)) {
            log.info(">>> [updateMemberIdCache] broadcast to others.");
            // todo: 优化 - 此处广播请求的封装，会包括节点自身，正常需要排除掉自身；
            broadcastService.postForObject(appName, "/cognitive/face/updateMemberIdCache?isBroadcast=false", parameters, String.class);
        }
        return BaseRes.success("OK");
    }


    @Operation(summary = "人脸库和数据库同步一下", method = "GET")
    @RequestMapping(value = "/refetch", method = RequestMethod.GET)
    public void refetch(@RequestParam(required = false) Boolean isBroadcast) {
        if (Boolean.TRUE.equals(isBroadcast)) {
            broadcastService.getForObject(appName, "/cognitive/face/refetch", Map.of(), String.class);
        } else {
            seekerFacade.reFetchData();
        }
    }
    
    
    /**
     * 重建人脸特征索引接口
     * 支持两种模式：
     * 1. 广播模式(isBroadcast=true)：向所有节点发送重建请求
     * 2. 单节点模式(isBroadcast=false)：仅在当前节点执行重建
     * 
     * 注意事项：
     * 1. 广播模式下，所有节点都会尝试获取锁并执行重建
     * 2. 通过分布式锁机制确保同一时间只有一个节点在执行重建
     * 3. 如果上次重建时间超过配置的强制重建时间，将强制执行重建
     * 
     * @param isBroadcast 是否广播到所有节点
     */
    @Operation(summary = "目标库（不包括陌生人库）数据库同步一下（特征索引重建）", method = "GET")
    @RequestMapping(value = "/reindexTargetsData", method = RequestMethod.GET)
    public void reindexTargetsData(@RequestParam(required = false) Boolean isBroadcast) {
        log.info(">>> [reindexTargetsData] Received index rebuild request, broadcast mode: {}", isBroadcast);
        
        try {
            if (Boolean.TRUE.equals(isBroadcast)) {
                log.info(">>> [reindexTargetsData] Broadcasting rebuild request to all nodes");
                broadcastService.getForObject(appName, "/cognitive/face/reindexTargetsData", Map.of(), String.class);
                log.info(">>> [reindexTargetsData] Broadcast completed");
            } else {
                log.info(">>> [reindexTargetsData] Executing rebuild on current node");
                seekerFacade.reindexPersonWithOnebyOne();
                log.info(">>> [reindexTargetsData] Local rebuild completed");
            }
        } catch (Exception e) {
            log.error(">>> [reindexTargetsData] Error occurred during index rebuild", e);
            throw e;
        }
    }
    
    

    @Operation(summary = "清空表", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/scrub/feature", method = RequestMethod.POST)
    public BaseRes<Object> scrubFeature(@Parameter(name = "type", description = "类型") @RequestParam(value = "type") String type) {
        if ("person".equals(type)) {
            personMapper.deleteAll();
            facePedestrianClusterRepository.deleteByFacePersonType(0);
        }
        
        if ("passer".equals(type)) {
            passerMapper.deleteAll();
            facePedestrianClusterRepository.deleteByFacePersonType(1);
        }
        
        refetch(true);
        
        return BaseRes.success("OK");
    }
}
