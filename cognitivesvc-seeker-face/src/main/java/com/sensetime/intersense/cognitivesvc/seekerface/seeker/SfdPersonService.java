package com.sensetime.intersense.cognitivesvc.seekerface.seeker;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.sfd.FaceRecognitionMultiDbResponse;
import com.sensetime.intersense.cognitivesvc.server.entities.sfd.FaceRecognitionResponse;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PersonFaceObject;
import com.sensetime.intersense.cognitivesvc.server.services.SfdBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SFD人员搜索服务
 * 实现人员1:N搜索
 */
@Slf4j
@Service
public class SfdPersonService extends SfdBaseService<PersonParam, PersonFaceObject, List<Pair<PersonFaceObject, Float>>> {

    @Override
    protected float[] getFeature(PersonParam param) {
        return param.getFeature();
    }

    @Override
    protected String getFeatureType() {
        return "face";
    }

    @Override
    protected Integer getFeatureVersion() {
        return sfdProperties.getDb().getFeatureVersion();
    }

    @Override
    protected String getDbName() {
        return sfdProperties.getDb().getName();
    }

    @Override
    protected String getSfdSearchPath() {
        return sfdProperties.getFace1N();
    }

    @Override
    protected int getTopK(PersonParam param) {
        // 默认返回32个结果，如果设置了count则返回count个
        return Objects.requireNonNullElse(param.getCount(), 32);
    }

    @Override
    protected float getMinScore(PersonParam param) {
        // 使用参数中的阈值，如果没有设置则默认为0.6
        return Objects.requireNonNullElse(param.getThreshold(), 0.6f);
    }

    @Override
    protected List<Pair<PersonFaceObject, Float>> mergeResults(PersonParam param, List<String> groups) {
        Map<String, Pair<PersonFaceObject, Float>> resultMap = new HashMap<>();
        
        for (String group : groups) {
            List<Pair<PersonFaceObject, Float>> result = super.searchInDb(param, group);
            
            // 处理每个结果
            for (Pair<PersonFaceObject, Float> pair : result) {
                PersonFaceObject person = pair.getLeft();
                Float score = pair.getRight();
                
                // 如果已存在相同的pid，比较分数，保留分数较高的
                if (resultMap.containsKey(person.pid)) {
                    if (score > resultMap.get(person.pid).getRight()) {
                        log.info("Found duplicate pid {} with higher score {} (old score {})", 
                                person.pid, score, resultMap.get(person.pid).getRight());
                        resultMap.put(person.pid, pair);
                    } else {
                        log.info("Found duplicate pid {} with lower score {} (keeping old score {})", 
                                person.pid, score, resultMap.get(person.pid).getRight());
                    }
                } else {
                    resultMap.put(person.pid, pair);
                }
            }
        }
        
        // 将结果转换为列表并按分数排序
        List<Pair<PersonFaceObject, Float>> mergedResults = resultMap.values().stream()
                .sorted((a, b) -> Float.compare(b.getRight(), a.getRight()))
                .collect(Collectors.toList());
        
        // 如果设置了数量限制，截取指定数量
        if (param.getCount() != null && mergedResults.size() > param.getCount()) {
            mergedResults = mergedResults.subList(0, param.getCount());
        }
        
        return mergedResults;
    }
    
    @Override
    protected List<Pair<PersonFaceObject, Float>> processResponse(FaceRecognitionResponse response, PersonParam param) {
        List<Pair<PersonFaceObject, Float>> result = new ArrayList<>();
        
        if (response.getFeature_results() != null && 
            response.getFeature_results().get(0) != null &&
            CollectionUtils.isNotEmpty(response.getFeature_results().get(0).getResults())) {
            
            List<FaceRecognitionResponse.BaselineData> baselineDataList = response.getFeature_results().get(0).getResults()
                    .stream()
                    .sorted(Comparator.comparing(FaceRecognitionResponse.BaselineData::getScore).reversed())
                    .collect(Collectors.toList());
            
            for (FaceRecognitionResponse.BaselineData baselineData : baselineDataList) {
                if (baselineData.getScore() < getMinScore(param)) {
                    continue;
                }
                
                PersonFaceObject personFaceObject = new PersonFaceObject();
                personFaceObject.pid = baselineData.getItem().getKey();
                
                // 从info中提取附加信息
                if (baselineData.getItem().getExtra_info() != null) {
                    try {
                        JSONObject info = JSONObject.parseObject(baselineData.getItem().getExtra_info());
                        if (info.containsKey("avatar")) {
                            personFaceObject.avatar = info.getString("avatar");
                        }
                        if (info.containsKey("cnName")) {
                            personFaceObject.cnName = info.getString("cnName");
                        }
                        if (info.containsKey("enName")) {
                            personFaceObject.enName = info.getString("enName");
                        }
                        if (info.containsKey("tag")) {
                            personFaceObject.tag = info.getString("tag");
                        }
                    } catch (Exception e) {
                        log.warn("Failed to parse info: {}", e.getMessage());
                    }
                }
                
                result.add(new ImmutablePair<>(personFaceObject,  ((Number)baselineData.getScore()).floatValue()));
            }
        }
        
        // 如果设置了count，限制返回数量
        if (param.getCount() != null && result.size() > param.getCount()) {
            result = result.subList(0, param.getCount());
        }
        
        return result;
    }

    @Override
    protected List<Pair<PersonFaceObject, Float>> getEmptyResult() {
        return new ArrayList<>();
    }

    /**
     * 处理多数据库搜索的结果
     * 与单数据库搜索保持一致的返回格式
     */
    @Override
    protected List<Pair<PersonFaceObject, Float>> processMultiDbResponse(FaceRecognitionMultiDbResponse response, PersonParam param) {
        List<Pair<PersonFaceObject, Float>> result = new ArrayList<>();
        Map<String, Pair<PersonFaceObject, Float>> resultMap = new HashMap<>();
        
        if (response.getSearch_results() != null) {
            // 遍历每个数据库的结果
            for (FaceRecognitionMultiDbResponse.SearchResult searchResult : response.getSearch_results()) {
                // 处理每个数据库内的搜索结果
                if (searchResult.getSimilar_results() != null) {
                    for (FaceRecognitionMultiDbResponse.SimilarResult similarResult : searchResult.getSimilar_results()) {
                        if (similarResult.getScore() < getMinScore(param)) {
                            continue;
                        }
                        
                        FaceRecognitionMultiDbResponse.Item item = similarResult.getItem();
                        float score = similarResult.getScore().floatValue();
                        
                        PersonFaceObject personFaceObject = new PersonFaceObject();
                        personFaceObject.pid = item.getKey(); // 使用key作为pid
                        
                        // 从extra_info中提取附加信息
                        if (item.getExtra_info() != null) {
                            try {
                                // 如果extra_info不是JSON格式，尝试解析从";;"分隔的格式
                                if (item.getExtra_info().contains(";;")) {
                                    String[] parts = item.getExtra_info().split(";;");
                                    if (parts.length > 0) {
                                        personFaceObject.avatar = parts[0];
                                    }
                                } else {
                                    JSONObject info = JSONObject.parseObject(item.getExtra_info());
                                    if (info.containsKey("avatar")) {
                                        personFaceObject.avatar = info.getString("avatar");
                                    }
                                    if (info.containsKey("cnName")) {
                                        personFaceObject.cnName = info.getString("cnName");
                                    }
                                    if (info.containsKey("enName")) {
                                        personFaceObject.enName = info.getString("enName");
                                    }
                                    if (info.containsKey("tag")) {
                                        personFaceObject.tag = info.getString("tag");
                                    }
                                }
                            } catch (Exception e) {
                                log.warn("Failed to parse info: {}", e.getMessage());
                            }
                        }
                        
                        // 如果存在相同pid，保留得分高的
                        if (resultMap.containsKey(personFaceObject.pid)) {
                            if (score > resultMap.get(personFaceObject.pid).getRight()) {
                                log.info("Found duplicate pid {} with higher score {} (old score {})", 
                                       personFaceObject.pid, score, resultMap.get(personFaceObject.pid).getRight());
                                resultMap.put(personFaceObject.pid, new ImmutablePair<>(personFaceObject, score));
                            } else {
                                log.info("Found duplicate pid {} with lower score {} (keeping old score {})", 
                                       personFaceObject.pid, score, resultMap.get(personFaceObject.pid).getRight());
                            }
                        } else {
                            resultMap.put(personFaceObject.pid, new ImmutablePair<>(personFaceObject, score));
                        }
                    }
                }
            }
            
            // 将结果转换为列表并按分数排序
            result = resultMap.values().stream()
                    .sorted((a, b) -> Float.compare(b.getRight(), a.getRight()))
                    .collect(Collectors.toList());
            
            // 如果设置了数量限制，截取指定数量
            if (param.getCount() != null && result.size() > param.getCount()) {
                result = result.subList(0, param.getCount());
            }
        }
        
        return result;
    }

    /**
     * 公开getFeatureVersion方法，用于外部获取特征版本号
     */
    public Integer getFeatureVersionPublic() {
        return sfdProperties.getDb().getFeatureVersion();
    }
} 