package com.sensetime.intersense.cognitivesvc.strangerface.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;


import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EnableAspectJAutoProxy(proxyTargetClass = true)
@DependsOn({"just", "simplify"})
public class StrangerFaceSeekerFacade{
	
	private static final int bucketLength = 1000;
	
	@Autowired
	private DiscoveryClient discoveryClient;
	
	@Autowired
	private Map<String, AbstractFaceStrangerSeeker> seekers;
	
	@Value("${spring.application.name}")
	private String name;
	
	private volatile InstanceItem[] instances = new InstanceItem[0];
	
	public String strangerIsHere(StrangerFaceObject obj) {
		if("none".equals(Utils.instance.runningType)) {
			return null;
		}else if("just".equals(Utils.instance.runningType)) {
			return seekers.get(Utils.instance.runningType).strangerIsHere(obj);
		}else if("simplify".equals(Utils.instance.runningType)) {
			int targetIndex = (int)Utils.keyToContextId(obj.getInfra().getDeviceTag()) % bucketLength;
			ServiceInstance target = null;
			
			try {
				InstanceItem[] instances = this.instances;
				
				if(targetIndex <= instances[0].index)
					target = instances[0].instance;
				
				if(targetIndex >= instances[instances.length - 1].index)
					target = instances[instances.length - 1].instance;
				
				for(int index = 0; index < instances.length - 1; index ++)
					if(targetIndex > instances[index].index && targetIndex < instances[index + 1].index)
						target = instances[index].instance;
				
				if(target != null) {
					HttpHeaders headers = new HttpHeaders();
					headers.setContentType(MediaType.APPLICATION_JSON);
					HttpEntity<Object> httpEntity = new HttpEntity<Object>(obj, headers);
					RestUtils.restTemplate1000ms.postForEntity("http://" + target.getHost() + ":" + target.getPort() + "/cognitive/stranger/is/here", httpEntity, String.class);
				}
			}catch(Exception e) {
				log.warn("fail to send tracklet entity devicetag[" + obj.getInfra().getDeviceTag() + "], to [" + target.getHost() + ":" + target.getPort() + "].", e);
			}
		}
		
		return null;
	};
	
	/** 同步归档服务器地址
     */
    @Scheduled(fixedDelay = 30000)
    public void updateStrangerHandler() {
    	instances = discoveryClient.getInstances(name)
			    	   .stream()
			    	   .map(instance -> {
			    		   InstanceItem[] instances = new InstanceItem[10];
			    		   for(int index = 0 ;index < instances.length; index ++) {
			    			   int bucket = Math.abs(("$" + index + "$" + instance.getInstanceId() + "$" + index + "$").hashCode()) % bucketLength;
			    			   instances[index] = new InstanceItem(bucket, instance);
			    		   }
			    		   return instances;
			    	   })
			    	   .flatMap(Arrays::stream)
			    	   .sorted((l, r) -> Integer.compare(l.index, r.index))
			    	   .toArray(InstanceItem[]::new);
    }

	/** 图片流过来的陌生人 通过一致性哈希 把相同标签设备来源的陌生人 发送到相同的服务上
	 */
	@RestController
	@RequestMapping(value = "/cognitive/stranger", produces = MediaType.APPLICATION_JSON_VALUE)
	@Tag(name="DlcStrangerProvider",description = "icon controller")
	public static class DlcStrangerProvider extends BaseProvider implements Runnable {

		private final LinkedBlockingQueue<StrangerFaceObject> handlingQueue = new LinkedBlockingQueue<StrangerFaceObject>(128);

		@Autowired
		private Map<String, AbstractFaceStrangerSeeker> seekers;

		@Operation(summary = "分布式输入人脸", method = "POST", hidden = true)
		@RequestMapping(value = "/is/here", method = RequestMethod.POST)
		public synchronized void dlcEnqueue(@RequestBody StrangerFaceObject obj) {
			handlingQueue.offer(obj);
		}
		
		@Override
		public void run() {
			while(true) {
				List<StrangerFaceObject> objs = new ArrayList<StrangerFaceObject>(16);
				
				for(int index = 0; index < 16; index ++) {
					try { 
						StrangerFaceObject item = handlingQueue.poll(100, TimeUnit.MILLISECONDS); 
						if(item == null)
							break;
								
						objs.add(item);
					}
					catch (Exception e) {} 
				}
				
				objs.parallelStream().forEach(item -> seekers.get(Utils.instance.runningType).strangerIsHere(item));
			}
		}
		
		@PostConstruct
		public void initializer() {			
			Thread recognizeThread = new Thread(Utils.cogGroup, this);
			recognizeThread.setDaemon(true);
			recognizeThread.setName("StrangerHandler");
			recognizeThread.start();
		}
	}
	
	@Data
	@Accessors(chain = true)
	@Builder
	private static class InstanceItem{
		int index;
		ServiceInstance instance;
	}
	
	@Data
	@Accessors(chain = true)
	@Builder
    @NoArgsConstructor
    @AllArgsConstructor
	public static class RawMessage{
		public String eventName;
		public String eventAction;
		public Map<String, Object> data;
	}
}
