<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<profiles>
		<profile>
			<id>stg</id>
			<properties>
				<target.env>stg</target.env>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
	</profiles>

	<properties>
		<bom.version>2.14.0-SNAPSHOT</bom.version>
	</properties>

	<groupId>com.sensetime.intersense</groupId>
	<artifactId>cognitivesvc</artifactId>
	<version>2.14.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<name>cognitivesvc-pom</name>

	<modules>
		<module>cognitivesvc-client</module>
		<module>cognitivesvc-server</module>
		<module>cognitivesvc-seeker-face</module>
		<module>cognitivesvc-seeker-ped</module>
		<module>cognitivesvc-stream</module>
		<module>cognitivesvc-stream-face</module>
		<module>cognitivesvc-stream-ped</module>
		<module>cognitivesvc-xswitcher</module>
		<module>cognitivesvc-xworker</module>
		<module>cognitivesvc-web</module>
	</modules>

	<repositories>
		<repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>http://nexus.stg.intersense.sensetime.com/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
		<repository>
			<id>snapshots</id>
			<name>snapshots</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
				<checksumPolicy>fail</checksumPolicy>
			</snapshots>
		</repository>
	</repositories>

	<distributionManagement>
		<snapshotRepository>
			<id>snapshots</id>
			<url>http://nexus.intersense.sensetime.com/repository/maven-snapshots/</url>
		</snapshotRepository>
		<repository>
			<id>release</id>
			<url>http://nexus.intersense.sensetime.com/repository/maven-releases/</url>
		</repository>
	</distributionManagement>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.3.10</version>
		<relativePath />
	</parent>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.sensetime.dependencies-bom</groupId>
				<artifactId>dependencies-bom</artifactId>
				<version>${bom.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>2023.0.5</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>1.2.83</version>
			</dependency>

			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-math</artifactId>
				<version>2.2</version>
			</dependency>

			<dependency>
				<groupId>org.yaml</groupId>
				<artifactId>snakeyaml</artifactId>
				<version>2.0</version>
			</dependency>

			<dependency>
				<groupId>net.java.dev.jna</groupId>
				<artifactId>jna</artifactId>
				<version>5.8.0</version>
			</dependency>

			<dependency>
				<groupId>net.java.dev.jna</groupId>
				<artifactId>jna-platform</artifactId>
				<version>5.8.0</version>
			</dependency>

			<dependency>
				<groupId>com.thoughtworks.xstream</groupId>
				<artifactId>xstream</artifactId>
				<version>1.4.21</version>
			</dependency>


			<dependency>
				<groupId>org.bytedeco</groupId>
				<artifactId>javacv-platform</artifactId>
				<version>1.5.9</version>
			</dependency>

			<dependency>
				<groupId>org.bytedeco</groupId>
				<artifactId>ffmpeg-platform</artifactId>
				<version>6.0-1.5.9</version>
			</dependency>

			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-pool2</artifactId>
				<version>2.6.2</version>
			</dependency>

<!--			<dependency>-->
<!--				<groupId>net.sf.ehcache</groupId>-->
<!--				<artifactId>ehcache</artifactId>-->
<!--				<version>2.10.8</version>-->
<!--			</dependency>-->


			<dependency>
				<groupId>commons-collections</groupId>
				<artifactId>commons-collections</artifactId>
				<version>3.2.2</version>
			</dependency>

			<dependency>
                <groupId>com.mysql</groupId>
				<artifactId>mysql-connector-j</artifactId>
				<version>9.2.0</version>
			</dependency>

			<dependency>
				<groupId>org.hsqldb</groupId>
				<artifactId>hsqldb</artifactId>
				<version>2.7.1</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid</artifactId>
				<version>1.2.22</version>
			</dependency>

			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
				<version>2.5.0</version>
			</dependency>

			<dependency>
				<groupId>javax.xml.bind</groupId>
				<artifactId>jaxb-api</artifactId>
				<version>2.3.0</version>
			</dependency>

			<dependency>
				<groupId>com.sun.xml.bind</groupId>
				<artifactId>jaxb-impl</artifactId>
				<version>2.3.0</version>
			</dependency>

			<dependency>
				<groupId>com.sun.xml.bind</groupId>
				<artifactId>jaxb-core</artifactId>
				<version>2.3.0</version>
			</dependency>

			<dependency>
				<groupId>javax.activation</groupId>
				<artifactId>activation</artifactId>
				<version>1.1.1</version>
			</dependency>
<!--			&lt;!&ndash; https://mvnrepository.com/artifact/com.github.jsqlparser/jsqlparser &ndash;&gt;-->
<!--			<dependency>-->
<!--				<groupId>com.github.jsqlparser</groupId>-->
<!--				<artifactId>jsqlparser</artifactId>-->
<!--				<version>4.5</version>-->
<!--			</dependency>-->

		</dependencies>
	</dependencyManagement>

	<build>
		<resources>
<!--			<resource>-->
<!--				<directory>../.git/refs/remotes/origin</directory>-->
<!--				<includes>-->
<!--					<include>master</include>-->
<!--				</includes>-->
<!--			</resource>-->
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
					<include>**/*.yml</include>
					<include>**/*.yaml</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.yml</include>
					<include>**/*.yaml</include>
					<include>**/*.xml</include>
					<include>**/*.jpg</include>
					<include>**/*.mp4</include>
					<include>**/*.factories</include>
					<include>**/org.springframework.boot.autoconfigure.AutoConfiguration.imports</include>
					<include>META-INf/lib/*</include>
					<include>META-INf/model/*</include>
				</includes>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<release>11</release>
<!--					<compilerArgs>-->
<!--			            <arg>&#45;&#45;enable-preview</arg>-->
<!--			            <arg>-Xlint:all</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>-->
<!--			            <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.jvm=ALL-UNNAMED</arg>-->
<!--			        </compilerArgs>-->
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<phase>compile</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<copy failonerror="false" overwrite="true" force="true"
									todir="${project.build.outputDirectory}/refs/">
									<fileset dir="${project.basedir}/.git/refs"
										includes="**" />
								</copy>
								<copy failonerror="false" overwrite="true" force="true"
									file="${project.basedir}/.git/HEAD"
									tofile="${project.build.outputDirectory}/HEAD" />
								<copy failonerror="false" overwrite="true" force="true"
									todir="${project.build.outputDirectory}/refs/">
									<fileset dir="${project.basedir}/../.git/refs"
										includes="**" />
								</copy>
								<copy failonerror="false" overwrite="true" force="true"
									file="${project.basedir}/../.git/HEAD"
									tofile="${project.build.outputDirectory}/HEAD" />
								<copy failonerror="false" overwrite="true" force="true"
									file="${project.build.outputDirectory}/application-stg.yml"
									tofile="${project.build.outputDirectory}/application.yml" />
								<copy failonerror="false" overwrite="true" force="true"
									file="${project.build.outputDirectory}/config-stg.properties"
									tofile="${project.build.outputDirectory}/config.properties" />
								<delete quiet="true"
									dir="${project.build.outputDirectory}/sql" />
								<delete quiet="true"
									dir="${project.build.outputDirectory}/db" />
								<delete quiet="true"
									dir="${project.build.outputDirectory}/cpp" />
								<delete quiet="true"
									dir="${project.build.outputDirectory}/testcase" />
								<delete quiet="true">
									<fileset dir="${project.build.outputDirectory}/"
										includes="*.sql" />
								</delete>
								<delete quiet="true">
									<fileset dir="${project.build.outputDirectory}/"
										includes="rebel.xml" />
								</delete>
								<delete quiet="true">
									<fileset dir="${project.build.outputDirectory}/"
										includes="*lic.yml" />
								</delete>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
