# shortVideo 服务集成说明

## 概述
本功能实现了在发送消息到 Kafka 时，自动调用 shortVideo 服务获取 record_id 并添加到 extra 字段中。

## 配置

### 1. video_stream_infra 表 processors 字段配置
在 `video_stream_infra` 表的 `processors` 字段中添加 `record_stream_id`：

```json
{
    "stream_multiplier": 1.0,
    "useH265": 0,
    "record_stream_id": "1"
}
```

### 2. application-stg.yml 配置
在 `cognitivesvc-xworker/src/main/resources/application-stg.yml` 中配置 shortVideo 服务地址：

```yaml
shortvideo:
  enable: true
  host: http://*************:31891
  recordUrl: /v1/records
  prerecordSeconds: 2
  appendRecordSeconds: 3
  recordAtOffsetSeconds: 0      # recordAt时间偏移秒数，0表示不调整
  enableRecordAtOffset: false   # 是否启用recordAt时间偏移功能
```

### recordAt 时间偏移配置说明

- **recordAtOffsetSeconds**: 时间偏移秒数
  - `0`: 不调整时间（默认）
  - `120`: 当前时间减少2分钟
  - `-60`: 当前时间增加1分钟
- **enableRecordAtOffset**: 是否启用时间偏移功能
  - `false`: 关闭时间偏移（默认）
  - `true`: 启用时间偏移

### 使用示例

如果配置 `recordAtOffsetSeconds: 120` 和 `enableRecordAtOffset: true`：
- 当前时间: `2025-06-20T10:30:00.000Z`
- 调整后时间: `2025-06-20T10:28:00.000Z` (减少2分钟)

## 工作流程

1. **读取配置**: 系统从 `video_stream_infra` 表的 `processors` 字段中读取 `record_stream_id`
2. **调用服务**: 在 `sendOutputObject` 方法中调用 shortVideo 服务
3. **获取完整信息**: 发送 POST 请求到 shortVideo 服务获取完整的记录信息
4. **添加到 extra**: 将获取到的信息添加到 extra 字段中：
   - `record_stream_id`: 流ID
   - `record_id`: 记录ID
   - `prerecordSeconds`: 预录制秒数
   - `appendRecordSeconds`: 追加录制秒数
5. **推送到 Kafka**: 将包含完整信息的消息推送到 Kafka

## shortVideo 服务请求格式

### 请求
```bash
curl --location 'http://*************:31891/v1/records' \
--header 'accept: application/json' \
--header 'Content-Type: application/json' \
--data '{
  "stream_id": "1",
  "record_at": "2025-06-16T08:39:01.307Z",
  "prerecord_seconds": 2,
  "append_record_seconds": 3
}'
```

**注意**: 如果启用了时间偏移功能（`enableRecordAtOffset: true` 和 `recordAtOffsetSeconds: 120`），
那么 `record_at` 时间会自动调整为当前时间减去120秒。

### 响应
```json
{
    "record_info": {
        "record_id": "10002025061608403973001",
        "stream_id": "1",
        "record_at": "2025-06-16T08:41:01.307Z",
        "prerecord_seconds": 2,
        "append_record_seconds": 3,
        "creation_time": "2025-06-16T08:40:39.731148734Z",
        "record_status": "RS_INITIAL",
        "error_message": "",
        "record_url": ""
    }
}
```

## Kafka 消息中的 extra 字段示例

调用 shortVideo 服务后，Kafka 消息的 extra 字段将包含以下信息：

```json
{
  "frameIndex": 12345,
  "captureToKafkaTime": 150,
  "startFrameToKafkaTime": 2500,
  "record_stream_id": "1",
  "record_id": "10002025061608403973001",
  "prerecordSeconds": 2,
  "appendRecordSeconds": 3
}
```

### 字段说明：
- **record_stream_id**: 从 processors 配置中读取的流ID
- **record_id**: shortVideo 服务返回的记录ID
- **prerecordSeconds**: 预录制时间（秒）
- **appendRecordSeconds**: 追加录制时间（秒）

## 代码修改说明

### 1. VideoStreamInfra 实体类
- 添加了 `record_stream_id` 字段到 `ProcessorsConfig` 类
- 添加了 `getRecordStreamId()` 方法来解析 processors JSON

### 2. ShortVideoService 服务类
- 实现了调用 shortVideo 服务的逻辑
- 参考 `SfdBaseService#searchInDb` 的实现模式
- 使用 `RestUtils.restTemplate10000ms` 进行 HTTP 请求

### 3. XSenseyexEventHandler 处理器
- 在 `sendOutputObject` 方法中集成了 shortVideo 服务调用
- 在发送到 Kafka 之前获取 record_id 并添加到 extra 字段

## 容错处理

- 如果 `record_stream_id` 为空，跳过 shortVideo 服务调用
- 如果 shortVideo 服务调用失败，记录错误日志但不影响正常的消息发送
- 如果获取不到 record_id，不会添加到 extra 字段中

## 日志记录

系统会记录以下日志：
- shortVideo 服务调用的详细信息（DEBUG 级别）
- 成功获取 record_id 的信息（DEBUG 级别）
- 服务调用失败的错误信息（ERROR 级别）

## 配置示例

### 1. 默认配置（不调整时间）
```yaml
shortvideo:
  enable: true
  host: http://*************:31891
  recordUrl: /v1/records
  prerecordSeconds: 2
  appendRecordSeconds: 3
  recordAtOffsetSeconds: 0
  enableRecordAtOffset: false
```

### 2. 时间回退2分钟配置
```yaml
shortvideo:
  enable: true
  host: http://*************:31891
  recordUrl: /v1/records
  prerecordSeconds: 2
  appendRecordSeconds: 3
  recordAtOffsetSeconds: 120      # 回退2分钟
  enableRecordAtOffset: true
```

### 3. 时间前进1分钟配置
```yaml
shortvideo:
  enable: true
  host: http://*************:31891
  recordUrl: /v1/records
  prerecordSeconds: 2
  appendRecordSeconds: 3
  recordAtOffsetSeconds: -60      # 前进1分钟
  enableRecordAtOffset: true
```

## 测试

可以通过以下方式测试功能：
1. 配置 `video_stream_infra` 表的 `processors` 字段
2. 确保 shortVideo 服务正常运行
3. 发送消息并检查 Kafka 中的 extra 字段是否包含 record_id

### 测试时间偏移功能
1. 设置 `enableRecordAtOffset: true` 和 `recordAtOffsetSeconds: 120`
2. 观察日志中的 `recordAt` 时间是否比当前时间早2分钟
3. 检查发送到 shortVideo 服务的请求中的 `record_at` 字段
