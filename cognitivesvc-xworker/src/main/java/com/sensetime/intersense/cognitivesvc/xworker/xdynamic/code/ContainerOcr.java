package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.*;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ContainerOcr extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {


    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds);
        PointerByReference[] sub1_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[1])[0], imageIds);

        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();

            if (sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            if(sub1_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub1_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            handlingList.get(index).setKeson(new PointerByReference(array));
        }

        KesonUtils.kesonDeepDelete(outputKesons);
    }

    @SuppressWarnings({ "unchecked"})
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return false;

        List<Map<String, Object>> tracklets = (List<Map<String, Object>>)detectResult.get(1).get("targets");

        return !tracklets.isEmpty();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {

        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return;

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(1).getOrDefault("targets", List.of());

        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        String deviceId = (String)modelResult.getModelRequest().getParameter().get("deviceId");

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 1), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        for(Map<String, Object> target : targets) {
            Map<String, Object> valueResult = new HashMap<String, Object>();
            valueResult.put("roi", target.get("roi"));
            valueResult.put("detect", target.get("target_image_roi"));
            valueResult.put("deviceId", deviceId);
            valueResult.put("confidence", target.get("confidence"));
            valueResult.put("status", target.get("status"));

            if (target.containsKey("textline")) {
                //long text_image_id = (long) target.get("image_id");
                List<Map<String, Object>> textLines = (List<Map<String, Object>>) target.get("textline");
                List<Map<String, Object>> contents = textLines.stream()
                        .map(textL -> {
                            Map<String, Object> contentsSingle = new HashMap<>();
                            Map<String, Object> content = (Map<String, Object>) textL.getOrDefault("content", Map.of());
                            contentsSingle.put("name", textL.getOrDefault("name", ""));
                            contentsSingle.put("score", textL.getOrDefault("score", 0));
                            contentsSingle.put("roi", (Map<String, Integer>) textL.get("roi"));
                            Map<String, String> readable = (Map<String, String>) ((Map<String, Object>) content.getOrDefault("$binary", Map.of())).get("$readable");
                            contentsSingle.put("content", readable.getOrDefault("content", ""));
                            return contentsSingle;
                        })
                        .collect(Collectors.toList());
//                Float confidence = textLines.stream().map(line -> ((Number) line.get("score")).floatValue()).reduce(Float::sum).get() / textLines.size();
//                String contentText = textLines.stream().map(line -> (String) ((Map<String, Object>) line.getOrDefault("content", Map.of())).get("utf8")).filter(StringUtils::isNotBlank).collect(Collectors.joining());
//                contents[text_image_id] = Map.of("score", confidence, "content", Map.of("utf8", contentText));

                valueResult.put("textline", contents);
            }

            Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                    .limit(kesonTarget_size)
                    .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "id")) == ((Number) target.get("id")).longValue())
                    .findAny()
                    .orElse(null);

            if (kesonTarget != null) {
                PointerByReference targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                String targetImagePath = FrameUtils.save_image_as_jpg(targetImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                valueResult.put("targetImage", targetImagePath);

                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "scene_frame"));
                String sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()),processor.getImgSaveTag());
                valueResult.put("sceneImage", sceneImagePath);
            }

            outputResult.add(valueResult);

        }

        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[] {FrameUtils.NOIMAGE});

        if(outputResult.isEmpty())
            modelResult.setOutputResult(null);
        else
            modelResult.setOutputResult(outputResult);

    }
    @Override
    public synchronized void destroy() {
        log.info("***************************************************");
        log.info("destroying xmodel for [" + annotatorName() + "]");
        log.info("***************************************************");

        stopHandle();

        Initializer.bindDeviceOrNot();

        for(int index = 0; holders != null && index < holders.length; index ++) {
            if(holders[index] != null)
                holders[index].close();

            holders[index] = null;
        }

        for(int index = 0; plugins != null && index < plugins.length; index ++) {
            if(plugins[index] == null)
                continue;

            KestrelApi.kestrel_plugin_unload(plugins[index]);

            plugins[index] = null;
        }

        for(File attachedTarg : attachedLned)
            HostUtils.runLinux(new String[] {"/bin/sh", "-c", "rm -f " + attachedTarg.getAbsolutePath()});
    }
}
