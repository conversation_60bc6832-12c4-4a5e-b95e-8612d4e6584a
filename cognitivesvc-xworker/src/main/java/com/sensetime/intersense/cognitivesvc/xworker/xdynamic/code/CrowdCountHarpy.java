package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import lombok.Getter;

public class CrowdCountHarpy extends DynamicXModelHandler {
	
    @Getter
    protected final Integer interval = 24;//妫€涓€璺充笁
    
    @Getter
    protected final Integer frameBuffer = 4;
    
    @Getter
    protected final String frameBufferStrategy = "pedantic_swallow";
    
    @Getter
    protected final boolean lazyInit = true;  
    
    @Getter
    protected final int drainPollcount = 5;    
    
    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        super.buildOutputValue(modelResult);
        
        List<Map<String, Object>> detectTargets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
        for(Map<String, Object> detect : detectTargets) 
            for(Map<String, Object> output : ((List<Map<String, Object>>)modelResult.getOutputResult())) 
                if(detect.get("roi") == output.get("detect")) 
                    output.put("label", detect.get("label"));
        
        Map<String, List<Map<String, Object>>> roiValueMap = ((List<Map<String, Object>>)modelResult.getOutputResult()).stream()
            .filter(item -> Objects.equals(221488, item.get("label")))
            .map(item ->{
                String[] rois = (String[])item.getOrDefault("rois", SCREEN);
                return Arrays.stream(rois).map(roi -> new MutablePair<>(roi, item)).collect(Collectors.toList());
            })
            .flatMap(List::stream)
            .collect(Collectors.toMap(
                pair -> pair.getLeft(), 
                pair -> List.of(pair.getRight()),
                ListUtils::union
            ));
        
        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        for(Entry<String, List<Map<String, Object>>> entry : roiValueMap.entrySet()) {
            Map<String, Object> output = new HashMap<String, Object>();
            outputResult.add(output);

            output.put("roiId", entry.getKey());
            output.put("deviceId", (String)modelResult.getModelRequest().getParameter().get("deviceId"));
            output.put("maxAlert", entry.getValue().size());
            
            List<Map<String, Object>> targets = new ArrayList<Map<String, Object>>();
            output.put("targets", targets);
            
            for(Map<String, Object> target : entry.getValue()) {
                Map<String, Number> detect = (Map<String, Number>)target.get("detect");
                targets.add(Map.of("x", detect.get("left").floatValue() + detect.get("width").floatValue() / 2, "y",  detect.get("top").floatValue()));
            }
        }
        
        modelResult.setOutputResult(outputResult);
    }
}