package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
@SuppressWarnings("unused")
public class CrossLinesFBArea extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {


    protected ConcurrentHashMap<Long, Map<String,String>> deviceRoiMapCrossFaceBody = new ConcurrentHashMap<Long, Map<String,String>>();

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds);
        PointerByReference[] sub1_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[1])[0], imageIds);

        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();

            if (sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            if (sub1_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub1_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            handlingList.get(index).setKeson(new PointerByReference(array));
        }

        KesonUtils.kesonDeepDelete(outputKesons);
    }

    /**
     * 按照 函数包含的模型 顺序的跑一边
     */
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];

        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);

        String deviceId = (String)handlingList.get(0).getModelRequest().getParameter().get("deviceId");
        VideoStreamInfra deviceInfra = (VideoStreamInfra) handlingList.get(0).getModelRequest().getParameter().get("deviceInfra");

        Long streamSourceId = Utils.keyToContextId(deviceId);


        Map<String, String> roiCrossFaceBody = deviceRoiMapCrossFaceBody.get(streamSourceId);

        Integer[][][] proi = handlingList.get(0).getModelRequest().getProcessor().getRoi();

        CognitiveEntity.Processor processor = handlingList.get(0).getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        if(ArrayUtils.isEmpty(polygons))
            polygons = new Polygon[] {new Polygon(new int[] {0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[] {0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};


        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);


        Map<String, Object> additional = (Map<String, Object>) Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
        for (int index = 0; index < pointers.length; index++) {

            long now = System.currentTimeMillis();

            List<Integer[][]> rois = new ArrayList<>();
            List<Integer[][]> roisStart = new ArrayList<>();
            List<Integer[][]> roisEnd = new ArrayList<>();
            List<Integer[][]> roisExclude = new ArrayList<>();
            for(int indexs = 0 ;indexs < polygons.length; indexs ++) {
                Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
                if (extra.isEmpty())
                    continue;
                Integer[][] singleRoi = proi[indexs];
                if(singleRoi.length <=0)
                    continue;
                if(extra.containsKey("area")){
                    if(extra.get("area").equals(STARTAREA)){
                        roisStart.add(singleRoi);
                    }else if(extra.get("area").equals(ENDAREA)){
                        roisEnd.add(singleRoi);
                    }else if(extra.get("area").equals(EXCLUDEAREA)){
                        roisExclude.add(singleRoi);
                    }
                }
            }

            String[]  roisStartList =  toArrayStringRoiList(roisStart);
            String[]  roisEndList =  toArrayStringRoiList(roisEnd);
            String[]  roisExcludeList =  toArrayStringRoiList(roisExclude);

            String policyRoiString = JSON.toJSONString(toArrayStringRoiList(rois));
            String policyRoiStringStart = JSON.toJSONString(roisStartList);
            String policyRoiStringEnd = JSON.toJSONString(roisEndList);
            String policyRoiStringExclude = JSON.toJSONString(roisExcludeList);


            if (index == 0) {

                if(roiCrossFaceBody.get(ROISTRING) == null){
                    roiCrossFaceBody.put(ROISTRING, policyRoiString);
                    //updateRoi(JSON.toJSONString(toArrayStringRi(rois)), pointers[index].pointers[0], streamSourceId.toString());
                }else {
                    String oldRoiString = roiCrossFaceBody.get(ROISTRING);
                    if(!oldRoiString.equals(policyRoiString)){
                        roiCrossFaceBody.put(ROISTRING, policyRoiString);
                        //updateRoi(JSON.toJSONString(toArrayStringRi(rois)), pointers[index].pointers[0], streamSourceId.toString());
                    }
                }
                monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
            }
            if (index == 1) {

                //startArea
                if(roiCrossFaceBody.get(STARTAREA) == null){
                    roiCrossFaceBody.put(STARTAREA, policyRoiStringStart);
                    updateArea(roisStartList, roisEndList, roisExcludeList, pointers[index].pointers[0], streamSourceId.toString());
                }else {
                    String oldRoiString = roiCrossFaceBody.get(STARTAREA);
                    if(!oldRoiString.equals(policyRoiStringStart)){
                        roiCrossFaceBody.put(STARTAREA, policyRoiStringStart);
                        updateArea(roisStartList, roisEndList, roisExcludeList, pointers[index].pointers[0], streamSourceId.toString());                  }
                }
                //endArea
                if(roiCrossFaceBody.get(ENDAREA) == null){
                    roiCrossFaceBody.put(ENDAREA, policyRoiStringEnd);
                    updateArea(roisStartList, roisEndList, roisExcludeList, pointers[index].pointers[0], streamSourceId.toString());
                }else {
                    String oldRoiString = roiCrossFaceBody.get(ENDAREA);
                    if(!oldRoiString.equals(policyRoiStringEnd)){
                        roiCrossFaceBody.put(ENDAREA, policyRoiStringEnd);
                        updateArea(roisStartList, roisEndList, roisExcludeList, pointers[index].pointers[0], streamSourceId.toString());
                    }
                }
                //excludeArea
                if(roiCrossFaceBody.get(EXCLUDEAREA) == null){
                    roiCrossFaceBody.put(EXCLUDEAREA, policyRoiStringExclude);
                    updateArea(roisStartList, roisEndList, roisExcludeList, pointers[index].pointers[0], streamSourceId.toString());
                }else {
                    String oldRoiString = roiCrossFaceBody.get(EXCLUDEAREA);
                    if(!oldRoiString.equals(policyRoiStringExclude)){
                        roiCrossFaceBody.put(EXCLUDEAREA, policyRoiStringExclude);
                        updateArea(roisStartList, roisEndList, roisExcludeList, pointers[index].pointers[0], streamSourceId.toString());
                    }
                }


            }

            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            /** 执行模型 获取数据*/
            pointers[index].process(inTo.getValue(), outOf);
        }

        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }

    private void updateArea(String[] policyRoiStringStart, String[] policyRoiStringEnd, String[] policyRoiStringExclude, Pointer pipelinePoint, String sourceID) {


        JSONObject polygons = new JSONObject();

        polygons.put("start_area", policyRoiStringStart);
        polygons.put("end_area", policyRoiStringEnd);
        polygons.put("exclude_area", policyRoiStringExclude);

        JSONObject data = new JSONObject();
        // data.put("id", i);
        data.put("check_type", 1);
        //data.put("border_offset", 300);
        data.put("polygons", polygons);


        String crossLines =" {\n" +
                "                \"streams\": [\n" +
                "                           {\n" +
                "                                \"name\": \"video_face_body_analyze_stream\",\n" +
                "                                \"modules\": [\n" +
                "                                               {\n" +
                                                    "                    \"name\": \"cross_line\",\n" +
                                                    "                    \"source_id\": 49651,\n" +
                                                    "                    \"type\": \"FacebodyCrossline\",\n" +
                                                    "                    \"parallel_group\":\"target_analysis\",\n"+
                                                    "                    \"inputs\": [\n" +
                                                    "                        \"analyzed_face_targets\"\n" +
                                                    "                    ],\n" +
                                                    "                    \"outputs\": [\n" +
                                                    "                        \"output\"\n" +
                                                    "                    ],\n" +
                "                                                    \"config\": aaaa\n" +
                "                                              }\n" +
                "                                         ]\n" +
                "                             }\n" +
                "                    ]\n" +
                "          }";



        crossLines = crossLines.replace("49651",   sourceID.toString());

        crossLines = crossLines.replace("aaaa", data.toJSONString());

        log.info("jsonStrDirectionLines{}", crossLines);

        PointerByReference input = KesonUtils.stringToKeson(crossLines);
        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

    }


    public static String[] toArrayStringRi(List<Integer[][]> arr) {
        int numRows = arr.size();
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr.get(i));
        }
        return result;
    }

    private void updateRoiAll(String[] json1,  List<List<Number>>  roi3, Pointer pipelinePoint, VideoStreamInfra deviceInfra) {

        JSONArray json2 = JSONArray.parseArray(roi3.toString());
        // json3 = new JSONArray();
        //for (int i = 0; i < 1; i++) {
            JSONArray lines = new JSONArray();
            String[] lineArr = json1[0].replaceAll("\\[|\\]", "").split(",");
            for (int j = 0; j < lineArr.length; j += 4) {
                JSONObject line = new JSONObject();
                line.put("x1", Integer.parseInt(lineArr[j].trim()));
                line.put("y1", Integer.parseInt(lineArr[j + 1].trim()));
                line.put("x2", Integer.parseInt(lineArr[j + 2].trim()));
                line.put("y2", Integer.parseInt(lineArr[j + 3].trim()));
                lines.add(line);
            }
            JSONArray positiveDirection = new JSONArray();
            JSONArray directionArr = json2.getJSONArray(0);
            for (int k = 0; k < directionArr.size(); k += 4) {
                JSONObject point = new JSONObject();
                point.put("x1", directionArr.get(k));
                point.put("y1", directionArr.get(k + 1));
                point.put("x2", directionArr.get(k + 2));
                point.put("y2", directionArr.get(k + 3));
                positiveDirection.add(point);
            }
            JSONObject data = new JSONObject();
           // data.put("id", i);
            data.put("positive_direction", positiveDirection.getJSONObject(0));
            //data.put("border_offset", 300);
            data.put("lines", lines);
         //   json3.add(data);
        //}


        String crossLines =" {\n" +
                "                \"streams\": [\n" +
                "                           {\n" +
                "                                \"name\": \"video_face_body_analyze_stream\",\n" +
                "                                \"modules\": [\n" +
                "                                               {\n" +
                                                "                    \"name\": \"cross_line\",\n" +
                                                "                    \"source_id\": 49651,\n" +
                                                "                    \"type\": \"FacebodyCrossline\",\n" +
                                                "                    \"parallel_group\":\"target_analysis\",\n"+
                                                "                    \"inputs\": [\n" +
                                                "                        \"analyzed_face_targets\"\n" +
                                                "                    ],\n" +
                                                "                    \"outputs\": [\n" +
                                                "                        \"output\"\n" +
                                                "                    ],\n" +
                "                                                    \"config\": aaaa\n" +
                "                                              }\n" +
                "                                         ]\n" +
                "                             }\n" +
                "                    ]\n" +
                "          }";



        Long sourceId = Utils.keyToContextId(deviceInfra.getDeviceId());

        crossLines = crossLines.replace("49651",   sourceId.toString());

        crossLines = crossLines.replace("aaaa", data.toJSONString());

        log.info("jsonStrDirectionLines{}", crossLines);

        PointerByReference input = KesonUtils.stringToKeson(crossLines);
        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

    }
    public void updateRoi(String policyRoiString, Pointer pipelinePoint, String sourceId){
        PointerByReference out = new PointerByReference();
        String controlPipeStrng =" {\n" +
                "                \"streams\": [\n" +
                "                        {\n" +
                "                                \"name\": \"video_face_body_track_stream\",\n" +
                "                                \"modules\": [\n" +
                "                                              {\n" +
                "                                                \"name\": \"roi_filter\",\n" +
                "                                                \"source_id\": 49650,\n" +
                "                                                \"type\": \"RoiFilter\",\n" +
                "                                                \"parallel_group\": \"parallel_group\",\n" +
                "                                               \"inputs\": [\n" +
                "                                                   \"associated_facebodys\"\n" +
                "                                                ],\n" +
                "                                               \"outputs\": [\n" +
                "                                                     \"filtered_associated_facebodys\"\n" +
                "                                                ],\n" +

                "                                                \"config\": {\n" +
                "                                                    \"roi_filter\": [\n" +
                "                                                        {\n" +
                "                                                            \"label_id\": 221488,\n" +
                "                                                            \"polygons\": aaaa\n" +
                "                                                        }\n" +
                "                                                    ]\n" +
                "                                                }\n" +
                "                                           }\n" +
                "                                   ]\n" +
                "                         }\n" +
                "                ]\n" +
                "        }";
        controlPipeStrng = controlPipeStrng.replace("aaaa",policyRoiString);
        controlPipeStrng = controlPipeStrng.replace("49650",sourceId);
        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);


        log.info("update roi_filter device is  {} roi is  {}", sourceId,controlPipeStrng);

    }


    @SuppressWarnings({"unchecked"})
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return false;

        long source_id = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        float threshold = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getThreshold(), 0f);
        int minSize = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getMinSize(), Integer.MIN_VALUE);
        Pointer kesonTargetsValidate = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 1), "targets");
        int kesonTarget_size_validate = KestrelApi.keson_array_size(kesonTargetsValidate);


        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");

        /**
         * Start processing each selected frame
         */
        List<Map<String, Object>> tracklets = (List<Map<String, Object>>) detectResult.get(1).get("targets");

        boolean checkCross = tracklets.stream()
                .anyMatch(track -> {
                    List<Number> inIds = (List<Number>) track.getOrDefault("in_track_ids", List.of());
                    List<Number> outIds = (List<Number>) track.getOrDefault("out_track_ids", List.of());
                    return inIds.size() > 0 || outIds.size() > 0;
                });

        if(checkCross){
            log.info("tracklets{}", tracklets);
        }

        return checkCross && !tracklets.isEmpty();
    }


    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return;
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        long source_id = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        List<Map<String, Object>> tracklets = (List<Map<String, Object>>) detectResult.get(1).getOrDefault("targets", List.of());

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 1), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        for (Map<String, Object> target : tracklets) {
            Map<String, Object> output = new HashMap<String, Object>();

            output.put("label", target.get("label"));
            output.put("position", target.get("position"));
            output.put("confidence", target.get("confidence"));
            output.put("targetRoi", target.get("roi"));
            if (target.containsKey("aligner_confidence")) {
                output.put("alignerConfidence", target.get("aligner_confidence"));
            }
            if (target.containsKey("in_track_ids")) {
                output.put("inTrackIds", target.get("in_track_ids"));
            }
            if (target.containsKey("out_track_ids")) {
                output.put("outTrackIds", target.get("out_track_ids"));
            }
            if (target.containsKey("integrate_quality")) {
                output.put("integrateQuality", target.get("integrate_quality"));
            }
            if (target.containsKey("track_id")) {
                output.put("track_id", target.get("track_id"));
            }
            if (target.containsKey("yaw") && target.containsKey("pitch")) {
                output.put("head_pose", Map.of("yaw", target.get("yaw"), "pitch", target.get("pitch"), "roll", target.get("roll")));
            }
            if (target.containsKey("status")) {
                output.put("status", target.get("status"));
            }
            output.put("sourceId", source_id);

            Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                    .limit(kesonTarget_size)
                    .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "id")) == ((Number) target.get("id")).longValue())
                    .findAny()
                    .orElse(null);

            if (kesonTarget != null) {
                PointerByReference targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                if (targetImage.getValue() != null) {

                    output.put("personInfo", findoutWhoItIs(targetImage.getValue(),((Number) target.get("track_id")).intValue(), ((Number)target.get("quality")).floatValue(), modelResult.getModelRequest().getParameter().get("deviceId").toString()) );

                    String targetImagePath = FrameUtils.save_image_as_jpg(targetImage.getValue(), ImageUtils.newFile(annotatorName()),processor.getImgSaveTag());
                    output.put("targetImage", targetImagePath);
                }

                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "scene_frame"));
                if (sceneImage.getValue() != null) {
                    String sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()),processor.getImgSaveTag());
                    output.put("sceneImage", sceneImagePath);
                }
            }

            outputResult.add(output);
        }

        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
        modelResult.setOutputResult(outputResult);
    }

    @Getter
    private  static  final class TrackPerson{

        private volatile Pointer faceFrame;

        @Setter
        private volatile String personId;

        @Setter
        private volatile Float personScore;

        @Setter
        private volatile String targetType;

        public TrackPerson() {

        }
    }

    @SuppressWarnings("unchecked")
    private final TrackPerson findoutWhoItIs(Pointer videoFrame, int trackId, float quality, String deviceId) {

        TrackPerson track = new TrackPerson();

        if(videoFrame == null )
            return track;

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> data = new LinkedMultiValueMap<String, String>();

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(data, headers);

            String figureImageBase64 = ImageUtils.bytesToBase64(FrameUtils.encode_image_as_jpg(videoFrame));

            data.add("figureImageBase64", figureImageBase64);

            JSONObject response = restTemplate.postForObject("http://cognitivesvc/cognitive/face/compareFaceImage", request , JSONObject.class);
            List<Map<String, Object>> persons = (List<Map<String, Object>>)response.getOrDefault("data", List.of());
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(persons)) {
                track.setPersonId(persons.get(0).get("personID").toString());
                track.setTargetType(persons.get(0).get("targetType").toString());
                track.setPersonScore(((Number)persons.get(0).get("score")).floatValue());
            }else{
                //add passer
                MultiValueMap<String, Object> dataStranger = new LinkedMultiValueMap<String, Object>();

                HttpEntity<MultiValueMap<String, Object>> requestStranger = new HttpEntity<MultiValueMap<String, Object>>(dataStranger, headers);

                dataStranger.add("figureImageBase64", figureImageBase64);
                dataStranger.add("trackId", trackId);
                dataStranger.add("quality", quality);
                dataStranger.add("deviceId", deviceId);

                JSONObject responseSnger = restTemplate.postForObject("http://cognitivesvc/cognitive/face/strangerIsHere", requestStranger , JSONObject.class);
                Map<String, Object> personsStrange = (Map<String, Object>) responseSnger.getOrDefault("data", Map.of());
                if(!personsStrange.isEmpty()) {
                    track.setPersonId(personsStrange.get("personUUID").toString());
                    track.setTargetType("Passer");
                    track.setPersonScore(1.0f);
                }
            }
        }catch(Exception e) {
            e.printStackTrace();
        }

        return track;
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("crossLins start context [" + contextId + "].");


        deviceRoiMapCrossFaceBody.put(contextId,  new ConcurrentHashMap<String,String>());

    }
    private void stopContext(String deviceId, long contextId) {

        log.info("crossLins stop deviceId [" + deviceId + "].");

        deviceRoiMapCrossFaceBody.remove(contextId);
    }


    public static String[] toArrayStringRoiList(List<Integer[][]> arr) {
        int numRows = arr.size();
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr.get(i));
        }
        return result;
    }
    @SuppressWarnings({ "unchecked", "rawtypes" })
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if(e != null)
            for(Map<String, Object> param : (List<Map<String, Object>>)e) {
                if(!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer)param.get("roiIndex");
                if(roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };


    @SuppressWarnings("unchecked")
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return result;

        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();

        long contextId = Utils.keyToContextId(deviceId);

        CvScalar color = opencv_core.CV_RGB(255, 0,0 );

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        if(ArrayUtils.isEmpty(polygons))
            polygons = new Polygon[] {new Polygon(new int[] {0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[] {0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};


        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);

        for(int indexs = 0 ;indexs < polygons.length; indexs ++) {
            Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
            if (extra.isEmpty())
                continue;

            if(extra.containsKey("crossDot")){

                List<Number> crossDot = (List<Number>)extra.getOrDefault("crossDot", List.of());
                if(crossDot.size() > 2) {
                    result.add(Line.builder().thickness(3).color(color).from(new int[]{crossDot.get(0).intValue(), crossDot.get(1).intValue()}).to(new int[]{crossDot.get(2).intValue(), crossDot.get(3).intValue()}).build());
                }

            }
        }

        color = opencv_core.CV_RGB(0, 0,255 );

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).getOrDefault("targets", List.of());
        for (Map<String, Object> target : targets) {
            int label = ((Number) target.getOrDefault("label", -1)).intValue();
            int trackId = ((Number) target.getOrDefault("track_id", -1)).intValue();
            if (trackId < 0)
                continue;

            if (label == 221488) {
                long sourceId = ((Number) target.get("source_id")).longValue();
                Map<String, Integer> roi = (Map<String, Integer>) target.get("roi");

                result.add(Rect.builder().thickness(1).processor(annotatorName()).color(color).text("BY" + trackId).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());

            } else {
                Map<String, Integer> roi = (Map<String, Integer>) target.get("roi");
                result.add(Rect.builder().thickness(1).processor(annotatorName()).color(color).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());
            }
        }

        color = opencv_core.CV_RGB(0, 255,0 );
        for (Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for (int index = 0; index < roi.length - 1; index++) {
                result.add(Line.builder().thickness(2).color(color).from(new int[]{roi[index][0], roi[index][1]}).to(new int[]{roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().thickness(2).color(color).from(new int[]{roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[]{roi[0][0], roi[0][1]}).build());
        }

        return result;
    }

    private static final String STARTAREA = "start";

    private static final String ROISTRING = "roi";

    private static final String EXCLUDEAREA = "exclude";

    private static final String ENDAREA = "end";


}
