package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.HostUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OnnxDetection extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    
    private final ConcurrentHashMap<Thread, Pointer> pointerMap = new ConcurrentHashMap<Thread, Pointer>();
    
    @Getter
    protected final Integer interval = 50;

    @Getter
    protected final String decoderFormat = "rgb24";
    
    @Getter
    protected final Integer frameBuffer = 12;
    
    @Getter
    protected final String frameBufferStrategy = "smart";
    
    @Override
    public ModelResult handle(ModelRequest modelRequest) {
        ModelResult result = ModelResult.builder().modelRequest(modelRequest).build();
        if (status != 0)
            return result;
                        
        BatchItem batchItem = BatchItem.builder().modelRequest(modelRequest).build();
        
        try {
            if(extractQueue(modelRequest.getParameter()).offer(batchItem)) {
                while(status == 0 && !batchItem.getLatch().await(1, TimeUnit.SECONDS))
                    ;
                monitor.getAndIncrementHandled();
            }else {
                monitor.getAndIncrementUnHandled();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            result.setKeson(batchItem.getKeson());
        }
        
        return result;
    }
    
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];
        
        Pointer param = KestrelApi.keson_create_object();
        KestrelApi.keson_add_item_to_object(param, "cutoff", KestrelApi.keson_create_double(0.05));
        KestrelApi.keson_add_item_to_object(param, "save_mask_to_npy", KestrelApi.keson_create_bool(0));
        
        Pointer image_array = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_object(param, "images", image_array);
        
        for (int index = 0; index < handlingList.size(); index ++) {
            Pointer imageObj = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(image_array, imageObj);
            Pointer frame = handlingList.get(index).getModelRequest().getVideoFrames()[0].getCpuFrame();
            KestrelApi.keson_add_item_to_object(imageObj, "name", KestrelApi.keson_create_string("index" + (index)));
            KestrelApi.keson_add_item_to_object(imageObj, "image", KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), frame));
        }

        Pointer pointer = pointerMap.get(Thread.currentThread());
        if(pointer == null)
            return new PointerByReference[0];
        
        PointerByReference output_keson = new PointerByReference();
        KestrelApi.kestrel_annotator_process(pointer, param, output_keson);
        KesonUtils.kesonDeepDelete(param);
        
        return new PointerByReference[] {output_keson};
    }
    
    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        if(ArrayUtils.isEmpty(outputKesons))
            return ;
        
        PointerByReference keson = new PointerByReference(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), outputKesons[0].getValue()));
        handlingList.get(0).setKeson(keson);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || modelResult.getKeson().getValue() == null)
            return ;
        
        Object detectResult = KesonUtils.kesonToJson(modelResult.getKeson());
        
        Map<String, Object> additional = (Map<String, Object>)Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
        for(KestrelInterceptor interceptor : interceptors)
            interceptor.postReadDetectResult(additional, (PointerByReference)modelResult.getKeson(), (Map<String, Object>)detectResult);
        
        modelResult.setDetectResult(detectResult);
    }
    
	@Override
    public boolean validateOutputValue(ModelResult modelResult) {
        return true;
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public void buildOutputValue(ModelResult modelResult) {
    	List<Map<String, Object>> detectResult = (List<Map<String, Object>>)modelResult.getDetectResult();
    	List<Map<String, Object>> classifiers  = (List<Map<String, Object>>)detectResult.get(0).get("result");
    	Map<String, Object> yes = classifiers.stream().filter(clazz -> Objects.equals("Yes", clazz.get("label_name"))).findAny().get();
    	float yesScore = ((Number)yes.get("score")).floatValue();
    	
    	List<Map<String, Object>> outputResults = new ArrayList<Map<String, Object>>();
    	Map<String, Object> outputResult = new HashMap<String, Object>();
    	outputResults.add(outputResult);
    	
    	outputResult.put("deviceId", modelResult.getModelRequest().getParameter().get("deviceId"));
    	outputResult.put("confidence", yesScore);
    	outputResult.put("detect", Map.of("left", 0, "top", 0, "width", KestrelApi.kestrel_frame_video_width(modelResult.getModelRequest().getVideoFrames()[0].getFrame()), "height", KestrelApi.kestrel_frame_video_height(modelResult.getModelRequest().getVideoFrames()[0].getFrame())));
    	outputResult.put("targetType", 1420);
    	outputResult.put("attributes", List.of(Map.of("key", "label_name", "value", "yes", "confidence", yesScore), Map.of("key", "label_name", "value", "no", "confidence", 1 - yesScore)));
    	
        modelResult.setOutputResult(outputResults);
    }
    
    @Override
    @PostConstruct
    public synchronized void initialize() {
        log.info("***************************************************");
        log.info("initializing [" + annotatorName() + "]");
        log.info("***************************************************");
        
        Initializer.bindDeviceOrNot();

        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + handlerEntity.getAttacheds()[1] + " " + "/usr/cognitivesvc/libonline_communicator.so.2;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + handlerEntity.getAttacheds()[0] + " " + "/usr/cognitivesvc/kestrel_pplnn.kep;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + handlerEntity.getAttacheds()[0] + " " + "/usr/cognitivesvc/kestrel_pplnn.kep.kep;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + handlerEntity.getAttacheds()[3] + " " + "/usr/cognitivesvc/pos_mmdetection.kep;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + handlerEntity.getAttacheds()[3] + " " + "/usr/cognitivesvc/pos_mmdetection.kep.kep;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + handlerEntity.getAttacheds()[4] + " " + "/usr/cognitivesvc/pos_mmclassification.kep;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + handlerEntity.getAttacheds()[4] + " " + "/usr/cognitivesvc/pos_mmclassification.kep.kep;"});

        KestrelApi.kestrel_plugin_load(handlerEntity.getAttacheds()[0], "");

        plugins = new String[1];
        plugins[0] = KestrelApi.kestrel_plugin_load(handlerEntity.getAttacheds()[3], "");
        
        holders = new ModelHolder[1];
        holders[0] = new ModelHolder(plugins[0], handlerEntity.getAttacheds()[5], 1, 1, true, false, true);
        
        startHandle();
    }
    
    @PreDestroy
    @Override
    public synchronized void destroy() {
        log.info("***************************************************");
        log.info("destroying xmodel [" + annotatorName() + "]");
        log.info("***************************************************");
        
        stopHandle();
        
        KestrelApi.kestrel_plugin_unload(plugins[0]);
        
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "rm -f /usr/cognitivesvc/kestrel_pplnn.kep;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "rm -f /usr/cognitivesvc/libonline_communicator.so.2;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "rm -f /usr/cognitivesvc/pos_mmdetection.kep;"});
        HostUtils.runLinux(new String[] {"/bin/sh", "-c", "rm -f /usr/cognitivesvc/pos_mmclassification.kep;"});
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> drawings = new ArrayList<Drawing>();

        List<Map<String, Object>> outputResult = (List<Map<String, Object>>)modelResult.getOutputResult();
        if(CollectionUtils.isNotEmpty(outputResult)) {
            int width = KestrelApi.kestrel_frame_video_width(modelResult.getModelRequest().getVideoFrames()[0].getCpuFrame());
            int height = KestrelApi.kestrel_frame_video_height(modelResult.getModelRequest().getVideoFrames()[0].getCpuFrame());
            
            List<Map<String, Object>> result = (List<Map<String, Object>>)outputResult.get(0).get("result");
            Map<String, Object> maxResult = result.stream().max((l, r) -> Float.compare(((Number)l.get("score")).floatValue(), ((Number)r.get("score")).floatValue())).get();
            
            String labelName = maxResult.get("label_name").toString();
            drawings.add(Rect.builder().processor(annotatorName()).text(labelName).left(width / 2).top(height / 2 - 60).width(3).height(3).build());
            drawings.add(Rect.builder().processor(annotatorName()).text(labelName).left(width / 2).top(height / 2 - 40).width(3).height(3).build());
            drawings.add(Rect.builder().processor(annotatorName()).text(labelName).left(width / 2).top(height / 2 - 20).width(3).height(3).build());
            drawings.add(Rect.builder().processor(annotatorName()).text(labelName).left(width / 2).top(height / 2     ).width(3).height(3).build());
            drawings.add(Rect.builder().processor(annotatorName()).text(labelName).left(width / 2).top(height / 2 + 20).width(3).height(3).build());
            drawings.add(Rect.builder().processor(annotatorName()).text(labelName).left(width / 2).top(height / 2 + 40).width(3).height(3).build());
            drawings.add(Rect.builder().processor(annotatorName()).text(labelName).left(width / 2).top(height / 2 + 60).width(3).height(3).build());
        }
        
        return drawings;
    }
    
    @Override
    public void run() {
        Initializer.bindDeviceOrNot();
        
        pointerMap.put(Thread.currentThread(), initPointer());
        
        super.run();
        
        Initializer.bindDeviceOrNot();
        
        Pointer pointer = pointerMap.remove(Thread.currentThread());
        if(pointer != null) {
            KestrelApi.kestrel_annotator_terminate(pointer, null, null);
            KestrelApi.kestrel_annotator_close(new PointerByReference(pointer));
        }
    }
    
    @Override
    protected synchronized void startHandle() {
        if (xModelThreads != null)
            return ;
        
        status = 0;
        
        xModelThreads = new Thread[1];
        latch = new CountDownLatch(xModelThreads.length);
        
        for (int index = 0; index < xModelThreads.length; index++) {
            Thread xModelThread = new Thread(Utils.cogGroup, this);
            xModelThread.setDaemon(true);
            xModelThread.setPriority(Thread.NORM_PRIORITY + 2);
            xModelThread.setName("XModel[" + annotatorName() + "]" + "-No." + index);
            xModelThread.start();
            
            xModelThreads[index] = xModelThread;
        }
    }
    
    private final Pointer initPointer() {
        Initializer.bindDeviceOrNot();
        
        return KestrelApi.kestrel_annotator_open(plugins[0], "{"
                    + "\"model\":\"" + handlerEntity.getAttacheds()[5] + "\","
                    + "\"max_batch_size\":" + 1 + ","
                    + "\"license\": \"" + handlerEntity.getAttacheds()[2] + "\","
                    + "\"product_name\":\"parrots-os-sdk-test\"}");
    }
}
