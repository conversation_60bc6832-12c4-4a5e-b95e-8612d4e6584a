package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;


import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Scalar;
import org.bytedeco.opencv.opencv_imgproc.CvFont;

import java.awt.*;
import java.io.File;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Stream;

@Slf4j
public class AreaCountHeadPoint extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    @Getter
    protected final Integer interval = 0;//检一跳三

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final boolean queueMap = true;

    @Getter
    protected final Integer frameBuffer = 20;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";



    private final ConcurrentHashMap<Long, Long> trackAreaMap = new ConcurrentHashMap<Long, Long>();

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        //log.info("outputKesons{}", KesonUtils.kesonToJson(outputKesons[0]));
        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons)[0], imageIds, "channel_id");

        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {

        List<Map<String, Object>> targets = (List<Map<String, Object>>)Objects.requireNonNullElse((Map<String, Object>)modelResult.getDetectResult(), Map.of()).getOrDefault("targets", List.of());

        return !targets.isEmpty();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        if(ArrayUtils.isEmpty(polygons))
            polygons = new Polygon[] {new Polygon(new int[] {0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[] {0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};

        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);

        List<Map<String, Object>> roiTargets[] = new List[polygons.length];
        for(int index = 0 ;index < polygons.length; index ++)
            roiTargets[index] = new ArrayList<Map<String, Object>>();

        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();
        if(MapUtils.isEmpty(detectResult))
            return ;

        boolean opencv = false;

        Pointer kesonTargets = KestrelApi.keson_get_object_item(modelResult.getKeson().getValue(), "targets");

        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.get("targets");
//
//        log.info("targetsArea{}", targets);

        String sceneImagePath = "";
        for(Map<String, Object> target : targets) {

            //
            for(Map<String, Object> positions :  (List<Map<String, Object>>)target.get("positions")) {

                int x = ((Number) ((Map<String, Object>)positions.get("point")).get("x")).intValue();
                int y = ((Number) ((Map<String, Object>)positions.get("point")).get("y")).intValue();

                for (int index = 0; index < polygons.length; index++)
                    if (polygons[index].contains(x, y))
                        roiTargets[index].add(positions);
            }

            Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                    .limit(1)
                    .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "channel_id")) == ((Number) target.get("channel_id")).longValue())
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "image_id")) == ((Number) target.get("image_id")).longValue())
                    .findAny()
                    .orElse(null);

            if (kesonTarget != null && opencv) {

                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                if (sceneImage.getValue() != null) {
                    sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                }
            }
        }
        List<String> roiIds = (List<String>)((List<Map<String, Object>>)Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getExtras(), List.of()))
                .stream()
                .filter(item -> "roiIds".equals(item.get("type")))
                .findAny()
                .orElse(Map.of())
                .getOrDefault("roiIds", List.of());

        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for(int index = 0 ;index < polygons.length; index ++) {
            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());

            Number minAlert = (Number)extrasMap.get("minAlert");
            Number maxAlert = (Number)extrasMap.get("maxAlert");

            List<Map<String, Object>> roiTarget = roiTargets[index];
            Map<String, Object> round = new HashMap<String, Object>();

            if(maxAlert != null && minAlert != null) {
                if(roiTarget.size() >= minAlert.intValue() && roiTarget.size() <= maxAlert.intValue())
                    round.put("maxAlert", roiTarget.size());
            }else if(maxAlert != null){
                if(roiTarget.size() <= maxAlert.intValue())
                    round.put("maxAlert", roiTarget.size());
            }else if(minAlert != null){
                if(roiTarget.size() >= minAlert.intValue())
                    round.put("maxAlert", roiTarget.size());
            }else {
                round.put("maxAlert", roiTarget.size());
            }
            if(!round.isEmpty() && roiTarget.size() > 0) {
                round.put("targets", roiTarget);
                round.put("roiId", (!roiIds.isEmpty()) ? roiIds.get(index): 0);
                round.put("rois", (processor.getRoi()!=null && processor.getRoi().length > 0) ? processor.getRoi()[index]:new int[]{});
                round.put("roiIndex", index);
                result.add(round);
            }
        }
        if(!result.isEmpty()) {
//           String  sceneImagePath = FrameUtils.save_image_as_jpg( modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame(), ImageUtils.newFile("areaCountHP"));

            if(!sceneImagePath.isEmpty()) {
                log.info("sceneImagePath{}",sceneImagePath);
                Mat sourceImage = null;
                sourceImage = opencv_imgcodecs.imread(sceneImagePath);

                for (Map<String, Object> res :result){
                    res.put("image", sceneImagePath);


                    if(opencv) {
                        List<Map<String, Object>> resTarget = (List<Map<String, Object>>) res.get("targets");

                        for (Map<String, Object> targetXY : resTarget) {

                            int x = ((Number) ((Map<String, Object>) targetXY.get("point")).get("x")).intValue();
                            int y = ((Number) ((Map<String, Object>) targetXY.get("point")).get("y")).intValue();

                            org.bytedeco.opencv.opencv_core.Rect rect = new org.bytedeco.opencv.opencv_core.Rect(Math.max(0, x), Math.max(0, y), 50, 50);
                            opencv_imgproc.rectangle(sourceImage, rect, new Scalar(0, 255, 255, 0), 1, opencv_imgproc.LINE_AA, 0);

                            org.bytedeco.opencv.opencv_core.Point org = new org.bytedeco.opencv.opencv_core.Point(rect.x() + rect.width(), rect.y() + rect.height());

                        }
                    }

                }
                if(opencv && !sceneImagePath.isBlank()) {
                    String fileName = sceneImagePath.substring(sceneImagePath.lastIndexOf('/') + 1);
                    File path = new File(Utils.instance.savePath + "/opencv/" + Objects.requireNonNullElse(ImageUtils.dateFormatter_DAY.get().format(new Date()), "/") + annotatorName());
                    if (!path.exists())
                        path.mkdirs();
                    // 保存结果图片
                    opencv_imgcodecs.imwrite(path.getAbsolutePath() + "/" + fileName + "_opencv_target.jpg", sourceImage);
                }
            }

            modelResult.setOutputResult(result);


        } else {
            modelResult.setOutputResult(null);
        }

        if(opencv) {
            modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
        }

        postProcessOutputValue(modelResult);

    }


    @SuppressWarnings("unchecked")
    @Override
    protected void postProcessOutputValue(ModelResult modelResult) {
        super.postProcessOutputValue(modelResult);

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        if (StringUtils.isBlank(deviceId) || processor == null || CollectionUtils.isEmpty(processor.getExtras()))
            return;

        Long contextID =Utils.keyToContextId(deviceId);
        long now = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);

        Polygon[] polygons = processor.fetchPolygons();

        boolean toRemove = false;
        for(int index = 0 ;index < polygons.length; index ++) {

            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());

            if(extrasMap.containsKey("trigger")) {
                Map<String, Object> trigger = (Map<String, Object>) extrasMap.get("trigger");

                int trackFreq = (int) trigger.getOrDefault("trackFreq", 1000);

                if( trackAreaMap.get(contextID) !=null && (now -  trackAreaMap.getOrDefault(contextID, 0L) < trackFreq)) {
                    log.info("LiteFalcon checkFail {}, {}, {}, {}", deviceId, now, trackAreaMap.getOrDefault(contextID, 0L), trackFreq);
                    toRemove = true;
                }
            }
        }


        if(toRemove){
            modelResult.setOutputResult(null);
            return;
        }

        trackAreaMap.put(contextID, now);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {

        List<Drawing> result = new ArrayList<Drawing>();

        CvScalar color = opencv_core.CV_RGB(0, 0, 255); // red
        CvScalar colorRoi = opencv_core.CV_RGB(255, 255,0);
        CvFont cvFont = opencv_imgproc.cvFont(2, 2);
        List<Map<String, Object>> outputResults = (List<Map<String, Object>>)modelResult.getOutputResult();

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        for(Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for(int index = 0; index < roi.length - 1; index ++)
                result.add(Line.builder().color(color).from(new int[] {roi[index][0], roi[index][1]}).to(new int[] {roi[index + 1][0], roi[index + 1][1]}).build());

            result.add(Line.builder().color(color).from(new int[] {roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[] {roi[0][0], roi[0][1]}).build());

        }

        if(outputResults !=null) {
            int areaInt = 1;
            for (Map<String, Object> outputResult : outputResults) {

                List<Map<String, Object>> targets = (List<Map<String, Object>>) outputResult.get("targets");

                for (Map<String, Object> target :targets){
                    int x = ((Number) ((Map<String, Object>)target.get("point")).get("x")).intValue();
                    int y = ((Number) ((Map<String, Object>)target.get("point")).get("y")).intValue();
                    result.add(Rect.builder().processor(annotatorName()).thickness(2).left(x).top(y).width(50).height(50).build());

                }
                Number maxAlert = (Number) outputResult.get("maxAlert");

                Integer[][] rois = (Integer[][]) outputResult.get("rois");

                Number roiIndex = (Number) outputResult.get("roiIndex");

                int minLeft = Integer.MAX_VALUE;
                int minTop = Integer.MAX_VALUE;

                for (Integer[] point : rois) {
                    int left = point[0];
                    int top = point[1];
                    if (left < minLeft) {
                        minLeft = left;
                    }
                    if (top < minTop) {
                        minTop = top;
                    }
                }
                if(roiIndex.intValue() == 0){
                    minTop = minTop + 20;
                }

                result.add(Rect.builder().color(colorRoi).textFont(cvFont).text("area" + areaInt +":"+ maxAlert.intValue()).color(color).left(minLeft).top(minTop).width(0).height(0).build());
                areaInt ++;

            }
        }


        return result;
    }


    @SuppressWarnings({ "unchecked", "rawtypes" })
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if(e != null)
            for(Map<String, Object> param : (List<Map<String, Object>>)e) {
                if(!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer)param.get("roiIndex");
                if(roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };


    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            long contextId = Utils.keyToContextId(event.getDeviceId());

            try { Thread.sleep(1000); } catch (InterruptedException x) { }
            stopContext(contextId);
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("areaCountHeadPoint [" + getClass().getSimpleName() + "] start context [" + contextId + "].");
        trackAreaMap.put(contextId,  System.currentTimeMillis() );
    }

    private void stopContext(long contextId) {
        holders[0].controlForRemoveSource(contextId);
        log.info("areaCountHeadPoint [" + getClass().getSimpleName() + "] stop context [" + contextId + "].");
        if(!trackAreaMap.containsKey(contextId))
            return ;

        trackAreaMap.remove(contextId);
        
        controlForRemoveSource(contextId);
    }

    public PointerByReference controlForRemoveSource(long contextId) {
        if(holders[0].pointers[0] == null)
            return new PointerByReference();

        String aa = "{\n" +
                "  \"streams\": [\n" +
                "    {\n" +
                "      \"name\": \"count_pipeline\",\n" +
                "      \"source_id\": 1926064092,\n" +
                "      \"modules\": [\n" +
                "        {\n" +
                "          \"name\": \"original_input\",\n" +
                "          \"source_id\": 1926064092\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"crowd_input\",\n" +
                "          \"source_id\": 1926064092\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"localization\",\n" +
                "          \"source_id\": 1926064092\n" +
                "        },\n" +
                "        {\n" +
                "          \"name\": \"output\",\n" +
                "          \"source_id\": 1926064092\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        aa = aa.replace("1926064092",String.valueOf(contextId));
        PointerByReference input = KesonUtils.stringToKeson(aa);
//                Pointer input = KesonUtils.buildFlockRemoveInput(contextId, key);
        PointerByReference out = new PointerByReference();

        for(int index = 0; index < holders[0].pointers.length; index++) {
            synchronized(this){
                KestrelApi.flock_pipeline_control(holders[0].pointers[index], KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), out);
            }
        }

        KesonUtils.kesonDeepDelete(input);
        return out;
    }


}
