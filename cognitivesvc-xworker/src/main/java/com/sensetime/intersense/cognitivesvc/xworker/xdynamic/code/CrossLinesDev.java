package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Scalar;

import java.awt.*;
import java.io.File;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
@SuppressWarnings("unused")
public class CrossLinesDev extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {


    @Getter
    protected final Integer interval = 0;//检一跳三

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final boolean queueMap = true;


    protected  final ConcurrentHashMap<Long,  Map<String, Integer>> trackingMapOutCount = new ConcurrentHashMap<Long, Map<String, Integer>>();
    protected final ConcurrentHashMap<Long, Map<Long, Track>> trackingMap = new ConcurrentHashMap<Long, Map<Long, Track>>();
    protected ConcurrentHashMap<Long, Map<String,String>> deviceRoiMapCross = new ConcurrentHashMap<Long, Map<String,String>>();


    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
//        String[] imageIdsDevice = handlingList.stream().map(item ->item.getModelRequest().getParameter().get("deviceId")).toArray(String[]::new);
//
//        log.info("batch_handle_extract_result{},{},{},{}", annotatorName(), handlingList.size(),imageIdsDevice, outputKesons.length);

//        PointerByReference[] tryReformFlockKeson = KesonUtils.tryReformFlockKeson(outputKesons[0]);
//
//        log.info("DetectResult_tryReformFlockKeson{},{}",tryReformFlockKeson.length, KesonUtils.kesonToJson(tryReformFlockKeson[0]));
//        PointerByReference[] sub_kesons = outputKesons;
//        for (int index = 0; index < handlingList.size(); index++) {
//            BatchItem item = handlingList.get(index);
//            item.setKeson(sub_kesons[index]);
//        }

        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds, "source_id");

        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();

            if (sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));


            handlingList.get(index).setKeson(new PointerByReference(array));
        }

        KesonUtils.kesonDeepDelete(outputKesons);


    }

    /**
     * 按照 函数包含的模型 顺序的跑一边
     */
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];

        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);

        String deviceId = (String)handlingList.get(0).getModelRequest().getParameter().get("deviceId");
        VideoStreamInfra deviceInfra = (VideoStreamInfra) handlingList.get(0).getModelRequest().getParameter().get("deviceInfra");

        Long streamSourceId = Utils.keyToContextId(deviceId);

        Map<String, Object> additional = (Map<String, Object>) Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
        for (int index = 0; index < pointers.length; index++) {


            long now = System.currentTimeMillis();

            if (index == 0) {

                Map<String, String> roiCross = deviceRoiMapCross.get(streamSourceId);

                Integer[][][] proi = handlingList.get(index).getModelRequest().getProcessor().getRoi();

                CognitiveEntity.Processor processor = handlingList.get(index).getModelRequest().getProcessor();
                Polygon[] polygons = processor.fetchPolygons();
                if(ArrayUtils.isEmpty(polygons))
                    polygons = new Polygon[] {new Polygon(new int[] {0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[] {0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};


                Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);

                List<Integer[][]> rois = new ArrayList<>();
                List<Integer[][]> roisDirection = new ArrayList<>();
                List<List<Number>> roisLines = new ArrayList<>();
                for(int indexs = 0 ;indexs < polygons.length; indexs ++) {
                    Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
                    if (extra.isEmpty())
                        continue;
                    Integer[][] singleRoi = proi[indexs];
                    if(singleRoi.length <=0)
                        continue;
                    if(extra.containsKey("crossDot")){
                        roisDirection.add(singleRoi);
                        List<Number> crossDot = (List<Number>)extra.getOrDefault("crossDot", List.of());
//                        String crossDirection = (String)extra.get("crossDirection");
//                        Number endPointX = 0;
//                        Number endPointY = 0;
//
//                        double[][] polygon = new double[singleRoi.length][];
//                        for (int i = 0; i < singleRoi.length; i++) {
//                            polygon[i] = new double[]{singleRoi[i][0], singleRoi[i][1]};
//                        }
//                        double[] newPoint;
//                        if (singleRoi.length > 2) {
//                            // Given shape is a polygon
//                            double[] pointDbl = {crossDot.get(0).intValue(), crossDot.get(1).intValue()};
//                            newPoint = calculatePointForPolygon(pointDbl, polygon, crossDirection);
//                        } else {
//                            // Given shape is a line segment
//                            double[] pointDbl = {crossDot.get(0).intValue(), crossDot.get(1).intValue()};
//                            newPoint = calculatePointForLine(pointDbl, polygon, crossDirection);
//                        }
//                        int[] newPointInt = new int[]{(int) Math.round(newPoint[0]), (int) Math.round(newPoint[1])};
//
//                        crossDot.add(newPointInt[0]);
//                        crossDot.add(newPointInt[1]);

                        roisLines.add(crossDot);
                    }else{
                        rois.add(singleRoi);
                    }
                }
                String policyRoiString = JSON.toJSONString(toArrayStringRoiList(rois));
                String policyRoiStringDirection = JSON.toJSONString(toArrayStringRoiList(roisDirection));
                String policyRoiStringLines = JSON.toJSONString((roisLines));


                if(roiCross.get(ROISTRING) == null){
                    roiCross.put(ROISTRING, policyRoiString);
                    updateRoi(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString());
                }else {
                    String oldRoiString = roiCross.get(ROISTRING);
                    if(!oldRoiString.equals(policyRoiString)){
                        roiCross.put(ROISTRING, policyRoiString);
                        updateRoi(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString());
                    }
                }

                if(roiCross.get(DIRECTION) == null && roiCross.get(LINESTRING) == null){
                    roiCross.put(DIRECTION, policyRoiStringDirection);
                    roiCross.put(LINESTRING, policyRoiStringLines);
                    updateRoiAll(toArrayStringRoiList(roisDirection), roisLines, pointers[index].pointers[0], deviceInfra);
                }else {
                    String oldRoiString2 = roiCross.get(DIRECTION);
                    if(!oldRoiString2.equals(policyRoiStringDirection)){
                        roiCross.put(DIRECTION, policyRoiStringDirection);
                        updateRoiAll(toArrayStringRoiList(roisDirection), roisLines, pointers[index].pointers[0], deviceInfra);
                    }
                    String oldRoiString3 = roiCross.get(LINESTRING);
                    if(!oldRoiString3.equals(policyRoiStringLines)){
                        roiCross.put(LINESTRING, policyRoiStringLines);
                        updateRoiAll(toArrayStringRoiList(roisDirection), roisLines, pointers[index].pointers[0], deviceInfra);
                    }
                }
                monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
            }

            /** 执行模型 获取数据*/
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            pointers[index].process(inTo.getValue(), outOf);


        }

        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }

    private void updateRoiAll(String[] json1,  List<List<Number>>  roi3, Pointer pipelinePoint, VideoStreamInfra deviceInfra) {

        JSONArray json2 = JSONArray.parseArray(roi3.toString());
        JSONArray json3 = new JSONArray();
        for (int i = 0; i < json1.length; i++) {
            JSONArray lines = new JSONArray();
            String[] lineArr = json1[i].replaceAll("\\[|\\]", "").split(",");
            int numOfPoints = lineArr.length / 2;
            if (numOfPoints == 2) {
                JSONObject line = new JSONObject();
                line.put("x1", Integer.parseInt(lineArr[0].trim()));
                line.put("y1", Integer.parseInt(lineArr[1].trim()));
                line.put("x2", Integer.parseInt(lineArr[2].trim()));
                line.put("y2", Integer.parseInt(lineArr[3].trim()));
                lines.add(line);
            } else {
                for (int j = 0; j < numOfPoints; j++) {
                    JSONObject line = new JSONObject();
                    if (j == numOfPoints - 1) {//无需闭环
//                        line.put("x1", Integer.parseInt(lineArr[j * 2].trim()));
//                        line.put("y1", Integer.parseInt(lineArr[j * 2 + 1].trim()));
//                        line.put("x2", Integer.parseInt(lineArr[0].trim()));
//                        line.put("y2", Integer.parseInt(lineArr[1].trim()));
                    } else {
                        line.put("x1", Integer.parseInt(lineArr[j * 2].trim()));
                        line.put("y1", Integer.parseInt(lineArr[j * 2 + 1].trim()));
                        line.put("x2", Integer.parseInt(lineArr[j * 2 + 2].trim()));
                        line.put("y2", Integer.parseInt(lineArr[j * 2 + 3].trim()));
                    }
                    lines.add(line);
                }
            }
            JSONArray positiveDirection = new JSONArray();
            JSONArray directionArr = json2.getJSONArray(i);
            for (int k = 0; k < directionArr.size(); k += 4) {
                JSONObject point = new JSONObject();
                point.put("x1", directionArr.get(k));
                point.put("y1", directionArr.get(k + 1));
                point.put("x2", directionArr.get(k + 2));
                point.put("y2", directionArr.get(k + 3));
                positiveDirection.add(point);
            }
            JSONObject data = new JSONObject();
            data.put("id", i);
            data.put("positive_direction", positiveDirection.getJSONObject(0));
            data.put("border_offset", 300);
            data.put("lines", lines);
            json3.add(data);
        }



        String crossLines =" {\n" +
                "                \"streams\": [\n" +
                "                        {\n" +
                "                                \"name\": \"cross_line_pipeline\",\n" +
                "                                \"modules\": [\n" +
                "                                              {\n" +
                                            "                    \"name\": \"cross_line\",\n" +
                                            "                     \"source_id\": 49651,\n" +
                                            "                    \"type\": \"CrossLine\",\n" +
                                            "                    \"inputs\": [\n" +
                                            "                        \"tracked_targets\"\n" +
                                            "                    ],\n" +
                                            "                    \"outputs\": [\n" +
                                            "                        \"cross_line_targets\"\n" +
                                            "                    ],\n" +
                                            "                    \"config\": {\n" +
                                            "                        \"image_width\": " +((deviceInfra.getRtspWidth()>0)?deviceInfra.getRtspWidth():1920)+",\n" +
                                            "                        \"image_height\": "+ ((deviceInfra.getRtspHeight()>0)?deviceInfra.getRtspHeight():1080) +",\n" +
                                            "                        \"turn_on\": true,\n" +
                                            "                        \"rois\": "+ json3.toJSONString() +
                                            "                    }\n" +
                                            "                }"+
                "                                   ]\n" +
                "                         }\n" +
                "                ]\n" +
                "        }";



        Long sourceId = Utils.keyToContextId(deviceInfra.getDeviceId());

        crossLines = crossLines.replace("49651",   sourceId.toString());

        log.info("jsonStrDirectionLines{}", crossLines);

        PointerByReference input = KesonUtils.stringToKeson(crossLines);
        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

    }

    private void updateRoi(String[] json1, Pointer pipelinePoint, String sourceId) {

        JSONArray json2 = new JSONArray();
        for (int i = 0; i < json1.length; i++) {
            String[] vertexArr = json1[i].replaceAll("\\[|\\]|\\(|\\)", "").split(",");
            StringBuilder vertices = new StringBuilder();
            for (int j = 0; j < vertexArr.length; j += 2) {
                vertices.append("(").append(vertexArr[j].trim()).append(",").append(vertexArr[j + 1].trim()).append(")");
                if (j != vertexArr.length - 2) {
                    vertices.append(",");
                }
            }
            JSONObject roi = new JSONObject();
            roi.put("roi_id", i);
            roi.put("point_cal_type", 1);
            roi.put("vertexes", vertices.toString());
            json2.add(roi);
        }


        String controlPipeStrng =" {\n" +
                "                \"streams\": [\n" +
                "                        {\n" +
                "                                \"name\": \"cross_line_pipeline\",\n" +
                "                                \"modules\": [\n" +
                                                            "        {\n" +
                                                            "            \"name\": \"roi_filter\",\n" +
                                                            "             \"source_id\": 49650,\n" +
                                                            "             \"type\": \"Roifilter\",\n" +
                                                            "             \"inputs\": [\n" +
                                                            "                     \"targets\",\n" +
                                                            "                     \"pers_info\"\n" +
                                                            "               ],\n" +
                                                            "            \"outputs\": [\n" +
                                                            "                 \"filtered_targets\"\n" +
                                                            "             ],\n" +
                                                            "            \"config\": {\n" +
                                                            "                    \"type\": \"FilterHeadBox\",\n" +
                                                            "                    \"cross_line\": {\n" +
                                                            "                       \"detect_result_type\": 2,\n" +
                                                            "                        \"rois\": "+ json2 +
                                                            "                }\n" +
                                                            "            }\n" +
                                                            "        }"+
                "                                   ]\n" +
                "                         }\n" +
                "                ]\n" +
                "        }";


        log.info("jsonStrDirectionRois{}", controlPipeStrng);

        controlPipeStrng = controlPipeStrng.replace("49650",sourceId);
        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);
        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

    }



    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        //log.info("DetectResult{}",  modelResult.getDetectResult());
        if (CollectionUtils.isEmpty(detectResult))
            return false;

        long contextId =Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));


        Map<Long, Track> tracks = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        if(tracks == null)
            return false;


        boolean hasCheckBoxes = false;
        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");



        for (Map<String, Object> target : targets) {

            if(target.containsKey("rois")){
                List<Map<String, Object>> rois = (List<Map<String, Object>>)   target.get("rois") ;
                for (Map<String, Object> roi :rois) {

                    int label  = 0;
                    if(roi.containsKey("boxes")){
                        List<Map<String, Object>> boxes = (List<Map<String, Object>>)   roi.get("boxes") ;
                        if(!boxes.isEmpty()){
                            log.info("boxes{}",  boxes);

                            for (Map<String, Object> box :boxes) {
                                label = ((Number) box.getOrDefault("label", 0)).intValue();
                            }
                        }
                    }

                    if(roi.containsKey("in_track_ids")){

                        Integer[] inIds = JSON.parseObject(roi.get("in_track_ids").toString(), Integer[].class);
                        if(roi.get("in_track_ids") !=null && inIds.length >0) {
                            log.info("in_track_ids{}", (Object) inIds);
                            log.info("targetsResultIn{}", targets);

                            hasCheckBoxes = true;
                            for (Integer trackId: inIds ){
                                Track track = tracks.get(trackId.longValue() + contextId );
                                if(track == null) {
                                    track = Track.builder().label(label).trackId(trackId).build();
                                    tracks.put(trackId.longValue(), track);
                                }
                                track.setTrackInCount(track.getTrackInCount() + 1);
                            }


                        }
                    }
                    if(roi.containsKey("out_rack_ids")){
                        Integer[] outIds = JSON.parseObject(roi.get("out_rack_ids").toString(), Integer[].class);
                        if(roi.get("out_rack_ids") !=null && outIds.length >0) {
                            log.info("out_rack_ids{}", (Object) outIds);

                            log.info("targetsResultOut{}", targets);

                            hasCheckBoxes = true;
                            for (Integer trackId: outIds ){
                                Track track = tracks.get(trackId.longValue() + contextId );
                                if(track == null) {
                                    track = Track.builder().label(label).trackId(trackId).build();
                                    tracks.put(trackId.longValue(), track);
                                }
                                track.setTrackOutCount(track.getTrackOutCount() + 1);
                            }
                        }
                    }

                }
            }
        }


        return !targets.isEmpty();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return;

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        long contextId =Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));


        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");

        if (CollectionUtils.isEmpty(targets))
            return;

        Map<Long, Track> tracksLine = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        if(tracksLine == null)
            return ;

        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 0), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);



        int tCountLine = tracksLine.values().stream().mapToInt(Track::getTrackInCount).sum();
        int tOutCountLine = tracksLine.values().stream().mapToInt(Track::getTrackOutCount).sum();

        // 字体样式、字体大小、字体颜色
        int fontFace =opencv_imgproc.FONT_HERSHEY_DUPLEX;
        double fontScale = 0.8;
        Scalar color = new Scalar(0, 0, 255, 0);
        int thickness = 1;


        for (Map<String, Object> target : targets) {
            if(target.containsKey("rois")){
                List<Map<String, Object>> rois = (List<Map<String, Object>>)   target.get("rois") ;
                for (Map<String, Object> roi :rois) {

                    List<Integer> in_track_ids = new ArrayList<>();
                    if(roi.containsKey("in_track_ids")){

                        Integer[] inIds = JSON.parseObject(roi.get("in_track_ids").toString(), Integer[].class);
                        if(roi.get("in_track_ids") !=null && inIds.length >0) {
                            in_track_ids = Arrays.asList(inIds);
                        }
                    }
                    List<Integer> out_track_ids = new ArrayList<>();
                    if(roi.containsKey("out_track_ids")){

                        Integer[] inIds = JSON.parseObject(roi.get("out_track_ids").toString(), Integer[].class);
                        if(roi.get("out_track_ids") !=null && inIds.length >0) {
                            out_track_ids = Arrays.asList(inIds);
                        }
                    }
                    String sceneImagePath = null;
                    if(!in_track_ids.isEmpty() || !out_track_ids.isEmpty() ){

                        Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                                .limit(kesonTarget_size)
                                .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                                .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "image_id")) == ((Number) target.get("image_id")).longValue())
                                .findAny()
                                .orElse(null);

                        if (kesonTarget != null) {

                                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                                if (sceneImage.getValue() != null) {
                                     sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()),processor.getImgSaveTag());
                                }
                        }
                    }
                    if(in_track_ids.isEmpty() && out_track_ids.isEmpty()){
                        continue;
                    }

                    Mat sourceImage = null;
                    if(sceneImagePath != null) {
                         sourceImage = opencv_imgcodecs.imread(sceneImagePath);
                    }


                    if(roi.containsKey("boxes")){
                        List<Map<String, Object>> boxes = (List<Map<String, Object>>)   roi.get("boxes") ;
                        if(!boxes.isEmpty()){
                            for (Map<String, Object> box :boxes) {
                                Map<String, Object> output = new HashMap<String, Object>();

                                Integer trackId =  ((Number) box.get("track_id")).intValue();
                                output.put("track_id", trackId);
                                output.put("label", box.get("label"));
                                output.put("point", box.get("point"));
                                output.put("confidence", box.get("confidence"));
                                output.put("roi", box.get("roi"));
                                if(in_track_ids.contains(trackId)){
                                    output.put("crossLine", "in");
                                }
                                if(out_track_ids.contains(trackId)){
                                    output.put("crossLine", "out");
                                }
                                output.put("in_track_ids", in_track_ids);
                                output.put("out_track_ids", out_track_ids);

                                output.put("sceneImage", sceneImagePath);
                                if(sceneImagePath != null && sourceImage !=null){
                                    Map<String, Number> roiBox = (Map<String, Number>) box.get("roi");
                                    int left = roiBox.get("left").intValue();
                                    int top = roiBox.get("top").intValue();
                                    int width = roiBox.get("width").intValue();
                                    int height = roiBox.get("height").intValue();


                                    org.bytedeco.opencv.opencv_core.Rect rect = new org.bytedeco.opencv.opencv_core.Rect(Math.max(0, left), Math.max(0, top) , width  ,  height);
                                    opencv_imgproc.rectangle(sourceImage, rect,  new Scalar(0,255,255,0), 1, opencv_imgproc.LINE_AA, 0);

                                    org.bytedeco.opencv.opencv_core.Point  org = new org.bytedeco.opencv.opencv_core.Point(rect.x() + rect.width(), rect.y() + rect.height());

                                    opencv_imgproc.putText(sourceImage, trackId.toString(), org, fontFace, fontScale, color, thickness, opencv_imgproc.LINE_AA, false);

                                }

                                outputResult.add(output);
                            }
                        }
                    }
                    if(sceneImagePath != null && sourceImage !=null) {

                        org.bytedeco.opencv.opencv_core.Rect rectCount = new org.bytedeco.opencv.opencv_core.Rect(100, 100 , 100  ,  100);
                        org.bytedeco.opencv.opencv_core.Point  org = new org.bytedeco.opencv.opencv_core.Point(rectCount.x() + rectCount.width(), rectCount.y() + rectCount.height());
                        String countTotal = "in : "+tCountLine+"--" + "out :" + tOutCountLine;
                        opencv_imgproc.putText(sourceImage, countTotal , org, fontFace, fontScale, color, thickness, opencv_imgproc.LINE_AA, false);


                        String fileName = sceneImagePath.substring(sceneImagePath.lastIndexOf('/') + 1);
                        File path = new File(Utils.instance.savePath + "/opencv/" + Objects.requireNonNullElse(ImageUtils.dateFormatter_DAY.get().format(new Date()), "/") + annotatorName());
                        if (!path.exists())
                            path.mkdirs();
                        // 保存结果图片
                        opencv_imgcodecs.imwrite(path.getAbsolutePath() + "/" + fileName + "_opencv.jpg", sourceImage);
                    }
                }
            }
        }




        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
        if(outputResult.isEmpty())
            modelResult.setOutputResult(null);
        else
            modelResult.setOutputResult(outputResult);

    }


    @Getter
    private  static  final class TrackPerson{

        private volatile Pointer faceFrame;

        @Setter
        private volatile String personId;

        @Setter
        private volatile Float personScore;

        @Setter
        private volatile String targetType;

        public TrackPerson() {

        }
    }

    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class Track{
        private int label;
        private int trackId;

        private int associationsId;

        private int trackInCount;
        private int trackOutCount;

        @Builder.Default
        private boolean startAreaCount = false;
        @Builder.Default
        private boolean endAreaCount =false;

        public void setTrackInCount(int in) {
            trackInCount = in;
        }
        public void setTrackOutCount(int out) {
            trackOutCount = out;
        }

        public void setStartAreaCount() {
            startAreaCount = true;
        }
        public void setEndAreaCount() {
            endAreaCount = true;
        }

        public void setAssociationsId(int ass) {
            associationsId = ass;
        }
        private Rect currentPosition;


        public void setCurrentPosition(Map<String, Number> roi) {
            currentPosition = Rect.builder().top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).build();
        }

        @Data
        @Accessors(chain = true)
        @Builder
        public static class Rect{
            private int left;
            private int top;
            private int width;
            private int height;
        }
    }


    @SuppressWarnings("unchecked")
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return result;



        Map<Long, Track> tracksLine = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        if(tracksLine == null)
            return result;

        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();

        long contextId =Utils.keyToContextId(deviceId);

        CvScalar color = opencv_core.CV_RGB(255, 0,0 );

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        if(ArrayUtils.isEmpty(polygons))
            polygons = new Polygon[] {new Polygon(new int[] {0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[] {0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};


        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);


        for(int indexs = 0 ;indexs < polygons.length; indexs ++) {
            Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
            if (extra.isEmpty())
                continue;

            if(extra.containsKey("crossDot")){

                List<Number> crossDot = (List<Number>)extra.getOrDefault("crossDot", List.of());
                if(crossDot.size() > 2) {
                    result.add(Line.builder().thickness(3).color(color).from(new int[]{crossDot.get(0).intValue(), crossDot.get(1).intValue()}).to(new int[]{crossDot.get(2).intValue(), crossDot.get(3).intValue()}).build());
                }

            }

        }


        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");
        for (Map<String, Object> target : targets) {

            if(target.containsKey("rois")){
                List<Map<String, Object>> rois = (List<Map<String, Object>>)   target.get("rois") ;
                for (Map<String, Object> roi :rois){

                    if(roi.containsKey("boxes")){
                        List<Map<String, Object>> boxes = (List<Map<String, Object>>)   roi.get("boxes") ;
                        if(!boxes.isEmpty()){
                            log.info("boxes{}",  boxes);

                            for (Map<String, Object> box :boxes) {
                                Map<String, Integer> roibox = (Map<String, Integer>) box.get("roi");
                                result.add(Rect.builder().processor(annotatorName()).top(roibox.get("top")).left(roibox.get("left")).width(roibox.get("width")).height(roibox.get("height")).build());

                            }
                        }
                    }

                }
            }
        }

//        Map<String, Integer> tracks = trackingMapOutCount.get(contextId);
//        if(tracks == null)
//            return result;
//        int tCount = 0;
//        int tOutCount =0;
//        for(Map.Entry<String, Integer> entry : tracks.entrySet()) {
//            if(entry.getKey().equals("in")){
//                tCount = entry.getValue();
//            }else{
//                tOutCount= entry.getValue();
//            }
//        }

        int tCountLine = 0;
        int tOutCountLine =0;
        for(Map.Entry<Long, Track> entry : tracksLine.entrySet()) {
            Track currentTrack = entry.getValue();
            tCountLine += currentTrack.trackInCount;
            tOutCountLine += currentTrack.trackOutCount;
        }
        log.info("crossDirectionCountsLine{},{},{}", deviceId, tCountLine, tOutCountLine);

        color = opencv_core.CV_RGB(0, 0,255 );
        result.add(Rect.builder().color(color).processor(annotatorName()).text("in [" + tCountLine + "],out [" +tOutCountLine+ "]").top(100).left(100).width(100).height(100).build());



         color = opencv_core.CV_RGB(0, 255,0 );
        for (Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for (int index = 0; index < roi.length - 1; index++) {
                result.add(Line.builder().thickness(2).from(new int[]{roi[index][0], roi[index][1]}).to(new int[]{roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().thickness(2).color(color).from(new int[]{roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[]{roi[0][0], roi[0][1]}).build());
        }




        return result;
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("crossLins start context [" + contextId + "].");

        updateTrack(contextId, "in", 0);
        updateTrack(contextId, "out", 0);

        trackingMap.put(contextId, new ConcurrentHashMap<Long, Track>());

        deviceRoiMapCross.put(contextId,  new ConcurrentHashMap<String,String>());

    }

    private void stopContext(String deviceId, long contextId) {

        if(!trackingMapOutCount.containsKey(contextId))
            return ;

        trackingMapOutCount.remove(contextId);
        trackingMap.remove(contextId);

        log.info("crossLins stop deviceId [" + deviceId + "].");
    }

    private boolean updateTrack(Long contextID ,   String innerKey, Integer innerValue ){


        if(trackingMapOutCount.containsKey(contextID)) {
            Map<String, Integer> innerMap = trackingMapOutCount.get(contextID);
            innerMap.put(innerKey, innerValue);
        } else {
            Map<String, Integer> innerMap = new HashMap<>();
            innerMap.put(innerKey, innerValue);
            trackingMapOutCount.put(contextID, innerMap);
        }
        return true;
    }

    private Integer getTrack(Long contextID ,   String innerKey ){


        if(trackingMapOutCount.containsKey(contextID)) {
            Map<String, Integer> innerMap = trackingMapOutCount.get(contextID);
            if(innerMap.containsKey(innerKey))
                return innerMap.get(innerKey);
        }
        return 0;
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if(e != null)
            for(Map<String, Object> param : (List<Map<String, Object>>)e) {
                if(!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer)param.get("roiIndex");
                if(roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };

    public static String[] toArrayStringRi(Integer[][][] arr) {
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr[i]);
        }
        return result;
    }
    public static String[] toArrayStringRoi(Integer[][] arr) {
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr[i]);
        }
        return result;
    }

    public static String[] toArrayStringRoiList(List<Integer[][]> arr) {
        int numRows = arr.size();
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr.get(i));
        }
        return result;
    }

    public static double[] calculateIncrementValues(double[] point, double[][] line, String direction) {
        double x1 = line[0][0];
        double y1 = line[0][1];
        double x2 = line[1][0];
        double y2 = line[1][1];

        // Calculate slope of the line
        double slope = (y2 - y1) / (x2 - x1);

        // Calculate increment values based on slope and direction
        double increment_x = Math.sqrt(1 + Math.pow(slope, 2)) / Math.abs(slope);
        double increment_y = increment_x * slope;

// Calculate the new point based on direction
        if (direction.equals("backward")) {
            increment_x = -increment_x;
            increment_y = -increment_y;
        }

        return new double[]{increment_x, increment_y};
    }
    public static double[] calculatePointForPolygon(double[] point, double[][] polygon, String direction) {
        double minDistance = Double.MAX_VALUE;
        double[][] closestLine = null;

        // Find the closest line segment to the given point in the polygon
        for (int i = 0; i < polygon.length; i++) {
            double[] p1 = polygon[i];
            double[] p2 = polygon[(i + 1) % polygon.length];

            // Calculate distance between point and line segment
            double distance = Math.abs((p2[1] - p1[1]) * point[0] - (p2[0] - p1[0]) * point[1] + p2[0] * p1[1] - p2[1] * p1[0]) / Math.sqrt(Math.pow(p2[1] - p1[1], 2) + Math.pow(p2[0] - p1[0], 2));

            // Store closest line segment so far
            if (distance < minDistance) {
                minDistance = distance;
                closestLine = new double[][]{p1, p2};
            }
        }

        // Calculate increment values based on closest line and direction
        double[] increments = calculateIncrementValues(point, closestLine, direction);

        // Calculate the new point based on increments and direction
        double x_new = point[0] + increments[0];
        double y_new = point[1] + increments[1];

        return new double[]{x_new, y_new};
    }
    public static double[] calculatePointForLine(double[] point, double[][] line, String direction) {
        double x1 = line[0][0];
        double y1 = line[0][1];
        double x2 = line[1][0];
        double y2 = line[1][1];

        // Calculate slope of the line
        double slope = (y2 - y1) / (x2 - x1);

        // Calculate increment values based on slope and direction
        double increment_x = Math.sqrt(1 + Math.pow(slope, 2)) / Math.abs(slope);
        double increment_y = increment_x * slope;

        // Calculate the new point based on increments and direction
        if (direction.equals("backward")) {
            increment_x = -increment_x;
            increment_y = -increment_y;
        }

        double x_new = point[0] + increment_x;
        double y_new = point[1] + increment_y;

        return new double[]{x_new, y_new};
    }





    private static final String DIRECTION = "direction";

    private static final String ROISTRING = "roi";

    private static final String LINESTRING = "lines";

    private static final String directionForWard = "forward";

    private static final String directionBackWard = "backward";
}
