package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;

import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XStoreHandler;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FallingObject extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    private Pointer faodkTracker;

    @Getter
    private final Integer interval = 0;

    @Getter
    private final Boolean blocking = false;

    @Getter
    private final Boolean noBatch = true;
    
    @Getter
    protected final Boolean needContext = true;

    @Getter
    protected final Integer frameBuffer = 64;//设置这么大是因为算法里会引用至少60帧 所以为了性能 只能设置这么大

    @Getter
    protected final String decoderFormat = "rgb24";
    
    @Getter
    protected final String frameBufferStrategy = "smart";
    
    @Autowired
    private XStoreHandler xStoreHandler;
    
    private static final float IMAGESAVERATE = 25; //存图比率 默认25%

    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        PointerByReference[] result = new PointerByReference[handlingList.size()];
        if(status != 0)
            return result;

        Set<Long> currentContextIds = contextMap.keySet();
        int[] runningIndexs = IntStream.range(0, handlingList.size()).filter(index -> currentContextIds.contains(itemToContextId(handlingList.get(index)))).toArray();
        if(runningIndexs.length <= 0)
            return result;
        
        BatchItem[] targetItems = new BatchItem[runningIndexs.length];
        for(int index = 0; index < runningIndexs.length; index ++)
            targetItems[index] = handlingList.get(runningIndexs[index]);

        long contextIds[] = new long[targetItems.length];
        Integer[][][] roiArrays[] = new Integer[targetItems.length][][][];
        Pointer rgbFrames[] = new Pointer[targetItems.length];

        for(int index = 0; index < targetItems.length; index ++) {
            contextIds[index] = itemToContextId(targetItems[index]);
            roiArrays[index] = targetItems[index].getModelRequest().getProcessor().getRoi();
            Pointer frame = targetItems[index].getModelRequest().getVideoFrames()[0].getGpuFrame();
            
            rgbFrames[index] = FrameUtils.ref_or_cvtcolor_buffered_frame(frame, KestrelApi.KESTREL_VIDEO_RGB);
        }

        Pointer input_keson = buildInputKeson(contextIds, rgbFrames, roiArrays);
        PointerByReference output_keson = new PointerByReference();

        try {
            int err = -1;

            synchronized(faodkTracker) {
            	if(faodkTracker != null) {
                    long now = System.currentTimeMillis();
                    err = Kestrel_faodkLibrary.INSTANCE.faodk_processor_process(faodkTracker, input_keson, output_keson);
                    monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
            	}
            }

            if(err != 0) {
                log.error("faodk_processor_process err is [" + err + "]");
                return result;
            }

            Pointer targets = KestrelApi.keson_get_object_item(output_keson.getValue(), "targets");
            int arraySize = KestrelApi.keson_array_size(targets);

            for(int index = 0; index < arraySize; index ++) {
                Pointer target = KestrelApi.keson_detach_from_array(targets, index);
                long source_id = (int)KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "source_id"));

                for(int jndex = handlingList.size() - 1; jndex >= 0; jndex --) {
                    long itemSourceId = itemToContextId(handlingList.get(jndex));
                    if(itemSourceId != source_id)
                        continue;

                    if(result[jndex] == null) {
                        Pointer newArray = KestrelApi.keson_create_array();
                        result[jndex] = new PointerByReference(newArray);
                    }

                    Pointer array = result[jndex].getValue();
                    KestrelApi.keson_add_item_to_array(array, target);
                    break;
                }
            }
        }finally {
            KesonUtils.kesonDeepDelete(input_keson, output_keson.getValue());
            FrameUtils.batch_free_frame(rgbFrames);
        }

        for(PointerByReference output : result) {
            if(output == null)
                continue;

            Pointer param_keson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
            KestrelApi.keson_add_item_to_object(param_keson, "targets", output.getValue());
            output.setValue(param_keson);
        }

        return result;
    }

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] output_kesons) {
        for (int index = 0; index < handlingList.size() && index < output_kesons.length; index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(output_kesons[index]);
        }
    }

    @SuppressWarnings("rawtypes")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        return MapUtils.isNotEmpty((Map)modelResult.getDetectResult());//Nothing to do
    }
    
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        Pointer targets = KestrelApi.keson_get_object_item(modelResult.getKeson().getValue(), "targets");
        Pointer target = KestrelApi.keson_get_array_item(targets, 0);
        Pointer boxes = KestrelApi.keson_get_object_item(target, "trajectory");
        Pointer ref_frames = KestrelApi.keson_get_object_item(target, "ref_frames");
        int arraySize = Math.min(KestrelApi.keson_array_size(boxes), KestrelApi.keson_array_size(ref_frames));

        String deviceId = (String)modelResult.getModelRequest().getParameter().get("deviceId");
        Set<Integer> saveImageIndex = arraySize <= 2 ? Set.of(0, 1) : new HashSet<Integer>();
        if(arraySize > 2) {
        	int imageCount = (int)(arraySize * Math.min(1, Math.abs(IMAGESAVERATE)) / 100);
        	
        	for(int index = 0; index < imageCount; index ++)
        		saveImageIndex.add((arraySize / (imageCount - 1)) * index);
        }
        
        List<Map<String, Object>> result = IntStream.range(0, arraySize)
        		.parallel()
        		.mapToObj(index -> {
		        	Pointer box = KestrelApi.keson_get_array_item(boxes, index);
		            Pointer ref_frame = KestrelApi.keson_get_array_item(ref_frames, index);
		
		            PointerByReference frame = new PointerByReference();
		            KestrelApi.keson_get_ext_data(ref_frame, frame);
		            String imageFile = saveImageIndex.contains(index) ? FrameUtils.save_image_as_jpg(frame.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag()) : FrameUtils.NOIMAGE;
		            
		            JSONObject roi = (JSONObject)KesonUtils.kesonToJson(box);
		            roi.remove("#keson_code");
		            return Map.of("detect", (Object)roi, "image", imageFile, "deviceId", deviceId, "targetType", "fallingObject");
		        })
		        .collect(Collectors.toList());

        modelResult.setOutputResult(result);
    }
    
    private final ConcurrentHashMap<String, MutablePair<List<Drawing>, Integer>> drawingMap = new ConcurrentHashMap<String, MutablePair<List<Drawing>, Integer>>();
    
    @SuppressWarnings("unchecked")
	@Override
    public List<Drawing> draw(ModelResult modelResult){
        List<Drawing> result = new ArrayList<Drawing>();
        String deviceId = (String)modelResult.getModelRequest().getParameter().get("deviceId");
        
        Processor processor = modelResult.getModelRequest().getProcessor();
    	for(Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for(int index = 0; index < roi.length - 1; index ++) {
                result.add(Line.builder().from(new int[] {roi[index][0], roi[index][1]}).to(new int[] {roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().from(new int[] {roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[] {roi[0][0], roi[0][1]}).build());
        }
		
    	MutablePair<List<Drawing>, Integer> drawingPair = drawingMap.get(deviceId);
    	List<Map<String, Object>> outputResult = (List<Map<String, Object>>)modelResult.getOutputResult();
    	if(CollectionUtils.isEmpty(outputResult)) {
    		if(drawingPair != null) {
    			result.addAll(drawingPair.getLeft());
    			
    			if(drawingPair.getValue() > 100)
    				drawingMap.remove(deviceId);
    			else 
    				drawingPair.setRight(drawingPair.getRight() + 1);
    		}	
    	}else {
    		List<Drawing> rects = new ArrayList<Drawing>();
    		drawingPair = MutablePair.of(rects, 0);
    		drawingMap.put(deviceId, drawingPair);
    		
    		for(Map<String, Object> output : outputResult) {
    			Map<String, Number> roi = (Map<String, Number>)output.get("detect");
    			
    			Rect rect = Rect.builder().processor(annotatorName()).top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).build();
    			rects.add(rect);
    		}
    	}
    	
		return result;
	}

    private final ConcurrentHashMap<Long, Map<String, Object>> contextMap = new ConcurrentHashMap<Long, Map<String, Object>>();
    
    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            if(!contextMap.containsKey(contextId)) {
                startContext(contextId, event.getInfra());
                contextMap.put(contextId, new HashMap<String, Object>());
            }else {
            	Map<String, Object> parameter = contextMap.get(contextId);
            	long previousStamp = (long)parameter.getOrDefault("previousStamp", -1L);
            	long now = System.currentTimeMillis();
            	if(now - previousStamp > 1000 * 60 * 30) {
            		parameter.put("previousStamp", now);
            		
            		log.info("device [" + event.getInfra().getDeviceId() + "] , more than 30 minite, restart context.");
            		
            		synchronized(faodkTracker) {
            			stopContext(contextId);
            			startContext(contextId, event.getInfra());
            		}
            	}
            }
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            long contextId = Utils.keyToContextId(event.getDeviceId());
            if(!contextMap.containsKey(contextId))
                return ;

            contextMap.remove(contextId);
            try { Thread.sleep(1000); } catch (InterruptedException x) { }
            stopContext(contextId);
        }
    }

    @PostConstruct
    @Override
    public synchronized void initialize() {
        super.initialize();

        try {
            Files.writeString(Path.of("/dev/shm/config.conf"), "{\"min_object_size\": 10, \"max_falling_angle\": 60, \"event_gap\": 30}");

            if(faodkTracker == null)
                faodkTracker = Kestrel_faodkLibrary.INSTANCE.faodk_processor_create("/dev/shm/config.conf");
            
            if(faodkTracker == null)
                throw new RuntimeException("Error occured in faodk_processor_create.");
        } catch (IOException e) {
            e.printStackTrace();
            destroy();
        }

        log.info("Falling Object initializing. ");
    }

    @PreDestroy
    @Override
    public synchronized void destroy() {
        Initializer.bindDeviceOrNot();

        stopHandle();
        
        if(faodkTracker != null)
            synchronized(faodkTracker) {
            	for(Long contextId : contextMap.keySet())
                    stopContext(contextId);
            	
                Kestrel_faodkLibrary.INSTANCE.faodk_pipeline_destroy(faodkTracker);
                
                faodkTracker = null;
            }

        super.destroy();
        
        contextMap.clear();

        log.info("Falling Object destroying.");
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        if(faodkTracker == null)
            throw new RuntimeException("faodkTracker is null!!!");
        
        Initializer.bindDeviceOrNot();
        
        VideoFrame videoFrame = FrameUtils.fetch_up_down_load_frame_path(infra.getRtspSource());
        Processor processor = xStoreHandler.getHighProcessorMap().getOrDefault(infra.getDeviceId(), Map.of()).getOrDefault(annotatorName(), Processor.builder().processor(annotatorName()).build());
        Pointer input_keson = buildInputKeson(new long[] {contextId}, new Pointer[] {videoFrame.getFrame()}, new Integer[][][][] {processor == null ? null : processor.getRoi()});
        
        int err = -1;
        synchronized(faodkTracker) {
            err = Kestrel_faodkLibrary.INSTANCE.faodk_processor_start_up(faodkTracker, input_keson);
        }

        KesonUtils.kesonDeepDelete(input_keson);
        FrameUtils.batch_free_frame(videoFrame);

        if(err != 0)
            throw new RuntimeException("Falling Object start ["+ err +"].");
        

        log.info("Falling Object start context [" + contextId + "].");
    }

    private void stopContext(long contextId) {
        if(faodkTracker == null)
            throw new RuntimeException("faodkTracker is null!!!");
        
        Pointer input_keson = buildInputKeson(new long[] {contextId}, new Pointer[] {null}, new Integer[][][][] {null});
        PointerByReference output_keson = new PointerByReference();
        synchronized(faodkTracker) {
            Kestrel_faodkLibrary.INSTANCE.faodk_processor_stop(faodkTracker, input_keson, output_keson);
        }
        KesonUtils.kesonDeepDelete(input_keson, output_keson.getValue());
        
        log.info("Falling Object stop context [" + contextId + "].");
    }

    private static Pointer buildInputKeson(long contextIds[], Pointer[] rgbFrames, Integer[][][][] roiArrays) {
        Pointer input_keson = KestrelApi.keson_create_object();
        KestrelApi.keson_add_item_to_object(input_keson, "id", KestrelApi.keson_create_int(0));
        Pointer targets = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_object(input_keson, "targets", targets);

        for(int index = 0; index < contextIds.length && index < rgbFrames.length; index ++) {
            if(rgbFrames[index] == null)
                continue;

            Pointer source_keson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(targets, source_keson);
            KestrelApi.keson_add_item_to_object(source_keson, "source_id", KestrelApi.keson_create_int(contextIds[index]));
            KestrelApi.keson_add_item_to_object(source_keson, "id", KestrelApi.keson_create_int(KestrelApi.kestrel_frame_pts(rgbFrames[index])));
            
            if(ArrayUtils.isNotEmpty(roiArrays[index]) && ArrayUtils.getLength(roiArrays[index][0]) >= 3) {
                int left = Integer.MAX_VALUE, top = Integer.MAX_VALUE, right = Integer.MIN_VALUE, bottom = Integer.MIN_VALUE;
                for(Integer[] point : roiArrays[index][0]) {
                    left = Math.min(left, point[0]);
                    right = Math.max(right, point[0]);
                    top = Math.min(top, point[1]);
                    bottom = Math.max(bottom, point[1]);
                }
                
                Pointer roiFrame = FrameUtils.roi_frame(rgbFrames[index], left, top, right - left, bottom - top);
                KestrelApi.keson_add_item_to_object(source_keson, "image", KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), roiFrame));
                FrameUtils.batch_free_frame(roiFrame);
            }else {
                KestrelApi.keson_add_item_to_object(source_keson, "image", KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), rgbFrames[index]));
            }
        }

        return input_keson;
    }

    private static long itemToContextId(BatchItem item) {
        return Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"));
    }
    
    public interface Kestrel_faodkLibrary extends Library {
        public static final String JNA_LIBRARY_NAME = "faodk";
        public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_faodkLibrary.JNA_LIBRARY_NAME);
        public static final Kestrel_faodkLibrary INSTANCE = (Kestrel_faodkLibrary)Native.load(Kestrel_faodkLibrary.JNA_LIBRARY_NAME, Kestrel_faodkLibrary.class);

        /**
         * @brief 创建Faodk句柄句柄，初始化Faodk句柄资源
         * @param[in] config 配置json字符串或json文件路径
         * @return 成功返回Faodk句柄句柄，失败返回nullptr
         */
        Pointer faodk_processor_create(String config);

        /**
         * @brief 单独配置每一路的处理参数
         * @param[in] config 配置json字符串或json文件路径
         * @return 成功返回Faodk句柄句柄，失败返回nullptr
         */
        int faodk_processor_set_source_config(Pointer handle, String config);

        /**
         * @brief 输入数据
         * @param[in] handle Faodk句柄
         * @param[in] input 输入的keson数据
         * @return 成功返回KESTREL_OK，失败返回其他错误码
         */
        int faodk_processor_start_up(Pointer handle, Pointer input_keson);
        
        /**
         * @brief pipeline的主处理函数
         *
         * @param[in] handle Faodk句柄
         * @param[in] input 对应视频通道的图像帧
         * @param[out] output 若检测到高空抛物，且已结束，则输出其图像帧序列
         * @return 成功返回KESTREL_OK，失败返回其他错误码
         */
        int faodk_processor_process(Pointer handle, Pointer input_keson, PointerByReference output_keson);
        
        /**
         * @brief pipeline的背景建模函数
         *
         * @param[in] handle Faodk句柄
         * @param[in] input 对应视频通道的图像帧
         * @param[out] output 输出前景帧序列
         * @return 成功返回KESTREL_OK，失败返回其他错误码
         */
        int faodk_processor_background_modeling(Pointer handle, Pointer input_keson, PointerByReference output_keson);
        
        /**
         * @brief pipeline的抛落物分析函数
         *
         * @param[in] handle Faodk句柄
         * @param[in] input 对应视频通道的图像帧
         * @param[out] output 若检测到高空抛物，且已结束，则输出其图像帧序列
         * @return 成功返回KESTREL_OK，失败返回其他错误码
         */
        int faodk_processor_analyze(Pointer handle, Pointer input_keson, PointerByReference output_keson);

        /**
         * @brief 停止对应通道的高空抛物检测
         *
         * @param[in] handle Faodk句柄
         * @param[in] input 要结束的视频通道id
         * @param[out] output 将各通道里面的高空抛物的图像帧序列都输出
         * @return 成功返回KESTREL_OK，失败返回其他错误码
         */
        int faodk_processor_stop(Pointer handle, Pointer input_keson, PointerByReference output_keson);

        /**
         * @brief 销毁Pipeline句柄，释放Pipeline资源
         */
        void faodk_pipeline_destroy(Pointer handle);
    }
}
