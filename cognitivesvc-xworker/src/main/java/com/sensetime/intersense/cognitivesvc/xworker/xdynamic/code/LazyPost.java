package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.*;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.*;

@Slf4j
public class LazyPost extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    private volatile Set<Long> contextIds = new HashSet<Long>();
    
    private String[] detainedKeps;

    private Pointer[] faodkTrackers = new Pointer[1];

    @Getter
    private final Boolean blocking = true;

    @Getter
    private final Boolean noBatch = true;
    
    @Getter
    protected final Boolean needContext = true;
    
    @Getter
    protected final boolean lazyInit = true;    

    @Getter
    protected final String decoderFormat = "rgb24";

    @Getter
    protected final Integer frameBuffer = 4;

    @Getter
    protected final String frameBufferStrategy = "pedantic_swallow";
    
    @Getter
    protected final Integer minBatchSize = 2;//不低于2

    @Getter
    protected final Integer minBatchStagger = 1;//数字越小 精度越高 资源消耗也越大

    @Getter
    protected final Integer interval = 25 * 3;

    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if(status != 0)
            return new PointerByReference[0];    
        
        BatchItem item = handlingList.get(0);
        long contextId = Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"));
        if(!contextIds.contains(contextId))
            return new PointerByReference[0];
        
        Pointer input_keson = buildInframeKeson(contextId, item.getModelRequest().getVideoFrames()[0].getGpuFrame(), item.getModelRequest().getVideoFrames()[1].getGpuFrame());
        PointerByReference output_keson = new PointerByReference();
        
        Pointer faodkTracker = faodkTrackers[(int)contextId  % faodkTrackers.length];
        synchronized(faodkTracker) {
            long now = System.currentTimeMillis();
            Kestrel_LazypostLibrary.INSTANCE.lazy_post_process(faodkTracker, input_keson, output_keson);
            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        }
        
        KesonUtils.kesonDeepDelete(input_keson);
        return new PointerByReference[] {output_keson};
    }

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] output_kesons) {
    	if(ArrayUtils.isEmpty(output_kesons))
    		return;
    	
        Pointer firstTargets = KestrelApi.keson_get_object_item(output_kesons[0].getValue(), "targets");
        int firstSize = KestrelApi.keson_array_size(firstTargets);
        for(int jndex = firstSize - 1; jndex >= 0; jndex --) {
            Pointer item = KestrelApi.keson_get_array_item(firstTargets, jndex);
            KestrelApi.keson_add_item_to_object(item, "image_id", KestrelApi.keson_create_int(0));
        }
        
        for(int index = 1; index < output_kesons.length; index ++) {
            Pointer targets = KestrelApi.keson_get_object_item(output_kesons[index].getValue(), "targets");
            int size = KestrelApi.keson_array_size(targets);
            
            for(int jndex = size - 1; jndex >= 0; jndex --) {
                Pointer item = KestrelApi.keson_detach_from_array(targets, jndex);
                KestrelApi.keson_add_item_to_object(item, "image_id", KestrelApi.keson_create_int(index));
                KestrelApi.keson_add_item_to_array(firstTargets, item);
            }
            
            KesonUtils.kesonDeepDelete(output_kesons[index]);
        }
        
        handlingList.get(0).setKeson(output_kesons[0]);
    }

    @SuppressWarnings({ "unchecked" })
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
        return CollectionUtils.isNotEmpty(targets);//Nothing to do
    }

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        
        List<Map<String, Object>>[] results = new List[modelResult.getModelRequest().getVideoFrames().length];
        for(Map<String, Object> target : targets) {
            int image_id = ((Number)target.get("image_id")).intValue();
            if(results[image_id] == null)
                results[image_id] = new ArrayList<Map<String, Object>>();
            
            int track_id  = ((Number)target.get("track_id")).intValue();
            float det_conf  = ((Number)target.get("det_conf")).floatValue();
            float diff_conf = ((Number)target.get("diff_conf")).floatValue();
                
            results[image_id].add(Map.of("diff_conf", diff_conf, "track_id", track_id, "det_conf", det_conf, "deviceId", deviceId, "targetType", "lazyPost", "roi", target.get("roi")));
        }
        
        modelResult.setOutputResult(results);
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            if(contextIds.contains(contextId))
                return ;

            startContext(contextId, event.getInfra());

            Set<Long> contextIds = new HashSet<Long>(this.contextIds);
            contextIds.add(contextId);
            this.contextIds = contextIds;
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            long contextId = Utils.keyToContextId(event.getDeviceId());
            if(!contextIds.contains(contextId))
                return ;

            Set<Long> contextIds = new HashSet<Long>(this.contextIds);
            contextIds.remove(contextId);
            this.contextIds = contextIds;

            try { Thread.sleep(2000); } catch (InterruptedException x) { }
            stopContext(contextId);
        }
    }

    @PostConstruct
    @Override
    public synchronized void initialize() {
        super.initialize();
        
        detainedKeps = Arrays.stream(handlerEntity.getAttacheds()).filter(attached -> StringUtils.endsWith(attached, ".kep")).toArray(String[]::new);
        for(int index = 0; index < detainedKeps.length; index ++)
            detainedKeps[index] = KestrelApi.kestrel_plugin_load(detainedKeps[index], "");
        
        String kep   = detainedKeps[0];
        String model = handlerEntity.getAttacheds()[1];
        
        try {
            if(faodkTrackers[0] == null)
            	for(int index = 0 ; index < faodkTrackers.length; index ++) 
            		faodkTrackers[index] = Kestrel_LazypostLibrary.INSTANCE.lazy_post_handle_create(buildTrackerConfig(kep, model, 8));
        } catch (Exception e) {
            e.printStackTrace();
            destroy();
        }

        log.info("LazyPost initializing. ");
    }

    @PreDestroy
    @Override
    public synchronized void destroy() {
        Initializer.bindDeviceOrNot();

        stopHandle();

        for(Long contextId : contextIds)
            stopContext(contextId);

        contextIds = new HashSet<Long>();

        if(faodkTrackers[0] != null)
        	for(int index = 0 ; index < faodkTrackers.length; index ++) 
	            synchronized(faodkTrackers[index]) {
	                Kestrel_LazypostLibrary.INSTANCE.lazy_post_handle_release(faodkTrackers[index]);
	                faodkTrackers[index] = null;
	            }
        
        for(String kep : detainedKeps)
        	KestrelApi.kestrel_plugin_unload(kep);
        
        super.destroy();

        log.info("LazyPost destroying.");
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
    	Pointer faodkTracker = faodkTrackers[(int)contextId  % faodkTrackers.length];
        if(faodkTracker != null) {
            Pointer input_keson = buildContextCreateKeson(contextId, 0.8, 40, 300, 8);
            int err = -1;
            
            synchronized(faodkTracker) {
                err = Kestrel_LazypostLibrary.INSTANCE.lazy_post_context_create(faodkTracker, input_keson);
            }

            KesonUtils.kesonDeepDelete(input_keson);
            
            if(err != 0)
                throw new RuntimeException("LazyPost start ["+ err +"].");
        }

        log.info("LazyPost start context [" + contextId + "].");
    }

    private void stopContext(long contextId) {
    	Pointer faodkTracker = faodkTrackers[(int)contextId  % faodkTrackers.length];
    	
        if(faodkTracker != null)
            synchronized(faodkTracker) {
                Kestrel_LazypostLibrary.INSTANCE.lazy_post_context_release(faodkTracker, contextId);
            }

        log.info("Lazy Post stop context [" + contextId + "].");
    }

    private static String buildTrackerConfig(String kep, String model, int batchSize) {
        return "{ \"models\": {\"harpy\":{\"plugin\": \"" + kep + "\", \"model\": \"" + model + "\", \"max_batch_size\": " + batchSize + "}}}";
    }
    
    private static Pointer buildContextCreateKeson(long ctx_id, double model_conf_thresh, long diff_thresh, long diff_count_thresh, long state_thresh) {
        Pointer input_keson = KestrelApi.keson_create_object();
        KestrelApi.keson_add_item_to_object(input_keson, "ctx_id"            , KestrelApi.keson_create_int(ctx_id));
        KestrelApi.keson_add_item_to_object(input_keson, "model_conf_thresh" , KestrelApi.keson_create_double(model_conf_thresh));
        KestrelApi.keson_add_item_to_object(input_keson, "diff_thresh"       , KestrelApi.keson_create_int(diff_thresh));
        KestrelApi.keson_add_item_to_object(input_keson, "diff_count_thresh" , KestrelApi.keson_create_int(diff_count_thresh));
        KestrelApi.keson_add_item_to_object(input_keson, "state_thresh"      , KestrelApi.keson_create_int(state_thresh));
        return input_keson;
    }
    
    private static Pointer buildInframeKeson(long contextId, Pointer... frames) {
        Pointer input_keson = KestrelApi.keson_create_object();
        Pointer targets_keson = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_object(input_keson, "targets", targets_keson);
        
        for(int index = 0; index < frames.length; index ++) {
            Pointer items_keson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(targets_keson, items_keson);
            
            KestrelApi.keson_add_item_to_object(items_keson, "ctx_id", KestrelApi.keson_create_int(contextId));
            KestrelApi.keson_add_item_to_object(items_keson, "image", KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), frames[index]));
        }

        return input_keson;
    }
    
    public interface Kestrel_LazypostLibrary extends Library {
        public static final String JNA_LIBRARY_NAME = "kestrel_lazy_post";
        public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_LazypostLibrary.JNA_LIBRARY_NAME);
        public static final Kestrel_LazypostLibrary INSTANCE = (Kestrel_LazypostLibrary)Native.load(Kestrel_LazypostLibrary.JNA_LIBRARY_NAME, Kestrel_LazypostLibrary.class);

        /// @brief 创建句柄
        /// @param[in] config 配置文件
        /// @return 调用成功则返回懒岗句柄，否则返回空指针
        Pointer lazy_post_handle_create(String config);

        /// @brief 释放句柄
        /// @param[in] handle 懒岗句柄
        /// @return 调用成功则返回KESTREL_OK，否则返回对应的错误码
        void lazy_post_handle_release(Pointer handle);

        /// @brief 创建通道
        /// @param[in] handle 懒岗句柄
        /// @param[in] context_config_raw 通道配置，见schema
        /// @return 调用成功则返回KESTREL_OK，否则返回对应的错误码
        int lazy_post_context_create(Pointer handle,  Pointer input_keson);

        /// @brief 释放通道
        /// @param[in] handle 懒岗句柄
        /// @param[in] ctx_ids 将释放的通道号
        /// @return 调用成功则返回KESTREL_OK，否则返回对应的错误码
        int lazy_post_context_release(Pointer handle, long ctx_id);

        /// @brief 处理懒岗
        /// @param[in] handle 懒岗句柄
        /// @param[in] input_frames_raw 多通道视频帧输入。如一通道有大于2张，取前两张。详见scehma
        /// @param[out] results_raw
        /// 懒岗告警序列，每一个result为一个通道的结果，如通道中无告警，则该result中num等于0。详见schema
        /// @return 调用成功则返回KESTREL_OK，否则返回对应的错误码
        int lazy_post_process(Pointer handle, Pointer input_keson, PointerByReference output_keson);
    }
}
