package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

@Slf4j
public class FireSmog extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    private final ConcurrentHashMap<Long, Long> trackSmogMap = new ConcurrentHashMap<Long, Long>();

    @SuppressWarnings("unchecked")
    @Override
    protected void postProcessOutputValue(ModelResult modelResult) {
        super.postProcessOutputValue(modelResult);

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        if (StringUtils.isBlank(deviceId) || processor == null || CollectionUtils.isEmpty(processor.getExtras()))
            return;

        Long contextID = Utils.keyToContextId(deviceId);
        long now = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);

        Polygon[] polygons = processor.fetchPolygons();

        boolean toRemove = false;
        for (int index = 0; index < polygons.length; index++) {

            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());

            if (extrasMap.containsKey("trigger")) {

                long triggerKey = contextID + index;

                Map<String, Object> trigger = (Map<String, Object>) extrasMap.get("trigger");

                int trackFreq = (int) trigger.getOrDefault("trackFreq", 1000);

                if (trackSmogMap.containsKey(triggerKey) &&
                        trackSmogMap.get(triggerKey) != null &&
                        (now - trackSmogMap.getOrDefault(triggerKey, 0L) < trackFreq)
                ) {
                    log.info("FireSmog checkFail {}, {}, {}, {}", deviceId, now, trackSmogMap.getOrDefault(triggerKey, 0L), trackFreq);
                    toRemove = true;
                }

                //不存在则save
                if(!trackSmogMap.containsKey(triggerKey)){
                    trackSmogMap.putIfAbsent(triggerKey, System.currentTimeMillis());
                }
            }

        }


        if (toRemove) {
            modelResult.setOutputResult(null);
            return;
        }

        trackSmogMap.put(contextID, now);
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if (e != null)
            for (Map<String, Object> param : (List<Map<String, Object>>) e) {
                if (!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer) param.get("roiIndex");
                if (roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;
            long contextId = Utils.keyToContextId(event.getDeviceId());

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(contextId);
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("fireSmog [" + getClass().getSimpleName() + "] start context [" + contextId + "].");
        trackSmogMap.put(contextId, System.currentTimeMillis());
    }

    private void stopContext(long contextId) {
        holders[0].controlForRemoveSource(contextId);
        log.info("fireSmog [" + getClass().getSimpleName() + "] stop context [" + contextId + "].");
        if (!trackSmogMap.containsKey(contextId))
            return;

        trackSmogMap.remove(contextId);
    }

}
