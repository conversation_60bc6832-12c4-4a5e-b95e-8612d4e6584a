package com.sensetime.intersense.cognitivesvc.xworker.zutils;

import lombok.Builder;

/**
 * @Author: Cirmons
 * @Date: 2020-04-29
 */
@Builder
public class KafkaXWorkerConvertor {
    
    private static final String appNameExpr = "\\#appName";
    private static final String objectTypeExpr = "\\#objectType";
    private static final String valueExpr = "\\#value";
    private static final String deviceIdExpr = "\\#deviceId";
    private static final String imageUrlExpr = "\\#url";
    private static final String capturedTimeExpr = "\\#capturedTime";
    private static final String receivedTimeExpr = "\\#receivedTime";
    private static final String frameIndexExpr = "\\#frameIndex";
    private static final String framePtsExpr = "\\#framePts";
    private static final String extraInfoExpr = "\\#extraInfo";
    
    private final String msgTpl = "{" +
            "\"object_\": {" +
            "\"bitField0_\": 0," +
            "\"type_\": 0," +
            "\"portraitImageLocation_\": {" +
            "\"panoramicImageSize_\": {" +
            "\"width_\": 0," +
            "\"height_\": 0," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"objectId_\": \"\"," +
            "\"associations_\": []," +
            "\"algo_\": {" +
            "\"appName_\": \"#appName\"," +
            "\"appVersion_\": 1," +
            "\"objectType_\": \"#objectType\"," +
            "\"objectVersion_\": 1," +
            "\"data_\": {" +
            "\"typeUrl_\": \"\"," +
            "\"value_\": \"#value\"," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"cameraInfo_\": {" +
            "\"cameraId_\": \"\"," +
            "\"deviceId_\": \"#deviceId\"," +
            "\"deviceType_\": \"\"," +
            "\"placeCode_\": \"\"," +
            "\"placeName_\": \"\"," +
            "\"tollgateId_\": \"\"," +
            "\"tollgateName_\": \"\"," +
            "\"sourceId_\": \"\"," +
            "\"internalId_\": {" +
            "\"regionId_\": 1," +
            "\"frameIdx_\": \"#frameIndex\"," +
            "\"framePts_\": \"#framePts\"," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"zoneId_\": \"\"," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"panoramicImage_\": {" +
            "\"format_\": 1," +
            "\"data_\": {" +
            "\"bytes\": []," +
            "\"hash\": 0" +
            "}," +
            "\"url_\": \"#url\"," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"capturedTime_\": {" +
            "\"seconds_\": #capturedTime," +
            "\"nanos_\": 0," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"receivedTime_\": {" +
            "\"seconds_\": #receivedTime," +
            "\"nanos_\": 0," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"objectIndexInFrame_\": 0," +
            "\"extraInfo_\": #extraInfo," +
            "\"trackEvent_\": 1," +
            "\"relativeTime_\": {" +
            "\"seconds_\": 0," +
            "\"nanos_\": 0," +
            "\"memoizedIsInitialized\": -1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}," +
            "\"nsId_\": \"\"," +
            "\"memoizedIsInitialized\": 1," +
            "\"unknownFields\": {" +
            "\"fields\": {}," +
            "\"fieldsDescending\": {}" +
            "}," +
            "\"memoizedSize\": -1," +
            "\"memoizedHashCode\": 0" +
            "}";
    
    private final String appName;
    private final String objectType;
    private final String value;
    private final String deviceId;
    private final String imageUrl;
    private final Long capturedTime;
    private final Long receivedTime;
    private final Long frameIndex;
    private final Long framePts;
    private final String extraInfo;
    
    public String generateXworkerResult() {
        return msgTpl.replaceAll(appNameExpr, appName)
                .replaceAll(objectTypeExpr, objectType)
                .replaceAll(valueExpr, value)
                .replaceAll(deviceIdExpr, deviceId)
                .replaceAll(frameIndexExpr, String.valueOf(frameIndex))
                .replaceAll(framePtsExpr, String.valueOf(framePts))
                .replaceAll(imageUrlExpr, imageUrl)
                .replaceAll(capturedTimeExpr, String.valueOf(capturedTime))
                .replaceAll(extraInfoExpr, String.valueOf(extraInfo))
                .replaceAll(receivedTimeExpr, String.valueOf(receivedTime));
    }
}
