package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_imgproc.CvFont;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.IntStream;

@Slf4j
public class AreaCountHeadShoulder extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {


    private final ConcurrentHashMap<Long, Long> trackAreaMap = new ConcurrentHashMap<Long, Long>();

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons)[0], imageIds);

        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);
        }
    }

    @SuppressWarnings({"unchecked"})
    @Override
    /**
     * 按照 函数包含的模型 顺序的跑一边
     */
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0) return new PointerByReference[0];

        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);


        for (int index = 0; index < pointers.length; index++) {
//            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
//            PointerByReference outOf = output_kesons[index];
//            /** 执行模型 获取数据*/
//            pointers[index].process(inTo.getValue(), outOf);

            long now = System.currentTimeMillis();
            String deviceId = (String) handlingList.get(0).getModelRequest().getParameter().get("deviceId");
            Long streamSourceId = Utils.keyToContextId(deviceId);

            if (index == 0) {
                Integer[][][] proi = handlingList.get(0).getModelRequest().getProcessor().getRoi();
                String policyRoiString = JSON.toJSONString(toArrayStringRi(proi));

                if (deviceRoiMap.get(streamSourceId) == null) {
                    log.info("policyRoiStrings:{}", policyRoiString);
                    deviceRoiMap.put(streamSourceId, policyRoiString);
                    updateRoi(pointers[index].pointers[0], proi, streamSourceId.toString());
                } else {
                    String oldRoiString = deviceRoiMap.get(streamSourceId);
                    if (!oldRoiString.equals(policyRoiString)) {
                        log.info("policyRoiStrings:{}", policyRoiString);
                        deviceRoiMap.put(streamSourceId, policyRoiString);
                        updateRoi(pointers[index].pointers[0], proi, streamSourceId.toString());
                    }
                }
                monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
            }

            /** 执行模型 获取数据*/
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            pointers[index].process(inTo.getValue(), outOf);

        }

        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }


    public void updateRoi(Pointer pipelinePoint, Integer[][][] policyRoi, String sourceId) {

        JSONArray json2 = new JSONArray();
        for (int i = 0; i < policyRoi.length; i++) {

            JSONObject roi = new JSONObject();
            roi.put("label_id",4384938);
            roi.put("polygons", policyRoi[i]);
            json2.add(roi);
        }
        if(json2.isEmpty()){
            return;
        }

        PointerByReference out = new PointerByReference();

        String controlPipeStrng =
                "{\n" +
                " \"streams\": [\n" +
                "   {\n" +
                "              \"name\": \"video_headshoulder_track_stream\",\n" +
                "              \"modules\": [\n" +
                "                  {\n" +

                "                    \"name\": \"roi_filter\",\n" +
                "                     \"source_id\": 49650,\n" +
                "                    \"type\": \"RoiFilter\",\n" +
                "                    \"parallel_group\": \"target_select\",\n" +
                "                    \"inputs\": [\n" +
                "                        \"tracked_faces\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"filtered_face_targets\"\n" +
                "                    ],\n" +
                "                    \"config\": {\n" +
                "                         \"source_id\": 49650,\n" +
                 "                        \"roi_filter\": " + json2 +
                "                    }\n" +

                "               }\n" +
                "             ]\n" +
                "          }\n" +
                "    ]\n" +
                "}";


        controlPipeStrng = controlPipeStrng.replace("49650", sourceId);
        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);


        log.info(">>> [areaCountHeadShoulder] update roi_filter device is: {} , roi is: {}", sourceId, controlPipeStrng);

    }

    public String getSelectRoiFilter(String roi, String defRoi) {
        if (StringUtils.isBlank(roi)) {
            return defRoi;
        }
        Integer[][][] rois = JSON.parseObject(roi, Integer[][][].class);
        if (rois.length <= 0) {
            return defRoi;
        }
        return JSON.toJSONString(toArrayStringRi(rois));
    }

    public static String[] toArrayStringRi(Integer[][][] arr) {
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr[i]);
        }
        return result;
    }


    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {

        super.buildOutputValue(modelResult);

        List<Map<String, Object>> outputResults = (List<Map<String, Object>>) modelResult.getOutputResult();

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        if (ArrayUtils.isEmpty(polygons))
            polygons = new Polygon[]{new Polygon(new int[]{0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[]{0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};

        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);

        List<Map<String, Object>> roiTargets[] = new List[polygons.length];
        for (int index = 0; index < polygons.length; index++)
            roiTargets[index] = new ArrayList<Map<String, Object>>();

        Map<String, Object> detectResult = (Map<String, Object>) modelResult.getDetectResult();
        if (MapUtils.isEmpty(detectResult))
            return;

        List<String> roiIds = (List<String>) ((List<Map<String, Object>>) Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getExtras(), List.of()))
                .stream()
                .filter(item -> "roiIds".equals(item.get("type")))
                .findAny()
                .orElse(Map.of())
                .getOrDefault("roiIds", List.of());

//        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.get("targets");
//
//        log.info("targetsArea{}", targets);

        for (Map<String, Object> target : outputResults) {

            for (int index = 0; index < polygons.length; index++) {

//                Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());
//
//
//                Number positionType = (Number) extrasMap.getOrDefault("positionType", 0);
//
//                Map<String, Number> roi = (Map<String, Number>) target.get("detect");
//                //利用人体中心点作为目标点
//                int currentX = roi.get("left").intValue() + roi.get("width").intValue() / 2;
//                int currentY = roi.get("top").intValue() + roi.get("height").intValue() / 2;
//                if (positionType.intValue() == 1) //利用头顶顶点作为目标点
//                    currentY = roi.get("top").intValue();
//                else if (positionType.intValue() == 2) //利用脚底板底点作为目标点
//                    currentY = roi.get("top").intValue() + roi.get("height").intValue();

                //if(polygons[index].contains(currentX, currentY)){

                roiTargets[index].add(Map.copyOf(target));
                // }
            }
        }

        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (int index = 0; index < polygons.length; index++) {
            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());

            Number minAlert = (Number) extrasMap.get("minAlert");
            Number maxAlert = (Number) extrasMap.get("maxAlert");

            List<Map<String, Object>> roiTarget = roiTargets[index];
            Map<String, Object> round = new HashMap<String, Object>();

            if (maxAlert != null && minAlert != null) {
                if (roiTarget.size() >= minAlert.intValue() && roiTarget.size() <= maxAlert.intValue())
                    round.put("maxAlert", roiTarget.size());
            } else if (maxAlert != null) {
                if (roiTarget.size() <= maxAlert.intValue())
                    round.put("maxAlert", roiTarget.size());
            } else if (minAlert != null) {
                if (roiTarget.size() >= minAlert.intValue())
                    round.put("maxAlert", roiTarget.size());
            } else {
                round.put("maxAlert", roiTarget.size());
            }

            if (!round.isEmpty() && roiTarget.size() > 0) {
                round.put("targets", roiTarget);

                round.put("roiId", (!roiIds.isEmpty()) ? roiIds.get(index) : 0);
                round.put("rois", (processor.getRoi() != null && processor.getRoi().length > 0) ? processor.getRoi()[index] : new int[]{});
                round.put("roiIndex", index);
                result.add(round);
            }
        }

        if (!result.isEmpty())
            modelResult.setOutputResult(result);
        else {
            modelResult.setOutputResult(null);
        }

        postProcessOutputValue(modelResult);

    }


    @SuppressWarnings("unchecked")
    @Override
    protected void postProcessOutputValue(ModelResult modelResult) {
        super.postProcessOutputValue(modelResult);

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        if (StringUtils.isBlank(deviceId) || processor == null || CollectionUtils.isEmpty(processor.getExtras()))
            return;

        Long contextID = Utils.keyToContextId(deviceId);
        long now = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);

        Polygon[] polygons = processor.fetchPolygons();

        boolean toRemove = false;
        for (int index = 0; index < polygons.length; index++) {

            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());

            if (extrasMap.containsKey("trigger")) {
                Map<String, Object> trigger = (Map<String, Object>) extrasMap.get("trigger");

                int trackFreq = (int) trigger.getOrDefault("trackFreq", 1000);

                if (trackAreaMap.get(contextID) != null && (now - trackAreaMap.getOrDefault(contextID, 0L) < trackFreq)) {
                    log.info("LiteFalcon checkFail {}, {}, {}, {}", deviceId, now, trackAreaMap.getOrDefault(contextID, 0L), trackFreq);
                    toRemove = true;
                }
            }
        }
        if (toRemove) {
            modelResult.setOutputResult(null);
            return;
        }

        trackAreaMap.put(contextID, now);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {

        List<Drawing> result = new ArrayList<Drawing>();

        CvScalar colorTarget = opencv_core.CV_RGB(0, 255, 0); // red
        CvScalar color = opencv_core.CV_RGB(0, 0, 255); // red
        CvScalar colorRoi = opencv_core.CV_RGB(255, 255, 0);
        CvFont cvFont = opencv_imgproc.cvFont(2, 2);
        List<Map<String, Object>> outputResults = (List<Map<String, Object>>) modelResult.getOutputResult();

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        for (Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for (int index = 0; index < roi.length - 1; index++)
                result.add(Line.builder().color(color).from(new int[]{roi[index][0], roi[index][1]}).to(new int[]{roi[index + 1][0], roi[index + 1][1]}).build());

            result.add(Line.builder().color(color).from(new int[]{roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[]{roi[0][0], roi[0][1]}).build());

        }

        if (outputResults != null) {
            for (Map<String, Object> outputResult : outputResults) {

                List<Map<String, Object>> targets = (List<Map<String, Object>>) outputResult.get("targets");

                for (Map<String, Object> target : targets) {
                    Map<String, Integer> roiDetect = (Map<String, Integer>) target.get("detect");
                    result.add(Rect.builder().color(colorTarget).thickness(1).top(roiDetect.get("top")).left(roiDetect.get("left")).width(roiDetect.get("width")).height(roiDetect.get("height")).build());
                }
                Number maxAlert = (Number) outputResult.get("maxAlert");

                Integer[][] rois = (Integer[][]) outputResult.get("rois");

                Number roiIndex = (Number) outputResult.get("roiIndex");

                int minLeft = Integer.MAX_VALUE;
                int minTop = Integer.MAX_VALUE;

                for (Integer[] point : rois) {
                    int left = point[0];
                    int top = point[1];
                    if (left < minLeft) {
                        minLeft = left;
                    }
                    if (top < minTop) {
                        minTop = top;
                    }
                }
                if (roiIndex.intValue() == 0) {
                    minTop = minTop + 20;
                }

                result.add(Rect.builder().color(colorRoi).textFont(cvFont).text("areaCount:" + maxAlert.intValue()).color(color).left(minLeft).top(minTop + 20).width(0).height(0).build());

            }
        }


        return result;
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if (e != null)
            for (Map<String, Object> param : (List<Map<String, Object>>) e) {
                if (!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer) param.get("roiIndex");
                if (roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };


    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;
            long contextId = Utils.keyToContextId(event.getDeviceId());

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(contextId);
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("falcon [" + getClass().getSimpleName() + "] start context [" + contextId + "].");
        trackAreaMap.put(contextId, System.currentTimeMillis());
    }

    private void stopContext(long contextId) {
        holders[0].controlForRemoveSource(contextId);
        log.info("falcon [" + getClass().getSimpleName() + "] stop context [" + contextId + "].");
        if (!trackAreaMap.containsKey(contextId))
            return;

        trackAreaMap.remove(contextId);
    }


}
