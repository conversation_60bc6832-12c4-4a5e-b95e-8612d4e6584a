package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;


@Slf4j
public class FloorOCRAuto extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final String decoderFormat = "rgb24";

    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";

    @Getter
    protected final Integer interval = 0;

    @Getter
    protected final boolean queueMap = true;



    protected ConcurrentHashMap<Long, String>  floorOcrAutoMap = new ConcurrentHashMap<Long, String>();


    @SuppressWarnings({"unchecked"})
    @Override
    /**
     * 按照 函数包含的模型 顺序的跑一边
     */
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0) return new PointerByReference[0];

        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);


        for (int index = 0; index < pointers.length; index++) {
//            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
//            PointerByReference outOf = output_kesons[index];
//            /** 执行模型 获取数据*/
//            pointers[index].process(inTo.getValue(), outOf);

            long now = System.currentTimeMillis();
            String deviceId = (String) handlingList.get(0).getModelRequest().getParameter().get("deviceId");
            Long streamSourceId = Utils.keyToContextId(deviceId);

            if (index == 0) {
                Integer[][][] proi = handlingList.get(0).getModelRequest().getProcessor().getRoi();
                String policyRoiString = JSON.toJSONString(toArrayStringRi(proi));

                CognitiveEntity.Processor processor = handlingList.get(index).getModelRequest().getProcessor();
                Polygon[] polygons = processor.fetchPolygons();
                if (ArrayUtils.isEmpty(polygons))
                    polygons = new Polygon[]{new Polygon(new int[]{0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[]{0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};

                Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);


                List<Float> angleList = new ArrayList<>();
                for (int indexs = 0; indexs < polygons.length; indexs++) {

                    Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
                    if (extra.isEmpty())
                        continue;

                    //目前pipeline只支持一个roi
                    angleList.add( ((Number) extra.getOrDefault("angle", 0f)).floatValue());
                }

                if (floorOcrAutoMap.get(streamSourceId) == null) {
                    log.info("policyRoiStrings:{}", policyRoiString);
                    floorOcrAutoMap.put(streamSourceId, policyRoiString);
                    updateRoi(pointers[index].pointers[0], proi, streamSourceId.toString(), angleList);
                } else {
                    String oldRoiString = floorOcrAutoMap.get(streamSourceId);
                    if (!oldRoiString.equals(policyRoiString)) {
                        log.info("policyRoiStrings update:{}", policyRoiString);
                        floorOcrAutoMap.put(streamSourceId, policyRoiString);
                        updateRoi(pointers[index].pointers[0], proi, streamSourceId.toString(), angleList);
                    }
                }

            }

            /** 执行模型 获取数据*/
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            pointers[index].process(inTo.getValue(), outOf);

            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);

        }

        KesonUtils.kesonDeepDelete(param_keson);
        //log.info("outputKesons{}", KesonUtils.kesonToJson(output_kesons[0]));
        return output_kesons;
    }


    public void updateRoi(Pointer pipelinePoint, Integer[][][] policyRoi, String sourceId, List<Float> angleList) {

        PointerByReference out = new PointerByReference();

        String controlPipeStrng = " " +
                "{\n" +
                "    \"streams\": [\n" +
                "              {\n" +
                "                   \"name\": \"carplate_ips\",\n" +
                "                   \"modules\": [\n" +
                "                       {\n" +
                "                    \"name\": \"elevator_detection\",\n" +
                "                    \"source_id\": 49650,\n" +
                "                    \"type\": \"RotateElevatorLevelDetection\",\n" +
                "                    \"inputs\": [\n" +
                "                        \"images\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"detected_targets\"\n" +
                "                    ],\n" +
                "                    \"config\": {\n" +
                "                       \"source_id\": 49650,\n" +
                "                        \"plugin\": \"dbtextdet\",\n" +
                "                        \"model\": \"KM_dbtextdet_Scene_General_nart_cuda11.0-trt7.1-fp32-T4_b8_1.2.7.model\",\n" +
                "                        \"max_batch_size\": 8,\n" +
                "                        \"model_name\": \"b_digit_case\",\n" +
                "                        \"scale\": 1280,\n" +
                "                        \"polygons\": aaaa\n" +
                "                       }\n" +
                "                    }\n" +
                "                 ]\n" +
                "             }\n" +
                "   ]\n" +
                "}";

        controlPipeStrng = controlPipeStrng.replace("aaaa", JSON.toJSONString(toArrayStringRi(policyRoi)));

        controlPipeStrng = controlPipeStrng.replace("49650", sourceId);
        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);


        log.info(">>> [FloorOcr] update roi_filter device is: {} , roi is: {}", sourceId, controlPipeStrng);

    }

    public String getSelectRoiFilter(String roi, String defRoi) {
        if (StringUtils.isBlank(roi)) {
            return defRoi;
        }
        Integer[][][] rois = JSON.parseObject(roi, Integer[][][].class);
        if (rois.length <= 0) {
            return defRoi;
        }
        return JSON.toJSONString(toArrayStringRi(rois));
    }

    public static String[] toArrayStringRi(Integer[][][] arr) {
        if(arr == null){
            return  new String[0];
        }
        if(arr.length <= 0){
            return  new String[0];
        }
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr[i]);
        }
        return result;
    }

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        //log.info("outputKesons{}", KesonUtils.kesonToJson(outputKesons[0]));
        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons)[0], imageIds, "source_id");

        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);
        }
    }


    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        Map<String, Object> detectResult = (Map<String, Object>) modelResult.getDetectResult();
        log.info("detectResult{}", detectResult);
        if (MapUtils.isEmpty(detectResult))
            return false;

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get("targets");
        Iterator<Map<String, Object>> its = targets.iterator();


        return !targets.isEmpty();
    }


    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        Map<String, Object> detectResult = (Map<String, Object>) modelResult.getDetectResult();
        if (MapUtils.isEmpty(detectResult))
            return;

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get("targets");


        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        Pointer kesonTargets = KestrelApi.keson_get_object_item(modelResult.getKeson().getValue(), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        log.info("targets {}, size{}, kenson{}", targets, kesonTarget_size, KesonUtils.kesonToJson(modelResult.getKeson()));

        for (Map<String, Object> target : targets) {
            Map<String, Object> valueResult = new HashMap<String, Object>();
//            valueResult.put("roi", target.get("roi"));
//            valueResult.put("detect", target.get("target_image_roi"));
            valueResult.put("deviceId", deviceId);

            if (target.containsKey("textline")) {
                //long text_image_id = (long) target.get("image_id");
                List<Map<String, Object>> textLines = (List<Map<String, Object>>) target.get("textline");
                List<Map<String, Object>> contents = textLines.stream()
                        .map(textL -> {
                            Map<String, Object> contentsSingle = new HashMap<>();
                            Map<String, Object> content = (Map<String, Object>) textL.getOrDefault("content", Map.of());

                            contentsSingle.put("score", textL.getOrDefault("score", 0));
                            contentsSingle.put("roi", (List<Map<String, Object>>) textL.get("roi"));
                            Map<String, String> readable = (Map<String, String>) ((Map<String, Object>) content.getOrDefault("$binary", Map.of())).get("$readable");
                            contentsSingle.put("content", readable.getOrDefault("content", ""));
                            return contentsSingle;
                        })
                        .collect(Collectors.toList());
//                Float confidence = textLines.stream().map(line -> ((Number) line.get("score")).floatValue()).reduce(Float::sum).get() / textLines.size();
//                String contentText = textLines.stream().map(line -> (String) ((Map<String, Object>) line.getOrDefault("content", Map.of())).get("utf8")).filter(StringUtils::isNotBlank).collect(Collectors.joining());
//                contents[text_image_id] = Map.of("score", confidence, "content", Map.of("utf8", contentText));

                valueResult.put("textline", contents);
            }

            Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                    .limit(kesonTarget_size)
                    .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "id")) == ((Number) target.get("id")).longValue())
                    .findAny()
                    .orElse(null);

            if (kesonTarget != null) {
                log.info("kesonTarget{}", kesonTarget_size);

//                PointerByReference targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
//                String targetImagePath = FrameUtils.save_image_as_jpg(targetImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
//                //valueResult.put("targetImage", targetImagePath);

                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                String sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                valueResult.put("sceneImage", sceneImagePath);
            }

            outputResult.add(valueResult);

        }

        //log.info("outputResult {}", outputResult);

        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});

        if (outputResult.isEmpty())
            modelResult.setOutputResult(null);
        else
            modelResult.setOutputResult(outputResult);

    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if (e != null)
            for (Map<String, Object> param : (List<Map<String, Object>>) e) {
                if (!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer) param.get("roiIndex");
                if (roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;
            long contextId = Utils.keyToContextId(event.getDeviceId());

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(contextId);
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("floorOcrAuto [" + getClass().getSimpleName() + "] start context [" + contextId + "].");

        floorOcrAutoMap.put(contextId, "");
    }

    private void stopContext(long contextId) {
        holders[0].controlForRemoveSource(contextId);
        log.info("floorOcrAuto [" + getClass().getSimpleName() + "] stop context [" + contextId + "].");

        floorOcrAutoMap.remove(contextId);
    }


}



