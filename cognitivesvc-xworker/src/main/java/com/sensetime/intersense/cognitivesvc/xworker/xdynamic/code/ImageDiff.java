package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
public class ImageDiff extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    @Getter
    protected final boolean lazyInit = false;

    @Getter
    protected final Integer interval = 0;//检一跳三

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final boolean queueMap = true;

    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";

    protected final double minDiffRate = 0.7;

    protected ConcurrentHashMap<Long, Map<String, String>> diffStore = new ConcurrentHashMap<Long, Map<String, String>>();

    //<deviceId_roiId, counter>
    protected final ConcurrentHashMap<String, LinkedBlockingQueue<BufferImageDiff>> bufferCounterImageDiff = new ConcurrentHashMap<String, LinkedBlockingQueue<BufferImageDiff>>();

    private final ConcurrentHashMap<Long, Long> trackDiffMap = new ConcurrentHashMap<Long, Long>();

    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        boolean logged = Utils.instance.watchFrameTiktokLevel == -321;
        if (logged) {
            log.info("[imageDiff] batch_extract_xmodel_asap status={}", status);
        }
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];

        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);

        if (logged) {
            log.info("[imageDiff] batch_extract_xmodel_asap param_keson={}", KesonUtils.kesonToJson(param_keson));
        }
        int handlingIndex = 0;
        String deviceId = (String) handlingList.get(handlingIndex).getModelRequest().getParameter().get("deviceId");
        VideoStreamInfra deviceInfra = (VideoStreamInfra) handlingList.get(handlingIndex).getModelRequest().getParameter().get("deviceInfra");
        Long streamSourceId = Utils.keyToContextId(deviceId);
        if (!diffStore.containsKey(streamSourceId)) {
            diffStore.put(streamSourceId, new ConcurrentHashMap<String, String>());
        }
        Map<String, String> roiCongregate = diffStore.get(streamSourceId);

        for (int index = 0; index < pointers.length; index++) {
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            if (index == 0) {
                Integer[][][] proi = handlingList.get(index).getModelRequest().getProcessor().getRoi();

                CognitiveEntity.Processor processor = handlingList.get(index).getModelRequest().getProcessor();
                Polygon[] polygons = processor.fetchPolygons();
                if (ArrayUtils.isEmpty(polygons))
                    polygons = new Polygon[]{new Polygon(new int[]{0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[]{0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};

                Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);

                List<Integer[][]> rois = new ArrayList<>();
                List<Integer> positionTypeList = new ArrayList<>();

                String useSingleFramePath = (String) getExtraValue(handlingList.get(index).getModelRequest().getProcessor().getExtras(), "useSingleFramePath", "");

                for (int indexs = 0; indexs < polygons.length; indexs++) {
                    Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
                    if (extra.isEmpty())
                        continue;
                    Integer[][] singleRoi = proi[indexs];
                    if (singleRoi.length <= 0)
                        continue;
                    rois.add(singleRoi);
                    positionTypeList.add(((Number) extra.getOrDefault("positionType", 0)).intValue());
                }
                String policyRoiString = JSON.toJSONString(toArrayStringRoiList(rois));

                //roi
                if (roiCongregate.get(ROISTRING) == null) {
                    roiCongregate.put(ROISTRING, policyRoiString);
                    updateRoi(pointers[index].pointers[0], proi, streamSourceId, true,
                            handlingList.get(index).getModelRequest().getVideoFrames()[0].getFrame(),
                            useSingleFramePath, "roi");
                } else {
                    String oldRoiString = roiCongregate.get(ROISTRING);
                    if (!oldRoiString.equals(policyRoiString)) {
                        roiCongregate.put(ROISTRING, policyRoiString);
                        updateRoi(pointers[index].pointers[0], proi, streamSourceId, false, handlingList.get(index).getModelRequest().getVideoFrames()[0].getFrame(), useSingleFramePath, "roiUp");
                    }
                }
                if (roiCongregate.get(BASEIMAGESTRING) == null) {
                    roiCongregate.put(BASEIMAGESTRING, useSingleFramePath);
                    updateRoi(pointers[index].pointers[0], proi, streamSourceId, true,
                            handlingList.get(index).getModelRequest().getVideoFrames()[0].getFrame(),
                            useSingleFramePath, "Frame");
                } else {
                    String oldRoiString = roiCongregate.get(BASEIMAGESTRING);
                    if (!oldRoiString.equals(useSingleFramePath)) {
                        roiCongregate.put(BASEIMAGESTRING, useSingleFramePath);
                        updateRoi(pointers[index].pointers[0], proi, streamSourceId, false,
                                handlingList.get(index).getModelRequest().getVideoFrames()[0].getFrame(),
                                useSingleFramePath, "FrameU");
                    }
                }

            }
            long now = System.currentTimeMillis();
            /** 执行模型 获取数据*/
            pointers[index].process(inTo.getValue(), outOf);
            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        }
        KesonUtils.kesonDeepDelete(param_keson);
        //log.info("output_kesons={}", KesonUtils.kesonToJson(output_kesons[0]));
        return output_kesons;
    }

    /** 准备输入数据 */
    protected PointerByReference prepareInput(List<BatchItem> handlingList) {
        Pointer[] frames = new Pointer[handlingList.size()];
        Long[] sourceIds = new Long[handlingList.size()];

        for (int index = 0; index < handlingList.size(); index++) {
            frames[index] = handlingList.get(index).getModelRequest().getVideoFrames()[0].getFrame();
        }
        //cpu frame也不行，性能18路

        if(StringUtils.isBlank(handlerEntity.getFlockConfig()))
            return new PointerByReference(KesonUtils.frameToKeson(frames, sourceIds));
        else {
            for (int index = 0; index < handlingList.size(); index++) {
                Object deviceId = handlingList.get(index).getModelRequest().getParameter().get("deviceId");
                if(deviceId == null)
                    sourceIds[index] = (long)index;
                else
                    sourceIds[index] = Utils.keyToContextId(deviceId);
            }
            //log.info("frameToKesons {},{}", KesonUtils.kesonToJson(frames[0]),KesonUtils.kesonToJson(KesonUtils.frameToKeson(frames, sourceIds)));

            return new PointerByReference(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), KesonUtils.frameToKeson(frames, sourceIds)));
        }
    }

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        //log.info("outputKesonsRRRR{}", KesonUtils.kesonToJson(outputKesons[0]));
        //log.info("handlingListSize{}", handlingList.size());
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds, "source_id");
        //log.info("sub0_kesons{}", sub0_kesons.length);
        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();

            if (sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            handlingList.get(index).setKeson(new PointerByReference(array));
        }
        KesonUtils.kesonDeepDelete(outputKesons);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {

        boolean logged = Utils.instance.watchFrameTiktokLevel == -321;
        if (logged) {
            log.info("[imageDiff] DetectResult{}", modelResult.getDetectResult());
        }
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");

        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return false;

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        Map<String, Object> bufferMap = processor.fetchBufferMap();
        if (MapUtils.isEmpty(bufferMap)) {
            log.error("[imageDiff] bufferMap is null ,deviceId={}", deviceId);
            return false;
        }
        long now = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();

        int bufferSize = (Integer) bufferMap.getOrDefault("bufferSize", 5);
        int bufferExpire = (Integer) bufferMap.getOrDefault("bufferExpire", bufferSize * 2 * Objects.requireNonNullElse(processor.getInterval(), 10));

        float minDiffRate = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "minDiffRate", this.minDiffRate)).floatValue();

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");

        Iterator<Map<String, Object>> its = targets.iterator();

        while (its.hasNext()) {
            Map<String, Object> target = its.next();

            LinkedBlockingQueue<BufferImageDiff> counter = bufferCounterImageDiff.get(deviceId);
            if (counter == null) {
                counter = new LinkedBlockingQueue<BufferImageDiff>();
                bufferCounterImageDiff.put(deviceId, counter);
            }
            for (BufferImageDiff first = counter.peek(); !counter.isEmpty() && (first != null && now - first.getTime() > bufferExpire * 1000); first = counter.peek())
                counter.poll();

            Map<String, Integer> detect = (Map<String, Integer>) target.get("roi");
            if(detect == null){
                log.error("detect is empty={}", detect);
                continue;
            }
            counter.add(new BufferImageDiff(now, (Map<String, Integer>) detect, 0.0f, 0.0f));

            BufferImageDiff current = new BufferImageDiff(now, detect, ((Number) target.get("nomoralize_diff_score")).floatValue(), minDiffRate);

            if (logged) {
                log.info(">>> [imageDiff] target.get(\"nomoralize_diff_score\")={},minDiffRate={},deviceId={}", target.get("nomoralize_diff_score"),minDiffRate,deviceId);
            }
            counter.add(current);
            //计算满足的个数
            List<BufferImageDiff> bingos = counter.stream().filter(previous -> current.in(previous)).collect(Collectors.toList());

            long diffTimeCount = bingos.stream().mapToDouble(b -> b.getScale()).distinct().count();

            if (logged) {
                log.info(">>> [imageDiff] bingosize={},bingos={}, diffTimeCount={},deviceId={}", bingos.size(),bingos, diffTimeCount, deviceId);
            }
            //阈值满足
            if (diffTimeCount < bufferSize) {
                counter.add(current);
                its.remove();
            } else {
                counter.removeAll(bingos);
            }
            //刷新
        }

        return !CollectionUtils.isEmpty(detectResult);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void buildOutputValue(ModelResult modelResult) {

        boolean logged = Utils.instance.watchFrameTiktokLevel == -321;
        if (logged) {
            log.info("[imageDiff] DetectResult{}", modelResult.getDetectResult());
        }
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult)) {
            return;
        }
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 0), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        List<String> roiIds = (List<String>) ((List<Map<String, Object>>) Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getExtras(), List.of()))
                .stream()
                .filter(item -> "roiIds".equals(item.get("type")))
                .findAny()
                .orElse(Map.of())
                .getOrDefault("roiIds", List.of());

        String useSingleFramePath = (String) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "useSingleFramePath", "");

        Polygon[] polygons = processor.fetchPolygons();

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");

        if (CollectionUtils.isEmpty(targets)) {
            //log.warn("imageDiff  targets is empty");
            return;
        }
        List<Map<String, Object>> roisArray = new ArrayList<>();

        for (Map<String, Object> target : targets) {
            Map<String, Object> transformedTarget = new HashMap<>();

            transformedTarget.put("trackId", -1);
            transformedTarget.put("baseImage", useSingleFramePath);


            transformedTarget.put("detect", target.get("roi"));

            Map<String, Object> roi = (Map<String, Object>) target.get("roi");
            if (roi == null) {
                log.error("rois is null error");
                continue;
            }
            int top = ((Number) roi.get("top")).intValue();
            int left = ((Number) roi.get("left")).intValue();
            int width = ((Number) roi.get("width")).intValue();
            int height = ((Number) roi.get("height")).intValue();
            int x = left + width / 2;
            int y = top + height / 2;


            if (ArrayUtils.isEmpty(polygons)) {
                transformedTarget.put("rois", SCREEN);
            } else {
                int[] roiIndexes = IntStream.range(0, polygons.length).filter(index -> polygons[index].contains(x, y)).toArray();
                if (ArrayUtils.isEmpty(roiIndexes)) {
                    log.warn("imageDiff  rois is not contains");
                    continue;
                }
                transformedTarget.put("rois", Arrays.stream(roiIndexes).mapToObj(id -> id < roiIds.size() ? roiIds.get(id) : String.valueOf(id)).toArray(String[]::new));
            }

            transformedTarget.put("diffScore", target.get("diff_score"));
            transformedTarget.put("nomoralizeDiffScore", target.get("nomoralize_diff_score"));
            Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                    .limit(kesonTarget_size)
                    .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "source_id")) == ((Number) target.get("source_id")).longValue())
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "image_id")) == ((Number) target.get("image_id")).longValue())
                    .findAny()
                    .orElse(null);

            if (kesonTarget != null) {
                log.info("start save image for congregate");

                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                if (sceneImage.getValue() != null) {
                    String sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                    transformedTarget.put("sceneImage", sceneImagePath);
                }
            }


            transformedTarget.put("attributes", List.of(
                    Map.of("key", "nomoralizeDiffScore", "value", target.get("nomoralize_diff_score"), "confidence", 1.0f),
                    Map.of("key", "diffScore", "value", target.get("diff_score"), "confidence", 1.0f)));

            transformedTarget.put("deviceId", target.get("source_id"));

            roisArray.add(transformedTarget);
        }
        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});

        if (roisArray.isEmpty())
            modelResult.setOutputResult(null);
        else
            modelResult.setOutputResult(roisArray);

        postProcessOutputValue(modelResult);
    }

    public void updateRoi(Pointer pipelinePoint, Integer[][][] policyRoi, Long sourceId, boolean check, Pointer frame,
                          String useSingleFramePath,String from) {

        int left = Integer.MAX_VALUE;
        int top = Integer.MAX_VALUE;
        int right = Integer.MIN_VALUE;
        int bottom = Integer.MIN_VALUE;

        for (Integer[][] subArray : policyRoi) {
            for (Integer[] point : subArray) {
                left = Math.min(left, point[0]);
                top = Math.min(top, point[1]);
                right = Math.max(right, point[0]);
                bottom = Math.max(bottom, point[1]);
            }
        }

        int width = right - left;
        int height = bottom - top;

        String controlPipeStrng = "{\n" +
                "            \"streams\": [\n" +
                "             {\n" +
                "                 \"name\": \"image_diff_ips\",\n" +
                "                 \"modules\": [\n" +
                "                   {\n" +
                "                    \"name\": \"image_diff\",\n" +
                "                    \"source_id\": 49650,\n" +
                "                    \"type\": \"ImagesDiff\",\n" +
                "                    \"inputs\": [\n" +
                "                        \"images\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"diff_targets\"\n" +
                "                    ],\n" +
                "                    \"config\": {\n" +
                "                        \"base_image_base64\": \"base_image_base64_path\",\n" +
                "                        \"detect_area\": {\n" +
                "                            \"left\": leftValue,\n" +
                "                            \"top\":  topValue,\n" +
                "                            \"width\": widthValue,\n" +
                "                            \"height\": heightValue\n" +
                "                       }\n" +
                "                    }\n" +
                "                 }\n" +
                "              ]\n" +
                "           }\n" +
                "       ]\n" +
                "  }";

        controlPipeStrng = controlPipeStrng.replace("leftValue", String.valueOf(left));
        controlPipeStrng = controlPipeStrng.replace("topValue", String.valueOf(top));
        controlPipeStrng = controlPipeStrng.replace("widthValue", String.valueOf(width));
        controlPipeStrng = controlPipeStrng.replace("heightValue", String.valueOf(height));


        String targetImagePath = "";
        if (StringUtils.isBlank(useSingleFramePath)) {
            Pointer FrameCgo = FrameUtils.ref_frame(frame);
            targetImagePath = FrameUtils.save_image_as_jpg(FrameCgo, ImageUtils.newFile(annotatorName()), 0);
            FrameUtils.batch_free_frame(FrameCgo);
        } else {
            targetImagePath = useSingleFramePath;
        }
        //osg
        byte[] bytes = new byte[0];
        try {
            bytes = fileAccessor.readImage(targetImagePath);

            Base64.Encoder encoder = Base64.getEncoder();
            targetImagePath = encoder.encodeToString(bytes);

            controlPipeStrng = controlPipeStrng.replace("base_image_base64_path", targetImagePath);
            //controlPipeStrng = controlPipeStrng.replace("base_image_path", "");
        } catch (Exception e) {
            e.printStackTrace();
            //controlPipeStrng = controlPipeStrng.replace("base_image_path", targetImagePath);
        }

        controlPipeStrng = controlPipeStrng.replace("49650", sourceId.toString());

        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);

        log.info(">>> [imageDiff] update_roi_filter device is={}, roi={},check={},useSingleFramePath={}", sourceId, controlPipeStrng, check, useSingleFramePath);
        //holders[0].controlForRemoveSource(sourceId);
        if (!check) {
            PointerByReference outRemove = new PointerByReference();
            KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), outRemove);
            KesonUtils.kesonDeepDelete(outRemove);
        }
        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

        KesonUtils.kesonDeepDelete(out);

        KesonUtils.kesonDeepDelete(input);
        log.info(">>> [imageDiff] update_roi_filter device end={},{}", sourceId, from);
    }

    public void updateRoiBuildFlock(Pointer pipelinePoint, Integer[][][] policyRoi, Long sourceId, boolean check, Pointer frame) {

        int left = Integer.MAX_VALUE;
        int top = Integer.MAX_VALUE;
        int right = Integer.MIN_VALUE;
        int bottom = Integer.MIN_VALUE;

        for (Integer[][] subArray : policyRoi) {
            for (Integer[] point : subArray) {
                left = Math.min(left, point[0]);
                top = Math.min(top, point[1]);
                right = Math.max(right, point[0]);
                bottom = Math.max(bottom, point[1]);
            }
        }

        int width = right - left;
        int height = bottom - top;


        Pointer inputDiff = KestrelApi.keson_create_object();

        Pointer array = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_object(inputDiff, "streams", array);

        Pointer item = KestrelApi.keson_create_object();
        KestrelApi.keson_add_item_to_array(array, item);

        Pointer arrayModules = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_object(item, "modules", arrayModules);

        KestrelApi.keson_add_item_to_object(item, "name", KestrelApi.keson_create_string("image_diff_ips"));

        Pointer itemModules = KestrelApi.keson_create_object();
        KestrelApi.keson_add_item_to_array(arrayModules, itemModules);

        KestrelApi.keson_add_item_to_object(itemModules, "name", KestrelApi.keson_create_string("image_diff"));
        KestrelApi.keson_add_item_to_object(itemModules, "type", KestrelApi.keson_create_string("ImagesDiff"));
        KestrelApi.keson_add_item_to_object(itemModules, "source_id", KestrelApi.keson_create_int(sourceId));
        KestrelApi.keson_add_item_to_object(itemModules, "context_id", KestrelApi.keson_create_int(sourceId));

        // 添加 "inputs" 数组
        Pointer inputsArray = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_array(inputsArray, KestrelApi.keson_create_string("images"));
        KestrelApi.keson_add_item_to_object(itemModules, "inputs", inputsArray);

        // 添加 "outputs" 数组
        Pointer outputsArray = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_array(outputsArray, KestrelApi.keson_create_string("diff_targets"));
        KestrelApi.keson_add_item_to_object(itemModules, "outputs", outputsArray);

        // 添加 "config" 对象
        Pointer configObject = KestrelApi.keson_create_object();

        Pointer detectAreaObject = KestrelApi.keson_create_object();

        KestrelApi.keson_add_item_to_object(detectAreaObject, "left", KestrelApi.keson_create_int(left));
        KestrelApi.keson_add_item_to_object(detectAreaObject, "top", KestrelApi.keson_create_int(top));
        KestrelApi.keson_add_item_to_object(detectAreaObject, "width", KestrelApi.keson_create_int(width));
        KestrelApi.keson_add_item_to_object(detectAreaObject, "height", KestrelApi.keson_create_int(height));
        KestrelApi.keson_add_item_to_object(configObject, "detect_area", detectAreaObject);

        Pointer FrameCgo = FrameUtils.ref_frame(frame);
        KestrelApi.keson_add_item_to_object(configObject, "base_image", KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), FrameCgo));
        KestrelApi.keson_add_item_to_object(itemModules, "config", configObject);

        log.info("inputDiff:{}", KesonUtils.kesonToJson(inputDiff));

        //log.info("buildFlockInput:{}", KesonUtils.buildFlockInput(sourceId, "ImagesDiff", "image_diff"));

//        if (!check) {
//            PointerByReference outRemove = new PointerByReference();
//            KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, inputDiff, outRemove);
//            KesonUtils.kesonDeepDelete(outRemove);
//        }
//
//        PointerByReference out = new PointerByReference();
//        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, inputDiff, out);

//        KesonUtils.kesonDeepDelete(out);
//
//        KesonUtils.kesonDeepDelete(inputDiff);
//       FrameUtils.batch_free_frame(FrameCgo);
//
//
//        try {
//            Thread.sleep(100);
//        }catch (Exception e){
//
//        }
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void postProcessOutputValue(ModelResult modelResult) {

        super.postProcessOutputValue(modelResult);

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        if (StringUtils.isBlank(deviceId) || processor == null || org.apache.commons.collections4.CollectionUtils.isEmpty(processor.getExtras()))
            return;

        Long contextID = Utils.keyToContextId(deviceId);
        long now = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);

        Polygon[] polygons = processor.fetchPolygons();

        boolean toRemove = false;
        for (int index = 0; index < polygons.length; index++) {

            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());

            if (extrasMap.containsKey("trigger")) {
                Map<String, Object> trigger = (Map<String, Object>) extrasMap.get("trigger");

                int trackFreq = (int) trigger.getOrDefault("trackFreq", 1000);

                if (trackDiffMap.get(contextID) != null && (now - trackDiffMap.getOrDefault(contextID, 0L) < trackFreq)) {
                    log.info("imageDiff checkFail {}, {}, {}, {}", deviceId, now, trackDiffMap.getOrDefault(contextID, 0L), trackFreq);
                    toRemove = true;
                }
            }
        }

        if (toRemove) {
            modelResult.setOutputResult(null);
            return;
        }

        trackDiffMap.put(contextID, now);
    }


    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {

        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            log.info("e.getMesasge{}", e.getAnnotatorName());
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        diffStore.remove(contextId);

        log.info("imageDiff start context [" + infra.getDeviceId() + "]. contextID " + contextId);

        diffStore.put(contextId, new ConcurrentHashMap<String, String>());

        trackDiffMap.put(contextId, System.currentTimeMillis());
    }

    private void stopContext(String deviceId, long contextId) {
        log.info("imageDiff stop deviceId [" + deviceId + "].");
        if (!diffStore.containsKey(contextId))
            return;

        holders[0].controlForRemoveSource(contextId);
        diffStore.remove(contextId);
        log.info("imageDiff stop contextId [" + contextId + "].");

        if (!trackDiffMap.containsKey(contextId))
            return;

        trackDiffMap.remove(contextId);

        log.info("imageDiff trackDiffMap stop contextId [" + contextId + "].");
    }

    //point_cal_type：每个结果框会需要计算是否在感兴趣区域，
    //会取一个点来判断：0-> upper_middle ,1-> center ,2-> lower_middle
    private int retypeByPosition(Integer position) {
        if (position == 0) {
            return 1;
        } else if (position == 1) {
            return 0;
        } else if (position == 2) {
            return 2;
        }
        return 1;
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if (e != null)
            for (Map<String, Object> param : (List<Map<String, Object>>) e) {
                if (!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer) param.get("roiIndex");
                if (roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };


    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> functionLabelPerson = e -> {
        Map<String, Object> result = new HashMap<String, Object>();

        if (e != null) {
            return (Map<String, Object>) e;
        }

        return result;

    };

    public static String[] toArrayStringCrowdFunc(Integer[] arr) {
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr);
        }
        return result;
    }


    private static String[] toArrayStringRoiList(List<Integer[][]> arr) {
        int numRows = arr.size();
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr.get(i));
        }
        return result;
    }


    private static String toArrayStringLabelList(Map<String, Object> labelPerson) {

        StringBuilder markHeight = new StringBuilder();
        StringBuilder manualList = new StringBuilder();

        if (labelPerson.containsKey("markHeight")) {
            markHeight.append(labelPerson.get("markHeight").toString());
        }

        if (labelPerson.containsKey("manualList")) {

            JSONArray jsonArray = (JSONArray) labelPerson.get("manualList");

            List<Integer[][]> list = jsonArray.toJavaList(Integer[][].class);
            for (Integer[][] arr : list) {
                for (Integer[] innerArray : arr) {
                    manualList.append(Arrays.deepToString(innerArray));
                }
            }
        }
        return manualList.toString() + markHeight.toString();
    }


    @Data
    @Accessors(chain = true)
    protected static class BufferImageDiff {
        private long time;

        private float scale;
        private float minDiffRate;

        private float centerX;
        private float centerY;

        private float left;
        private float top;
        private float right;
        private float bottom;

        public BufferImageDiff(long time, Map<String, Integer> detect, float scale, float minDiffRate) {
            centerX = detect.get("left") + detect.get("width") / 2;
            centerY = detect.get("top") + detect.get("height") / 2;

            left = centerX - (1 + scale) * detect.get("width") / 2;
            top = centerY - (1 + scale) * detect.get("height") / 2;
            right = centerX + (1 + scale) * detect.get("width") / 2;
            bottom = centerY + (1 + scale) * detect.get("height") / 2;

            this.time = time;

            this.scale = scale;
            this.minDiffRate = minDiffRate;
        }

        public boolean in(BufferImageDiff other) {

            //log.info("BufferImageDiff{},minDiffRate={}", other.scale, minDiffRate);
            return other.scale >= minDiffRate;
        }
    }

    @SuppressWarnings("unchecked")
    private static Object getExtraValue(List<Map<String, Object>> extras, String key, Object def) {
        return Objects.requireNonNullElse(extras, List.of())
                .stream()
                .filter(extra -> key.equals(((Map<String, Object>) extra).get("type")))
                .map(extra -> ((Map<String, Object>) extra).getOrDefault("value", def))
                .findAny()
                .orElse(def);

    }

    private static final String ROISTRING = "diff_roi";
    private static final String BASEIMAGESTRING = "base_image";


}
