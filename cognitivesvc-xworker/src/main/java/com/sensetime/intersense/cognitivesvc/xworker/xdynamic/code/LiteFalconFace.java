package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LiteFalconFace extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    
    //<contextid, trackid, track>
    protected final ConcurrentHashMap<Long, Map<Integer, Track>> trackingMap = new ConcurrentHashMap<Long, Map<Integer, Track>>();
    
    @Getter
    protected final int drainPollcount = 2;
    
    @Getter
    protected final int drainTimeout = 10;
    
    @Getter
    protected final Boolean blocking = false;
    
    @Getter
    protected final Boolean needContext = true;

    @Getter
    protected final String decoderFormat = "rgb24";
    
    @Getter
    protected final Integer frameBuffer = 24;//显存小的用20, 显存大了稍微提高一些
    
    @Getter
    protected final String frameBufferStrategy = "smart";
    
    @Getter
    protected final Integer interval = 8;
    
    @Getter
    protected final boolean queueMap = true;
    
    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {        
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds);
        PointerByReference[] sub1_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[1])[0], imageIds);
        
        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();
            
            if(sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));
            
            if(sub1_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub1_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));
            
            handlingList.get(index).setKeson(new PointerByReference(array));
        }

        KesonUtils.kesonDeepDelete(outputKesons);
    }
    
    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || modelResult.getKeson().getValue() == null)
            return ;
        
        modelResult.setDetectResult(KesonUtils.kesonToJson(modelResult.getKeson()));
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>)modelResult.getDetectResult();
        if(CollectionUtils.isEmpty(detectResult))
            return false;

        long sourceId = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        Map<Integer, Track> trackMap = trackingMap.get(sourceId);
        if(trackMap == null)
            return false;
        
        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.get(0).get("targets");
        List<Map<String, Object>> tracklets = (List<Map<String, Object>>)detectResult.get(1).get("targets");

        List<Map<String, Object>> actions = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> faces = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> bodys = new ArrayList<Map<String, Object>>();
        List<Number> drops = new ArrayList<Number>();
        
        for(Map<String, Object> target : targets) {
            int dropped_track_id = ((Number)target.getOrDefault("dropped_track_id", -1)).intValue();
            if(dropped_track_id >= 0) 
            	drops.add(dropped_track_id);
            else {
            	int label = ((Number)target.getOrDefault("label", -1)).intValue();
                if(label == 37017)
                    faces.add(target);
                else if(label == 221488) 
                    bodys.add(target);
                else if(target.containsKey("action_type")) 
                    actions.add(target);
            }
        }
        
        for(Map<String, Object> body : bodys) { 
            int trackId = ((Number)body.get("track_id")).intValue();
            
            Track track = trackMap.get(trackId);
            if(track == null) {
                track = Track.builder().trackId(trackId).build();
                trackMap.put(trackId, track);
            }
        }
        
        for(Map<String, Object> face : faces) {
            int trackId = ((Number)face.get("track_id")).intValue();
            Track track = trackMap.get(trackId);
            if(track == null) {
                track = Track.builder().trackId(trackId).faceTrackId(trackId).build();
                trackMap.put(trackId, track);
            }
            
            List<Map<String, Object>> associations = (List<Map<String, Object>>)face.get("associations");
            if(CollectionUtils.isNotEmpty(associations)) {
                int faceTrackId = ((Number)face.get("track_id")).intValue();
                int bodyTrackId = ((Number)associations.get(0).get("track_id")).intValue();
                
                Track bodyTrack = trackMap.get(bodyTrackId);
                if(bodyTrack != null)
                    bodyTrack.setFaceTrackId(faceTrackId);
            }
        }
        
        for(Map<String, Object> tracklet : tracklets) {
            int label = ((Number)tracklet.getOrDefault("label", -1)).intValue();
            if(label == 37017){
                int trackId = ((Number)tracklet.get("track_id")).intValue();
                Pointer sceneImage = findSceneImage(modelResult.getKeson().getValue(), sourceId, trackId);
                Map<String, Number> roi = (Map<String, Number>)tracklet.get("target_image_roi");
                
                Track faceTrack = trackMap.get(trackId);
                if(faceTrack != null) {
                    faceTrack.setFaceFrame(FrameUtils.roi_frame(sceneImage, roi, 0.3f));
                    faceTrack.setFaceAttributes((Map<String, Map<String, Number>>) tracklet.get("attribute")); 
                }
                
                Track bodyTrack = trackMap.values().stream().filter(track -> track.getFaceTrackId() != null && track.getFaceTrackId() == trackId).findAny().orElse(null);
                if(bodyTrack != null) {
                    bodyTrack.setFaceFrame(FrameUtils.roi_frame(sceneImage, roi, 0.3f));
                    bodyTrack.setFaceAttributes((Map<String, Map<String, Number>>) tracklet.get("attribute")); 
                }
            }else if(label == 221488) {
                int trackId = ((Number)tracklet.get("track_id")).intValue();
                Track bodyTrack = trackMap.get(trackId);
                if(bodyTrack != null)
                    bodyTrack.setPedAttributes(dealWithAge((Map<String, Map<String, Number>>) tracklet.get("attribute")));
            }
        }
        
        if(CollectionUtils.isNotEmpty(drops))
            modelResult.getModelRequest().getParameter().put("droppedIds", drops);
        
        detectResult.get(0).put("targets", actions);
        return !actions.isEmpty();
    }
    
    @SuppressWarnings({ "unchecked"})
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();
        Map<Integer, Track> trackMap = trackingMap.get(Utils.keyToContextId(deviceId));

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        
        Function<Map<String, Object>, Map<String, Object>> mapper = target -> {
            Map<String, Object> result = new HashMap<String, Object>();
            
            result.put("detect",      target.get("action_roi"));
            result.put("targetType",  target.get("label"));
            result.put("label",       target.get("label"));
            result.put("action_type", target.get("action_type"));
            result.put("value",       target.get("action_type"));
            result.put("confidence",  target.get("confidence"));
            
            List<Map<String, Object>> associations = ((List<Map<String, Object>>)target.getOrDefault("associations", List.of()))
                    .parallelStream()
                    .map(association -> {
                        int label = ((Number)association.getOrDefault("label", -1)).intValue();
                        int trackId = ((Number)association.getOrDefault("track_id", -1)).intValue();
                        
                        if(label == 37017) {
                            Track bodyTrack = trackMap.values().stream().filter(t -> t.getFaceTrackId() != null && t.getFaceTrackId() == trackId).findAny().orElse(null);
                            if(bodyTrack != null) {
                                bodyTrack.setBaggage(association);
                                return bodyTrack;
                            }else {
                                Track track = trackMap.get(trackId);
                                track.setBaggage(association);
                                return track;
                            }
                        }
                        Track track = trackMap.get(trackId);
                        track.setBaggage(association);
                        return track;
                    })
                    .filter(Objects::nonNull)
                    .distinct()
                    .map(track -> {
                        findoutWhoItIs(track);
                        
                        Map<String, Object> association = track.getBaggage();
                        track.setBaggage(null);
                        association.put("pedAttributes", rawAttributeMapToOutput(track.getPedAttributes()));
                        
                        if(track.getPersonId() != null) {
                            association.put("personId", track.getPersonId());
                            association.put("personScore", track.getPersonScore());
                            association.put("faceAttributes", rawAttributeMapToOutput(track.getFaceAttributes()));
                        }else if(track.getFaceFrame() != null) {
                            String imagePath = FrameUtils.save_image_as_jpg(track.getFaceFrame(), ImageUtils.newFile(handlerEntity.getAnnotatorName()),processor.getImgSaveTag());
                            association.put("image", imagePath);
                            association.put("faceAttributes", rawAttributeMapToOutput(track.getFaceAttributes()));
                        }
                        
                        return association;
                    })
                    .collect(Collectors.toList());

            result.put("associations", associations);
            return result;
        };
        
        List<Map<String, Object>> actions = (List<Map<String, Object>>)((List<Map<String, Object>>)modelResult.getDetectResult()).get(0).get("targets");
        modelResult.setOutputResult(actions.parallelStream().map(mapper).collect(Collectors.toList()));
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void releaseModelResult(ModelResult modelResult) {
        super.releaseModelResult(modelResult);
        
        long sourceId = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        Map<Integer, Track> trackMap = trackingMap.get(sourceId);
        if(trackMap == null)
            return ;
        
        List<Number> drops = (List<Number>)modelResult.getModelRequest().getParameter().getOrDefault("droppedIds", List.of());
        for(Number drop : drops) {
            Track track = trackMap.remove(drop.intValue());
            if(track != null)
                track.setFaceFrame(null);
        }
    }
    
    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;            
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            long contextId = Utils.keyToContextId(event.getDeviceId());
            
            try { Thread.sleep(1000); } catch (InterruptedException x) { }
            stopContext(contextId);
        }
    }
    
    private void startContext(long contextId, VideoStreamInfra infra) {
        if(trackingMap.containsKey(contextId))
            return ;
        
        trackingMap.put(contextId, new ConcurrentHashMap<Integer, Track>());
        
        log.info("LiteFalconFace start context [" + contextId + "].");
    }
    
    private void stopContext(long contextId) {
        if(!trackingMap.containsKey(contextId))
            return ;
        
        Map<Integer, Track> tracks = trackingMap.remove(contextId);
        tracks.values().forEach(t -> t.setFaceFrame(null));
        
        holders[0].controlForRemoveSource(contextId);
        
        log.info("LiteFalconFace stop context [" + contextId + "].");
    }
    
    @SuppressWarnings("unchecked")
    private final Track findoutWhoItIs(Track track) {
        if(track.getFaceFrame() == null || track.getPersonId() != null)
            return track;
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> data = new LinkedMultiValueMap<String, String>();
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(data, headers);
            
            data.add("figureImageBase64", ImageUtils.bytesToBase64(FrameUtils.encode_image_as_jpg(track.getFaceFrame())));
            
            JSONObject response = restTemplate.postForObject("http://cognitivesvc/cognitive/face/compareFaceImage", request , JSONObject.class);
            List<Map<String, Object>> persons = (List<Map<String, Object>>)response.getOrDefault("data", List.of());
            if(CollectionUtils.isNotEmpty(persons)) {
                track.setPersonId(persons.get(0).get("personID").toString());
                track.setPersonScore(((Number)persons.get(0).get("score")).floatValue());
            }
        }catch(Exception e) {
            e.printStackTrace();
        }
        
        return track;
    }
    
    private static final Map<String, Map<String, Number>> dealWithAge(Map<String, Map<String, Number>> rawAttributeMap) {
        if(MapUtils.isEmpty(rawAttributeMap))
            return rawAttributeMap;
        
        Map<String, Number> upper = rawAttributeMap.remove("age_up_limit");
        if(upper != null)
            rawAttributeMap.put("age_up_limit", new HashMap<String, Number>(Map.of(upper.get("age_up_limit").toString(), 1)));
        
        Map<String, Number> lower = rawAttributeMap.remove("age_lower_limit");
        if(lower != null) 
            rawAttributeMap.put("age_lower_limit", new HashMap<String, Number>(Map.of(lower.get("age_lower_limit").toString(), 1)));
        
        return rawAttributeMap;
    }
    
    private static final Pointer findSceneImage(Pointer keson, long sourceId, int trackId) {
        Pointer tracklets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(keson, 1), "targets");
        int trackletsize = KestrelApi.keson_array_size(tracklets);
        
        for(int index = 0; index < trackletsize; index ++) {
            Pointer tracklet = KestrelApi.keson_get_array_item(tracklets, index);
            if(sourceId == KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(tracklet, "source_id"))
                    && trackId  == KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(tracklet, "track_id"))) {
                return KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(tracklet, "scene_frame")).getValue();
            }
        }
        
        return null;
    }
    
    private static final List<Map<String, Object>> rawAttributeMapToOutput(Map<String, Map<String, Number>> rawAttributeMap){
        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        
        if(rawAttributeMap != null) {
            for(Entry<String, Map<String, Number>> entry : rawAttributeMap.entrySet()) {
                try {
                    Entry<String, Number> maxEntry = entry.getValue().entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
                    outputResult.add(Map.of("key", entry.getKey(), "value", maxEntry.getKey(), "confidence", maxEntry.getValue()));
                }catch(Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return outputResult;
    }
    
    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class Track{        
        private final int trackId;
        
        private volatile Pointer faceFrame;
        
        @Setter
        private volatile Map<String, Map<String, Number>> faceAttributes;
        
        @Setter
        private volatile Map<String, Map<String, Number>> pedAttributes;

        @Setter
        private volatile Integer faceTrackId;

        @Setter
        private volatile String personId;

        @Setter
        private volatile Float personScore;
        
        @Setter
        private transient volatile Map<String, Object> baggage;
        
        public void setFaceFrame(Pointer faceFrame) {
            if(this.faceFrame != null)
                FrameUtils.batch_free_frame(this.faceFrame);
            
            this.faceFrame = faceFrame;
            this.personId = null;
        }
    }
}