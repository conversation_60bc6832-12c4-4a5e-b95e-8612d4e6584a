package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.*;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import java.util.*;

public class CraneScoreProcess extends DynamicXModelHandler{
	
    @Getter
    private final Boolean blocking = false;
    
    @Getter
    protected final boolean lazyInit = true;

    @Getter
    protected final String decoderFormat = "rgb24";
    
    @Getter
    protected final Integer frameBuffer = 4;//显存小的用20, 显存大了稍微提高一些
    
    @Getter
    protected final String frameBufferStrategy = "smart";
    
    @Getter
    protected final Integer minBatchSize = 2;//不低于2

    @Getter
    protected final Integer minBatchStagger = 1;//数字越小 精度越高 资源消耗也越大

    @Getter
    private final Integer interval = 25 * 2;    

    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if(status != 0)
            return new PointerByReference[0];
        
        Pointer[] befores = handlingList.stream().map(item -> item.getModelRequest().getVideoFrames()[0].getGpuFrame()).toArray(Pointer[]::new);
        Pointer[] afters  = handlingList.stream().map(item -> item.getModelRequest().getVideoFrames()[1].getGpuFrame()).toArray(Pointer[]::new);
        
        Pointer input_keson = KesonUtils.createCraneInputKeson(befores, afters);
        PointerByReference output_keson = new PointerByReference();
        
        long now = System.currentTimeMillis();
        holders[0].process(input_keson, output_keson);
        monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        
        KesonUtils.kesonDeepDelete(input_keson);
        return new PointerByReference[] {output_keson};    
    }
    
    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(outputKesons[0], handlingList.size(), "id");
        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);
        }
    }
    
    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || !(modelResult.getKeson() instanceof PointerByReference))
            return ;
        
        Pointer scores = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(KestrelApi.keson_get_object_item(modelResult.getKeson().getValue(), "targets"), 0), "scores");

        modelResult.setDetectResult(KesonUtils.kesonToJson(scores));
    }
    
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
         return true;
    }
    
    @Override
    public void buildOutputValue(ModelResult modelResult) {
         modelResult.setOutputResult(modelResult.getDetectResult());
    }
}
