package com.sensetime.intersense.cognitivesvc.xworker.zutils;

import com.sensetime.intersense.cognitivesvc.xworker.event.out.DeviceStatusSyncOutput;
import com.sensetime.intersense.cognitivesvc.xworker.event.out.SenseyeXRawVideoEventOutput;
import com.sensetime.intersense.cognitivesvc.xworker.event.out.SenseyexRawEventOutput;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration("xworkerSenderKafkaConfiguration")
@ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "yes")
//@EnableBinding({KafkaSenderConfiguration.KafkaSender.class, KafkaSenderConfiguration.KafkaSender2.class, KafkaSenderConfiguration.KafkaSenderDeviceStatus.class})
@PropertySource("classpath:xworker-stream.properties")
public class KafkaSenderConfiguration {

    //	public static interface KafkaSender {
//		String SENSEYEX_RAW_EVENT_OUTPUT = "senseyex_raw_event_output";
//		@Output(SENSEYEX_RAW_EVENT_OUTPUT) MessageChannel senseyex_raw_event_output();
//	}

    //    public static interface KafkaSender2 {
//        String SENSEYEX_RAW_VIDEO_EVENT_OUTPUT = "senseyex_raw_video_event_output";
//
//        @Output(SENSEYEX_RAW_VIDEO_EVENT_OUTPUT)
//        MessageChannel senseyex_raw_video_event_output();
//    }

    //    public static interface KafkaSenderDeviceStatus {
//        String DEVICE_STATUS_EVENT_OUTPUT = "device_status_event_output";
//
//        @Output(DEVICE_STATUS_EVENT_OUTPUT)
//        MessageChannel device_status_event_output();
//    }
    @Bean("device_status_event_output")
    public DeviceStatusSyncOutput deviceStatusSyncOutput(StreamBridge streamBridgeTemplate) {
        return new DeviceStatusSyncOutput(streamBridgeTemplate);
    }

    @Bean("senseyex_raw_event_output")
    public SenseyexRawEventOutput senseyexRawEventOutput(StreamBridge streamBridgeTemplate) {
        return new SenseyexRawEventOutput(streamBridgeTemplate);
    }

    @Bean("senseyex_raw_video_event_output")
    public SenseyeXRawVideoEventOutput senseyeXRawVideoEventOutput(StreamBridge streamBridgeTemplate) {
        return new SenseyeXRawVideoEventOutput(streamBridgeTemplate);
    }


}
