package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
public class CrowdAnalysis extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    @Getter
    protected final Integer interval = 0;//检一跳三

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final boolean queueMap = true;

    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";


    protected ConcurrentHashMap<Long, Map<String, String>> crowdRoiMapCongregate = new ConcurrentHashMap<Long, Map<String, String>>();

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {

        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];


        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);

        Collections.shuffle(handlingList);
        
        String deviceId = (String) handlingList.get(0).getModelRequest().getParameter().get("deviceId");
        VideoStreamInfra deviceInfra = (VideoStreamInfra) handlingList.get(0).getModelRequest().getParameter().get("deviceInfra");

        Long streamSourceId = Utils.keyToContextId(deviceId);

        if (!crowdRoiMapCongregate.containsKey(streamSourceId)) {
            crowdRoiMapCongregate.put(streamSourceId, new ConcurrentHashMap<String, String>());
        }
        Map<String, String> roiCongregate = crowdRoiMapCongregate.get(streamSourceId);


        for (int index = 0; index < pointers.length; index++) {
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            if (index == 0) {
                //   try {
                Integer[][][] proi = handlingList.get(index).getModelRequest().getProcessor().getRoi();

                CognitiveEntity.Processor processor = handlingList.get(index).getModelRequest().getProcessor();
                Polygon[] polygons = processor.fetchPolygons();
                if (ArrayUtils.isEmpty(polygons))
                    polygons = new Polygon[]{new Polygon(new int[]{0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[]{0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};

                Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);

                List<Integer[][]> rois = new ArrayList<>();
                List<Integer> positionTypeList = new ArrayList<>();
                List<TrackThreshold> trackThresholdList = new ArrayList<>();

                for (int indexs = 0; indexs < polygons.length; indexs++) {
                    Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
                    if (extra.isEmpty())
                        continue;

                    Integer[][] singleRoi = proi[indexs];
                    if (singleRoi.length <= 0)
                        continue;
                    rois.add(singleRoi);
                    positionTypeList.add(((Number) extra.getOrDefault("positionType", 0)).intValue());

                    TrackThreshold trackThreshold = TrackThreshold.builder()
                            .cntThreshold((Number) extra.getOrDefault("cntThreshold", 2))
                            .timeThreshold((Number) extra.getOrDefault("timeThreshold", 0.1))
                            .distThreshold((Number) extra.getOrDefault("distThreshold", 1.0f))
                            .runningDurationThreshold((Number) extra.getOrDefault("runningDurationThreshold", 0))
                            .speedThreshold((Number) extra.getOrDefault("speedThreshold", 0f))
                            .crowdNumberThreshold((Number) extra.getOrDefault("crowdNumberThreshold", 0))
                            .build();

                    trackThresholdList.add(trackThreshold);
                }
                String policyRoiString = JSON.toJSONString(toArrayStringRoiList(rois));

                //roi
                if (roiCongregate.get(ROISTRING) == null) {
                    roiCongregate.put(ROISTRING, policyRoiString);
                    updateRoi(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString(), deviceInfra, positionTypeList);
                } else {
                    String oldRoiString = roiCongregate.get(ROISTRING);
                    if (!oldRoiString.equals(policyRoiString)) {
                        roiCongregate.put(ROISTRING, policyRoiString);
                        updateRoi(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString(), deviceInfra, positionTypeList);
                    }
                }
                //threshold
                String policyThresholdString = JSON.toJSONString(toArrayStringThresholdList(trackThresholdList, processor.getCrowdFunction()));
                //log.info("policyThresholdString{},---{}", policyThresholdString, roiCongregate.get(THRESHOLDSTRING));
                if (roiCongregate.get(THRESHOLDSTRING) == null) {
                    log.info("[CrowdAnalysis] thresholdString start{}", policyThresholdString);
                    roiCongregate.put(THRESHOLDSTRING, policyThresholdString);
                    updateThreshold(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString(), trackThresholdList, processor.getCrowdFunction());
                } else {
                    String oldThresholdString = roiCongregate.get(THRESHOLDSTRING);
                    if (!oldThresholdString.equals(policyThresholdString)) {
                        log.info("[CrowdAnalysis] thresholdString{}", policyThresholdString);
                        roiCongregate.put(THRESHOLDSTRING, policyThresholdString);
                        updateThreshold(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString(), trackThresholdList, processor.getCrowdFunction());
                    }
                }
                //labeledPerson
                Map<String, Object> labeledPersonMap = processor.getLabeledPerson();
                if (labeledPersonMap != null) {
                    String policyLabelString = JSON.toJSONString(toArrayStringLabelList(labeledPersonMap));
                    if (roiCongregate.get(LABELSTRING) == null) {
                        log.info("[CrowdAnalysis] policyLabelString start{}", policyLabelString);
                        roiCongregate.put(LABELSTRING, policyLabelString);
                        updateLabelPerson(labeledPersonMap, pointers[index].pointers[0], streamSourceId.toString());
                    } else {

                        String oldLableString = roiCongregate.get(LABELSTRING);
                        if (!oldLableString.equals(policyLabelString)) {
                            log.info("[CrowdAnalysis] policyLabelString{}", policyLabelString);
                            roiCongregate.put(LABELSTRING, policyLabelString);
                            updateLabelPerson(labeledPersonMap, pointers[index].pointers[0], streamSourceId.toString());
                        }
                    }
                }

//                } catch (Exception e) {
//                    log.error("[CrowdAnalysis] congregate handle error {}", e.getMessage());
//                }
            }
            long now = System.currentTimeMillis();
            /** 执行模型 获取数据*/
            pointers[index].process(inTo.getValue(), outOf);
            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        }

        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }

    private void updateLabelPerson(Map<String, Object> labeledPersonMap, Pointer pipelinePoint, String streamSourceId) {


        float markHeight = labeledPersonMap.containsKey("markHeight") ? ((Number) labeledPersonMap.get("markHeight")).floatValue() : 1.7f;
        JSONArray jsonArray = (JSONArray) labeledPersonMap.get("manualList");

        List<Integer[][]> inputArray = jsonArray.toJavaList(Integer[][].class);

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < inputArray.size(); i++) {
            int minY = Integer.MAX_VALUE;
            int maxY = Integer.MIN_VALUE;
            for (int j = 0; j < inputArray.get(i).length; j++) {
                minY = Math.min(minY, inputArray.get(i)[j][1]);
                maxY = Math.max(maxY, inputArray.get(i)[j][1]);
            }
            result.append("(").append(minY).append(",").append(maxY).append(")");
            if (i != inputArray.size() - 1) {
                result.append(",");
            }
        }
        if (result.length() <= 0) {
            return;
        }

        String jsonString = "\"" + result.toString() + "\"";

        String controlPipeStrng = " {\n" +
                "   \"streams\": [\n" +
                "    {\n" +
                "        \"name\": \"congregate_pipeline\",\n" +
                "         \"modules\": [\n" +
                "        {\n" +
                "                    \"name\": \"pmap\",\n" +
                "                     \"source_id\": 49650,\n" +
                "                    \"type\": \"Pmap\",\n" +
                "                    \"inputs\": [\n" +
                "                        \"images\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"pers_info\"\n" +
                "                    ],\n" +
                "                    \"config\": {\n" +

                "                        \"type\": \"Manual\",\n" +
                "                        \"person_height\": " + markHeight + ",\n" +
                "                        \"labeled_person\": " + jsonString +
                "                    }\n" +
                "         }" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "}";

        controlPipeStrng = controlPipeStrng.replace("49650", streamSourceId);

        log.info("[CrowdAnalysis] jsonStrLabelPerson{}", controlPipeStrng);

        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);

        PointerByReference outRemove = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), outRemove);


        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

        KesonUtils.kesonDeepDelete(out);
        KesonUtils.kesonDeepDelete(outRemove);
        KesonUtils.kesonDeepDelete(input);


    }

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        //log.info("outputKesonsRRRR{}", KesonUtils.kesonToJson(outputKesons[0]));
        //log.info("handlingListSize{}", handlingList.size());
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds, "source_id");

        //log.info("sub0_kesons{}", sub0_kesons.length);

        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();

            if (sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));


            handlingList.get(index).setKeson(new PointerByReference(array));
        }

        KesonUtils.kesonDeepDelete(outputKesons);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        //log.info("DetectResult{}",  modelResult.getDetectResult());
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();

        if (CollectionUtils.isEmpty(detectResult))
            return false;

        return !CollectionUtils.isEmpty(detectResult);
    }

    @Override
    public void buildOutputValue(ModelResult modelResult) {

        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult)) {
            return;
        }
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        boolean scatterSingle = getCrowdFuncScatter(processor.getCrowdFunction());

        boolean congrateNot = getCrowdFuncNotCongrate(processor.getCrowdFunction());

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 0), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);


        List<String> roiIds = (List<String>) ((List<Map<String, Object>>) Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getExtras(), List.of()))
                .stream()
                .filter(item -> "roiIds".equals(item.get("type")))
                .findAny()
                .orElse(Map.of())
                .getOrDefault("roiIds", List.of());


        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");

        if (CollectionUtils.isEmpty(targets)) {
            return;
        }

        List<Map<String, Object>> roisArray = new ArrayList<>();

        for (Map<String, Object> target : targets) {
            Map<String, Object> transformedTarget = new HashMap<>();

            List<Map<String, Object>> rois = new ArrayList<>();

            List<Map<String, Object>> originalRois = (List<Map<String, Object>>) target.get("rois");
            for (Map<String, Object> originalRoi : originalRois) {
                Map<String, Object> transformedRoi = new HashMap<>();

                if (originalRoi.containsKey("roi_id")) {
                    int roiIdInt = ((Number) originalRoi.get("roi_id")).intValue();
                    transformedRoi.put("roiId", (!roiIds.isEmpty() && roiIds.contains(roiIds.get(roiIdInt))) ? roiIds.get(roiIdInt) : "");
                }
                transformedRoi.put("roi_id", originalRoi.get("roi_id"));
                transformedRoi.put("crowd_function", originalRoi.get("crowd_function"));
                transformedRoi.put("point_cal_type", originalRoi.get("point_cal_type"));


                transformedRoi.put("crowd_speed", originalRoi.get("crowd_speed"));
                transformedRoi.put("crowd_running", originalRoi.get("crowd_running"));
                transformedRoi.put("crowd_number", originalRoi.get("crowd_number"));
                transformedRoi.put("crowd_running_duration", originalRoi.get("crowd_running_duration"));

                if (originalRoi.containsKey("crowd_running") && !((Boolean) originalRoi.get("crowd_running"))) {
                    continue;
                }
                List<Map<String, Object>> congregateFlocks = new ArrayList<>();
                if (originalRoi.containsKey("congregate_flocks")) {
                    List<Map<String, Object>> transformedCongregateFlocks = new ArrayList<>();
                    congregateFlocks = (List<Map<String, Object>>) originalRoi.get("congregate_flocks");
                    //log.info("congregateFlocks_empty{}", congregateFlocks);
                    if (!congregateFlocks.isEmpty()) {
                        for (Map<String, Object> congregateFlock : congregateFlocks) {
                            //log.info("congregateFlocks_empty index{}", congregateFlocks);
                            Map<String, Object> transformedCongregateFlock = new HashMap<>();
                            transformedCongregateFlock.put("congregate_type", congregateFlock.get("congregate_type"));

                            List<Map<String, Integer>> congregateBox = new ArrayList<>();
                            List<Map<String, Object>> originalBox = (List<Map<String, Object>>) congregateFlock.get("congregate_box");
                            transFormedBox(congregateBox, originalBox);
                            transformedCongregateFlock.put("congregate_box", congregateBox);

                            transformedCongregateFlock.put("congregate_duration", congregateFlock.get("congregate_duration"));
                            transformedCongregateFlock.put("congregate_number", congregateFlock.get("congregate_number"));
                            transformedCongregateFlocks.add(transformedCongregateFlock);
                            //log.info("congregateFlocks_empty end{}", transformedCongregateFlocks);
                        }
                    }
                    transformedRoi.put("congregate_flocks", transformedCongregateFlocks);
                }

                List<Map<String, Object>> scatterFlocks = new ArrayList<>();
                if (originalRoi.containsKey("scatter_flocks")) {
                    List<Map<String, Object>> transformedScatterFlocks = new ArrayList<>();
                    scatterFlocks = (List<Map<String, Object>>) originalRoi.get("scatter_flocks");
                    if (!scatterFlocks.isEmpty()) {
                        for (Map<String, Object> scatterFlock : scatterFlocks) {
                            //log.info("scatter_flocks_not_empty");
                            Map<String, Object> transformedScatterFlock = new HashMap<>();
                            transformedScatterFlock.put("scatter_type", scatterFlock.get("scatter_type"));

                            List<Map<String, Integer>> scatterBox = new ArrayList<>();
                            List<Map<String, Object>> originalBox = (List<Map<String, Object>>) scatterFlock.get("scatter_box");
                            transFormedBox(scatterBox, originalBox);
                            transformedScatterFlock.put("scatter_box", scatterBox);

                            transformedScatterFlock.put("scatter_number", scatterFlock.get("scatter_number"));
                            transformedScatterFlocks.add(transformedScatterFlock);
                        }
                        transformedRoi.put("scatter_flocks", transformedScatterFlocks);
                    }
                }
                List<Map<String, Object>> boxes = new ArrayList<>();
                if (originalRoi.containsKey("boxes")) {
                    boxes = (List<Map<String, Object>>) originalRoi.get("boxes");
                    if (!boxes.isEmpty()) {
                        List<Map<String, Object>> transformedBoxEsFlocks = new ArrayList<>();
                        for (Map<String, Object> box : boxes) {
                            Map<String, Object> transformedBoxEsFlock = new HashMap<>();
                            transformedBoxEsFlock.put("roi", box.get("roi"));
                            transformedBoxEsFlock.put("confidence", box.get("confidence"));
                            transformedBoxEsFlocks.add(transformedBoxEsFlock);
                        }
                        transformedRoi.put("boxes", transformedBoxEsFlocks);
                    }
                }
                if (originalRoi.containsKey("crowd_function")
                        && ((Number) originalRoi.get("crowd_function")).intValue() == CROWD_CONGREGATE
                        && scatterFlocks.isEmpty() && congregateFlocks.isEmpty()) {
                    continue;
                }
                if (scatterSingle && !scatterFlocks.isEmpty()) {
                    transformedRoi.put("crowd_function", CROWD_CONGREGATE_SCATTER);
                    transformedRoi.remove("congregate_flocks");
                }
                //没有聚集勾选，则删除聚集数据
                if (congrateNot && !congregateFlocks.isEmpty()) {
                    transformedRoi.remove("congregate_flocks");
                }

                List<Map<String, Integer>> vertexes = new ArrayList<>();
                List<Map<String, Object>> originalVertexes = (List<Map<String, Object>>) originalRoi.get("vertexes");
                transFormedBox(vertexes, originalVertexes);


                transformedRoi.put("vertexes", vertexes);
                rois.add(transformedRoi);
            }
            if (rois.isEmpty()) {
                continue;
            }

            transformedTarget.put("rois", rois);


            Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                    .limit(kesonTarget_size)
                    .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "source_id")) == ((Number) target.get("source_id")).longValue())
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "image_id")) == ((Number) target.get("image_id")).longValue())
                    .findAny()
                    .orElse(null);

            if (kesonTarget != null) {
                log.info("start save image for congregate");
//                PointerByReference targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
//                if (targetImage.getValue() != null) {
//                    String targetImagePath = FrameUtils.save_image_as_jpg(targetImage.getValue(), ImageUtils.newFile("shieldFace"));
//                    output.put("targetImage", targetImagePath);
//                }

                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                if (sceneImage.getValue() != null) {
                    String sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                    transformedTarget.put("sceneImage", sceneImagePath);
                }
            }

            transformedTarget.put("source_id", target.get("source_id"));

            roisArray.add(transformedTarget);
        }


        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});

        if (roisArray.isEmpty())
            modelResult.setOutputResult(null);
        else
            modelResult.setOutputResult(roisArray);

    }

    private void updateThreshold(String[] json1, Pointer pipelinePoint, String sourceId, List<TrackThreshold> trackThresholdList, Integer[] crowdFunc) {

        log.info("CrowdType{}, {}", crowdFunc, getCrowdFuncType(CROWD_CONGREGATE_SCATTER_NAME, crowdFunc));
        JSONArray jsonThreshold = new JSONArray();
        for (int i = 0; i < json1.length; i++) {
            String[] vertexArr = json1[i].replaceAll("\\[|\\]|\\(|\\)", "").split(",");
            StringBuilder vertices = new StringBuilder();
            for (int j = 0; j < vertexArr.length; j += 2) {
                vertices.append("(").append(vertexArr[j].trim()).append(",").append(vertexArr[j + 1].trim()).append(")");
                if (j != vertexArr.length - 2) {
                    vertices.append(",");
                }
            }
            JSONObject roi = new JSONObject();
            roi.put("roi_id", i);
            roi.put("time_converting_ratio", 1000.0);
            roi.put("cnt_threshold", (trackThresholdList.size() >= i + 1) ? trackThresholdList.get(i).cntThreshold : 1);
            roi.put("time_threshold", (trackThresholdList.size() >= i + 1) ? trackThresholdList.get(i).timeThreshold : 1);
            roi.put("dist_threshold", (trackThresholdList.size() >= i + 1) ? trackThresholdList.get(i).distThreshold : 1.0);
            roi.put("turn_scatter_on", getCrowdFuncType(CROWD_CONGREGATE_SCATTER_NAME, crowdFunc));

            jsonThreshold.add(roi);
        }

        JSONArray jsonThresholdSpeed = new JSONArray();
        for (int i = 0; i < json1.length; i++) {
            String[] vertexArr = json1[i].replaceAll("\\[|\\]|\\(|\\)", "").split(",");
            StringBuilder vertices = new StringBuilder();
            for (int j = 0; j < vertexArr.length; j += 2) {
                vertices.append("(").append(vertexArr[j].trim()).append(",").append(vertexArr[j + 1].trim()).append(")");
                if (j != vertexArr.length - 2) {
                    vertices.append(",");
                }
            }
            JSONObject roi = new JSONObject();
            roi.put("roi_id", i);
            roi.put("time_converting_ratio", 1000.0);
            roi.put("crowd_number_threshold", (trackThresholdList.size() >= i + 1) ? trackThresholdList.get(i).crowdNumberThreshold : 1);
            roi.put("avg_crowd_speed_threshold", (trackThresholdList.size() >= i + 1) ? trackThresholdList.get(i).speedThreshold : 0f);
            roi.put("crowd_running_duration_threshold", (trackThresholdList.size() >= i + 1) ? trackThresholdList.get(i).runningDurationThreshold.floatValue() / 1000 : 0);

            jsonThresholdSpeed.add(roi);
        }
        if (jsonThresholdSpeed.isEmpty()) {
            return;
        }

        if (jsonThreshold.isEmpty()) {
            return;
        }
        String controlPipeStrng = " {\n" +
                "   \"streams\": [\n" +
                "    {\n" +
                "        \"name\": \"congregate_pipeline\",\n" +
                "         \"modules\": [\n" +

                "        {\n" +
                "                    \"name\": \"abg_speed\",\n" +
                "                     \"source_id\": 49650,\n" +
                "                    \"type\": \"AbgSpeed\",\n" +
                "                    \"inputs\": [\n" +
                "                        \"speed_tracked_targets\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"speed_targets\"\n" +
                "                    ],\n" +
                "                    \"config\":{\n" +

                "                         \"turn_on\":" + getCrowdFuncType(CROWD_SPEED_NAME, crowdFunc) + ",\n" +
                "                         \"rois\": " + jsonThresholdSpeed +
                "                    }\n" +
                "         }," +

                "        {\n" +
                "                    \"name\": \"congregate_scatter\",\n" +
                "                     \"source_id\": 49650,\n" +
                "                    \"type\": \"AbgCongregateScatter\",\n" +
                "                    \"inputs\": [\n" +
                "                        \"congregate_tracked_targets\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"congregate_scatter_targets\"\n" +
                "                    ],\n" +
                "                    \"config\":{\n" +

                "                         \"turn_on\":" + getCrowdFuncType(CROWD_CONGREGATE_NAME, crowdFunc) + ",\n" +
                "                         \"rois\": " + jsonThreshold +
                "                    }\n" +
                "         }" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "}";

        controlPipeStrng = controlPipeStrng.replace("49650", sourceId);

        log.info("jsonStrThreshold{}", controlPipeStrng);

        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);

        PointerByReference outRemove = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), outRemove);


        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

        KesonUtils.kesonDeepDelete(out);
        KesonUtils.kesonDeepDelete(outRemove);
        KesonUtils.kesonDeepDelete(input);

    }

    private boolean getCrowdFuncType(String crowdTypeName, Integer[] crowdFuncs) {

        if (crowdFuncs == null) {
            return true;
        }

        List<Integer> crowdFunc = Arrays.asList(crowdFuncs);
        if (crowdTypeName.equals(CROWD_SPEED_NAME) && crowdFunc.contains(CROWD_SPEED)) {
            return true;
        }

        if (crowdTypeName.equals(CROWD_CONGREGATE_NAME) && (crowdFunc.contains(CROWD_CONGREGATE) || crowdFunc.contains(CROWD_CONGREGATE_SCATTER))) {
            return true;
        }
        if (crowdTypeName.equals(CROWD_CONGREGATE_SCATTER_NAME) && crowdFunc.contains(CROWD_CONGREGATE_SCATTER)) {
            return true;
        }

        return false;
    }

    private boolean getCrowdFuncScatter(Integer[] crowdFuncs) {

        if (crowdFuncs == null) {
            return true;
        }
        List<Integer> crowdFunc = Arrays.asList(crowdFuncs);
        return crowdFunc.size() == 1 && crowdFunc.get(0).equals(CROWD_CONGREGATE_SCATTER);
    }

    private boolean getCrowdFuncNotCongrate(Integer[] crowdFuncs) {

        if (crowdFuncs == null) {
            return true;
        }
        List<Integer> crowdFunc = Arrays.asList(crowdFuncs);

        return !crowdFunc.contains(CROWD_CONGREGATE);

    }


    private void updateRoi(String[] json1, Pointer pipelinePoint, String sourceId, VideoStreamInfra deviceInfra, List<Integer> positionTypeList) {

        JSONArray json2 = new JSONArray();
        for (int i = 0; i < json1.length; i++) {
            String[] vertexArr = json1[i].replaceAll("\\[|\\]|\\(|\\)", "").split(",");
            StringBuilder vertices = new StringBuilder();
            for (int j = 0; j < vertexArr.length; j += 2) {
                vertices.append("(").append(vertexArr[j].trim()).append(",").append(vertexArr[j + 1].trim()).append(")");
                if (j != vertexArr.length - 2) {
                    vertices.append(",");
                }
            }
            JSONObject roi = new JSONObject();
            roi.put("roi_id", i);
            roi.put("point_cal_type", (positionTypeList.size() >= i + 1) ? retypeByPosition(positionTypeList.get(i)) : 1);
            roi.put("vertexes", vertices.toString());
            json2.add(roi);
        }

        //默认全屏
        if (json2.isEmpty()) {
            String vertices = "(0,0),(0," + ((deviceInfra.getRtspHeight() > 0) ? deviceInfra.getRtspHeight() : 1080) + "),(" + ((deviceInfra.getRtspWidth() > 0) ? deviceInfra.getRtspWidth() : 1920) + "," + ((deviceInfra.getRtspHeight() > 0) ? deviceInfra.getRtspHeight() : 1080) + "),(" + ((deviceInfra.getRtspWidth() > 0) ? deviceInfra.getRtspWidth() : 1920) + ",0)";
            JSONObject roi = new JSONObject();
            roi.put("roi_id", 0);
            roi.put("point_cal_type", 1);
            roi.put("vertexes", vertices);
            json2.add(roi);
        }

        String controlPipeStrng = " {\n" +
                "   \"streams\": [\n" +
                "    {\n" +
                "        \"name\": \"congregate_pipeline\",\n" +
                "         \"modules\": [\n" +
                "          {\n" +
                "                    \"name\": \"speed_filter\",\n" +
                "                    \"source_id\": 49650,\n" +
                "                    \"type\": \"CrowdRoiFilter\",\n" +
                "                    \"inputs\": [\n" +
                "                        \"loc_targets\",\n" +
                "                        \"pers_info\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"filtered_speed_targets\"\n" +
                "                    ],\n" +
                "                    \"config\": {\n" +

                "                        \"type\": \"FilterHeadPoint\",\n" +
                "                        \"speed\": {\n" +
                "                            \"detect_result_type\":1,\n" +
                "                             \"rois\": " + json2 +
                "                            }\n" +
                "                          }\n" +
                "          }," +
                "        {\n" +
                "            \"name\": \"congregate_filter\",\n" +
                "             \"source_id\": 49650,\n" +
                "             \"type\": \"CrowdRoiFilter\",\n" +
                "             \"inputs\": [\n" +
                "                     \"loc_targets\",\n" +
                "                     \"pers_info\"\n" +
                "               ],\n" +
                "            \"outputs\": [\n" +
                "                 \"filtered_congregate_targets\"\n" +
                "             ],\n" +
                "            \"config\": {\n" +

                "                    \"type\": \"FilterHeadPoint\",\n" +
                "                    \"congregate_scatter\": {\n" +
                "                       \"detect_result_type\": 1,\n" +
                "                        \"rois\": " + json2 +
                "                      }\n" +
                "                  }\n" +
                "            }" +

                "           ]\n" +
                "          }\n" +
                "      ]\n" +
                "  }";

        controlPipeStrng = controlPipeStrng.replace("49650", sourceId);

        log.info("[crowdAnalysis] jsonStrCongregateRois{}", controlPipeStrng);

        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);

        PointerByReference outRemove = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), outRemove);


        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

        KesonUtils.kesonDeepDelete(out);
        KesonUtils.kesonDeepDelete(outRemove);
        KesonUtils.kesonDeepDelete(input);

    }


    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("crowdAnalysis start context [" + infra.getDeviceId() + "]. contextID " + contextId);

        crowdRoiMapCongregate.put(contextId, new ConcurrentHashMap<String, String>());
    }

    private void stopContext(String deviceId, long contextId) {
        log.info("crowdAnalysis stop deviceId [" + deviceId + "].");
        if (!crowdRoiMapCongregate.containsKey(contextId))
            return;

        holders[0].controlForRemoveSource(contextId);
        crowdRoiMapCongregate.remove(contextId);
        log.info("crowdAnalysis stop contextId [" + contextId + "].");

    }

    //point_cal_type：每个结果框会需要计算是否在感兴趣区域，
    //会取一个点来判断：0-> upper_middle ,1-> center ,2-> lower_middle
    private int retypeByPosition(Integer position) {
        if (position == 0) {
            return 1;
        } else if (position == 1) {
            return 0;
        } else if (position == 2) {
            return 2;
        }
        return 1;
    }

    private void transFormedBox(List<Map<String, Integer>> vertexes, List<Map<String, Object>> originalVertexes) {
        for (Map<String, Object> vertex : originalVertexes) {
            Map<String, Integer> transformedVertex = new HashMap<>();
            Map<String, Object> binaryData = (Map<String, Object>) vertex.get("$binary");
            if (binaryData != null) {
                Map<String, Integer> readableData = (Map<String, Integer>) binaryData.get("$readable");
                if (readableData != null) {
                    transformedVertex.put("x", readableData.get("x"));
                    transformedVertex.put("y", readableData.get("y"));
                    vertexes.add(transformedVertex);
                }
            }
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if (e != null)
            for (Map<String, Object> param : (List<Map<String, Object>>) e) {
                if (!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer) param.get("roiIndex");
                if (roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };

    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> functionLabelPerson = e -> {
        Map<String, Object> result = new HashMap<String, Object>();

        if (e != null) {
            return (Map<String, Object>) e;
        }

        return result;

    };

    public static String[] toArrayStringCrowdFunc(Integer[] arr) {
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr);
        }
        return result;
    }


    private static String[] toArrayStringRoiList(List<Integer[][]> arr) {
        int numRows = arr.size();
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr.get(i));
        }
        return result;
    }

    private static StringBuilder toArrayStringThresholdList(List<TrackThreshold> arr, Integer[] crowdFunc) {
        String[] result = new String[arr.size()];
        for (int i = 0; i < arr.size(); i++) {
            TrackThreshold track = arr.get(i);
            result[i] = track.cntThreshold.toString()
                    + track.timeThreshold.toString()
                    + track.runningDurationThreshold.toString()
                    + track.speedThreshold.toString()
                    + track.crowdNumberThreshold.toString();
        }

        StringBuilder sb = new StringBuilder();
        for (String str : result) {
            sb.append(str);
        }
        if (crowdFunc != null && crowdFunc.length > 0) {
            for (String str : toArrayStringCrowdFunc(crowdFunc)) {
                sb.append(str);
            }
        }

        return sb;
    }


    private static String toArrayStringLabelList(Map<String, Object> labelPerson) {

        StringBuilder markHeight = new StringBuilder();
        StringBuilder manualList = new StringBuilder();

        if (labelPerson.containsKey("markHeight")) {
            markHeight.append(labelPerson.get("markHeight").toString());
        }

        if (labelPerson.containsKey("manualList")) {

            JSONArray jsonArray = (JSONArray) labelPerson.get("manualList");

            List<Integer[][]> list = jsonArray.toJavaList(Integer[][].class);
            for (Integer[][] arr : list) {
                for (Integer[] innerArray : arr) {
                    manualList.append(Arrays.deepToString(innerArray));
                }
            }
        }
        return manualList.toString() + markHeight.toString();
    }

    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class TrackThreshold {

        private final Number cntThreshold;
        private final Number timeThreshold;
        private final Number distThreshold;

        private final Number runningDurationThreshold;
        private final Number speedThreshold;
        private final Number crowdNumberThreshold;

    }

    private static final String ROISTRING = "crowdAnalysis_roi";
    private static final String THRESHOLDSTRING = "crowdAnalysis_threshold";
    private static final String LABELSTRING = "crowdAnalysis_labelPerson";

    private static int CROWD_SPEED = 5;
    private static int CROWD_CONGREGATE = 3;
    private static int CROWD_CONGREGATE_SCATTER = 9;


    private static String CROWD_SPEED_NAME = "speed";
    private static String CROWD_CONGREGATE_NAME = "congregate";
    private static String CROWD_CONGREGATE_SCATTER_NAME = "scatter";


}
