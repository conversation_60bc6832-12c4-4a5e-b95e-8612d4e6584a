package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.awt.Polygon;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.Map.Entry;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;

import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XModelHandler.ModelResult;
import com.sun.jna.Memory;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.Builder.Default;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SuppressWarnings("unused")
public class PedIntrudeStay extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    
    //<contextid, bodyid, bodytrack>
    protected final ConcurrentHashMap<Long, Map<Integer, BodyTrack>> trackingMap = new ConcurrentHashMap<Long, Map<Integer, BodyTrack>>();

    @Getter
    protected final Integer interval = 3;
    
    @Getter
    protected final Boolean blocking = false;
    
    @Getter
    protected final Boolean needContext = true;

    @Getter
    protected final String decoderFormat = "rgb24";
    
    @Getter
    protected final Integer frameBuffer = 24;
    
    @Getter
    protected final String frameBufferStrategy = "smart";
    
    @Getter
    protected final boolean lazyInit = true;
    
    @Getter
    protected final boolean queueMap = true;
    
    /** 将批量处理结果 整理后放到每个项目 */
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds);
        PointerByReference[] sub1_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[1])[0], imageIds);
        
        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();
            
            if(sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));
            
            if(sub1_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub1_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));
            
            handlingList.get(index).setKeson(new PointerByReference(array));
        }

        KesonUtils.kesonDeepDelete(outputKesons);
    }
    
    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || modelResult.getKeson().getValue() == null)
            return ;
        
        modelResult.setDetectResult(KesonUtils.kesonToJson(modelResult.getKeson()));
    }
    
    @SuppressWarnings({ "unchecked"})
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>)modelResult.getDetectResult();
        if(CollectionUtils.isEmpty(detectResult))
            return false;

        long sourceId = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        Map<Integer, BodyTrack> trackMap = trackingMap.get(sourceId);
        if(trackMap == null)
            return false;
        
        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.get(0).get("targets");
        List<Map<String, Object>> tracklets = (List<Map<String, Object>>)detectResult.get(1).get("targets");

        Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        Map<Integer, Map<Integer, Map<String, Object>>> labelExtrasMap = (Map<Integer, Map<Integer, Map<String, Object>>>)processor.fetchExtras(function);

        List<Map<String, Object>> faces = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> bodys = new ArrayList<Map<String, Object>>();
        List<Number> drops = new ArrayList<Number>();
        for(Map<String, Object> target : targets) {
            int label = ((Number)target.getOrDefault("label", -1)).intValue();
            if(label == 37017)
                faces.add(target);
            else if(label == 221488) 
                bodys.add(target);
            else 
                drops.add((Number)target.get("dropped_track_id"));
        }
        
        Iterator<Map<String, Object>> bodyIts = bodys.iterator();
        while(bodyIts.hasNext()) {
            Map<String, Object> body = bodyIts.next();
            
            int trackId = ((Number)body.get("track_id")).intValue();
            int label = ((Number)body.get("label")).intValue();
            float quality = (((Number)body.get("confidence")).floatValue());
            long capturedTime = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();
            
            BodyTrack track = trackMap.get(trackId);
            if(track == null) {
                track = BodyTrack.builder().trackId(trackId).firstInStamp(new Long[polygons.length]).keepingInStamp(new Long[polygons.length]).build();
                trackMap.put(trackId, track);
            }
            
            track.setCurrentPosition((Map<String, Number>)body.get("roi"));
            
            boolean toRemove = true;
            
            for(int index = 0 ;index < polygons.length; index ++) {
                Map<String, Object> extra = labelExtrasMap.getOrDefault(index, Map.of()).get(label);
                if(extra == null)
                    continue;

                Polygon polygon = polygons[index];
                Number threshold = (Number)extra.getOrDefault("threshold", processor.getThreshold());
                body.put("threshold", threshold);
                
                Integer minSize = (Integer)extra.getOrDefault("minSize", processor.getMinSize());
                Integer maxSize = (Integer)extra.getOrDefault("maxSize", processor.getMaxSize());
                
                boolean baseCheck = true;
                baseCheck &= threshold == null || quality > threshold.floatValue();
                baseCheck &= minSize == null || (track.getCurrentPosition().getWidth() >= minSize.intValue() && track.getCurrentPosition().getHeight() >= minSize.intValue());
                baseCheck &= maxSize == null || (track.getCurrentPosition().getWidth() <= maxSize.intValue() && track.getCurrentPosition().getHeight() <= maxSize.intValue());

                if(!baseCheck)
                    continue;
                
                Number positionType = (Number)extra.getOrDefault("positionType", 0);
                
                //利用人体中心点作为目标点
                int currentX  = track.getCurrentPosition().getLeft()  + track.getCurrentPosition().getWidth()   / 2;
                int currentY  = track.getCurrentPosition().getTop()   + track.getCurrentPosition().getHeight()  / 2;
                if(positionType.intValue() == 1) //利用头顶顶点作为目标点
                    currentY  = track.getCurrentPosition().getTop();
                else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                    currentY  = track.getCurrentPosition().getTop()   + track.getCurrentPosition().getHeight();

                List<Number> crossDot = (List<Number>)extra.getOrDefault("crossDot", List.of());
                if(!crossDot.isEmpty() && track.getPreviousPosition() != null) {
                    int previousX = track.getPreviousPosition().getLeft() + track.getPreviousPosition().getWidth()  / 2;
                    int previousY = track.getPreviousPosition().getTop()  + track.getPreviousPosition().getHeight() / 2;
                    
                    if(positionType.intValue() == 1)//利用头顶顶点作为目标点
                        previousY = track.getPreviousPosition().getTop();
                    else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                        previousY = track.getPreviousPosition().getTop()  + track.getPreviousPosition().getHeight();

                    int cx, cy;
                    if(crossDot.size() != 2) {
                        cx = Arrays.stream(polygon.xpoints).sum() / polygon.npoints;
                        cy = Arrays.stream(polygon.ypoints).sum() / polygon.npoints;
                    }else {
                        cx = crossDot.get(0).intValue();
                        cy = crossDot.get(1).intValue();
                    }
                    
                    String crossDirection = (String)extra.get("crossDirection");
                    for(int dotIndex = 0; dotIndex < polygon.npoints - 1 || (polygon.npoints > 2 && dotIndex == polygon.npoints - 1); dotIndex ++) {
                        int ax = polygon.xpoints[dotIndex];
                        int ay = polygon.ypoints[dotIndex];
                        
                        int bx = (dotIndex == polygon.npoints - 1) ? polygon.xpoints[0] : polygon.xpoints[dotIndex + 1];
                        int by = (dotIndex == polygon.npoints - 1) ? polygon.ypoints[0] : polygon.ypoints[dotIndex + 1];
                        
                        if(!intersect(ax, ay, bx, by, previousX, previousY, currentX, currentY))
                            continue;

                        boolean previousIntersected = intersect(ax, ay, bx, by, previousX, previousY, cx, cy);
                        boolean targetIntersected   = intersect(ax, ay, bx, by, currentX , currentY , cx, cy);
                        
                        String direction = "unknown";
                        if(previousIntersected && !targetIntersected)
                            direction = "forward";
                        else if(!previousIntersected && targetIntersected)
                            direction = "backward";
                        
                        if(StringUtils.isNotBlank(crossDirection) && !crossDirection.equals(direction))
                            continue;
                        
                        List<Map<String, Object>> crossAlert = (List<Map<String, Object>>)body.get("crossAlert");
                        if(crossAlert == null) {
                            crossAlert = new ArrayList<Map<String, Object>>();
                            body.put("crossAlert", crossAlert);
                        }
                        
                        Map<String, Object> cross = new HashMap<String, Object>();
                        cross.put("roiIndex", index);
                        cross.put("direction", direction);
                        crossAlert.add(cross);
                        
                        toRemove = false;
                    }
                }
                
                if(polygon.contains(currentX, currentY)) {
                    if(track.getFirstInStamp()[index] == null) {
                        track.getFirstInStamp()[index] = capturedTime;
                        
                        if(Boolean.TRUE.equals(extra.get("entryAlert"))) {
                            List<Integer> enterRoiIndexes = (List<Integer>)body.get("entryAlert");
                            if(enterRoiIndexes == null) {
                                enterRoiIndexes = new ArrayList<Integer>();
                                body.put("entryAlert", enterRoiIndexes);
                            }
                            
                            enterRoiIndexes.add(index);
                            toRemove = false;
                        }
                    }
                    
                    if(extra.containsKey("trigger")) {
                        Map<String, Object> trigger = (Map<String, Object>)extra.get("trigger");
                        
                        int triggerTime = (int)trigger.getOrDefault("triggerTime", 1000);
                        int maxTrackTime = (int)trigger.getOrDefault("maxTrackTime", 0);
                        int trackFreq = (int)trigger.getOrDefault("trackFreq", 1000);
                        
                        boolean bingo = false;
                        
                        if(track.getKeepingInStamp()[index] == null) {                           
                            if(capturedTime - track.getFirstInStamp()[index] >= triggerTime)
                                bingo = true;
                        }else if(maxTrackTime < 0 || (capturedTime - track.getFirstInStamp()[index]) <= maxTrackTime) {
                            if(capturedTime - track.getKeepingInStamp()[index] > trackFreq)
                                bingo = true;
                        }
                        
                        if(bingo) {
                            track.getKeepingInStamp()[index] = capturedTime;
                            
                            List<Integer> stayRoiIndexes = (List<Integer>)body.get("triggerAlert");
                            if(stayRoiIndexes == null) {
                                stayRoiIndexes = new ArrayList<Integer>();
                                body.put("triggerAlert", stayRoiIndexes);
                            }
                            
                            stayRoiIndexes.add(index);
                            toRemove = false;
                        }
                    }
                }else {
                    if(track.getFirstInStamp()[index] != null) {
                        if(Boolean.TRUE.equals(extra.get("exitAlert"))) {
                            List<Integer> exitRoiIndexes = (List<Integer>)body.get("exitAlert");
                            if(exitRoiIndexes == null) {
                                exitRoiIndexes = new ArrayList<Integer>();
                                body.put("exitAlert", exitRoiIndexes);
                            }
                            exitRoiIndexes.add(index);
                            body.put("exitAlert", exitRoiIndexes);
                            
                            toRemove = false;
                        }    
                        
                        track.getFirstInStamp()[index] = null;
                        track.getKeepingInStamp()[index] = null;
                    }
                }
            }
            
            if(toRemove)
                bodyIts.remove();
            else 
                body.put("roiIds", labelExtrasMap.getOrDefault(Integer.MAX_VALUE, Map.of()).getOrDefault(Integer.MAX_VALUE, Map.of()).get("roiIds"));
        }
        
        Iterator<Map<String, Object>> faceIts = faces.iterator();
        while(faceIts.hasNext()) {
            Map<String, Object> face = faceIts.next();
            
            List<Map<String, Object>> associations = (List<Map<String, Object>>)face.get("associations");
            if(CollectionUtils.isNotEmpty(associations)) {
                int faceTrackId = ((Number)face.get("track_id")).intValue();
                int bodyTrackId = ((Number)associations.get(0).get("track_id")).intValue();
                
                BodyTrack track = trackMap.get(bodyTrackId);
                if(track != null)
                    track.setFaceTrackId(faceTrackId);
            }
        }
        
        if(CollectionUtils.isNotEmpty(drops))
            modelResult.getModelRequest().getParameter().put("droppedIds", drops);
        
        for(Map<String, Object> tracklet : tracklets) {
            int label = ((Number)tracklet.getOrDefault("label", -1)).intValue();
            if(label == 37017){
                int trackId = ((Number)tracklet.get("track_id")).intValue();
                BodyTrack bodyTrack = trackMap.values().stream().filter(track -> track.getFaceTrackId() != null && track.getFaceTrackId() == trackId).findAny().orElse(null);
                
                if(bodyTrack != null) {
                    Pointer sceneImage = findSceneImage(modelResult.getKeson().getValue(), sourceId, trackId);
                    Map<String, Number> roi = (Map<String, Number>)tracklet.get("target_image_roi");
                    bodyTrack.setFaceFrame(FrameUtils.roi_frame(sceneImage, roi, 0.3f));
                    bodyTrack.setFaceAttributes((Map<String, Map<String, Number>>) tracklet.get("attribute")); 
                }
            }else if(label == 221488) {
                int trackId = ((Number)tracklet.get("track_id")).intValue();
                BodyTrack bodyTrack = trackMap.get(trackId);
                if(bodyTrack != null)
                    bodyTrack.setPedAttributes(dealWithAge((Map<String, Map<String, Number>>) tracklet.get("attribute")));
            }
        }
        
        detectResult.get(0).put("targets", bodys);
        return !bodys.isEmpty();
    }
    
    @SuppressWarnings({ "unchecked"})
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();
        Map<Integer, BodyTrack> trackMap = trackingMap.get(Utils.keyToContextId(deviceId));
        
        List<Map<String, Object>> targets = (List<Map<String, Object>>)((List<Map<String, Object>>)modelResult.getDetectResult()).get(0).get("targets");
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        Function<Map<String, Object>, Map<String, Object>> mapper = target -> {
            Map<String, Object> result = new HashMap<String, Object>();
            
            Number threshold = (Number)target.get("threshold");
            
            result.put("detect", target.get("roi"));
            result.put("targetType", target.get("label"));
            result.put("capturedTime", target.get("capturedTime"));
            result.put("confidence", target.get("confidence"));
            result.put("deviceId", deviceId);
            
            List<String> roidIds = (List<String>)target.remove("roiIds");
            if(CollectionUtils.isNotEmpty(roidIds)) {
                List<Integer> enter = (List<Integer>)target.remove("entryAlert");
                if(CollectionUtils.isNotEmpty(enter))
                    result.put("entryAlert", enter.stream().map(index -> roidIds.get(index)).collect(Collectors.toList()));

                List<Integer> stay = (List<Integer>)target.remove("triggerAlert");
                if(CollectionUtils.isNotEmpty(stay))
                    result.put("triggerAlert", stay.stream().map(index -> roidIds.get(index)).collect(Collectors.toList()));

                List<Integer> exit = (List<Integer>)target.remove("exitAlert");
                if(CollectionUtils.isNotEmpty(exit))
                    result.put("exitAlert", exit.stream().map(index -> roidIds.get(index)).collect(Collectors.toList()));
                
                List<Map<String, Object>> cross = (List<Map<String, Object>>)target.get("crossAlert");
                if(CollectionUtils.isNotEmpty(cross)) {
                    for(Map<String, Object> crossItem : cross)
                        crossItem.put("roiId", roidIds.get((int)crossItem.remove("roiIndex")));
                    
                    result.put("crossAlert", cross);
                }
            }
            
            BodyTrack track = findoutWhoItIs(trackMap.get(((Number)target.get("track_id")).intValue()));
            result.put("pedAttributes", rawAttributeMapToOutput(track.getPedAttributes()));
            
            if(track.getPersonId() != null) {
                result.put("personId", track.getPersonId());
                result.put("personScore", track.getPersonScore());
                result.put("faceAttributes", rawAttributeMapToOutput(track.getFaceAttributes()));
            }else if(track.getFaceFrame() != null) {
                String imagePath = FrameUtils.save_image_as_jpg(track.getFaceFrame(), ImageUtils.newFile(handlerEntity.getAnnotatorName()),processor.getImgSaveTag());
                result.put("image", imagePath);
                result.put("faceAttributes", rawAttributeMapToOutput(track.getFaceAttributes()));
            }
            
            return result;
        };
        
        modelResult.setOutputResult(targets.parallelStream().map(mapper).collect(Collectors.toList()));
    }

    @SuppressWarnings("unchecked")
    @Override
    public void releaseModelResult(ModelResult modelResult) {
        super.releaseModelResult(modelResult);
        
        long sourceId = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        Map<Integer, BodyTrack> trackMap = trackingMap.get(sourceId);
        if(trackMap == null)
            return ;
        
        List<Number> drops = (List<Number>)modelResult.getModelRequest().getParameter().getOrDefault("droppedIds", List.of());
        for(Number drop : drops) {
            BodyTrack track = trackMap.remove(drop.intValue());
            if(track != null)
                track.setFaceFrame(null);
        }
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        Processor processor = modelResult.getModelRequest().getProcessor();
        
        for(Integer[][] roi : processor.getRoi()) {
            for(int index = 0; index < roi.length - 1; index ++) {
                result.add(Line.builder().from(new int[] {roi[index][0], roi[index][1]}).to(new int[] {roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().from(new int[] {roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[] {roi[0][0], roi[0][1]}).build());
        }
        
        Map<Integer, Map<Integer, Map<String, Object>>> extras = (Map<Integer, Map<Integer, Map<String, Object>>>)processor.fetchExtras(function);
        for(Map<Integer, Map<String, Object>> top : extras.values())
            for(Map<String, Object> sub : top.values()) {
                List<Integer> crossDot = (List<Integer>)sub.get("crossDot");
                if(CollectionUtils.isNotEmpty(crossDot)) {
                    result.add(Line.builder().from(new int[] {crossDot.get(0) - 5, crossDot.get(1) - 5}).to(new int[] {crossDot.get(0) + 5, crossDot.get(1) + 5}).build());
                    result.add(Line.builder().from(new int[] {crossDot.get(0) + 5, crossDot.get(1) - 5}).to(new int[] {crossDot.get(0) - 5, crossDot.get(1) + 5}).build());
                }
            }
        
        Object outputResult = modelResult.getOutputResult();
        if(outputResult != null) {
            List<Map<String, Object>> outputs = List.of();
            if(outputResult.getClass().isArray()) {
                outputs = Arrays.stream((List<Map<String, Object>>[])outputResult).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
            }else if(outputResult instanceof List){
                outputs = (List<Map<String, Object>>)outputResult;
            }
            
            for(Map<String, Object> output : outputs) {
                String text = (String)output.getOrDefault("personId", "");
                
                if(output.containsKey("entryAlert"))
                    text += "entry:[" + output.get("entryAlert") + "]";
                
                if(output.containsKey("triggerAlert")) 
                    text += "trigger:[" + output.get("triggerAlert") + "]";
                
                if(output.containsKey("exitAlert")) 
                    text += "exit:[" + output.get("exitAlert") + "]";
                
                if(output.containsKey("crossAlert"))
                    text += "cross:[" + output.get("crossAlert") + "]";
                
                Map<String, Integer> roi = (Map<String, Integer>)output.get("detect");
                result.add(Rect.builder().processor(annotatorName()).text(text).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());
            }
        }
        
        Set<Integer> trackIds = (Set<Integer>)modelResult.getModelRequest().getParameter().getOrDefault("trackIds", Set.of());
        Map<Integer, BodyTrack> ongoingTracks = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        for(BodyTrack track : ongoingTracks.values()) {
            if(!trackIds.contains(track.getTrackId()))
                continue;
            
            for(Long in : track.getFirstInStamp()) {
                if(in != null) {
                    result.add(Rect.builder().processor(annotatorName()).top(track.getCurrentPosition().getTop()).left(track.getCurrentPosition().getLeft()).width(track.getCurrentPosition().getWidth()).height(track.getCurrentPosition().getHeight()).build());
                    break;
                }
            }
        }
        
        return result;
    }
    
    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            long contextId = Utils.keyToContextId(event.getDeviceId());
            
            try { Thread.sleep(1000); } catch (InterruptedException x) { }
            stopContext(contextId);
        }
    }
    
    private void startContext(long contextId, VideoStreamInfra infra) {
        if(trackingMap.containsKey(contextId))
            return ;
        
        trackingMap.put(contextId, new ConcurrentHashMap<Integer, BodyTrack>());
        
        log.info("PedIntrudeStay start context [" + contextId + "].");
    }
    
    private void stopContext(long contextId) {
        if(!trackingMap.containsKey(contextId))
            return ;

        Map<Integer, BodyTrack> tracks = trackingMap.remove(contextId);
        tracks.values().forEach(t -> t.setFaceFrame(null));
        
        holders[0].controlForRemoveSource(contextId);
        
        log.info("PedIntrudeStay stop context [" + contextId + "].");
    }
    
    @SuppressWarnings({ "unchecked", "rawtypes"})
    private static final Function<Object, Map> function = new Function<Object, Map>() {

        @Override
        public Map apply(Object e) {
            List<Map<String, Object>> params = (List<Map<String, Object>>)e;
            
            Map<Integer, Map<Integer, Map<String, Object>>> result = new HashMap<Integer, Map<Integer, Map<String, Object>>>();
            for(Map<String, Object> param : params) {
                if(!"mapping".equals(param.get("type"))) 
                    continue;
                
                Integer roiIndex = (Integer)param.getOrDefault("roiIndex", 0);
                Object targetLabel = param.get("targetType");
                
                Map<Integer, Map<String, Object>> roiParam = result.get(roiIndex);
                if(roiParam == null) {
                    roiParam = new HashMap<Integer, Map<String, Object>>();
                    result.put(roiIndex, roiParam);
                }

                roiParam.put(221488,  param);
            }
            
            for(Map<String, Object> param : params) {
                if("roiIds".equals(param.get("type"))) {
                    result.put(Integer.MAX_VALUE, Map.of(Integer.MAX_VALUE, Map.of("roiIds", param.get("roiIds"))));
                    break;
                }
            }
            
            return result;
        }
    }; 
    
    private static final List<Map<String, Object>> rawAttributeMapToOutput(Map<String, Map<String, Number>> rawAttributeMap){
        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        
        if(rawAttributeMap != null) {
            for(Entry<String, Map<String, Number>> entry : rawAttributeMap.entrySet()) {
                try {
                    Entry<String, Number> maxEntry = entry.getValue().entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
                    outputResult.add(Map.of("key", entry.getKey(), "value", maxEntry.getKey(), "confidence", maxEntry.getValue()));
                }catch(Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return outputResult;
    }
    
    private static final Pointer findSceneImage(Pointer keson, long sourceId, int trackId) {
        Pointer tracklets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(keson, 1), "targets");
        int trackletsize = KestrelApi.keson_array_size(tracklets);
        
        for(int index = 0; index < trackletsize; index ++) {
            Pointer tracklet = KestrelApi.keson_get_array_item(tracklets, index);
            if(sourceId == KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(tracklet, "source_id"))
                    && trackId  == KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(tracklet, "track_id"))) {
                return KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(tracklet, "scene_frame")).getValue();
            }
        }
        
        return null;
    }
    
    private static final Map<String, Map<String, Number>> dealWithAge(Map<String, Map<String, Number>> rawAttributeMap) {
        if(MapUtils.isEmpty(rawAttributeMap))
            return rawAttributeMap;
        
        Map<String, Number> upper = rawAttributeMap.remove("age_up_limit");
        if(upper != null)
            rawAttributeMap.put("age_up_limit", new HashMap<String, Number>(Map.of(upper.get("age_up_limit").toString(), 1)));
        
        Map<String, Number> lower = rawAttributeMap.remove("age_lower_limit");
        if(lower != null) 
            rawAttributeMap.put("age_lower_limit", new HashMap<String, Number>(Map.of(lower.get("age_lower_limit").toString(), 1)));
        
        return rawAttributeMap;
    }
    
    @SuppressWarnings("unchecked")
    private final BodyTrack findoutWhoItIs(BodyTrack track) {
        if(track.getFaceFrame() == null || track.getPersonId() != null)
            return track;
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> data = new LinkedMultiValueMap<String, String>();
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(data, headers);
            
            data.add("figureImageBase64", ImageUtils.bytesToBase64(FrameUtils.encode_image_as_jpg(track.getFaceFrame())));
            
            JSONObject response = restTemplate.postForObject("http://cognitivesvc/cognitive/face/compareFaceImage", request , JSONObject.class);
            List<Map<String, Object>> persons = (List<Map<String, Object>>)response.getOrDefault("data", List.of());
            if(CollectionUtils.isNotEmpty(persons)) {
                track.setPersonId(persons.get(0).get("personID").toString());
                track.setPersonScore(((Number)persons.get(0).get("score")).floatValue());
            }
        }catch(Exception e) {
            e.printStackTrace();
        }
        
        return track;
    }
    
    private static final boolean intersect(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4) {
        float bx = x2 - x1; 
        float by = y2 - y1; 
        float dx = x4 - x3; 
        float dy = y4 - y3;
        float b_dot_d_perp = bx * dy - by * dx;
  
        if (b_dot_d_perp == 0)
            return false;
  
        float cx = x3 - x1;
        float cy = y3 - y1;
        float t = (cx * dy - cy * dx) / b_dot_d_perp;
        if (t < 0 || t > 1) 
            return false;
  
        float u = (cx * by - cy * bx) / b_dot_d_perp;
        if (u < 0 || u > 1)
            return false;
  
        return true;
    }
    
    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class BodyTrack{        
        private final int trackId;
        
        private final Long[] firstInStamp;
        private final Long[] keepingInStamp;

        private volatile Rect previousPosition;
        private volatile Rect currentPosition;

        private volatile Pointer faceFrame;
        
        @Setter
        private volatile Map<String, Map<String, Number>> faceAttributes;
        
        @Setter
        private volatile Map<String, Map<String, Number>> pedAttributes;

        @Setter
        private volatile Integer faceTrackId;

        @Setter
        private volatile String personId;

        @Setter
        private volatile Float personScore;
        
        public void setCurrentPosition(Map<String, Number> roi) {
            previousPosition = currentPosition;
            currentPosition = Rect.builder().top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).build();
        }
        
        public void setFaceFrame(Pointer faceFrame) {
            if(this.faceFrame != null)
                FrameUtils.batch_free_frame(this.faceFrame);
            
            this.faceFrame = faceFrame;
            this.personId = null;
        }

        @Data
        @Accessors(chain = true)
        @Builder
        public static class Rect{
            private int left;
            private int top;
            private int width;
            private int height;
        }
    }
}