package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;


public class FaceNoTrack extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{


    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {

        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();
        if(MapUtils.isEmpty(detectResult))
            return ;


        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();

        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of());

        Pointer videoFrame = modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame();

        Function<Map<String, Object>, Map<String, Object>> mapper = target -> {
            Map<String, Object> result = new HashMap<String, Object>();

            Map<String, Object> roi = (Map<String, Object>) target.get("roi");
            if (roi != null) {
                result.put("detect", target.get("roi"));
                result.put("targetType", target.get("label"));
                result.put("capturedTime", target.get("capturedTime"));
                result.put("confidence", target.get("confidence"));
                result.put("deviceId", deviceId);

                int top    = ((Number)roi.get("top")).intValue();
                int left   = ((Number)roi.get("left")).intValue();
                int width  = ((Number)roi.get("width")).intValue();
                int height = ((Number)roi.get("height")).intValue();

                List<Map<String, Object>> attributeList = new ArrayList<Map<String, Object>>();
                result.put("attributes", attributeList);

                Pointer targetRoiFrame = FrameUtils.roi_frame(videoFrame,  left,  top,  width,  height);
                try {
                    NoTrack track = findoutWhoItIs(targetRoiFrame);

                    attributeList.add(Map.of("personId", track.getPersonId(), "personScore", track.getPersonScore()));

                }catch (Exception e){
                    FrameUtils.batch_free_frame(targetRoiFrame);
                }finally {
                    FrameUtils.batch_free_frame(targetRoiFrame);
                }
            }
            return result;
        };

        modelResult.setOutputResult(targets.parallelStream().map(mapper).filter(i -> !i.isEmpty()).collect(Collectors.toList()));

    }

    @SuppressWarnings("unchecked")
    private final NoTrack findoutWhoItIs(Pointer videoFrame) {

        NoTrack track = new NoTrack();

        if(videoFrame == null )
            return track;

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> data = new LinkedMultiValueMap<String, String>();
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(data, headers);

            data.add("figureImageBase64", ImageUtils.bytesToBase64(FrameUtils.encode_image_as_jpg(videoFrame)));

            JSONObject response = restTemplate.postForObject("http://cognitivesvc/cognitive/face/compareFaceImage", request , JSONObject.class);
            List<Map<String, Object>> persons = (List<Map<String, Object>>)response.getOrDefault("data", List.of());
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(persons)) {
                track.setPersonId(persons.get(0).get("personID").toString());
                track.setPersonScore(((Number)persons.get(0).get("score")).floatValue());
            }
        }catch(Exception e) {
            e.printStackTrace();
        }

        return track;
    }

    @Getter
    private  static  final class NoTrack{

        private volatile Pointer faceFrame;

        @Setter
        private volatile String personId;

        @Setter
        private volatile Float personScore;

        public NoTrack() {

        }
    }





}
