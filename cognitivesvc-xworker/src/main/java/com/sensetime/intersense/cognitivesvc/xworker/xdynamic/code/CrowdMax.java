package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.awt.Polygon;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_crowd_overseaLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_deviceLibrary.kestrel_mem_type_e;
import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_frameLibrary.kestrel_video_format_e;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.*;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XStoreHandler;
import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CrowdMax extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    private static final ThreadLocal<Memory> frameBatchLocal = ThreadLocal.withInitial(() -> new Memory(Native.POINTER_SIZE * 512));
    
    private static final ThreadLocal<Memory> contextIdsBatchLocal = ThreadLocal.withInitial(() -> new Memory(Native.LONG_SIZE * 512));
    
    private volatile Set<Long> contextIds = new HashSet<Long>();
    
    private Map<String, Object> trackerConfig;
    
    private Pointer crowd_tracker;    
    
    private String[] detainedKeps;

    //<deviceId, processorJson>
    private final Map<String, String> detainedProcessorMap = new HashMap<String, String>();
    
    @Autowired
    private XStoreHandler xStoreHandler;

    @Getter
    protected final Boolean blocking = false;   
    
    @Getter
    protected final Integer interval = 3;//��һ����
    
    @Getter
    protected final Integer frameBuffer = 24;//�Դ�С����20, �Դ������΢���һЩ
    
    @Getter
    protected final String frameBufferStrategy = "pedantic_swallow";
    
    @Getter
    protected final Integer minBatchSize = 8;//�ܹ����������һ����ģ�� ���ִ�һЩ������������Ϣ��
    
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if(status != 0)
            return new PointerByReference[0];    
            
        Set<Long> currentContextIds = this.contextIds;
        int[] runningIndexs = IntStream.range(0, handlingList.size())
                .filter(index -> currentContextIds.contains(deviceIdToContextId(handlingList.get(index).getModelRequest().getParameter().get("deviceId").toString())))
                .toArray();
            
        if(runningIndexs.length <= 0) 
            return new PointerByReference[0];   
        
        Memory frameBatch = frameBatchLocal.get();
        Memory contextIdsBatch = contextIdsBatchLocal.get();
        
        handlingList.get(0).getModelRequest().getParameter().put("runningIndexs", runningIndexs);
        
        for(int index = 0; index < runningIndexs.length; index ++) {
            BatchItem item = handlingList.get(runningIndexs[index]);
            
            long itemContextId = deviceIdToContextId(item.getModelRequest().getParameter().get("deviceId").toString());
            VideoFrame videoFrames[] = item.getModelRequest().getVideoFrames();
            
            for(int jndex = 0; jndex < minBatchSize; jndex ++) {
                int realIndex = index * minBatchSize + jndex;
                frameBatch.setPointer(realIndex * Native.POINTER_SIZE , videoFrames[jndex].getGpuFrame());
                contextIdsBatch.setLong(realIndex  * Native.LONG_SIZE , itemContextId);
            }
        }
        
        PointerByReference result_target = new PointerByReference(); 
        IntByReference result_num = new IntByReference();
        
        synchronized(crowd_tracker) {
            long now = System.currentTimeMillis();
            Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_update_batch(crowd_tracker, frameBatch, contextIdsBatch, runningIndexs.length * minBatchSize, result_target, result_num);
            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        }
        
        frameBatch.clear();
        contextIdsBatch.clear();
        
        PointerByReference[] result = new PointerByReference[2];
        result[0] = result_target;
        result[1] = new PointerByReference(KestrelApi.keson_create_int(result_num.getValue()));

        if(status != 0) {
            Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_release_update_result(result_target.getValue(), result_num.getValue());
            return new PointerByReference[0];    
        }else    
            return result;  
    }
    
    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        if(ArrayUtils.isEmpty(outputKesons))
            return;
        
        PointerByReference result_target = outputKesons[0];
        IntByReference result_num = new IntByReference((int)KestrelApi.keson_get_int(outputKesons[1].getValue())); 
        
        KesonUtils.kesonDeepDelete(outputKesons[1]);
        for(int index = 0; index < outputKesons.length; index ++)
            outputKesons[index] = null;
        
        Map<Long, List<crowd_analyze_result>> analyzeMap = new HashMap<Long, List<crowd_analyze_result>>();
        for(int index = 0; index < result_num.getValue(); index ++) {
            crowd_analyze_result crowd_analyze = new crowd_analyze_result(new Pointer(Pointer.nativeValue(result_target.getValue()) + KestrelApi._SIZE_crowd_analyze_result * index));
            crowd_analyze.read();

            List<crowd_analyze_result> analyzes = analyzeMap.get(crowd_analyze.context_id);
            if(analyzes == null) {
                analyzes = new ArrayList<crowd_analyze_result>();
                analyzeMap.put(crowd_analyze.context_id, analyzes);
            }
            
            analyzes.add(crowd_analyze);
        }
        
        for(List<crowd_analyze_result> analyzes : analyzeMap.values())
            analyzes.sort((l, r) -> Long.compare(l.frame_index, r.frame_index));
                
        int[] runningIndexs = (int[])handlingList.get(0).getModelRequest().getParameter().remove("runningIndexs");
        
        for(int index = 0; index < runningIndexs.length; index ++) {
            BatchItem item = handlingList.get(runningIndexs[index]);
            long contextId = deviceIdToContextId(item.getModelRequest().getParameter().get("deviceId").toString());
            
            List<crowd_analyze_result> analyzes = analyzeMap.get(contextId);
            if(CollectionUtils.isEmpty(analyzes))
                continue;
            
            Pointer resultKeson = KestrelApi.keson_create_object();
            item.setKeson(new PointerByReference(resultKeson));
            
            KestrelApi.keson_add_item_to_object(resultKeson, "contextId", KestrelApi.keson_create_int(contextId));
            
            Pointer frameArray = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(resultKeson, "frameArray", frameArray);
            
            for(int jndex = 0; jndex < analyzes.size(); jndex ++) {
                crowd_analyze_result crowd_analyze = analyzes.get(jndex);
                
                Pointer keson = KestrelApi.keson_create_object();
                KestrelApi.keson_add_item_to_object(keson, "frame_index", KestrelApi.keson_create_int(jndex));
                KestrelApi.keson_add_item_to_array(frameArray, keson);
                
                //��ͷ�������ռ�
                if(crowd_analyze.global_density_result.head_num > 0)
                    extractGlobalDensityResult(keson, crowd_analyze.global_density_result, "density");
                
                //���������ռ�    
                if(crowd_analyze.intrusion_roi_result_list.roi_cnt > 0)
                    extractCrowdRoiResult(keson, crowd_analyze.intrusion_roi_result_list, "intrusion");
                
                //���������ռ�
                if(crowd_analyze.retention_roi_result_list.roi_cnt > 0)
                    extractCrowdRoiResult(keson, crowd_analyze.retention_roi_result_list, "retention");
                
                //���������ռ�    
                if(crowd_analyze.retrograde_roi_result_list.roi_cnt > 0)
                    extractCrowdRoiResult(keson, crowd_analyze.retrograde_roi_result_list, "retrograde");
                
                //��Ⱥ�ٶ������ռ�    
                if(crowd_analyze.speed_roi_result_list.roi_cnt > 0)
                    extractCrowdRoiResult(keson, crowd_analyze.speed_roi_result_list, "speed");
                
                //�ۼ������ռ�
                if(crowd_analyze.congregate_roi_result_list.roi_cnt > 0)
                    extractCrowdMovementRoiResult(keson, crowd_analyze.congregate_roi_result_list, "congregate");
                
                //��ɢ�����ռ�
                if(crowd_analyze.scatter_roi_result_list.roi_cnt > 0) 
                    extractCrowdMovementRoiResult(keson, crowd_analyze.scatter_roi_result_list, "scatter");

                //���������ռ�
                if(crowd_analyze.cross_roi_result_list.cross_roi_cnt > 0)
                    extractCrowdCrossRoiResult(keson, crowd_analyze.cross_roi_result_list, "cross");
                
                //�罻�����ռ�
                if(crowd_analyze.close_roi_result_list.roi_cnt > 0)
                    extractCrowdCloseRoiResult(keson, crowd_analyze.close_roi_result_list, "close");
                
                //�Ŷ��ռ�
                if(crowd_analyze.queue_roi_result_list.roi_cnt > 0)
                    extractCrowdQueueRoiResult(keson, crowd_analyze.queue_roi_result_list, "queue");
            }
        }
        Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_release_update_result(result_target.getValue(), result_num.getValue());
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        if(ArrayUtils.isEmpty(polygons))
            return false;

        List<List<Map<String, Object>>> results = new ArrayList<List<Map<String, Object>>>();
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);
        List<Map<String, Object>> frameArray = (List<Map<String, Object>>)Objects.requireNonNullElse((Map<String, Object>)modelResult.getDetectResult(), Map.of()).getOrDefault("frameArray", List.of());
        
        for(Map<String, Object> detectedItem : frameArray) {
            List<Map<String, Object>> roiTargets[] = new List[polygons.length];
            for(int index = 0 ;index < polygons.length; index ++)
                roiTargets[index] = new ArrayList<Map<String, Object>>();
            
            List<Map<String, Object>> targets = (List<Map<String, Object>>)detectedItem.getOrDefault("density_targets", List.of());
            for(Map<String, Object> target : targets) {
                int x = (int)target.get("x");
                int y = (int)target.get("y");
                
                for(int index = 0 ;index < polygons.length; index ++)
                    if(polygons[index].contains(x, y))
                        roiTargets[index].add(Map.copyOf(target));
            }
            
            List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
            for(int index = 0 ;index < polygons.length; index ++) {
                Map<String, Object> extrasMap = roiIndexExtrasMap.get(index);
                if(MapUtils.isEmpty(extrasMap))
                    continue;
                
                Number minAlert = (Number)extrasMap.get("minAlert");
                Number maxAlert = (Number)extrasMap.get("maxAlert");
                
                List<Map<String, Object>> roiTarget = roiTargets[index];
                Map<String, Object> round = new HashMap<String, Object>();
                
                if(maxAlert != null && minAlert != null) {
                	if(roiTarget.size() >= minAlert.intValue() && roiTarget.size() <= maxAlert.intValue())
                		round.put("Alert", roiTarget.size());
                }else if(maxAlert != null){
                	if(roiTarget.size() <= maxAlert.intValue())
                		round.put("Alert", roiTarget.size());
                }else if(minAlert != null){
                	if(roiTarget.size() >= minAlert.intValue()) 
                		round.put("Alert", roiTarget.size());
                }else {
                	round.put("Alert", roiTarget.size());
                }
                
                if(round.isEmpty())
                    continue;
                
                reFormatRois(detectedItem, index, "intrusion", round);
                reFormatRois(detectedItem, index, "cross", round);
                reFormatRois(detectedItem, index, "retention", round);
                reFormatRois(detectedItem, index, "retrograde", round);
                reFormatRois(detectedItem, index, "speed", round);
                reFormatRois(detectedItem, index, "congregate", round);
                reFormatRois(detectedItem, index, "scatter", round);
                reFormatRois(detectedItem, index, "close", round);
                reFormatRois(detectedItem, index, "queue", round);
                
                round.put("roiId", index);
                round.put("frameId", detectedItem.get("frame_index"));
                result.add(round);
            }
            
            if(!result.isEmpty())
                results.add(result);
        }
        
        if(!results.isEmpty())
            modelResult.setOutputResult(results);
        
        return !results.isEmpty();
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) { 
        List<List<Map<String, Object>>> resultLists = new ArrayList<List<Map<String, Object>>>();
        
        List<List<Map<String, Object>>> outputLists = (List<List<Map<String, Object>>>)modelResult.getOutputResult();
        List<String> roiIds = (List<String>)modelResult.getModelRequest().getProcessor().getExtras().stream().filter(item -> "roiIds".equals(item.get("type"))).findAny().orElse(Map.of()).getOrDefault("roiIds", List.of());

        for(List<Map<String, Object>> outputList : outputLists) {
            List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
            
            for(Map<String, Object> outputItem : outputList) {
                Map<String, Object> result = new HashMap<String, Object>(outputItem);
                resultList.add(result);
                
                result.put("deviceId", modelResult.getModelRequest().getParameter().get("deviceId"));
                int roiIndex = (int)outputItem.get("roiId");
                if(roiIndex < roiIds.size())
                    result.put("roiId", roiIds.get(roiIndex));
            }
            
            resultLists.add(resultList);
        }
        
        modelResult.setOutputResult(resultLists);
    }
     
    @Override
    public List<Drawing> draw(ModelResult modelResult){
        List<Drawing> result = new ArrayList<Drawing>();
        return result;
    }
    
    @PostConstruct
    @Override
    public synchronized void initialize() {
        if(crowd_tracker != null)
            return ;
        
        log.info("CrowdMax initializing.");

        super.initialize();
        
        String[] keps = Arrays.stream(handlerEntity.getAttacheds()).filter(attached -> attached.endsWith("kep")).toArray(String[]::new);
        String[] models = Arrays.stream(handlerEntity.getAttacheds()).filter(attached -> attached.endsWith("model")).toArray(String[]::new);
        
        detainedKeps = new String[keps.length];
        for(int index = 0; index < keps.length; index ++)
            detainedKeps[index] = KestrelApi.kestrel_plugin_load(keps[index], "");
        
        trackerConfig = trackerConfig(models);
        crowd_tracker = Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_create(JSON.toJSONString(trackerConfig));

        log.info("CrowdMax initialized. config : " + (Utils.instance.logged ? JSON.toJSONString(trackerConfig) : "."));
    }
    
    
    
    @PreDestroy
    @Override
    public synchronized void destroy() {
        log.info("CrowdMax destroying.");
        
        if(crowd_tracker == null)
            return ;
        
        Set<Long> contextIds = this.contextIds;
        this.contextIds = new HashSet<Long>();
        
        try { Thread.sleep(1000); } catch (InterruptedException e) { }
        for(Long contextId : contextIds)
            stopContext(contextId);
        
        synchronized(crowd_tracker) {
            Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_destroy(crowd_tracker);
            crowd_tracker = null;
        }
        
        for(String kep : detainedKeps)
        	KestrelApi.kestrel_plugin_unload(kep);
        
        super.destroy();
        
        log.info("CrowdMax destroyed.");
    }
    
    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
            if(!Objects.equals(handlerEntity.getAnnotatorName(), event.getAnnotatorName()))
                return;

            Processor processor = xStoreHandler.getHighProcessorMap().getOrDefault(event.getInfra().getDeviceId(), Map.of()).get(annotatorName());
            if(processor == null)
                return;
            
            long contextId = deviceIdToContextId(event.getInfra().getDeviceId());
            if(contextIds.contains(contextId))
                return ;

            detainedProcessorMap.put(event.getInfra().getDeviceId(), JSON.toJSONString(processor));
            
            startContext(contextId, event.getInfra(), processor);
            try { Thread.sleep(5000); } catch (InterruptedException x) { }

            Set<Long> newContextIds = new HashSet<Long>(contextIds);
            newContextIds.add(contextId);
            this.contextIds = newContextIds;
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            long contextId = deviceIdToContextId(event.getDeviceId());
            if(!contextIds.contains(contextId) || XworkerStreamClosedEvent.REOPEN.equals(event.getCause()))
                return ;
            
            Set<Long> newContextIds = new HashSet<Long>(contextIds);
            newContextIds.remove(contextId);
            this.contextIds = newContextIds;

            try { Thread.sleep(5000); } catch (InterruptedException x) { }
            stopContext(contextId);

            detainedProcessorMap.remove(event.getDeviceId());
        }
    }
    
    private void startContext(long contextId, VideoStreamInfra infra, Processor processor) {
        log.info("CrowdMax starting context [" + contextId + "].");
        
        Map<String, Object> contextConfig = contextConfig(infra, processor);
        String context = JSON.toJSONString(contextConfig);
        
        Map<String, Object> trackerConfig = new HashMap<String, Object>(this.trackerConfig);
        trackerConfig.put("context_config", List.of(contextConfig));
        String contextLog = Utils.instance.logged ? JSON.toJSONString(trackerConfig) : ".";
        
        synchronized(crowd_tracker) {
            int ret = Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_create_context(crowd_tracker, contextId, context);
            if(ret != 0)
                throw new RuntimeException("CrowdMax error start context [" + contextId + "] config [" + contextLog + "].");
        }
        
        log.info("CrowdMax started context [" + contextId + "] config [" + contextLog + "].");
    }
    
    private void stopContext(long contextId) {
        log.info("CrowdMax stoping context [" + contextId + "].");
        
        synchronized(crowd_tracker) {            
            PointerByReference result_target = new PointerByReference(); 
            IntByReference result_num = new IntByReference();
            
            Memory frameBatch = new Memory(Native.POINTER_SIZE);
            Memory contextIdsBatch = new Memory(Native.LONG_SIZE);
            
            Pointer frame = KestrelApi.kestrel_frame_alloc(kestrel_mem_type_e.KESTREL_MEM_DEVICE, kestrel_video_format_e.KESTREL_VIDEO_RGB, 1920, 1080, new int[] {5760, 0, 0, 0}, new int[] {0});

            frameBatch.setPointer(0 , frame);
            contextIdsBatch.setLong(0 , contextId);
            
            for(int index = 0; index < 50; index ++) {
            	KestrelApi.kestrel_frame_set_pts(frame, System.currentTimeMillis() + index * 100);
                Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_update_batch(crowd_tracker, frameBatch, contextIdsBatch, 1, result_target, result_num);
            }
            
            Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_release_update_result(result_target.getValue(), result_num.getValue());
            Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_free_context(crowd_tracker, contextId);
            
            FrameUtils.batch_free_frame(frame);
        }
        
        log.info("CrowdMax stoped context [" + contextId + "].");
    }
    
    @SuppressWarnings({ "unchecked", "rawtypes" })
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();
        
        for(Map<String, Object> param : (List<Map<String, Object>>)e) {
            if(!"mapping".equals(param.get("type"))) 
                continue;
    
            Integer roiIndex = (Integer)param.get("roiIndex");
            if(roiIndex != null)
                result.put(roiIndex, param);
        }
    
        return result;
    };
    
    private static final long deviceIdToContextId(String deviceId) {
        return Math.abs(deviceId.hashCode());
    }
    
    private Map<String, Object> trackerConfig(String[] models) {
        Map<String, Object> trackerConfig = new HashMap<String, Object>();

        Map<String, Object> modelsConfig = new HashMap<String, Object>();
        trackerConfig.put("models", modelsConfig);
        
        Map<String, Object> localizationModelConfig = new HashMap<String, Object>();
        modelsConfig.put("localization", localizationModelConfig);
        localizationModelConfig.put("model", models[0]);
        localizationModelConfig.put("plugin", "colo");
        localizationModelConfig.put("type", "localization");
        localizationModelConfig.put("max_batch_size", 1);
        
        Map<String, Object> countModelConfig = new HashMap<String, Object>();
        modelsConfig.put("count", countModelConfig);
        countModelConfig.put("model", models[1]);
        countModelConfig.put("plugin", "colo");
        countModelConfig.put("process_rate", "1");
        countModelConfig.put("type", "count");
        countModelConfig.put("max_batch_size", 8);
        
        Map<String, Object> headShoulderModelConfig = new HashMap<String, Object>();
        modelsConfig.put("head_shoulder", headShoulderModelConfig);
        headShoulderModelConfig.put("model", models[2]);
        headShoulderModelConfig.put("plugin", "hunter");
        headShoulderModelConfig.put("type", "head_shoulder");
        headShoulderModelConfig.put("max_batch_size", 8);
        
        Map<String, Object> faceBodyModelConfig = new HashMap<String, Object>();
        modelsConfig.put("face_body", faceBodyModelConfig);
        faceBodyModelConfig.put("model", models[3]);
        faceBodyModelConfig.put("plugin", "hunter");
        faceBodyModelConfig.put("type", "face_body");
        faceBodyModelConfig.put("max_batch_size", 8);
        
        Map<String, Object> harpyModelConfig = new HashMap<String, Object>();
        modelsConfig.put("harpy", harpyModelConfig);
        harpyModelConfig.put("model", models[4]);
        harpyModelConfig.put("plugin", "harpy");
        harpyModelConfig.put("type", "harpy");
        harpyModelConfig.put("max_batch_size", 8);
        
        Map<String, Object> trackerParamsConfig = new HashMap<String, Object>();
        trackerConfig.put("tracker_params", trackerParamsConfig);
        Map<String, Object> sotModelConfig = new HashMap<String, Object>();
        trackerParamsConfig.put("sot_model", sotModelConfig);
        sotModelConfig.put("plugin", "hermes");
        sotModelConfig.put("model", models[5]);
        sotModelConfig.put("max_batch_size", 128);
        
        return trackerConfig;
    }
    
    @SuppressWarnings("unchecked")
    private Map<String, Object> contextConfig(VideoStreamInfra infra, Processor processor) {
        Map<String, Object> contextConfig = new HashMap<String, Object>();
        contextConfig.put("video_width", infra.getRtspWidth());
        contextConfig.put("video_height", infra.getRtspHeight());
        contextConfig.put("head_thres", 0.25);
        contextConfig.put("head_suppress_radius", 1);
        contextConfig.put("count_process_rate", 1);
        contextConfig.put("crowd_sparse_threshold", 30);
        contextConfig.put("sot_predependency_detection_model", List.of("harpy", "head_shoulder", "face_body"));
        
        Map<String, Object> modelMap = new HashMap<String, Object>();
        contextConfig.put("model_map", modelMap);    

        modelMap.put("default_count",     "count");
        modelMap.put("default_detection", "localization");
        modelMap.put("intrusion",         "localization");
        modelMap.put("cross_line",        "harpy");
        modelMap.put("retention",         "harpy");
        modelMap.put("retrograde",        "harpy");
        modelMap.put("congregate",        "harpy");
        modelMap.put("speed",             "harpy");
        modelMap.put("social_distance",   "head_shoulder");
        modelMap.put("queue",             "face_body");
        
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);
        Integer[][][] rois = processor.getRoi();
        for(Integer[][] roi : rois) {
            for(Integer[] point : roi) {
                if(point[0] < 20)
                    point[0] = 20;
                
                if(point[0] > infra.getRtspWidth() - 20)
                    point[0] = infra.getRtspWidth() - 20;
                
                if(point[1] < 20)
                    point[1] = 20;
                
                if(point[1] > infra.getRtspHeight() - 20)
                    point[1] = infra.getRtspHeight() - 20;
            }
        }
        
        for(int index = 0; index < rois.length; index ++) {
            Map<String, Object> roiIndexParam = roiIndexExtrasMap.get(index);
            if(roiIndexParam == null || (!roiIndexParam.containsKey("maxAlert") && !roiIndexParam.containsKey("minAlert")))
                continue;
            
            if(buildParamVertexes(contextConfig, roiIndexParam, index, rois[index], "congregate",     "congregate_rois"))
                contextConfig.put("turn_congregate_on", true);
            
            if(buildParamVertexes(contextConfig, roiIndexParam, index, rois[index], "intrusion",      "intrusion_rois"))
                contextConfig.put("turn_intrusion_on", true);
            
            if(buildParamVertexes(contextConfig, roiIndexParam, index, rois[index], "queue",          "queue_rois"))
                contextConfig.put("turn_queue_on", true);
            
            if(buildParamVertexes(contextConfig, roiIndexParam, index, rois[index], "retention",      "retention_rois"))
                contextConfig.put("turn_retention_on", true);
            
            if(buildParamVertexes(contextConfig, roiIndexParam, index, rois[index], "retrograde",     "retrograde_rois"))
                contextConfig.put("turn_retrograde_on", true);
            
            if(buildParamVertexes(contextConfig, roiIndexParam, index, rois[index], "socialDistance", "social_distance_rois"))
                contextConfig.put("turn_social_distance_on", true);
            
            if(buildParamVertexes(contextConfig, roiIndexParam, index, rois[index], "speed",          "speed_rois"))
                contextConfig.put("turn_speed_on", true);
            
            if(buildParamLines   (contextConfig, roiIndexParam, index, rois[index], "crossLine",      "cross_line_rois"))
                contextConfig.put("turn_cross_line_on", true);
        }
        
        return contextConfig;
    }
    
    @SuppressWarnings("unchecked")
    private static final boolean buildParamVertexes(Map<String, Object> contextConfig, Map<String, Object> roiIndexParam, int index, Integer[][] roi, String switcherType, String sdkType) {
        if(!roiIndexParam.containsKey(switcherType)) 
            return false;

        Map<String, Object> param = (Map<String, Object>)roiIndexParam.get(switcherType);
        if(!Boolean.TRUE.equals(param.remove("enabled")))
            return false;

        List<Map<String, Object>> context_rois = (List<Map<String, Object>>)contextConfig.get(sdkType);
        if(context_rois == null) {
            context_rois = new ArrayList<Map<String, Object>>();
            contextConfig.put(sdkType, context_rois);
        }

        param.put("id", index);
        param.put("vertexes", Arrays.stream(roi).map(point -> "(" + point[0] + "," + point[1] + ")").collect(Collectors.joining(",")));
        context_rois.add(param);
        
        return true;
    }
    
    @SuppressWarnings("unchecked")
    private static final boolean buildParamLines(Map<String, Object> contextConfig, Map<String, Object> roiIndexParam, int index, Integer[][] roi, String switcherType, String sdkType) {
        if(!roiIndexParam.containsKey(switcherType)) 
            return false;

        Map<String, Object> param = (Map<String, Object>)roiIndexParam.get(switcherType);
        if(!Boolean.TRUE.equals(param.remove("enabled")))
            return false;

        List<Map<String, Object>> context_rois = (List<Map<String, Object>>)contextConfig.get(sdkType);
        if(context_rois == null) {
            context_rois = new ArrayList<Map<String, Object>>();
            contextConfig.put(sdkType, context_rois);
        }
        
        List<Map<String, Object>> lines = new ArrayList<Map<String, Object>>();
        for(int jndex = 0; jndex < roi.length; jndex ++) {
            Integer[] pointX = roi[jndex];
            Integer[] pointY = jndex < roi.length - 1 ? roi[jndex + 1] : roi[0];
            
            Map<String, Object> line = new HashMap<String, Object>();
            line.put("x1", pointX[0]);
            line.put("y1", pointX[1]);
            line.put("x2", pointY[0]);
            line.put("y2", pointY[1]);
            lines.add(line);
        }
        
        param.put("id", index);
        param.put("lines", lines);
        context_rois.add(param);
        
        return true;
    }
    
    @SuppressWarnings("unchecked")
    private static void reFormatRois(Map<String, Object> detectedResult, int index, String type, Map<String, Object> result) {
        List<Map<String, Object>> rois = (List<Map<String, Object>>)detectedResult.getOrDefault(type + "_rois", List.of());
        for(Map<String, Object> roi : rois) {
            if(index != (int)roi.getOrDefault("roi_id", -1)) 
                continue;
            
            result.put(type, roi);
            roi.remove("roi_id");
            break;
        }
    }
    
    private static final void extractGlobalDensityResult(Pointer keson, crowd_density_result_t crowd_density_result, String type) {
        Pointer targetsKeson = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_object(keson, type + "_targets", targetsKeson);

        for(int kndex = 0; kndex < crowd_density_result.head_num; kndex ++) {
            kestrel_point2df_t points = new kestrel_point2df_t(new Pointer(Pointer.nativeValue(crowd_density_result.head_loc_array) + KestrelApi._SIZE_kestrel_point2df_t * kndex));
            points.read();
            
            Pointer targetKeson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(targetsKeson, targetKeson);
            
            KestrelApi.keson_add_item_to_object(targetKeson, "x",  KestrelApi.keson_create_int((long)points.x));
            KestrelApi.keson_add_item_to_object(targetKeson, "y", KestrelApi.keson_create_int((long)points.y));
        }
    }
    
    private static final void extractCrowdCrossRoiResult(Pointer keson, crowd_cross_roi_result_list_t cross_result_list, String type) {
        Pointer roisKeson = KestrelApi.keson_create_array();
        
        for(int kndex = 0; kndex < cross_result_list.cross_roi_cnt; kndex ++) {
            crowd_cross_roi_result_t crowdRoi = new crowd_cross_roi_result_t(new Pointer(Pointer.nativeValue(cross_result_list.cross_roi_results) + KestrelApi._SIZE_crowd_cross_roi_result_t * kndex));
            crowdRoi.read();
            
            if(crowdRoi.head_targets.targets_num <= 0)
                continue;
            
            Pointer roiKeson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(roisKeson, roiKeson);
            
            KestrelApi.keson_add_item_to_object(roiKeson, "roi_id",  KestrelApi.keson_create_int(crowdRoi.roi_id));
            
            Pointer targetsKeson = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(roiKeson, "targets", targetsKeson);
            
            for(int mndex = 0; mndex < crowdRoi.head_targets.targets_num; mndex ++) {
                crowd_head_target_t target = new crowd_head_target_t(new Pointer(Pointer.nativeValue(crowdRoi.head_targets.targets) + KestrelApi._SIZE_crowd_head_target_t * mndex));
                target.read();
                
                Pointer targetKeson = KestrelApi.keson_create_object();
                KestrelApi.keson_add_item_to_array(targetsKeson, targetKeson);
                
                KestrelApi.keson_add_item_to_object(targetKeson, "x",  KestrelApi.keson_create_int((long)target.pt_position.x));
                KestrelApi.keson_add_item_to_object(targetKeson, "y",  KestrelApi.keson_create_int((long)target.pt_position.y));
                
                if(target.speed >= 0)
                    KestrelApi.keson_add_item_to_object(targetKeson, "speed",  KestrelApi.keson_create_int((long)target.speed));
            }
        }
        
        if(KestrelApi.keson_array_size(roisKeson) > 0)
            KestrelApi.keson_add_item_to_object(keson, type + "_rois", roisKeson);
        else
            KesonUtils.kesonDeepDelete(roisKeson);
    }
    
    private static final void extractCrowdRoiResult(Pointer keson, crowd_roi_result_list_t roi_result_list, String type) {
        Pointer roisKeson = KestrelApi.keson_create_array();
        
        for(int kndex = 0; kndex < roi_result_list.roi_cnt; kndex ++) {
            crowd_roi_result_t crowdRoi = new crowd_roi_result_t(new Pointer(Pointer.nativeValue(roi_result_list.roi_results) + KestrelApi._SIZE_crowd_roi_result_t * kndex));
            crowdRoi.read();
            
            if(crowdRoi.head_targets.targets_num <= 0)
                continue;
            
            Pointer roiKeson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(roisKeson, roiKeson);
            KestrelApi.keson_add_item_to_object(roiKeson, "roi_id",  KestrelApi.keson_create_int(crowdRoi.roi_id));
            if(crowdRoi.crowd_speed >= 0)
                KestrelApi.keson_add_item_to_object(roiKeson, "crowd_speed",  KestrelApi.keson_create_double(crowdRoi.crowd_speed));
            
            Pointer targetsKeson = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(roiKeson, "targets", targetsKeson);
            
            for(int mndex = 0; mndex < crowdRoi.head_targets.targets_num; mndex ++) {
                crowd_head_target_t target = new crowd_head_target_t(new Pointer(Pointer.nativeValue(crowdRoi.head_targets.targets) + KestrelApi._SIZE_crowd_head_target_t * mndex));
                target.read();
                
                Pointer targetKeson = KestrelApi.keson_create_object();
                KestrelApi.keson_add_item_to_array(targetsKeson, targetKeson);
                
                KestrelApi.keson_add_item_to_object(targetKeson, "x",  KestrelApi.keson_create_int((long)target.pt_position.x));
                KestrelApi.keson_add_item_to_object(targetKeson, "y",  KestrelApi.keson_create_int((long)target.pt_position.y));
                
                if(target.speed >= 0)
                    KestrelApi.keson_add_item_to_object(targetKeson, "speed",  KestrelApi.keson_create_int((long)target.speed));
            }
        }
        
        if(KestrelApi.keson_array_size(roisKeson) > 0)
            KestrelApi.keson_add_item_to_object(keson, type + "_rois", roisKeson);
        else
            KesonUtils.kesonDeepDelete(roisKeson);
    }
    
    private static final void extractCrowdMovementRoiResult(Pointer keson, crowd_movement_roi_result_list_t roi_result_list, String type) {
        Pointer roisKeson = KestrelApi.keson_create_array();
        
        for(int kndex = 0; kndex < roi_result_list.roi_cnt; kndex ++) {
            crowd_movement_roi_result_t movementRoi = new crowd_movement_roi_result_t(new Pointer(Pointer.nativeValue(roi_result_list.roi_results) + KestrelApi._SIZE_crowd_movement_roi_result_t * kndex));
            movementRoi.read();
            
            if(movementRoi.array_size <= 0)
                continue;
            
            Pointer roiKeson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(roisKeson, roiKeson);
            
            KestrelApi.keson_add_item_to_object(roiKeson, "roi_id",  KestrelApi.keson_create_int(movementRoi.roi_id));
            
            Pointer targetsKeson = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(roiKeson, "targets", targetsKeson);
            
            for(int mndex = 0; mndex < movementRoi.array_size; mndex ++) {
                crowd_head_target_t target = new crowd_head_target_t(new Pointer(Pointer.nativeValue(movementRoi.head_target_list_array) + KestrelApi._SIZE_crowd_head_target_t * mndex));
                target.read();

                Pointer targetKeson = KestrelApi.keson_create_object();
                KestrelApi.keson_add_item_to_array(targetsKeson, targetKeson);
                
                KestrelApi.keson_add_item_to_object(targetKeson, "direct",  KestrelApi.keson_create_int((long)movementRoi.crowd_direct[mndex]));
                KestrelApi.keson_add_item_to_object(targetKeson, "x",  KestrelApi.keson_create_int((long)target.pt_position.x));
                KestrelApi.keson_add_item_to_object(targetKeson, "y",  KestrelApi.keson_create_int((long)target.pt_position.y));

                if(target.speed >= 0)
                    KestrelApi.keson_add_item_to_object(targetKeson, "speed",  KestrelApi.keson_create_int((long)target.speed));
            }
        }
        
        if(KestrelApi.keson_array_size(roisKeson) > 0)
            KestrelApi.keson_add_item_to_object(keson, type + "_rois", roisKeson);
        else
            KesonUtils.kesonDeepDelete(roisKeson);
    }
    
    private static final void extractCrowdQueueRoiResult(Pointer keson, crowd_queue_roi_result_list roi_queue_result_list, String type) {
        Pointer roisKeson = KestrelApi.keson_create_array();
        
        for(int kndex = 0; kndex < roi_queue_result_list.roi_cnt; kndex ++) {
            crowd_queue_roi_result queueRoi = new crowd_queue_roi_result(new Pointer(Pointer.nativeValue(roi_queue_result_list.roi_results) + KestrelApi._SIZE_crowd_queue_roi_result_t * kndex));
            queueRoi.read();
            
            if(queueRoi.all_targets.targets_num <= 0)
                continue;
            
            Pointer roiKeson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(roisKeson, roiKeson);
            
            KestrelApi.keson_add_item_to_object(roiKeson, "roi_id",  KestrelApi.keson_create_int(queueRoi.roi_id));
            
            Pointer targetsKeson = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(roiKeson, "targets", targetsKeson);
            
            for(int mndex = 0; mndex < queueRoi.all_targets.targets_num; mndex ++) {
                crowd_queue_target_t target = new crowd_queue_target_t(new Pointer(Pointer.nativeValue(queueRoi.all_targets.targets) + KestrelApi._SIZE_crowd_queue_target_t * mndex));
                target.read();

                Pointer targetKeson = KestrelApi.keson_create_object();
                KestrelApi.keson_add_item_to_array(targetsKeson, targetKeson);
                
                KestrelApi.keson_add_item_to_object(targetKeson, "x",  KestrelApi.keson_create_int((long)target.pt_position.x));
                KestrelApi.keson_add_item_to_object(targetKeson, "y",  KestrelApi.keson_create_int((long)target.pt_position.y));
            }
        }
        
        if(KestrelApi.keson_array_size(roisKeson) > 0)
            KestrelApi.keson_add_item_to_object(keson, type + "_rois", roisKeson);
        else
            KesonUtils.kesonDeepDelete(roisKeson);
    }
    
    private static final void extractCrowdCloseRoiResult(Pointer keson, crowd_close_roi_result_list roi_close_result_list, String type) {
        Pointer roisKeson = KestrelApi.keson_create_array();
        
        for(int kndex = 0; kndex < roi_close_result_list.roi_cnt; kndex ++) {
            crowd_close_roi_result closeRoi = new crowd_close_roi_result(new Pointer(Pointer.nativeValue(roi_close_result_list.roi_results) + KestrelApi._SIZE_crowd_close_roi_result_t * kndex));
            closeRoi.read();
            
            if(closeRoi.close_edge_list.edge_num <= 0)
                continue;
            
            Pointer roiKeson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(roisKeson, roiKeson);
            
            KestrelApi.keson_add_item_to_object(roiKeson, "roi_id",  KestrelApi.keson_create_int(closeRoi.roi_id));
            KestrelApi.keson_add_item_to_object(roiKeson, "alarm_type",  KestrelApi.keson_create_int(closeRoi.alarm_type));
            
            Pointer targetsKeson = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(roiKeson, "targets", targetsKeson);
            
            for(int mndex = 0; mndex < closeRoi.close_edge_list.edge_num; mndex ++) {
                int startIndex = closeRoi.close_edge_list.start_indexes.getInt(Native.SIZE_T_SIZE * mndex);
                crowd_head_target_t startTarget = new crowd_head_target_t(new Pointer(Pointer.nativeValue(closeRoi.all_targets.targets) + KestrelApi._SIZE_crowd_head_target_t * startIndex));
                startTarget.read();

                int endIndex = closeRoi.close_edge_list.end_indexes.getInt(Native.SIZE_T_SIZE * mndex);
                crowd_head_target_t endTarget = new crowd_head_target_t(new Pointer(Pointer.nativeValue(closeRoi.all_targets.targets) + KestrelApi._SIZE_crowd_head_target_t * endIndex));
                endTarget.read();

                Pointer targetKeson = KestrelApi.keson_create_object();
                KestrelApi.keson_add_item_to_array(targetsKeson, targetKeson);
                
                KestrelApi.keson_add_item_to_object(targetKeson, "startX",  KestrelApi.keson_create_int((long)startTarget.pt_position.x));
                KestrelApi.keson_add_item_to_object(targetKeson, "startY",  KestrelApi.keson_create_int((long)startTarget.pt_position.y));
                KestrelApi.keson_add_item_to_object(targetKeson, "endX",  KestrelApi.keson_create_int((long)endTarget.pt_position.x));
                KestrelApi.keson_add_item_to_object(targetKeson, "endY",  KestrelApi.keson_create_int((long)endTarget.pt_position.y));
            }
        }
        
        if(KestrelApi.keson_array_size(roisKeson) > 0)
            KestrelApi.keson_add_item_to_object(keson, type + "_rois", roisKeson);
        else
            KesonUtils.kesonDeepDelete(roisKeson);
    }
}