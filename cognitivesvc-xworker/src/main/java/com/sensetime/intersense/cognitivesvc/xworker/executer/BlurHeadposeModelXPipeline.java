package com.sensetime.intersense.cognitivesvc.xworker.executer;


import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;

import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

@Slf4j

public class BlurHeadposeModelXPipeline extends AbstractHandler<BlurHeadposeModelXPipeline.FaceBody> {


    public static final String stageOne = "video_face_track_stream";

    public static final String stageTwo = "video_face_analyze_stream";

    private static final String roi_filter =    "[]";

    public BlurHeadposeModelXPipeline() {
        super(true);
        long contextId = Utils.keyToContextId("face");
        BlurHeadposeModelPipelineConfig config = BlurHeadposeModelPipelineConfig.builder()
                .scpPipelineBoolean(Utils.instance.scgFacePipeline)
                .contextId(contextId)
                .quality_thresh(0.3f)
                .quick_response_time(-1)
                .time_interval(-1)
                .max_track_time(120)
                .max_tracklet_num(16)
                .duplicate_targets_time(Utils.instance.duplicateTargetsTime)
                .small_quality_thresh(Objects.requireNonNullElse(Utils.instance.smallDetectThresh, 0.3f))
                .large_quality_thresh(Objects.requireNonNullElse(Utils.instance.largeDetectThresh, 0.4f))
                .pageant_quality_thresh(Objects.requireNonNullElse(Utils.instance.selectFrameThresh, 0.2f))
                .quality_step_thresh(Objects.requireNonNullElse(Utils.instance.qualityStepThresh, 0.01f))
                .build();

        String faceConfig = config.toString();
        //log.info("face_flock_pipeline_create_new : " + faceConfig);

        this.handlerEntity = HandlerEntity.builder().build();
        this.pointers = new ModelHolder[]{
                new ModelHolder(stageOne, faceConfig, 32,1, true, true, false),
                new ModelHolder(stageTwo, faceConfig, 32,1, true, true, false)
        };
    }

    @SuppressWarnings("unchecked")
    @Override
    protected FaceBody[] readModelResult(ModelResult modelResult) {
        PointerByReference keson = modelResult.getResult();
        if (keson == null || keson.getValue() == null)
            return new FaceBody[0];

        log.info("keson{}", KesonUtils.kesonToJson(keson));

        Pointer targets = KestrelApi.keson_get_object_item(keson.getValue(), "targets");
        int arr_size = KestrelApi.keson_array_size(targets);

        FaceBody[] result = new FaceBody[arr_size];
        for(int index = 0 ; index < arr_size; index ++) {
            result[index] = new FaceBody();
            Pointer target = KestrelApi.keson_get_array_item(targets, index);
            //UtilsReader.readHunterData(target, result[index]);

            result[index].setQuality(KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "quality")));
            result[index].setIntegrateQuality(KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "integrate_quality")));
            result[index].setConfidence(KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "confidence")));
            result[index].setAlignerConfidence(KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "aligner_confidence")));
//            result[index].setFeature(UtilsReader.readFeatureData(target));
//            result[index].setAttribute(UtilsReader.readAttributeData(target));
        }

        return result;
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FaceBody extends Detection {
        /**
         * 标签
         */
        private int label;

        /**
         * 匹配id
         */
        private Integer matchedId;
        /**
         * 匹配实体
         */
        private FaceBody matchedFaceBody;

        /** 特征 */
        private float[] feature;
        /** 属性 */
        private Map<String, Object> attribute;

        private double quality;

        private double confidence;

        private double alignerConfidence;

        private double integrateQuality;
    }

    @Data
    @Accessors(chain = true)
    @Builder
    public static class BlurHeadposeModelPipelineConfig {


        public Long contextId;

        @Builder.Default
        public int video_width = 1920;

        @Builder.Default
        public int video_height = 1080;

        @Builder.Default
        public float expand_ratio = 1.5f;

        @Builder.Default
        public int max_tracklet_num = 16;

        @Builder.Default
        public int max_tracklet_item_size = 3;

        @Builder.Default
        public int duplicate_targets_time = 1;

        @Builder.Default
        public float quality_thresh = 0.3f;

        @Builder.Default
        public float large_quality_thresh = 0.4f;

        @Builder.Default
        public float small_quality_thresh = 0.2f;

        @Builder.Default
        public float pageant_quality_thresh = 0.2f;

        @Builder.Default
        public float quality_step_thresh = 0.01f;

        @Builder.Default
        public float ingrate_quality_thresh = 0.2f;

        @Builder.Default
        public int quick_response_time = -1;

        @Builder.Default
        public String roi_filter = "[]";

        @Builder.Default
        public int time_interval = -1;

        @Builder.Default
        public int max_track_time = -1;

        public boolean scpPipelineBoolean;

        private String contextIdString() {
            if (contextId == null)
                return "";

            return "\"source_id\": " + contextId + ", \"context_id\": " + contextId + ",";
        }

        @Override
        public String toString() {
            if(scpPipelineBoolean){
                return faceScgPipelineConfig();
            } else {
                return faceStdConfig();
            }
        }

        private String faceScgPipelineConfig() {
            return "{" +
                    "    \"streams\": [" +
                    "        {" +
                    "            \"name\": \"" + stageOne + "\"," + contextIdString() +
                    "            \"module_plugins\": [" +
                    "                \"detection.fmd\"," +
                    "                \"multiple_target_tracking.fmd\"," +
                    "                \"plugin.fmd\"," +
                    "                \"mergence.fmd\"" +
                    "            ]," +
                    "            \"modules\": [" +
                    "                {" +
                    "                    \"name\": \"input\"," + contextIdString() +
                    "                    \"type\": \"Input\"," +
                    "                    \"inputs\": []," +
                    "                    \"outputs\": [\"images\"]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_detector\"," + contextIdString() +
                    "                    \"type\": \"Detection\"," +
                    "                    \"parallel_group\": \"detect\"," +
                    "                    \"inputs\": [\"images\"]," +
                    "                    \"outputs\": [\"detected_faces\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"hunter\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module_scg") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"confidence_threshold\": " + small_quality_thresh  + "," +
                    "                        \"model_key\": \"small_face_detection\"" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_tracker\"," + contextIdString() +
                    "                    \"type\": \"MultipleTargetTracking\"," +
                    "                    \"parallel_group\": \"target_tracking\"," +
                    "                    \"inputs\": [" +
                    "                        \"detected_faces\"" +
                    "                    ]," +
                    "                    \"outputs\": [" +
                    "                        \"tracked_faces\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"output\"," + contextIdString() +
                    "                    \"type\": \"Output\"," +
                    "                    \"parallel_group\": \"output\"," +
                    "                    \"inputs\": [" +
                    "                        \"tracked_faces\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]," +
                    "                    \"outputs\": []" +
                    "                }" +
                    "            ]" +
                    "        }," +
                    "        {" +
                    "            \"name\": \"" + stageTwo + "\"," + contextIdString() +
                    "            \"module_plugins\": [" +
                    "                \"plugin.fmd\"," +
                    "                \"mergence.fmd\"," +
                    "                \"target_selection_287.fmd\"," +
                    "                \"refinement.fmd\"," +
                    "                \"roi_filter.fmd\"," +
                    "                \"operation.fmd\"," +
                    "                \"face_quality.fmd\"," +
                    "                \"face_quality_filter.fmd\"," +
                    "                \"concat.fmd\"," +
                    "                \"lightweight_targets_slice.fmd\"," +
                    "                \"multidim_face_quality.fmd\"," +
                    "                \"binary_classification_filter.fmd\"" +
                    "            ]," +
                    "            \"modules\": [" +
                    "                {" +
                    "                    \"name\": \"input\"," + contextIdString() +
                    "                    \"type\": \"Input\"," +
                    "                    \"inputs\": []," +
                    "                    \"outputs\": [" +
                    "                        \"tracked_faces\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_quality_calculate\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"face_quality\"," +
                    "                    \"inputs\": [\"tracked_faces\"]," +
                    "                    \"outputs\": [\"face_quality\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"pageant\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("pageant_module_scg") + "\"," +
                    "                        \"max_batch_size\": 16," +
                    "                        \"model_key\": \"face_quality\"" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_merge\"," + contextIdString() +
                    "                    \"type\": \"Mergence\"," +
                    "                    \"parallel_group\": \"face_quality\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_quality\"," +
                    "                        \"tracked_faces\"" +
                    "                    ]," +
                    "                    \"outputs\": [" +
                    "                        \"face_targets_with_quality\"" +
                    "                    ]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"roi_filter\"," + contextIdString() +
                    "                    \"type\": \"RoiFilter\"," +
                    "                    \"parallel_group\": \"target_select\"," +
                    "                    \"inputs\": [\"face_targets_with_quality\"]," +
                    "                    \"outputs\": [\"selected_faces\"]," +
                    "                    \"config\": {" +
                    "                        \"roi_filter\": [" +
                    "                            {" +
                    "                                \"label_id\": 37017," +
                    "                                \"polygons\": " + roi_filter  +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +


                    "                {" +
                    "                    \"name\": \"analyzed_lightweight_slice\"," + contextIdString() +
                    "                    \"type\": \"LightweightTargetsSlice\"," +
                    "                    \"parallel_group\": \"target_select\"," +
                    "                    \"inputs\": [\"selected_faces\"]," +
                    "                    \"outputs\": [" +
                    "                        \"selected_faces\"," +
                    "                        \"selected_face_tracklets_lightweight\"" +
                    "                    ]," +
                    "                    \"config\": {" +
                    "                        \"target_label_configs\": [" +
                    "                            {" +
                    "                                \"label_id\": 37017," +
                    "                               \"min_width\": 20," +
                    "                               \"min_height\": 20" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_classifier\"," + contextIdString() +
                    "                    \"type\": \"BinaryClassificationFilter\"," +
                    "                    \"parallel_group\": \"classifier_filter_thread\"," +
                    "                    \"inputs\": [\"selected_faces\"]," +
                    "                    \"outputs\": [\"selected_faces\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"classifier\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("face_classifier_module_scg") + "\"," +
                    "                        \"max_batch_size\": 128," +
                    "                        \"support_label\": 37017," +
                    "                        \"enable\": true," +
                    "                        \"confidence_threshold\": 0.7," +
                    "                        \"model_key\": \"face_binary_classifier\"" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_classifier_filtered_lightweight_slice\"," + contextIdString() +
                    "                    \"type\": \"LightweightTargetsSlice\"," +
                    "                    \"parallel_group\": \"classifier_filter_thread\"," +
                    "                    \"inputs\": [" +
                    "                        \"selected_faces\"" +
                    "                    ]," +
                    "                    \"outputs\": [" +
                    "                        \"selected_faces\"," +
                    "                        \"face_classifier_filtered_targets_lightweight\"" +
                    "                    ]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"aligner\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"selected_faces\"]," +
                    "                    \"outputs\": [\"face_landmarks\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"aligner\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module_scg") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"model_key\": \"face_aligner_with_occlusion\"" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_landmarks_pack\"," + contextIdString() +
                    "                    \"type\": \"Operation\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"face_landmarks\"]," +
                    "                    \"outputs\": [\"face_landmarks\"]," +
                    "                    \"config\": {" +
                    "                        \"operations\": [" +
                    "                            {" +
                    "                                \"cmd\": \"move\"," +
                    "                                \"args\": [" +
                    "                                    \"confidence\"," +
                    "                                    \"aligner_confidence\"" +
                    "                                ]" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_headpose\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"face_landmarks\"]," +
                    "                    \"outputs\": [\"face_headpose\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"headpose\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"model_key\": \"face_headpose\"" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
                    "                    \"type\": \"Mergence\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_landmarks\"," +
                    "                        \"face_headpose\"," +
                    "                        \"selected_faces\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"face_targets_quality_element\"]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_quality_update\"," + contextIdString() +
                    "                    \"type\": \"FaceQuality\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"face_targets_quality_element\"]," +
                    "                    \"outputs\": [\"new_face_tracklets\"]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_multidim_quality\"," + contextIdString() +
                    "                    \"type\": \"MultidimFaceQuality\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"new_face_tracklets\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"new_face_tracklets\"]," +
                    "                    \"config\": {" +
                    "                        \"quality_model\": {" +
                    "                            \"plugin\": \"classifier\"," +
                    "                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_quality_module") + "\"," +
                    "                            \"max_batch_size\": 128," +
                    "                            \"model_key\": \"multi_face_quality\"" +
                    "                         }," +

                    "                        \"clear_model\": {" +
                    "                            \"plugin\": \"classifier\"," +
                    "                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_blur_module_scg") + "\"," +
                    "                            \"max_batch_size\": 128," +
                    "                            \"model_key\": \"multi_face_quality_blur\"" +
                    "                         }," +
                    "                         \"clear_score_threshold\": "       + 0 + "," +
                    "                         \"angle_score_threshold\": "       + 0 + "," +
                    "                         \"yaw_angle_threshold\": "         + 180 + "," +
                    "                         \"pitch_angle_threshold\": "       + 180 + "," +
                    "                         \"visible_score_threshold\": "     + 0 + "," +
                    "                         \"shine_score_threshold\": "       + 0 +

                    "                     }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_multidim_quality_filtered_lightweight_slice\"," + contextIdString() +
                    "                    \"type\": \"LightweightTargetsSlice\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [\"new_face_tracklets\"]," +
                    "                    \"outputs\": [" +
                    "                        \"new_face_tracklets\"," +
                    "                        \"face_multidim_quality_filtered_targets_lightweight\"" +
                    "                    ]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_quality_filter\"," + contextIdString() +
                    "                    \"type\": \"FaceQualityFilter\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"new_face_tracklets\"]," +
                    "                    \"outputs\": [\"selected_faces_with_landmarks\"]," +
                    "                    \"config\": {\"quality_threshold\": " + ingrate_quality_thresh + "}" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_quality_filtered_lightweight_slice\"," + contextIdString() +
                    "                    \"type\": \"LightweightTargetsSlice\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [\"selected_faces_with_landmarks\"]," +
                    "                    \"outputs\": [" +
                    "                        \"face_quality_filtered_target_tracklets_normal\"," +
                    "                        \"face_quality_filtered_target_tracklets_lightweight\"" +
                    "                    ]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_feature_extraction\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [\"face_quality_filtered_target_tracklets_normal\"]," +
                    "                    \"outputs\": [\"face_features\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"feature\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_face_module_scg") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"model_key\": \"face_feature\"" +
                    "                     }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_feature_mergence\"," + contextIdString() +
                    "                    \"type\": \"Mergence\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_features\"," +
                    "                        \"face_quality_filtered_target_tracklets_normal\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"face_tracklets_with_feature\"]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"faces_refinement\"," + contextIdString() +
                    "                    \"type\": \"Refinement\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_tracklets_with_feature\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"best_faces\"]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_attribute_extraciton\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [\"best_faces\"]," +
                    "                    \"outputs\": [\"face_attributes\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"attribute\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"filter\": true," +
                    "                        \"model_key\": \"face_attribute\"" +
                    "                     }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_targets_mergence\"," + contextIdString() +
                    "                    \"type\": \"Mergence\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_attributes\"," +
                    "                        \"best_faces\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"analyzed_face_targets\"]" +
                    "                }," +


                    "                {" +
                    "                    \"name\": \"lightweight_targets_concat\"," + contextIdString() +
                    "                    \"type\": \"Concat\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"selected_face_tracklets_lightweight\"," +
                    "                        \"face_classifier_filtered_targets_lightweight\"," +
                    "                        \"face_multidim_quality_filtered_targets_lightweight\"," +
                    "                        \"face_quality_filtered_target_tracklets_lightweight\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"lightweight_targets\"]," +
                    "                    \"config\": {" +
                    "                        \"concat_items\": [" +
                    "                          \"targets\"" +
                    "                        ]" +
                    "                     }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"output\"," + contextIdString() +
                    "                    \"type\": \"Output\"," +
                    "                    \"parallel_group\": \"output\"," +
                    "                    \"inputs\": [" +
                    "                        \"analyzed_face_targets\"," +
                    "                        \"lightweight_targets\"" +
                    "                    ]," +
                    "                    \"outputs\": []" +
                    "                }" +
                    "            ]" +
                    "        }" +
                    "    ]" +
                    "}" +
                    "";
        }

        public String faceStdConfig() {
            return "{" +
                    "    \"streams\": [" +
                    "        {" +
                    "            \"name\": \"" + stageOne + "\"," + contextIdString() +
                    "            \"module_plugins\": [" +
                    "                \"detection.fmd\"," +
                    "                \"multiple_target_tracking.fmd\"," +
                    "                \"plugin.fmd\"," +
                    "                \"roi_expand.fmd\"," +
                    "                \"operation.fmd\"," +
                    "                \"mergence.fmd\"," +
                    "                \"track_recall.fmd\"" +
                    "            ]," +
                    "            \"modules\": [" +
                    "                {" +
                    "                    \"name\": \"input\"," + contextIdString() +
                    "                    \"type\": \"Input\"," +
                    "                    \"parallel_group\": \"input\"," +
                    "                    \"inputs\": []," +
                    "                    \"outputs\": [\"images\"]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_detector\"," + contextIdString() +
                    "                    \"type\": \"Detection\"," +
                    "                    \"parallel_group\": \"detect\"," +
                    "                    \"inputs\": [\"images\"]," +
                    "                    \"outputs\": [\"detected_faces_small\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"hunter\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module") + "\"," +
                    "                        \"max_batch_size\": 48," +
                    "                        \"confidence_threshold\": " + small_quality_thresh  + "," +
                    "                        \"model_key\": \"small_face_detection\"" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_tracker\"," + contextIdString() +
                    "                    \"type\": \"MultipleTargetTracking\"," +
                    "                    \"parallel_group\": \"target_tracking\"," +
                    "                    \"inputs\": [" +
                    "                        \"detected_faces_small\"" +
                    "                    ]," +
                    "                    \"outputs\": [" +
                    "                        \"tracked_faces_small\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"roi_expander\"," + contextIdString() +
                    "                    \"type\": \"RoiExpand\"," +
                    "                    \"parallel_group\": \"detect\"," +
                    "                    \"inputs\": [\"tracked_faces_small\"]," +
                    "                    \"outputs\": [\"tracked_faces_expanded\"]," +
                    "                    \"config\": {" +
                    "                        \"roi_expand_ratio\": 1" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_detector_large\"," + contextIdString() +
                    "                    \"type\": \"DetectionInROI\"," +
                    "                    \"parallel_group\": \"detect\"," +
                    "                    \"inputs\": [\"tracked_faces_expanded\"]," +
                    "                    \"outputs\": [\"tracked_faces_filtered\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"hunter\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_large_module") + "\"," +
                    "                        \"max_batch_size\": 48," +
                    "                        \"confidence_threshold\": " + large_quality_thresh +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"track_recall\"," + contextIdString() +
                    "                    \"type\": \"TrackRecall\"," +
                    "                    \"parallel_group\": \"detect\"," +
                    "                    \"inputs\": [\"tracked_faces_filtered\"]," +
                    "                    \"outputs\": [\"tracked_faces\"]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"confidence_to_quality\"," + contextIdString() +
                    "                    \"type\": \"Operation\"," +
                    "                    \"parallel_group\": \"target_tracking\"," +
                    "                    \"inputs\": [\"tracked_faces\"]," +
                    "                    \"outputs\": [\"tracked_faces\"]," +
                    "                    \"config\": {" +
                    "                        \"operations\": [" +
                    "                            {" +
                    "                                \"cmd\": \"copy\"," +
                    "                                \"args\": [" +
                    "                                    \"confidence\"," +
                    "                                    \"quality\"" +
                    "                                ]" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"aligner\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"tracked_faces\"]," +
                    "                    \"outputs\": [\"face_landmarks\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"aligner\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"model_key\": \"face_aligner_with_occlusion\"" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_headpose\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"face_landmarks\"]," +
                    "                    \"outputs\": [\"face_headpose\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"headpose\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"model_key\": \"face_headpose\"" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
                    "                    \"type\": \"Mergence\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [" +
                    "                        \"tracked_faces\"," +
                    "                        \"face_headpose\"" +
                    "                    ]," +
                    "                    \"outputs\": [" +
                    "                        \"tracked_faces_headpose\"" +
                    "                    ]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"output\"," + contextIdString() +
                    "                    \"type\": \"Output\"," +
                    "                    \"parallel_group\": \"output\"," +
                    "                    \"inputs\": [" +
                    "                        \"tracked_faces_headpose\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]," +
                    "                    \"outputs\": []" +
                    "                }" +
                    "            ]" +
                    "        }," +
                    "        {" +
                    "            \"name\": \"" + stageTwo + "\"," + contextIdString() +
                    "            \"module_plugins\": [" +
                    "                \"plugin.fmd\"," +
                    "                \"mergence.fmd\"," +
                    "                \"target_selection.fmd\"," +
                    "                \"refinement.fmd\"," +
                    "                \"roi_filter.fmd\"," +
                    "                \"operation.fmd\"," +
                    "                \"face_quality.fmd\"," +
                    "                \"face_quality_filter.fmd\"," +
                    "                \"duplicated_targets_filter.fmd\"," +
                    "                \"concat.fmd\"," +
                    "                \"lightweight_targets_slice.fmd\"," +
                    "                \"face_quality_evaluator.fmd\"," +
                    "                \"multidim_face_quality.fmd\"" +
                    "            ]," +
                    "            \"modules\": [" +
                    "                {" +
                    "                    \"name\": \"input\"," + contextIdString() +
                    "                    \"type\": \"Input\"," +
                    "                    \"parallel_group\": \"input\"," +
                    "                    \"inputs\": []," +
                    "                    \"outputs\": [" +
                    "                        \"face_targets_with_quality\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"roi_filter\"," + contextIdString() +
                    "                    \"type\": \"RoiFilter\"," +
                    "                    \"parallel_group\": \"target_select\"," +
                    "                    \"inputs\": [\"face_targets_with_quality\"]," +
                    "                    \"outputs\": [\"filtered_face_targets\"]," +
                    "                    \"config\": {" +
                    "                        \"roi_filter\": [" +
                    "                            {" +
                    "                                \"label_id\": 37017," +
                    "                                \"polygons\": " + roi_filter  +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"face_selector\"," + contextIdString() +
                    "                    \"type\": \"TargetSelection\"," +
                    "                    \"parallel_group\": \"target_select\"," +
                    "                    \"inputs\": [" +
                    "                        \"filtered_face_targets\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"selected_faces\"]," +
                    "                    \"config\": {" +
                    "                        \"max_device_memory_usage_per_source\": 128," +
                    "                        \"max_roi_ref_frame_size\": 512," +
                    "                        \"max_source_tracklet_num\": " + max_tracklet_num + "," +
                    "                        \"keep_low_quality_target\": true," +
                    "                        \"selection\": [" +
                    "                            {" +
                    "                                \"label_id\": "               + "37017" + "," +
                    "                                \"quick_response_time\": "    + quick_response_time + "," +
                    "                                \"time_interval\": "          + time_interval + "," +
                    "                                \"max_track_time\": "         + max_track_time + "," +
                    "                                \"roi_expand_ratio\": "       + expand_ratio + "," +
                    "                                \"quality_threshold\": "      + pageant_quality_thresh + "," +
                    "                                \"max_tracklet_num\": "       + 512 + "," +
                    "                                \"max_tracklet_item_size\": " + max_tracklet_item_size +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"aligner\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"selected_faces\"]," +
                    "                    \"outputs\": [\"face_landmarks\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"aligner\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"model_key\": \"face_aligner_with_occlusion\"" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_landmarks_pack\"," + contextIdString() +
                    "                    \"type\": \"Operation\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"face_landmarks\"]," +
                    "                    \"outputs\": [\"face_landmarks\"]," +
                    "                    \"config\": {" +
                    "                        \"operations\": [" +
                    "                            {" +
                    "                                \"cmd\": \"move\"," +
                    "                                \"args\": [" +
                    "                                    \"confidence\"," +
                    "                                    \"aligner_confidence\"" +
                    "                                ]" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_headpose\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"face_landmarks\"]," +
                    "                    \"outputs\": [\"face_headpose\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"headpose\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," +
                    "                        \"max_batch_size\": 16," +
                    "                        \"model_key\": \"face_headpose\"" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
                    "                    \"type\": \"Mergence\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_landmarks\"," +
                    "                        \"face_headpose\"," +
                    "                        \"selected_faces\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"face_targets_quality_element\"]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_quality_update\"," + contextIdString() +
                    "                    \"type\": \"FaceQuality\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"face_targets_quality_element\"]," +
                    "                    \"outputs\": [\"new_face_tracklets\"]" +
                    "                }," +

                    "                {" +
                    "                    \"name\": \"quality_transfer\"," + contextIdString() +
                    "                    \"type\": \"Operation\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"new_face_tracklets\"]," +
                    "                    \"outputs\": [\"new_face_tracklets\"]," +
                    "                    \"config\": {" +
                    "                        \"operations\": [" +
                    "                            {" +
                    "                                \"cmd\": \"copy\"," +
                    "                                \"args\": [" +
                    "                                    \"quality\"," +
                    "                                    \"pageant_quality\"" +
                    "                                ]" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_quality_filter\"," + contextIdString() +
                    "                    \"type\": \"FaceQualityFilter\"," +
                    "                    \"parallel_group\": \"face_landmarks\"," +
                    "                    \"inputs\": [\"new_face_tracklets\"]," +
                    "                    \"outputs\": [\"selected_faces_with_landmarks\"]," +
                    "                    \"config\": {\"quality_threshold\": " + ingrate_quality_thresh + "}" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_quality_filtered_lightweight_slice\"," + contextIdString() +
                    "                    \"type\": \"LightweightTargetsSlice\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [\"selected_faces_with_landmarks\"]," +
                    "                    \"outputs\": [" +
                    "                        \"face_quality_filtered_target_tracklets_normal\"," +
                    "                        \"face_quality_filtered_target_tracklets_lightweight\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_feature_extraction\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [\"face_quality_filtered_target_tracklets_normal\"]," +
                    "                    \"outputs\": [\"face_features\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"feature\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_face_module_scg") + "\"," +
                    "                        \"max_batch_size\": 32," +
                    "                        \"model_key\": \"face_feature\"" +
                    "                     }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_feature_mergence\"," + contextIdString() +
                    "                    \"type\": \"Mergence\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_features\"," +
                    "                        \"face_quality_filtered_target_tracklets_normal\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"face_tracklets_with_feature\"]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"faces_refinement\"," + contextIdString() +
                    "                    \"type\": \"Refinement\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_tracklets_with_feature\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"best_faces\"]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_attribute_extraciton\"," + contextIdString() +
                    "                    \"type\": \"Plugin\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [\"best_faces\"]," +
                    "                    \"outputs\": [\"face_attributes\"]," +
                    "                    \"config\": {" +
                    "                        \"plugin\": \"attribute\"," +
                    "                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," +
                    "                        \"max_batch_size\": 64," +
                    "                        \"filter\": false," +
                    "                        \"model_key\": \"face_attribute\"" +
                    "                     }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_targets_mergence\"," + contextIdString() +
                    "                    \"type\": \"Mergence\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_attributes\"," +
                    "                        \"best_faces\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"face_targets_with_attr_feature\"]" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"analyzed_lightweight_slice\"," + contextIdString() +
                    "                    \"type\": \"LightweightTargetsSlice\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [\"face_targets_with_attr_feature\"]," +
                    "                    \"outputs\": [" +
                    "                        \"analyzed_face_targets\"," +
                    "                        \"analyzed_face_targets_lightweight\"" +
                    "                    ]," +
                    "                    \"config\": {" +
                    "                        \"target_label_configs\": [" +
                    "                            {" +
                    "                                \"cmd\": 37017," +
                    "                               \"min_width\": 10," +
                    "                               \"min_height\": 10" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"face_multidim_quality\"," + contextIdString() +
                    "                    \"type\": \"MultidimFaceQuality\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"analyzed_face_targets\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"analyzed_face_targets\"]," +
                    "                    \"config\": {" +
                    "                        \"quality_model\": {" +
                    "                            \"plugin\": \"classifier\"," +
                    "                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_quality_module") + "\"," +
                    "                            \"max_batch_size\": 128," +
                    "                            \"model_key\": \"face_multidim_quality\"" +
                    "                         }" +
                    "                     }" +
                    "                }," +


                    "                {" +
                    "                    \"name\": \"lightweight_targets_concat\"," + contextIdString() +
                    "                    \"type\": \"Concat\"," +
                    "                    \"parallel_group\": \"target_analysis\"," +
                    "                    \"inputs\": [" +
                    "                        \"face_quality_filtered_target_tracklets_lightweight\"," +
                    "                        \"analyzed_face_targets_lightweight\"" +
                    "                    ]," +
                    "                    \"outputs\": [\"lightweight_targets\"]," +
                    "                    \"config\": {" +
                    "                        \"concat_items\": [" +
                    "                          \"targets\"" +
                    "                        ]" +
                    "                     }" +
                    "                }," +
                    "                {" +
                    "                    \"name\": \"output\"," + contextIdString() +
                    "                    \"type\": \"Output\"," +
                    "                    \"parallel_group\": \"output\"," +
                    "                    \"inputs\": [" +
                    "                        \"analyzed_face_targets\"," +
                    "                        \"lightweight_targets\"" +
                    "                    ]," +
                    "                    \"outputs\": []" +
                    "                }" +
                    "            ]" +
                    "        }" +
                    "    ]" +
                    "}" +
                    "";
        }
    }
}
