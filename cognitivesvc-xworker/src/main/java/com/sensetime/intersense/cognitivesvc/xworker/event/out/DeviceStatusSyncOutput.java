package com.sensetime.intersense.cognitivesvc.xworker.event.out;

import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.http.MediaType;
import org.springframework.messaging.Message;

@Slf4j
public class DeviceStatusSyncOutput implements BaseOutput {
    private final StreamBridge streamBridgeTemplate;

    String SENSEYEX_RAW_EVENT_OUTPUT = "device_status_event_output-out-0";
    public DeviceStatusSyncOutput(StreamBridge streamBridgeTemplate) {
        this.streamBridgeTemplate = streamBridgeTemplate;
    }

    @Override
    public boolean send(Message<?> message, long timeout) {
        if (streamBridgeTemplate != null) {
            boolean send = streamBridgeTemplate.send(SENSEYEX_RAW_EVENT_OUTPUT,
                    message, MediaType.TEXT_PLAIN);
            if (send) {
                log.debug("senseyex_raw_event_output msg send success ");
                return true;
            }
        }
        return false;
    }
}
