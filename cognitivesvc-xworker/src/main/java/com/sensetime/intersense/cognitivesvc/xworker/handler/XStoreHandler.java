package com.sensetime.intersense.cognitivesvc.xworker.handler;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.DeviceInfoEntity;
import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import jakarta.annotation.PostConstruct;
import javax.tools.JavaCompiler;
import javax.tools.JavaFileObject;
import javax.tools.StandardJavaFileManager;
import javax.tools.ToolProvider;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.springframework.http.HttpEntity;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamXswitcher;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.ModelHandlerEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawVideo;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamXswitcherRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoStatus;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;

import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.ConfigAccessor;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XModelHandler.KestrelInterceptor;
import com.sensetime.intersense.cognitivesvc.xworker.zutils.VideoStreamXWorker;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sun.jna.Pointer;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/** 通过dispatcher同步过来心跳，实时加载模型和流
 */
@Component
@EnableScheduling
@Slf4j
public class XStoreHandler {

	@Autowired
	BaseOutput device_status_event_output;

	@Autowired
	BaseOutput senseyex_raw_event_output;
	
	private final Semaphore modelSemaphore = new Semaphore(0);
	
	private final Semaphore streamSemaphore = new Semaphore(0);
	
	private final Semaphore tempStreamSemaphore = new Semaphore(0);
	
	/** 模型名*/
	@Getter
	private volatile Set<String> ongoingModels = new HashSet<String>();
	
    /** 正在运行中的动态模型*/
	@Getter
	private volatile Map<String, XModelHandler> xDynamicHandlerMap = new HashMap<String, XModelHandler>();
	
	/** 
	 * <deviceId, <annotatorName, Type>>
	 * */
	@Getter
	private volatile Map<String, Map<String, Integer>> highTypeMap = new HashMap<String, Map<String, Integer>>();
	
	/** switcher 表中的所有高频数据
	 * <deviceId, <annotatorName, Processor>>
	 * */
	@Getter
	private volatile Map<String, Map<String, Processor>> highProcessorMap = new HashMap<String, Map<String, Processor>>();
	
	/** switcher 表中的所有低频数据
	 * <deviceId, <annotatorName, Processor>>
	 * */
	@Getter
	private volatile Map<String, Map<String, Processor>> lowProcessorMap = new HashMap<String, Map<String, Processor>>();
	
	/** <deviceId, List<annotator>>*/
	private volatile Map<String, List<String>> ongoingStream = new HashMap<String, List<String>>();
	
	/** <deviceId>*/
	@Getter
	private volatile List<String> switcherOnWorkers = new ArrayList<String>();
	
	/** <deviceId, List<annotator>>*/
	private final ConcurrentHashMap<String, Pair<VideoStreamInfra, List<Processor>>> temporaryStream = new ConcurrentHashMap<String, Pair<VideoStreamInfra, List<Processor>>>();
	
	/** <deviceId, stream>*/
	private final Map<String, Pair<VideoStreamInfra, VideoStreamXWorker>> temporaryVideoStreamMap = new HashMap<String, Pair<VideoStreamInfra, VideoStreamXWorker>>();
	
	/** <deviceId, stream>*/
	private final Map<String, VideoStreamXWorker> ongoingVideoStreamMap = new HashMap<String, VideoStreamXWorker>();
	
	@Autowired
	private XDynamicModelRepository dynamicMapper;
	
	@Autowired
	private VideoStreamInfraRepository infraMapper;

	@Autowired
	private XDynamicModelRepository xDynamicModelRepository;
	
	@Autowired
	private VideoStreamXswitcherRepository switcherMapper;
	
	@Autowired
	private ApplicationContext applicationContext;
	
	@Autowired
	private ExecutorService cogThreadPool;
	
	@Autowired
	private DiscoveryClient discoveryClient;

	/** 调度器发过来的同步信息*/
	public void setOngoingModel(List<String> ongoingModels) {
		this.ongoingModels = new HashSet<String>(ongoingModels);
		
		if(modelSemaphore.availablePermits() < 1) 
			modelSemaphore.release();
	}

	public void setOngoingStream(Map<String, List<String>> ongoingStream) {
		this.ongoingStream = ongoingStream;
		
		if(streamSemaphore.availablePermits() < 1) 
			streamSemaphore.release();
	}

	public void setSwitcherOnWorker(List<String> switcherOnWorkers) {
		this.switcherOnWorkers = switcherOnWorkers;
		
		if(streamSemaphore.availablePermits() < 1) 
			streamSemaphore.release();
	}
	
	/** <annotator, List<deviceId>*/
	public Map<String, List<String>> getProcessorDeviceMap() {
		return ongoingStream.entrySet().stream()
					 .map(entry -> entry.getValue().stream()
										.map(p -> new ImmutablePair<String, String>(entry.getKey(), p))
										.collect(Collectors.toList()))
					 .flatMap(List::stream)
					 .collect(Collectors.toMap(
								pair -> pair.getRight(), 
								pair -> Lists.newArrayList(pair.getLeft()),
								(l, r) -> (List<String>)ListUtils.union(l, r)
					 ));
	}
	
	/** <annotator, List<deviceId>*/
	public Map<String, List<String>> getTemporaryDeviceMap() {
		return temporaryStream.entrySet().stream()
					 .map(entry -> entry.getValue().getRight().stream()
								.map(p -> new ImmutablePair<String, String>(entry.getKey(), p.getProcessor()))
								.collect(Collectors.toList()))
					 .flatMap(List::stream)
					 .collect(Collectors.toMap(
								pair -> pair.getRight(), 
								pair -> Lists.newArrayList(pair.getLeft()),
								(l, r) -> (List<String>)ListUtils.union(l, r)
					 ));
	}

	public void executeTemporaryStream(SenseyexRawVideo message){
		if(temporaryStream.containsKey(message.getDeviceId()))
			throw new RuntimeException("video[" + message.getDeviceId() + "] is running.");
		
		Map<String, Processor> processorMap = new HashMap<String, Processor>(highProcessorMap.getOrDefault(message.getDeviceId(), Map.of()));
		processorMap.putAll(lowProcessorMap.getOrDefault(message.getDeviceId(), Map.of()));
		
		List<Processor> processors = processorMap.isEmpty()
				? Arrays.stream(message.getProcessors())
						.map(p -> {
							try {
								Object processorObj = Objects.requireNonNullElse(message.getExtra(), Map.of()).get(p);
								return JSON.parseObject(JSON.toJSONString(processorObj), Processor.class);
							}catch(Exception e) {
								e.printStackTrace();
								return null;
							}
						})
						.filter(Objects::nonNull)
						.collect(Collectors.toList())
				: List.copyOf(processorMap.values());
		
		if(processors.isEmpty())
			return ;
		try{
			VideoFrame frame = FrameUtils.fetch_up_down_load_frame_path(message.getVideo());

			VideoStreamInfra.VideoStreamInfraBuilder builder = VideoStreamInfra.builder()
					.deviceId(message.getDeviceId())
					.rtspSource(message.getVideo())
					.videoRate(25)
					.rtmpDestination(message.getRtmpDestination())
					.rtmpOn(message.getRtmpOn())
					.rtspHeight(KestrelApi.kestrel_frame_video_height(frame.getFrame()))
					.rtspWidth(KestrelApi.kestrel_frame_video_width(frame.getFrame()));

			FrameUtils.batch_free_frame(frame);

			if(MapUtils.isNotEmpty(message.getExtra()))
				builder.frameMax((Integer)message.getExtra().get("frameMax"));

			VideoStreamInfra infra = builder.build();
			synchronized(infra) {
				temporaryStream.put(infra.getDeviceId(), new MutablePair<VideoStreamInfra, List<Processor>>(infra, processors));

				if(tempStreamSemaphore.availablePermits() < 1)
					tempStreamSemaphore.release();

				try { infra.wait(); } catch (InterruptedException e) { }
			}
		}catch (Exception e){
			e.printStackTrace();
			log.error("executeTemporaryStream exception:{}", e.getMessage());
		}
	}
	
	@SuppressWarnings("unchecked")
	@Scheduled(fixedDelay = 2000)
	synchronized void heartBeatModel(){
		if(!modelSemaphore.tryAcquire())
			return ;
		
		Initializer.bindDeviceOrNot();
		
		Map<String, XDynamicModel> modelMap = dynamicMapper.findAll()
				.stream()
				.collect(Collectors.toMap(
						XDynamicModel::getAnnotatorName, 
						Function.identity()
				));
		
		for(String dynamic : xDynamicHandlerMap.keySet())
			if(!ongoingModels.contains(dynamic)) 
				removeDynamicXHandler(dynamic);
		
		for(String ongoing : ongoingModels) {
			ModelHandlerEntity entity = ModelHandlerEntity.ofWorkerEntity(modelMap.get(ongoing));
			if(entity == null)
				continue;
			
			DynamicXModelHandler dynamicHandler = (DynamicXModelHandler)xDynamicHandlerMap.get(ongoing);
			if(dynamicHandler == null) {//新建
				addDynamicXHandler(entity);
				continue;
			}
			
			ModelHandlerEntity previousEntity = dynamicHandler.getHandlerEntity();
			//多一层检测 防止每次都比对代码 消耗性能
			if(Math.abs(entity.getUpdateTs().getTime() - previousEntity.getUpdateTs().getTime()) < 1000 || entity.isEqual(previousEntity)) {
				dynamicHandler.setHandlerEntity(entity);
			}else {
				removeDynamicXHandler(entity.getAnnotatorName());
				addDynamicXHandler(entity);
			}
			
			Map<String, Object> additional = (Map<String, Object>)Objects.requireNonNullElse(entity.getAdditional(), Map.of());
			
			List<KestrelInterceptor> interceptors = Arrays.stream(applicationContext.getBeanNamesForType(KestrelInterceptor.class))
					.map(name -> applicationContext.getBean(name, KestrelInterceptor.class))
					.collect(Collectors.toList());
			
			List<KestrelInterceptor> wantedInterceptors = interceptors.stream()
					.filter(interceptor -> additional.containsKey(interceptor.name()))
		    		.sorted((l, r) -> Integer.compare(l.getOrder(), r.getOrder()))
		    		.collect(Collectors.toList());
			
			List<KestrelInterceptor> fixedInterceptors = dynamicHandler.getInterceptors().stream().filter(i -> !interceptors.contains(i)).collect(Collectors.toList());
			wantedInterceptors.addAll(fixedInterceptors);
			
			dynamicHandler.setInterceptors(wantedInterceptors);
		}
	}
	
	@Scheduled(fixedDelay = 2000)
	synchronized void heartBeatPermanentStream(){
		if(!streamSemaphore.tryAcquire())
			return ;
		
		updateProcessorMap();
		
		Map<String, List<String>> ongoingStream = this.ongoingStream;
		
		//检查所有流是否达到最大帧数
		for(VideoStream stream : ongoingVideoStreamMap.values()){
			Integer max = stream.getDevice().getFrameMax();
			if(stream.getVideoStatus() == VideoStatus.EOF || (max != null && stream.getFrameIndex() >= max)) {
				log.warn("Video[" + stream.getDevice().getDeviceId() + "] , update sts =1,", stream.getVideoStatus(), " ",stream.getFrameIndex());

				infraMapper.updateStsAndSeedByDeviceId(1, VideoStreamInfra.initSeed, new Date(), stream.getDevice().getDeviceId());
			}
			
			if(stream.getVideoStatus() == VideoStatus.UNKNOWN)
				log.warn("Video[" + stream.getDevice().getDeviceId() + "] , the video stream may not be running for 10s, please check if this message show up many times.");
			else if(stream.getVideoStatus() == VideoStatus.OK)
				stream.setVideoStatus(VideoStatus.UNKNOWN);
		}
		
		Map<String, VideoStreamInfra> infraMap = new HashMap<String, VideoStreamInfra>();
		if(!ongoingStream.isEmpty()) {
			infraMap = infraMapper.findByDeviceIdIn(ongoingStream.keySet())
					.stream()
					.collect(Collectors.toMap(
						VideoStreamInfra::getDeviceId, 
						Function.identity()
					));
		}
		
		/** 关闭流*/
		Iterator<Entry<String, VideoStreamXWorker>> streamIt = ongoingVideoStreamMap.entrySet().iterator();
		while(streamIt.hasNext()) {
			Entry<String, VideoStreamXWorker> entry = streamIt.next();
			if(!ongoingStream.containsKey(entry.getKey())) {
				VideoStreamXWorker worker = entry.getValue();
				try {
					log.info("[videoHandleLog] [stream] worker.stop {}", worker.getDevice().getDeviceId());
					worker.stop();
				}catch(Exception e) {
					log.error("[videoHandleLog] [stream] remove stream:{} Exception:{}",worker.getDevice().getDeviceId(), e.getMessage());
					e.printStackTrace();
				}
				streamIt.remove();

				//发现XworkerStreamClosedEvent event收不到了，因为@EventListener(classes = {XworkerEvent.class}) 方法里面 xDynamicHandlerMap为空，备注下
				for(Processor processor : entry.getValue().getHighRateProcessors().values())
					try {
						applicationContext.publishEvent(new XworkerStreamClosedEvent(entry.getKey(), processor.getProcessor(), XworkerStreamClosedEvent.CLOSE));
					}catch(Exception e) {
						e.printStackTrace();
					}
			}
		}
		
		/** 检查流 打开流*/
		for(Entry<String, List<String>> entry : ongoingStream.entrySet()) {			
			VideoStreamXWorker stream = ongoingVideoStreamMap.get(entry.getKey());
			VideoStreamInfra infra = infraMap.get(entry.getKey());
			Map<String, Processor> targetProcessorMap = highProcessorMap.get(entry.getKey())
					.entrySet().stream()
					.filter(e -> entry.getValue().contains(e.getKey()))
					.collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));

			String  infraDecoderFormat  = infra.getDecoderFormat();
			String  infraBufferStrategy = infra.getFrameBufferStrategy();
			Integer infraFrameBuffer    = infra.getFrameBuffer();
			
			Set<String> accessorDecoderFormats  = new HashSet<String>();
			Set<String> accessorBufferStrategys = new HashSet<String>();
			Set<Integer> accessorFrameBuffers   = new HashSet<Integer>();
			
			for(Processor processor : targetProcessorMap.values()) {
				XModelHandler handler = getXDynamicHandlerMap().get(processor.getProcessor());
				
				if(handler instanceof ConfigAccessor) {
					ConfigAccessor accessor = (ConfigAccessor)handler;
					
					if(StringUtils.isNotBlank(accessor.getDecoderFormat()))
						accessorDecoderFormats.add(accessor.getDecoderFormat());
					
					if(StringUtils.isNotBlank(accessor.getFrameBufferStrategy()))
						accessorBufferStrategys.add(accessor.getFrameBufferStrategy());
					
					if(Objects.nonNull(accessor.getFrameBuffer()))
						accessorFrameBuffers.add(accessor.getFrameBuffer());
					
					if(processor.getInterval() == null && accessor.getInterval() != null)
						processor.setInterval(accessor.getInterval());
				}
				
				if(processor.getInterval() == null)
					processor.setInterval(Objects.requireNonNullElse(infra.getFrameSkip(), 0));
			}
			
			if(StringUtils.isBlank(infraDecoderFormat)) {
				if(accessorDecoderFormats.size() == 1)
					infra.setDecoderFormat(accessorDecoderFormats.stream().findAny().get());
				else
					infra.setDecoderFormat("rgb24");//default
			}
			
			if(StringUtils.isBlank(infraBufferStrategy)) {
				if(accessorBufferStrategys.size() == 1)
					infra.setFrameBufferStrategy(accessorBufferStrategys.stream().findAny().get());
				else
					infra.setFrameBufferStrategy("smart");//default
			}
			
			if(Objects.isNull(infraFrameBuffer)) {
				if(CollectionUtils.isNotEmpty(accessorFrameBuffers))
					infra.setFrameBuffer(accessorFrameBuffers.stream().max(Integer::compare).get());
				else
					infra.setFrameBuffer(Utils.instance.nonSeenReusedFrameCount);//default
			}
			
			if(stream == null) {
				List<Integer> types = highTypeMap.get(infra.getDeviceId())
						.entrySet().stream()
						.filter(e -> targetProcessorMap.containsKey(e.getKey()))
						.map(e -> e.getValue())
						.collect(Collectors.toList());

				boolean isGpuDecoder = types.contains(2) || types.contains(3);
				boolean isMultiplex = types.contains(3) && !types.contains(2);
				
				stream = new VideoStreamXWorker(infra, isGpuDecoder, isMultiplex, applicationContext);
				stream.setDevice_status_event_output(device_status_event_output);
				stream.setSenseyex_raw_event_output(senseyex_raw_event_output);
				
				for(Processor processor : targetProcessorMap.values())
					try {
						applicationContext.publishEvent(new XworkerStreamStartedEvent(infraMap.get(entry.getKey()), processor.getProcessor()));
					}catch(Exception e) {
						e.printStackTrace();
					}
				
				try {
					stream.start();
					JSONObject codecPar = (JSONObject)VideoStream.chosenStreamInfoMap.get(infra.getDeviceId()).get("codecpar");
					int frameRate = (Integer) VideoStream.chosenStreamInfoMap.get(infra.getDeviceId()).get("frameRate");
					int width = (Integer)  codecPar.get("width");
					if(width!=infra.getRtspWidth() || frameRate != infra.getVideoRate()){
						int height = (Integer)  codecPar.get("height");
						infra.setVideoRate(frameRate);
						infra.setRtspWidth(width);
						infra.setRtspHeight(height);
						infraMapper.saveAndFlush(infra);
						List<DeviceInfoEntity.DeviceInfo> deviceInfoList = new ArrayList<>();
						DeviceInfoEntity.DeviceInfo deviceInfo = DeviceInfoEntity.DeviceInfo.builder()
								.did(infra.getDeviceId())
								.lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
								.rtspWidth(String.valueOf(width))
								.rtspHeight(String.valueOf(height))
								.videoRate(String.valueOf(frameRate))
								.build();
						deviceInfoList.add(deviceInfo);
						DeviceInfoEntity deviceInfoEntity = DeviceInfoEntity.builder()
								.eventAction("DeviceInfoUpdate")
								.eventName("cognitive")
								.data(deviceInfoList)
								.build();
						try{
							device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceInfoEntity)).build());
							log.info("device_info_event_output send output {}",deviceInfoEntity);
						}catch (Exception ex){
							log.warn("device_info_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceInfoEntity);
						}
					}

					ongoingVideoStreamMap.put(entry.getKey(), stream);
				}catch(Exception e) {
					log.error("[videoHandleLog] [stream] open stream:{} Exception:{}",stream.getDevice().getDeviceId(), e.getMessage());
					e.printStackTrace();
				}
			}else {
				List<String> creatings = new ArrayList<String>();
				List<String> removings = new ArrayList<String>();
				
				for(String previous : stream.getHighRateProcessors().keySet())
					if(!targetProcessorMap.containsKey(previous))
						removings.add(previous);
				
				for(String current : targetProcessorMap.keySet())
					if(!stream.getHighRateProcessors().containsKey(current))
						creatings.add(current);
				
				for(String processor : creatings) {
					try {
						applicationContext.publishEvent(new XworkerStreamStartedEvent(infraMap.get(entry.getKey()), processor));
					}catch(Exception e) {
						e.printStackTrace();
					}
				}
				
				for(String processor : removings) {
					try {
						applicationContext.publishEvent(new XworkerStreamClosedEvent(entry.getKey(), processor, XworkerStreamClosedEvent.CLOSE));
					}catch(Exception e) {
						e.printStackTrace();
					}
				}
			}
			
			stream.setDevice(infra);
			stream.setXStoreHandler(this);
			stream.setHighRateProcessors(targetProcessorMap);
			
			if(switcherOnWorkers.contains(entry.getKey()))
				stream.setLowRateProcessors(lowProcessorMap.get(entry.getKey()));
			else
				stream.setLowRateProcessors(Map.of());
		}
	}
	
	@Scheduled(fixedDelay = 2000)
	synchronized void heartBeatTemporaryStream(){
		/** 关闭临时流*/
		List<String> currentDeviceIds = List.copyOf(temporaryVideoStreamMap.keySet());
		
		for(String currentDeviceId : currentDeviceIds) {
			Pair<VideoStreamInfra, VideoStreamXWorker> pair = temporaryVideoStreamMap.get(currentDeviceId);
			if(pair.getRight().getVideoStatus() != VideoStatus.EOF && pair.getRight().getFrameIndex() < Objects.requireNonNullElse(pair.getLeft().getFrameMax(), Integer.MAX_VALUE)) 
				continue;

			log.info("[videoHandleLog] [stream] heartBeatTemporaryStream close stop {}", pair.getRight().getDevice().getDeviceId());
			try { pair.getRight().stop(); }catch(Exception e) { e.printStackTrace(); }
			
			temporaryVideoStreamMap.remove(currentDeviceId);
			List<Processor> processors = temporaryStream.remove(currentDeviceId).getRight();
			
			for(Processor processor : processors) {
				try {
					applicationContext.publishEvent(new XworkerStreamClosedEvent(currentDeviceId, processor.getProcessor(), XworkerStreamClosedEvent.CLOSE));
				}catch(Exception e) {
					e.printStackTrace();
				}
			}
			
			synchronized(pair.getLeft()) {
				pair.getLeft().notifyAll();
			}
		}
		
		if(!tempStreamSemaphore.tryAcquire())
			return ;
		
		/** 打开临时流*/
		for(Entry<String, Pair<VideoStreamInfra, List<Processor>>> entry : temporaryStream.entrySet()) {
			VideoStreamInfra infra = entry.getValue().getLeft();
			Map<String, Processor> targetProcessorMap = entry.getValue().getRight().stream().collect(Collectors.toMap(p -> p.getProcessor(), p -> p));

			String  infraDecoderFormat  = infra.getDecoderFormat();
			String  infraBufferStrategy = infra.getFrameBufferStrategy();
			Integer infraFrameBuffer    = infra.getFrameBuffer();
			
			Set<String> accessorDecoderFormats  = new HashSet<String>();
			Set<String> accessorBufferStrategys = new HashSet<String>();
			Set<Integer> accessorFrameBuffers   = new HashSet<Integer>();
			
			for(Processor processor : targetProcessorMap.values()) {
				XModelHandler handler = getXDynamicHandlerMap().get(processor.getProcessor());
				
				if(handler instanceof ConfigAccessor) {
					ConfigAccessor accessor = (ConfigAccessor)handler;
					
					if(StringUtils.isNotBlank(accessor.getDecoderFormat()))
						accessorDecoderFormats.add(accessor.getDecoderFormat());
					
					if(StringUtils.isNotBlank(accessor.getFrameBufferStrategy()))
						accessorBufferStrategys.add(accessor.getFrameBufferStrategy());
					
					if(Objects.nonNull(accessor.getFrameBuffer()))
						accessorFrameBuffers.add(accessor.getFrameBuffer());
					
					if(processor.getInterval() == null && accessor.getInterval() != null)
						processor.setInterval(accessor.getInterval());
				}
				
				if(processor.getInterval() == null)
					processor.setInterval(Objects.requireNonNullElse(infra.getFrameSkip(), 0));
			}
			
			if(StringUtils.isBlank(infraDecoderFormat)) {
				if(accessorDecoderFormats.size() == 1)
					infra.setDecoderFormat(accessorDecoderFormats.stream().findAny().get());
				else
					infra.setDecoderFormat("rgb24");//default
			}
			
			if(StringUtils.isBlank(infraBufferStrategy)) {
				if(accessorBufferStrategys.size() == 1)
					infra.setFrameBufferStrategy(accessorBufferStrategys.stream().findAny().get());
				else
					infra.setFrameBufferStrategy("smart");//default
			}
			
			if(Objects.isNull(infraFrameBuffer)) {
				if(CollectionUtils.isNotEmpty(accessorFrameBuffers))
					infra.setFrameBuffer(accessorFrameBuffers.stream().max(Integer::compare).get());
				else
					infra.setFrameBuffer(Utils.instance.nonSeenReusedFrameCount);//default
			}
			
			if(infra.getVideoRate() == null)
				infra.setVideoRate(Integer.MAX_VALUE);
			
			if(!temporaryVideoStreamMap.containsKey(entry.getKey())) {				
				VideoStreamXWorker streamWorker = new VideoStreamXWorker(infra, true, false, applicationContext);
				streamWorker.setDevice(infra);
				streamWorker.setXStoreHandler(this);
				streamWorker.setDevice_status_event_output(device_status_event_output);
				streamWorker.setSenseyex_raw_event_output(senseyex_raw_event_output);
				streamWorker.setHighRateProcessors(targetProcessorMap);
				streamWorker.setApiCalled(true);
				streamWorker.setUseJavaCapturedTime(false);
				
				for(Processor processor : targetProcessorMap.values()) {
					try {
						applicationContext.publishEvent(new XworkerStreamStartedEvent(infra, processor.getProcessor()));
					}catch(Exception e) {
						e.printStackTrace();
					}
				}
				
				try {
					streamWorker.start();
					temporaryVideoStreamMap.put(entry.getKey(), new MutablePair<VideoStreamInfra, VideoStreamXWorker>(infra, streamWorker));
				}catch(Exception e) {
					e.printStackTrace();
				}
			}
		}
	}
	

//    @Scheduled(fixedDelay = 10 * 1000)
//	public void checkQueueMapEmpty() {
//    	for(XModelHandler handler : xDynamicHandlerMap.values())
//    		((AbstractXModelHandler)handler).checkQueueMapEmpty();
//    }

	private void updateProcessorMap() {

		Map<String,Integer> processorSaveImgMap = xDynamicModelRepository.findAll().stream().
				collect(Collectors.toMap(
						XDynamicModel::getAnnotatorName,
						XDynamicModel::getImgSaveTag
				));


		List<VideoStreamXswitcher> switchers = switcherMapper.findAll();
		
		Map<String, Map<String, Processor>> highProcessorMap = new HashMap<String, Map<String, Processor>>();
		Map<String, Map<String, Processor>> lowProcessorMap  = new HashMap<String, Map<String, Processor>>();
		Map<String, Map<String, Integer>> highTypeMap = new HashMap<String, Map<String, Integer>>();
		
		for(VideoStreamXswitcher switcher : switchers) {
			if(switcher.getType() == 1 || switcher.getType() == 2 || switcher.getType() == 3) {
				Map<String, Processor> highProcessors = highProcessorMap.get(switcher.getDeviceId());
				if(highProcessors == null) {
					highProcessors = new HashMap<String, Processor>();
					highProcessorMap.put(switcher.getDeviceId(), highProcessors);
				}
				
				List<Processor> processors = JSON.parseArray(switcher.getProcessors(), Processor.class);
				for(Processor processor : processors){
					if(processorSaveImgMap!=null) {
						processor.setImgSaveTag(processorSaveImgMap.getOrDefault(processor.getProcessor(), 0));
					}
					highProcessors.put(processor.getProcessor(), processor);
				}
				
				Map<String, Integer> highTypes = highTypeMap.get(switcher.getDeviceId());
				if(highTypes == null) {
					highTypes = new HashMap<String, Integer>();
					highTypeMap.put(switcher.getDeviceId(), highTypes);
				}
				
				for(Processor processor : processors)
					highTypes.put(processor.getProcessor(), switcher.getType());
			}else {
				Map<String, Processor> lowProcessors = lowProcessorMap.get(switcher.getDeviceId());
				if(lowProcessors == null) {
					lowProcessors = new HashMap<String, Processor>();
					lowProcessorMap.put(switcher.getDeviceId(), lowProcessors);
				}
				
				List<Processor> processors = JSON.parseArray(switcher.getProcessors(), Processor.class);
				for(Processor processor : processors){
					processor.setImgSaveTag(processorSaveImgMap.get(processor.getProcessor()));
					lowProcessors.put(processor.getProcessor(), processor);
				}
			}
		}
		
		this.highProcessorMap = highProcessorMap;
		this.lowProcessorMap  = lowProcessorMap;
		this.highTypeMap  = highTypeMap;
	}
	
	@EventListener(classes = {XworkerEvent.class})
	void onWorkerEvent(XworkerEvent event) throws Exception{
		for(XModelHandler handler : xDynamicHandlerMap.values())
			if(handler instanceof DynamicXModelHandler && Objects.equals(handler.annotatorName(), event.getAnnotatorName()))
				((DynamicXModelHandler)handler).onWorkerEvent(event);
	}

    @EventListener(classes = ContextRefreshedEvent.class)
	void init(ContextRefreshedEvent event) throws Exception{ 
    	if(event.getApplicationContext() == applicationContext)
    		heartBeatModel();
    }
	
	/** 启动一个动态模型 */
	synchronized void addDynamicXHandler(ModelHandlerEntity entity){
		log.info("[videoHandleLog] [model] add DynamicXHandler:{}",entity.getAnnotatorName());
		if(xDynamicHandlerMap.containsKey(entity.getAnnotatorName()))
			return ;
		
		DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();  
		
		Class<?> targetClass ;
		List<KestrelInterceptor> interceptors = new ArrayList<KestrelInterceptor>();
		
		boolean hasJavaName = StringUtils.isNotBlank(entity.getJavaClassName()) ;
		boolean hasJavaCode = StringUtils.isNotBlank(entity.getJavaCode());
		
		if(hasJavaName) {
			try {
				if(hasJavaCode) 
					targetClass = compileJavaCode(entity.getJavaClassName(), entity.getJavaCode());
				else 
					targetClass = Class.forName("com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code." + entity.getJavaClassName());
			}catch(Exception e) {
				log.error("[videoHandleLog] [model] add DynamicXHandler:{} error:{}",entity.getAnnotatorName(),e.getMessage());
				e.printStackTrace();
				return;
			}
			
			if(DynamicXModelHandler.class.isAssignableFrom(targetClass.getSuperclass())) {//是模型类
				/** 暂时啥也不干 */
			}else if(Arrays.stream(targetClass.getInterfaces()).filter(i -> i == KestrelInterceptor.class).findAny().isPresent()) {//是拦截器
				try {
					interceptors.add((KestrelInterceptor)targetClass.getDeclaredConstructor().newInstance());
				} catch (Exception e) {
					log.error("[videoHandleLog] [model] add DynamicXHandler:{} error:{}",entity.getAnnotatorName(),e.getMessage());
					e.printStackTrace();
				}
				
				targetClass = DynamicXModelHandler.class;
			}else {
				new RuntimeException(targetClass.getName() + " is not [extends DynamicXModelHandler] or [implements KestrelInterceptor]").printStackTrace();
		    	return ;
			}
		}else {
			targetClass = DynamicXModelHandler.class;
		}

	    BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(targetClass);
	    beanDefinitionBuilder.addPropertyValue("handlerEntity", entity);
	    beanDefinitionBuilder.addPropertyValue("interceptors",  interceptors);
	    defaultListableBeanFactory.registerBeanDefinition(DYNAMIC_PREFFIX + entity.getAnnotatorName(), beanDefinitionBuilder.getBeanDefinition());
	    AbstractXModelHandler handler = (AbstractXModelHandler)applicationContext.getBean(DYNAMIC_PREFFIX + entity.getAnnotatorName());
	    
		Map<String, XModelHandler> xDynamicHandlerMap = new HashMap<String, XModelHandler>(this.xDynamicHandlerMap);
		xDynamicHandlerMap.put(entity.getAnnotatorName(), handler);
		this.xDynamicHandlerMap = xDynamicHandlerMap;
	}
	
	/** 移除一个动态模型*/
	synchronized void removeDynamicXHandler(String annotatorName) {
		log.info("[videoHandleLog] [model] remove DynamicXHandler:{}",annotatorName);
		if(!xDynamicHandlerMap.containsKey(annotatorName))
			return ;
		
		Map<String, XModelHandler> xDynamicHandlerMap = new HashMap<String, XModelHandler>(this.xDynamicHandlerMap);
		xDynamicHandlerMap.remove(annotatorName);
		this.xDynamicHandlerMap = xDynamicHandlerMap;
		
		try { Thread.sleep(500); } catch (InterruptedException e) { }
		
		DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();  
		defaultListableBeanFactory.removeBeanDefinition(DYNAMIC_PREFFIX + annotatorName);
	}
	
	@PostConstruct
    void extractJar() throws IOException {
    	new File("/tmp/jars/").mkdirs();
    	
    	Resource[] resources = ResourcePatternUtils.getResourcePatternResolver(applicationContext).getResources("classpath*:/BOOT-INF/lib/*.jar");
    	
    	List<String> jars = new ArrayList<String>();
    	
    	for(int index = 0; index < resources.length; index ++) {
    		String newPath = "/tmp/jars/" + resources[index].getFilename();
    		try { Files.copy(resources[index].getInputStream(), Path.of(newPath)); }catch(Exception e) { }
    		jars.add(newPath);
    	}
    	
    	JARPATH = jars.stream().collect(Collectors.joining(":"));
    }
	
	public void sendSwitcherOnWorkerEvent(String deviceId, Pointer refFrame, long now, List<Processor> lowRates) {
		Runnable run = () -> {
			String image64 = ImageUtils.bytesToBase64(FrameUtils.encode_image_as_jpg(refFrame));
			FrameUtils.batch_free_frame(refFrame);
			
			List<ServiceInstance> swticherInstances = discoveryClient.getServices().stream()
					.map(serviceId -> discoveryClient.getInstances(serviceId))
					.flatMap(List::stream)
					.filter(instance -> "true".equals(instance.getMetadata().get("isXSwitcher")))
					.collect(Collectors.toList());
			
			SenseyexRawImage senseyexEvent = SenseyexRawImage.builder()
					.images(new String[] {image64})
					.processors(lowRates.stream().map(p -> p.getProcessor()).toArray(String[]::new))
					.deviceId(deviceId)
					.capturedTime(now)
					.build();
			
			ServiceInstance instance = swticherInstances.get(ThreadLocalRandom.current().nextInt(swticherInstances.size()));
			
			RestUtils.restTemplate4000ms.postForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/xswitcher/send/senseyex/raw/image/input", new HttpEntity<SenseyexRawImage>(senseyexEvent, RestUtils.headers), BaseRes.class);
		};
		
		try {
			cogThreadPool.execute(run);
		}catch(Exception e) {
			FrameUtils.batch_free_frame(refFrame);
			log.error("[videoHandleLog] [stream] sendSwitcherOnWorkerEvent deviceId:{} error:{}",deviceId,e.getMessage());
			e.printStackTrace();
		}
	}
    
    /** 编译动态代码*/
	public static Class<?> compileJavaCode(String javaClassName, String javaCode) throws FileNotFoundException, IOException, ClassNotFoundException {
		File directory = new File("/tmp/dynamicCode/");
		if(directory.exists()) {
			Arrays.stream(directory.listFiles()).forEach(File::delete);
			directory.delete();
		}
		
		directory.mkdirs();
		
		File java = new File(directory.getAbsolutePath() + "/" + javaClassName + ".java");
		Class<?> targetClass;
		try {
			try(FileOutputStream output = new FileOutputStream(java)){
				javaCode = javaCode.replaceFirst(javaClassName, javaClassName);
				output.write(javaCode.getBytes());
				output.flush();
			}
			
			JavaCompiler compiler = ToolProvider.getSystemJavaCompiler(); 
			StandardJavaFileManager fileManager = compiler.getStandardFileManager(null, null, null);
		    Iterable<? extends JavaFileObject> javaFileObjects = fileManager.getJavaFileObjects(java.getAbsolutePath());  
		    compiler.getTask(null, fileManager, null, List.of("-classpath", JARPATH), null, javaFileObjects).call();
		    URLClassLoader classLoader = new URLClassLoader(new URL[] {new URL("file:" + directory.getAbsolutePath() + "/")}, DynamicXModelHandler.class.getClassLoader());
		    
		    for(File clazz : directory.listFiles((dir, name) -> name.endsWith("class") && !name.equals(javaClassName + ".class"))) {
		    	String clazzName = clazz.getName().replaceAll(".class", "");
		    	classLoader.loadClass(clazzName);
		    }
		    
		    targetClass = classLoader.loadClass(javaClassName);
		    classLoader.close();
		}finally {
			Arrays.stream(directory.listFiles()).forEach(File::delete);
			directory.delete();
		}
		
	    return targetClass;
	}
	
    private static String JARPATH;
    private static final String DYNAMIC_PREFFIX     = "dynamic_";

	public Map<String, VideoStreamXWorker> getOngoingVideoStreamMap() {
		return this.ongoingVideoStreamMap;
	}
}
