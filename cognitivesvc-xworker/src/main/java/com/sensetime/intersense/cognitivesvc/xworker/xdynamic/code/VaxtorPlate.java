package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.*;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateInput;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;
import jakarta.annotation.PreDestroy;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.IntStream;
import java.util.stream.Stream;


@Slf4j
@SuppressWarnings("unused")
public class VaxtorPlate extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    private static final String ROISTRING = "diff_roi";
    @Getter
    protected final boolean lazyInit = false;
    @Getter
    protected final Integer interval = 0;//检一跳三
    @Getter
    protected final Boolean blocking = false;
    @Getter
    protected final boolean queueMap = true;
    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些
    @Getter
    protected final String frameBufferStrategy = "smart";

    protected final Integer vaxtorPlateTimeoutLimit = 500;

    private RestTemplate vaxtorRestTemplate = null;

    @Getter
    protected final String decoderFormat = "nv12";
    protected ConcurrentHashMap<Long, Map<String, String>> vaxtorDevice = new ConcurrentHashMap<Long, Map<String, String>>();
    @Value("${lpr-service.plateOcrByte-url}")
    private String lprServiceUrl;

    private static String[] toArrayStringRoiList(List<Integer[][]> arr) {
        int numRows = arr.size();
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr.get(i));
        }
        return result;
    }

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);

        //log.info("outputKesons{}", KesonUtils.kesonToJson(outputKesons[0]));
        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons)[0], imageIds);

        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);
        }
    }

    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];

        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);

        String deviceId = (String) handlingList.get(0).getModelRequest().getParameter().get("deviceId");
        VideoStreamInfra deviceInfra = (VideoStreamInfra) handlingList.get(0).getModelRequest().getParameter().get("deviceInfra");
        Long streamSourceId = Utils.keyToContextId(deviceId);
        if (!vaxtorDevice.containsKey(streamSourceId)) {
            vaxtorDevice.put(streamSourceId, new ConcurrentHashMap<String, String>());
        }
        Map<String, String> roiCongregate = vaxtorDevice.get(streamSourceId);

        for (int index = 0; index < pointers.length; index++) {
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            if (index == 0) {
                Integer[][][] proi = handlingList.get(index).getModelRequest().getProcessor().getRoi();

                CognitiveEntity.Processor processor = handlingList.get(index).getModelRequest().getProcessor();
                Polygon[] polygons = processor.fetchPolygons();
                if (ArrayUtils.isEmpty(polygons))
                    polygons = new Polygon[]{new Polygon(new int[]{0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[]{0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};

                List<Integer[][]> rois = new ArrayList<>();
                for (int indexs = 0; indexs < polygons.length; indexs++) {
                    Integer[][] singleRoi = proi[indexs];
                    if (singleRoi.length <= 0)
                        continue;
                    rois.add(singleRoi);
                }
                String policyRoiString = JSON.toJSONString(toArrayStringRoiList(rois));

                //roi
                if (roiCongregate.get(ROISTRING) == null) {
                    roiCongregate.put(ROISTRING, policyRoiString);
                    updateRoi(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString(), true);
                } else {
                    String oldRoiString = roiCongregate.get(ROISTRING);
                    if (!oldRoiString.equals(policyRoiString)) {
                        roiCongregate.put(ROISTRING, policyRoiString);
                        updateRoi(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString(), false);
                    }
                }
            }
            long now = System.currentTimeMillis();
            /** 执行模型 获取数据*/
            pointers[index].process(inTo.getValue(), outOf);
            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        }
        KesonUtils.kesonDeepDelete(param_keson);
        //log.info("output_kesons={}", KesonUtils.kesonToJson(output_kesons[0]));
        return output_kesons;
    }

    public void updateRoi(String[] json1, Pointer pipelinePoint, String sourceId, boolean firstEntry) {

        if (json1.length <= 0) {
            return;
        }
        JSONArray jsonPolyGons = new JSONArray();
        // 直接添加字符串，不再使用 Collections.singletonList
        jsonPolyGons.addAll(Arrays.asList(json1));

        JSONArray json2 = new JSONArray();

        JSONObject roi = new JSONObject();
        roi.put("label_id", 1420); // 机动车 四轮
        roi.put("polygons", jsonPolyGons);
        json2.add(roi);

        JSONObject roiHunmanPoweredVehicle = new JSONObject();
        roiHunmanPoweredVehicle.put("label_id", 1507442); // 摩托车
        roiHunmanPoweredVehicle.put("polygons", jsonPolyGons);
        json2.add(roiHunmanPoweredVehicle);

// point_cal_type 0 => 1 =>
        if (json2.isEmpty()) {
            return;
        }

        String controlPipeStrng = " {\n" +
                "   \"streams\": [\n" +
                "    {\n" +
                "      \"name\": \"video_detect_stream\",\n" +
                "       \"modules\": [\n" +
                "        {\n" +
                "          \"name\": \"roi_filter\",\n" +
                "          \"type\": \"RoiFilter\",\n" +
                "          \"source_id\": 49650,\n" +
                "          \"inputs\": [\n" +
                "            \"tracked_targets\"\n" +
                "          ],\n" +
                "          \"outputs\": [\n" +
                "            \"tracked_targets\"\n" +
                "          ],\n" +
                "          \"config\": {\n" +
                "             \"roi_filter\": " + json2 +
                "          }\n" +
                "       }" +
                "      ]\n" +
                "   }\n" +
                " ]\n" +
                "}";

        controlPipeStrng = controlPipeStrng.replace("49650", sourceId);
        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);

        PointerByReference outRemove = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), outRemove);


        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

        KesonUtils.kesonDeepDelete(out);
        KesonUtils.kesonDeepDelete(outRemove);
        KesonUtils.kesonDeepDelete(input);
        log.info(">>> [vaxtorPlate] update roi_filter device is: {} , roi is: {}", sourceId, controlPipeStrng);
    }

    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        if (modelResult.getDetectResult() == null || !(modelResult.getDetectResult() instanceof Map))
            return false;

        List<Map<String, Object>> targets = (List<Map<String, Object>>) ((Map<String, Object>) Objects.requireNonNullElse(modelResult.getDetectResult(), Map.of())).get("targets");
        if (CollectionUtils.isEmpty(targets))
            return false;

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        float threshold = Objects.requireNonNullElse(processor.getThreshold(), 0f);
        int minSize = Objects.requireNonNullElse(processor.getMinSize(), Integer.MIN_VALUE);
        int maxSize = Objects.requireNonNullElse(processor.getMaxSize(), Integer.MAX_VALUE);
        Polygon[] polygons = processor.fetchPolygons();

        List<String> roiIdStrings = Objects.requireNonNullElse(processor.getExtras(), List.of())
                .stream()
                .filter(extra -> CognitiveEntity.Processor.ROIIDS.equals(((Map<String, Object>) extra).get("type")))
                .map(extra -> (List<String>) ((Map<String, Object>) extra).getOrDefault(CognitiveEntity.Processor.ROIIDS, List.of()))
                .findAny()
                .orElse(List.of());

        Map<String, Object> additional = (Map<String, Object>) Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());

        Iterator<Map<String, Object>> its = targets.iterator();
        while (its.hasNext()) {
            Map<String, Object> target = its.next();

            Map<String, Object> roi = (Map<String, Object>) target.get("target_image_roi");
            if (roi == null) {
                its.remove();
                continue;
            }

            Number conf = (Number) target.get("confidence");
            if (conf != null && conf.floatValue() < threshold) {
                its.remove();
                continue;
            }

            int top = ((Number) roi.get("top")).intValue();
            int left = ((Number) roi.get("left")).intValue();
            int width = ((Number) roi.get("width")).intValue();
            int height = ((Number) roi.get("height")).intValue();
            int x = left + width / 2;
            int y = top + height / 2;

            boolean minSizeCheck = width >= minSize && height >= minSize;
            boolean maxSizeCheck = width <= maxSize && height <= maxSize;
            if (!maxSizeCheck || !minSizeCheck) {
                its.remove();
                continue;
            }

            if (ArrayUtils.isEmpty(polygons)) {
                target.put("roi_hits", SCREEN);
            } else {
                int[] roiIndexes = IntStream.range(0, polygons.length).filter(index -> polygons[index].contains(x, y)).toArray();
                if (ArrayUtils.isEmpty(roiIndexes)) {
                    its.remove();
                    continue;
                }

                target.put("roi_hits", Arrays.stream(roiIndexes).mapToObj(id -> id < roiIdStrings.size() ? roiIdStrings.get(id) : String.valueOf(id)).toArray(String[]::new));
            }
        }

        return !targets.isEmpty();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        if (modelResult.getDetectResult() == null || !(modelResult.getDetectResult() instanceof Map))
            return;

        boolean logged = Utils.instance.watchFrameTiktokLevel == -321;
        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        Pointer kesonTargets = KestrelApi.keson_get_object_item(modelResult.getKeson().getValue(), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        Map<String, Object> additional = (Map<String, Object>) Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());

        List<Map<String, Object>> targets = (List<Map<String, Object>>) ((Map<String, Object>) modelResult.getDetectResult()).getOrDefault("targets", List.of());

        for (Map<String, Object> target : targets) {
            Map<String, Object> valueResult = new HashMap<String, Object>();
            valueResult.put("trackId", target.get("track_id"));
            valueResult.put("detect", target.get("target_image_roi"));
            valueResult.put("confidence", target.get("confidence"));
            valueResult.put("quality", target.get("quality"));
            valueResult.put("filterTag", target.get("filter_tag"));
            valueResult.put("status", target.get("status"));
            valueResult.put("deviceId", deviceId);
            if (target.containsKey("confidence"))
                valueResult.put("confidence", target.get("confidence"));
            if (target.containsKey("label"))
                valueResult.put("targetType", target.get("label"));
            else
                valueResult.put("targetType", annotatorName());

            Object roiHits = target.get("roi_hits");
            if (roiHits != null && roiHits != SCREEN)
                valueResult.put("rois", roiHits);

            int targetLabel = ((Number) target.get("label")).intValue();

            Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                    .limit(kesonTarget_size)
                    .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "source_id")) == ((Number) target.get("source_id")).longValue())
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "id")) == ((Number) target.get("id")).longValue())
                    .findAny()
                    .orElse(null);

            if (kesonTarget == null) {
                log.error("start save image null,kesonTarget_size={}, target={}", kesonTarget_size, target);
            }

            //Pointer videoFrame = modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame();
            // 启用第三方厂商车牌
            boolean targetIsViechle = 1420 == targetLabel || 1507422 == targetLabel;
//            if (targetIsViechle && vaxtorConfigProperties.getEnableVaxtorPlateRecognition() && kesonTarget != null) {
            if (targetIsViechle && kesonTarget != null) {
                if (logged) log.info(">>> build output vaxtor plate info modify start!");
                Map<String, Object> imageFrame = (Map<String, Object>) target.get("image");
                StCarPlate vaxtorPlateResult = dealCarplateWithVaxtorHttp(KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image")).getValue(),
                        "bgr", logged, imageFrame);

                // 修改原有的车牌属性
                Map<String, Object> targetAttribute = (Map<String, Object>) target.get("attributes");
                if (vaxtorPlateResult != null) {
                    List<Map<String, Object>> attributeList = new ArrayList<Map<String, Object>>();
                    valueResult.put("attributes", attributeList);

                    if (targetAttribute != null) {
                        for (Map.Entry<String, Object> entry : targetAttribute.entrySet()) {
                            Map<String, Number> attribute = (Map<String, Number>) entry.getValue();
                            attribute.forEach((value, conf) -> {
                                attributeList.add(Map.of("key", entry.getKey(), "value", value, "confidence", conf));
                            });
                        }
                    }
                    attributeList.add(Map.of("key", "st_plate_text", "value", vaxtorPlateResult.st_plate_text, "confidence", vaxtorPlateResult.st_plate_text_score));

                    valueResult.put("attributes", attributeList);
                } else {
                    log.warn(">>> build output vaxtor plate result null, device={},trackId={},image={}", deviceId, target.get("track_id"), imageFrame);
                }
                if (logged)
                    log.info(">>> build output vaxtor plate info modify end!, device={},trackId={},targetAttribute={}",
                            deviceId, target.get("track_id"), JSON.toJSONString(vaxtorPlateResult));
            }
            if (kesonTarget != null) {
                PointerByReference targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                if (targetImage.getValue() != null && logged) {
//                    Map<String, Integer> roi =  (Map<String, Integer>)target.get("target_image_roi");
//                    Pointer roiFrame = FrameUtils.roi_frame(videoFrame, roi.get("left"), roi.get("top"), roi.get("width"), roi.get("height"));
                    String targetImagePath = FrameUtils.save_image_as_jpg(targetImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                    valueResult.put("targetImage", targetImagePath);
                }
                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "scene_frame"));
                if (sceneImage.getValue() != null) {
                    String sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                    valueResult.put("sceneImage", sceneImagePath);
                }
            }
            outputResult.add(valueResult);
        }

        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});

        modelResult.setOutputResult(outputResult);
    }

    private byte[] getQueueItemRoiImageByte(Pointer roiFrame) {
        byte[] imageResPointer = null;
        if (roiFrame == null) {
            return imageResPointer;
        }
        //调用 vps imageio jna or c++ so
        LongByReference outSize = new LongByReference();
        Pointer resultPointer = KestrelApi.kestrel_frame_encoder(roiFrame, 0, outSize);
        if (resultPointer != null) {
            // 使用result字节数组
            long size = outSize.getValue();
            imageResPointer = resultPointer.getByteArray(0, (int) size);
            // 释放内存
            Native.free(Pointer.nativeValue(resultPointer));
        }
        return imageResPointer;
    }

    private byte[] getQueueItemRoiImageByte(Pointer bgFrame, Map<String, Integer> roi) {
        Pointer roiFrame = FrameUtils.roi_frame(bgFrame, roi.get("left"), roi.get("top"), roi.get("width"), roi.get("height"));

        byte[] imageResPointer = null;
        //调用 vps imageio jna or c++ so
        LongByReference outSize = new LongByReference();
        Pointer resultPointer = KestrelApi.kestrel_frame_encoder(roiFrame, 0, outSize);
        if (resultPointer != null) {
            // 使用result字节数组
            long size = outSize.getValue();
            imageResPointer = resultPointer.getByteArray(0, (int) size);
            // 释放内存
            Native.free(Pointer.nativeValue(resultPointer));
        }
        return imageResPointer;
    }

    private StCarPlate dealCarplateWithVaxtorHttp(Pointer image, String pixformat, boolean logged, Map<String, Object> imageFrame) {
        long startTime = System.currentTimeMillis(); // 添加时间统计
        StCarPlate vaxtorPlateResult = null;
        byte[] imageData = getQueueItemRoiImageByte(image);
        if (imageData == null || imageFrame == null) {
            log.error("vaxtorPlateResult imageData is null");
            return vaxtorPlateResult;
        }
        ResponseEntity<BaseRes> response = null;
        String postUrl = lprServiceUrl;

        PlateInput input = new PlateInput();
        input.setImageData(imageData);
        input.setWidth(Integer.parseInt(imageFrame.get("width").toString()));
        input.setHeight(Integer.parseInt(imageFrame.get("height").toString()));
        input.setPixformat(pixformat);
        input.setImageSize(imageData.length);

        try {
            HttpEntity<Object> httpEntity = new HttpEntity<Object>(JSONObject.toJSONString(input), RestUtils.headers);
            // RestTemplate已经配置了超时参数：
            // - vaxtorPlateTimeoutLimit: 连接超时时间(500ms)
            // - maxTotal: 连接池最大连接数(500)
            // - maxPerRoute: 每个路由最大连接数(100)
            vaxtorRestTemplate = vaxtorRestTemplate != null ? vaxtorRestTemplate : RestUtils.get(vaxtorPlateTimeoutLimit, 500, 100);
            response = vaxtorRestTemplate.postForEntity(postUrl, httpEntity, BaseRes.class);

            BaseRes result = response.getBody();
            if (result.getData() == null) {
                if (logged)
                    log.warn(">>> lpr result is null...");
                return null;
            }

            String plateText = ((Map<String, String>) result.getData()).get("plateText");

            // 处理结果
            vaxtorPlateResult = StCarPlate.builder()
                    .st_plate_text(plateText)
                    .st_plate_text_score(0.99999f)
                    .build();
            if (logged) {
                long endTime = System.currentTimeMillis();
                log.info(">>> vaxtor carplate,lprServiceUrl:{}, result={}, cost={}ms",
                    lprServiceUrl, JSON.toJSONString(result), (endTime - startTime));
            }
        } catch (RestClientException restClientException) {
            // 这里会捕获到连接超时异常
            log.error(">>> put object error : " + postUrl + ", cost=" + (System.currentTimeMillis() - startTime) + "ms, error=" + restClientException.getMessage());
            return null;
        } catch (Exception e) {
            log.error(">>> put object error : unknown error, " + postUrl + ", cost=" + (System.currentTimeMillis() - startTime) + "ms", e);
            return null;
        }

        return vaxtorPlateResult;
    }

    @PreDestroy
    @Override
    public synchronized void destroy() {
        super.destroy();

        log.info("VaxtorPlate destroying.");
        vaxtorDevice.clear();
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {

        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            log.info("e.getMesasge{}", e.getAnnotatorName());
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {

        vaxtorDevice.put(contextId, new ConcurrentHashMap<String, String>());

        log.info("VaxtorPlate start context [" + contextId + "].device=" + "[" + infra.getDeviceId() + "]");
    }

    private void stopContext(String deviceId, long contextId) {

        vaxtorDevice.remove(contextId);

        holders[0].controlForRemoveSource(contextId);

        log.info("VaxtorPlate stop context [" + contextId + "].device=" + "[" + deviceId + "]");
    }

    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class StCarPlate {
        private String st_carplate_type;
        private Float st_carplate_type_score;
        private String st_carplate_color;
        private Float st_carplate_color_score;
        private String st_plate_text;
        private Float st_plate_text_score;

    }


}
