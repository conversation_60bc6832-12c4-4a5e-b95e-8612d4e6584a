import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Memory;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.opencv_core.CvScalar;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.Queue;
import java.util.Map.Entry;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class RoiIntrudeStayOBK extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{

    @Getter
    protected final Integer interval = 3;

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final Boolean needContext = true;

    @Getter
    protected final String decoderFormat = "nv12";

    @Getter
    protected final Integer frameBuffer = 24;

    @Getter
    protected final String frameBufferStrategy = "smart";

    @Getter
    protected final boolean lazyInit = false;

    @Getter
    protected final boolean queueMap = true;

    protected Thread carAttributeThread;
    protected ArrayBlockingQueue<QueueItem> carAttributeQueue;

    protected Thread pedAttributeThread;
    protected ArrayBlockingQueue<QueueItem> pedAttributeQueue;

    protected final double scaleRate = 0.1d;

    //<contextid, trackid, track>
    protected final ConcurrentHashMap<Long, Map<Integer, Track>> trackingMap = new ConcurrentHashMap<Long, Map<Integer, Track>>();

    private Pointer pointerRoi = null;

    /** 跑结构化追踪 */
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];
        PointerByReference   param_keson  = prepareInput(handlingList);
        PointerByReference[] outputKesons = new PointerByReference[] {new PointerByReference()};

        synchronized(holders[0]) {
            long now = System.currentTimeMillis();
            holders[0].process(param_keson.getValue(), outputKesons[0]);
            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        }

        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -791;
        if(loggedForCost)
            log.info("batch_extract_xmodel_asap step0 handlingListSize{},{}, {},", handlingList.size(),KesonUtils.kesonToJson(param_keson),KesonUtils.kesonToJson(outputKesons[0]));

        KesonUtils.kesonDeepDelete(param_keson);
        return outputKesons;
    }

    /** 将批量处理结果 整理后放到每个项目 */
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        KesonUtils.tryReformFlockKeson(outputKesons);
        if(Math.abs(scaleRate) > 0.01)
            KesonUtils.scaleAllTargetUp(outputKesons[0].getValue(), scaleRate);

        Pointer targets = KestrelApi.keson_get_object_item(outputKesons[0].getValue(), "targets");
        int targetSize = KestrelApi.keson_array_size(targets);
        for(int index = 0; index < targetSize; index ++) {
            Pointer target = KestrelApi.keson_get_array_item(targets, index);
            long flockIndex = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "flock_index"));
            if(flockIndex != 4)
                continue;

            long track_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "dropped_track_id"));
            log.info("dropped_track_id{}", track_id);
            long image_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "image_id"));

            Map<Integer, Track> currentMap = trackingMap.get(image_id);
            if(currentMap != null)
                currentMap.remove((int)track_id);
        }
        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -791;

        Pointer frames[] = handlingList.stream()
                .map(item -> item.getModelRequest().getVideoFrames()[0].getGpuFrame())
                .toArray(Pointer[]::new);

        PointerByReference[] sub_kesons = KesonUtils.splitFlockKesonAndDestroy(outputKesons[0], frames);
        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);

            if(loggedForCost)
                log.info("batch_handle_extract_result step0 {}, {},", item.getModelRequest().getParameter().get("deviceId").toString(),(sub_kesons[index]!=null) ? KesonUtils.kesonToJson(sub_kesons[index]): null);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public void readModelResult(ModelResult modelResult) {
        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -790;
        if(loggedForCost)
            log.info("readModelResult step0{},", (modelResult !=null && modelResult.getKeson()!=null ? KesonUtils.kesonToJson(modelResult.getKeson()):null));
        super.readModelResult(modelResult);
        if(loggedForCost)
            log.info("readModelResult step1{},", JSON.toJSONString(modelResult.getDetectResult()));
        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();
        if(detectResult != null) {
            List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of());
            Set<Integer> trackIds = targets.stream().map(target -> ((Number)target.get("track_id")).intValue()).collect(Collectors.toSet());

            modelResult.getModelRequest().getParameter().put("trackIds", trackIds);
        }
    }

    @SuppressWarnings({ "unchecked"})
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        boolean logged = Utils.instance.watchFrameTiktokLevel == -321;
        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -790;
        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();
        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();

        if(MapUtils.isEmpty(detectResult)) {
            if(loggedForCost)
                log.info("validateOutputValue step1{}, {},", deviceId, JSON.toJSONString(detectResult));
            return false;
        }
        if(loggedForCost)
            log.info("validateOutputValue step1{}, {},", deviceId, JSON.toJSONString(detectResult));

        Map<Integer, Track> tracks = trackingMap.get(Utils.keyToContextId(deviceId));
        long contextId = Utils.keyToContextId(deviceId);
        if (!trackingMap.containsKey(contextId)) {
            trackingMap.put(contextId,  new HashMap<Integer, Track>());
        }

        Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        Map<Integer, Map<Integer, Map<String, Object>>> labelExtrasMap = (Map<Integer, Map<Integer, Map<String, Object>>>)processor.fetchExtras(function);

        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of());
        Iterator<Map<String, Object>> its = targets.iterator();
        while(its.hasNext()) {
            Map<String, Object> target = its.next();

            int trackId = ((Number)target.get("track_id")).intValue();
            int label = ((Number)target.get("label")).intValue();
            float quality = (((Number)target.get("confidence")).floatValue());
            long capturedTime = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();

            Pointer videoFrame = modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame();
            Track track = tracks.get(trackId);
            if(track == null) {
                track = Track.builder().label(label).trackId(trackId).firstInStamp(new Long[polygons.length]).keepingInStamp(new Long[polygons.length]).build();
                tracks.put(trackId, track);
            }

            track.setCurrentPosition((Map<String, Number>)target.get("roi"),KestrelApi.kestrel_frame_pts(videoFrame));

            Track.Rect previousPosion = track.getPreviousPosition();
            Track.Rect currentPosition = track.getCurrentPosition();

            int currentIndex = track.addCrossData((Map<String, Number>)target.get("roi"));

            track.setCurrentTarget(target);

            boolean toRemove = true;
            String attributeType = "none";

            for(int index = 0 ;index < polygons.length; index ++) {
                Map<String, Object> extra = labelExtrasMap.getOrDefault(index, Map.of()).get(label);
                if(extra == null) {
                    if(loggedForCost)
                        log.info("validateOutputValue step3 {},{},",deviceId, modelResult);
                    continue;
                }

                Polygon polygon = polygons[index];
                Number threshold = (Number)extra.getOrDefault("threshold", processor.getThreshold());
                target.put("threshold", threshold);

                Integer minSize = (Integer)extra.getOrDefault("minSize", processor.getMinSize());
                Integer maxSize = (Integer)extra.getOrDefault("maxSize", processor.getMaxSize());

                boolean baseCheck = true;
                baseCheck &= threshold == null || quality > threshold.floatValue();
                baseCheck &= minSize == null || (currentPosition.getWidth() >= minSize.intValue() && currentPosition.getHeight() >= minSize.intValue());
                baseCheck &= maxSize == null || (currentPosition.getWidth() <= maxSize.intValue() && currentPosition.getHeight() <= maxSize.intValue());

                if(!baseCheck) {
                    if(loggedForCost)
                        log.info("validateOutputValue step4 {},{},",  deviceId,JSON.toJSONString(extra));
                    continue;
                }
                String crossDirection = (String)extra.get("crossDirection");

                Number positionType = (Number)extra.getOrDefault("positionType", 0);

                //利用人体中心点作为目标点
                int currentX  = currentPosition.getLeft()  + currentPosition.getWidth()   / 2;
                int currentY  = currentPosition.getTop()   + currentPosition.getHeight()  / 2;
                if(positionType.intValue() == 1) //利用头顶顶点作为目标点
                    currentY  = currentPosition.getTop();
                else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                    currentY  = currentPosition.getTop()   + currentPosition.getHeight();

                List<Number> crossDot = (List<Number>)extra.getOrDefault("crossDot", List.of());
                if(!crossDot.isEmpty() && previousPosion != null) {
                    int previousX = previousPosion.getLeft() + previousPosion.getWidth()  / 2;
                    int previousY = previousPosion.getTop()  + previousPosion.getHeight() / 2;

                    long previousPts = previousPosion.getFramePts();

                    if(positionType.intValue() == 1)//利用头顶顶点作为目标点
                        previousY = previousPosion.getTop();
                    else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                        previousY = previousPosion.getTop()  + previousPosion.getHeight();

                    int cx, cy;
                    if(crossDot.size() != 2) {
                        cx = Arrays.stream(polygon.xpoints).sum() / polygon.npoints;
                        cy = Arrays.stream(polygon.ypoints).sum() / polygon.npoints;
                    }else {
                        cx = crossDot.get(0).intValue();
                        cy = crossDot.get(1).intValue();
                    }

                    for(int dotIndex = 0; dotIndex < polygon.npoints - 1 || (polygon.npoints > 2 && dotIndex == polygon.npoints - 1); dotIndex ++) {
                        int ax = polygon.xpoints[dotIndex];
                        int ay = polygon.ypoints[dotIndex];

                        int bx = (dotIndex == polygon.npoints - 1) ? polygon.xpoints[0] : polygon.xpoints[dotIndex + 1];
                        int by = (dotIndex == polygon.npoints - 1) ? polygon.ypoints[0] : polygon.ypoints[dotIndex + 1];

                        if(track.getCrossIndex() <= 0) {
                            if (!intersect(ax, ay, bx, by, previousX, previousY, currentX, currentY)) {
                                continue;
                            }
                        }
                        //发生跨线
                        if(track.getCrossIndex() <= 0) {
                            track.setCrossIndex(currentIndex);
                        }
                        //如果发生了跨线，获取target在crossData里的index，index+2，等2帧在判断，
                        int getNumberFrames = 5;

                        if(currentIndex == track.getCrossIndex()  && track.getCrossData().size() >= getNumberFrames ) {

                            boolean previousIntersected = intersect(ax, ay, bx, by, previousX, previousY, cx, cy);
                            boolean targetIntersected = intersect(ax, ay, bx, by, currentX, currentY, cx, cy);

                            String predirection = "unknown";
                            if (previousIntersected) {
                                predirection = "forward";
                            } else if (!previousIntersected) {
                                predirection = "backward";
                            }
                            if(logged) {
                                log.info("crossline1 trackId={},currentIndex={},direction={}",trackId, currentIndex, predirection);
                            }
                            if (StringUtils.isNotBlank(crossDirection) && !crossDirection.equals(predirection)) {
                                if (loggedForCost)
                                    log.info("validateOutputValue step5{} {},", deviceId, modelResult);
                                //continue;
                            }
                            //设置getCrossIndex跨线的为current
                            //计算前面两帧previous，current + post后面两帧的跨线
                            int intersectCount = 0; // 计数相交的线段数量
                            List<Track.Rect> preData = track.getPreData(track.getCrossIndex(), getNumberFrames);
                            int totalSegments = preData.size();
                            int nonIntersectCount = 0; // 计数不相交的线段数量

                            for(Track.Rect rect : preData) {

                                int prex = rect.getLeft() + rect.getWidth()  / 2;
                                int preY = rect.getTop()  + rect.getHeight() / 2;

                                if(positionType.intValue() == 1)//利用头顶顶点作为目标点
                                    preY = rect.getTop();
                                else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                                    preY = rect.getTop()  + rect.getHeight();

                                // 检查线段是否相交
                                boolean isIntersecting = intersect(ax, ay, bx, by, prex, preY, cx, cy);

                                // 根据相交结果更新计数
                                if (isIntersecting) {
                                    intersectCount++; // 计数相交的线段
                                } else {
                                    nonIntersectCount++; // 计数不相交的线段
                                }
                            }
                            // 判断大部分相交或不相交
                            boolean majorityIntersect = intersectCount >= (totalSegments + 1) / 2;
                            boolean majorityNonIntersect = nonIntersectCount >= (totalSegments + 1) / 2;

                            //previousIntersected = majorityIntersect || majorityNonIntersect; // 只要大部分相交或不相交则返回 true
                            String direction = "unknown";
                            if (majorityNonIntersect) {
                                direction = "backward";
                            }
                            if (majorityIntersect) {
                                direction = "forward";
                            }
                            if(logged) {
                                log.info("reCheckRoi deviceId={},trackId={},predirection={},direction={},totalSegments={},track.getCrossIndex()={},track.getCrossData()={},intersectCount={},nonIntersectCount={},preData={}", deviceId, trackId,predirection, direction, totalSegments, track.getCrossIndex(), track.getCrossData().size(),intersectCount,nonIntersectCount,JSON.toJSONString(preData));
                            }

                            if (StringUtils.isNotBlank(crossDirection) && !crossDirection.equals(direction)) {
                                //不告警
                                continue;
                            }
                            //判断pts
                            if(StringUtils.isNotBlank(crossDirection)){
                                long currentFramePts = 0;
                                Map<String, Object> imageFrame = (Map<String, Object>) target.get("image");
                                if (imageFrame != null) {
                                    // 检查 "pts" 是否存在并进行类型转换以获得当前帧的时间戳
                                    if (imageFrame.containsKey("pts")) {
                                        currentFramePts = Long.parseLong(imageFrame.get("pts").toString());
                                    }
                                }
                                Map<String, Object> trackletJson = new HashMap<String, Object>();
                                trackletJson.put("trackId", trackId);
                                trackletJson.put("target", target);
                                trackletJson.put("currentTarget", track.getCurrentTarget());
                                trackletJson.put("previoustarget", track.getPreviousTarget());
                                trackletJson.put("direction", direction);
                                trackletJson.put("crossDirection", crossDirection);
                                trackletJson.put("getPreviousPosition", track.getPreviousPosition());
                                trackletJson.put("previousPositionCache", previousPosion);
                                trackletJson.put("currentPositionCache", currentPosition);
                                trackletJson.put("targetIntersected", targetIntersected);
                                trackletJson.put("position", "ax=" + ax + ",ay=" + ay + ",bx=" + bx + ", by=" + by
                                        +",currentX=" +currentX + ",currentY" +currentY +  ",previousX=" +previousX + ",previousY" +previousY + ",cx="+ cx + ",cy="+ cy + ",prePts=" + previousPts + ",currentFramePts=" + currentFramePts);
                                //ImageUtils.newFileNameJson("dropFaceJson", fileName, trackletJson,"roi");
                                if(logged) {
                                    log.info("currentRoiFrame={},previousPathFrame={},trackletJson={}", target, targetIntersected, JSON.toJSONString(trackletJson));
                                }
                                if (currentFramePts > 0 && previousPts > 0 && previousPts > currentFramePts) {
                                    // 出现时间戳错误
                                    log.error("roiCrossLine error trackId={}, imageFrame={}, previousPts={}", trackId, imageFrame, previousPts);
                                    // 检查交叉方向并切换方向
                                    if (StringUtils.isNotBlank(crossDirection)) {
                                        continue; // 处理完错误，继续下一个循环
                                    } else {
                                        // 不区分方向的，切换方向
                                        log.error("roiCrossLine direction error trackId={}, imageFrame={}, previousPts={}", trackId, imageFrame, previousPts);
                                        direction = direction.equals("forward") ? "backward" : "forward";
                                    }
                                }
                            }

                            List<Map<String, Object>> crossAlert = (List<Map<String, Object>>)target.get("crossAlert");
                            if(crossAlert == null) {
                                crossAlert = new ArrayList<Map<String, Object>>();
                                target.put("crossAlert", crossAlert);
                            }

                            Map<String, Object> cross = new HashMap<String, Object>();
                            cross.put("roiIndex", index);
                            cross.put("direction", direction);
                            crossAlert.add(cross);

                            attributeType = judgeAttributeType(attributeType, extra);
                            toRemove = false;
                        }
                    }
                }

                if(polygon.contains(currentX, currentY)) {
                    if(track.getFirstInStamp()[index] == null) {
                        track.getFirstInStamp()[index] = capturedTime;

                        if(Boolean.TRUE.equals(extra.get("entryAlert"))) {
                            List<Integer> enterRoiIndexes = (List<Integer>)target.get("entryAlert");
                            if(enterRoiIndexes == null) {
                                enterRoiIndexes = new ArrayList<Integer>();
                                target.put("entryAlert", enterRoiIndexes);
                            }

                            enterRoiIndexes.add(index);

                            attributeType = judgeAttributeType(attributeType, extra);
                            toRemove = false;
                        }
                    }

                    if(extra.containsKey("trigger")) {
                        Map<String, Object> trigger = (Map<String, Object>)extra.get("trigger");

                        int triggerTime = (int)trigger.getOrDefault("triggerTime", 1000);
                        int maxTrackTime = (int)trigger.getOrDefault("maxTrackTime", 0);
                        int trackFreq = (int)trigger.getOrDefault("trackFreq", 1000);

                        boolean bingo = false;

                        if(track.getKeepingInStamp()[index] == null) {
                            if(capturedTime - track.getFirstInStamp()[index] >= triggerTime)
                                bingo = true;
                        }else if(maxTrackTime < 0 || (capturedTime - track.getFirstInStamp()[index]) <= maxTrackTime) {
                            if(capturedTime - track.getKeepingInStamp()[index] > trackFreq)
                                bingo = true;
                        }


                        if(bingo) {
                            track.getKeepingInStamp()[index] = capturedTime;

                            List<Integer> stayRoiIndexes = (List<Integer>)target.get("triggerAlert");
                            if(stayRoiIndexes == null) {
                                stayRoiIndexes = new ArrayList<Integer>();
                                target.put("triggerAlert", stayRoiIndexes);
                            }
                            stayRoiIndexes.add(index);

                            attributeType = judgeAttributeType(attributeType, extra);
                            toRemove = false;
                        }
                        if(loggedForCost)
                            log.info("validateOutputValue step6 {},trackId={},{},track.getKeepingInStamp()[index]={},capturedTime={}", deviceId,trackId, toRemove, track.getKeepingInStamp()[index], capturedTime);
                    }
                }else {
                    if(track.getFirstInStamp()[index] != null) {
                        if(Boolean.TRUE.equals(extra.get("exitAlert"))) {
                            List<Integer> exitRoiIndexes = (List<Integer>)target.get("exitAlert");
                            if(exitRoiIndexes == null) {
                                exitRoiIndexes = new ArrayList<Integer>();
                                target.put("exitAlert", exitRoiIndexes);
                            }
                            exitRoiIndexes.add(index);
                            target.put("exitAlert", exitRoiIndexes);

                            attributeType = judgeAttributeType(attributeType, extra);
                            toRemove = false;
                        }

                        track.getFirstInStamp()[index] = null;
                        track.getKeepingInStamp()[index] = null;
                    }
                    if(loggedForCost)
                        log.info("validateOutputValue step7 {},trackId={},{},", deviceId,trackId, toRemove);
                }
            }
            if(toRemove) {
                its.remove();
            } else {
                target.put("roiIds", labelExtrasMap.getOrDefault(Integer.MAX_VALUE, Map.of()).getOrDefault(Integer.MAX_VALUE, Map.of()).get("roiIds"));
                target.put("attributeType", attributeType);
                if(target.get("attributes") !=null){
                    target.put("attributesStart", target.get("attributes"));
                }
            }
        }

        return !targets.isEmpty();
    }

    @SuppressWarnings({ "unchecked"})
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();
        Processor processor = (Processor)modelResult.getModelRequest().getProcessor();
        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();

        List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();

        injectAttributeToTargets(modelResult);

        long frameIndex = modelResult.getModelRequest().getVideoFrames()[0].getFrameIndex();

        Pointer videoFrame = modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame();

        for(Map<String, Object> target : (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of())) {
            Map<String, Object> result = new HashMap<String, Object>();

            Number threshold = (Number)target.get("threshold");

            result.put("detect", target.get("roi"));
            result.put("targetType", target.get("label"));
            result.put("capturedTime", target.get("capturedTime"));
            result.put("confidence", target.get("confidence"));
            result.put("deviceId", deviceId);
            result.put("frameIndex", frameIndex);
            result.put("attributeType", target.get("attributeType"));

            result.put("attributesFaceBody",( target.get("attributes")!=null ?  target.get("attributes"):1));
            result.put("attributesStart", target.get("attributesStart"));

            List<String> roidIds = (List<String>)target.remove("roiIds");
            if(CollectionUtils.isNotEmpty(roidIds)) {
                List<Integer> enter = (List<Integer>)target.remove("entryAlert");
                if(CollectionUtils.isNotEmpty(enter))
                    result.put("entryAlert", enter.stream().map(index -> roidIds.get(index)).collect(Collectors.toList()));

                List<Integer> stay = (List<Integer>)target.remove("triggerAlert");
                if(CollectionUtils.isNotEmpty(stay))
                    result.put("triggerAlert", stay.stream().map(index -> roidIds.get(index)).collect(Collectors.toList()));

                List<Integer> exit = (List<Integer>)target.remove("exitAlert");
                if(CollectionUtils.isNotEmpty(exit))
                    result.put("exitAlert", exit.stream().map(index -> roidIds.get(index)).collect(Collectors.toList()));

                List<Map<String, Object>> cross = (List<Map<String, Object>>)target.get("crossAlert");
                if(CollectionUtils.isNotEmpty(cross)) {
                    for(Map<String, Object> crossItem : cross)
                        crossItem.put("roiId", roidIds.get((int)crossItem.remove("roiIndex")));

                    result.put("crossAlert", cross);
                }
            }

            Map<String, Map<String, Number>> rawAttributeMap = (Map<String, Map<String, Number>>)target.get("attributes");


            String attributeType = (String)target.remove("attributeType");

            if(attributeType.equals("strong") || attributeType.equals("weak")){
                if( ((Number)target.get("label")).intValue() == 221488 && MapUtils.isEmpty(rawAttributeMap)){
                    continue;
                }
            }
            Integer trackId = ((Number)target.get("track_id")).intValue();
            result.put("trackId", trackId);
            Track track = trackingMap.getOrDefault(Utils.keyToContextId(deviceId), Map.of()).get(trackId);
            if(track != null) {
                List<Map<String, Map<String, Number>>> detainedAttributes = track.getDetainedAttributes();
                if(detainedAttributes.size() > 1024)
                    detainedAttributes.clear();

                Map<String, Map<String, Number>> attributes = Map.of();

                if(MapUtils.isNotEmpty(rawAttributeMap)) {
                    detainedAttributes.add(rawAttributeMap);//如果track不断的话 有可能会爆内存  但是真实场景不应该会出现 暂时不管了

                    Map<String, Object> bufferMap = processor.fetchBufferMap();
                    if(MapUtils.isEmpty(bufferMap)) {
                        attributes = rawAttributeMap;
                    }else {
                        int bufferSize = (Integer)bufferMap.getOrDefault("bufferSize", 3);
                        int bufferType = (Integer)bufferMap.getOrDefault("bufferType", 1);

                        if(target.containsKey("exitAlert") || detainedAttributes.size() >= bufferSize)
                            attributes = track.detainedAttributesToBest(bufferType);
                    }

                    if(MapUtils.isNotEmpty(attributes)){
                        result.put("attributesBestBuffer", bufferMap);
                    }
                }else {
                    if(MapUtils.isNotEmpty(track.getBestAttribute()))
                        attributes = track.getBestAttribute();
                    else if(CollectionUtils.isNotEmpty(detainedAttributes))
                        attributes = detainedAttributes.get(detainedAttributes.size() - 1);

                    if(MapUtils.isNotEmpty(attributes)){
                        result.put("attributesBest", MapUtils.isNotEmpty(track.getBestAttribute()) );
                        result.put("attributesAll", CollectionUtils.isNotEmpty(detainedAttributes) );
                    }

                }

                if(MapUtils.isNotEmpty(attributes))
                    result.put("attributes", rawAttributeMapToOutput(attributes, threshold));
            }
            if(!"strong".equals(attributeType) || result.containsKey("attributes"))
                resultList.add(result);
        }

        if(resultList.isEmpty())
            modelResult.setOutputResult(null);
        else
            modelResult.setOutputResult(resultList);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        Processor processor = modelResult.getModelRequest().getProcessor();

        long frameIndex = modelResult.getModelRequest().getVideoFrames()[0].getFrameIndex();

        for(Integer[][] roi : processor.getRoi()) {
            for(int index = 0; index < roi.length - 1; index ++) {
                result.add(Line.builder().from(new int[] {roi[index][0], roi[index][1]}).to(new int[] {roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().from(new int[] {roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[] {roi[0][0], roi[0][1]}).build());
        }

        Map<Integer, Map<Integer, Map<String, Object>>> extras = (Map<Integer, Map<Integer, Map<String, Object>>>)processor.fetchExtras(function);
        for(Map<Integer, Map<String, Object>> top : extras.values())
            for(Map<String, Object> sub : top.values()) {
                List<Integer> crossDot = (List<Integer>)sub.get("crossDot");
                if(CollectionUtils.isNotEmpty(crossDot)) {
                    result.add(Line.builder().from(new int[] {crossDot.get(0) - 5, crossDot.get(1) - 5}).to(new int[] {crossDot.get(0) + 5, crossDot.get(1) + 5}).build());
                    result.add(Line.builder().from(new int[] {crossDot.get(0) + 5, crossDot.get(1) - 5}).to(new int[] {crossDot.get(0) - 5, crossDot.get(1) + 5}).build());
                }
            }

        Object outputResult = modelResult.getOutputResult();
        if(outputResult != null) {
            List<Map<String, Object>> outputs = List.of();
            if(outputResult.getClass().isArray()) {
                outputs = Arrays.stream((List<Map<String, Object>>[])outputResult).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
            }else if(outputResult instanceof List){
                outputs = (List<Map<String, Object>>)outputResult;
            }
            CvScalar color = opencv_core.CV_RGB(0, 0, 255); // red
            for(Map<String, Object> output : outputs) {
                String text = "";

                if(output.containsKey("entryAlert"))
                    text += "entry:[" + output.get("entryAlert") + "]";

                if(output.containsKey("triggerAlert"))
                    text += "trigger:[" + output.get("triggerAlert") + "]";

                if(output.containsKey("exitAlert"))
                    text += "exit:[" + output.get("exitAlert") + "]";

                if(output.containsKey("crossAlert"))
                    text += "cross:[" + output.get("crossAlert") + "]";

                if(output.containsKey("trackId"))
                    text += "trackId:[" + output.get("trackId") + "]";

                if(frameIndex > 0)
                    text += "frameIndex:[" + frameIndex + "]";

                Map<String, Integer> roi = (Map<String, Integer>)output.get("detect");
                result.add(Rect.builder().processor(annotatorName()).color(color).text(text).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());
            }
        }

        Set<Integer> trackIds = (Set<Integer>)modelResult.getModelRequest().getParameter().getOrDefault("trackIds", Set.of());
        Map<Integer, Track> ongoingTracks = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        for(Track track : ongoingTracks.values()) {
            if(!trackIds.contains(track.getTrackId()))
                continue;

            for(Long in : track.getFirstInStamp()) {
                if(in != null) {
                    result.add(Rect.builder().processor(annotatorName()).text(track.getTrackId() + "-" + frameIndex).top(track.getCurrentPosition().getTop()).left(track.getCurrentPosition().getLeft()).width(track.getCurrentPosition().getWidth()).height(track.getCurrentPosition().getHeight()).build());
                    break;
                }
            }
        }

        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();

        if(MapUtils.isEmpty(detectResult))
            return result;
        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of());

        for (Map<String, Object> target : targets) {
            int trackId = ((Number) target.get("track_id")).intValue();

            Map<String, Number> rois = (Map<String, Number>) target.get("roi");


            result.add(Rect.builder().processor(annotatorName()).text(trackId + "-" + frameIndex).top(rois.get("top").intValue()).left(rois.get("left").intValue()).width(rois.get("width").intValue()).height(rois.get("height").intValue()).build());
        }
        return result;
    }

    private List<Map<String, Object>> rawAttributeMapToOutput(Map<String, Map<String, Number>> rawAttributeMap, Number threshold){
        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();

        for(Entry<String, Map<String, Number>> entry : rawAttributeMap.entrySet()) {
            Entry<String, Number> maxEntry;

            if("car_plate".equals(entry.getKey()))
                maxEntry = entry.getValue().entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
            else
                maxEntry = entry.getValue().entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue() % 1, r.getValue().floatValue() % 1)).get();

            float realConf = maxEntry.getValue().floatValue() % 1;
            if(threshold == null || realConf > threshold.floatValue())
                outputResult.add(Map.of("key", entry.getKey(), "value", maxEntry.getKey(), "confidence", realConf));
        }

        return outputResult;
    }

    @SuppressWarnings({ "unchecked" })
    private void injectAttributeToTargets(ModelResult modelResult) {
        List<QueueItem> addedItems = new ArrayList<QueueItem>();
        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());

        Pointer rgbFrame = null;

        for(Map<String, Object> target : targets) {
            if(target.get("attributes") !=null){
                target.put("attributesStart", target.get("attributes"));
            }
            String attributeType = (String)target.getOrDefault("attributeType", "none");
            if(!"weak".equals(attributeType) && !"strong".equals(attributeType))
                continue;

            Queue<QueueItem> attributeQueue = null;
            int label = ((Number)target.get("label")).intValue();
            if(label == 221488 || label == 1507442 || label == 2125610)
                attributeQueue = pedAttributeQueue;
            else if(label == 1420)
                attributeQueue = carAttributeQueue;

            if(attributeQueue == null)
                continue;

            if(rgbFrame == null)
                rgbFrame = FrameUtils.ref_or_cvtcolor_buffered_frame(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame(), KestrelApi.KESTREL_VIDEO_RGB);

            QueueItem item = QueueItem.builder().target(target).rgbImage(FrameUtils.ref_frame(rgbFrame)).frameIndex(modelResult.getModelRequest().getVideoFrames()[0].getFrameIndex()).build();
            if(attributeQueue.offer(item))
                addedItems.add(item);
            else
                FrameUtils.batch_free_frame(item.getRgbImage());
        }

        for(int index = 0; index < addedItems.size(); index ++)
            try {
                addedItems.get(index).getLatch().await();

            } catch (InterruptedException e) { }

        FrameUtils.batch_free_frame(rgbFrame);

        for(Map<String, Object> target : targets) {
            Map<String, Map<String, Number>> attributes = (Map<String, Map<String, Number>>)target.get("attributes");
            if(attributes != null)
                attributes.values().forEach(v -> v.entrySet().forEach(ee -> {if(ee.getValue().floatValue() >= 1) ee.setValue(0.99999f);}));
        }
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            long contextId = Utils.keyToContextId(event.getDeviceId());

            try { Thread.sleep(1000); } catch (InterruptedException x) { }
            stopContext(contextId);
        }
    }

    private final Pointer initPointer() {
        Initializer.bindDeviceOrNot();
        KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/hunter.kep", "");
        return KestrelApi.kestrel_annotator_open("hunter", "{\"model\":\"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("facebody_module") + "\",\"max_batch_size\":" + 32 + "}");
    }

    @PostConstruct
    @Override
    public synchronized void initialize() {
        super.initialize();

        carAttributeQueue = new ArrayBlockingQueue<QueueItem>(Math.max(32, 4 * this.batchSize));
        carAttributeThread = new Thread(carAttributeRunnable);
        carAttributeThread.setDaemon(true);
        carAttributeThread.setPriority(Thread.NORM_PRIORITY + 2);
        carAttributeThread.setName("XModel[" + annotatorName() + "-Ped AttrbiuteRunner]");
        carAttributeThread.start();

        pedAttributeQueue = new ArrayBlockingQueue<QueueItem>(Math.max(32, 4 * this.batchSize));
        pedAttributeThread = new Thread(pedAttributeRunnable);
        pedAttributeThread.setDaemon(true);
        pedAttributeThread.setPriority(Thread.NORM_PRIORITY + 2);
        pedAttributeThread.setName("XModel[" + annotatorName() + "-Vec AttrbiuteRunner]");
        pedAttributeThread.start();

        Object flockPipeline = JSON.parseObject(handlerEntity.getFlockConfig(), Map.class);
        log.info("roi Intrude Stay initializing : " + JSON.toJSONString(flockPipeline));
    }

    @PreDestroy
    @Override
    public synchronized void destroy() {
        super.destroy();

        log.info("roi Intrude Stay destroying.");
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        if(trackingMap.containsKey(contextId))
            return ;

        trackingMap.put(contextId, new ConcurrentHashMap<Integer, Track>());

        log.info("RoiIntrudeStay start context [" + contextId + "].");
    }

    private void stopContext(long contextId) {
        if(!trackingMap.containsKey(contextId))
            return ;

        trackingMap.remove(contextId);

        holders[0].controlForRemoveSource(contextId);

        log.info("RoiIntrudeStay stop context [" + contextId + "].");
    }

    private String judgeAttributeType(String attributeType, Map<String, Object> extra) {
        String type = (String)extra.getOrDefault("attributeType", "none");
        if("strong".equals(type))
            attributeType = "strong";
        else if("weak".equals(type) && "none".equals(attributeType))
            attributeType = "weak";

        return attributeType;
    }

    @SuppressWarnings("unchecked")
    private final Runnable pedAttributeRunnable = () -> {
        Initializer.bindDeviceOrNot();

        while(status == 0 || !pedAttributeQueue.isEmpty()) {
            List<QueueItem> handlingList = Utils.drainFromQueue(pedAttributeQueue, this.batchSize, getDrainPollCount(), getDrainTimeout());
            if(CollectionUtils.isEmpty(handlingList)) {
                try { Thread.sleep(20); } catch (InterruptedException e) { }
                continue;
            }

            List<QueueItem> pedQueue = handlingList.stream().filter(item -> ((Number)item.getTarget().get("label")).intValue() == 221488).collect(Collectors.toList());
            List<QueueItem> bikQueue = handlingList.stream().filter(item -> ((Number)item.getTarget().get("label")).intValue() == 1507442).collect(Collectors.toList());

            Pointer pedInput = KesonUtils.frameToKeson(
                    pedQueue.stream().map(b -> b.getRgbImage()).toArray(Pointer[]::new),
                    pedQueue.stream().map(b -> KesonUtils.roiMapToMemory((Map<String, Number>)b.getTarget().get("roi"))).toArray(Memory[]::new),
                    target -> KestrelApi.keson_add_item_to_object(target, "label", KestrelApi.keson_create_int(221488)));

            Pointer bikeInput = KesonUtils.frameToKeson(
                    bikQueue.stream().map(b -> b.getRgbImage()).toArray(Pointer[]::new),
                    bikQueue.stream().map(b -> KesonUtils.roiMapToMemory((Map<String, Number>)b.getTarget().get("roi"))).toArray(Memory[]::new),
                    target -> KestrelApi.keson_add_item_to_object(target, "label", KestrelApi.keson_create_int(1507442)));

            Pointer input = KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), pedInput, bikeInput);
            PointerByReference out = new PointerByReference();

            try {
                long now = System.currentTimeMillis();
                holders[1].process(input, out);
                monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);

                List<Map<String, Object>> attributes = (List<Map<String, Object>>)KesonUtils.kesonToJson(out);

//                handlingList.stream().forEach(handle->{
//                    log.info("pedAttr{},attributes,{} frameIndexRoiOutput{}", handle, attributes, handle.frameIndex, handle.getTarget());
//                });

                //log.info("bike attributes:{}", JSON.toJSONString(attributes));

                for(Map<String, Object> pedTarget : (List<Map<String, Object>>)attributes.get(0).get("targets")) {
                    Map<String, Object> attribute = (Map<String, Object>)pedTarget.get("attribute");
//
//                    Map<String, Object> carplate = (Map<String, Object>)((List)pedTarget.getOrDefault("carplate", List.of(Map.of()))).get(0);
//
//                    if(MapUtils.isNotEmpty(carplate)) {
//                        attribute.put("st_carplate_type",  Maps.newHashMap(Map.of(carplate.get("carplate_type"),  carplate.get("plate_score"))));
//                        attribute.put("st_carplate_color", Maps.newHashMap(Map.of(carplate.get("carplate_color"), carplate.get("plate_score"))));
//                        attribute.put("st_plate_text",     Maps.newHashMap(Map.of(carplate.get("plate_text"),     carplate.get("plate_score"))));
//                    }

                    try { dealWithAge(attribute); }catch(Exception e) { }
                    pedQueue.get(((Number)pedTarget.get("image_id")).intValue()).getTarget().put("attributes", attribute);


                }
//                if( ((List<Map<String, Object>>)((List<?>) attributes.get(0).get("targets"))).size() <=0 ){
//                    for (QueueItem ped :pedQueue ){
//                        ped.getTarget().put("attributes", null);
//                    }
//                }


                for(Map<String, Object> bikeTarget : (List<Map<String, Object>>)attributes.get(1).get("targets")) {
                    Map<String, Object> attribute = (Map<String, Object>)bikeTarget.get("attribute");

                    Map<String, Object> carplate = (Map<String, Object>)((List)bikeTarget.getOrDefault("carplate", List.of(Map.of()))).get(0);

                    if(MapUtils.isNotEmpty(carplate)) {
                        attribute.put("st_carplate_type",  Maps.newHashMap(Map.of(carplate.get("carplate_type"),  carplate.get("plate_score"))));
                        attribute.put("st_carplate_color", Maps.newHashMap(Map.of(carplate.get("carplate_color"), carplate.get("plate_score"))));
                        attribute.put("st_plate_text",     Maps.newHashMap(Map.of(carplate.get("plate_text"),     carplate.get("plate_score"))));
                    }


                    try { dealWithAge(attribute); }catch(Exception e) { }
                    //log.info("bike attributes after construct:{}", JSON.toJSONString(attribute));
                    bikQueue.get(((Number)bikeTarget.get("image_id")).intValue()).getTarget().put("attributes", attribute);
                }
            }catch(Exception e) {
                e.printStackTrace();
            }finally {
                KesonUtils.kesonDeepDelete(input, out.getValue());

                for(QueueItem item : handlingList)
                    item.close();
            }
        }
    };

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private final Runnable carAttributeRunnable = () -> {
        Initializer.bindDeviceOrNot();

        while(status == 0 || !carAttributeQueue.isEmpty()) {
            List<QueueItem> handlingList = Utils.drainFromQueue(carAttributeQueue, this.batchSize, getDrainPollCount(), getDrainTimeout());
            if(CollectionUtils.isEmpty(handlingList)) {
                try { Thread.sleep(20); } catch (InterruptedException e) { }
                continue;
            }

            Pointer carInput = KesonUtils.frameToKeson(
                    handlingList.stream().map(b -> b.getRgbImage()).toArray(Pointer[]::new),
                    handlingList.stream().map(b -> KesonUtils.roiMapToMemory((Map<String, Number>)b.getTarget().get("roi"))).toArray(Memory[]::new),
                    target -> KestrelApi.keson_add_item_to_object(target, "label", KestrelApi.keson_create_int(1420)));

            Pointer input = KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), carInput);
            PointerByReference out = new PointerByReference();
            try {
                long now = System.currentTimeMillis();
                holders[2].process(input, out);
                monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);

                List<Map<String, Object>> attributes = (List<Map<String, Object>>)KesonUtils.kesonToJson(out);

                for(Map<String, Object> carTarget : (List<Map<String, Object>>)attributes.get(0).get("targets")) {
                    Map<String, Object> attribute = (Map<String, Object>)carTarget.get("attribute");
                    Map<String, Object> carplate = (Map<String, Object>)((List)carTarget.getOrDefault("carplate", List.of(Map.of()))).get(0);

                    if(MapUtils.isNotEmpty(carplate)) {
                        attribute.put("st_carplate_type",  Maps.newHashMap(Map.of(carplate.get("carplate_type"),  carplate.get("plate_score"))));
                        attribute.put("st_carplate_color", Maps.newHashMap(Map.of(carplate.get("carplate_color"), carplate.get("plate_score"))));
                        attribute.put("st_plate_text",     Maps.newHashMap(Map.of(carplate.get("plate_text"),     carplate.get("plate_score"))));
                    }

                    handlingList.get(((Number)carTarget.get("image_id")).intValue()).getTarget().put("attributes", attribute);
                }
            }catch(Exception e) {
                e.printStackTrace();
            }finally {
                KesonUtils.kesonDeepDelete(input, out.getValue());

                for(QueueItem item : handlingList)
                    item.close();
            }
        }
    };

    @SuppressWarnings({ "unchecked", "rawtypes"})
    private static final Function<Object, Map> function = new Function<Object, Map>() {

        @Override
        public Map apply(Object e) {
            List<Map<String, Object>> params = (List<Map<String, Object>>)e;

            Map<Integer, Map<Integer, Map<String, Object>>> result = new HashMap<Integer, Map<Integer, Map<String, Object>>>();
            for(Map<String, Object> param : params) {
                if(!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer)param.getOrDefault("roiIndex", 0);
                Object targetLabel = param.get("targetType");

                Map<Integer, Map<String, Object>> roiParam = result.get(roiIndex);
                if(roiParam == null) {
                    roiParam = new HashMap<Integer, Map<String, Object>>();
                    result.put(roiIndex, roiParam);
                }

                if(targetLabel == null) {
                    roiParam.put(221488,  param);
                    roiParam.put(1420,    param);
                    roiParam.put(1507442, param);
                    roiParam.put(2125610, param);
                }else {
                    if(targetLabel instanceof Number)
                        roiParam.put(((Number)targetLabel).intValue(), param);
                    else if(targetLabel instanceof String)
                        for(String label : ((String) targetLabel).split(","))
                            roiParam.put(Integer.parseInt(label), param);
                }
            }

            for(Map<String, Object> param : params) {
                if("roiIds".equals(param.get("type"))) {
                    result.put(Integer.MAX_VALUE, Map.of(Integer.MAX_VALUE, Map.of("roiIds", param.get("roiIds"))));
                    break;
                }
            }

            return result;
        }
    };

    @SuppressWarnings("unchecked")
    private void dealWithAge(Map<String, Object> rawAttributeMap) {
        Map<String, Number> upper = (Map<String, Number>)rawAttributeMap.remove("age_up_limit");
        if(upper != null)
            rawAttributeMap.put("age_up_limit", new HashMap<String, Number>(Map.of(upper.get("age_up_limit").toString(), 1)));

        Map<String, Number> lower = (Map<String, Number>)rawAttributeMap.remove("age_lower_limit");
        if(lower != null)
            rawAttributeMap.put("age_lower_limit", new HashMap<String, Number>(Map.of(lower.get("age_lower_limit").toString(), 1)));
    }

    private static final boolean intersect(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4) {
        float bx = x2 - x1;
        float by = y2 - y1;
        float dx = x4 - x3;
        float dy = y4 - y3;
        float b_dot_d_perp = bx * dy - by * dx;

        if (b_dot_d_perp == 0)
            return false;

        float cx = x3 - x1;
        float cy = y3 - y1;
        float t = (cx * dy - cy * dx) / b_dot_d_perp;
        if (t < 0 || t > 1)
            return false;

        float u = (cx * by - cy * bx) / b_dot_d_perp;
        if (u < 0 || u > 1)
            return false;

        return true;
    }

    @Data
    @Accessors(chain = true)
    @Builder
    public static class QueueItem implements AutoCloseable{
        private Pointer rgbImage;

        private Map<String, Object> target;

        @Default
        protected long frameIndex = -1;

        @Default
        private CountDownLatch latch = new CountDownLatch(1);

        @Override
        public synchronized void close(){
            FrameUtils.batch_free_frame(rgbImage);
            rgbImage = null;
            latch.countDown();
        }
    }

    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class Track{
        private int label;
        private int trackId;

        @Default
        private  int crossIndex = 0;

        private Rect previousPosition;
        private Rect currentPosition;

        private Long[] firstInStamp;
        private Long[] keepingInStamp;

        @Default
        private List<Rect> crossData = new ArrayList<>(); // 存储数据的列表

        private Map<String, Object> previousTarget;
        private Map<String, Object> currentTarget;

        /** 该追踪中已经融合过的最佳的属性数据 */
        @Default
        private Map<String, Map<String, Number>> bestAttribute = new HashMap<>();
        /** 该追踪中所有累积的属性数据 */
        @Default
        private List<Map<String, Map<String, Number>>> detainedAttributes = new ArrayList<>();

        public Map<String, Map<String, Number>> detainedAttributesToBest(int bufferType){
            if(CollectionUtils.isEmpty(detainedAttributes))
                return bestAttribute;

            List<Map<String, Map<String, Number>>> detainedAttributes = this.detainedAttributes;
            this.detainedAttributes = new ArrayList<>();

            for(Map<String, Map<String, Number>> detainedAttribute : detainedAttributes)
                for(Entry<String, Map<String, Number>> detainedAttributeItemEntry : detainedAttribute.entrySet())
                    handleEntry(detainedAttributeItemEntry);

            if(bufferType != 0){
                var returnBestAttribute = bestAttribute;
                bestAttribute = new HashMap<>();
                return returnBestAttribute;
            }else
                return bestAttribute;
        }

        public synchronized void setCurrentPosition(Map<String, Number> roi, long pts ) {
            previousPosition = currentPosition;
            currentPosition = Rect.builder().top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).framePts(pts).build();
        }

        public int addCrossData(Map<String, Number> roi) {

            crossData.add(Rect.builder().top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).build());
            return crossData.size() - 1; // 返回添加项的索引
        }
        public void setCrossIndex(int currentIndex) {
            crossIndex = currentIndex;
        }
        // 根据给定的索引获取前两个和后两个数据
        public List<Rect> getPreData(int index, int numFrames) {
            List<Rect> result = new ArrayList<>();

            // 确保请求的帧数大于零
            if (numFrames <= 0) {
                return result; // 如果请求的帧数不合法，返回空列表
            }

            // 通过循环获取指定数量的帧数据
            for (int i = 1; i <= numFrames; i++) {
                if (index - i >= 0) {
                    result.add(crossData.get(index - i));
                } else {
                    break; // 如果已经没有更多的帧，就终止循环
                }
            }

            return result;
        }
        public List<Rect> getPostData(int index) {
            List<Rect> result = new ArrayList<>();


            // 获取后两个数据
            if (index + 1 < crossData.size()) {
                result.add(crossData.get(index + 1));
            }
            if (index + 2 < crossData.size()) {
                result.add(crossData.get(index + 2));
            }

            return result;
        }
        public void setCurrentTarget(Map<String, Object> target) {
            previousTarget = currentTarget;
            currentTarget = target;
        }


        private final void handleEntry(Entry<String, Map<String, Number>> detainedAttributeItemEntry) {
            Map<String, Number> bestAttributeItem = bestAttribute.get(detainedAttributeItemEntry.getKey());
            Map<String, Number> detainedAttributeItem = detainedAttributeItemEntry.getValue();

            if(bestAttributeItem == null) {
                for(Entry<String, Number> entry : detainedAttributeItem.entrySet())
                    if(entry.getValue().floatValue() >= 1)
                        entry.setValue(0.9999f);

                bestAttribute.put(detainedAttributeItemEntry.getKey(), detainedAttributeItem);
            }else {
                for(Entry<String, Number> detainedItemEntry : detainedAttributeItem.entrySet()) {
                    float detainedConf = detainedItemEntry.getValue().floatValue();
                    detainedConf = detainedConf >= 1 ? 0.9999f : detainedConf;

                    if(bestAttributeItem.containsKey(detainedItemEntry.getKey())) {
                        float bestNumber = bestAttributeItem.get(detainedItemEntry.getKey()).floatValue();

                        int count = (int)bestNumber;
                        float bestConf = bestNumber % 1;

                        bestAttributeItem.put(detainedItemEntry.getKey(), (count + 1) + (bestConf * count + detainedConf) / (count + 1));
                    }else {
                        bestAttributeItem.put(detainedItemEntry.getKey(), detainedConf);
                    }
                }
            }
        }

        @Data
        @Accessors(chain = true)
        @Builder
        public static class Rect{
            private int left;
            private int top;
            private int width;
            private int height;
            private long framePts;
        }
    }
}