package com.sensetime.intersense.cognitivesvc.xworker.zutils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Map.Entry;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_area2d_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XModelHandler.KestrelInterceptor;
import com.sun.jna.Memory;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Getter;

@Configuration("xworkerKestrelInterceptorConfiguration")
public class KestrelInterceptorConfiguration {
	
	@Bean
	public KestrelInterceptor inputIndexMapInterceptor() {
		return new KestrelInterceptor() {
			
			@Getter
			private int order = Ordered.HIGHEST_PRECEDENCE;
			
			@Override 
			public String name() { return "inputIndexMap"; }
			
			@SuppressWarnings("unchecked")
			@Override 
			public void beforeExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {
				String indexString = String.valueOf(index);
				Number paramIndex = ((Map<String, Number>)additional.getOrDefault(name(), Map.of())).get(indexString);
				if(paramIndex == null || paramIndex.intValue() >= index)
					return ;
				
	        	inTo.setValue(outputKesons[paramIndex.intValue()].getValue());
			}
		};
	}
	
	@Bean
	public KestrelInterceptor labelFilterMapInterceptor() {
		return new KestrelInterceptor() {
			
			@Getter
			private int order = -1;
			
			@Override 
			public String name() { return "labelFilterMap"; }
			
			@SuppressWarnings("unchecked")
			@Override 
			public void beforeExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {
				String indexString = String.valueOf(index);
				Number targetLabel = ((Map<String, Number>)additional.getOrDefault(name(), Map.of())).get(indexString);
				if(targetLabel == null)
					return ;
		    	
		    	Pointer targets = KestrelApi.keson_get_object_item(inTo.getValue(), "targets");
		        int targets_length = KestrelApi.keson_array_size(targets);

		        List<Pointer> noMatchTargets = new ArrayList<Pointer>();
		        for (int jndex = targets_length - 1; jndex >= 0; jndex --) {//从后往前
		        	Pointer target = KestrelApi.keson_get_array_item(targets, jndex);
		        	int label = (int)KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "label"));
		        	
		        	if(targetLabel.intValue() != label)
		        		noMatchTargets.add(KestrelApi.keson_detach_from_array(targets, jndex));
		        }
		        
		        additional.put("context_" + name(), noMatchTargets);
			}
			
			@SuppressWarnings("unchecked")
			@Override 
			public void afterExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {
				String indexString = String.valueOf(index);
				Number targetLabel = ((Map<String, Number>)additional.getOrDefault(name(), Map.of())).get(indexString);
				if(targetLabel == null)
					return ;
				
				List<Pointer> noMatchTargets = (List<Pointer>)additional.remove("context_" + name());
				
				Pointer targets = KestrelApi.keson_get_object_item(inTo.getValue(), "targets");
		    	for(Pointer noMatchTarget : noMatchTargets)
		    		KestrelApi.keson_add_item_to_array(targets, noMatchTarget);
			}
		};
	}
	
	@Bean
	public KestrelInterceptor attributeFilterMapInterceptor() {
		return new KestrelInterceptor() {
			
			@Getter
			private int order = 0;
			
			@Override 
			public String name() { return "attributeFilterMap"; }
			
			@SuppressWarnings("unchecked")
			@Override 
			public boolean validateOutput(Map<String, Object> additional, Map<String, Object> target) {
				Map<String, Object> attributeFilterMap = (Map<String, Object>)additional.get("attributeFilterMap");
				if(MapUtils.isEmpty(attributeFilterMap))
		        	return true;
		        
				String label = (String)attributeFilterMap.get("label");
				String mainType = (String)attributeFilterMap.get("mainType");
				String subType = (String)attributeFilterMap.get("subType");
				
				if(StringUtils.isBlank(mainType))
					return true;
				
				if(StringUtils.isNotBlank(label) && !Objects.equals(label, String.valueOf(target.get("label"))))
					return false;
				
				Map<String, Object> attributes = (Map<String, Object>)target.getOrDefault("attribute", Map.of());
				Map<String, Number> targetAttribute = (Map<String, Number>)attributes.get(mainType);
		    	if(MapUtils.isEmpty(targetAttribute)) 
		    		return false;
				
		    	if(StringUtils.isNotBlank(subType)) {
		    		Number conf = targetAttribute.get(subType);
		    		if(conf == null)
		    			return false;
		    		
		    		Entry<String, Number> maxItem = targetAttribute.entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
		    		if(!StringUtils.equals(maxItem.getKey(), subType))
		        		return false;
		    		
		    		if(maxItem.getValue().floatValue() < conf.floatValue())
		    			return false;
		    	}
		    	
				return true;
			}
			
			@SuppressWarnings("unchecked")
			@Override 
			public void postProcessOutput(Map<String, Object> additional, Map<String, Object> target, Map<String, Object> value) { 
				Map<String, Object> attributeFilterMap = (Map<String, Object>)additional.get("attributeFilterMap");
				if(MapUtils.isEmpty(attributeFilterMap))
		        	return ;
				
				String mainType = (String)attributeFilterMap.get("mainType");
				boolean removeOther = Boolean.TRUE.equals(attributeFilterMap.get("removeOther"));
				
				if(removeOther) {
					Iterator<Map<String, Object>> it = ((List<Map<String, Object>>)value.get("attributes")).iterator();
					while(it.hasNext())
						if(!Objects.equals(it.next().get("key"), mainType))
							it.remove();
				}
			}
		};
	}
	
	@Bean
	public KestrelInterceptor scaleUpMapInterceptor() {
		return new KestrelInterceptor() {
			
			@Getter
			private int order = 0;
			
			@Override 
			public String name() { return "scaleUpMap"; }
			
			@SuppressWarnings("unchecked")
			@Override 
			public void afterExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {
				String indexString = String.valueOf(index);
				Number scaleRate = ((Map<String, Number>)additional.getOrDefault(name(), Map.of())).getOrDefault(indexString, 0.0f);				
				KesonUtils.scaleAllTargetUp(outputKesons[index].getValue(), scaleRate.floatValue());
			}
		};
	}
	
	@Bean
	public KestrelInterceptor fillRoiMapInterceptor() {
		return new KestrelInterceptor() {
			
			@Getter
			private int order = 0;
			
			@Override 
			public String name() { return "fillRoiMap"; }
			
			@SuppressWarnings("unchecked")
			@Override 
			public void beforeExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {
				String indexString = String.valueOf(index);
				Number fillRate = ((Map<String, Number>)additional.getOrDefault(name(), Map.of())).get(indexString);
				if(fillRate == null)
					return ;
		    	
				Memory area2dMemory = new Memory(KestrelApi._SIZE_kestrel_area2d_t);
		    	Pointer targets = KestrelApi.keson_get_object_item(inTo.getValue(), "targets");
		        int targets_length = KestrelApi.keson_array_size(targets);
		        
		        for(int jndex = 0; jndex < targets_length; jndex ++) {
		        	Pointer target = KestrelApi.keson_get_array_item(targets, jndex);
					Pointer roi = KestrelApi.keson_detach_item_from_object(target, "roi");
					if(roi != null) 
						continue;
		        	
		        	Pointer image = KestrelApi.keson_get_object_item(target, "image");
					if(image == null)
						continue;
					
					float rate = Math.min(1, Math.abs(((Number)fillRate).floatValue()));
					
					PointerByReference imageData = new PointerByReference();
	    			KestrelApi.keson_get_ext_data(image, imageData);

	    			int width  = KestrelApi.kestrel_frame_video_width(imageData.getValue());
	    			int height = KestrelApi.kestrel_frame_video_height(imageData.getValue());
	            	
	    			kestrel_area2d_t newArea2d_t = new kestrel_area2d_t(area2dMemory);
	                newArea2d_t.left   = (int)((1 - rate) * width  / 2);
	                newArea2d_t.top    = (int)((1 - rate) * height / 2);
	                newArea2d_t.width  = (int)(rate * width);
	                newArea2d_t.height = (int)(rate * height);
	                newArea2d_t.write();
	                
	                KestrelApi.keson_add_item_to_object(target, "roi", KestrelApi.keson_create_ext_object(KestrelApi.KESON_AREA2D(), area2dMemory));
					
		        }
			}
			
			@SuppressWarnings("unchecked")
			@Override 
			public void afterExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {
				String indexString = String.valueOf(index);
				Number fillRate = ((Map<String, Number>)additional.getOrDefault(name(), Map.of())).get(indexString);
				if(fillRate == null)
					return ;
				
				Pointer targets = KestrelApi.keson_get_object_item(outputKesons[index].getValue(), "targets");
		        int targets_length = KestrelApi.keson_array_size(targets);
		        
		        for(int jndex = 0; jndex < targets_length; jndex ++) {
		        	Pointer target = KestrelApi.keson_get_array_item(targets, jndex);
					Pointer roi = KestrelApi.keson_detach_item_from_object(target, "roi");
					if(roi != null) 
						continue;
		        	
					
	            	long target_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "id"));
	            	long target_imageid = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "image_id"));
	            	
					Pointer srcTargets = KestrelApi.keson_get_object_item(inTo.getValue(), "targets");
			        int srcTargets_length = KestrelApi.keson_array_size(targets);
			        
	            	for(int kndex = 0; kndex < srcTargets_length; kndex ++) {
	                	Pointer srcTarget = KestrelApi.keson_get_array_item(srcTargets, kndex);
	                	long srcTarget_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(srcTarget, "id"));
	                	long srcTarget_imageid = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(srcTarget, "image_id"));
	                	
	                	if(target_id != srcTarget_id || target_imageid != srcTarget_imageid)
	                		continue;
	                	
	                	KestrelApi.keson_add_item_to_object(target, "roi", KestrelApi.keson_duplicate(KestrelApi.keson_get_object_item(srcTarget, "roi"), 1));
	                	break;
	            	}
		        }
			}
		};
	}
	
	@Bean
	public KestrelInterceptor moveTargetMapInterceptor() {
		return new KestrelInterceptor() {
			
			@Getter
			private int order = Ordered.LOWEST_PRECEDENCE;
			
			@Override 
			public String name() { return "moveTargetMap"; }
			
			@SuppressWarnings("unchecked")
			@Override 
			public void afterExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {
				String indexString = String.valueOf(index);
				Number toIndex = ((Map<String, Number>)additional.getOrDefault("moveTargetMap",  Map.of())).get(indexString);
				
				if(toIndex == null || toIndex.intValue() >= index)
					return;
				
				Pointer src = outputKesons[index].getValue();
				Pointer dest = outputKesons[toIndex.intValue()].getValue();
				
				Pointer srcTargets = KestrelApi.keson_get_object_item(src, "targets");
		    	if(srcTargets == null)
		    		return ;
		    	
		    	Pointer destTargets = KestrelApi.keson_get_object_item(dest, "targets");
		    	if(destTargets == null) {
		    		KestrelApi.keson_add_item_to_object(dest, "targets", KestrelApi.keson_detach_item_from_object(src, "targets"));
		    	}else {
		            int srcTargets_length = KestrelApi.keson_array_size(srcTargets);
		            int destTargets_length = KestrelApi.keson_array_size(destTargets);
		            
		            Map<Long, Long> imageMaxIdMap = new HashMap<Long, Long>();
		            for(int jndex = destTargets_length - 1; jndex >= 0; jndex --) {
		            	Pointer destTarget = KestrelApi.keson_get_array_item(destTargets, jndex);
		            	long destTarget_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(destTarget, "id"));
		            	long destTarget_imageid = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(destTarget, "image_id"));
		            	
		            	Long maxId = imageMaxIdMap.getOrDefault(destTarget_imageid, -1L);
		            	if(destTarget_id > maxId)
		            		imageMaxIdMap.put(destTarget_imageid, destTarget_id);
		            }
		            
		            for(int jndex = srcTargets_length - 1; jndex >= 0; jndex --) {
		            	Pointer movingTarget = KestrelApi.keson_detach_from_array(srcTargets, jndex);

		            	KestrelApi.keson_delete_item_from_object(movingTarget, "id");
		            	
		            	long movingTarget_imageid = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(movingTarget, "image_id"));
		            	Long maxId = imageMaxIdMap.getOrDefault(movingTarget_imageid, -1L) + 1;
		            	imageMaxIdMap.put(movingTarget_imageid, maxId);
		            	
		            	KestrelApi.keson_add_item_to_object(movingTarget, "id", KestrelApi.keson_create_int(maxId));
		            	
		            	KestrelApi.keson_add_item_to_array(destTargets, movingTarget);
		            }
		    	}
			}
		};
	}
}
