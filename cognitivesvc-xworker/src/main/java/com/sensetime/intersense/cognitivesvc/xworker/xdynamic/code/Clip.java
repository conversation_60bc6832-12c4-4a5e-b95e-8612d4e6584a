import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.IntStream;

public class Clip extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    @Getter
    protected final Integer interval = 0;//检一跳三

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final boolean queueMap = true;

    @Getter
    protected final String decoderFormat = "rgb24";

    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";


    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];

        ModelHolder[]        pointers      = prepareModelHolder(handlingList);
        PointerByReference   param_keson   = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);


        for (int index = 0; index < pointers.length; index++) {
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            long now = System.currentTimeMillis();
            /** 执行模型 获取数据*/
            pointers[index].process(inTo.getValue(), outOf);
            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        }

        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }


    @Override
    protected PointerByReference prepareInput(List<BatchItem> handlingList) {
        Pointer[] frames = new Pointer[handlingList.size()];
        Long[] sourceIds = new Long[handlingList.size()];

        for (int index = 0; index < handlingList.size(); index++) {
            if(handlerEntity.getCpuModelDup() > 0)
                frames[index] = handlingList.get(index).getModelRequest().getVideoFrames()[0].getCpuFrame();
            else
                frames[index] = handlingList.get(index).getModelRequest().getVideoFrames()[0].getGpuFrame();
        }

        if(StringUtils.isBlank(handlerEntity.getFlockConfig()))
            return new PointerByReference(KesonUtils.frameToKeson(frames, sourceIds));
        else {
            for (int index = 0; index < handlingList.size(); index++) {
                Object deviceId = handlingList.get(index).getModelRequest().getParameter().get("deviceId");
                if(deviceId == null)
                    sourceIds[index] = (long)index;
                else
                    sourceIds[index] = Utils.keyToContextId(deviceId);
            }

            return new PointerByReference(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), KesonUtils.frameToKeson(frames, sourceIds)));
        }
    }




}
