package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
public class Carbin extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {


    // 抛出原始attribute
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        if (modelResult.getDetectResult() == null || !(modelResult.getDetectResult() instanceof Map))
            return;

        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");

        Map<String, Object> additional = (Map<String, Object>) Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());

        List<Map<String, Object>> targets = (List<Map<String, Object>>) ((Map<String, Object>) modelResult.getDetectResult()).getOrDefault("targets", List.of());
        for (Map<String, Object> target : targets) {
            Map<String, Object> valueResult = new HashMap<String, Object>();
            valueResult.put("detect", target.get("roi"));
            valueResult.put("deviceId", deviceId);

            if (target.containsKey("textline")) {
                int text_image_id = (int) target.get("image_id");
                List<Map<String, Object>> textLines = (List<Map<String, Object>>) target.get("textline");
                Map<String, Object>[] contents = new Map[textLines.size()];

                Float confidence = textLines.stream().map(line -> ((Number) line.get("score")).floatValue()).reduce(Float::sum).get() / textLines.size();
                String contentText = textLines.stream().map(line -> (String) ((Map<String, Object>) line.getOrDefault("content", Map.of())).get("utf8")).filter(StringUtils::isNotBlank).collect(Collectors.joining());
                contents[text_image_id] = Map.of("score", confidence, "content", Map.of("utf8", contentText));

                valueResult.put("textline", contents);
            }

            if (target.containsKey("confidence"))
                valueResult.put("confidence", target.get("confidence"));

            if (target.containsKey("label"))
                valueResult.put("targetType", target.get("label"));
            else
                valueResult.put("targetType", annotatorName());

            Object roiHits = target.get("roi_hits");
            if (roiHits != null && roiHits != SCREEN)
                valueResult.put("rois", roiHits);
            List<Map<String, Object>> attributeList = new ArrayList<Map<String, Object>>();
            valueResult.put("attributes", attributeList);

            Map<String, Object> attributes = target.entrySet().stream()
                    .filter(entry -> entry.getKey().startsWith("attribute"))
                    .map(entry -> ((Map<String, Object>) entry.getValue()).entrySet())
                    .flatMap(Set::stream)
                    .collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue(), (l, r) -> l));

            if (MapUtils.isEmpty(attributes)) {
                attributeList.add(Map.of("key", target.getOrDefault("key", annotatorName()), "value", target.getOrDefault("value", annotatorName()), "confidence", ((Number) target.getOrDefault("confidence", -1f)).floatValue()));
            } else {
                for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                    Map<String, Number> attribute = (Map<String, Number>) entry.getValue();
                    attribute.forEach((value, conf) -> {
                        attributeList.add(Map.of("key", entry.getKey(), "value", value, "confidence", conf));
                    });
                }
            }

            valueResult.put("attributes", attributeList);

            for (KestrelInterceptor interceptor : interceptors)
                interceptor.postProcessOutput(additional, target, valueResult);

            outputResult.add(valueResult);
        }
        // 不存图
        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
        modelResult.setOutputResult(outputResult);
        postProcessOutputValue(modelResult);
    }

    @Override
    protected void postProcessOutputValue(ModelResult modelResult) {
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        if (StringUtils.isBlank(deviceId) || processor == null)
            return;
        // 根据配置阈值来获得属性抛出结果 二分类 那 yes的conf 比较
        // if yes > threshold output yes
        // else output no
        Float thresholdOfYes = processor.getThreshold();

        List<Map<String, Object>> outputResult = (List<Map<String, Object>>) modelResult.getOutputResult();
        Map<String, List<Map<String, Object>>> roiOutputResult = new HashMap<String, List<Map<String, Object>>>();
        for (Map<String, Object> valueResult : outputResult) {

            List<Map<String, Object>> attributeList = (List<Map<String, Object>>) valueResult.get("attributes");
            //attributeList.iterator();
            AtomicBoolean yes = new AtomicBoolean(true);
            attributeList.forEach(attribute -> {
                if (!StringUtils.equals((String) attribute.get("key"), "label_name")) return;
                if (!StringUtils.equals((String) attribute.get("value"), "yes")) return;
                Double conf = (Double) attribute.get("confidence");
                if (conf.floatValue() >= thresholdOfYes) {
                    yes.set(true);
                } else {
                    yes.set(false);
                }

            });

            Iterator<Map<String, Object>> mapIterator = attributeList.iterator();
            while (mapIterator.hasNext()) {
                Map<String, Object> iterm = mapIterator.next();
                if (!StringUtils.equals((String) iterm.get("key"), "label_name")) return;
                if (yes.get()) {
                    if (StringUtils.equals((String) iterm.get("value"), "no")) {
                        mapIterator.remove();
                    }
                } else {
                    if (StringUtils.equals((String) iterm.get("value"), "yes")) {
                        mapIterator.remove();
                    }
                }
            }
        }
        modelResult.setOutputResult(outputResult);
    }
}
