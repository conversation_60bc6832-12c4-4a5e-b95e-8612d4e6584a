package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import org.apache.commons.collections.CollectionUtils;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Library;
import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class IdCard extends DynamicXModelHandler {
    private static final Pointer input = KesonUtils.stringToKeson("{}").getValue();
    
    private String[] detainedKeps;
    
    private PointerByReference[] modelZooHandles;
    
    private PointerByReference[] modelHandles;
    
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if(status != 0)
            return new PointerByReference[0];    

        PointerByReference ocr_results = new PointerByReference();
        
        Memory frames = new Memory(Native.POINTER_SIZE * handlingList.size());        
        for(int index = 0; index < handlingList.size(); index ++) {
            VideoFrame videoFrame = handlingList.get(index).getModelRequest().getVideoFrames()[0];
            frames.setPointer(index * Native.POINTER_SIZE , handlerEntity.getCpuModelDup() == 0 ? videoFrame.getGpuFrame() : videoFrame.getCpuFrame());
        }

        PointerByReference ocr_handle = modelHandles[ThreadLocalRandom.current().nextInt(modelHandles.length)];
        synchronized(ocr_handle){
            Libkestrel_ocrLibrary.INSTANCE.kestrel_ocr_process(ocr_handle.getValue(), input, frames, handlingList.size(), ocr_results);
        }
        
        return new PointerByReference[] {ocr_results};
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void readModelResult(ModelResult modelResult) {
        super.readModelResult(modelResult);
       
        List<Map<String, Object>> targets = ((List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).get("targets"));
        if(CollectionUtils.isNotEmpty(targets))
            modelResult.setDetectResult(targets.get(0).get("object_array"));
        else
            modelResult.setDetectResult(null);
    }
    
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        return modelResult.getDetectResult() != null;
    }
    
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        modelResult.setOutputResult(modelResult.getDetectResult());
    }
    
    @PostConstruct
    @Override
    public synchronized void initialize() {
        super.initialize();
        
        detainedKeps = new String[3];
        for(int index = 0; index < 3; index ++)
            detainedKeps[index] = KestrelApi.kestrel_plugin_load(handlerEntity.getAttacheds()[index + 1], "");
        
        modelZooHandles = new PointerByReference[Math.max(1, handlerEntity.getCpuModelDup())];
        modelHandles = new PointerByReference[Math.max(1, handlerEntity.getCpuModelDup())];
        
        for(int index = 0; index < handlerEntity.getCpuModelDup(); index ++) {
            modelZooHandles[index] = new PointerByReference();
            modelHandles[index] = new PointerByReference();
            
            Libkestrel_ocrLibrary.INSTANCE.kestrel_ocr_modelzoo_handle_create(config(), modelZooHandles[index]);
            Libkestrel_ocrLibrary.INSTANCE.kestrel_ocr_handle_create(config(), modelZooHandles[index].getValue(), modelHandles[index]);
        }
        
        log.info("IdCard initializing : " + config().trim());
    }
    
    @PreDestroy
    @Override
    public synchronized void destroy() {
        Initializer.bindDeviceOrNot();
        
        stopHandle();
        
        for(int index = 0; index < handlerEntity.getCpuModelDup(); index ++) {
            Libkestrel_ocrLibrary.INSTANCE.kestrel_ocr_modelzoo_handle_destroy(modelZooHandles[index]);
            Libkestrel_ocrLibrary.INSTANCE.kestrel_ocr_handle_destroy(modelHandles[index]);
        }
        
        for(String kep : detainedKeps)
        	KestrelApi.kestrel_plugin_unload(kep);
        
        super.destroy();
        log.info("IdCard destroying.");
    }
    
    public interface Libkestrel_ocrLibrary extends Library {
        public static final String JNA_LIBRARY_NAME = "kestrel_ocr";
        public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Libkestrel_ocrLibrary.JNA_LIBRARY_NAME);
        public static final Libkestrel_ocrLibrary INSTANCE = (Libkestrel_ocrLibrary)Native.load(Libkestrel_ocrLibrary.JNA_LIBRARY_NAME, Libkestrel_ocrLibrary.class);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_modelzoo_handle_create(const char*, kestrel_modelzoo_handle*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:51</i>
         */
        int kestrel_ocr_modelzoo_handle_create(String config_json, PointerByReference modelzoo);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_modelzoo_handle_destroy(kestrel_modelzoo_handle*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:58</i>
         */
        int kestrel_ocr_modelzoo_handle_destroy(PointerByReference modelzoo);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_handle_create(const char*, kestrel_modelzoo_handle, kestrel_ocr_handle*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:66</i>
         */
        int kestrel_ocr_handle_create(String config_json, Pointer modelzoo, PointerByReference ocr_handle);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_handle_destroy(kestrel_ocr_handle*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:73</i>
         */
        int kestrel_ocr_handle_destroy(PointerByReference ocr_handle);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_process(kestrel_ocr_handle, const keson, const const kestrel_frame_t**, size_t, keson*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:83</i>
         */
        int kestrel_ocr_process(Pointer ocr_handle, Pointer input_json, Pointer frames, int len, PointerByReference ocr_results);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_prepare_input_for_template_match(keson*, size_t, keson, const template_match_obj_t*, size_t)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:96</i>
         */
        int kestrel_ocr_prepare_input_for_template_match(Pointer in_out_json, int image_len, Pointer template_dict, Pointer template_config, int config_count);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_process_from_to(kestrel_ocr_handle, const char*, const char*, const char*, const const kestrel_frame_t**, size_t, const keson, keson*, int)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:114</i>
         */
        int kestrel_ocr_process_from_to(Pointer ocr_handle, String app_name, String start_op, String end_op, Pointer frames, int len, Pointer input_json, Pointer ocr_results, int is_origin_coord);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_process_from_to(kestrel_ocr_handle, const char*, const char*, const char*, const const kestrel_frame_t**, size_t, const keson, keson*, int)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:114</i>
         */
        int kestrel_ocr_process_from_to(Pointer ocr_handle, Pointer app_name, Pointer start_op, Pointer end_op, Pointer frames, int len, Pointer input_json, Pointer ocr_results, int is_origin_coord);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_convert_output_to_former_schema(const keson, keson**, int*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:127</i>
         */
        int kestrel_ocr_convert_output_to_former_schema(Pointer in, Pointer out[], IntByReference out_target_num);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_fetch_feature_from_keson(const keson, ocr_object_type_t**, size_t*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:136</i>
         */
        int kestrel_ocr_fetch_feature_from_keson(Pointer input, ocr_object_type_t.ByReference out_feature_items[], Pointer out_feature_item_num);
        /**
         * @return 错误码<br>
         * Original signature : <code>void kestrel_ocr_result_release_object_types(const ocr_object_type_t*, size_t)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:145</i>
         */
        void kestrel_ocr_result_release_object_types(ocr_object_type_t types, int len);
        /**
         * @return<br>
         * Original signature : <code>void kestrel_ocr_result_array_release(keson*, size_t)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:152</i>
         */
        void kestrel_ocr_result_array_release(Pointer ocr_results, int result_num);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_reorg_keson_by_type(keson, const ocr_object_type_t*, size_t, ocr_reorg_result_t**, size_t*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:162</i>
         */
        int kestrel_ocr_reorg_keson_by_type(Pointer ocr_result_in, ocr_object_type_t types, int len, Pointer ocr_result_out, Pointer len_out);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_release_reorg_result(ocr_reorg_result_t**, size_t)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:170</i>
         */
        int kestrel_ocr_release_reorg_result(Pointer ocr_result, int len);
        /**
         * Original signature : <code>int kestrel_ocr_merge_keson(const keson*, size_t, keson*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:173</i>
         */
        int kestrel_ocr_merge_keson(Pointer keson_result_in[], int len, Pointer out_keson);
        /**
         * @return 错误码<br>
         * Original signature : <code>int kestrel_ocr_reorg_keson_to_ips_schema(keson*, size_t, keson**, size_t*)</code><br>
         * <i>native declaration : include/libkestrel_ocr.h:183</i>
         */
        int kestrel_ocr_reorg_keson_to_ips_schema(Pointer keson_result_in, int len, Pointer out_keson[], Pointer out_target_num);
    }
    
    private final String config() {
        String attacheds[] = handlerEntity.getAttacheds();
        
        return "{" + 
                "    \"apps\": [" + 
                "        {" + 
                "            \"name\": \"IDCardObjDetect\"," + 
                "            \"type\": \"IDCardObjDetect\"," + 
                "            \"pipeline\": [" + 
                "                {" + 
                "                    \"name\": \"ObjDetect\"," + 
                "                    \"operation\": \"ObjDetect\"," + 
                "                    \"model\": \"idcard_objdetect\"" + 
                "                }," + 
                "                {" + 
                "                    \"name\": \"Rotate\"," + 
                "                    \"operation\": \"Rotate\"," + 
                "                    \"bottom\": [" + 
                "                        \"ObjDetect\"" + 
                "                    ]" + 
                "                }" + 
                "            ]" + 
                "        }," + 
                "        {" + 
                "            \"name\": \"IDCardFront\"," + 
                "            \"type\": \"IDCardFront\"," + 
                "            \"pipeline\": [" + 
                "                {" + 
                "                    \"name\": \"Filter\"," + 
                "                    \"operation\": \"Filter\"" + 
                "                }," + 
                "                {" + 
                "                    \"name\": \"Align\"," + 
                "                    \"operation\": \"Align\"," + 
                "                    \"model\": \"idcard_front_align\"," + 
                "                    \"params\": {" + 
                "                        \"transform_type\": \"perspective\"," + 
                "                        \"fix\": {" + 
                "                            \"width\": 512," + 
                "                            \"height\": 320," + 
                "                            \"roi\": {" + 
                "                                \"#keson_code\": \"RCT\"," + 
                "                                \"left\": 0," + 
                "                                \"top\": 0," + 
                "                                \"width\": 512," + 
                "                                \"height\": 320" + 
                "                            }" + 
                "                        }," + 
                "                        \"points_index\": [" + 
                "                            0," + 
                "                            1," + 
                "                            2," + 
                "                            3" + 
                "                        ]" + 
                "                    }," + 
                "                    \"bottom\": [" + 
                "                        \"Filter\"" + 
                "                    ]" + 
                "                }," + 
                "                {" + 
                "                    \"name\": \"TextRecog\"," + 
                "                    \"operation\": \"TextRecog\"," + 
                "                    \"model\": \"idcard_textrecognition\"," + 
                "                    \"bottom\": [" + 
                "                        \"Align\"" + 
                "                    ]," + 
                "                    \"params\": {" + 
                "                        \"expand_ratio_h\": 0.2," + 
                "                        \"expand_ratio_w\": 0.56" + 
                "                    }" + 
                "                }," + 
                "                {" + 
                "                    \"name\": \"Correct\"," + 
                "                    \"operation\": \"Correct\"," + 
                "                    \"model\": \"idcard_correct\"," + 
                "                    \"bottom\": [" + 
                "                        \"TextRecog\"" + 
                "                    ]" + 
                "                }" + 
                "            ]," + 
                "            \"bottom\": [" + 
                "                \"IDCardObjDetect\"" + 
                "            ]" + 
                "        }," + 
                "        {" + 
                "            \"name\": \"IDCardBack\"," + 
                "            \"type\": \"IDCardBack\"," + 
                "            \"pipeline\": [" + 
                "                {" + 
                "                    \"name\": \"Filter\"," + 
                "                    \"operation\": \"Filter\"" + 
                "                }," + 
                "                {" + 
                "                    \"name\": \"Align\"," + 
                "                    \"operation\": \"Align\"," + 
                "                    \"model\": \"idcard_back_align\"," + 
                "                    \"params\": {" + 
                "                        \"transform_type\": \"perspective\"," + 
                "                        \"fix\": {" + 
                "                            \"width\": 512," + 
                "                            \"height\": 320," + 
                "                            \"roi\": {" + 
                "                                \"#keson_code\": \"RCT\"," + 
                "                                \"left\": 0," + 
                "                                \"top\": 0," + 
                "                                \"width\": 512," + 
                "                                \"height\": 320" + 
                "                            }" + 
                "                        }," + 
                "                        \"points_index\": [" + 
                "                            0," + 
                "                            1," + 
                "                            2," + 
                "                            3" + 
                "                        ]" + 
                "                    }," + 
                "                    \"bottom\": [" + 
                "                        \"Filter\"" + 
                "                    ]" + 
                "                }," + 
                "                {" + 
                "                    \"name\": \"TextRecog\"," + 
                "                    \"operation\": \"TextRecog\"," + 
                "                    \"model\": \"idcard_textrecognition\"," + 
                "                    \"bottom\": [" + 
                "                        \"Align\"" + 
                "                    ]," + 
                "                    \"params\": {" + 
                "                        \"expand_ratio_h\": 0.2," + 
                "                        \"expand_ratio_w\": 0.56" + 
                "                    }" + 
                "                }," + 
                "                {" + 
                "                    \"name\": \"Correct\"," + 
                "                    \"operation\": \"Correct\"," + 
                "                    \"model\": \"idcard_correct\"," + 
                "                    \"bottom\": [" + 
                "                        \"TextRecog\"" + 
                "                    ]" + 
                "                }" + 
                "            ]," + 
                "            \"bottom\": [" + 
                "                \"IDCardObjDetect\"" + 
                "            ]" + 
                "        }," + 
                "        {" + 
                "            \"name\": \"Merge\"," + 
                "            \"type\": \"Merge\"," + 
                "            \"pipeline\": [" + 
                "                {" + 
                "                    \"name\": \"Merge\"," + 
                "                    \"operation\": \"Merge\"" + 
                "                }" + 
                "            ]," + 
                "            \"bottom\": [" + 
                "                \"IDCardFront\"," + 
                "                \"IDCardBack\"" + 
                "            ]" + 
                "        }" + 
                "    ]," + 
                "    \"models\": {" + 
                "        \"idcard_objdetect\": {" + 
                "            \"plugin\": \"harpy\"," + 
                "            \"plugin_lib\": \"" + attacheds[1] + "\"," + 
                "            \"model\": \"" + attacheds[4] + "\"" + 
                "        }," + 
                "        \"idcard_front_align\": {" + 
                "            \"plugin\": \"aligner\"," + 
                "            \"plugin_lib\": \"" + attacheds[2] + "\"," + 
                "            \"model\": \"" + attacheds[5] + "\"" + 
                "        }," + 
                "        \"idcard_back_align\": {" + 
                "            \"plugin\": \"aligner\"," + 
                "            \"plugin_lib\": \"" + attacheds[2] + "\"," + 
                "            \"model\": \"" + attacheds[6] + "\"" + 
                "        }," + 
                "        \"idcard_textrecognition\": {" + 
                "            \"plugin\": \"textrecognition\"," + 
                "            \"plugin_lib\": \"" + attacheds[3] + "\"," + 
                "            \"model\": \"" + attacheds[7] + "\"," + 
                "            \"max_batch_size\": 8" + 
                "        }," + 
                "        \"idcard_correct\": {" + 
                "            \"plugin\": \"IDCardCorrect\"," + 
                "            \"model\": \"" + attacheds[8] + "\"," + 
                "            \"is_custom\": true" + 
                "        }" + 
                "    }" + 
                "}";
    }
    
    public static class ocr_object_type_t extends Structure {
        public int big_image_id;
        public int small_image_id;
        /** C type : const char* */
        public Pointer type;
        /**
         * user extra type, will be filled into keson `extra_type` field<br>
         * C type : const char*
         */
        
        public Pointer extra_type;
        /**
         * this field not takes ownership<br>
         * C type : keson
         */
        public Pointer feature;
        
        public float feature_score;
        
        public ocr_object_type_t() {
            super();
        }
        
        protected List<String> getFieldOrder() {
            return Arrays.asList("big_image_id", "small_image_id", "type", "extra_type", "feature", "feature_score");
        }
        
        public ocr_object_type_t(Pointer peer) {
            super(peer);
        }
        
        public static class ByReference extends ocr_object_type_t implements Structure.ByReference { };
        
        public static class ByValue extends ocr_object_type_t implements Structure.ByValue { };
    }
    
    public static class ocr_reorg_result_t extends Structure {
        /** C type : char*
         */
        public Pointer type;
        
        /** C type : keson */
        public Pointer ocr_result;
        
        public int target_count_in_result;
        
        public ocr_reorg_result_t() {
            super();
        }
        
        protected List<String> getFieldOrder() {
            return Arrays.asList("type", "ocr_result", "target_count_in_result");
        }
        
        public ocr_reorg_result_t(Pointer peer) {
            super(peer);
        }
        
        public static class ByReference extends ocr_reorg_result_t implements Structure.ByReference { };
        
        public static class ByValue extends ocr_reorg_result_t implements Structure.ByValue { };
    }
    
    public static class template_match_obj_t extends Structure {
        public int big_image_id;
        
        /** if it's -1, then apply to all objs of big_image_id */
        public int small_image_id;
        
        /** C type : const char* */
        public Pointer template_name;
        
        public template_match_obj_t() {
            super();
        }
        protected List<String> getFieldOrder() {
            return Arrays.asList("big_image_id", "small_image_id", "template_name");
        }
        public template_match_obj_t(Pointer peer) {
            super(peer);
        }
        
        public static class ByReference extends template_match_obj_t implements Structure.ByReference { };
        
        public static class ByValue extends template_match_obj_t implements Structure.ByValue { };
    }
}
