package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;

public class AppTag extends DynamicXModelHandler{
    
    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        float threshold = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getThreshold(), 0.0f);
        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)Objects.requireNonNullElse(modelResult.getDetectResult(), Map.of())).getOrDefault("targets", List.of());
        
        Iterator<Map<String, Object>> its = targets.iterator();
        while(its.hasNext()) {
            Map<String, Object> target = its.next();
            List<Map<String, Object>> tasks = (List<Map<String, Object>>)target.getOrDefault("tasks", List.of());
            
            Iterator<Map<String, Object>> its2 = tasks.iterator();
            while(its2.hasNext()) {
                Map<String, Object> task = its2.next();
                List<Map<String, Object>> results = (List<Map<String, Object>>)task.getOrDefault("results", List.of());
                
                Iterator<Map<String, Object>> its3 = results.iterator();
                while(its3.hasNext()) {
                    Map<String, Object> result = its3.next();
                    
                    Number cls_conf = (Number)result.getOrDefault("cls_conf", 1.0d);
                    Number det_conf = (Number)result.getOrDefault("det_conf", 1.0d);
                    
                    if(cls_conf.floatValue() < threshold || det_conf.floatValue() < threshold)
                        its3.remove();
                }
                
                if(results.isEmpty())
                    its2.remove();
            }
            
            if(tasks.isEmpty())
                its.remove();
        }
        
        return !targets.isEmpty();
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
        for(Map<String, Object> target : targets) {            
            List<Map<String, Object>> tasks = (List<Map<String, Object>>)target.getOrDefault("tasks", List.of());
            outputResult.addAll(tasks);
        }
        modelResult.setOutputResult(outputResult);
    }
}