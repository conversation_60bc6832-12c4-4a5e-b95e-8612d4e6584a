//package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;
//
//import java.util.*;
//import java.util.List;
//import java.util.function.Function;
//
//import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
//import com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code.CrossLinesFaceReg;
//import com.sun.jna.Pointer;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
//import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
//import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
//import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
//import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
//import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
//import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
//import com.sun.jna.ptr.PointerByReference;
//import lombok.Getter;
//import lombok.extern.slf4j.Slf4j;
//
//
//@Slf4j
//public class VideoQuality extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
//
//    @Getter
//    protected final Boolean blocking = false;
//
//    @Getter
//    protected final String decoderFormat = "rgb24";
//
//    @Getter
//    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些
//
//    @Getter
//    protected final String frameBufferStrategy = "smart";
//
//    @Getter
//    protected final Integer interval = 0;
//
//    @Getter
//    protected final boolean queueMap = true;
//
//
//    @Override
//    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
//        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
//        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons)[0], imageIds);
//
//        for (int index = 0; index < handlingList.size(); index++) {
//            BatchItem item = handlingList.get(index);
//            item.setKeson(sub_kesons[index]);
//        }
//    }
//
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public boolean validateOutputValue(ModelResult modelResult) {
//        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();
//        if(MapUtils.isEmpty(detectResult))
//            return false;
//
//        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.get("targets");
//        Iterator<Map<String, Object>> its = targets.iterator();
//
//
//        return !targets.isEmpty();
//    }
//
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public void buildOutputValue(ModelResult modelResult) {
//        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();
//        if(MapUtils.isEmpty(detectResult))
//            return ;
//
//        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.get("targets");
//        log.info("buildOutputValue{}", targets);
//        modelResult.setOutputResult(targets);
//
//    }
//
//
//    @SuppressWarnings({ "unchecked", "rawtypes" })
//    private static final Function<Object, Map> function = e -> {
//        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();
//
//        if(e != null)
//            for(Map<String, Object> param : (List<Map<String, Object>>)e) {
//                if(!"mapping".equals(param.get("type")))
//                    continue;
//
//                Integer roiIndex = (Integer)param.get("roiIndex");
//                if(roiIndex != null)
//                    result.put(roiIndex, param);
//            }
//
//        return result;
//    };
//
//    @Override
//    public synchronized void onWorkerEvent(XworkerEvent e) {
//        if(e instanceof XworkerStreamStartedEvent) {
//            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
//            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
//            startContext(contextId, event.getInfra());
//        }else if(e instanceof XworkerStreamClosedEvent) {
//            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
//            long contextId = Utils.keyToContextId(event.getDeviceId());
//
//            try { Thread.sleep(1000); } catch (InterruptedException x) { }
//            stopContext(contextId);
//        }
//    }
//
//    private void startContext(long contextId, VideoStreamInfra infra) {
//        log.info("falcon [" + getClass().getSimpleName() + "] start context [" + contextId + "].");
//
//    }
//
//    private void stopContext(long contextId) {
//        holders[0].controlForRemoveSource(contextId);
//        log.info("falcon [" + getClass().getSimpleName() + "] stop context [" + contextId + "].");
//
//    }
//}