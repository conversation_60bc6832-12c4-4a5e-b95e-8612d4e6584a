package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.List;

import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.ptr.PointerByReference;

/**
 * 动态代码模板类
 */
public class XTemplateModel extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
	
	/**
	 * 在初始化job线程执行，XStoreHandler的addDynamicXHandler
	 */
    @Override
	public synchronized void initialize() {
		super.initialize();
	}
    
    /**
	 * 在初始化job线程执行，XStoreHandler的removeDynamicXHandler
	 */
	@Override
	public synchronized void destroy() {
		super.destroy();
	}

	/**
	 * 在动态模型内部的双线程组里面执行， AbstractXModelHandler的run
	 */
	@Override
	protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
		return super.batch_extract_xmodel_asap(handlingList);
	}

	/**
	 * 在动态模型内部的双线程组里面执行， AbstractXModelHandler的run
	 */
	@Override
	protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
		super.batch_handle_extract_result(handlingList, outputKesons);
	}

	/**
	 * 在执行线程跑，关注XSenseyexEventHandler的handleHighRateEvent函数
	 */
	@Override
	public ModelResult handle(ModelRequest modelRequest) {
		return super.handle(modelRequest);
	}

	@Override
	public void readModelResult(ModelResult modelResult) {
		super.readModelResult(modelResult);
	}

	@Override
    public boolean validateOutputValue(ModelResult modelResult) {
        return super.validateOutputValue(modelResult);
    }
    
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        super.buildOutputValue(modelResult);
    }

	@Override
	public void releaseModelResult(ModelResult modelResult) {
		super.releaseModelResult(modelResult);
	}
	/**
	 * 在解码流所在线程中执行。
	 */
//	@Override
//	public List<Drawing> draw(ModelResult modelResult) {
//		return DynamicXModelHandler.VideoRecorderAccessor.draw(modelResult);
//	}
}