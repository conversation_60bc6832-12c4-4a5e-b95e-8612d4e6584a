import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Scalar;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.awt.*;
import java.io.File;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
@SuppressWarnings("unused")
public class CrossLinesFaceReg extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    @Getter
    protected final Integer interval = 0;//检一跳三

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final boolean queueMap = true;

    @Getter
    protected final String decoderFormat = "rgb24";

    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";

    protected final ConcurrentHashMap<Long, Map<String, Track>> trackingMap = new ConcurrentHashMap<Long, Map<String, Track>>();
    protected ConcurrentHashMap<Long, Map<String, String>> deviceRoiMapCross = new ConcurrentHashMap<Long, Map<String, String>>();

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
//        String[] imageIdsDevice = handlingList.stream().map(item ->item.getModelRequest().getParameter().get("deviceId")).toArray(String[]::new);
//
//        log.info("batch_handle_extract_result{},{},{},{}", annotatorName(), handlingList.size(),imageIdsDevice, outputKesons.length);

//        PointerByReference[] tryReformFlockKeson = KesonUtils.tryReformFlockKeson(outputKesons[0]);
//
//        log.info("DetectResult_tryReformFlockKeson{},{}",tryReformFlockKeson.length, KesonUtils.kesonToJson(tryReformFlockKeson[0]));
//        PointerByReference[] sub_kesons = outputKesons;
//        for (int index = 0; index < handlingList.size(); index++) {
//            BatchItem item = handlingList.get(index);
//            item.setKeson(sub_kesons[index]);
//        }

        //log.info("outputKesons{}", KesonUtils.kesonToJson(outputKesons[0]));

        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds, "source_id");

        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();

            if (sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            handlingList.get(index).setKeson(new PointerByReference(array));
        }
        KesonUtils.kesonDeepDelete(outputKesons);
    }

    /**
     * 按照 函数包含的模型 顺序的跑一边
     */
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];

        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);

        Collections.shuffle(handlingList);
        String deviceId = (String) handlingList.get(0).getModelRequest().getParameter().get("deviceId");
        VideoStreamInfra deviceInfra = (VideoStreamInfra) handlingList.get(0).getModelRequest().getParameter().get("deviceInfra");

        Long streamSourceId = Utils.keyToContextId(deviceId);

        Map<String, Object> additional = (Map<String, Object>) Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
        for (int index = 0; index < pointers.length; index++) {
            long now = System.currentTimeMillis();

            if (index == 0) {
                Map<String, String> roiCross = deviceRoiMapCross.get(streamSourceId);
                Integer[][][] proi = handlingList.get(index).getModelRequest().getProcessor().getRoi();
                CognitiveEntity.Processor processor = handlingList.get(index).getModelRequest().getProcessor();
                Polygon[] polygons = processor.fetchPolygons();
                if (ArrayUtils.isEmpty(polygons))
                    polygons = new Polygon[]{new Polygon(new int[]{0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[]{0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};

                Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);

                List<Integer[][]> rois = new ArrayList<>();
                List<Integer[][]> roisDirection = new ArrayList<>();
                List<List<Number>> roisLines = new ArrayList<>();
                List<Integer> positionTypeList = new ArrayList<>();
                for (int indexs = 0; indexs < polygons.length; indexs++) {
                    Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
                    if (extra.isEmpty())
                        continue;
                    Integer[][] singleRoi = proi[indexs];
                    if (singleRoi.length <= 0)
                        continue;
                    if (extra.containsKey("crossDot")) {
                        roisDirection.add(singleRoi);
                        List<Number> crossDot = (List<Number>) extra.getOrDefault("crossDot", List.of());
//                        String crossDirection = (String)extra.get("crossDirection");
//                        Number endPointX = 0;
//                        Number endPointY = 0;
//
//                        double[][] polygon = new double[singleRoi.length][];
//                        for (int i = 0; i < singleRoi.length; i++) {
//                            polygon[i] = new double[]{singleRoi[i][0], singleRoi[i][1]};
//                        }
//                        double[] newPoint;
//                        if (singleRoi.length > 2) {
//                            // Given shape is a polygon
//                            double[] pointDbl = {crossDot.get(0).intValue(), crossDot.get(1).intValue()};
//                            newPoint = calculatePointForPolygon(pointDbl, polygon, crossDirection);
//                        } else {
//                            // Given shape is a line segment
//                            double[] pointDbl = {crossDot.get(0).intValue(), crossDot.get(1).intValue()};
//                            newPoint = calculatePointForLine(pointDbl, polygon, crossDirection);
//                        }
//                        int[] newPointInt = new int[]{(int) Math.round(newPoint[0]), (int) Math.round(newPoint[1])};
//
//                        crossDot.add(newPointInt[0]);
//                        crossDot.add(newPointInt[1]);

                        roisLines.add(crossDot);
                        positionTypeList.add(((Number) extra.getOrDefault("positionType", 1)).intValue());
                    } else {//不用了
                        rois.add(singleRoi);
                    }
                }
                String policyRoiString = JSON.toJSONString(toArrayStringRoiList(rois));
                String policyRoiStringDirection = JSON.toJSONString(toArrayStringRoiList(roisDirection));
                String policyRoiStringLines = JSON.toJSONString((roisLines));
                String policyPositionTypeJson = JSON.toJSONString((positionTypeList));

                if (roiCross.get(ROISTRING) == null) {
                    roiCross.put(ROISTRING, policyRoiString);
                    updateRoi(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString(), positionTypeList);
                } else {
                    String oldRoiString = roiCross.get(ROISTRING);
                    if (!oldRoiString.equals(policyRoiString)) {
                        roiCross.put(ROISTRING, policyRoiString);
                        updateRoi(toArrayStringRoiList(rois), pointers[index].pointers[0], streamSourceId.toString(), positionTypeList);
                    }
                }
                //positiontype变化
                if (roiCross.get(POSITIONTYPE) == null) {
                    roiCross.put(POSITIONTYPE, policyPositionTypeJson);
                    updatePositionType(toArrayStringRoiList(roisDirection), pointers[index].pointers[0], deviceInfra, positionTypeList);
                } else {
                    String oldRoiString = roiCross.get(POSITIONTYPE);
                    if (!oldRoiString.equals(policyPositionTypeJson)) {
                        roiCross.put(POSITIONTYPE, policyPositionTypeJson);
                        updatePositionType(toArrayStringRoiList(roisDirection), pointers[index].pointers[0], deviceInfra, positionTypeList);
                    }
                }
                if (roiCross.get(DIRECTION) == null && roiCross.get(LINESTRING) == null) {
                    roiCross.put(DIRECTION, policyRoiStringDirection);
                    roiCross.put(LINESTRING, policyRoiStringLines);
                    updateRoiAll(toArrayStringRoiList(roisDirection), roisLines, pointers[index].pointers[0], deviceInfra);
                } else {
                    String oldRoiString2 = roiCross.get(DIRECTION);
                    if (!oldRoiString2.equals(policyRoiStringDirection)) {
                        roiCross.put(DIRECTION, policyRoiStringDirection);
                        updateRoiAll(toArrayStringRoiList(roisDirection), roisLines, pointers[index].pointers[0], deviceInfra);
                    }
                    String oldRoiString3 = roiCross.get(LINESTRING);
                    if (!oldRoiString3.equals(policyRoiStringLines)) {
                        roiCross.put(LINESTRING, policyRoiStringLines);
                        updateRoiAll(toArrayStringRoiList(roisDirection), roisLines, pointers[index].pointers[0], deviceInfra);
                    }
                }
                monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
            }

            /** 执行模型 获取数据*/
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            pointers[index].process(inTo.getValue(), outOf);
        }
        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }

    private void updateRoiAll(String[] json1, List<List<Number>> roi3, Pointer pipelinePoint, VideoStreamInfra deviceInfra) {

        JSONArray json2 = JSONArray.parseArray(roi3.toString());
        JSONArray json3 = new JSONArray();
        for (int i = 0; i < json1.length; i++) {
            JSONArray lines = new JSONArray();
            String[] lineArr = json1[i].replaceAll("\\[|\\]", "").split(",");
            int numOfPoints = lineArr.length / 2;
            if (numOfPoints == 2) {
                JSONObject line = new JSONObject();
                line.put("x1", Integer.parseInt(lineArr[0].trim()));
                line.put("y1", Integer.parseInt(lineArr[1].trim()));
                line.put("x2", Integer.parseInt(lineArr[2].trim()));
                line.put("y2", Integer.parseInt(lineArr[3].trim()));
                lines.add(line);
            } else {
                for (int j = 0; j < numOfPoints - 1; j++) {
                    JSONObject line = new JSONObject();
                    if (j == numOfPoints - 1) {//无需闭环
//                        line.put("x1", Integer.parseInt(lineArr[j * 2].trim()));
//                        line.put("y1", Integer.parseInt(lineArr[j * 2 + 1].trim()));
//                        line.put("x2", Integer.parseInt(lineArr[0].trim()));
//                        line.put("y2", Integer.parseInt(lineArr[1].trim()));
                    } else {
                        line.put("x1", Integer.parseInt(lineArr[j * 2].trim()));
                        line.put("y1", Integer.parseInt(lineArr[j * 2 + 1].trim()));
                        line.put("x2", Integer.parseInt(lineArr[j * 2 + 2].trim()));
                        line.put("y2", Integer.parseInt(lineArr[j * 2 + 3].trim()));
                    }
                    lines.add(line);
                }
            }
            JSONArray positiveDirection = new JSONArray();
            JSONArray directionArr = json2.getJSONArray(i);
            for (int k = 0; k < directionArr.size(); k += 4) {
                JSONObject point = new JSONObject();
                point.put("x1", directionArr.get(k));
                point.put("y1", directionArr.get(k + 1));
                point.put("x2", directionArr.get(k + 2));
                point.put("y2", directionArr.get(k + 3));
                positiveDirection.add(point);
            }
            JSONObject data = new JSONObject();
            data.put("roi_id", i);
            data.put("positive_direction", positiveDirection.getJSONObject(0));
            data.put("border_offset", 506);
            data.put("lines", lines);
            json3.add(data);
        }
        String crossLines = " {\n" +
                "   \"streams\": [\n" +
                "         {\n" +
                "          \"name\": \"cross_line_pipeline\",\n" +
                "          \"modules\": [\n" +
                "                                              {\n" +
                "                    \"name\": \"abg_cross_line\",\n" +
                "                     \"source_id\": 49651,\n" +
                "                    \"type\": \"AbgStudioCrossLine\",\n" +
                "                    \"inputs\": [\n" +
                "                        \"tracked_targets\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"cross_line_targets\"\n" +
                "                    ],\n" +
                "                    \"config\": {\n" +
                "                        \"image_width\": " + ((deviceInfra.getRtspWidth() > 0) ? deviceInfra.getRtspWidth() : 1920) + ",\n" +
                "                        \"image_height\": " + ((deviceInfra.getRtspHeight() > 0) ? deviceInfra.getRtspHeight() : 1080) + ",\n" +
                "                        \"turn_on\": true,\n" +
                "                        \"rois\": " + json3.toJSONString() +
                "                    }\n" +
                "                }" +
                "             ]\n" +
                "          }\n" +
                "      ]\n" +
                "  }";

        Long sourceId = Utils.keyToContextId(deviceInfra.getDeviceId());

        String controlPipeStrng = crossLines.replace("49651", sourceId.toString());

        log.info("jsonStrDirectionLines{}", controlPipeStrng);

        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);

        PointerByReference outRemove = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), outRemove);


        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

        KesonUtils.kesonDeepDelete(out);
        KesonUtils.kesonDeepDelete(outRemove);
        KesonUtils.kesonDeepDelete(input);
    }

    private void updateRoi(String[] json1, Pointer pipelinePoint, String sourceId, List<Integer> positionTypeList) {

        JSONArray json2 = new JSONArray();
        for (int i = 0; i < json1.length; i++) {
            String[] vertexArr = json1[i].replaceAll("\\[|\\]|\\(|\\)", "").split(",");
            StringBuilder vertices = new StringBuilder();
            for (int j = 0; j < vertexArr.length; j += 2) {
                vertices.append("(").append(vertexArr[j].trim()).append(",").append(vertexArr[j + 1].trim()).append(")");
                if (j != vertexArr.length - 2) {
                    vertices.append(",");
                }
            }
            JSONObject roi = new JSONObject();
            roi.put("roi_id", i);
            roi.put("point_cal_type", (positionTypeList.size() >= i + 1) ? retypeByPosition(positionTypeList.get(i)) : 1);
            roi.put("vertexes", vertices.toString());
            json2.add(roi);
        }
        //point_cal_type 0 =》1 =》
        if (json2.isEmpty()) {
            return;
        }

        String controlPipeStrng = "{\n" +
                "\"streams\": [\n" +
                "   {\n" +
                "        \"name\": \"cross_line_pipeline\",\n" +
                "         \"modules\": [\n" +
                "        {\n" +
                "            \"name\": \"roi_filter\",\n" +
                "             \"source_id\": 49650,\n" +
                "             \"type\": \"AbgStudioCrowdRoiFilter\",\n" +
                "             \"inputs\": [\n" +
                "                     \"targets\",\n" +
                "                     \"pers_info\"\n" +
                "               ],\n" +
                "            \"outputs\": [\n" +
                "                 \"filtered_targets\"\n" +
                "             ],\n" +
                "            \"config\": {\n" +
                "                    \"type\": \"FilterHeadBox\",\n" +
                "                    \"cross_line\": {\n" +
                "                       \"detect_result_type\": 2,\n" +
                "                        \"rois\": " + json2 +
                "                }\n" +
                "            }\n" +
                "         }" +
                "       ]\n" +
                "     }\n" +
                "   ]\n" +
                "}";


        controlPipeStrng = controlPipeStrng.replace("49650", sourceId);

        log.info("jsonStrDirectionRois{}", controlPipeStrng);

        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);

        PointerByReference outRemove = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), outRemove);

        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

        KesonUtils.kesonDeepDelete(out);
        KesonUtils.kesonDeepDelete(outRemove);
        KesonUtils.kesonDeepDelete(input);
    }

    private void updatePositionType(String[] json1, Pointer pipelinePoint, VideoStreamInfra deviceInfra, List<Integer> positionTypeList) {

        JSONArray json2 = new JSONArray();
        for (int i = 0; i < json1.length; i++) {
            String vertices = "(0,0),(0," + ((deviceInfra.getRtspHeight() > 0) ? deviceInfra.getRtspHeight() : 1080) + "),(" + ((deviceInfra.getRtspWidth() > 0) ? deviceInfra.getRtspWidth() : 1920) + "," + ((deviceInfra.getRtspHeight() > 0) ? deviceInfra.getRtspHeight() : 1080) + "),(" + ((deviceInfra.getRtspWidth() > 0) ? deviceInfra.getRtspWidth() : 1920) + ",0)";

            JSONObject roi = new JSONObject();
            roi.put("roi_id", i);
            roi.put("point_cal_type", (positionTypeList.size() >= i + 1) ? retypeByPosition(positionTypeList.get(i)) : 1);
            roi.put("vertexes", vertices);
            json2.add(roi);
        }
        if (json2.isEmpty()) {
            return;
        }


        String controlPipeStrng = "{\n" +
                " \"streams\": [\n" +
                "    {\n" +
                "      \"name\": \"cross_line_pipeline\",\n" +
                "      \"modules\": [\n" +
                "        {\n" +
                "            \"name\": \"roi_filter\",\n" +
                "             \"source_id\": 49650,\n" +
                "             \"type\": \"AbgStudioCrowdRoiFilter\",\n" +
                "             \"inputs\": [\n" +
                "                     \"targets\",\n" +
                "                     \"pers_info\"\n" +
                "               ],\n" +
                "            \"outputs\": [\n" +
                "                 \"filtered_targets\"\n" +
                "             ],\n" +
                "            \"config\": {\n" +
                "                    \"type\": \"FilterHeadBox\",\n" +
                "                    \"cross_line\": {\n" +
                "                       \"detect_result_type\": 2,\n" +
                "                        \"rois\": " + json2 +
                "                }\n" +
                "            }\n" +
                "        }" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";


        long sourceId = Utils.keyToContextId(deviceInfra.getDeviceId());
        controlPipeStrng = controlPipeStrng.replace("49650", Long.toString(sourceId));

        log.info("jsonStrPositonType{}", controlPipeStrng);

        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);

        PointerByReference outRemove = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), outRemove);


        PointerByReference out = new PointerByReference();
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

        KesonUtils.kesonDeepDelete(out);
        KesonUtils.kesonDeepDelete(outRemove);
        KesonUtils.kesonDeepDelete(input);


    }

    //point_cal_type：每个结果框会需要计算是否在感兴趣区域，会取一个点来判断：0-> upper_middle ,1-> center ,2-> lower_middle
    private int retypeByPosition(Integer position) {
        if (position == 0) {
            return 1;
        } else if (position == 1) {
            return 0;
        } else if (position == 2) {
            return 2;
        }
        return 1;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        //log.info("DetectResult{}",  modelResult.getDetectResult());
        if (CollectionUtils.isEmpty(detectResult))
            return false;
        long contextId = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        Map<String, Track> tracks = trackingMap.get(contextId);
        if (tracks == null)
            return false;

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        float threshold = Objects.requireNonNullElse(processor.getThreshold(), 0f);
        int minSize = Objects.requireNonNullElse(processor.getMinSize(), Integer.MIN_VALUE);

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 0), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");

        Iterator<Map<String, Object>> its = targets.iterator();
        while(its.hasNext()) {
            Map<String, Object> target = its.next();

            Number conf = (Number)target.get("confidence");
            if(conf != null && conf.floatValue() < threshold) {
                log.info("confidence less than threshold, threshold:{}, confidence:{}", threshold, conf);
                its.remove();
                continue;
            }
            if(target.containsKey("target_image_roi")) {
                Map<String, Object> roi = (Map<String, Object>)target.get("target_image_roi");
                int width = ((Number) roi.get("width")).intValue();
                int height = ((Number) roi.get("height")).intValue();

                boolean minSizeCheck = width >= minSize && height >= minSize;
                if (!minSizeCheck) {
                    log.info("minSize too small, roi:{}, minsize:{}", JSON.toJSONString(roi), minSize);
                    its.remove();
                    continue;
                }
            }
        }
        return !targets.isEmpty();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return;
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        long contextId = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");

        if (CollectionUtils.isEmpty(targets))
            return;

        Map<String, Track> tracksLine = trackingMap.get(contextId);
        if (tracksLine == null)
            return;

        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 0), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        int faceRegSwitch = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getFaceRegSwitch(), 0);

        List<String> roiIds = (List<String>) ((List<Map<String, Object>>) Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getExtras(), List.of()))
                .stream()
                .filter(item -> "roiIds".equals(item.get("type")))
                .findAny()
                .orElse(Map.of())
                .getOrDefault("roiIds", List.of());

        // 字体样式、字体大小、字体颜色
        int fontFace = opencv_imgproc.FONT_HERSHEY_DUPLEX;
        double fontScale = 0.8;
        Scalar color = new Scalar(0, 0, 255, 0);
        int thickness = 1;
        int opencv_image = 0;
        float quality = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "quality", Integer.MIN_VALUE)).floatValue();
        float threshold = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getThreshold(), 0f);
        List<String> dropIds = new ArrayList<>();

        for (Map<String, Object> target : targets) {
            if (target.containsKey("rois")) {
                List<Map<String, Object>> rois = (List<Map<String, Object>>) target.get("rois");
                List<Integer> in_track_ids = new ArrayList<>();
                List<Integer> out_track_ids = new ArrayList<>();
                Map<String, Map<String, Number>> roiTrackIds = new HashMap<String, Map<String, Number>>();

                for (Map<String, Object> roi : rois) {
                    int label = 0;
                    if (roi.containsKey("boxes")) {
                        List<Map<String, Object>> boxes = (List<Map<String, Object>>) roi.get("boxes");
                        if (!boxes.isEmpty()) {
                            for (Map<String, Object> box : boxes) {
                                label = ((Number) box.getOrDefault("label", 0)).intValue();
                                Map<String, Number> roiBox = (Map<String, Number>) box.get("roi");
                                long trackId = ((Number) box.get("track_id")).longValue() + (contextId << 32);
                                roiTrackIds.put(Long.toString(trackId) + roiType, roiBox);
                            }
                        }
                    }
                    if (roi.containsKey("in_track_ids")) {

                        Integer[] inIds = JSON.parseObject(roi.get("in_track_ids").toString(), Integer[].class);
                        if (roi.get("in_track_ids") != null && inIds.length > 0) {
                            //log.info("in_track_ids={},targets={}", Arrays.asList(inIds), roi.get("cross_lines"));
                            log.info("cross-{}-in_track_ids{}", deviceId,Arrays.asList(inIds));
                            //hasCheckBoxes = true;
                            for (Integer trackId : inIds) {
                                long compositeTrackId = trackId.longValue() + (contextId << 32);
                                Track track = tracksLine.get(Long.toString(compositeTrackId) + inType);
                                if (track == null) {
                                    track = Track.builder().label(label).trackId(trackId).build();
                                    tracksLine.put(Long.toString(compositeTrackId) + inType, track);
                                }
                                track.setTrackInCount(track.getTrackInCount() + 1);

                                track.setLineID(findPointInROI((List<Map<String, Object>>) roi.get("cross_lines"), processor.getRoi()));
                                in_track_ids.add(trackId);
                            }
                        }
                    }
                    if (roi.containsKey("out_track_ids")) {
                        Integer[] outIds = JSON.parseObject(roi.get("out_track_ids").toString(), Integer[].class);
                        if (roi.get("out_track_ids") != null && outIds.length > 0) {
                            //log.info("out_track_ids={},targets{}",Arrays.asList(outIds), roi.get("cross_lines"));
                            log.info("cross-{}-out_track_ids{}",deviceId, Arrays.asList(outIds));
                            //hasCheckBoxes = true;
                            for (Integer trackId : outIds) {
                                long compositeTrackIdOut = trackId.longValue() + (contextId << 32);
                                Track track = tracksLine.get(Long.toString(compositeTrackIdOut) + outTtype);
                                if (track == null) {
                                    track = Track.builder().label(label).outTrackId(trackId).type(1).build();
                                    tracksLine.put(Long.toString(compositeTrackIdOut) + outTtype, track);
                                }
                                track.setTrackOutCount(track.getTrackOutCount() + 1);
                                track.setLineID(findPointInROI((List<Map<String, Object>>) roi.get("cross_lines"), processor.getRoi()));
                                out_track_ids.add(trackId);
                            }
                        }
                    }
                }

                if (in_track_ids.isEmpty() && out_track_ids.isEmpty()) {
                    //log.info("in_track_ids and out_track_ids is empty,deviceId:{}", deviceId);
                    continue;
                }
                //log.info("DetectResult222{}",  modelResult.getDetectResult());
                String sceneImagePath = null;

                Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                        .limit(kesonTarget_size)
                        .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                        .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "source_id")) == ((Number) target.get("source_id")).longValue())
                        .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "image_id")) == ((Number) target.get("image_id")).longValue())
                        .findAny()
                        .orElse(null);

                if (kesonTarget != null) {

                    PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                    if (sceneImage.getValue() != null) {
                        sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                    }
                }
                //log.info("sceneImagePaths-{}-{}", deviceId,sceneImagePath);
                for (Integer trackId : out_track_ids) {
                    long compositeTrackId = trackId.longValue() + (contextId << 32);
                    Track track = tracksLine.get(Long.toString(compositeTrackId) + outTtype);

                    if (track != null) {
                        //log.info("sceneImagePathOut-{}-{}-{}-{}", deviceId, trackId,sceneImagePath, Long.toString(compositeTrackId) + outTtype);
                        track.setSceneImagePath(sceneImagePath);

                        Map<String, Number> roiTrack = roiTrackIds.get(Long.toString(compositeTrackId) + roiType);
                        if (!roiTrack.isEmpty()) {
                            track.setRoiTrack(roiTrack);
                        }
                    }
                }
                for (Integer trackId : in_track_ids) {
                    long compositeTrackId = trackId.longValue() + (contextId << 32);
                    Track track = tracksLine.get(Long.toString(compositeTrackId) + inType);

                    if (track != null) {
                        //log.info("sceneImagePathIn{},{}", trackId, sceneImagePath);
                        track.setSceneImagePath(sceneImagePath);
                        Map<String, Number> roiTrack = roiTrackIds.get(Long.toString(compositeTrackId) + roiType);
                        if (!roiTrack.isEmpty()) {
                            track.setRoiTrack(roiTrack);
                        }
                    }
                }
                roiTrackIds.clear();
            }
        }
        List<String> removeTrck = new ArrayList<String>();

        for (Map<String, Object> target : targets) {

            if (target.containsKey("flock_index") && ((Number) target.get("flock_index")).intValue() == 1) {
                Map<String, Object> output = new HashMap<String, Object>();

                output.put("label", target.get("label"));
                output.put("position", target.get("position"));
                output.put("confidence", target.get("confidence"));
                output.put("targetRoiSelected", target.get("target_image_roi"));
                output.put("roi", target.get("roi"));
                if (target.containsKey("aligner_confidence")) {
                    output.put("alignerConfidence", target.get("aligner_confidence"));
                }
                if (target.containsKey("in_track_ids")) {
                    output.put("inTrackIds", target.get("in_track_ids"));
                }
                if (target.containsKey("out_track_ids")) {
                    output.put("outTrackIds", target.get("out_track_ids"));
                }
                if (target.containsKey("integrate_quality")) {
                    output.put("integrateQuality", target.get("integrate_quality"));
                }
                if (target.containsKey("quality")) {
                    output.put("quality", target.get("quality"));
                }
                if (target.containsKey("id")) {
                    output.put("id", target.get("id"));
                }
                output.put("inTrackIdsCount", -1);
                output.put("outTrackIdsCount", -1);

                //if (target.containsKey("track_id")) {
                int trackId = ((Number) target.get("track_id")).intValue();
                output.put("trackId", trackId);
                //log.info("trackIdOut{}", trackId);

                output.put("crossLine", "");
                boolean hadSenseImage = false;
                Long typeTrack = ((Number) target.get("track_id")).longValue() + (contextId << 32);
                Track track = tracksLine.get(Long.toString(typeTrack) + inType);
                if (track != null && track.trackInCount > 0) {
                    output.put("inTrackIdsCount", track.trackInCount);
                    output.put("crossLine", "in");
                    output.put("inTrackIds", appendTrack(track.trackInCount, trackId));
                    output.put("line_id", track.getLineId());
                    output.put("roiHits", new String[]{(track.getLineId() >= 0 ? roiIds.get(track.getLineId()) : roiIds.get(0))});
                    output.put("sceneImage", track.getSceneImagePath());
                    output.put("targetRoi", track.getRoiTrack());
                    if (track.getSceneImagePath() != null && !track.getSceneImagePath().isEmpty()) {
                        hadSenseImage = true;
                    } else {
                        output.put("dropTrackTypeIn", trackId);
                    }
                    removeTrck.add(Long.toString(typeTrack) + inType);
                }
                Track trackOut = tracksLine.get(Long.toString(typeTrack) + outTtype);

                if (trackOut != null && trackOut.trackOutCount > 0) {
                    output.put("outTrackIdsCount", trackOut.trackOutCount);
                    output.put("crossLine", "out");
                    output.put("outTrackIds", appendTrack(trackOut.trackOutCount, trackId));
                    output.put("line_id", trackOut.getLineId());
                    output.put("roiHits", new String[]{(trackOut.getLineId() >= 0 ? roiIds.get(trackOut.getLineId()) : roiIds.get(0))});
                    output.put("sceneImage", trackOut.getSceneImagePath());
                    output.put("targetRoi", trackOut.getRoiTrack());
                    if (trackOut.getSceneImagePath() != null && !trackOut.getSceneImagePath().isEmpty()) {
                        hadSenseImage = true;
                    } else {
                        output.put("dropTrackType", trackId);
                    }
                    removeTrck.add(Long.toString(typeTrack) + outTtype);
                }
                if (output.get("targetRoi") == null) {
                    output.put("targetRoi", target.get("target_image_roi"));
                }
                //}

                if (((Number) output.get("inTrackIdsCount")).intValue() <= 0
                        && ((Number) output.get("outTrackIdsCount")).intValue() <= 0) {
                    continue;
                }
                if (target.containsKey("yaw") && target.containsKey("pitch")) {
                    output.put("head_pose", Map.of("yaw", target.get("yaw"), "pitch", target.get("pitch"), "roll", target.get("roll")));
                }
                if (target.containsKey("status")) {
                    output.put("status", target.get("status"));
                }

                Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                        .limit(kesonTarget_size)
                        .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                        .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "source_id")) == ((Number) target.get("source_id")).longValue())
                        .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "track_id")) == ((Number) target.get("track_id")).longValue())
                        .findAny()
                        .orElse(null);

                //small image
                String targetImagePath = "";
                //scene image
                String sceneImagePath = "";

                if (kesonTarget != null) {
                    PointerByReference targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                    if (targetImage.getValue() != null) {

                        Map<String, Number> roiBox = (Map<String, Number>) target.get("roi");
                        int left = roiBox.get("left").intValue();
                        int top = roiBox.get("top").intValue();
                        int width = roiBox.get("width").intValue();
                        int height = roiBox.get("height").intValue();
                        log.info("targetImagePath:{}, {}",trackId,  target.get("image"));

                        targetImagePath = FrameUtils.save_image_as_jpg(targetImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                        output.put("targetImage", targetImagePath);

                        //人脸开关
                        if (faceRegSwitch == 1) {
                            extracted(modelResult, target, quality, targetImage, output, targetImagePath);
                        }
                        if (targetImagePath != null && !targetImagePath.isEmpty() && opencv_image > 0) {
                            extracted(target, targetImagePath, left, top, width, height, output, fontFace, fontScale, color, thickness);
                        }
                    }
                    if (output.get("sceneImage") != null) {
                        sceneImagePath = output.get("sceneImage").toString();
                    }
                    if (!hadSenseImage) {
                        //log.info("sceneImages{}", target);
                        PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "scene_frame"));
                        if (sceneImage.getValue() != null) {
                            sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                            output.put("sceneImage", sceneImagePath);
                        }
                    }
                    if (!sceneImagePath.isEmpty() && opencv_image > 0) {
                        extracted(target, sceneImagePath, output, fontFace, fontScale, color, thickness);
                    }

                }
                outputResult.add(output);
            }
            if (target.containsKey("flock_index") && ((Number) target.get("flock_index")).intValue() == 2) {//dropids
                dropIds.add(target.get("dropped_track_id").toString() + "_" + Long.toString(contextId));
            }
        }

        // delete map key
        //log.info("removeTrcks{}", removeTrcks);
        removeTrck.forEach(tracksLine::remove);

        //dropids from trackMap is exit=drop
        for (String drop : dropIds) {
            Map<String, Object> output = new HashMap<String, Object>();
            //log.info("removeTrcksDrop{}-{}", deviceId, drop);
            String[] data = drop.split("_");
            if (data.length <= 1) {
                continue;
            }

            int trackId = Integer.parseInt(data[0]);
            long typeTrack = Long.parseLong(data[0]) + (contextId << 32);

            output.put("inTrackIdsCount", -1);
            output.put("outTrackIdsCount", -1);

            //if (target.containsKey("track_id")) {
            output.put("trackId", trackId);
            output.put("personInfo", TrackPerson.builder().personId("Noface-Id-" + trackId).targetType("Noface").build());

            if (tracksLine.containsKey(Long.toString(typeTrack) + outTtype)) {
                //log.info("removeTrcksOut-{}-{}",trackId, Long.toString(typeTrack) + outTtype);
                Track trackOut = tracksLine.get(Long.toString(typeTrack) + outTtype);
                output.put("outTrackIdsCount", trackOut.trackOutCount);
                output.put("crossLine", "out");
                output.put("outTrackIds", appendTrack(trackOut.trackOutCount, trackId));
                output.put("sceneImage", trackOut.getSceneImagePath());
                output.put("targetRoi", trackOut.getRoiTrack());
                output.put("roiHits", new String[]{(trackOut.getLineId() >= 0 ? roiIds.get(trackOut.getLineId()) : roiIds.get(0))});
                if (trackOut.getSceneImagePath() != null && !trackOut.getSceneImagePath().isEmpty()) {
                    outputResult.add(output);
                }
            }
            if (tracksLine.containsKey(Long.toString(typeTrack) + inType)) {
                //log.info("removeTrcksIn-{}-{}", trackId, Long.toString(typeTrack) + inType);
                Track track = tracksLine.get(Long.toString(typeTrack) + inType);
                output.put("inTrackIdsCount", track.trackInCount);
                output.put("crossLine", "in");
                output.put("inTrackIds", appendTrack(track.trackInCount, trackId));
                output.put("sceneImage", track.getSceneImagePath());
                output.put("targetRoi", track.getRoiTrack());
                output.put("roiHits", new String[]{(track.getLineId() >= 0 ? roiIds.get(track.getLineId()) : roiIds.get(0))});
                if (track.getSceneImagePath() != null && !track.getSceneImagePath().isEmpty()) {
                    outputResult.add(output);
                }
            }
        }
        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
        if (outputResult.isEmpty())
            modelResult.setOutputResult(null);
        else
            modelResult.setOutputResult(outputResult);
    }

    private void extracted(Map<String, Object> target, String sceneImagePath, Map<String, Object> output, int fontFace, double fontScale, Scalar color, int thickness) {
        Mat sourceImage = null;
        sourceImage = opencv_imgcodecs.imread(sceneImagePath);

        Map<String, Number> roiBox = null;
        if (output.get("targetRoi") != null) {
            roiBox = (Map<String, Number>) output.get("targetRoi");
        } else {
            roiBox = (Map<String, Number>) target.get("target_image_roi");
        }

        int left = roiBox.get("left").intValue();
        int top = roiBox.get("top").intValue();
        int width = roiBox.get("width").intValue();
        int height = roiBox.get("height").intValue();

        org.bytedeco.opencv.opencv_core.Rect rect = new org.bytedeco.opencv.opencv_core.Rect(Math.max(0, left), Math.max(0, top), width, height);
        opencv_imgproc.rectangle(sourceImage, rect, new Scalar(0, 255, 255, 0), 1, opencv_imgproc.LINE_AA, 0);

        org.bytedeco.opencv.opencv_core.Point org = new org.bytedeco.opencv.opencv_core.Point(rect.x() + rect.width(), rect.y() + rect.height());

        opencv_imgproc.putText(sourceImage, target.get("track_id").toString() + "-" + output.get("crossLine").toString(), org, fontFace, fontScale, color, thickness, opencv_imgproc.LINE_AA, false);


        String fileName = sceneImagePath.substring(sceneImagePath.lastIndexOf('/') + 1);
        File path = new File(Utils.instance.savePath + "/opencv/" + Objects.requireNonNullElse(ImageUtils.dateFormatter_DAY.get().format(new Date()), "/") + annotatorName());
        if (!path.exists())
            path.mkdirs();
        // 保存结果图片
        opencv_imgcodecs.imwrite(path.getAbsolutePath() + "/" + fileName + "_opencv_target_scene.jpg", sourceImage);
    }

    private void extracted(Map<String, Object> target, String targetImagePath, int left, int top, int width, int height, Map<String, Object> output, int fontFace, double fontScale, Scalar color, int thickness) {
        Mat sourceImage = null;
        sourceImage = opencv_imgcodecs.imread(targetImagePath);

        org.bytedeco.opencv.opencv_core.Rect rect = new org.bytedeco.opencv.opencv_core.Rect(Math.max(0, left), Math.max(0, top), width, height);
        opencv_imgproc.rectangle(sourceImage, rect, new Scalar(0, 255, 255, 0), 1, opencv_imgproc.LINE_AA, 0);

        org.bytedeco.opencv.opencv_core.Point org = new org.bytedeco.opencv.opencv_core.Point(rect.x() + rect.width(), rect.y() + rect.height());

        opencv_imgproc.putText(sourceImage, target.get("track_id").toString() + "-" + output.get("crossLine").toString(), org, fontFace, fontScale, color, thickness, opencv_imgproc.LINE_AA, false);


        String fileName = targetImagePath.substring(targetImagePath.lastIndexOf('/') + 1);
        File path = new File(Utils.instance.savePath + "/opencv/" + Objects.requireNonNullElse(ImageUtils.dateFormatter_DAY.get().format(new Date()), "/") + annotatorName());
        if (!path.exists())
            path.mkdirs();
        // 保存结果图片
        opencv_imgcodecs.imwrite(path.getAbsolutePath() + "/" + fileName + "_opencv_target.jpg", sourceImage);
    }

    private void extracted(ModelResult modelResult, Map<String, Object> target, float quality, PointerByReference targetImage, Map<String, Object> output, String targetImagePath) {
        String isFaceReg = "";
        Number qualityTarget = (Number) target.get("quality");
        if (qualityTarget != null) {
            if (quality > Integer.MIN_VALUE && qualityTarget.floatValue() < quality) {//low quality
                isFaceReg = "quality";
            }
        }
        Number integrateQuality = (Number) target.get("integrate_quality");
        if (integrateQuality != null && integrateQuality.floatValue() < Utils.instance.integrateQuality) {
            isFaceReg = "integrateQuality";
        }

        float yaw = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "yaw", Integer.MIN_VALUE)).floatValue();
        float pitch = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "pitch", Integer.MIN_VALUE)).floatValue();
        float roll = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "roll", Integer.MIN_VALUE)).floatValue();

        Number yawTarget = (Number) target.get("yaw");
        Number pitchTarget = (Number) target.get("pitch");
        Number rollTarget = (Number) target.get("roll");
        if (yawTarget == null && pitchTarget == null && rollTarget == null) {
            if (yaw > Integer.MIN_VALUE || pitch > Integer.MIN_VALUE || roll > Integer.MIN_VALUE) {
                try {
                    HeadPostCross headPostCross = findoutHeadPose(targetImage.getValue());
                    if (headPostCross != null) {
                        yawTarget = headPostCross.getYaw();
                        pitchTarget = headPostCross.getPitch();
                        rollTarget = headPostCross.getRoll();
                        if (yawTarget != null && pitchTarget != null) {
                            output.put("head_pose", Map.of("yaw", yawTarget, "pitch", pitchTarget, "roll", rollTarget));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (yawTarget != null && yaw > Integer.MIN_VALUE) {
            if (!(Math.abs(yawTarget.floatValue()) <= yaw)) {
                isFaceReg = "yaw";
            }
        }
        if (pitchTarget != null && pitch > Integer.MIN_VALUE) {
            if (!(Math.abs(pitchTarget.floatValue()) <= pitch)) {
                isFaceReg = "pitch";
            }
        }
        if (rollTarget != null && roll > Integer.MIN_VALUE) {
            if (!(Math.abs(rollTarget.floatValue()) <= roll)) {
                isFaceReg = "roll";
            }
        }
        float faceSizeLimit = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "boundingBoxSizeThreshold", Integer.MIN_VALUE)).floatValue();

        float widthFace = ((Map<String, Number>) target.get("target_image_roi")).get("width").floatValue();
        float heightFace = ((Map<String, Number>) target.get("target_image_roi")).get("height").floatValue();
        if (widthFace < faceSizeLimit && heightFace < faceSizeLimit) {
            isFaceReg = "faceSizeLimit";
        }

        if (!isFaceReg.isEmpty() && !isFaceReg.isBlank()) {
            output.put("personInfo", TrackPerson.builder().personId("Unknown-" + isFaceReg).targetType("isFaceReg").build());
        } else {
            float matchThreshold = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "matchingThreshold", Utils.instance.Lv1Threshold)).floatValue();
            output.put("personInfo", findoutWhoItIs(targetImage.getValue(), ((Number) target.get("track_id")).intValue(), ((Number) target.get("quality")).floatValue(), modelResult.getModelRequest().getParameter().get("deviceId").toString(), matchThreshold, targetImagePath));
        }
//                            targetImagePath = FrameUtils.save_image_as_jpg(FrameUtils.roi_frame(targetImage.getValue(), left, top, width, height), ImageUtils.newFile("CrossLinesFace"));
    }

    private List<Integer> appendTrack(int trackInCount, int track_id) {
        List<Integer> in_track_ids = new ArrayList<>(trackInCount);

        for (int i = 0; i < trackInCount; i++) {
            in_track_ids.add(track_id);
        }
        return in_track_ids;
    }

    @SuppressWarnings("unchecked")
    private final HeadPostCross findoutHeadPose(Pointer videoFrame) {
        HeadPostCross track = HeadPostCross.builder().build();

        if (videoFrame == null)
            return track;

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, Object> data = new LinkedMultiValueMap<String, Object>();

            HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(data, headers);

            String figureImageBase64 = ImageUtils.bytesToBase64(FrameUtils.encode_image_as_jpg(videoFrame));

            data.add("figureImageBase64", figureImageBase64);

            JSONObject response = restTemplate.postForObject("http://cognitivesvc/cognitive/face/getHeadposeBlurByBase64", request, JSONObject.class);
            Map<String, Object> persons = (Map<String, Object>) response.getOrDefault("data", List.of());

            if (persons != null && !persons.isEmpty()) {
                Map<String, Object> single = (Map<String, Object>) persons.get("face_0");
                if (single != null) {
                    track.setYaw(((Number) single.get("yaw")));
                    track.setPitch(((Number) single.get("pitch")));
                    track.setRoll(((Number) single.get("roll")));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return track;


    }

    @SuppressWarnings("unchecked")
    private final TrackPerson findoutWhoItIs(Pointer videoFrame, int trackId, float quality, String deviceId, float matchThreshold, String targetImagePath) {

        TrackPerson track = new TrackPerson();

        if (videoFrame == null)
            return track;

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, Object> data = new LinkedMultiValueMap<String, Object>();

            HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(data, headers);

            String figureImageBase64 = ImageUtils.bytesToBase64(FrameUtils.encode_image_as_jpg(videoFrame));

            data.add("figureImageBase64", figureImageBase64);
            if (matchThreshold > Integer.MIN_VALUE) {
                data.add("threshold", matchThreshold);
            }

            JSONObject response = restTemplate.postForObject("http://cognitivesvc/cognitive/face/compareFaceImage", request, JSONObject.class);
            List<Map<String, Object>> persons = (List<Map<String, Object>>) response.getOrDefault("data", List.of());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(persons)) {
                if (!persons.get(0).isEmpty() && persons.get(0) != null) {
                    track.setPersonId(persons.get(0).get("personID").toString());
                    track.setTargetType(persons.get(0).get("targetType").toString());
                    track.setPersonScore(((Number) persons.get(0).get("score")).floatValue());
                    track.setAvatar(persons.get(0).get("avatar").toString());
                }
            } else {
                //add passer
                MultiValueMap<String, Object> dataStranger = new LinkedMultiValueMap<String, Object>();

                HttpEntity<MultiValueMap<String, Object>> requestStranger = new HttpEntity<MultiValueMap<String, Object>>(dataStranger, headers);

                dataStranger.add("figureImageBase64", figureImageBase64);
                dataStranger.add("trackId", trackId);
                dataStranger.add("quality", quality);
                dataStranger.add("deviceId", deviceId);

                JSONObject responseSnger = restTemplate.postForObject("http://cognitivesvc/cognitive/face/strangerIsHere", requestStranger, JSONObject.class);
                Map<String, Object> personsStrange = (Map<String, Object>) responseSnger.getOrDefault("data", Map.of());
                if (!personsStrange.isEmpty()) {
                    track.setPersonId(personsStrange.get("personUUID").toString());
                    track.setTargetType("Passer");
                    track.setPersonScore(1.0f);
                    if (!targetImagePath.isBlank()) {
                        track.setAvatar(targetImagePath);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return track;
    }


    @Data
    @AllArgsConstructor
    @Accessors(chain = true)
    @Builder
    private static final class HeadPostCross {

        private double alignerConfidence;
        /**
         * 模糊度
         */
        private float blur;
        /**
         * roll
         */
        private Number roll;
        /**
         * pitch
         */
        private Number pitch;
        /**
         * yaw
         */
        private Number yaw;
        /**
         * 质量分
         */
        private float score;


        public HeadPostCross() {

        }
    }

    @Data
    @AllArgsConstructor
    @Accessors(chain = true)
    @Builder
    private static final class TrackPerson {

        private volatile Pointer faceFrame;

        @Setter
        private volatile String personId;

        @Setter
        private volatile Float personScore;

        @Setter
        private volatile String targetType;

        @Setter
        private volatile String avatar;

        public TrackPerson() {

        }
    }

    @Getter
    @Accessors(chain = true)
    @Builder
    public static final class Track {
        private int label;
        private int trackId;
        private int outTrackId;
        private int type;

        private int associationsId;

        private int trackInCount;
        private int trackOutCount;

        private int lineId;

        private String sceneImagePath;

        private Map<String, Number> roiTrack;

        @Builder.Default
        private boolean startAreaCount = false;
        @Builder.Default
        private boolean endAreaCount = false;

        public void setTrackInCount(int in) {
            trackInCount = in;
        }

        public void setLineID(int in) {
            lineId = in;
        }

        public void setTrackOutCount(int out) {
            trackOutCount = out;
        }

        public void setStartAreaCount() {
            startAreaCount = true;
        }

        public void setEndAreaCount() {
            endAreaCount = true;
        }

        public void setAssociationsId(int ass) {
            associationsId = ass;
        }

        public void setSceneImagePath(String path) {
            sceneImagePath = path;
        }

        public void setRoiTrack(Map<String, Number> roiTracks) {
            roiTrack = roiTracks;
        }

        private Rect currentPosition;


        public void setCurrentPosition(Map<String, Number> roi) {
            currentPosition = Rect.builder().top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).build();
        }

        @Data
        @Accessors(chain = true)
        @Builder
        public static class Rect {
            private int left;
            private int top;
            private int width;
            private int height;
        }
    }


    @SuppressWarnings("unchecked")
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return result;
//
//        Map<String, Track> tracksLine = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
//        if(tracksLine == null)
//            return result;

        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();

        long contextId = Utils.keyToContextId(deviceId);

        CvScalar color = opencv_core.CV_RGB(255, 0, 0);
        CvScalar colorNext = opencv_core.CV_RGB(255, 255, 0);

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        if (ArrayUtils.isEmpty(polygons))
            polygons = new Polygon[]{new Polygon(new int[]{0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[]{0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};


        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);


        for (int indexs = 0; indexs < polygons.length; indexs++) {
            Map<String, Object> extra = roiIndexExtrasMap.getOrDefault(indexs, Map.of());
            if (extra.isEmpty())
                continue;

            if (extra.containsKey("crossDot")) {

                List<Number> crossDot = (List<Number>) extra.getOrDefault("crossDot", List.of());
                if (crossDot.size() > 2) {
                    result.add(Line.builder().thickness(3).color(color).from(new int[]{crossDot.get(0).intValue(), crossDot.get(1).intValue()}).to(new int[]{crossDot.get(2).intValue(), crossDot.get(3).intValue()}).build());
                }

            }

        }
        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");
        for (Map<String, Object> target : targets) {

            if (target.containsKey("rois")) {
                List<Map<String, Object>> rois = (List<Map<String, Object>>) target.get("rois");

                Map<String, Map<String, Number>> roiTrackIds = new HashMap<String, Map<String, Number>>();

                for (Map<String, Object> roi : rois) {

                    if (roi.containsKey("boxes")) {
                        List<Map<String, Object>> boxes = (List<Map<String, Object>>) roi.get("boxes");
                        if (!boxes.isEmpty()) {
                            for (Map<String, Object> box : boxes) {
                                Map<String, Integer> roibox = (Map<String, Integer>) box.get("roi");
                                result.add(Rect.builder().processor(annotatorName()).text(((Number) box.get("track_id")).toString()).top(roibox.get("top")).left(roibox.get("left")).width(roibox.get("width")).height(roibox.get("height")).build());

                                Map<String, Number> roiBox = (Map<String, Number>) box.get("roi");
                                long trackId = ((Number) box.get("track_id")).longValue() + (contextId << 32);
                                roiTrackIds.put(Long.toString(trackId) + roiType, roiBox);

                            }
                        }
                    }

                    if (roi.containsKey("in_track_ids")) {

                        Integer[] inIds = JSON.parseObject(roi.get("in_track_ids").toString(), Integer[].class);
                        if (roi.get("in_track_ids") != null && inIds.length > 0) {
                            //hasCheckBoxes = true;
                            for (Integer trackId : inIds) {
                                long compositeTrackId = trackId.longValue() + (contextId << 32);
                                Map<String, Number> roiTrack = roiTrackIds.get(Long.toString(compositeTrackId) + roiType);
                                if (!roiTrack.isEmpty()) {
                                    result.add(Rect.builder().processor(annotatorName()).color(colorNext).text(trackId.toString()).top(roiTrack.get("top").intValue()).left(roiTrack.get("left").intValue()).width(roiTrack.get("width").intValue()).height(roiTrack.get("height").intValue()).build());
                                }
                            }
                        }
                    }
                    if (roi.containsKey("out_track_ids")) {
                        Integer[] outIds = JSON.parseObject(roi.get("out_track_ids").toString(), Integer[].class);
                        if (roi.get("out_track_ids") != null && outIds.length > 0) {
                            //log.info("out_track_ids{}", Arrays.asList(outIds));
                            //hasCheckBoxes = true;
                            for (Integer trackId : outIds) {
                                long compositeTrackId = trackId.longValue() + (contextId << 32);
                                Map<String, Number> roiTrack = roiTrackIds.get(Long.toString(compositeTrackId) + roiType);
                                if (!roiTrack.isEmpty()) {
                                    result.add(Rect.builder().processor(annotatorName()).color(colorNext).text(trackId.toString()).top(roiTrack.get("top").intValue()).left(roiTrack.get("left").intValue()).width(roiTrack.get("width").intValue()).height(roiTrack.get("height").intValue()).build());
                                }
                            }
                        }
                    }

                }
            }
        }

//        Map<String, Integer> tracks = trackingMapOutCount.get(contextId);
//        if(tracks == null)
//            return result;
//        int tCount = 0;
//        int tOutCount =0;
//        for(Map.Entry<String, Integer> entry : tracks.entrySet()) {
//            if(entry.getKey().equals("in")){
//                tCount = entry.getValue();
//            }else{
//                tOutCount= entry.getValue();
//            }
//        }

//        int tCountLine = 0;
//        int tOutCountLine =0;
//        for(Map.Entry<String, Track> entry : tracksLine.entrySet()) {
//            Track currentTrack = entry.getValue();
//            tCountLine += currentTrack.trackInCount;
//            tOutCountLine += currentTrack.trackOutCount;
//        }
//        log.info("crossDirectionCountsLine{},{},{}", deviceId, tCountLine, tOutCountLine);
//
//        color = opencv_core.CV_RGB(0, 0,255 );
//        result.add(Rect.builder().color(color).processor(annotatorName()).text("in [" + tCountLine + "],out [" +tOutCountLine+ "]").top(100).left(100).width(0).height(0).build());


        color = opencv_core.CV_RGB(0, 255, 0);
        for (Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for (int index = 0; index < roi.length - 1; index++) {
                result.add(Line.builder().thickness(2).color(color).from(new int[]{roi[index][0], roi[index][1]}).to(new int[]{roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().thickness(2).color(color).from(new int[]{roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[]{roi[0][0], roi[0][1]}).build());
        }


        return result;
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        super.onWorkerEvent(e);
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("crossLins start context [" + contextId + "].");


        trackingMap.put(contextId, new ConcurrentHashMap<String, Track>());

        deviceRoiMapCross.put(contextId, new ConcurrentHashMap<String, String>());

    }

    private void stopContext(String deviceId, long contextId) {

        deviceRoiMapCross.remove(contextId);
        if (!trackingMap.containsKey(contextId))
            return;

        trackingMap.remove(contextId);

        log.info("crossLins stop deviceId [" + deviceId + "].");
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if (e != null)
            for (Map<String, Object> param : (List<Map<String, Object>>) e) {
                if (!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer) param.get("roiIndex");
                if (roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };

    public static String[] toArrayStringRi(Integer[][][] arr) {
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr[i]);
        }
        return result;
    }

    public static String[] toArrayStringRoi(Integer[][] arr) {
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr[i]);
        }
        return result;
    }

    public static String[] toArrayStringRoiList(List<Integer[][]> arr) {
        int numRows = arr.size();
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr.get(i));
        }
        return result;
    }

    public static double[] calculateIncrementValues(double[] point, double[][] line, String direction) {
        double x1 = line[0][0];
        double y1 = line[0][1];
        double x2 = line[1][0];
        double y2 = line[1][1];

        // Calculate slope of the line
        double slope = (y2 - y1) / (x2 - x1);

        // Calculate increment values based on slope and direction
        double increment_x = Math.sqrt(1 + Math.pow(slope, 2)) / Math.abs(slope);
        double increment_y = increment_x * slope;

// Calculate the new point based on direction
        if (direction.equals("backward")) {
            increment_x = -increment_x;
            increment_y = -increment_y;
        }

        return new double[]{increment_x, increment_y};
    }

    public static double[] calculatePointForPolygon(double[] point, double[][] polygon, String direction) {
        double minDistance = Double.MAX_VALUE;
        double[][] closestLine = null;

        // Find the closest line segment to the given point in the polygon
        for (int i = 0; i < polygon.length; i++) {
            double[] p1 = polygon[i];
            double[] p2 = polygon[(i + 1) % polygon.length];

            // Calculate distance between point and line segment
            double distance = Math.abs((p2[1] - p1[1]) * point[0] - (p2[0] - p1[0]) * point[1] + p2[0] * p1[1] - p2[1] * p1[0]) / Math.sqrt(Math.pow(p2[1] - p1[1], 2) + Math.pow(p2[0] - p1[0], 2));

            // Store closest line segment so far
            if (distance < minDistance) {
                minDistance = distance;
                closestLine = new double[][]{p1, p2};
            }
        }

        // Calculate increment values based on closest line and direction
        double[] increments = calculateIncrementValues(point, closestLine, direction);

        // Calculate the new point based on increments and direction
        double x_new = point[0] + increments[0];
        double y_new = point[1] + increments[1];

        return new double[]{x_new, y_new};
    }

    public static double[] calculatePointForLine(double[] point, double[][] line, String direction) {
        double x1 = line[0][0];
        double y1 = line[0][1];
        double x2 = line[1][0];
        double y2 = line[1][1];

        // Calculate slope of the line
        double slope = (y2 - y1) / (x2 - x1);

        // Calculate increment values based on slope and direction
        double increment_x = Math.sqrt(1 + Math.pow(slope, 2)) / Math.abs(slope);
        double increment_y = increment_x * slope;

        // Calculate the new point based on increments and direction
        if (direction.equals("backward")) {
            increment_x = -increment_x;
            increment_y = -increment_y;
        }

        double x_new = point[0] + increment_x;
        double y_new = point[1] + increment_y;

        return new double[]{x_new, y_new};
    }

    @SuppressWarnings("unchecked")
    private static Object getExtraValue(List<Map<String, Object>> extras, String key, Object def) {
        return Objects.requireNonNullElse(extras, List.of())
                .stream()
                .filter(extra -> key.equals(((Map<String, Object>) extra).get("type")))
                .map(extra -> ((Map<String, Object>) extra).getOrDefault("value", def))
                .findAny()
                .orElse(def);

    }

    // 检查点是否在 ROI 中
    public static int findPointInROI(List<Map<String, Object>> vertex, Integer[][][] roi) {

        Map<String, Object> binaryData = (Map<String, Object>) vertex.get(0).get("$binary");
        if (binaryData != null) {
            Map<String, Integer> readableData = (Map<String, Integer>) binaryData.get("$readable");
            if (readableData != null) {
                int x = readableData.get("x");
                int y = readableData.get("y");

                for (int i = 0; i < roi.length; i++) {
                    // 检查 ROI 的两个点
                    if ((roi[i][0][0] == x && roi[i][0][1] == y) ||
                            (roi[i][1][0] == x && roi[i][1][1] == y)) {
                        return i; // 返回索引
                    }
                }
            }
        }

        return -1; // 未找到
    }

    private static final String DIRECTION = "direction";

    private static final String ROISTRING = "roi";

    private static final String LINESTRING = "lines";

    private static final String POSITIONTYPE = "positonType";

    private static final String directionForWard = "forward";

    private static final String directionBackWard = "backward";
    private static final String inType = "1L";
    private static final String outTtype = "2L";
    private static final String roiType = "3L";
}