package com.sensetime.intersense.cognitivesvc.xworker;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import jakarta.annotation.PostConstruct;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import com.sensetime.intersense.cognitivesvc.server.mapper.XDynamicModelRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.xworker.controller.XWorkerHeartBeatProvider;
import com.sensetime.intersense.cognitivesvc.xworker.zutils.VideoStreamXWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.scheduling.annotation.Scheduled;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XStoreHandler;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Configuration("xworkerConfiguation")
@ComponentScan
@Slf4j
@ConditionalOnProperty(value = "xworker.enabled", havingValue = "true", matchIfMissing = true)
@PropertySource("classpath:xworker.properties")
public class CognitivesvcConfiguation{

	@Autowired
	private ConfigurableEnvironment env;

	@Autowired
	private XStoreHandler modelStoreHandler;


	@Autowired
	XDynamicModelRepository modelRepository;
	
	@Autowired
	private Utils utils;

	@Autowired
	XWorkerHeartBeatProvider xWorkerHeartBeatProvider;
	
    @PostConstruct
    public void initialize() throws Exception {
		utils.toString();
		
    	Initializer.initialize(env);
    	
		log.warn("\n");
		log.warn("********************************");
		log.warn("******init kestrel xworker******");
		log.warn("xworker.enabled=false to disable");
		log.warn("********************************");
		log.warn("\n");

		ImageUtils.preMkDirPostContructCompleted = true;
		log.info("[preMakeDirsCron] preMakeDir Completed");
    }
    
    @Scheduled(fixedDelay = 5000)
	public void resetModelMonitor() throws Exception {
    	for(XModelHandler handler : modelStoreHandler.getXDynamicHandlerMap().values())
    		if(handler instanceof AbstractXModelHandler) 
				  ((AbstractXModelHandler)handler).getMonitor().reset();

		for(VideoStreamXWorker worker : modelStoreHandler.getOngoingVideoStreamMap().values()){
			worker.getMonitor().reset();
		}
	}

	// 低频流和noseen实时输出
	@Scheduled(cron = "${xworker.queryDevice.cron: 0 0/2 * * * ?}")
	public void queryDeviceForLog() throws Exception {
		CognitiveEntity.QueryWorkerResult result = xWorkerHeartBeatProvider.queryModelHandler(false);
		log.info("[query instance info] heartbeat query result: {}", JSON.toJSONString(result));
	}

}
