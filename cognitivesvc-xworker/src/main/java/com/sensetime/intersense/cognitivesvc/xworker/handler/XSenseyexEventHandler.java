package com.sensetime.intersense.cognitivesvc.xworker.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawVideo;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils.FramePoolUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.out.DeviceStatusSyncOutput;
import com.sensetime.intersense.cognitivesvc.xworker.event.out.SenseyeXRawVideoEventOutput;
import com.sensetime.intersense.cognitivesvc.xworker.event.out.SenseyexRawEventOutput;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.ConfigAccessor;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XModelHandler.ModelRequest;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XModelHandler.ModelResult;
import com.sensetime.intersense.cognitivesvc.xworker.zutils.KafkaXWorkerConvertor;
import com.sensetime.intersense.cognitivesvc.xworker.zutils.KafkaXWorkerConvertor.KafkaXWorkerConvertorBuilder;
import com.sensetime.intersense.cognitivesvc.xworker.zutils.VideoStreamXWorker;
import com.sensetime.storage.service.FileAccessor;
import com.sensetime.storage.utils.CommonUtils;
import com.sun.jna.Pointer;
import jakarta.annotation.PostConstruct;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.lang.reflect.Array;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class XSenseyexEventHandler {

    public static final Map<String, LinkedBlockingQueue<SenseyexRawFrame>> highRateHandleMap = new ConcurrentHashMap<String, LinkedBlockingQueue<SenseyexRawFrame>>();

    public static final LinkedBlockingQueue<SenseyexRawImage> seneyexRawImageHandleQueue = new LinkedBlockingQueue<SenseyexRawImage>(1024);

    public static final LinkedBlockingQueue<SenseyexRawVideo> seneyexRawVideoHandleQueue = new LinkedBlockingQueue<SenseyexRawVideo>(128);

    private final Semaphore tempStreamSemaphore = new Semaphore(16);

    private final ConcurrentHashMap<String, Long> lowContextTimeMap = new ConcurrentHashMap<String, Long>();

    @Autowired
    private ExecutorService cogThreadPool;

    @Value("${preMakeDirs}")
    private String preMakeDirs;

    @Autowired
    private BaseOutput senseyex_raw_event_output;

    @Autowired
    FileAccessor fileAccessor;

    @Autowired
    BaseOutput device_status_event_output;

    @Autowired
    private BaseOutput senseyex_raw_video_event_output;

    @Autowired
    private XStoreHandler xStoreHandler;

    @Autowired
    private ApplicationContext applicationContext;

    //从接口调用来接收到的消息处理 目前是动态模型的低频流
    public ModelResult[] handleLowRateEvent(SenseyexRawImage message) {
        if (ArrayUtils.isEmpty(message.getProcessors()))
            throw new RuntimeException("processors should not be empty.");

        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

        long start = 0l;
        if (loggedForCost) {
            start = new Date().getTime();
        }

        if (loggedForCost) {
            log.info("[VideoHandleLog] [Cost] [handleLowRateEvent] step -1 intoHandle deviceId :{}, cost,{} ms", message.getDeviceId(), start - message.getCapturedTime());
        }

        long afterhandle = 0L;
        if (loggedForCost) {
            afterhandle = new Date().getTime();
        }

        String images[] = message.getImages();

        if (ArrayUtils.isEmpty(images))
            throw new RuntimeException("image should not be empty.");

        Processor[] processors = Arrays.stream(message.getProcessors())
                .map(p -> {
                    if (xStoreHandler.getXDynamicHandlerMap().containsKey(p))
                        return xStoreHandler.getLowProcessorMap().getOrDefault(message.getDeviceId(), Map.of()).getOrDefault(p, Processor.builder().processor(p).build());

                    return null;
                })
                .filter(Objects::nonNull)
                .toArray(Processor[]::new);

        if (ArrayUtils.isEmpty(processors))
            return new ModelResult[message.getProcessors().length];

        long now = Objects.requireNonNullElse(message.getCapturedTime(), System.currentTimeMillis());

        VideoFrame[] videoFrames = new VideoFrame[images.length];
        for (int index = 0; index < images.length; index++) {
            FramePoolUtils.useBufferKey("LowRate");
            if (StringUtils.length(images[index]) > 2048) {
                videoFrames[index] = FrameUtils.decode_up_down_load_Buffered_frame_base64(images[index]);
                images[index] = null;
            } else{
                if(!CommonUtils.matchesOsgImageUrl(images[index]))
                    videoFrames[index] = FrameUtils.decode_up_down_load_Buffered_frame_path(images[index]);
                else{
                    try{
                        byte[] bytes = fileAccessor.readImage(images[index]);
                        videoFrames[index] = FrameUtils.decode_up_down_load_from_buffered_memory(bytes);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }

            videoFrames[index].setCapturedTime(now);
        }
        long readModelResult = 0L;
        if (loggedForCost) {
            readModelResult = new Date().getTime();
            log.info("[VideoHandleLog] [Cost] [handleLowRateEvent] step -2 intoHandle deviceId :{}, cost,{} ms, req:{}", message.getDeviceId(), readModelResult - afterhandle, JSON.toJSONString(message));
        }

        VideoStreamInfra infra = new VideoStreamInfra();
        infra.setDeviceId(message.getDeviceId());
        infra.setVideoRate(25);
        infra.setRtspWidth(KestrelApi.kestrel_frame_video_width(videoFrames[0].getCpuFrame()));
        infra.setRtspHeight(KestrelApi.kestrel_frame_video_height(videoFrames[0].getCpuFrame()));

        Function<Processor, ModelResult> mapper = processor -> {
            XModelHandler handler = xStoreHandler.getXDynamicHandlerMap().get(processor.getProcessor());
            if (handler instanceof ConfigAccessor && Boolean.TRUE.equals(((ConfigAccessor) handler).getNeedContext())) {
                lowContextTimeMap.put(message.getDeviceId() + "___" + processor.getProcessor(), System.currentTimeMillis());
                try {
                    applicationContext.publishEvent(new XworkerStreamStartedEvent(infra, processor.getProcessor()));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            try {
                Processor processorMessage = JSON.parseObject(JSON.toJSONString(Objects.requireNonNullElse(message.getExtra(), Map.of()).get(processor.getProcessor())), Processor.class);
                if (processorMessage != null) {
                    processor = processorMessage;
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }

            SenseyexRawFrame entity = SenseyexRawFrame.builder()
                    .deviceInfra(infra)
                    .processor(processor)
                    .videoFrames(Arrays.stream(videoFrames).map(v -> v.ref()).toArray(VideoFrame[]::new))
                    .extra(message.getExtra())
                    .imageUrls(message.getImages())
                    .build();

            try {
                return handleHighRateEvent(entity);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            } finally {
                entity.close();
            }
        };

        try {
            return Arrays.stream(processors).parallel().map(mapper).toArray(ModelResult[]::new);
        } finally {
            FrameUtils.batch_free_frame(videoFrames);
        }
    }

    //高频流直接处理消息
    public ModelResult handleHighRateEvent(SenseyexRawFrame entity) {
        boolean logged = Utils.instance.watchFrameTiktokLevel == -7971;

        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789 || Utils.instance.printLog;

        long startHandleTime = 0l;
        if (loggedForCost) {
            startHandleTime = new Date().getTime();
        }
        Processor processor = entity.getProcessor();
        XModelHandler handler = xStoreHandler.getXDynamicHandlerMap().get(processor.getProcessor());

        ModelRequest modelRequest = ModelRequest.builder().handler(handler).videoFrames(entity.getVideoFrames()).processor(processor).build();
        modelRequest.getParameter().put("deviceId", entity.getDeviceInfra().getDeviceId());
        modelRequest.getParameter().put("deviceInfra", entity.getDeviceInfra());
        modelRequest.getParameter().putAll(Objects.requireNonNullElse(entity.getExtra(), Map.of()));
        if (entity.getLatch() != null) {
            Boolean isApiCalled = (Boolean) modelRequest.getParameter().get("isApiCalled");
            if (!Boolean.TRUE.equals(isApiCalled))
                modelRequest.getParameter().put("asap", true);
        }

        if (handler == null)
            return ModelResult.builder().modelRequest(modelRequest).build();
        if (loggedForCost) {
            if (entity.getVideoFrames().length > 0)
                log.info("[VideoHandleLog] [Cost] [intoHandleHigh] step 0 intoHandle deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, startHandleTime - entity.getVideoFrames()[0].getCapturedTime());
        }

        long start = 0l;
        if (loggedForCost) {
            start = new Date().getTime();
        }

        ModelResult modelResult = handler.handle(modelRequest);

        long afterhandle = 0L;
        if (loggedForCost) {
            afterhandle = new Date().getTime();
        }

        if (loggedForCost) {
            long handleCost = afterhandle - start;
            log.info("[VideoHandleLog] [Cost] [intoHandleHigh] step 1 handle deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, handleCost);
            if (handleCost > 1000) {
                log.warn("[VideoHandleLog] [Cost] [intoHandleHigh] step 1 handle deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, handleCost);
            }
        }

        VideoStreamXWorker monitorWorker = xStoreHandler.getOngoingVideoStreamMap().get(entity.getDeviceInfra().getDeviceId());
        try {
            long postPipeTime = 0L;
            if (logged) {
                postPipeTime = new Date().getTime();
            }
            handler.readModelResult(modelResult);
            long readModelResult = 0l;
            if (loggedForCost) {
                readModelResult = new Date().getTime();
                log.info("[VideoHandleLog] [Cost] [intoHandleHigh] step 2 readModelResult deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms, kesonResult:{}", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, readModelResult - afterhandle, ((modelResult.getKeson()!=null)?KesonUtils.kesonToJson(modelResult.getKeson()):null));
            }

            boolean validate = handler.validateOutputValue(modelResult);

            long validateCost = 0l;
            if (loggedForCost) {
                validateCost = new Date().getTime();
                log.info("[VideoHandleLog] [Cost] [intoHandleHigh] step 3 validateOutputValue deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms, validate:{}", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, validateCost - readModelResult, validate);
            }

            if (validate) {
                handler.buildOutputValue(modelResult);
                long buildOutputValueCost = 0l;
                if (loggedForCost) {
                    buildOutputValueCost = new Date().getTime();
                    log.info("[VideoHandleLog] [Cost] [intoHandleHigh] step 4 buildOutputValue deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, buildOutputValueCost - validateCost);
                }
            }
            long afterhandlePipe = 0L;
            if (logged) {
                afterhandlePipe = new Date().getTime();
                long handlePipeCost = afterhandlePipe - postPipeTime;
                if (handlePipeCost  > Utils.instance.longAfterPipeCost) {
                    log.warn("[VideoHandleLog] [Cost] [intoHandleHigh] handlePipeCost step 5 handle deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, handlePipeCost);
                }
            }
        } catch (Throwable e) {
            if (monitorWorker != null) {
                monitorWorker.getMonitor().getAndIncrementUnHandled();
                log.error("[VideoHandleLog] unhandled frame deviceId {} annotator {} unhandledTotal:{} unhandled_in_minute:{}. exception:{}", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), monitorWorker.getMonitor().unhandledTotal.get(), monitorWorker.getMonitor().getUnhandledCount(), e.getMessage());
            }
            e.printStackTrace();
        } finally {
            handler.releaseModelResult(modelResult);
            if (monitorWorker != null) {
                monitorWorker.getMonitor().getAndIncrementHandled();
                if (logged) {
                    if (monitorWorker.getMonitor().handledTotal.get() % 50 == 0) {
                        log.info("[VideoHandleLog] XModel handle deviceID {}, annotator {} handledTotal:{} handle_in_minute :{}", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), monitorWorker.getMonitor().handledTotal.get(), monitorWorker.getMonitor().getHandledCount());
                    }
                }
            }


        }

        entity.setModelResult(modelResult);

        Object outputObject = modelResult.getOutputResult();
        if (outputObject == null)
            return modelResult;

        boolean sendMessage = !Boolean.FALSE.equals(modelRequest.getParameter().getOrDefault("sendMessage", true));
        if (sendMessage) {

            long beforeSend = 0l;
            if (loggedForCost) {
                beforeSend = new Date().getTime();
                //log.info("[VideoHandleLog] [Cost] [intoHandleHigh] step 3 validateOutputValue deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms" ,entity.getDeviceInfra().getDeviceId(),modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, validateCost - readModelResult);
            }
            // todo 热加载 Img save tag

            if (processor.getImgSaveTag() == 1) {
                // modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
                String[] imagesUrls = entity.getImageUrls();
                if (imagesUrls == null)
                    imagesUrls = new String[entity.getVideoFrames().length];

                for (int index = 0; index < imagesUrls.length; index++)
                    imagesUrls[index] = FrameUtils.NOIMAGE;

                entity.setImageUrls(imagesUrls);

            } else {
                // 原来逻辑
                String replaceImageUrls[] = (String[]) modelResult.getModelRequest().getParameter().remove("replaceImageUrls");
                if (!ArrayUtils.isEmpty(replaceImageUrls)) {
                    String[] imagesUrls = entity.getImageUrls();
                    if (imagesUrls == null)
                        imagesUrls = new String[entity.getVideoFrames().length];

                    for (int index = 0; index < imagesUrls.length && index < replaceImageUrls.length; index++)
                        imagesUrls[index] = replaceImageUrls[index];

                    entity.setImageUrls(imagesUrls);
                }
            }

            if (outputObject.getClass().isArray() && Array.getLength(outputObject) == entity.getVideoFrames().length) {
                int length = Array.getLength(outputObject);
                for (int index = 0; index < length; index++) {
                    Object output = Array.get(outputObject, index);
                    if (output == null)
                        continue;

                    sendOutputObject(entity, index, processor.getProcessor(), output);

                    if (handler instanceof AbstractXModelHandler)
                        ((AbstractXModelHandler) handler).getMonitor().getAndIncrementSended();

                    if (monitorWorker != null)
                        monitorWorker.getMonitor().getAndIncrementSended();
                }
            } else {
                int index = (entity.getVideoFrames().length - 1) / 2;
                sendOutputObject(entity, index, processor.getProcessor(), outputObject);

                if (handler instanceof AbstractXModelHandler)
                    ((AbstractXModelHandler) handler).getMonitor().getAndIncrementSended();
                if (monitorWorker != null)
                    monitorWorker.getMonitor().getAndIncrementSended();
            }

            if (loggedForCost) {
                long aftersend = new Date().getTime();
                log.info("[VideoHandleLog] [Cost] [intoHandleHigh] step 5 sendMessage and saveImg deviceId :{}, annotator: {}, frameSize:{}, cost,{} ms", entity.getDeviceInfra().getDeviceId(), modelRequest.getHandler().annotatorName(), modelRequest.getVideoFrames().length, aftersend - beforeSend);
            }
        }

        return modelResult;
    }

    private void sendOutputObject(SenseyexRawFrame entity, int index, String annotatorName, Object outputObject) {
        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

        long frameSaveStart = new Date().getTime();
        //Boolean preCheckPath = Arrays.asList(preMakeDirs.split(",")).contains(entity.processor.getProcessor());
        String imageUrl = entity.saveImage(index);

        if (loggedForCost) {
            long cost = new Date().getTime() - frameSaveStart;
            log.info("[VideoHandleLog] [Cost] saveImage deviceId: {}, annotator: {}, cost: {} ms", entity.getDeviceInfra().getDeviceId(), annotatorName, cost);
        }

        String outputString = JSON.toJSONString(outputObject, SerializerFeature.DisableCircularReferenceDetect);

        Pointer imageFrame = Objects.requireNonNullElse(entity.getVideoFrames()[index].getGpuFrame(), entity.getVideoFrames()[index].getCpuFrame());

        if (Utils.instance.debug_0) {
            try {
                List<Map<String, Object>> outputResult = (List<Map<String, Object>>) outputObject;
                int i = 0;
                for (Map<String, Object> valueResult : outputResult) {
                    Map<String, Integer> detect = (Map<String, Integer>) valueResult.get("detect");
                    if (org.apache.commons.collections4.MapUtils.isEmpty(detect))
                        continue;
                    Pointer crop = FrameUtils.roi_frame(imageFrame, detect.get("left"), detect.get("top"), detect.get("width"), detect.get("height"), Utils.instance.clipExtend);
                    String filePath = ImageUtils.newFileName("dropFace", imageUrl.substring(imageUrl.lastIndexOf("/") + 1) + "_" + i, annotatorName).getAbsolutePath();
                    int response = KestrelApi.kestrel_frame_save(crop, filePath);
                    entity.getExtra().put("clipCropImage_" + i, filePath);
                    i++;
                    log.info("save clip image {} to {}, response {}", annotatorName, filePath, response);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }

        long receivedTime = System.currentTimeMillis();
        long captureTime = KestrelApi.kestrel_frame_pts(imageFrame);
        if (entity.getExtra() != null) {
            entity.getExtra().put("frameIndex", entity.getVideoFrames()[0].getFrameIndex());
            entity.getExtra().put("captureToKafkaTime", receivedTime - captureTime);
            if (entity.getExtra().containsKey("startFrameTime")) {
                entity.getExtra().put("startFrameToKafkaTime", receivedTime - ((Number) entity.getExtra().get("startFrameTime")).longValue());
            }
        }
        KafkaXWorkerConvertorBuilder builder = KafkaXWorkerConvertor.builder()
                .appName(annotatorName)
                .objectType(annotatorName)
                .deviceId(entity.getDeviceInfra().getDeviceId())
                .imageUrl(imageUrl)
                .value(outputString.replaceAll("\"", "\\\\\\\\\\\""))
                .capturedTime(captureTime)
                .receivedTime(receivedTime)
                .extraInfo(JSON.toJSONString(entity.getExtra()))
                .frameIndex(entity.getVideoFrames()[0].getFrameIndex())
                .framePts(entity.getVideoFrames()[0].getCapturedTime());

        String messageStr = builder.build().generateXworkerResult();

        if (Utils.instance.logged) {


            String logged = "\n**********" + new SimpleDateFormat("yy-MM-dd HH:mm:ss").format(new Date()) + "************\n";
            logged += "output deviceId: [" + entity.getDeviceInfra().getDeviceId() + "] annotator: [" + annotatorName + "], output image: [" + imageUrl + "] pts[" + KestrelApi.kestrel_frame_pts(imageFrame) + "] plane[" + KestrelApi.kestrel_frame_plane_origin(imageFrame, 0) + "] frameIndex:" + entity.getVideoFrames()[0].getFrameIndex() + " capturedTime:" + Utils.dateFormat.get().format(entity.getVideoFrames()[index].getCapturedTime()) + "\n";
            logged += "output value: " + outputString + "\n\n";
            logged += "extra value: " + JSON.toJSONString(entity.getExtra()) + "\n\n";
            log.info(logged);
        }

        long cost = receivedTime - KestrelApi.kestrel_frame_pts(imageFrame);
        if (cost > Utils.instance.longCostTs) {
            log.warn("[VideoHandleLog] [Cost] frame long cost deviceId: {}, annotator: {}, from capture to kafka time cost: {} ms", entity.getDeviceInfra().getDeviceId(), annotatorName, cost);
        }
        if (loggedForCost) {
            log.info("[VideoHandleLog] [Cost] frame deviceId: {}, annotator: {}, from capture to kafka time cost: {} ms", entity.getDeviceInfra().getDeviceId(), annotatorName, cost);
        }

        long beforekafka = new Date().getTime();
        try {
            Boolean isApiCalled = (Boolean) entity.getExtra().getOrDefault("isApiCalled", false);
            if (isApiCalled)
                senseyex_raw_video_event_output.send(MessageBuilder.withPayload(messageStr).setHeader(KafkaHeaders.KEY, entity.getDeviceInfra().getDeviceId().getBytes()).build());
            else
                senseyex_raw_event_output.send(MessageBuilder.withPayload(messageStr).setHeader(KafkaHeaders.KEY, entity.getDeviceInfra().getDeviceId().getBytes()).build());

            if (loggedForCost) {
                cost = new Date().getTime() - beforekafka;
                log.info("[VideoHandleLog] [Cost] kafka deviceId: {}, annotator: {}, from capture to kafka time cost: {} ms", entity.getDeviceInfra().getDeviceId(), annotatorName, cost);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostConstruct
    public void postConstruct() {
        Runnable runnable = () -> {
            while (true) {
                boolean idle = false;

                idle |= handleSenseyexRawImage(seneyexRawImageHandleQueue.poll());

                if (tempStreamSemaphore.tryAcquire())
                    idle |= handleSenseyexRawVideo(seneyexRawVideoHandleQueue.poll());

                for (LinkedBlockingQueue<SenseyexRawFrame> queue : highRateHandleMap.values())
                    idle |= handleSenseyexRawFrame(queue.poll());

                if (!idle)
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                    }
            }
        };

        Thread workerRunnerThread = new Thread(runnable);
        workerRunnerThread.setDaemon(true);
        workerRunnerThread.setPriority(Thread.MAX_PRIORITY);
        workerRunnerThread.setName("XWorkerRunnerThread");
        workerRunnerThread.start();
    }

    @Scheduled(fixedDelay = 60 * 1000)
    synchronized void heartBeatModel() {
        Iterator<Entry<String, Long>> its = lowContextTimeMap.entrySet().iterator();
        while (its.hasNext()) {
            Entry<String, Long> entry = its.next();
            if (System.currentTimeMillis() - entry.getValue() < 5 * 60 * 1000)
                continue;

            its.remove();

            log.info("[" + entry.getKey() + "] low rate annotator close context.");

            try {
                String keys[] = entry.getKey().split("___");
                applicationContext.publishEvent(new XworkerStreamClosedEvent(keys[0], keys[1], XworkerStreamClosedEvent.CLOSE));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private boolean handleSenseyexRawFrame(SenseyexRawFrame entity) {
        if (entity == null)
            return false;

        boolean logged = Utils.instance.watchFrameTiktokLevel == -789;

        try {
            cogThreadPool.submit(() -> {
                long now = 0;
                if (logged)
                    now = System.currentTimeMillis();

                try {
                    Initializer.bindDeviceOrNot();
                    handleHighRateEvent(entity);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    entity.close();

                    if (logged)
                        log.info("[VideoHandleLog] [Cost] HandleHigh [" + entity.getDeviceInfra().getDeviceId() + " : " + entity.getProcessor().getProcessor() + "] handle size :[ " + entity.getVideoFrames().length + "] Cost : " + (System.currentTimeMillis() - now));
                }
            });

            if(Utils.instance.printLog) {
                // Print thread pool status after submitting the task
                if (cogThreadPool instanceof ThreadPoolExecutor) {
                    ThreadPoolExecutor executor = (ThreadPoolExecutor) cogThreadPool;
                    log.info("ThreadPoolStatus: Active Threads: {}, Pool Size: {},MaxPool Size: {}, Queue Size: {}, Completed Tasks: {}",
                            executor.getActiveCount(),
                            executor.getPoolSize(),
                            executor.getMaximumPoolSize(),
                            executor.getQueue().size(),
                            executor.getCompletedTaskCount());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            entity.close();
        }
  //2.13-obk
//        catch (RejectedExecutionException e) {
//            if (cogThreadPool instanceof ThreadPoolExecutor) {
//                ThreadPoolExecutor executor = (ThreadPoolExecutor) cogThreadPool;
//                //if (executor.getPoolSize() >= 1024) {
//                    log.warn("ThreadPool is full. Clearing the queue to prevent RejectedExecutionException.{}", executor.getPoolSize());
//                    executor.getQueue().clear();
//                //}
//            }
//            e.printStackTrace();
//            entity.close();
//        }



        return true;
    }

    private boolean handleSenseyexRawImage(SenseyexRawImage message) {
        if (message == null)
            return false;

        boolean logged = Utils.instance.watchFrameTiktokLevel == -789;

        try {
            cogThreadPool.submit(() -> {
                long now = 0;
                if (logged)
                    now = System.currentTimeMillis();

                List<Object> result = new ArrayList<>();
                try {
                    Initializer.bindDeviceOrNot();
                    ModelResult[] modelResults  = handleLowRateEvent(message);
                    result = Arrays.stream(modelResults).map(r -> r == null ? null : r.getOutputResult()).collect(Collectors.toList());
                } catch (Exception e){
                    log.error(">>> handleSenseyexRawImage failed",e);
                } finally {
                    if (Boolean.TRUE.equals(MapUtils.getBoolean(message.getExtra(), "response"))) {
                        message.getExtra().put("response", result);
                        synchronized (message) {
                            message.notifyAll();
                        }
                    }

                    if (logged) {
                        if (ArrayUtils.isEmpty(message.getProcessors()))
                            log.info("[VideoHandleLog] [Cost] HandleLow [" + message.getDeviceId() + "] Cost : " + (System.currentTimeMillis() - now));
                        else
                            log.info("[VideoHandleLog] [Cost] HandleLow [" + message.getDeviceId() + " : " + Arrays.stream(message.getProcessors()).collect(Collectors.joining(",")) + "] Cost : " + (System.currentTimeMillis() - now));
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }

    private boolean handleSenseyexRawVideo(SenseyexRawVideo message) {
        if (message == null) {
            tempStreamSemaphore.release();
            return false;
        }

        boolean logged = Utils.instance.watchFrameTiktokLevel == -789;

        try {
            cogThreadPool.submit(() -> {
                long now = 0;
                if (logged)
                    now = System.currentTimeMillis();

                try {
                    Initializer.bindDeviceOrNot();
                    xStoreHandler.executeTemporaryStream(message);
                } finally {
                    tempStreamSemaphore.release();

                    if (logged)
                        if (ArrayUtils.isEmpty(message.getProcessors()))
                            log.info("[VideoHandleLog] [Cost] [VideoHandleLog] [Cost] HandleVideo [" + message.getDeviceId() + "] Cost : " + (System.currentTimeMillis() - now));
                        else
                            log.info("[VideoHandleLog] [Cost] [VideoHandleLog] [Cost] HandleVideo [" + message.getDeviceId() + " : " + Arrays.stream(message.getProcessors()).collect(Collectors.joining(",")) + "] Cost : " + (System.currentTimeMillis() - now));
                }
            });
        } catch (Exception e) {
            tempStreamSemaphore.release();
            ;
            e.printStackTrace();
        }

        return true;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Accessors(chain = true)
    public static class SenseyexRawFrame implements AutoCloseable {
        private VideoStreamInfra deviceInfra;
        private Processor processor;
        private VideoFrame[] videoFrames;
        private Map<String, Object> extra;

        private String[] imageUrls;//低频流会直接传入此字段  高频流只有帧所以这个字段为空需要存图时候写入
        private ModelResult modelResult;
        private CountDownLatch latch;

        public synchronized String saveImage(int index) {

            boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

            if (loggedForCost) {
                log.info("[VideoHandleLog] [Cost] saveImage start at now");
            }

            if (modelResult == null || modelResult.getOutputResult() == null)
                return StringUtils.EMPTY;


            Boolean isApiCalled = (Boolean) extra.getOrDefault("isApiCalled", false);

            boolean checkSaveImage = isApiCalled && processor.getImgSaveTag() == 0;


            if ((imageUrls != null && StringUtils.isNotBlank(imageUrls[index])) && !checkSaveImage)
                return imageUrls[index];

            imageUrls = new String[videoFrames.length];
            Pointer imageFrame = Objects.requireNonNullElse(videoFrames[index].getGpuFrame(), videoFrames[index].getCpuFrame());
            if (loggedForCost) {
                log.info("[VideoHandleLog] [Cost] saveImage imageFrame start at now");
            }

            imageUrls[index] = FrameUtils.save_image_as_jpg(imageFrame, ImageUtils.newFile(processor.getProcessor()), 0);
            return imageUrls[index];
        }

        public void setModelResult(ModelResult modelResult) {
            this.modelResult = modelResult;

            if (latch != null)
                latch.countDown();
        }

        @Override
        public synchronized void close() {
            if (latch != null)
                latch.countDown();

            FrameUtils.batch_free_frame(videoFrames);
        }
    }
}