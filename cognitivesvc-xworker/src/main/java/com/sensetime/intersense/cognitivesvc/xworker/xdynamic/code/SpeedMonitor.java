package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;
import java.awt.Polygon;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;

import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.opencv_core.CvScalar;

@Slf4j
public class SpeedMonitor extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{

    @Getter
    protected final Integer interval = 3;

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final Boolean needContext = true;

    @Getter
    protected final String decoderFormat = "nv12";

    @Getter
    protected final Integer frameBuffer = 24;

    @Getter
    protected final String frameBufferStrategy = "smart";

    @Getter
    protected final boolean lazyInit = true;

    @Getter
    protected final boolean queueMap = true;

    //<contextid, trackid, track>
    protected final ConcurrentHashMap<Long, Map<Integer, Track>> trackingMap = new ConcurrentHashMap<Long, Map<Integer, Track>>();

    /** 将批量处理结果 整理后放到每个项目 */
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Pointer frames[] = handlingList.stream()
                .map(item -> handlerEntity.getCpuModelDup() > 0 ? item.getModelRequest().getVideoFrames()[0].getCpuFrame() : item.getModelRequest().getVideoFrames()[0].getGpuFrame())
                .toArray(Pointer[]::new);

        Object dropIds = KesonUtils.kesonToJson(KestrelApi.keson_get_array_item(outputKesons[0].getValue(), 4));

        PointerByReference[] sub_kesons = KesonUtils.splitFlockKesonAndDestroy(KesonUtils.mergeRenameAllToFirstFlockKeson(KesonUtils.tryReformFlockKeson(outputKesons)), frames);
        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.getModelRequest().getParameter().put("dropIds", dropIds);
            item.setKeson(sub_kesons[index]);
        }
        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -791;
        if(loggedForCost)
           log.info("batch_handle_extract_result:{}", KesonUtils.kesonToJson(outputKesons[0]));
    }

    @SuppressWarnings("unchecked")
    @Override
    public void readModelResult(ModelResult modelResult) {
        super.readModelResult(modelResult);

        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();
        if(detectResult != null) {
            List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of());
            Set<Integer> trackIds = targets.stream().map(target -> ((Number)target.get("track_id")).intValue()).collect(Collectors.toSet());

            modelResult.getModelRequest().getParameter().put("trackIds", trackIds);
        }
    }
    @SuppressWarnings("unchecked")
    @Override
    public void releaseModelResult(ModelResult modelResult) {
        super.releaseModelResult(modelResult);

        Map<String, Object> dropItem = (Map<String, Object>)modelResult.getModelRequest().getParameter().get("dropIds");
        List<Map<String, Object>> dropTargets = (List<Map<String, Object>>)dropItem.get("targets");

        long contextId = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));

        for(Map<String, Object> dropTarget : dropTargets)
            if(((Number)dropTarget.get("image_id")).longValue() == contextId)
                trackingMap.get(contextId).remove(((Number)dropTarget.get("dropped_track_id")).intValue());
    }

    @SuppressWarnings({ "unchecked"})
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();
        if(MapUtils.isEmpty(detectResult))
            return false;

        Map<Integer, Track> trackMap = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        if(trackMap == null)
            return false;

        Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        Map<Integer, Map<Integer, Map<String, Object>>> labelExtrasMap = (Map<Integer, Map<Integer, Map<String, Object>>>)processor.fetchExtras(function);
        List<String> roidIds = (List<String>)labelExtrasMap.getOrDefault(Integer.MAX_VALUE, Map.of()).getOrDefault(Integer.MAX_VALUE, Map.of()).get("roiIds");

        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of());
        Iterator<Map<String, Object>> its = targets.iterator();
        while(its.hasNext()) {
            Map<String, Object> target = its.next();

            int trackId = ((Number)target.get("track_id")).intValue();
            int label = ((Number)target.get("label")).intValue();
            float quality = (((Number)target.get("confidence")).floatValue());

            Track track = trackMap.get(trackId);
            if(track == null) {
                track = Track.builder().trackId(trackId).build();
                trackMap.put(trackId, track);
            }
            track.setCurrentPosition((Map<String, Number>)target.get("roi"));

            boolean bingo = false;

            for(int index = 0 ;index < polygons.length; index ++) {
                Map<String, Object> extra = labelExtrasMap.getOrDefault(index, Map.of()).get(label);
                if(extra == null)
                    continue;

                Polygon polygon = polygons[index];
                Number threshold = (Number)extra.getOrDefault("threshold", processor.getThreshold());
                target.put("threshold", threshold);

                Integer minSize = (Integer)extra.getOrDefault("minSize", processor.getMinSize());
                Integer maxSize = (Integer)extra.getOrDefault("maxSize", processor.getMaxSize());

                boolean baseCheck = true;
                baseCheck &= threshold == null || quality > threshold.floatValue();
                baseCheck &= minSize == null || (track.getCurrentPosition().getWidth() >= minSize.intValue() && track.getCurrentPosition().getHeight() >= minSize.intValue());
                baseCheck &= maxSize == null || (track.getCurrentPosition().getWidth() <= maxSize.intValue() && track.getCurrentPosition().getHeight() <= maxSize.intValue());

                if(!baseCheck)
                    continue;

                Number positionType = (Number)extra.getOrDefault("positionType", 0);

                //利用人体中心点作为目标点
                int currentX  = track.getCurrentPosition().getLeft()  + track.getCurrentPosition().getWidth()   / 2;
                int currentY  = track.getCurrentPosition().getTop()   + track.getCurrentPosition().getHeight()  / 2;
                if(positionType.intValue() == 1) //利用头顶顶点作为目标点
                    currentY  = track.getCurrentPosition().getTop();
                else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                    currentY  = track.getCurrentPosition().getTop()   + track.getCurrentPosition().getHeight();

                List<Number> crossDot = (List<Number>)extra.getOrDefault("crossDot", List.of());
                if(!crossDot.isEmpty() && track.getPreviousPosition() != null) {
                    int previousX = track.getPreviousPosition().getLeft() + track.getPreviousPosition().getWidth()  / 2;
                    int previousY = track.getPreviousPosition().getTop()  + track.getPreviousPosition().getHeight() / 2;

                    if(positionType.intValue() == 1)//利用头顶顶点作为目标点
                        previousY = track.getPreviousPosition().getTop();
                    else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                        previousY = track.getPreviousPosition().getTop()  + track.getPreviousPosition().getHeight();

                    int cx, cy;
                    if(crossDot.size() != 2) {
                        cx = Arrays.stream(polygon.xpoints).sum() / polygon.npoints;
                        cy = Arrays.stream(polygon.ypoints).sum() / polygon.npoints;
                    }else {
                        cx = crossDot.get(0).intValue();
                        cy = crossDot.get(1).intValue();
                    }

                    String crossDirection = (String)extra.get("crossDirection");
                    for(int dotIndex = 0; dotIndex < polygon.npoints - 1 || (polygon.npoints > 2 && dotIndex == polygon.npoints - 1); dotIndex ++) {
                        int ax = polygon.xpoints[dotIndex];
                        int ay = polygon.ypoints[dotIndex];

                        int bx = (dotIndex == polygon.npoints - 1) ? polygon.xpoints[0] : polygon.xpoints[dotIndex + 1];
                        int by = (dotIndex == polygon.npoints - 1) ? polygon.ypoints[0] : polygon.ypoints[dotIndex + 1];

                        //log.info("index:{}, ax:{}, ay:{}, bx:{}, by:{}, cx:{},cy:{}", index, ax, ay,bx, by, cx, cy);

                        if(!intersect(ax, ay, bx, by, previousX, previousY, currentX, currentY))
                            continue;

                        boolean previousIntersected = intersect(ax, ay, bx, by, previousX, previousY, cx, cy);
                        boolean targetIntersected   = intersect(ax, ay, bx, by, currentX , currentY , cx, cy);

                        //log.info("index:{}, trackId:{}, previousIntersected:{}, targetIntersected:{}", index, track.trackId, previousIntersected, targetIntersected);


                        String direction = "unknown";
                        if(previousIntersected )
                            direction = "forward";
                        else if(!previousIntersected )
                            direction = "backward";

                        if(StringUtils.isNotBlank(crossDirection) && !crossDirection.equals(direction))
                            continue;

                        bingo = true;

                        track.getCrosses().add(Track.RoiCross.builder()
                                .roiId(roidIds.get(index))
                                .roiIndex(index)
                                .frameIndex(modelResult.getModelRequest().getVideoFrames()[0].getFrameIndex())
                                .pts(modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime())
                                .build());
                    }
                }
            }
            // 没发生跨线, 或 <= 1
            if(!bingo || track.getCrosses().size() <= 1)
                its.remove();
            else {
                List<Track.RoiCross> removeDuplicates = removeDuplicatesAndKeepMaxPts(track.getCrosses());
                if(removeDuplicates.size()<=1){
                    its.remove();
                }
                log.info("track {} Crosses size:{}  removeDuplicates size:{}", track.trackId, track.crosses.size(),removeDuplicates.size());
            }
        }

        return !targets.isEmpty();
    }

    @SuppressWarnings({ "unchecked"})
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        Map<Integer, Track> trackMap = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        if(trackMap == null)
            return ;

        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();
        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();

        List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
        modelResult.setOutputResult(resultList);

        for(Map<String, Object> target : (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of())) {
            Integer trackId = ((Number)target.get("track_id")).intValue();
            Track track = trackMap.get(trackId);
            if(track == null)
                continue;

            Map<String, Object> result = new HashMap<String, Object>();

            result.put("trackId",      trackId);
            result.put("deviceId",     deviceId);
            result.put("detect",       target.get("roi"));
            result.put("targetType",   target.get("label"));
            result.put("confidence",   target.get("confidence"));
            result.put("capturedTime", target.get("capturedTime"));
            result.put("crosses",      removeDuplicatesAndKeepMaxPts(track.getCrosses()));
            //log.info("buildOutputValue track {} Crosses size:{}", track.trackId, track.crosses.size());
            resultList.add(result);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        Processor processor = modelResult.getModelRequest().getProcessor();

        long frameIndex = modelResult.getModelRequest().getVideoFrames()[0].getFrameIndex();

        for(Integer[][] roi : processor.getRoi()) {
            for(int index = 0; index < roi.length - 1; index ++) {
                result.add(Line.builder().from(new int[] {roi[index][0], roi[index][1]}).to(new int[] {roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().from(new int[] {roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[] {roi[0][0], roi[0][1]}).build());
        }

        Map<Integer, Map<Integer, Map<String, Object>>> extras = (Map<Integer, Map<Integer, Map<String, Object>>>)processor.fetchExtras(function);
        for(Map<Integer, Map<String, Object>> top : extras.values())
            for(Map<String, Object> sub : top.values()) {
                List<Integer> crossDot = (List<Integer>)sub.get("crossDot");
                if(CollectionUtils.isNotEmpty(crossDot)) {
                    result.add(Line.builder().from(new int[] {crossDot.get(0) - 5, crossDot.get(1) - 5}).to(new int[] {crossDot.get(0) + 5, crossDot.get(1) + 5}).build());
                    result.add(Line.builder().from(new int[] {crossDot.get(0) + 5, crossDot.get(1) - 5}).to(new int[] {crossDot.get(0) - 5, crossDot.get(1) + 5}).build());
                }
            }

//        Object outputResult = modelResult.getOutputResult();
//        if(outputResult != null) {
//            List<Map<String, Object>> outputs = List.of();
//            if(outputResult.getClass().isArray()) {
//                outputs = Arrays.stream((List<Map<String, Object>>[])outputResult).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
//            }else if(outputResult instanceof List){
//                outputs = (List<Map<String, Object>>)outputResult;
//            }
//            CvScalar color = opencv_core.CV_RGB(0, 0, 255); // red
//            for(Map<String, Object> output : outputs) {
//                String text = "";
//
//                if(output.containsKey("trackId"))
//                    text += "trackId:[" + output.get("trackId") + "]";
//
//                if(frameIndex > 0)
//                    text += "frameIndex:[" + frameIndex + "]";
//
//                Map<String, Integer> roi = (Map<String, Integer>)output.get("detect");
//                result.add(Rect.builder().processor(annotatorName()).color(color).text(text).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());
//            }
//        }

        Set<Integer> trackIds = (Set<Integer>)modelResult.getModelRequest().getParameter().getOrDefault("trackIds", Set.of());
        CvScalar color = opencv_core.CV_RGB(0, 0, 255); // red
        Map<String, Object> detectResult = (Map<String, Object>)modelResult.getDetectResult();

        if(MapUtils.isEmpty(detectResult))
            return result;
        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.getOrDefault("targets", List.of());
        Iterator<Map<String, Object>> its = targets.iterator();

        while(its.hasNext()) {
            Map<String, Object> target = its.next();

            int trackId = ((Number) target.get("track_id")).intValue();

            Map<String, Number> rois = (Map<String, Number>)target.get("roi");


            result.add(Rect.builder().processor(annotatorName()).color(color).text(trackId + "-" + frameIndex).top(rois.get("top").intValue()).left(rois.get("left").intValue()).width(rois.get("width").intValue()).height(rois.get("height").intValue()).build());

        }

        return result;
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            long contextId = Utils.keyToContextId(event.getDeviceId());

            try { Thread.sleep(1000); } catch (InterruptedException x) { }
            stopContext(contextId);
        }
    }

    @PostConstruct
    @Override
    public synchronized void initialize() {
        super.initialize();

        Object flockPipeline = JSON.parseObject(handlerEntity.getFlockConfig(), Map.class);
        log.info("roi Intrude Stay initializing : " + JSON.toJSONString(flockPipeline));
    }

    @PreDestroy
    @Override
    public synchronized void destroy() {
        super.destroy();

        log.info("roi Intrude Stay destroying.");
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        if(trackingMap.containsKey(contextId))
            return ;

        trackingMap.put(contextId, new ConcurrentHashMap<Integer, Track>());

        log.info("RoiIntrudeStay start context [" + contextId + "].");
    }

    private void stopContext(long contextId) {
        if(!trackingMap.containsKey(contextId))
            return ;

        trackingMap.remove(contextId);

        holders[0].controlForRemoveSource(contextId);

        log.info("RoiIntrudeStay stop context [" + contextId + "].");
    }

    @SuppressWarnings({ "unchecked", "rawtypes"})
    private static final Function<Object, Map> function = new Function<Object, Map>() {

        @Override
        public Map apply(Object e) {
            List<Map<String, Object>> params = (List<Map<String, Object>>)e;

            Map<Integer, Map<Integer, Map<String, Object>>> result = new HashMap<Integer, Map<Integer, Map<String, Object>>>();
            for(Map<String, Object> param : params) {
                if(!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer)param.getOrDefault("roiIndex", 0);
                Object targetLabel = param.get("targetType");

                Map<Integer, Map<String, Object>> roiParam = result.get(roiIndex);
                if(roiParam == null) {
                    roiParam = new HashMap<Integer, Map<String, Object>>();
                    result.put(roiIndex, roiParam);
                }

                if(targetLabel == null) {
                    roiParam.put(221488,  param);
                    roiParam.put(1420,    param);
                    roiParam.put(1507442, param);
                    roiParam.put(2125610, param);
                }else {
                    if(targetLabel instanceof Number)
                        roiParam.put(((Number)targetLabel).intValue(), param);
                    else if(targetLabel instanceof String)
                        for(String label : ((String) targetLabel).split(","))
                            roiParam.put(Integer.parseInt(label), param);
                }
            }

            for(Map<String, Object> param : params) {
                if("roiIds".equals(param.get("type"))) {
                    result.put(Integer.MAX_VALUE, Map.of(Integer.MAX_VALUE, Map.of("roiIds", param.get("roiIds"))));
                    break;
                }
            }

            return result;
        }
    };

    private static final boolean intersect(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4) {
        float bx = x2 - x1;
        float by = y2 - y1;
        float dx = x4 - x3;
        float dy = y4 - y3;
        float b_dot_d_perp = bx * dy - by * dx;

        if (b_dot_d_perp == 0)
            return false;

        float cx = x3 - x1;
        float cy = y3 - y1;
        float t = (cx * dy - cy * dx) / b_dot_d_perp;
        if (t < 0 || t > 1)
            return false;

        float u = (cx * by - cy * bx) / b_dot_d_perp;
        if (u < 0 || u > 1)
            return false;

        return true;
    }

    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class Track{
        private int trackId;

        private Rect previousPosition;
        private Rect currentPosition;

        @Builder.Default
        private List<RoiCross> crosses = new ArrayList<RoiCross>();

        public void setCurrentPosition(Map<String, Number> roi) {
            previousPosition = currentPosition;
            currentPosition = Rect.builder().top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).build();
        }

        @Data
        @Accessors(chain = true)
        @Builder
        public static class Rect{
            private int left;
            private int top;
            private int width;
            private int height;
        }

        @Data
        @Accessors(chain = true)
        @Builder
        public static class RoiCross{
            private String roiId;
            private int roiIndex;
            private long frameIndex;
            private long pts;
        }
    }

    public List<Track.RoiCross> removeDuplicatesAndKeepMaxPts(List<Track.RoiCross> crosses) {
        return crosses.stream()
                .collect(Collectors.toMap(
                        Track.RoiCross::getRoiIndex, // 使用 roiIndex 作为键
                        roiCross -> roiCross,  // 使用 RoiCross 本身作为值
                        (existing, replacement) -> // 如果相同索引，选择 pts 最大的一个
                                (existing.getPts() >= replacement.getPts()) ? existing : replacement
                ))
                .values() // 获取 Map 中的值
                .stream() // 返回流
                .collect(Collectors.toList()); // 生成新的 List
    }
}