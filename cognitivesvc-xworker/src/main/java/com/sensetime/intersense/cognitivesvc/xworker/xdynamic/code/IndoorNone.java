package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.awt.Polygon;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;

import lombok.Getter;

@SuppressWarnings({"unchecked"})
public class IndoorNone extends DynamicXModelHandler{
    
    @Getter
    protected boolean lazyInit = true;
    
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        boolean exist = super.validateOutputValue(modelResult);
        
        Polygon[] polygons = modelResult.getModelRequest().getProcessor().fetchPolygons();
        if(ArrayUtils.isEmpty(polygons))
            return !exist;
        
        List<String> roiIdStrings = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getExtras(), List.of())
                .stream()
                .filter(extra -> Processor.ROIIDS.equals(((Map<String, Object>)extra).get("type")))
                .map(extra -> (List<String>)((Map<String, Object>)extra).getOrDefault(Processor.ROIIDS, List.of()))
                .findAny()
                .orElse(List.of());
        
        List<String> emptyRois = IntStream.range(0, polygons.length).mapToObj(id -> id < roiIdStrings.size() ? roiIdStrings.get(id) : String.valueOf(id)).collect(Collectors.toList());
        
        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)Objects.requireNonNullElse(modelResult.getDetectResult(), Map.of())).getOrDefault("targets", List.of());
        for(Map<String, Object> target : targets)
            for(String roi_hit : (String[])target.get("roi_hits")) {
                int has = emptyRois.indexOf(roi_hit);
                if(has >= 0)
                    emptyRois.set(has, null);
            }
        
        if(emptyRois.stream().filter(Objects::nonNull).findAny().isEmpty())
            return false;
        
        modelResult.getModelRequest().getParameter().put("emptyRois", emptyRois);
        return true;
    }
    
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> outputResults = new ArrayList<Map<String, Object>>();
        String deviceId = (String)modelResult.getModelRequest().getParameter().get("deviceId");
        
        List<String> emptyRois = (List<String>)modelResult.getModelRequest().getParameter().remove("emptyRois");
        if(emptyRois != null)
            for(int index = 0; index < emptyRois.size(); index ++)
            	buildResult(outputResults, deviceId, new String[] {emptyRois.get(index)});
         else       
            buildResult(outputResults, deviceId, SCREEN);
        

        modelResult.setOutputResult(outputResults);
        postProcessOutputValue(modelResult);
    }
    
    private final void buildResult(List<Map<String, Object>> outputResults, String deviceId, String[] emptyRois) {
    	Map<String, Object> outputResult = new HashMap<String, Object>();
        
        List<Map<String, Object>> attributes = new ArrayList<Map<String, Object>>();
        attributes.add(Map.of("key", annotatorName(), "value", annotatorName(), "confidence", 1.0f));
        outputResult.put("attributes", attributes);
        
        outputResult.put("confidence", 1.0f);
        outputResult.put("rois", emptyRois);
        
        if(StringUtils.isNotBlank(deviceId))
            outputResult.put("deviceId", deviceId);
        
        outputResults.add(outputResult);
    }
}