package com.sensetime.intersense.cognitivesvc.xworker.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.ModelHandlerEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_imgproc.CvFont;

import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 模型的抽象类，把跑模型抽象出来，模型加载放在实现类中
 */
@Slf4j
@SuppressWarnings("unchecked")
public abstract class AbstractXModelHandler implements XModelHandler, Runnable{
	protected static final String[] SCREEN = new String[] {"SCREEN"};

	@Getter
    protected final Monitor monitor = new Monitor();

	protected final Semaphore semaphores[]  = new Semaphore[] {new Semaphore(1), new Semaphore(1)};

    //<deviceId_roiId, counter>
    protected final ConcurrentHashMap<String, LinkedBlockingQueue<BufferTarget>> bufferCounter = new ConcurrentHashMap<String, LinkedBlockingQueue<BufferTarget>>();

	@Setter
	@Getter
	protected volatile ModelHandlerEntity handlerEntity;

	@Setter
	@Getter
	protected volatile List<KestrelInterceptor> interceptors = List.of();

    protected volatile int status = 1;

    protected Thread[] xModelThreads;
    protected CountDownLatch latch;
    protected Semaphore extractSemaphore;
    protected LinkedBlockingQueue<BatchItem> extractQueue;
    protected ConcurrentHashMap<String, Queue<BatchItem>> extractQueueMap = new ConcurrentHashMap<String, Queue<BatchItem>>();
    //protected ConcurrentHashMap<String, Long> extractQueueMapHealth = new ConcurrentHashMap<String, Long>();

    protected String      plugins[];
    protected ModelHolder holders[];
    protected int batchSize = Initializer.batchSize;

	protected ConcurrentHashMap<Long, String> deviceRoiMap = new ConcurrentHashMap<Long, String>();

    @Override
    public ModelResult handle(ModelRequest modelRequest) {
        ModelResult result = ModelResult.builder().modelRequest(modelRequest).build();
        if (status != 0)
            return result;

        boolean noBatch = this instanceof ConfigAccessor ? Boolean.TRUE.equals(((ConfigAccessor)this).getNoBatch()) : false;

        boolean asap = noBatch
        			|| batchSize == 1
	        		|| !Initializer.isGpu()
		        	|| MapUtils.getBooleanValue(modelRequest.getParameter(), "asap", false)
		        	|| handlerEntity.getCpuModelDup() > 0 ;

		BatchItem batchItem = BatchItem.builder().modelRequest(modelRequest).build();
		boolean logged = Utils.instance.watchFrameTiktokLevel == -789 || Utils.instance.printLog;
		if(logged){
			log.info("[VideoHandleLog] AbstractXModelHandler handle asap={},noBatch={}，batchSize={},modelRequest.getParameter()={},handlerEntity.getCpuModelDup()={}",
					asap,noBatch,batchSize,modelRequest.getParameter(),handlerEntity.getCpuModelDup());
		}
        if(asap) {
        	Semaphore semaphore = extractSemaphore(modelRequest.getParameter());
        	boolean acquire = semaphore.tryAcquire();
        	if(acquire) {
        		try {
        			Initializer.bindDeviceOrNot();
                    batch_handle_extract_result(List.of(batchItem), batch_extract_xmodel_asap(List.of(batchItem)));
                    result.setKeson(batchItem.getKeson());
        		}catch (Exception e) {
                    e.printStackTrace();
					log.error("[VideoHandleLog] AbstractXModelHandler asap deviceId {} handle exception {}",modelRequest.getParameter().get("deviceId"), e.getMessage());
                }finally {
                	monitor.getAndIncrementHandled();
                	semaphore.release();
        		}
        	}else {
        		monitor.getAndIncrementUnHandled();
				log.error("[VideoHandleLog] AbstractXModelHandler asap deviceId {} UnHandled ",modelRequest.getParameter().get("deviceId"));
        	}
        }else{
            try {
				long start = new Date().getTime();
				long temp = 0l;
                if(extractQueue(modelRequest.getParameter()).offer(batchItem)) {

					if(logged){
						temp = new Date().getTime();
						//log.info("[VideoHandleLog] [Cost] deviceId {}, offer cost:{} ms",modelRequest.getParameter().get("deviceId"), temp - start);
					}

                    while(status == 0 && !batchItem.latch.await(1, TimeUnit.SECONDS));

					if(logged){
						log.info("[VideoHandleLog] [Cost] deviceId {}, latch wait cost:{} ms",modelRequest.getParameter().get("deviceId"), new Date().getTime() - temp);
					}

                    monitor.getAndIncrementHandled();
                }else {
                	monitor.getAndIncrementUnHandled();
					log.error("[VideoHandleLog] AbstractXModelHandler deviceId {} highRate extractQueue UnHandled",modelRequest.getParameter().get("deviceId"));
                }
            } catch (Exception e) {
            	e.printStackTrace();
				log.error("[VideoHandleLog] AbstractXModelHandler deviceId {} handle exception {}",modelRequest.getParameter().get("deviceId"), e.getMessage());
            } finally {
                result.setKeson(batchItem.getKeson());
            }
        }
		return result;
    }

	@Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || modelResult.getKeson().getValue() == null)
            return ;

        Object detectResult = KesonUtils.kesonToJson(modelResult.getKeson());

        Map<String, Object> additional = (Map<String, Object>)Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
        for(KestrelInterceptor interceptor : interceptors)
        	interceptor.postReadDetectResult(additional, (PointerByReference)modelResult.getKeson(), detectResult);

		modelResult.setDetectResult(detectResult);
    }

	@Override
    public boolean validateOutputValue(ModelResult modelResult) {
		if(modelResult.getDetectResult() == null || !(modelResult.getDetectResult() instanceof Map))
			return false;

        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)Objects.requireNonNullElse(modelResult.getDetectResult(), Map.of())).get("targets");
        if(CollectionUtils.isEmpty(targets))
        	return false;

        Processor processor = modelResult.getModelRequest().getProcessor();

        float threshold = Objects.requireNonNullElse(processor.getThreshold(), 0f);
        int minSize = Objects.requireNonNullElse(processor.getMinSize(), Integer.MIN_VALUE);
        int maxSize = Objects.requireNonNullElse(processor.getMaxSize(), Integer.MAX_VALUE);
        Polygon[] polygons = processor.fetchPolygons();

		List<String> roiIdStrings = Objects.requireNonNullElse(processor.getExtras(), List.of())
				.stream()
				.filter(extra -> Processor.ROIIDS.equals(((Map<String, Object>)extra).get("type")))
				.map(extra -> (List<String>)((Map<String, Object>)extra).getOrDefault(Processor.ROIIDS, List.of()))
				.findAny()
				.orElse(List.of());

		Map<String, Object> additional = (Map<String, Object>)Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());

    	Iterator<Map<String,Object>> its = targets.iterator();
        while (its.hasNext()) {
        	Map<String,Object> target = its.next();

        	Map<String, Object> roi = (Map<String, Object>)target.get("roi");
        	if(roi == null) {
        		its.remove();
        		continue;
        	}

        	Number conf = (Number)target.get("confidence");
    		if(conf != null && conf.floatValue() < threshold) {
    			its.remove();
    			continue;
    		}

    		int top    = ((Number)roi.get("top")).intValue();
            int left   = ((Number)roi.get("left")).intValue();
            int width  = ((Number)roi.get("width")).intValue();
            int height = ((Number)roi.get("height")).intValue();
        	int x      = left + width / 2;
        	int y      = top + height / 2;

            boolean minSizeCheck = width >= minSize && height >= minSize;
            boolean maxSizeCheck = width <= maxSize && height <= maxSize;
            if(!maxSizeCheck || !minSizeCheck) {
            	its.remove();
    			continue;
            }

            boolean validated = true;
            for(KestrelInterceptor interceptor : interceptors)
            	validated &= interceptor.validateOutput(additional, target);
            if(!validated) {
            	its.remove();
    			continue;
            }

    		if (ArrayUtils.isEmpty(polygons)) {
    			target.put("roi_hits", SCREEN);
    		}else {
        		int[] roiIndexes = IntStream.range(0, polygons.length).filter(index -> polygons[index].contains(x, y)).toArray();
        		if(ArrayUtils.isEmpty(roiIndexes)) {
        			its.remove();
        			continue;
        		}

        		target.put("roi_hits", Arrays.stream(roiIndexes).mapToObj(id -> id < roiIdStrings.size() ? roiIdStrings.get(id) : String.valueOf(id)).toArray(String[]::new));
    		}
        }

        return !targets.isEmpty();
    }

    @Override
    public void buildOutputValue(ModelResult modelResult) {
    	if(modelResult.getDetectResult() == null || !(modelResult.getDetectResult() instanceof Map))
    		return ;

    	List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
		String deviceId = (String)modelResult.getModelRequest().getParameter().get("deviceId");

		Map<String, Object> additional = (Map<String, Object>)Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());

    	List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
    	for(Map<String, Object> target : targets) {
    		Map<String, Object> valueResult = new HashMap<String, Object>();
    		valueResult.put("detect", target.get("roi"));
    		valueResult.put("deviceId", deviceId);

			if(target.containsKey("textline")) {
				int text_image_id = (int)target.get("image_id");
				List<Map<String, Object>> textLines = (List<Map<String, Object>>) target.get("textline");
				Map<String, Object>[] contents = new Map[textLines.size()];

				Float confidence = textLines.stream().map(line -> ((Number) line.get("score")).floatValue()).reduce(Float::sum).get() / textLines.size();
				String contentText = textLines.stream().map(line -> (String) ((Map<String, Object>) line.getOrDefault("content", Map.of())).get("utf8")).filter(StringUtils::isNotBlank).collect(Collectors.joining());
				contents[text_image_id] = Map.of("score", confidence, "content", Map.of("utf8", contentText));

				valueResult.put("textline", contents);
			}

    		if(target.containsKey("confidence"))
    			valueResult.put("confidence", target.get("confidence"));

    		if(target.containsKey("label"))
    			valueResult.put("targetType", target.get("label"));
    		else
    			valueResult.put("targetType", annotatorName());

    		Object roiHits = target.get("roi_hits");
            if(roiHits != null && roiHits != SCREEN)
                valueResult.put("rois", roiHits);

    		List<Map<String, Object>> attributeList = new ArrayList<Map<String, Object>>();
    		valueResult.put("attributes", attributeList);

    		Map<String, Object> attributes = target.entrySet().stream()
	    			.filter(entry -> entry.getKey().startsWith("attribute"))
	    			.map(entry -> ((Map<String, Object>)entry.getValue()).entrySet())
	    			.flatMap(Set::stream)
	    			.collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue(), (l, r) -> l));

    		if(MapUtils.isEmpty(attributes)) {
    			Integer modelSource = modelResult.getModelRequest().getProcessor().getModelSource();
    			if(modelSource != null && modelSource==1){
    				String targetEnOption = "unK";
    				Integer modelType = modelResult.getModelRequest().getProcessor().getModelType();
    				if(modelType==1){
    					Integer targetLabel = (Integer)target.get("label");
    					if(target!=null){
    						List<Map<String, Object>> targetOptions = modelResult.getModelRequest().getProcessor().getTargetOptions();
							List<Object> targetEnoptions  = targetOptions.stream().filter(x->x.get("targetOptionIdentifier").equals(String.valueOf(targetLabel))).map(x->x.get("targetEnOption")).collect(Collectors.toList());
							if(targetEnoptions.size()>0){
								 targetEnOption = (String)targetEnoptions.get(0);
							}
						}
    					attributeList.add(Map.of("key", target.getOrDefault("key", annotatorName()), "value", targetEnOption, "confidence", ((Number)target.getOrDefault("confidence", -1f)).floatValue()));
					}
				}
    			else{
    				attributeList.add(Map.of("key", target.getOrDefault("key", annotatorName()), "value", target.getOrDefault("value", annotatorName()), "confidence", ((Number)target.getOrDefault("confidence", -1f)).floatValue()));

				}
    		}else {
    			for(Entry<String, Object> entry : attributes.entrySet()) {
    				Map<String, Number> attribute = (Map<String, Number>)entry.getValue();
                    Entry<String, Number> maxItem = attribute.entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
					Integer modelSource = modelResult.getModelRequest().getProcessor().getModelSource();
					String key = entry.getKey();
                    if(modelSource != null && modelSource==1){
						Integer modelType = modelResult.getModelRequest().getProcessor().getModelType();
						if(modelType==2){
							key = modelResult.getModelRequest().getProcessor().getProcessor();
						}
					}

                    if(attribute.size() <= 1 && Objects.equals(entry.getKey(), maxItem.getKey()) && maxItem.getValue().floatValue() > 1)
                    	attributeList.add(Map.of("key", key, "value", maxItem.getValue(), "confidence", 1.0f));
                    else
                    	attributeList.add(Map.of("key", key, "value", maxItem.getKey(), "confidence", maxItem.getValue()));
                }
    		}

    		boolean needRawAttribute = MapUtils.getBooleanValue(modelResult.getModelRequest().getParameter(), "rawAttribute", false);
    		if(needRawAttribute)
    			valueResult.put("rawAttribute", attributes);

    		for(KestrelInterceptor interceptor : interceptors)
            	interceptor.postProcessOutput(additional, target, valueResult);

    		outputResult.add(valueResult);
    	}

    	modelResult.setOutputResult(outputResult);
    	postProcessOutputValue(modelResult);
    }

    @Override
    public void releaseModelResult(ModelResult modelResult) {
    	if(modelResult == null)
    		return ;

        KesonUtils.kesonDeepDelete(modelResult.getKeson());
    	modelResult.setKeson(null);
    }

    @Override
    public String annotatorName() { return handlerEntity.getAnnotatorName(); }

    @Override
    public void run() {
        Initializer.bindDeviceOrNot();

        while (status == 0) {
            PointerByReference output_kesons[] = null;
            List<BatchItem> handlingList = List.of();

            long now = System.currentTimeMillis();
			long start = 0;
            boolean logged = Utils.instance.watchFrameTiktokLevel == -7891 || Utils.instance.printLog;
			if(logged)
				start = System.currentTimeMillis();

            try {
            	semaphores[0].acquireUninterruptibly();

				if(logged) {
					//log.info("[VideoHandleLog] [Cost] model semaphores[0].acquireUninterruptibly handled [" + annotatorName() + "] batch[" + handlingList.size() + "] cost[" + (System.currentTimeMillis() - start) + "]");
					start = System.currentTimeMillis();
				}
				try{
            		if(isQueueMap())
            			handlingList = Utils.drainFromMapQueue(extractQueueMap, getDrainPollCount(), getDrainTimeout());
                	else
                		handlingList = Utils.drainFromQueue(extractQueue, batchSize, getDrainPollCount(), getDrainTimeout());

                    if (handlingList.isEmpty()) {
                    	Thread.sleep(40);
                    	continue;
                    }

					if(logged)
						//log.info("[VideoHandleLog] [Cost] model drainFromMapQueue handled [" + annotatorName() + "] batch[" + handlingList.size() + "] cost[" + (System.currentTimeMillis() - start) + "]");

					if(logged)
                    	now = System.currentTimeMillis();

                    output_kesons = batch_extract_xmodel_asap(handlingList);
                }finally {
                	if(!handlingList.isEmpty())
                		semaphores[1].acquireUninterruptibly();

                	semaphores[0].release();
                }

                try {
                    batch_handle_extract_result(handlingList, output_kesons);
                }finally {
                	semaphores[1].release();
                }
                if(System.currentTimeMillis() - now > Utils.instance.printHandleTime){
					log.info("[VideoHandleLog] [Cost] models handled [" + annotatorName() + "] batch[" + handlingList.size() + "] cost[" +
							(System.currentTimeMillis() - now) + "]");
				}

            	if(logged)
            		log.info("[VideoHandleLog] [Cost] model handled [" + annotatorName() + "] batch[" + handlingList.size() + "] cost[" + (System.currentTimeMillis() - now) + "]");
            } catch (Exception e) {
                e.printStackTrace();
				log.error("[VideoHandleLog] AbstractXModelHandler batch handle exception {}",e.getMessage());
            }finally {
            	for (int index = 0; index < handlingList.size(); index++)
                    handlingList.get(index).latch.countDown();
            }
        }

        try {
        	Thread.sleep(2000);
        } catch (InterruptedException e) {
        	e.printStackTrace();
		}

        latch.countDown();

        synchronized(this) {
        	if(CollectionUtils.isNotEmpty(extractQueue))
                for(BatchItem item = extractQueue.poll(); item != null; item = extractQueue.poll())
                	FrameUtils.batch_free_frame(item.getModelRequest().getVideoFrames());

        	if(MapUtils.isNotEmpty(extractQueueMap)) {
        		for(Queue<BatchItem> extractQueue : extractQueueMap.values())
        			if(CollectionUtils.isNotEmpty(extractQueue))
        				for(BatchItem item = extractQueue.poll(); item != null; item = extractQueue.poll())
        					FrameUtils.batch_free_frame(item.getModelRequest().getVideoFrames());

        		extractQueueMap.clear();
				log.info("[VideoHandleLog] extractQueueMap clear, num:{}", extractQueueMap.size());
        	}
        }
    }

    /** 准备处理数据用的模型句柄 */
    protected ModelHolder[] prepareModelHolder(List<BatchItem> handlingList) { return holders; }

    /** 准备输入数据 */
    protected PointerByReference prepareInput(List<BatchItem> handlingList) {
    	Pointer[] frames = new Pointer[handlingList.size()];
    	Long[] sourceIds = new Long[handlingList.size()];

		for (int index = 0; index < handlingList.size(); index++) {
			if(handlerEntity.getCpuModelDup() > 0)
				frames[index] = handlingList.get(index).getModelRequest().getVideoFrames()[0].getCpuFrame();
			else
				frames[index] = handlingList.get(index).getModelRequest().getVideoFrames()[0].getGpuFrame();
		}

		if(StringUtils.isBlank(handlerEntity.getFlockConfig()))
			return new PointerByReference(KesonUtils.frameToKeson(frames, sourceIds));
		else {
			for (int index = 0; index < handlingList.size(); index++) {
				Object deviceId = handlingList.get(index).getModelRequest().getParameter().get("deviceId");
				if(deviceId == null)
					sourceIds[index] = (long)index;
				else
					sourceIds[index] = Utils.keyToContextId(deviceId);
			}

			return new PointerByReference(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), KesonUtils.frameToKeson(frames, sourceIds)));
		}
    }

    /** 按照 函数包含的模型 顺序的跑一边 */
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];

        ModelHolder[]        pointers      = prepareModelHolder(handlingList);
        PointerByReference   param_keson   = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);

        Map<String, Object> additional = (Map<String, Object>)Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
        for (int index = 0; index < pointers.length; index++) {
        	PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
        	PointerByReference outOf = output_kesons[index];

        	for(KestrelInterceptor interceptor : interceptors)
        		interceptor.beforeExecModel(additional, index, param_keson, output_kesons, inTo, outOf);

    		long now = System.currentTimeMillis();
    		/** 执行模型 获取数据*/
        	pointers[index].process(inTo.getValue(), outOf);
    		monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);

            for(KestrelInterceptor interceptor : interceptors)
        		interceptor.afterExecModel(additional, index, param_keson, output_kesons, inTo, outOf);
        }

        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }

	/** 将批量处理结果 整理后放到每个项目 */
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
    	if(StringUtils.isBlank(handlerEntity.getFlockConfig())){
        	PointerByReference output = KesonUtils.mergeRenameAllToFirstKeson(outputKesons);
            PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(output, handlingList.size());

            for (int index = 0; index < handlingList.size(); index++) {
                BatchItem item = handlingList.get(index);
                item.setKeson(sub_kesons[index]);
            }
    	}else {
        	Pointer frames[] = handlingList.stream()
        			.map(item -> handlerEntity.getCpuModelDup() > 0 ? item.getModelRequest().getVideoFrames()[0].getCpuFrame() : item.getModelRequest().getVideoFrames()[0].getGpuFrame())
        			.toArray(Pointer[]::new);

        	PointerByReference[] sub_kesons = KesonUtils.splitFlockKesonAndDestroy(KesonUtils.mergeRenameAllToFirstFlockKeson(KesonUtils.tryReformFlockKeson(outputKesons)), frames);
            for (int index = 0; index < handlingList.size(); index++) {
                BatchItem item = handlingList.get(index);
                item.setKeson(sub_kesons[index]);
            }
    	}
    }

    /** 后处理结果 多帧检测 */
    protected void postProcessOutputValue(ModelResult modelResult){
    	Processor processor = modelResult.getModelRequest().getProcessor();
		String deviceId = (String)modelResult.getModelRequest().getParameter().get("deviceId");
		if(StringUtils.isBlank(deviceId) || processor == null || CollectionUtils.isEmpty(processor.getExtras()))
			return;


		Map<String, Object> additional = (Map<String, Object>)Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
		if(additional.containsKey("saveImage") && ((Number)additional.get("saveImage")).intValue() <= 0 ){
			// 不存图
			modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
		}

		Map<String, Object> bufferMap = processor.fetchBufferMap();
		if(MapUtils.isEmpty(bufferMap))
			return ;

		List<Map<String, Object>> outputResult = (List<Map<String, Object>>)modelResult.getOutputResult();
		Map<String, List<Map<String, Object>>> roiOutputResult = new HashMap<String, List<Map<String, Object>>>();
		for(Map<String, Object> valueResult : outputResult) {
			Map<String, Integer> detect = (Map<String, Integer>)valueResult.get("detect");
			if(MapUtils.isEmpty(detect))
				continue;

			String[] rois = (String[])valueResult.get("rois");
			if(ArrayUtils.isEmpty(rois))
				continue;

			for(String roi : rois) {
				List<Map<String, Object>> roiMap = roiOutputResult.get(roi);
				if(roiMap == null) {
					roiMap = new ArrayList<Map<String, Object>>();
					roiOutputResult.put(roi, roiMap);
				}

				roiMap.add(valueResult);
			}
		}

		long now = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();
		int bufferSize = (Integer)bufferMap.getOrDefault("bufferSize", 5);
		int bufferExpire = (Integer)bufferMap.getOrDefault("bufferExpire", bufferSize * 2 * Objects.requireNonNullElse(processor.getInterval(), 1));
		Number scale = (Number)bufferMap.get("scale");

		for(Entry<String, List<Map<String, Object>>> entry : roiOutputResult.entrySet()) {
			LinkedBlockingQueue<BufferTarget> counter = bufferCounter.get(deviceId + "_" + entry.getKey());
			if(counter == null) {
				counter = new LinkedBlockingQueue<BufferTarget>();
				bufferCounter.put(deviceId + "_" + entry.getKey(), counter);
			}

			for(BufferTarget first = counter.peek(); !counter.isEmpty() && (first != null && now - first.getTime() > bufferExpire * 1000); first = counter.peek())
				counter.poll();

			if(Utils.instance.watchFrameTiktokLevel == -176) {
				long min = counter.stream().mapToLong(c -> c.getTime()).min().orElse(-1);
				long max = counter.stream().mapToLong(c -> c.getTime()).max().orElse(-1);

				log.warn("key[" + deviceId + "_" + entry.getKey() + "] now[" + now + "] maxTime[" + max + "] minTime[" + min + "] count[" + counter.size() + "] bufferSize[" + bufferSize + "] bufferExpire[" + bufferExpire + "] scale[" + scale + "]");
			}

			if(scale == null) {
				counter.add(new BufferTarget(now, (Map<String, Integer>)entry.getValue().get(0).get("detect"), 0.0f));

				if(counter.size() >= bufferSize)
    				counter.clear();
    			else
    				entry.getValue().clear();
			}else {
				Iterator<Map<String, Object>> its = entry.getValue().iterator();
				while(its.hasNext()) {
					Map<String, Object> target = its.next();
					Map<String, Integer> detect = (Map<String, Integer>)target.get("detect");

					BufferTarget current = new BufferTarget(now, detect, scale.floatValue());

					List<BufferTarget> bingos = counter.stream().filter(previous -> current.in(previous)).collect(Collectors.toList());
					long diffTimeCount = bingos.stream().mapToLong(b -> b.getTime()).distinct().count();
					if(diffTimeCount < bufferSize) {
						counter.add(current);
						its.remove();
					}else {
						counter.removeAll(bingos);
					}
				}
			}
		}

		outputResult = roiOutputResult.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList());

		if(outputResult.isEmpty())
			modelResult.setOutputResult(null);
		else
			modelResult.setOutputResult(outputResult);
    }

    protected synchronized void startHandle() {
        if (xModelThreads != null)
        	return ;

    	status = 0;

        xModelThreads = new Thread[getConcurrent()];
        latch = new CountDownLatch(xModelThreads.length);

        for (int index = 0; index < xModelThreads.length; index++) {
            Thread xModelThread = new Thread(Utils.cogGroup, this);
            xModelThread.setDaemon(true);
            xModelThread.setPriority(Thread.NORM_PRIORITY + 2);
            xModelThread.setName("XModel[" + annotatorName() + "]" + "-No." + index);
            xModelThread.start();

            xModelThreads[index] = xModelThread;
        }
    }

    protected synchronized void stopHandle() {
        if (xModelThreads == null)
        	return ;

        status = 1;

        try {
			boolean ret = latch.await(30, TimeUnit.SECONDS);
			if(!ret)
				new RuntimeException(annotatorName() + " await more than 30 second.").printStackTrace();

			Thread.sleep(500);
		} catch (InterruptedException e1) {}

        xModelThreads = null;
    }

    protected Semaphore extractSemaphore(Map<String, Object> parameter){
    	if(extractSemaphore != null)
    		return extractSemaphore;

		synchronized(this) {
			if(extractSemaphore == null)
				extractSemaphore = new Semaphore(Objects.requireNonNullElse(Math.max(16, batchSize * 4), 64));

	    	return extractSemaphore;
		}
    }

    protected Queue<BatchItem> extractQueue(Map<String, Object> parameter){
    	if(isQueueMap()) {
    		String deviceId = parameter.get("deviceId").toString();
    		//extractQueueMapHealth.put(deviceId, System.currentTimeMillis());
    		Queue<BatchItem> extractQueue = extractQueueMap.get(deviceId);
    		if(extractQueue != null)
        		return extractQueue;

    		synchronized(deviceId.intern()) {
    			extractQueue = extractQueueMap.get(deviceId);

    			if(extractQueue == null) {
    				extractQueue = new LinkedBlockingQueue<BatchItem>(Math.max(64, batchSize * 4));
    				extractQueueMap.put(deviceId, extractQueue);
    			}

    	    	return extractQueue;
    		}
    	}else {
        	if(extractQueue != null)
        		return extractQueue;

    		synchronized(this) {
    			if(extractQueue == null)
    				extractQueue = new LinkedBlockingQueue<BatchItem>(Math.max(64, batchSize * 4));

    	    	return extractQueue;
    		}
    	}
    }

//    protected void checkQueueMapEmpty() {
//    	if(!isQueueMap() || MapUtils.isEmpty(extractQueueMap))
//    		return ;
//
//    	Iterator<Entry<String, Queue<BatchItem>>> its = extractQueueMap.entrySet().iterator();
//    	while(its.hasNext()) {
//    		Entry<String, Queue<BatchItem>> entry = its.next();
//    		long timestamp = extractQueueMapHealth.getOrDefault(entry.getKey(), 0L);
//
//    		if(System.currentTimeMillis() - timestamp > 20 * 1000 * 60) {
//    			its.remove();
//    			extractQueueMapHealth.remove(entry.getKey());
//
//    			for(BatchItem item = entry.getValue().poll(); item != null; item = entry.getValue().poll())
//    				FrameUtils.batch_free_frame(item.getModelRequest().getVideoFrames());
//    		}
//    	}
//    }

    protected int getConcurrent() { return 2; }

    protected boolean isLazyInit() { return false; }

    protected boolean isQueueMap() { return false; }

    protected int getDrainPollCount() { return 4; }

    protected int getDrainTimeout() { return 5; }

    protected void onWorkerEvent(XworkerEvent e) {

		if(e instanceof XworkerStreamStartedEvent) {
			XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
			long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
			startContext(contextId, event.getInfra());
		}else if(e instanceof XworkerStreamClosedEvent) {
			XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
			long contextId = Utils.keyToContextId(event.getDeviceId());

			try { Thread.sleep(1000); } catch (InterruptedException x) { }
			stopContext(contextId);
		}
	}

	private void startContext(long contextId, VideoStreamInfra infra) {
		log.info("onWorkerEvent model [" + getClass().getSimpleName() + "] start context [" + contextId + "].");
	}

	private void stopContext(long contextId) {
		log.info("onWorkerEvent model [" + getClass().getSimpleName() + "] stop context [" + contextId + "].");
	}

    @Getter
    @Builder
    @Accessors(chain = true)
    protected static class BatchItem {
        private ModelRequest modelRequest;

        @Setter
        private PointerByReference keson;

        @Builder.Default
        private CountDownLatch latch = new CountDownLatch(1);
    }

    @Data
    @Accessors(chain = true)
    protected static class BufferTarget{
    	private long time;

    	private float centerX;
    	private float centerY;

    	private float left;
    	private float top;
    	private float right;
    	private float bottom;

    	public BufferTarget(long time, Map<String, Integer> detect, float scale) {
    		centerX = detect.get("left") + detect.get("width")  / 2;
    		centerY = detect.get("top")  + detect.get("height") / 2;

    		left   = centerX - (1 + scale) * detect.get("width")  / 2;
    		top    = centerY - (1 + scale) * detect.get("height") / 2;
    		right  = centerX + (1 + scale) * detect.get("width")  / 2;
    		bottom = centerY + (1 + scale) * detect.get("height") / 2;

    		this.time = time;
    	}

    	public boolean in(BufferTarget other) {
    		return centerX <= other.getRight() && centerX >= other.getLeft() && centerY <= other.getBottom() && centerY >= other.getTop();
    	}
    }

    public static class Monitor{
    	public ConcurrentLinkedDeque<AtomicLong>    expired;

    	public ConcurrentLinkedDeque<AtomicInteger> handled;
    	public ConcurrentLinkedDeque<AtomicInteger> unhandled;
    	public ConcurrentLinkedDeque<AtomicInteger> sended;
    	public AtomicInteger handledTotal;
    	public AtomicInteger unhandledTotal;
    	public AtomicInteger sendedTotal;

    	public Monitor(){
    		handled   = new ConcurrentLinkedDeque<AtomicInteger>();
        	unhandled = new ConcurrentLinkedDeque<AtomicInteger>();
        	expired   = new ConcurrentLinkedDeque<AtomicLong>();
        	sended   = new ConcurrentLinkedDeque<AtomicInteger>();

        	for(int index = 0; index < 12; index ++) {
        		handled.offer(new AtomicInteger());
        		unhandled.offer(new AtomicInteger());
        		sended.offer(new AtomicInteger());
        		expired.offer(new AtomicLong());
        	}

        	handledTotal   = new AtomicInteger();
        	unhandledTotal = new AtomicInteger();
        	sendedTotal    = new AtomicInteger();
    	}

    	public int getAndIncrementHandled() {
    		handledTotal.getAndIncrement();
    		return handled.peekFirst().getAndIncrement();
    	}

    	public int getAndIncrementUnHandled() {
    		unhandledTotal.getAndIncrement();
    		return unhandled.peekFirst().getAndIncrement();
    	}

    	public int getAndIncrementSended() {
    		sendedTotal.getAndIncrement();
    		return sended.peekFirst().getAndIncrement();
    	}

    	public int getHandledCount() {
    		return handled.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
    	}

    	public int getUnhandledCount() {
    		return unhandled.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
    	}

    	public int getSendedCount() {
    		return sended.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
    	}

    	public long getExpired() {
    		return expired.stream().map(AtomicLong::get).reduce(Long::sum).orElse(0L);
    	}

    	public void reset() {
    		handled.pollLast();
    		handled.offerFirst(new AtomicInteger());

    		unhandled.pollLast();
    		unhandled.offerFirst(new AtomicInteger());

    		sended.pollLast();
    		sended.offerFirst(new AtomicInteger());

    		expired.pollLast();
    		expired.offerFirst(new AtomicLong());
    	}
    }

    /** 能力一些配置 */
    public static interface ConfigAccessor {

    	public default Boolean getNeedContext() { return null; }

    	public default Boolean getBlocking() { return null;}

    	public default Boolean getNoBatch() { return null; }

    	public default String  getDecoderFormat() { return null; }

    	public default String  getFrameBufferStrategy() { return null; }

    	public default Integer getFrameBuffer() { return null; }

    	public default Integer getInterval() { return null; }

    	public default Integer getMinBatchSize() { return null; }

    	public default Integer getMinBatchStagger() { return null; }
    }

    /** 是否渲染*/
    public static interface VideoRecorderAccessor {

    	public default List<Drawing> draw(ModelResult modelResult){
			List<Drawing> result = new ArrayList<Drawing>();

			List<Map<String, Object>> outputResults = (List<Map<String, Object>>)Objects.requireNonNullElse(modelResult.getOutputResult(), List.of());
			String deviceID = modelResult.getModelRequest().getParameter().get("deviceId").toString();
			for(Map<String, Object> output : outputResults) {
				Integer modelSource = modelResult.getModelRequest().getProcessor().getModelSource();
				String v="";
				if(modelSource != null && modelSource==1){
					String attributesJson = JSON.toJSON(output.get("attributes")).toString();
					 v = (String)JSON.parseArray(attributesJson,HashMap.class).get(0).get("value");
					 if(v.equals("正常")){
					 	v="normal";
					 }else if(v.equals("有異物")){
					 	v="hasObject";
					 }
				}
				Map<String, Number> detect = (Map<String, Number>)output.get("detect");
				result.add(Rect.builder().processor(modelResult.getModelRequest().getProcessor().getProcessor()).deviceID(deviceID).text(v).top(detect.get("top").intValue()).left(detect.get("left").intValue()).width(detect.get("width").intValue()).height(detect.get("height").intValue()).build());
			}
			return result;
		}

    	public static interface Drawing{ }

    	@Data
    	@NoArgsConstructor
    	@AllArgsConstructor
    	@Accessors(chain = true)
    	@Builder
    	public static class Rect implements Drawing{
    		private String processor;
    		private int top;
    		private int left;
    		private int width;
    		private int height;
    		private String text;
			private CvScalar color;
			private int thickness;
            private CvFont textFont;

			private String deviceID;
    	}

    	@Data
    	@NoArgsConstructor
    	@AllArgsConstructor
    	@Accessors(chain = true)
    	@Builder
    	public static class Line implements Drawing{
    		private int[] from;
    		private int[] to;
			private CvScalar color;
			private int thickness;
            private int type = 8;
            private int shift = 0;
    	}

    	@Data
    	@NoArgsConstructor
    	@AllArgsConstructor
    	@Accessors(chain = true)
    	@Builder
    	public static class DensityMap implements Drawing{
    		private int width;
    		private int height;

    		/** density[height][width]*/
    		private double[][] density;

    		@Builder.Default
    		private double scale = 1.0;

    		@Builder.Default
    		private int colorMap = 2;//默认COLORMAP_JET, org.opencv.imgproc.Imgproc; 范围0-20
    	}
    }

}
