package com.sensetime.intersense.cognitivesvc.xworker.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class XworkerStreamStatusChangeEvent extends ApplicationEvent {
	private static final long serialVersionUID = 6519770944018305081L;

	private String deviceId;
	private String checkTs;
	private String sts;
	private String processSts;

	public XworkerStreamStatusChangeEvent(String deviceId, String checkTs, String sts, String processSts) {
		super(Thread.currentThread());

        this.deviceId = deviceId;
		this.checkTs = checkTs;
		this.sts = sts;
		this.processSts = sts;

	}
}
