package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.*;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

public class CraneSemanticProcess extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
	
    @Getter
    private final Boolean blocking = false;
    
    @Getter
    protected final Boolean lazyInit = true;

    @Getter
    protected final String decoderFormat = "rgb24";
    
    @Getter
    protected final Integer frameBuffer = 4;//显存小的用20, 显存大了稍微提高一些
    
    @Getter
    protected final String frameBufferStrategy = "smart";
    
    @Getter
    protected final Integer minBatchSize = 2;//不低于2

    @Getter
    protected final Integer minBatchStagger = 1;//数字越小 精度越高 资源消耗也越大

    @Getter
    protected final Integer interval = 25 * 2;

    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if(status != 0)
            return new PointerByReference[0];

        Pointer[] befores = handlingList.stream().map(item -> item.getModelRequest().getVideoFrames()[0].getGpuFrame()).toArray(Pointer[]::new);
        Pointer[] afters  = handlingList.stream().map(item -> item.getModelRequest().getVideoFrames()[1].getGpuFrame()).toArray(Pointer[]::new);
        
        Pointer input_keson = KesonUtils.createCraneInputKeson(befores, afters);      
        PointerByReference output_keson = new PointerByReference();
        
        long now = System.currentTimeMillis();
        holders[0].process(input_keson, output_keson);
        monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        
        KesonUtils.kesonDeepDelete(input_keson);
        return new PointerByReference[] {output_keson};    
    }
    
    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(outputKesons[0], handlingList.size(), "id");
        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);
        }
    }
    
    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || !(modelResult.getKeson() instanceof PointerByReference))
            return ;
        
        Pointer semantic_map = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(KestrelApi.keson_get_object_item(modelResult.getKeson().getValue(), "targets"), 0), "semantic_map");
        
        PointerByReference bufferExt = new PointerByReference();
        KestrelApi.keson_get_ext_data(semantic_map, bufferExt);
        
        int width = KestrelApi.kestrel_frame_video_width(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame());
        int height = KestrelApi.kestrel_frame_video_height(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame());
        
        modelResult.setDetectResult(bufferExt.getValue().getPointer(0).getByteArray(0, width * height));
    }
    
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        byte[] data = (byte[])modelResult.getDetectResult();
        int width = KestrelApi.kestrel_frame_video_width(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame());
        
        int abnormalCount = 0;
        int left = Integer.MAX_VALUE, top = Integer.MAX_VALUE, right = Integer.MIN_VALUE, bottom = Integer.MIN_VALUE;
        
        for(int index = 0; index < data.length ; index ++) {
            if(data[index] == 0)
                continue;
            
            abnormalCount ++;
            
            int x = index % width;
            int y = index / width;
            
            left = Math.min(left, x);
            top = Math.min(top, y);
            right = Math.max(right, x);
            bottom = Math.max(bottom, y);
        }

        if(abnormalCount >= 100) {//100项目以上才发消息
            modelResult.setOutputResult(Map.of("left", left, "top", top, "width", right - left, "height", bottom - top));
            return true;
        }else {
            return false;
        }
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        Map<String, Object> roi = (Map<String, Object>)modelResult.getOutputResult();
        
        Map<String, Object> valueResult = new HashMap<String, Object>();
        valueResult.put("targetType", annotatorName());
        valueResult.put("detect", roi);
        valueResult.put("confidence", 1.0f);
        valueResult.put("deviceId", modelResult.getModelRequest().getParameter().get("deviceId"));
        
        modelResult.setOutputResult(List.of(valueResult));
        
        ArrayUtils.swap(modelResult.getModelRequest().getVideoFrames(), 0, 1);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult){
        List<Drawing> result = new ArrayList<Drawing>();
        
        Processor processor = modelResult.getModelRequest().getProcessor();
        for(Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for(int index = 0; index < roi.length - 1; index ++) {
                result.add(Line.builder().from(new int[] {roi[index][0], roi[index][1]}).to(new int[] {roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().from(new int[] {roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[] {roi[0][0], roi[0][1]}).build());
        }

        List<Map<String, Object>> outputResult = (List<Map<String, Object>>)modelResult.getOutputResult();
        if(CollectionUtils.isNotEmpty(outputResult)) {
            Map<String, Number> roi = (Map<String, Number>)outputResult.get(0).get("detect");
            result.add( Rect.builder().processor(annotatorName()).top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).build());
        }
        
        return result;
    }
}
