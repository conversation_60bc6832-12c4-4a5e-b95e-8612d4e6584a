package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.*;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.ArrayUtils;

public class Segmentor extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    
    @Getter
    protected final boolean lazyInit = true;

    @Getter
    protected final Integer interval = 24;
    
    private static final int[] labelType = new int[] {1, 2, 3};// 目标的label
    
    private static final Float coverRate = 0.03f;// 0-1之间的小数 位图占比最低值
    
    private static final Float edgeRate = 0.1f;// 0-1之间的小数 边缘点的抽样率 维持在10%
    
    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || !(modelResult.getKeson() instanceof PointerByReference))
            return ;
        
        Pointer semantic_map = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(KestrelApi.keson_get_object_item(modelResult.getKeson().getValue(), "targets"), 0), "semantic_map");
        
        PointerByReference bufferExt = new PointerByReference();
        KestrelApi.keson_get_ext_data(semantic_map, bufferExt);
        
        int width = KestrelApi.kestrel_frame_video_width(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame());
        int height = KestrelApi.kestrel_frame_video_height(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame());
        
        byte[] data = bufferExt.getValue().getPointer(0).getByteArray(0, width * height);
        
        byte[][] result = new byte[height + 2][];
        result[0] = new byte[width + 2];
        result[height + 1] = new byte[width + 2];
        
        for(int index = 1; index < result.length - 1; index ++) {
            result[index] = new byte[width + 2];
            System.arraycopy(data, (index - 1) * width, result[index], 1, width);
        }
        
        modelResult.setDetectResult(result);
    }
    
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        Map<String, Object> rate = Map.of();
        List<Map<String, Object>> extras = modelResult.getModelRequest().getProcessor().getExtras();
        if(extras != null)
            rate = extras.stream().filter(extra -> "rate".equals(extra.get("type"))).findAny().orElse(Map.of());
        
        byte[][] data = (byte[][])modelResult.getDetectResult();
        
        int totalDot = 0;
        List<List<Dot>> edges = Arrays.stream(labelType).mapToObj(index -> new ArrayList<Dot>()).collect(Collectors.toList());
        for(int y = 0; y < data.length; y ++) {
            for(int x = 0; x < data[y].length; x ++) {
                int targetIndex = ArrayUtils.indexOf(labelType, data[y][x]);
                if(targetIndex == ArrayUtils.INDEX_NOT_FOUND)
                    continue;
                
                totalDot ++;
                
                int targetLabel = labelType[targetIndex];
                if(data[y - 1][x] != targetLabel || data[y + 1][x] != targetLabel || data[y][x - 1] != targetLabel || data[y][x + 1] != targetLabel)
                    if(ThreadLocalRandom.current().nextFloat() < ((Number)rate.getOrDefault("edgeRate", edgeRate)).floatValue())
                        edges.get(targetIndex).add(new Dot(x, y));
                
            }
        }
        
        boolean ret = edges.stream().filter(edge -> !edge.isEmpty()).findAny().isPresent();
        if(ret) {
            if(totalDot > data.length * data[0].length * ((Number)rate.getOrDefault("coverRate", coverRate)).floatValue()) {
                modelResult.setOutputResult(edges);
                return true;
            }else 
                return false;
        }else
            return false;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<List<Dot>> edges = (List<List<Dot>>)modelResult.getOutputResult();
        
        List<Map<String, Object>> targets = IntStream.iterate(0, i -> i + 1).limit(labelType.length)
                .mapToObj(index -> Map.of("label", labelType[index], "edges", edges.get(index)))
                .collect(Collectors.toList());
        
        Map<String, Object> valueResult = new HashMap<String, Object>();
        valueResult.put("targetType", annotatorName());
        valueResult.put("targets", targets);
        valueResult.put("confidence", 1.0f);
        valueResult.put("deviceId", modelResult.getModelRequest().getParameter().get("deviceId"));
        
        modelResult.setOutputResult(List.of(valueResult));
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult){
        List<Drawing> result = new ArrayList<Drawing>();
        if(modelResult.getOutputResult() == null)
            return result;
        
        Map<String, Object> valueResult = ((List<Map<String, Object>>)modelResult.getOutputResult()).get(0);
        List<Map<String, Object>> targets = (List<Map<String, Object>>)valueResult.get("targets");
        
        List<Dot> dots = targets.stream().map(target -> (List<Dot>)target.get("edges")).flatMap(List::stream).collect(Collectors.toList());         
        for(Dot dot : dots) {
            Rect rect = Rect.builder().left(dot.x - 1).top(dot.y - 1).width(2).height(2).build();
            result.add(rect);
        }
        
        return result;
    }
    
    public static class Dot{
        public int x;
        public int y;
        
        public Dot(int x, int y) {
            super();
            this.x = x;
            this.y = y;
        }
        
        @Override
        public String toString() {
            return "[" + x + " ," + y + "]";
        }
    }
}
