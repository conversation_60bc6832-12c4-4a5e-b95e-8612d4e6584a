package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.awt.Polygon;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import org.apache.commons.lang3.ArrayUtils;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_crowd_overseaLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.crowd_analyze_result;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_point2df_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_imgproc.CvFont;

@Slf4j
public class CrowdCount extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    private static final ThreadLocal<Memory> frameBatchLocal = ThreadLocal.withInitial(() -> new Memory(Native.POINTER_SIZE * 64));
    
    private static final ThreadLocal<Memory> contextIdsBatchLocal = ThreadLocal.withInitial(() -> new Memory(Native.LONG_SIZE * 64));
    
    private volatile Map<String, Long> videoSizeContextMap = new HashMap<String, Long>();
    
    private Pointer[] crowd_trackers = new Pointer[1];
    
    private String[] detainedKeps;    
    
    @Getter
    protected final Integer interval = 24;//检一跳三
    
    @Getter
    protected final Integer frameBuffer = 20;
    
    @Getter
    protected final String frameBufferStrategy = "smart";
    
    /** 渲染流密度图 true表示用head算出密度图, false表示用模型给的密度图
     */
    private boolean useHeadPointAsMap = true;
    
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if(status != 0 || crowd_trackers[0] == null)
            return new PointerByReference[0];
        
        int trackerIndex = ThreadLocalRandom.current().nextInt(crowd_trackers.length);
        Memory frameBatch = frameBatchLocal.get();
        Memory contextIdsBatch = contextIdsBatchLocal.get();
        
        for(int index = 0; index < handlingList.size(); index ++) {
            Pointer frame = handlingList.get(index).getModelRequest().getVideoFrames()[0].getGpuFrame();
            
            frameBatch.setPointer(index * Native.POINTER_SIZE , frame);
            contextIdsBatch.setLong(index  * Native.LONG_SIZE , frameToContextId(frame, trackerIndex));
        }
        
        PointerByReference result_target = new PointerByReference(); 
        IntByReference result_num = new IntByReference();
        
        synchronized(crowd_trackers[trackerIndex]) {
            long now = System.currentTimeMillis();
            Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_update_batch(crowd_trackers[trackerIndex], frameBatch, contextIdsBatch, handlingList.size(), result_target, result_num);
            monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        }
        
        frameBatch.clear();
        contextIdsBatch.clear();

        PointerByReference[] result = new PointerByReference[2];
        result[0] = result_target;
        result[1] = new PointerByReference(KestrelApi.keson_create_int(result_num.getValue()));
        
        return result;
    }
    
    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        if(ArrayUtils.isEmpty(outputKesons))
            return;
        
        PointerByReference result_target = outputKesons[0];
        IntByReference result_num = new IntByReference((int)KestrelApi.keson_get_int(outputKesons[1].getValue())); 
        
        for(int index = 0; index < result_num.getValue(); index ++) {
            crowd_analyze_result crowd_analyze = new crowd_analyze_result(new Pointer(Pointer.nativeValue(result_target.getValue()) + KestrelApi._SIZE_crowd_analyze_result * index));
            crowd_analyze.read();
            
            Pointer headsPointer = crowd_analyze.global_density_result.head_loc_array;
            int headCount = crowd_analyze.global_density_result.head_num;
            
            Pointer keson = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_object(keson, "contextId", KestrelApi.keson_create_int(crowd_analyze.context_id));
            
            Pointer densityKeson = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(keson, "density", densityKeson);
            
            float[] densitys = crowd_analyze.global_density_result.density_map.getFloatArray(0, 160 * 90);
            for(float density : densitys) KestrelApi.keson_add_item_to_array(densityKeson, KestrelApi.keson_create_double(density));
            
            Pointer targetsKeson = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(keson, "targets", targetsKeson);
            
            for(int kndex = 0; kndex < headCount; kndex ++) {
                kestrel_point2df_t points = new kestrel_point2df_t(new Pointer(Pointer.nativeValue(headsPointer) + KestrelApi._SIZE_kestrel_point2df_t * kndex));
                points.read();
                
                Pointer targetKeson = KestrelApi.keson_create_object();
                KestrelApi.keson_add_item_to_array(targetsKeson, targetKeson);
                
                KestrelApi.keson_add_item_to_object(targetKeson, "x",  KestrelApi.keson_create_int((long)points.x));
                KestrelApi.keson_add_item_to_object(targetKeson, "y", KestrelApi.keson_create_int((long)points.y));
            }
            
            handlingList.get(index).setKeson(new PointerByReference(keson));
        }
        
        Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_release_update_result(result_target.getValue(), result_num.getValue());
        
        KesonUtils.kesonDeepDelete(outputKesons[1]);
        for(int index = 0; index < outputKesons.length; index ++)
            outputKesons[index] = null;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();
        if(ArrayUtils.isEmpty(polygons))
            polygons = new Polygon[] {new Polygon(new int[] {0, Integer.MAX_VALUE, Integer.MAX_VALUE, 0}, new int[] {0, 0, Integer.MAX_VALUE, Integer.MAX_VALUE}, 4)};
        
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>)processor.fetchExtras(function);
        
        List<Map<String, Object>> roiTargets[] = new List[polygons.length];
        for(int index = 0 ;index < polygons.length; index ++)
            roiTargets[index] = new ArrayList<Map<String, Object>>();
        
        List<Map<String, Object>> targets = (List<Map<String, Object>>)Objects.requireNonNullElse((Map<String, Object>)modelResult.getDetectResult(), Map.of()).getOrDefault("targets", List.of());
        for(Map<String, Object> target : targets) {
            int x = ((Number)target.get("x")).intValue();
            int y = ((Number)target.get("y")).intValue();
            
            for(int index = 0 ;index < polygons.length; index ++)
                if(polygons[index].contains(x, y))
                    roiTargets[index].add(Map.copyOf(target));
        }
        
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for(int index = 0 ;index < polygons.length; index ++) {
            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());
            
            Number minAlert = (Number)extrasMap.get("minAlert");
            Number maxAlert = (Number)extrasMap.get("maxAlert");
            
            List<Map<String, Object>> roiTarget = roiTargets[index];
            Map<String, Object> round = new HashMap<String, Object>();
            
            if(maxAlert != null && minAlert != null) {
                if(roiTarget.size() >= minAlert.intValue() && roiTarget.size() <= maxAlert.intValue())
                    round.put("maxAlert", roiTarget.size());
            }else if(maxAlert != null){
                if(roiTarget.size() <= maxAlert.intValue())
                    round.put("maxAlert", roiTarget.size());
            }else if(minAlert != null){
                if(roiTarget.size() >= minAlert.intValue()) 
                    round.put("maxAlert", roiTarget.size());
            }else {
                round.put("maxAlert", roiTarget.size());
            }
            
            if(!round.isEmpty()) {
                round.put("targets", roiTarget);
                round.put("roiId", index);
                result.add(round);
            }
        }
        
        if(!result.isEmpty())
            modelResult.setOutputResult(result);
        
        return !result.isEmpty();
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<String> roiIds = (List<String>)((List<Map<String, Object>>)Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getExtras(), List.of()))
                .stream()
                .filter(item -> "roiIds".equals(item.get("type")))
                .findAny()
                .orElse(Map.of())
                .getOrDefault("roiIds", List.of());

        int width = KestrelApi.kestrel_frame_video_width(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame());
        int height = KestrelApi.kestrel_frame_video_height(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame());
        
        int resizeX = useHeadPointAsMap ? width / 32 : 160;
        int resizeY = useHeadPointAsMap ? height / 32 : 90;
        
        double[][] density = new double[resizeY][];
        for(int index = 0 ;index < density.length; index ++) 
            density[index] = new double[resizeX];
        
        if(useHeadPointAsMap) {
            List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
            for(Map<String, Object> target : targets) {
                float densityX = ((Number)target.get("x")).floatValue() * resizeX / width;
                float densityY = ((Number)target.get("y")).floatValue() * resizeY / height;
                
                try {
                    density[(int)densityY][(int)densityX] += 0.4f;
                    
                    density[(int)densityY - 1][(int)densityX] += 0.15f;
                    density[(int)densityY + 1][(int)densityX] += 0.15f;
                    density[(int)densityY][(int)densityX - 1] += 0.15f;
                    density[(int)densityY][(int)densityX + 1] += 0.15f;
                    
                    density[(int)densityY - 1][(int)densityX - 1] += 0.08f;
                    density[(int)densityY + 1][(int)densityX - 1] += 0.08f;
                    density[(int)densityY - 1][(int)densityX + 1] += 0.08f;
                    density[(int)densityY + 1][(int)densityX + 1] += 0.08f;
                }catch(Exception e) {}
            }
        }else {
            List<Number> densityList = (List<Number>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("density", List.of());
            
            for(int index = 0 ;index < density.length; index ++) 
                density[index] = densityList.subList(160 * index, 160 * index + 160).stream().mapToDouble(n -> Math.min(1.0f, n.doubleValue() * 2) ).toArray();
        }
        
        ((Map<String, Object>)modelResult.getDetectResult()).put("density", density);
        
        for(Map<String, Object> target : (List<Map<String, Object>>)modelResult.getOutputResult()) {
            int roiIndex = (int)target.get("roiId");
            if(roiIndex < roiIds.size())
                target.put("roiId", roiIds.get(roiIndex));
                
            target.put("deviceId", modelResult.getModelRequest().getParameter().get("deviceId"));
        }
    }
    
    @PostConstruct
    @Override
    public synchronized void initialize() {
        log.info("CrowdCount initializing.");
        
        if(crowd_trackers[0] != null)
            return ;

        super.initialize();
        
        String[] keps = ArrayUtils.subarray(handlerEntity.getAttacheds(), 0, 2);
        String[] models = ArrayUtils.subarray(handlerEntity.getAttacheds(), 2, 4);
        
        detainedKeps = new String[1];
        detainedKeps[0] = KestrelApi.kestrel_plugin_load(keps[1], "");
        
        for(int index = 0; index < crowd_trackers.length; index ++)
            crowd_trackers[index] = Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_create(trackerConfig(keps, models));
        
        log.info("CrowdCount initialized.");
    }
    
    @PreDestroy
    @Override
    public synchronized void destroy() {
        log.info("CrowdCount destroying.");
        
        if(crowd_trackers[0] == null)
            return ;
        
        Map<String, Long> videoSizeContextMap = this.videoSizeContextMap;
        this.videoSizeContextMap = new HashMap<String, Long>();
        
        for(int index = 0; index < crowd_trackers.length; index ++)
            synchronized(crowd_trackers[index]) {
                for(Entry<String, Long> entry : videoSizeContextMap.entrySet())
                    if(entry.getKey().startsWith(index + "_"))
                        Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_free_context(crowd_trackers[index], entry.getValue());
                
                Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_destroy(crowd_trackers[index]);
                crowd_trackers[index] = null;
            }
        
        videoSizeContextMap.clear();
        
        for(String kep : detainedKeps)
            KestrelApi.kestrel_plugin_unload(kep);
        
        super.destroy();
        
        log.info("CrowdCount destroyed.");
    }
    
    @SuppressWarnings({ "unchecked", "rawtypes" })
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();
        
        if(e != null)
            for(Map<String, Object> param : (List<Map<String, Object>>)e) {
                if(!"mapping".equals(param.get("type"))) 
                    continue;
        
                Integer roiIndex = (Integer)param.get("roiIndex");
                if(roiIndex != null)
                    result.put(roiIndex, param);
            }
    
        return result;
    };
    
    private Long frameToContextId(Pointer frame, int trackerIndex) {
        int width = KestrelApi.kestrel_frame_video_width(frame);
        int height = KestrelApi.kestrel_frame_video_height(frame);
        
        Map<String, Long> videoSizeContextMap = this.videoSizeContextMap;
        
        String key = trackerIndex + "_" + width + "_" + height;
        Long contextId = videoSizeContextMap.get(key);
        if(contextId == null) {
            contextId = Utils.keyToContextId(key);
            String config = contextConfig(width, height);
            
            synchronized(this) {
                if(!videoSizeContextMap.containsKey(key)) {
                    synchronized(crowd_trackers[trackerIndex]) {
                        log.info("CrowdCount create context [" + config + "]");
                        Kestrel_crowd_overseaLibrary.INSTANCE.crowd_analyzer_create_context(crowd_trackers[trackerIndex], contextId, config);
                    }
                    
                    Map<String, Long> newSizeContextMap = new HashMap<String, Long>(videoSizeContextMap);
                    newSizeContextMap.put(key, contextId);
                    this.videoSizeContextMap = newSizeContextMap;
                }
            }
        }
        
        return contextId;
    }
    
    private String trackerConfig(String[] keps, String[] models) {
        return "{" + 
                "    \"models\": {" + 
                "        \"localization\": {" + 
                "            \"model\": \"" + models[0] + "\"," + 
                "            \"max_batch_size\": 1," + 
                "            \"plugin\": \"colo\"," + 
                "            \"type\": \"Localization\"" + 
                "        }," + 
                "        \"count\": {" + 
                "            \"model\": \"" + models[1] + "\"," + 
                "            \"max_batch_size\": 8," + 
                "            \"process_rate\": 1," + 
                "            \"plugin\": \"colo\"," + 
                "            \"type\": \"Count\"" + 
                "        }" + 
                "    }" + 
                "}";
    }
    
    private String contextConfig(int width, int height) {
        Map<String, Object> contextConfig = new HashMap<String, Object>();
        contextConfig.put("video_width", width);
        contextConfig.put("video_height", height);
        contextConfig.put("head_thres", 0.25);
        contextConfig.put("head_suppress_radius", 1);
        contextConfig.put("count_process_rate", 1);
        contextConfig.put("crowd_sparse_threshold", 30);
        contextConfig.put("labeled_person", "(0,0),(1,1),(2,2)");
        
        Map<String, Object> modelMap = new HashMap<String, Object>();
        contextConfig.put("model_map", modelMap);
        modelMap.put("default_count",     "count");
        modelMap.put("default_detection", "localization");
        
        return JSON.toJSONString(contextConfig);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {
        int width = KestrelApi.kestrel_frame_video_width(modelResult.getModelRequest().getVideoFrames()[0].getGpuFrame());
        
        List<Drawing> result = new ArrayList<Drawing>();
        
        Processor processor = modelResult.getModelRequest().getProcessor();
        for(Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for(int index = 0; index < roi.length - 1; index ++)
                result.add(Line.builder().from(new int[] {roi[index][0], roi[index][1]}).to(new int[] {roi[index + 1][0], roi[index + 1][1]}).build());
            
            result.add(Line.builder().from(new int[] {roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[] {roi[0][0], roi[0][1]}).build());
        }
        
        int count = 0;
        
        List<Map<String, Object>> outputResults = (List<Map<String, Object>>)modelResult.getOutputResult();
        for(Map<String, Object> outputResult : outputResults) 
            count += ((List<Map<String, Number>>)outputResult.get("targets")).size();
        
        CvScalar color = opencv_core.CV_RGB(0, 0, 255); // red
        CvFont cvFont = opencv_imgproc.cvFont(3, 4);
        result.add(Rect.builder().text("[" + count + "]").textFont(cvFont).color(color).left(width / 2).top(60).width(0).height(0).build());
        
        double[][] density = (double[][])((Map<String, Object>)modelResult.getDetectResult()).get("density");
        if(count > 0)
            result.add(DensityMap.builder().width(density[0].length).height(density.length).scale(2.5).colorMap(2).density(density).build());
        
        return result;
    }
}