package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.IntStream;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Memory;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

public class PairClassifier extends DynamicXModelHandler{
    
    private static final List<Integer> pairLefLabel = List.of();
    
    private static final List<Integer> pairRigLabel = List.of();
    
    private static final int primary = 0; //0 左右都不重要，1 左侧重要， 2 右侧重要
    
    private static final int classifireLabel = 0; //分类模型支持的label
    
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[0];    
        
        Pointer[] gpuFrames = new Pointer[handlingList.size()];
        for(int index = 0; index < handlingList.size(); index ++) 
            gpuFrames[index] = handlingList.get(index).getModelRequest().getVideoFrames()[0].getGpuFrame();
         
        PointerByReference param_keson = new PointerByReference(KesonUtils.frameToKeson(gpuFrames));
        PointerByReference[] output_kesons = new PointerByReference[] {new PointerByReference(), new PointerByReference()};
        
        long now = System.currentTimeMillis();
        holders[0].process(param_keson.getValue(), output_kesons[0]);
        
        holders[1].process(param_keson.getValue(), output_kesons[1]);
        monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        
        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }

    @SuppressWarnings({ "unchecked", "rawtypes", "unused"})
    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] output_kesons) {
        List<Map<String, Object>> leftTargets  = (List<Map<String, Object>>)((Map<String, Object>)KesonUtils.kesonToJson(output_kesons[0].getValue())).get("targets");
        List<Map<String, Object>> rightTargets = (List<Map<String, Object>>)((Map<String, Object>)KesonUtils.kesonToJson(output_kesons[1].getValue())).get("targets");
        KesonUtils.kesonDeepDelete(output_kesons);
        
        List<Map<String, Object>>[] lefts  = IntStream.iterate(0, i -> i + 1).limit(handlingList.size()).mapToObj(index -> Lists.newArrayList()).toArray(List[]::new);
        for(Map<String, Object> left : leftTargets) {
            Integer label   = ((Number)left.get("label")).intValue();
            if(!CollectionUtils.isEmpty(pairLefLabel) && !pairLefLabel.contains(label))
                continue;
            
            float confidence = ((Number)left.get("confidence")).floatValue();
            Integer imageId = ((Number)left.get("image_id")).intValue();
            float threshold = Objects.requireNonNullElse(handlingList.get(imageId).getModelRequest().getProcessor().getThreshold(), 0.5f);
            if(confidence < threshold)
                continue;
            
            lefts[imageId].add(left);
        }
        
        List<Map<String, Object>>[] rights = IntStream.iterate(0, i -> i + 1).limit(handlingList.size()).mapToObj(index -> Lists.newArrayList()).toArray(List[]::new);
        for(Map<String, Object> right : rightTargets) {
            Integer label   = ((Number)right.get("label")).intValue();
            if(!CollectionUtils.isEmpty(pairRigLabel) && !pairRigLabel.contains(label))
                continue;
            
            float confidence = ((Number)right.get("confidence")).floatValue();
            Integer imageId = ((Number)right.get("image_id")).intValue();
            float threshold = Objects.requireNonNullElse(handlingList.get(imageId).getModelRequest().getProcessor().getThreshold(), 0.5f);
            if(confidence < threshold)
                continue;
            
            rights[imageId].add(right);
        }
        
        List<Triple<Map<String, Object>, Map<String, Object>, Long>>[] distances = IntStream.iterate(0, i -> i + 1).limit(handlingList.size()).mapToObj(index -> Lists.newArrayList()).toArray(List[]::new);
        for(int index = 0; index < handlingList.size(); index ++) {
            List<Map<String, Object>> leftList  = lefts[index];
            List<Map<String, Object>> eightList = rights[index];
            List<Triple<Map<String, Object>, Map<String, Object>, Long>> distanceList = distances[index];
            
            for(Map<String, Object> left : leftList)
                for(Map<String, Object> right : eightList) 
                    distanceList.add(new MutableTriple(left, right, PairClass.distance(left, right)));
            
            Collections.sort(distanceList, (l, r) -> - Long.compare(l.getRight(), r.getRight()));
        }
        
        List<PairClass> pairs = new ArrayList<PairClass>();
        for(int index = 0; index < handlingList.size(); index ++) {
            List<Triple<Map<String, Object>, Map<String, Object>, Long>> distanceList = distances[index];
            
            while(!distanceList.isEmpty()) {
                Triple<Map<String, Object>, Map<String, Object>, Long> goodPair = distanceList.remove(0);
                pairs.add(PairClass.builder().imageId(index).left(goodPair.getLeft()).right(goodPair.getMiddle()).build());
                
                Iterator<Triple<Map<String, Object>, Map<String, Object>, Long>> its = distanceList.iterator();
                while(its.hasNext()) {
                    Triple<Map<String, Object>, Map<String, Object>, Long> item = its.next();
                    if(primary == 1) {
                        if(item.getMiddle() == goodPair.getMiddle())
                            its.remove();
                    }else if(primary == 2) {
                        if(item.getLeft() == goodPair.getLeft())
                            its.remove();
                    }else {
                        if(item.getLeft() == goodPair.getLeft() || item.getMiddle() == goodPair.getMiddle())
                            its.remove();
                    }
                }
            }
        }
        
        if(pairs.isEmpty())
            return ;
        
        Pointer[] gpuFrames = new Pointer[pairs.size()];
        Memory[] area2ds    = new Memory[pairs.size()];
        
        for(int index = 0; index < pairs.size(); index ++) {
            PairClass pair = pairs.get(index);
            gpuFrames[index] = handlingList.get(pair.getImageId()).getModelRequest().getVideoFrames()[0].getGpuFrame();
            area2ds[index] = KesonUtils.roiMapToMemory(pair.rect());
        }
        
        Pointer param_keson = KesonUtils.frameToKeson(gpuFrames, area2ds, target -> KestrelApi.keson_add_item_to_object(target, "label", KestrelApi.keson_create_int(classifireLabel)));
        PointerByReference output_keson = new PointerByReference();
        
        long now = System.currentTimeMillis();
        holders[2].process(param_keson, output_keson);
        monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        
        List<Map<String, Object>> attributes = (List<Map<String, Object>>)((Map<String, Object>)KesonUtils.kesonToJson(output_keson.getValue())).get("targets");
        KesonUtils.kesonDeepDelete(param_keson);
        KesonUtils.kesonDeepDelete(output_keson);
        
        for(Map<String, Object> attribute : attributes) {
            int image_id = ((Number)attribute.get("image_id")).intValue();
            pairs.get(image_id).setAttributes((Map<String, Object>)attribute.get("attribute"));
        }
        
        for (int index = 0; index < handlingList.size(); index ++) {
            BatchItem item = handlingList.get(index);
            
            Map<String, Object> kesonObj = new HashMap<String, Object>();
            kesonObj.put("id", 0);
            List<Map<String, Object>> targets = new ArrayList<Map<String, Object>>();
            kesonObj.put("targets", targets);
            
            for(PairClass pair : pairs) {
                if(pair.getImageId() != index)
                    continue;
                
                Map<String, Object> target = new HashMap<String, Object>();
                targets.add(target);

                target.put("attributes", pair.getAttributes());
                target.put("roi", pair.rect());
            }
            
            item.setKeson(KesonUtils.stringToKeson(JSON.toJSONString(kesonObj)));
        }
    }
    
    @Getter
    @Builder
    public static class PairClass{
        
        private int imageId;
        
        private Map<String, Object> left;
        
        private Map<String, Object> right;
        
        @Setter
        private Map<String, Object> attributes;
        
        @SuppressWarnings("unchecked")
        public Map<String, Number> rect() {
            Map<String, Number> leftRoi = (Map<String, Number>)left.get("roi");
            Map<String, Number> rightRoi = (Map<String, Number>)right.get("roi");
            
            int left = Math.min(leftRoi.get("left").intValue(), rightRoi.get("left").intValue());
            int top = Math.min(leftRoi.get("top").intValue(), rightRoi.get("top").intValue());
            int width = Math.max(leftRoi.get("left").intValue() + leftRoi.get("width").intValue(), rightRoi.get("left").intValue() + rightRoi.get("width").intValue()) - left;
            int height = Math.max(leftRoi.get("top").intValue() + leftRoi.get("height").intValue(), rightRoi.get("top").intValue() + rightRoi.get("height").intValue()) - top;
            
            return Map.of("left", left, "top", top, "width", width, "height", height);
        }
        
        @SuppressWarnings("unchecked")
        public static Long distance(Map<String, Object> left, Map<String, Object> right) {
            Map<String, Number> leftRoi = (Map<String, Number>)left.get("roi");
            long leftX = leftRoi.get("left").intValue() + leftRoi.get("width").intValue()  / 2;
            long leftY = leftRoi.get("top").intValue()  + leftRoi.get("height").intValue() / 2;
            
            Map<String, Number> rightRoi = (Map<String, Number>)right.get("roi");
            long rightX = rightRoi.get("left").intValue() + rightRoi.get("width").intValue()  / 2;
            long rightY = rightRoi.get("top").intValue()  + rightRoi.get("height").intValue() / 2;
            
            return (rightX - leftX) * (rightX - leftX) + (rightY - leftY) * (rightY - leftY);
        }
    }
}