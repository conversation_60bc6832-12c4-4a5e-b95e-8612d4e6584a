package com.sensetime.intersense.cognitivesvc.xworker.handler;

import java.io.File;
import java.util.*;

import com.sensetime.storage.service.FileAccessor;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.ApplicationContext;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.HostUtils;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/** 抽象程度最大的动态模型 输入的插件路径和模型路径就开启的热拔插*/
@Getter
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class DynamicXModelHandler extends AbstractXModelHandler implements AbstractXModelHandler.ConfigAccessor, AbstractXModelHandler.VideoRecorderAccessor {
	
	protected List<File> attachedLned = new ArrayList<File>();
	
	@Autowired
	@LoadBalanced
	protected RestTemplate restTemplate;
	
	@Autowired
	protected DiscoveryClient discoveryClient;
	
	@Autowired
	protected ApplicationContext applicationContext;

	@Autowired
	private Utils.Sync sync;

	@Autowired
	protected FileAccessor fileAccessor;
    
    @SuppressWarnings("unchecked")
	@PostConstruct
    @Override
	public synchronized void initialize() {
    	log.info("***************************************************");
    	log.info("initializing [" + annotatorName() + "]");
    	log.info("***************************************************");
		try{
			List<String> dirs = new ArrayList<>();
			dirs.add(annotatorName());
			sync.sync();
			ImageUtils.mkdirsByHour(dirs, true);
			ImageUtils.mkdirsByHour(dirs, false);
			//用 annotatorName 创建目录
//			String[] paths = new String[]{"00","01","02","03","04","05","06","07","08","09",
//					"10","11","12","13","14","15","16","17","18","19",
//					"20","21","22","23"};
//
//			String dir = Utils.instance.savePath + "/" + annotatorName() + "/" + ImageUtils.dateFormatter_DAY.get().format(new Date());
//
//			File dirFile = new File(dir);
//			if(!dirFile.exists())
//				dirFile.mkdirs();
//
//			for (int i = 0; i < paths.length; i++) {
//				try{
//					File hour = new File(dir + "/" + paths[i]);
//					if(!hour.exists())
//						hour.mkdirs();
//				}catch (Exception e){
//					e.printStackTrace();
//				}
//			}
		}catch (Exception e){
			e.printStackTrace();
		}


		if(handlerEntity.getPluginPaths().length != handlerEntity.getAnnotatorPaths().length)
			throw new RuntimeException("pluginPaths and annotatorPaths is not even");
		
		for(String pluginPath : handlerEntity.getPluginPaths())
			if(!new File(pluginPath).exists()) {
                log.error(pluginPath + " not exist.");
                throw new BusinessException("3003",pluginPath + " not exist.");
            }
		
		for(String annotatorPath : handlerEntity.getAnnotatorPaths())
			if(!new File(annotatorPath).exists()) {
                log.error(annotatorPath + " not exist.");
                throw new BusinessException("3003",annotatorPath + " not exist.");
            }
		
		Initializer.bindDeviceOrNot();
		plugins = new String[handlerEntity.getPluginPaths().length];
		
		if(handlerEntity.getBatchSize() != null && handlerEntity.getBatchSize() > 0)
			batchSize = handlerEntity.getBatchSize();
		
		boolean hasException = false;
		String exceptionString = "";
		
		for(int index = 0; index < handlerEntity.getPluginPaths().length; index ++) {
			try {
				plugins[index] = KestrelApi.kestrel_plugin_load(handlerEntity.getPluginPaths()[index], "");
			}catch(Exception e) {
				hasException = true;
				exceptionString = handlerEntity.getPluginPaths()[index] + ", load plugin error.";
				break;
			}
		}
		
		if(hasException) {
			destroy();
			throw new RuntimeException(exceptionString);
		}
		
		if(StringUtils.isBlank(handlerEntity.getFlockConfig())) {
			holders = new ModelHolder[handlerEntity.getAnnotatorPaths().length];
			
			for(int index = 0; index < holders.length; index ++) {
				holders[index] = new ModelHolder(plugins[index], 
												 handlerEntity.getAnnotatorPaths()[index], 
												 handlerEntity.getCpuModelDup() > 0 ? 1 : batchSize, 
												 handlerEntity.getCpuModelDup(), 
												 isLazyInit(), 
												 false,
												 true);
				if(holders[index] == null) {
					hasException = true;
					exceptionString = handlerEntity.getAnnotatorPaths()[index] + ", load model error.";
					break;
				}
			}
		}else {
			for(String annotatorPath : handlerEntity.getAnnotatorPaths()) {
				File annotatorFile = new File(annotatorPath);
				handlerEntity.setFlockConfig(StringUtils.replace(handlerEntity.getFlockConfig(), annotatorFile.getName(), annotatorFile.getAbsolutePath()));
			}
			
			Map<String, Object> flockPipeline = (Map<String, Object>)JSON.parseObject(handlerEntity.getFlockConfig(), Map.class);
			List<Map<String, Object>> flockStreams = (List<Map<String, Object> >)flockPipeline.get("streams");
			
			holders = new ModelHolder[flockStreams.size()];
			
			for(int index = 0; index < flockStreams.size(); index ++) {
				Map<String, Object> flockStream = flockStreams.get(index);
				
				holders[index] = new ModelHolder(flockStream.get("name").toString(), 
												 JSON.toJSONString(Map.of("streams", List.of(flockStream))),
												 handlerEntity.getCpuModelDup() > 0 ? 1 : batchSize, Math.max(handlerEntity.getCpuModelDup(), 1), 
												 isLazyInit(), 
												 true,
												 true);
				
				if(holders[index] == null) {
					hasException = true;
					exceptionString = handlerEntity.getAnnotatorPaths()[index] + ", load model error.";
					break;
				}
			}
		}
		
		if(hasException) {
			destroy();
			throw new RuntimeException(exceptionString);
		}
		
		for(String attached : Objects.requireNonNullElse(handlerEntity.getAttacheds(), new String[0])) {
			File attachedFile = new File(attached);
			File attachedTarg = new File("/usr/cognitivesvc/" + attachedFile.getName());
			
			if(attachedTarg.exists()) {
				log.warn("target [" + attachedTarg.getAbsolutePath() + "] exist, use is instead of [" + attachedFile.getAbsolutePath() + "].");
				continue;
			}
			
			attachedLned.add(attachedTarg);
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + attachedFile.getAbsolutePath() + " " + attachedTarg.getAbsolutePath()});
		}
		
		if(hasException) {
			destroy();
			throw new RuntimeException(exceptionString);
		}
		
		startHandle();
	}
	
	@PreDestroy
	@Override
	public synchronized void destroy() {
    	log.info("***************************************************");
    	log.info("destroying xmodel [" + annotatorName() + "]");
    	log.info("***************************************************");
    	
        stopHandle();
        
        Initializer.bindDeviceOrNot();
        
		for(int index = 0; holders != null && index < holders.length; index ++) {
			if(holders[index] != null)
				holders[index].close();
			
			holders[index] = null;
		}
		
		for(int index = 0; plugins != null && index < plugins.length; index ++) {
			if(plugins[index] == null)
				continue;
			
			KestrelApi.kestrel_plugin_unload(plugins[index]);
			
			plugins[index] = null;
		}
		
		for(File attachedTarg : attachedLned)
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "rm -f " + attachedTarg.getAbsolutePath()});
	}
}
