package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.List;

import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;

public class UseOriginOutput extends DynamicXModelHandler{
    
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        return true;
    }
    
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        modelResult.setOutputResult(modelResult.getDetectResult());
        
        if(modelResult.getOutputResult() == null)
            modelResult.setOutputResult(List.of());
    }
}