import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.ptr.PointerByReference;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ForceHanding extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    @Getter
    protected final int drainPollcount = 4;

    @Getter
    protected final int drainTimeout = 10;

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final Boolean needContext = true;

    @Getter
    protected final String decoderFormat = "rgb24";

    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";

    @Getter
    protected final Integer interval = 8;

    @Getter
    protected final boolean queueMap = true;

    private final ConcurrentHashMap<String, Long> trackLiteMap = new ConcurrentHashMap<String, Long>();

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons)[0], imageIds);

        //log.info("handingRes:{}", KesonUtils.kesonToJson(outputKesons[0]));

        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);
        }

    }

    @SuppressWarnings("unchecked")
    @Override
    public void readModelResult(ModelResult modelResult) {
        super.readModelResult(modelResult);

        Map<String, Object> detectResult = (Map<String, Object>) modelResult.getDetectResult();
        if (MapUtils.isEmpty(detectResult))
            return;

        //log.info("detectResult:{}", JSON.toJSONString(detectResult));
        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get("targets");
        Iterator<Map<String, Object>> its = targets.iterator();
        while (its.hasNext()) {
            Map<String, Object> target = its.next();
            if (!target.containsKey("action_type"))
                its.remove();
            else {
                Map<String, Object> action = Map.of("roi", target.get("action_roi"),
                        "value", target.get("action_type"),
                        "action_type", target.get("action_type"),
                        "confidence", target.get("confidence"),
                        "image_id", target.get("image_id"));

                target.clear();
                target.putAll(action);
            }
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {
        List<Map<String, Object>> outputs = (List<Map<String, Object>>) modelResult.getOutputResult();
        if (CollectionUtils.isEmpty(outputs))
            return List.of();

        List<Drawing> result = new ArrayList<Drawing>();
        for (Map<String, Object> output : outputs) {
            Map<String, Integer> roi = (Map<String, Integer>) output.get("detect");

            String text = ((List<Map<String, Object>>) output.get("attributes")).stream()
                    .map(attribute -> attribute.get("value").toString())
                    .collect(Collectors.joining(","));

            result.add(Rect.builder()
                    .processor(annotatorName())
                    .text(text)
                    .top(roi.get("top"))
                    .left(roi.get("left"))
                    .width(roi.get("width"))
                    .height(roi.get("height"))
                    .build());
        }

        return result;
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void postProcessOutputValue(ModelResult modelResult) {
        super.postProcessOutputValue(modelResult);

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        if (StringUtils.isBlank(deviceId) || processor == null || CollectionUtils.isEmpty(processor.getExtras()))
            return;

        long now = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();

        List<Map<String, Object>> outputResult = (List<Map<String, Object>>) modelResult.getOutputResult();
        Map<String, List<Map<String, Object>>> roiOutputResult = new HashMap<String, List<Map<String, Object>>>();
        for (Map<String, Object> valueResult : (List<Map<String, Object>>) modelResult.getOutputResult()) {
            Map<String, Integer> detect = (Map<String, Integer>) valueResult.get("detect");
            if (org.apache.commons.collections4.MapUtils.isEmpty(detect))
                continue;

            String[] rois = (String[]) valueResult.get("rois");
            if (ArrayUtils.isEmpty(rois))
                continue;

            for (String roi : rois) {
                List<Map<String, Object>> roiMap = roiOutputResult.get(roi);
                if (roiMap == null) {
                    roiMap = new ArrayList<Map<String, Object>>();
                    roiOutputResult.put(roi, roiMap);
                }

                roiMap.add(valueResult);
            }
        }

        boolean logged = Utils.instance.watchFrameTiktokLevel == -321;
        if (logged) {
            log.info("roiOutputResult={}, deviceId={}", JSON.toJSONString(roiOutputResult), deviceId);
        }
        Polygon[] polygons = processor.fetchPolygons();
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);
        Long contextID = Utils.keyToContextId(deviceId);

        // 获取 ROI 是否告警
        Map<String, Boolean> roiTrigger = new HashMap<>();
        List<String> roiIds = ((List<Map<String, Object>>) Objects.requireNonNullElse(
                modelResult.getModelRequest().getProcessor().getExtras(), List.of()))
                .stream()
                .filter(item -> "roiIds".equals(item.get("type")))
                .findAny()
                .map(item -> (List<String>) item.getOrDefault("roiIds", List.of()))
                .orElse(List.of());

        for (int index = 0; index < polygons.length; index++) {
            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());

            String roiId = roiIds.isEmpty() ? "none" : roiIds.get(index);

            if (org.apache.commons.collections4.MapUtils.isNotEmpty(extrasMap) && extrasMap.containsKey("trigger") && !roiOutputResult.get(roiId).isEmpty()) {
                String triggerKey = contextID + "_" + index;

                Map<String, Object> trigger = (Map<String, Object>) extrasMap.get("trigger");
                // 获取 trackFreq，确保与数据类型的兼容
                Object trackFreqObj = trigger.getOrDefault("trackFreq", 1000);
                long trackFreq;

                if (trackFreqObj instanceof Long) {
                    trackFreq = (Long) trackFreqObj;
                } else if (trackFreqObj instanceof Integer) {
                    trackFreq = ((Integer) trackFreqObj).longValue();
                } else {
                    // 如果trackFreq不是Integer或Long，使用默认值
                    trackFreq = 1000L;
                }
                long lastTrackTime = trackLiteMap.getOrDefault(triggerKey, 0L);

                if (trackLiteMap.containsKey(triggerKey) && lastTrackTime != 0 &&
                        (now - lastTrackTime < trackFreq)) {
                    log.info("LiteFalcon checkFail {}, {}, {},{}, {}", deviceId, now, lastTrackTime, now - lastTrackTime, trackFreq);
                    roiTrigger.put(roiIds.isEmpty() ? "none" : roiIds.get(index), true);
                } else {
                    log.info("esosBag checkTrue {}, {}, {},{}, {}", deviceId, now, lastTrackTime, now - lastTrackTime, trackFreq);
                    roiTrigger.put(roiIds.isEmpty() ? "none" : roiIds.get(index), false);
                    trackLiteMap.put(triggerKey, now);
                }

                // 如果不存在，则 save 当前时间
                if (!trackLiteMap.containsKey(triggerKey)) {
                    trackLiteMap.put(triggerKey, now);
                }
            }
        }
        if (logged) {
            log.info("roiTrigger={}, deviceId={}", roiTrigger, deviceId);
        }

        // 收集需要删除的 ROI
        List<String> keysToRemove = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : roiOutputResult.entrySet()) {
            if (roiTrigger.getOrDefault(entry.getKey(), false)) {
                keysToRemove.add(entry.getKey());
            }
        }
        // 在循环外删除收集的键
        for (String key : keysToRemove) {
            roiOutputResult.remove(key);
        }
        if (logged) {
            log.info("roiOutputResult process end={}, deviceId={}", roiOutputResult, deviceId);
        }

        outputResult = roiOutputResult.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        if (outputResult.isEmpty()) {
            modelResult.setOutputResult(null);
        } else {
            log.info("outputResult{}", JSON.toJSONString(outputResult));
            modelResult.setOutputResult(outputResult);
        }
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if (e != null)
            for (Map<String, Object> param : (List<Map<String, Object>>) e) {
                if (!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer) param.get("roiIndex");
                if (roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;
            long contextId = Utils.keyToContextId(event.getDeviceId());

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(contextId);
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("falcon [" + getClass().getSimpleName() + "] start context [" + contextId + "].");
        //trackLiteMap.put(contextId,  System.currentTimeMillis() );
        trackLiteMap.put(String.valueOf(contextId), System.currentTimeMillis());
    }

    private void stopContext(long contextId) {
        holders[0].controlForRemoveSource(contextId);
        log.info("falcon [" + getClass().getSimpleName() + "] stop context [" + contextId + "].");


        String prefix = contextId + "_";
        // 收集要删除的键
        List<String> keysToRemove = new ArrayList<>();
        // 遍历 trackDiffMap，找到所有以 prefix 开头的键
        for (String key : trackLiteMap.keySet()) {
            if (key.startsWith(prefix)) {
                keysToRemove.add(key); // 收集与设备相关的键
            }
        }
        // 删除收集的键
        for (String key : keysToRemove) {
            trackLiteMap.remove(key);
        }

        controlForRemoveSource(contextId);
    }

    public PointerByReference controlForRemoveSource(long contextId) {
        if(holders[0].pointers[0] == null)
            return new PointerByReference();

        String aa = "{\n" +
                "  \"source_id\": 1926064092,\n" +
                "  \"streams\": [\n" +
                "    {\n" +
                "      \"modules\": [\n" +
                "        {\n" +
                "          \"name\": \"sot\",\n" +
                "          \"source_id\": 1926064092\n" +
                "        },\n" +
                "        {\n" +
                "\n" +
                "          \"name\": \"mot\",\n" +
                "          \"source_id\": 1926064092\n" +
                "        }\n" +
                "      ],\n" +
                "      \"name\": \"video_falcon_track_stream\",\n" +
                "      \"source_id\": 1926064092\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        aa = aa.replace("1926064092",String.valueOf(contextId));
        PointerByReference input = KesonUtils.stringToKeson(aa);
//                Pointer input = KesonUtils.buildFlockRemoveInput(contextId, key);
        PointerByReference out = new PointerByReference();

        for(int index = 0; index < holders[0].pointers.length; index++) {
            synchronized(this){
                KestrelApi.flock_pipeline_control(holders[0].pointers[index], KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input.getValue(), out);
            }
        }

        KesonUtils.kesonDeepDelete(input);
        return out;
    }
}