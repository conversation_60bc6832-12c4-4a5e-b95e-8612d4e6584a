package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ProcessPCBP extends DynamicXModelHandler{

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        super.buildOutputValue(modelResult);

        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
        List<Map<String, Object>> outputs = (List<Map<String, Object>>)modelResult.getOutputResult();
        
        for(Map<String, Object> output : outputs) {
            for(Map<String, Object> target : targets) {
                if(output.get("detect") != target.get("roi") || ((Number)target.get("label")).intValue() != 1420) //是相同的对象
                    continue;
                
                List<Map<String, Object>> carplates = (List<Map<String, Object>>)target.get("carplate");
                if(CollectionUtils.isEmpty(carplates))
                    continue;
                
                Map<String, Object> carplate = carplates.get(0);
                List<Map<String, Object>> attributes = (List<Map<String, Object>>)output.get("attributes");

                attributes.add(Map.of("key", "plate_text",     "value", carplate.get("plate_text"),     "confidence", carplate.get("plate_score")));
                attributes.add(Map.of("key", "carplate_type",  "value", carplate.get("carplate_type"),  "confidence", carplate.get("plate_score")));
                attributes.add(Map.of("key", "carplate_color", "value", carplate.get("carplate_color"), "confidence", carplate.get("plate_score")));

            }
        }


    }

    /** 后处理结果 多帧检测 */
    protected void postProcessOutputValue(ModelResult modelResult){

        super.postProcessOutputValue(modelResult);

        float threshold = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getThreshold(), 0.0f);

        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
        List<Map<String, Object>> outputs = (List<Map<String, Object>>)modelResult.getOutputResult();

        for(Map<String, Object> output : outputs) {

                List<Map<String, Object>> attributes = (List<Map<String, Object>>)output.get("attributes");

                for (int i = 0; i < attributes.size(); i++) {

                    if(attributes.get(i) == null){
                        continue;
                    }
                    if(attributes.get(i).get("key") !=null && attributes.get(i).get("key").toString().equals("vehicle_brand") ){

                        float quality = (((Number)attributes.get(i).getOrDefault("confidence", 0d)).floatValue());
                        if(quality < threshold) {
                            attributes.remove(i);
                        }
                    }

                    if(attributes.get(i).get("key") !=null && attributes.get(i).get("key").toString().equals("st_vehicle_vendor") ){
                        float quality = (((Number)attributes.get(i).getOrDefault("confidence", 0d)).floatValue());
                        if(quality < threshold) {
                            attributes.remove(i);
                        }
                    }
                    if(attributes.get(i).get("key") !=null && attributes.get(i).get("key").toString().equals("vehicle_model") ){
                        float quality = (((Number)attributes.get(i).getOrDefault("confidence", 0d)).floatValue());
                        if(quality < threshold) {
                            attributes.remove(i);
                        }
                    }

                    if(attributes.get(i).get("key") !=null && attributes.get(i).get("key").toString().equals("vehicle_styles") ){
                        float quality = (((Number)attributes.get(i).getOrDefault("confidence", 0d)).floatValue());
                        if(quality < threshold) {
                            attributes.remove(i);
                        }
                    }
                }

//                Iterator<Map<String, Object>> its = attributes.iterator();
//                while(its.hasNext()) {
//                    Map<String, Object> attribute = its.next();
//                    if(attribute.get("key") !=null && attribute.get("key").toString().equals("vehicle_brand") ){
//
//                        float quality = (((Number)attribute.getOrDefault("confidence", 0d)).floatValue());
//                        if(quality < threshold) {
//                            its.remove();
//                            attribute =null;
//                            log.info("pcbp{}--{},{}", quality, threshold, attribute);
//                        }
//                    }
//
//                    if(attribute.get("key") !=null && attribute.get("key").toString().equals("st_vehicle_vendor") ){
//                        float quality = (((Number)attribute.getOrDefault("confidence", 0d)).floatValue());
//                        if(quality < threshold) {
//                            its.remove();
//                            attribute =null;
//                            log.info("pcbp{}--{},{}", quality, threshold, attribute);
//                        }
//                    }
//                }
                    //log.info("pcbp{}", JSON.toJSONString(attributes));
                  output.put("attributes", attributes);

        }
        if(outputs.isEmpty())
            modelResult.setOutputResult(null);
        else
            modelResult.setOutputResult(outputs);
        //log.info("pcbp-out{}", JSON.toJSONString(outputs));
    }
}