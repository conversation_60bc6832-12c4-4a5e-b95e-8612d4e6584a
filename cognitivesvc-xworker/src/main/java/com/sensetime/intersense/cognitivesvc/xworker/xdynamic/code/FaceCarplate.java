package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.ptr.PointerByReference;

import lombok.Getter;

public class FaceCarplate extends DynamicXModelHandler{
    
    @Getter
    protected boolean lazyInit = true;
    
    @SuppressWarnings("unchecked")
    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || !(modelResult.getKeson() instanceof PointerByReference))
            return ;
        
        Map<String, Object> detectResult = (Map<String, Object>)KesonUtils.kesonToJson(((PointerByReference)modelResult.getKeson()).getValue());
        
        List<Map<String, Object>> targets = (List<Map<String, Object>>)detectResult.get("targets"); 
        List<Map<String, Object>> roiTargets = new ArrayList<Map<String, Object>>();
        
        Iterator<Map<String, Object>> its = targets.iterator();
        while(its.hasNext()) {
            Map<String, Object> target = its.next();
            
            if(Objects.equals(target.get("flock_index"), 2L)) {
                its.remove();
                roiTargets.add(target);
            }
        }
        
        for(Map<String, Object> target : targets) {
            if(!Objects.equals(target.get("flock_index"), 1L))
                continue;
            
            for(Map<String, Object> roiTarget : roiTargets) {
                if(!Objects.equals(target.get("image_id"), roiTarget.get("image_id")))
                    continue;
                
                target.put("image_id", roiTarget.get("parent_image_id"));
                
                Map<String, Number> roi = (Map<String, Number>)target.get("roi");
                Map<String, Number> targetRoi = (Map<String, Number>)roiTarget.get("parent_roi");
                
                roi.put("left", roi.get("left").intValue() + targetRoi.get("left").intValue());
                roi.put("top",  roi.get("top").intValue()  + targetRoi.get("top").intValue());
            }
        }
        
        Map<String, Object> additional = (Map<String, Object>)Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
        for(KestrelInterceptor interceptor : interceptors)
            interceptor.postReadDetectResult(additional, (PointerByReference)modelResult.getKeson(), detectResult);
        
        modelResult.setDetectResult(detectResult);
    }
}