package com.sensetime.intersense.cognitivesvc.xworker.controller;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawImage;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SenseyexRawVideo;
import com.sensetime.intersense.cognitivesvc.server.kestrel.OpencvDecoderLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XSenseyexEventHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XStoreHandler;
import com.sensetime.lib.clientlib.response.BaseRes;


import com.sensetime.lib.weblib.exception.BusinessException;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import io.swagger.v3.oas.annotations.Operation;

import java.util.Date;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController(value = "xWorkerProvider")
@RequestMapping(value = "/cognitive/xworker/", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
@Slf4j
public class XWorkerProvider extends BaseProvider {
    
    @Operation(summary = "跑图片喽", method = "POST", hidden = true)
    @RequestMapping(value = "/execute/event", method = RequestMethod.POST)
    public BaseRes<Object> executeEvent(@RequestBody SenseyexRawImage message) throws Exception {
    	if(XSenseyexEventHandler.seneyexRawImageHandleQueue.remainingCapacity() <= 0)
    		XSenseyexEventHandler.seneyexRawImageHandleQueue.poll();


		if(message.getCapturedTime() == null)
			message.setCapturedTime(System.currentTimeMillis());

		boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;
		long start = 0L;
		if(loggedForCost){
			start = new Date().getTime();
		}
		if(loggedForCost) {
			log.info("[VideoHandleLog] [Cost] [handleLowRateEvent] step -3 intoHandle deviceId :{}, cost,{} ms, req: {}", message.getDeviceId(), start - message.getCapturedTime(), JSON.toJSONString(message));
		}


		Boolean response = MapUtils.getBoolean(message.getExtra(), "response");
    	if(Boolean.TRUE.equals(response)) {
    		synchronized(message) {
    			XSenseyexEventHandler.seneyexRawImageHandleQueue.offer(message);
    			message.wait();
    		}
    		
    		return BaseRes.success(message.getExtra().get("response"));
    	}else
	    	return BaseRes.success(XSenseyexEventHandler.seneyexRawImageHandleQueue.offer(message));
    }
    
    @Operation(summary = "跑视频喽", method = "POST", hidden = true)
    @RequestMapping(value = "/execute/video", method = RequestMethod.POST)
    public BaseRes<Boolean> executeEvent(@RequestBody SenseyexRawVideo message) throws Exception {
    	if(XSenseyexEventHandler.seneyexRawVideoHandleQueue.remainingCapacity() <= 0)
    		XSenseyexEventHandler.seneyexRawVideoHandleQueue.poll();
    	
        return BaseRes.success(XSenseyexEventHandler.seneyexRawVideoHandleQueue.offer(message));
    }
    
    @Operation(summary = "编译动态代码", method = "POST", hidden = true)
    @RequestMapping(value = "/compile/dynamic/code", method = RequestMethod.POST)
    public BaseRes<String> compileDynamicCode(@RequestBody Map<String, String> javaCode){
    	String name = javaCode.get("javaClassName");
    	String code = javaCode.get("javaCode");
    	
    	try {
    		XStoreHandler.compileJavaCode(name, code);
		} catch (Exception e) {
			return BaseRes.success("failure");
		}
    	
        return BaseRes.success("success");
    }

	@Operation(description = "获取流的一帧", method = "GET")
	@RequestMapping(value = "/fetchFrame", method = RequestMethod.GET)
	public BaseRes<String> fetchFrame(@RequestParam(required = false) String rtsp_source) throws Exception {
    	//String path1 = FrameUtils.save_image_as_jpg(FrameUtils.ref_frame(frame), ImageUtils.newFileWithMkdir("fetchFrame"));
//			log.info("save path333 {}", path1);

		//String rtsp_source = "rtsp://**********:8554/265/265_2_11";


//		Initializer.bindDeviceOrNot();
//
//
//		OpencvDecoderLibrary.OpencvVideoDecoder h265PrivateDecoder = KestrelApi.kestrel_create_opencv_decoder(Initializer.isGpu());
//
//		boolean checkStream = KestrelApi.kestrel_create_opencv_stream(h265PrivateDecoder, rtsp_source);
//
//		PointerByReference frame = new PointerByReference();
//
//		int errCode = KestrelApi.kestrel_get_opencv_frame(h265PrivateDecoder, frame);
//
//		String path = FrameUtils.save_image_as_jpg(frame.getValue(), ImageUtils.newFileWithMkdir("fetchFrame"));
//		log.info("save path4444 {},{}", path, errCode);
//
//		h265PrivateDecoder = null;
//
//		FrameUtils.batch_free_frame(frame);
//		return BaseRes.success(path);

		VideoStream.VideoFrame videoFrame = null;
		try {
			videoFrame = FrameUtils.fetch_up_down_load_frame_path(rtsp_source);

			//Pointer bufferedFrame = FrameUtils.ref_or_cvtcolor_frame(videoFrame.getFrame(), KestrelApi.KESTREL_VIDEO_BGR);

			//log.info("xxxxxx{}",KestrelApi.kestrel_frame_video_format(videoFrame.getFrame()));

			String path1 = FrameUtils.save_image_as_jpg(videoFrame.getFrame(), ImageUtils.newFileWithMkdir("fetchFrame"));

			//FrameUtils.batch_free_frame(videoFrame.getFrame());

//			if(path1.equals(FrameUtils.NOIMAGE)){
//				return  BaseRes.success("");
//			}
			return BaseRes.success(path1);
		}catch (BusinessException e){
			e.printStackTrace();
			throw e;
		}catch (Exception ex){
			ex.printStackTrace();
			throw new BusinessException("1001", ex.getMessage());
		}finally {
			FrameUtils.batch_free_frame(videoFrame);
		}
		//return BaseRes.success("");
	}
}
