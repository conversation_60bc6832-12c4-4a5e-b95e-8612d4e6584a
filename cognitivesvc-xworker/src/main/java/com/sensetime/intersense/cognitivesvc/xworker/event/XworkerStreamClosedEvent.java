package com.sensetime.intersense.cognitivesvc.xworker.event;

import lombok.Getter;

@Getter
public class XworkerStreamClosedEvent extends XworkerEvent {
	private static final long serialVersionUID = 6519770944018305081L;

	private String deviceId;
	private String cause;
	
	public XworkerStreamClosedEvent(String deviceId, String annotatorName, String cause) {
		super(annotatorName);
		
		this.deviceId = deviceId;
		this.cause = cause;
	}
	
	public static final String CLOSE  = "CLOSE";
	public static final String REOPEN = "REOPEN";
}
