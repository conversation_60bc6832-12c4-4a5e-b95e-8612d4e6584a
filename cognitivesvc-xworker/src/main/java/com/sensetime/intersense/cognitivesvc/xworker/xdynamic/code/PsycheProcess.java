package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_tensor_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

public class PsycheProcess extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    
	@Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || !(modelResult.getKeson() instanceof PointerByReference))
            return ;
        
        Pointer featurePointer = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(KestrelApi.keson_get_object_item(modelResult.getKeson().getValue(), "targets"), 0), "feature");
        
        PointerByReference bufferExt = new PointerByReference();
        KestrelApi.keson_get_ext_data(featurePointer, bufferExt);
        
        kestrel_tensor_t tensor = new kestrel_tensor_t(bufferExt.getValue());
        tensor.read();
        
        int channel = (int)tensor.meta.dims[1];
        int width   = (int)tensor.meta.dims[2];
        int height  = (int)tensor.meta.dims[3];
        
        int[] result = new int[width * height];
        for(int h = 0; h < height; h ++) {
        	for(int w = 0; w < width; w ++) {
        		final int hh = h, ww = w;
        		
        		result[width * hh + ww] = Stream.iterate(0, i -> i + 1).limit(channel)
					        				  .max((l, r) -> Float.compare(tensor.data.getFloat(4 * (width * height * l + width * hh + ww)), tensor.data.getFloat(4 * (width * height * r + width * hh + ww))))
					        				  .get()
					        				  .intValue();
        	}
		}
        
        modelResult.setDetectResult(result);
    }
	
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
         return true;
    }
    
    @Override
    public void buildOutputValue(ModelResult modelResult) {
    	 modelResult.setOutputResult(List.of(Map.of("mask", modelResult.getDetectResult())));
    }
}
