package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.awt.Polygon;
import java.io.File;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import lombok.Builder;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;

import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.VideoRecorderAccessor.Line;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Scalar;

@Slf4j
@SuppressWarnings("unused")
public class ShieldFace extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {
    private final ConcurrentHashMap<Long, List<Map<String, Object>>> ongoingAttributeMap = new ConcurrentHashMap<Long, List<Map<String, Object>>>();

    private final ConcurrentHashMap<Long, Long> ongoingFaceBodyMatchMap = new ConcurrentHashMap<Long, Long>();
    private final ConcurrentHashMap<Long, TrackFace> ongoingFaceBodyMatchMapNew = new ConcurrentHashMap<Long, TrackFace>();
    @Getter
    protected final Integer interval = 0;//检一跳三

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final Boolean needContext = true;

    @Getter
    protected final boolean queueMap = true;

    @Getter
    protected final String decoderFormat = "nv12";

    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds);
        PointerByReference[] sub1_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[1])[0], imageIds);

        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();

            if (sub0_kesons[index] != null) KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            if (sub1_kesons[index] != null) KestrelApi.keson_add_item_to_array(array, sub1_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            handlingList.get(index).setKeson(new PointerByReference(array));
        }

        KesonUtils.kesonDeepDelete(outputKesons);
    }

    @SuppressWarnings({"unchecked"})
    @Override
    /**
     * 按照 函数包含的模型 顺序的跑一边
     */
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (ArrayUtils.isEmpty(holders) || status != 0) return new PointerByReference[0];

        ModelHolder[] pointers = prepareModelHolder(handlingList);
        PointerByReference param_keson = prepareInput(handlingList);
        PointerByReference[] output_kesons = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);

        Map<String, Object> additional = (Map<String, Object>) Objects.requireNonNullElse(getHandlerEntity().getAdditional(), Map.of());
        for (int index = 0; index < pointers.length; index++) {
            PointerByReference inTo = index == 0 ? param_keson : output_kesons[index - 1];
            PointerByReference outOf = output_kesons[index];

            boolean doStream2 = true;
            long now = System.currentTimeMillis();
            /** 执行模型 获取数据*/
            pointers[index].process(inTo.getValue(), outOf);
            String deviceId = (String) handlingList.get(0).getModelRequest().getParameter().get("deviceId");
            Long streamSourceId = Utils.keyToContextId(deviceId);


            if (index == 0) {
                Integer[][][] proi = handlingList.get(0).getModelRequest().getProcessor().getRoi();
                String policyRoiString = JSON.toJSONString(toArrayStringRi(proi));

                Map<String, Object> flockPipeline = (Map<String, Object>) JSON.parseObject(pointers[index].value, Map.class);
                List<Map<String, Object>> flockStreams = (List<Map<String, Object>>) flockPipeline.get("streams");
                List<Map<String, Object>> modules = (List<Map<String, Object>>) flockStreams.get(0).get("modules");

                if (deviceRoiMap.get(streamSourceId) == null) {
                    deviceRoiMap.put(streamSourceId, policyRoiString);
                    updateRoi(pointers[index].pointers[0], policyRoiString, streamSourceId.toString());
                } else {
                    String oldRoiString = deviceRoiMap.get(streamSourceId);
                    if (!oldRoiString.equals(policyRoiString)) {
                        deviceRoiMap.put(streamSourceId, policyRoiString);
                        updateRoi(pointers[index].pointers[0], policyRoiString, streamSourceId.toString());
                    }
                }
//                for(int i = 0; i < modules.size(); i ++) {
//                    Map<String, Object> module = modules.get(i);
//                    String moduleName = (String)module.get("name");
//                    if(moduleName.equals("roi_filter")){
//                        Map<String, Object> config = (Map<String, Object>)module.get("config");
//                        List<Map<String, Object>> roiFilters = (List<Map<String, Object>>)config.get("roi_filter");
//                        String polygonsString = roiFilters.get(0).get("polygons").toString();
//                        String proiString = JSON.toJSONString(toArrayStringRi(proi));
//                        if(!polygonsString.equals(proiString)){
//                            PointerByReference out = new PointerByReference();
//                            roiFilters.get(0).put("polygons", proiString);
//                            pointers[index].value = JSON.toJSONString(flockPipeline);
//
//
//                            String controlPipeStrng =" {\n" +
//                                    "                \"streams\": [\n" +
//                                    "                        {\n" +
//                                    "                                \"name\": \"video_face_body_track_stream\",\n" +
//                                    "                                \"modules\": [\n" +
//                                    "                                        {\n" +
//                                    "                                                \"name\": \"roi_filter\",\n" +
//                                    "                                                \"source_id\": 49650,\n" +
//                                    "                                                \"config\": {\n" +
//                                    "                                                    \"roi_filter\": [\n" +
//                                    "                                                        {\n" +
//                                    "                                                            \"label_id\": 221488,\n" +
//                                    "                                                            \"polygons\": aaaa\n" +
//                                    "                                                        }\n" +
//                                    "                                                    ]\n" +
//                                    "                                                }\n" +
//                                    "                                        }\n" +
//                                    "                                ]\n" +
//                                    "                        }\n" +
//                                    "                ]\n" +
//                                    "        }";
//                            controlPipeStrng = controlPipeStrng.replace("aaaa",proiString);
//                            controlPipeStrng = controlPipeStrng.replace("49650",streamSourceId.toString());
//                            PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);
//                            KestrelApi.flock_pipeline_control(pointers[index].pointers[0], KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);
////                            log.info("update roi_filter polygons from {} to {}", polygonsString, proiString);
//
//                        }
//
//                    }
//                }

//

                monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);

//                Object origin = KesonUtils.kesonToJson(outOf.getValue(),false);
//                Object replace = KesonUtils.kesonToJson(outOf.getValue(),true);
//                List<Map<String, Object>> detectResult = (List<Map<String, Object>>) origin;
//                List<Map<String, Object>> replaceDetectResult = (List<Map<String, Object>>) replace;
//                List<Map<String, Object>> targets = (List<Map<String, Object>>) replaceDetectResult.get(0).get("targets");
//                List<Map<String, Object>> originTargets = (List<Map<String, Object>>)detectResult.get(0).get("targets");
//                List<Map<String, Object>> filterList = new ArrayList<Map<String, Object>>();
//                for(int i =0;i<targets.size();i++){
//                    int size = filter(handlingList.get(index).getModelRequest().getProcessor(), Lists.newArrayList(targets.get(i)), "roi").size();
//                    if(size>0){
//                        filterList.add(originTargets.get(i));
//                    }else {
//                        doStream2 = false;
//                    }
//                }

            }


//            if(!doStream2){
//                break;
//            }


        }

        KesonUtils.kesonDeepDelete(param_keson);
        return output_kesons;
    }


    public void updateRoi(Pointer pipelinePoint, String policyRoiString, String sourceId) {
        PointerByReference out = new PointerByReference();
        String controlPipeStrng =" {\n" +
                "                \"streams\": [\n" +
                "                        {\n" +
                "                                \"name\": \"video_face_body_track_stream\",\n" +
                "                                \"modules\": [\n" +
                "                                {\n" +
                "                    \"name\": \"face_filter\",\n" +
                "                    \"type\": \"DetectionFilter\",\n" +
                "                    \"source_id\": 49650,\n" +
                "                    \"inputs\": [\n" +
                "                        \"face_targets_uid\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"face_targets_uid_filtered\"\n" +
                "                    ],\n" +
                "                    \"config\": {\n" +
                "                        \"roi_filter\": [\n" +
                "                            {\n" +
                "                                \"label_id\": 37017,\n" +
                "                                \"point_cal_type\": 1,\n" +
                "                                \"polygons\": aaaa\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                " }," +
                "                               {\n" +
                "                    \"name\": \"struct_filter\",\n" +
                "                    \"type\": \"DetectionFilter\",\n" +
                "                    \"source_id\": 49650,\n" +
                "                    \"inputs\": [\n" +
                "                        \"struct_detected_targets\"\n" +
                "                    ],\n" +
                "                    \"outputs\": [\n" +
                "                        \"struct_detected_targets_filtered\"\n" +
                "                    ],\n" +
                "                    \"config\": {\n" +
                "                        \"roi_filter\": [\n" +
                "                            {\n" +
                "                                \"label_id\": 221488,\n" +
                "                                \"point_cal_type\": 0,\n" +
                "                                \"polygons\": aaaa\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                                     }" +

                "                                ]\n" +
                "                        }\n" +
                "                ]\n" +
                "        }";
        controlPipeStrng = controlPipeStrng.replace("aaaa", policyRoiString);
        controlPipeStrng = controlPipeStrng.replace("49650", sourceId);
        PointerByReference input = KesonUtils.stringToKeson(controlPipeStrng);
        KestrelApi.flock_pipeline_control(pipelinePoint, KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);


        log.info(">>> [ShieldFace] update roi_filter device is: {} , roi is: {}", sourceId, controlPipeStrng);

    }

    public String getSelectRoiFilter(String roi, String defRoi) {
        if (StringUtils.isBlank(roi)) {
            return defRoi;
        }
        Integer[][][] rois = JSON.parseObject(roi, Integer[][][].class);
        if (rois.length <= 0) {
            return defRoi;
        }
        return JSON.toJSONString(toArrayStringRi(rois));
    }

    public static String[] toArrayStringRi(Integer[][][] arr) {
        int numRows = arr.length;
        String[] result = new String[numRows];
        for (int i = 0; i < numRows; i++) {
            result[i] = Arrays.deepToString(arr[i]);
        }
        return result;
    }

    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || modelResult.getKeson().getValue() == null) return;

        modelResult.setDetectResult(KesonUtils.kesonToJson(modelResult.getKeson()));
    }

    @SuppressWarnings({"unchecked"})
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult)) return false;
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();
        long source_id = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        float threshold = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getThreshold(), 0f);
        int minSize = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getMinSize(), Integer.MIN_VALUE);
        Pointer kesonTargetsValidate = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 1), "targets");
        int kesonTarget_size_validate = KestrelApi.keson_array_size(kesonTargetsValidate);

        /**
         * 开始里每一帧
         */
        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");
//        log.info("[shieldFace] target00000 {} ",JSON.toJSONString(targets));
        for (Map<String, Object> target : targets) {
            List<Map<String, Object>> associations = (List<Map<String, Object>>) target.get("associations");
            if (CollectionUtils.isEmpty(associations)) continue;

            int faceTrackId, bodyTrackId;

            int label = ((Number) target.get("label")).intValue();
            if (label == 37017) {
                faceTrackId = ((Number) target.get("track_id")).intValue();
                bodyTrackId = ((Number) associations.get(0).get("track_id")).intValue();
            } else {
                bodyTrackId = ((Number) target.get("track_id")).intValue();
                faceTrackId = ((Number) associations.get(0).get("track_id")).intValue();
            }

            ongoingFaceBodyMatchMap.put((source_id << 32) + bodyTrackId, (source_id << 32) + faceTrackId);
        }


        // 字体样式、字体大小、字体颜色
        int fontFace = opencv_imgproc.FONT_HERSHEY_DUPLEX;
        double fontScale = 0.8;
        Scalar color = new Scalar(0, 0, 255, 0);
        int thickness = 1;

        long frameIndex = modelResult.getModelRequest().getVideoFrames()[0].getFrameIndex();
        /**
         * 开始处理每个选帧
         */
        List<Map<String, Object>> tracklets = (List<Map<String, Object>>) detectResult.get(1).get("targets");
        for (Map<String, Object> tracklet : tracklets) {

            int label = ((Number) tracklet.get("label")).intValue();
            List<Map<String, Object>> associations = (List<Map<String, Object>>) tracklet.get("associations");

            int track_id = ((Number) tracklet.get("track_id")).intValue();
            log.info(">>> [shieldFace] trackletsFirstStep={}, associations-body-face, source_id: {} ,track_id: {}, label:{},tracklet.get(\"source_id\")={}, frameIndex={}", associations, deviceId, track_id, label, tracklet.get("source_id"), frameIndex);

//            if (label == 37017){
//                track_id = ((Number) associations.get(0).get("track_id")).intValue();
//            }
            List<Map<String, Object>> attributes = ongoingAttributeMap.get((source_id << 32) + track_id);
            if (attributes == null) {
                attributes = new ArrayList<Map<String, Object>>();
                ongoingAttributeMap.put((source_id << 32) + track_id, attributes);
            }

            if (label == 37017)
                attributes.add((Map<String, Object>) ((Map<String, Object>) tracklet.get("attribute")).get("respirator_color"));
            else if (label == 221488)
                attributes.add((Map<String, Object>) ((Map<String, Object>) tracklet.get("attribute")).get("st_pedestrian_angle"));

            //add
            int faceTrackId = 0, bodyTrackId = 0;
            int bodyId = 0;

            if (label == 37017) {
                faceTrackId = ((Number) tracklet.get("track_id")).intValue();
                if (!CollectionUtils.isEmpty(associations)) {
                    bodyTrackId = ((Number) associations.get(0).get("track_id")).intValue();
                }
            } else {
                bodyTrackId = ((Number) tracklet.get("track_id")).intValue();
                if (!CollectionUtils.isEmpty(associations)) {
                    faceTrackId = ((Number) associations.get(0).get("track_id")).intValue();
                }
            }
            log.info(">>> [shieldFace] tracklets Start. associations is not empty = {}. source_id={},bodyTrackId={}," +
                            "tracklet-confidence={},tracklet-aligner_confidence={},label={}," +
                            "tracklet-status={},faceTrackId={},tracklet-integrate_quality={},tracklet-match_status={}",
                    !CollectionUtils.isEmpty(associations), deviceId, bodyTrackId,
                    tracklet.get("confidence"), tracklet.get("aligner_confidence"), label,
                    tracklet.get("status"), faceTrackId, tracklet.get("integrate_quality"), tracklet.get("match_status"));

            if (Utils.instance.dropFace == 0 && label == 221488 ) {

                Pointer kesonTarget = Stream.iterate(0, i -> i + 1).limit(kesonTarget_size_validate).map(index -> KestrelApi.keson_get_array_item(kesonTargetsValidate, index)).filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "id")) == ((Number) tracklet.get("id")).longValue()).findAny().orElse(null);

                if (kesonTarget != null) {

                    String fileName = tracklet.get("source_id").toString() + "-" + track_id + "-" + tracklet.get("id").toString() + "-" + UUID.randomUUID().toString().substring(1, 6);

                    Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "scene_frame")).getValue();
                    String targetImagePath = FrameUtils.save_image_as_jpg(face, ImageUtils.newFileName("dropFace", fileName,annotatorName()));

                    tracklet.put("matchedTrackids", ongoingFaceBodyMatchMapNew.getOrDefault((source_id << 32) + track_id, null));
                    tracklet.put("pedeAttributess", ongoingAttributeMap.get((source_id << 32) + track_id));

                    ImageUtils.newFileNameJson("dropFaceJson", fileName, tracklet,annotatorName());


                    Mat sourceImage = null;
                    sourceImage = opencv_imgcodecs.imread(targetImagePath);

                    Map<String, Number> roiBox = (Map<String, Number>) tracklet.get("target_image_roi");
                    int left = roiBox.get("left").intValue();
                    int top = roiBox.get("top").intValue();
                    int width = roiBox.get("width").intValue();
                    int height = roiBox.get("height").intValue();


                    org.bytedeco.opencv.opencv_core.Rect rect = new org.bytedeco.opencv.opencv_core.Rect(Math.max(0, left), Math.max(0, top), width, height);
                    opencv_imgproc.rectangle(sourceImage, rect, new Scalar(0, 255, 255, 0), 1, opencv_imgproc.LINE_AA, 0);

                    org.bytedeco.opencv.opencv_core.Point org = new org.bytedeco.opencv.opencv_core.Point(rect.x() + rect.width(), rect.y() + rect.height());

                    opencv_imgproc.putText(sourceImage, tracklet.get("track_id").toString(), org, fontFace, fontScale, color, thickness, opencv_imgproc.LINE_AA, false);


                    String fileName1 = targetImagePath.substring(targetImagePath.lastIndexOf('/') + 1);
                    File path = new File(Utils.instance.savePath + "/opencv/" + Objects.requireNonNullElse(ImageUtils.dateFormatter_DAY.get().format(new Date()), "/") + annotatorName());
                    if (!path.exists()) path.mkdirs();
                    // 保存结果图片
                    opencv_imgcodecs.imwrite(path.getAbsolutePath() + "/" + fileName1 + "_opencv_target.jpg", sourceImage);


                }
            }

            if (!CollectionUtils.isEmpty(associations) && Utils.instance.shieldFaceSwitch == 0) {

                boolean checked = true;

                if (label == 37017) {
                    faceTrackId = ((Number) tracklet.get("track_id")).intValue();
                    bodyTrackId = ((Number) associations.get(0).get("track_id")).intValue();
                } else {
                    bodyTrackId = ((Number) tracklet.get("track_id")).intValue();
                    faceTrackId = ((Number) associations.get(0).get("track_id")).intValue();
                }

                log.info(">>> [shieldFace] tracklets associations is not empty={},source_id={},bodyTrackId={}," +
                                "tracklet.get(\"confidence\")={},tracklet.get(\"aligner_confidence\")={},label={}, " +
                                "tracklet.get(\"status\")={},faceTrackId={},tracklet.get(\"integrate_quality\")={},match_status={}",
                        !CollectionUtils.isEmpty(associations), deviceId,
                        bodyTrackId, tracklet.get("confidence"), tracklet.get("aligner_confidence"),
                        label, tracklet.get("status"), faceTrackId, tracklet.get("integrate_quality"), tracklet.get("match_status"));

                //有脸质量分过滤
                Number conf = (Number) tracklet.get("confidence");
                Number alignConf = (Number) tracklet.get("aligner_confidence");
                if (tracklet.containsKey("aligner_confidence")) {
                    log.info(">>> [shieldFace] aligner_confidence_check! source_id={}，bodyTrackId={},faceTrackId={}", deviceId, bodyTrackId, faceTrackId);
                }
                Number integrateQuality = (Number) tracklet.get("integrate_quality");
                //人脸才有align
                if (conf != null && alignConf != null) {
                    log.info(">>> [shieldFace] aligner_confidence checkFail! alignConf={}, threshold={} ,source_id={},bodyTrackId={},faceTrackId={} ", alignConf, threshold, deviceId, bodyTrackId, faceTrackId);


                    if (integrateQuality != null){
                        checked &= integrateQuality.floatValue() > Utils.instance.integrateQuality;

                        if (Utils.instance.logged) {
                            log.info(">>> [shieldFace] integrateQuality_remove, checkFail! integrateQuality.floatValue()= {},Utils.instance.integrateQuality={},source_id={},bodyTrackId={},faceTrackId={},checked={} ", integrateQuality.floatValue(), Utils.instance.integrateQuality, deviceId, bodyTrackId, faceTrackId, checked);
                        }
                    }

                    float quality = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "quality", Integer.MIN_VALUE)).floatValue();
                    Number qualityTarget = (Number) tracklet.get("quality");
                    if (qualityTarget != null && quality > Integer.MIN_VALUE) {

                        checked &= qualityTarget.floatValue() > quality;

                        if (Utils.instance.logged) {
                            log.info(">>> [shieldFace] quality_remove, checkFail! qualityTarget.floatValue()={},quality={} ,source_id={},bodyTrackId={},faceTrackId={} ,checked={}", qualityTarget.floatValue(), quality, source_id, bodyTrackId, faceTrackId, checked);
                        }

                        float qualitySum = conf.floatValue() * alignConf.floatValue();

                        checked &= qualitySum > quality;

                        if (Utils.instance.logged) {
                            log.info(">>> [shieldFace] aligner_confidence_remove, checkFail! qualitySum={}, threshold={},source_id={},bodyTrackId={},faceTrackId={},checked={} ", qualitySum, threshold, source_id, bodyTrackId, faceTrackId, checked);
                        }
                    }


                    float yaw = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "yaw", Integer.MIN_VALUE)).floatValue();
                    float pitch = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "pitch", Integer.MIN_VALUE)).floatValue();
                    float roll = ((Number) getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "roll", Integer.MIN_VALUE)).floatValue();


                    Number yawTarget = (Number) tracklet.get("yaw");
                    Number pitchTarget = (Number) tracklet.get("pitch");
                    Number rollTarget = (Number) tracklet.get("roll");



                    if (yawTarget != null && yaw > Integer.MIN_VALUE)
                        checked &= Math.abs(yawTarget.floatValue()) <= yaw;

                    if (pitchTarget != null && pitch > Integer.MIN_VALUE)
                        checked &= Math.abs(pitchTarget.floatValue()) <= pitch;

                    if (rollTarget != null && roll > Integer.MIN_VALUE)
                        checked &= Math.abs(rollTarget.floatValue()) <= roll;


                    TrackFace trackFace = TrackFace.builder()
                            .faceId((source_id << 32) + faceTrackId)
                            .integrateQuality(integrateQuality.floatValue())
                            .quality(qualityTarget.floatValue())
                            .yaw(yawTarget)
                            .pitch(pitchTarget)
                            .roll(rollTarget)
                            .checked(checked)
                            .build();

                    if (checked) {
                        if (Utils.instance.logged) {
                            log.info(">>> [shieldFace] headpose_remove checkFail!,source_id={},bodyTrackId={},{},{},{},checked={},{},{},{}", deviceId, bodyTrackId, yawTarget, pitchTarget, rollTarget, checked, yaw, pitch, roll);
                        }

                    } else {// false => add
                        if (Utils.instance.logged) {
                            log.info(">>> [shieldFace] headpose_add. source_id={},bodyTrackId={},{},{},{},checked={},{},{},{}", deviceId, bodyTrackId,yawTarget, pitchTarget, rollTarget,checked, yaw, pitch, roll);
                        }
                    }

                    ongoingFaceBodyMatchMapNew.put((source_id << 32) + bodyTrackId, trackFace);

                }
            }
        }

        /**
         * 最后处理最终选帧
         */
        Iterator<Map<String, Object>> its = tracklets.iterator();
        while (its.hasNext()) {
            Map<String, Object> target = its.next();
            int status = ((Number) target.get("status")).intValue();
            int label = ((Number) target.get("label")).intValue();
            int track_id = ((Number) target.get("track_id")).intValue();

            int motion_from_begin_y = 0;
            Map<String, Number> motion_from_begin = (Map<String, Number>) target.get("motion_from_begin");
            if (motion_from_begin != null && !motion_from_begin.isEmpty())
                motion_from_begin_y = motion_from_begin.get("y").intValue();

            boolean removed = true;
            int step = 0;

            List<Map<String, Object>> associations = (List<Map<String, Object>>) target.get("associations");

            if (label == 221488 && (status == TIMEOUT || status == TRACKING_FINISH)) {

                TrackFace matchedTrackid = ongoingFaceBodyMatchMapNew.get((source_id << 32) + track_id);
                List<Map<String, Object>> pedeAttributes = ongoingAttributeMap.get((source_id << 32) + track_id);

                Long matchedFaceTrackid = ongoingFaceBodyMatchMap.get((source_id << 32) + track_id);

                log.info(">>> [shieldFace] aligner_confidence_face motion_from_begin_y={}, pedeAttributes is not empty={}, threshold={}, source_id={},bodyTrackId={},matchedTrackid={},checkAngle(pedeAttributes)={},matchedFaceTrackid={},associations is not empty = {}",
                        motion_from_begin_y, CollectionUtils.isNotEmpty(pedeAttributes), threshold, deviceId, track_id, (matchedTrackid !=null)?JSON.toJSON(matchedTrackid): null, checkAngle(pedeAttributes), matchedFaceTrackid, !CollectionUtils.isEmpty(associations));

                if ( CollectionUtils.isNotEmpty(pedeAttributes)) {

                    if(matchedTrackid == null || (matchedTrackid !=null && !matchedTrackid.isChecked())){

                        if(matchedTrackid != null){
                            target.put("targetFace", matchedTrackid);
                        }
                        if (checkAngle(pedeAttributes)) {//角度判断
                            removed = false;
                            step = 1;
                            target.put("pedeAttributes", pedeAttributes);
                        }
                        //                    else if (motion_from_begin_y > 0) {//虽然角度不对，但是移动方向是靠近
                        //                        removed = false;
                        //                        step = 3;
                        //                    }
                        //mask
                        //                    Map<String,Object> attributeMask = (Map<String,Object>)((Map<String,Object>)target.get("attribute")).get("st_respirator_v2");
                        //                    if(checkAngle(pedeAttributes) && checkAttributeMask(attributeMask)){
                        //                        log.info("mask_remove{}", target);
                        //                        removed = true;
                        //                    }
                    } else {
                        if (Utils.instance.logged) {
                            log.info(">>> [shieldFace] matchedTrackid null remove target!  source_id={},track_id={}", source_id, track_id);
                        }
                    }

//                if(CollectionUtils.isEmpty(associations) && matchedFaceTrackid != null){
//                    step = 2;
//                    removed = true;
//                    log.info("[shieldFace] removed_step_2 {},{},{},{},{},{}", step, source_id, track_id, label, matchedFaceTrackid, CollectionUtils.isEmpty(associations));
//                }

                }
            }


//            Map<String, Object> roi = (Map<String, Object>)target.get("roi");
//            if(roi != null) {
//                int width = ((Number) roi.get("width")).intValue();
//                int height = ((Number) roi.get("height")).intValue();
//                boolean minSizeCheck = width >= minSize && height >= minSize;
//                if (!minSizeCheck) {
//                    log.info("minsize_remove{}", target);
//                    removed = true;
//                }
//            }
            if (!removed) {
                log.info(">>> [shieldFace] not removed step from step={},source_id={},track_id={},label{}", step, deviceId, track_id, label);
            }

            if (removed) {
                if (Utils.instance.logged) {
                    log.info("[shieldFace] removed step from step={},source_id={},track_id={},label{}", step, deviceId, track_id, label);
                }
                if (label == 221488 && (status == TIMEOUT || status == TRACKING_FINISH)) {
                    if (Utils.instance.dropFace == 0) {
                        //过滤的存图，方便查看
                        Pointer kesonTarget = Stream.iterate(0, i -> i + 1).limit(kesonTarget_size_validate).map(index -> KestrelApi.keson_get_array_item(kesonTargetsValidate, index)).filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "id")) == ((Number) target.get("id")).longValue()).findAny().orElse(null);

                        if (kesonTarget != null) {

                            String fileName = target.get("source_id").toString() + "-" + track_id + "-" + target.get("id").toString() + "-" + UUID.randomUUID().toString().substring(1, 6);

                            Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "scene_frame")).getValue();
                            String targetImagePath = FrameUtils.save_image_as_jpg(face, ImageUtils.newFileName("dropFace", fileName, annotatorName()));

                            target.put("matchedTrackids", ongoingFaceBodyMatchMapNew.getOrDefault((source_id << 32) + track_id, null));
                            target.put("pedeAttributess", ongoingAttributeMap.get((source_id << 32) + track_id));
                            target.put("checkAngle", checkAngle(ongoingAttributeMap.get((source_id << 32) + track_id)));

                            ImageUtils.newFileNameJson("dropFaceJson", fileName, target, annotatorName());


                            Mat sourceImage = null;
                            sourceImage = opencv_imgcodecs.imread(targetImagePath);

                            Map<String, Number> roiBox = (Map<String, Number>) target.get("target_image_roi");
                            int left = roiBox.get("left").intValue();
                            int top = roiBox.get("top").intValue();
                            int width = roiBox.get("width").intValue();
                            int height = roiBox.get("height").intValue();


                            org.bytedeco.opencv.opencv_core.Rect rect = new org.bytedeco.opencv.opencv_core.Rect(Math.max(0, left), Math.max(0, top), width, height);
                            opencv_imgproc.rectangle(sourceImage, rect, new Scalar(0, 255, 255, 0), 1, opencv_imgproc.LINE_AA, 0);

                            org.bytedeco.opencv.opencv_core.Point org = new org.bytedeco.opencv.opencv_core.Point(rect.x() + rect.width(), rect.y() + rect.height());

                            opencv_imgproc.putText(sourceImage, target.get("track_id").toString(), org, fontFace, fontScale, color, thickness, opencv_imgproc.LINE_AA, false);


                            String fileName1 = targetImagePath.substring(targetImagePath.lastIndexOf('/') + 1);
                            File path = new File(Utils.instance.savePath + "/opencv/" + Objects.requireNonNullElse(ImageUtils.dateFormatter_DAY.get().format(new Date()), "/") + annotatorName()) ;
                            if (!path.exists()) path.mkdirs();
                            // 保存结果图片
                            opencv_imgcodecs.imwrite(path.getAbsolutePath() + "/" + fileName + "_opencv_target.jpg", sourceImage);
                        }
                    }
                }

                its.remove();
            }

            //选帧完删除cache
            if (label == 221488 && (status == TIMEOUT || status == TRACKING_FINISH)) {
                ongoingFaceBodyMatchMapNew.remove((source_id << 32) + track_id);
                ongoingFaceBodyMatchMap.remove((source_id << 32) + track_id);
                ongoingAttributeMap.remove((source_id << 32) + track_id);
            }

        }

        List<Number> dropped_track_ids = targets.stream().map(target -> (Number) target.get("dropped_track_id")).filter(Objects::nonNull).collect(Collectors.toList());
        for (Number dropped_track_id : dropped_track_ids) {
            ongoingAttributeMap.remove((source_id << 32) + dropped_track_id.intValue());
            ongoingFaceBodyMatchMap.remove((source_id << 32) + dropped_track_id.intValue());
            ongoingFaceBodyMatchMapNew.remove((source_id << 32) + dropped_track_id.intValue());
        }


        return !tracklets.isEmpty();
    }

    private boolean checkAttributeMask(Map<String, Object> attributeMask) {

        float frot = ((Number) attributeMask.get("st_respirator_agnostic")).floatValue();
        float side = ((Number) attributeMask.get("st_respirator_without")).floatValue();
        float full = ((Number) attributeMask.get("st_respirator_full")).floatValue();
        return (full > side && full > frot);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult)) return;

        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();

        long source_id = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(1).getOrDefault("targets", List.of());

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 1), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();

        for (Map<String, Object> target : filter(modelResult.getModelRequest().getProcessor(), targets, "position")) {
            Map<String, Object> output = new HashMap<String, Object>();

            output.put("attributes", rawAttributeMapToOutput((Map<String, Map<String, Number>>) target.get("attribute")));
            output.put("label", target.get("label"));
            output.put("position", target.get("position"));
            output.put("confidence", target.get("confidence"));
            output.put("targetRoi", target.get("roi"));
            if (target.containsKey("aligner_confidence")) {
                output.put("aligner_confidence", target.get("aligner_confidence"));
            }
            if (target.containsKey("associations")) {
                output.put("associations", target.get("associations"));
            }
            if (target.containsKey("targetTrack")) {
                output.put("targetTrack", target.get("targetTrack"));
            }

            if (target.containsKey("match_status")) {
                output.put("match_status", target.get("match_status"));
                /// 2 由于缓存人脸达到上限强制输出当前最小track_id人脸，没有等到associations中关联的人体数据
                if (((Number) target.get("match_status")).intValue() == 2) {
                    continue;
                }
            }

            if (target.containsKey("pedeAttributes")) {
                output.put("pedeAttributes", target.get("pedeAttributes"));
            }
            if (target.containsKey("removed")) {
                output.put("removed", target.get("removed"));
            }
            if (target.containsKey("motion_from_begin")) {
                output.put("motion_from_begin", target.get("motion_from_begin"));
            }
            if (target.containsKey("integrate_quality")) {
                output.put("integrate_quality", target.get("integrate_quality"));
            }
            if (target.containsKey("track_id")) {
                output.put("track_id", target.get("track_id"));
            }
            if (target.containsKey("yaw") && target.containsKey("pitch")) {
                output.put("head_pose", Map.of("yaw", target.get("yaw"), "pitch", target.get("pitch"), "roll", target.get("roll")));
            }
            if (target.containsKey("status")) {
                output.put("status", target.get("status"));
            }
            if (target.containsKey("id")) {
                output.put("targetId", target.get("id"));
            }
            if (target.containsKey("targetFace")) {
                output.put("targetFace", target.get("targetFace"));
            }

            output.put("source_id", source_id);

            Pointer kesonTarget = Stream.iterate(0, i -> i + 1).limit(kesonTarget_size).map(index -> KestrelApi.keson_get_array_item(kesonTargets, index)).filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "id")) == ((Number) target.get("id")).longValue()).findAny().orElse(null);

            if (kesonTarget != null) {
                log.info("start save image");
//                PointerByReference targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
//                if (targetImage.getValue() != null) {
//                    String targetImagePath = FrameUtils.save_image_as_jpg(targetImage.getValue(), ImageUtils.newFile("shieldFace"));
//                    output.put("targetImage", targetImagePath);
//                }

                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "scene_frame"));
                if (sceneImage.getValue() != null) {
                    String sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()),processor.getImgSaveTag());
                    output.put("sceneImage", sceneImagePath);
                }
            }

            outputResult.add(output);
        }

        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
        modelResult.setOutputResult(outputResult);
    }

    @SuppressWarnings("unchecked")
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult)) return result;

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).getOrDefault("targets", List.of());
        for (Map<String, Object> target : targets) {
            int label = ((Number) target.getOrDefault("label", -1)).intValue();
            int trackId = ((Number) target.getOrDefault("track_id", -1)).intValue();
            if (trackId < 0) continue;

            if (label == 221488) {
                long sourceId = ((Number) target.get("source_id")).longValue();
                Map<String, Integer> roi = (Map<String, Integer>) target.get("roi");

                Long matchedId = ongoingFaceBodyMatchMap.get((sourceId << 32) + trackId);

                String trackIdTxt = null;
                Map<String, Map<String, Number>> targetAttr = (Map<String, Map<String, Number>>) target.get("attribute");
                if (targetAttr != null && !targetAttr.isEmpty()) {
                    for (Entry<String, Map<String, Number>> entry : targetAttr.entrySet()) {
                        try {
                            if (entry.getKey().equals("st_pedestrian_angle")) {
                                trackIdTxt = entry.getValue().toString();
                                break;
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                if (matchedId == null) {
                    result.add(Rect.builder().processor(annotatorName()).text("NO-" + trackIdTxt).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());
                } else {
                    result.add(Rect.builder().processor(annotatorName()).text("YE-EEEEEEE" + trackId).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());
                }
            } else {
                Map<String, Integer> roi = (Map<String, Integer>) target.get("roi");
                result.add(Rect.builder().processor(annotatorName()).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());
            }
        }

        Processor processor = modelResult.getModelRequest().getProcessor();

        for (Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for (int index = 0; index < roi.length - 1; index++) {
                result.add(Line.builder().from(new int[]{roi[index][0], roi[index][1]}).to(new int[]{roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().from(new int[]{roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[]{roi[0][0], roi[0][1]}).build());
        }

        return result;
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("Shield Face start context [" + contextId + "].");
    }

    private void stopContext(String deviceId, long contextId) {
        Iterator<Entry<Long, Long>> its1 = ongoingFaceBodyMatchMap.entrySet().iterator();
        Iterator<Entry<Long, List<Map<String, Object>>>> its2 = ongoingAttributeMap.entrySet().iterator();
        Iterator<Entry<Long, TrackFace>> its3 = ongoingFaceBodyMatchMapNew.entrySet().iterator();

        while (its1.hasNext()) if (contextId == (its1.next().getKey()) >>> 32) its1.remove();

        while (its2.hasNext()) if (contextId == (its2.next().getKey()) >>> 32) its2.remove();

        while (its3.hasNext()) if (contextId == (its3.next().getKey()) >>> 32) its3.remove();

        synchronized (holders[0]) {
            holders[0].controlForRemoveSource(contextId);
        }

        synchronized (holders[1]) {
            holders[1].controlForRemoveSource(contextId);
        }

        deviceRoiMap.remove(contextId);

        log.info("Shield Face stop deviceId [" + deviceId + "].");
    }

    private List<Map<String, Object>> rawAttributeMapToOutput(Map<String, Map<String, Number>> rawAttributeMap) {
        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();

        for (Entry<String, Map<String, Number>> entry : rawAttributeMap.entrySet()) {
            try {
                Entry<String, Number> maxEntry = entry.getValue().entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
                outputResult.add(Map.of("key", entry.getKey(), "value", maxEntry.getKey(), "confidence", maxEntry.getValue()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return outputResult;
    }

    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> filter(Processor processor, List<Map<String, Object>> targets, String roiName) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        if (CollectionUtils.isEmpty(targets)) {
            return result;
        }
        if (processor.fetchPolygons().length <= 0) {
            return targets;
        }
        for (Map<String, Object> target : targets) {
            float confidence = ((Number) target.getOrDefault("confidence", 1.0f)).floatValue();
            if (confidence < Objects.requireNonNullElse(processor.getThreshold(), Float.MIN_VALUE)) continue;

            Map<String, Number> roi = (Map<String, Number>) target.get(roiName);
            if (roi == null) continue;

            int minSize = Objects.requireNonNullElse(processor.getMinSize(), Integer.MIN_VALUE);
            if (roi.get("height").intValue() < minSize || roi.get("width").intValue() < minSize) continue;

            int x = roi.get("left").intValue() + roi.get("width").intValue() / 2;
            int y = roi.get("top").intValue() + roi.get("height").intValue() / 2;

            boolean exist = false;

            for (Polygon polygon : processor.fetchPolygons()) {
                if (!polygon.contains(x, y)) {
                    log.info("checkRoiFilter{}", target);
                    continue;
                }

                exist = true;
                break;
            }

            if (!exist) continue;

            result.add(target);
        }

        return result;
    }

    private static final boolean checkAngle(List<Map<String, Object>> attributes) {
        if (CollectionUtils.isEmpty(attributes)) return false;

        boolean checked = attributes.stream().filter(attribute -> {
            try {
                float frot = ((Number) attribute.get("st_front")).floatValue();
                float side = ((Number) attribute.get("st_side")).floatValue();
                float back = ((Number) attribute.get("st_back")).floatValue();

                if ((frot > side && frot > back)) return true;
                else if ((side > frot && side > back && frot > back)) return true;

                return false;
            } catch (Exception e) {
                return false;
            }

        }).findAny().isPresent();

        return checked;
    }

    private static final boolean checkRespirator(List<Map<String, Object>> attributes) {
        if (CollectionUtils.isEmpty(attributes)) return false;

        return attributes.stream().map(attribute -> (Number) attribute.getOrDefault("color_type_none", -1)).filter(conf -> conf.floatValue() > 0.5f).findAny().isEmpty();
    }

    @SuppressWarnings("unchecked")
    private static Object getExtraValue(List<Map<String, Object>> extras, String key, Object def) {
        return Objects.requireNonNullElse(extras, List.of()).stream().filter(extra -> key.equals(((Map<String, Object>) extra).get("type"))).map(extra -> ((Map<String, Object>) extra).getOrDefault("value", def)).findAny().orElse(def);

    }

    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class TrackFace{

        private boolean checked;

        private long faceId;
        private float quality;

        private float integrateQuality;

        private Number yaw;

        private Number pitch;

        private Number roll;


    }

    private static final int FACE = 37017;
    private static final int BODY = 221488;

    private static final int PERSON = 0;
    private static final int PASSER = 1;

    private static final int SELECT_NONE = 0;     // 未被选中输出
    private static final int QUICK_RESPONSE = 1;  // 先前没被筛选过 且跟踪时长达到了快速响应的筛选时间
    private static final int TIME_INTERVAL = 2;   // 距离上次筛选的时长达到了时间间隔周期
    private static final int HIGH_QUALITY = 3;    // 质量分数足够高
    private static final int TIMEOUT = 4;         // 跟踪超过最大跟踪时长
    private static final int TRACKING_FINISH = 5; // 跟踪结束
}