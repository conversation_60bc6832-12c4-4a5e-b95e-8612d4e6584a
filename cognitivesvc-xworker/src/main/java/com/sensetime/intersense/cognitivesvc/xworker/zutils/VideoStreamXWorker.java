package com.sensetime.intersense.cognitivesvc.xworker.zutils;

import java.nio.Buffer;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.DeviceStatusEntity;
import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.http.client.utils.DateUtils;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.ffmpeg.global.avutil;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.CvPoint;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.bytedeco.opencv.opencv_core.IplImage;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Size;
import org.bytedeco.opencv.opencv_imgproc.CvFont;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.ModelHandlerEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XSenseyexEventHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XStoreHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XSenseyexEventHandler.SenseyexRawFrame;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.ConfigAccessor;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.VideoRecorderAccessor;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.VideoRecorderAccessor.DensityMap;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.VideoRecorderAccessor.Drawing;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.VideoRecorderAccessor.Line;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.VideoRecorderAccessor.Rect;
import com.sun.jna.Pointer;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;

@Slf4j
public class VideoStreamXWorker extends VideoStream {

    @Setter
    private BaseOutput senseyex_raw_event_output;

    @Setter
    private BaseOutput device_status_event_output;
    
    @Getter
    protected final Monitor monitor = new Monitor();
    
    private Thread daemonThread;
    
    private Thread recorderThread;
    
    private volatile boolean recorderStoped;
    
    private final ApplicationContext context;
    
    private LinkedBlockingQueue<RecordItem> frameToRecord = new LinkedBlockingQueue<RecordItem>(5);
    
    private final Map<String, Long> switcherOnWorkerTicker = new HashMap<String, Long>();
    
    private final CountDownLatch latch = new CountDownLatch(1);
    
    private final Map<String, List<VideoFrame>> minBatchQueue = new HashMap<String, List<VideoFrame>>();
    
    private Map<String, List<Drawing>> currentDrawingsMap = Map.of();
    
    @Setter
    private XStoreHandler xStoreHandler;
    
    @Setter
    private boolean isApiCalled;
    /**
     * 处理帧次数
     */
    @Getter
    protected int handleIndex = 0;
    
    @Getter
    @Setter
    private volatile Map<String, Processor> highRateProcessors = Map.of();
    
    @Getter
    @Setter
    private volatile Map<String, Processor> lowRateProcessors = Map.of();
    
    static ConcurrentHashMap<String, Integer> colorIndexMap = new ConcurrentHashMap<String, Integer>();
    static int[][] ListColor = {{0, 0, 0}, {255, 0, 0}, {0, 255, 0}, {0, 0, 255}, {255, 0, 255}};//黑红绿蓝紫
    
    public VideoStreamXWorker(VideoStreamInfra device, boolean useGpuDecoder, boolean useMultiplex, ApplicationContext context) {
        super(device);
        this.useDeviceDecoder = useGpuDecoder && Initializer.isGpu();
        this.useMultiplexDecoder = useMultiplex;
        this.context = context;
    }
    
    private final Runnable daemonRunner = new Runnable() {
        private long lastExpire = 0;
        
        @Override
        public void run() {
            Initializer.bindDeviceOrNot();

            try {
                boolean keepRunning = true;

                long now = 0;
                now = System.currentTimeMillis();

                while (!stoped && keepRunning && frameIndex < maxLimitFrameIndex) {
                    boolean logged = Utils.instance.watchFrameTiktokLevel == -797;

                    keepRunning = handleNextFrame();
                    
                    if (logged){
                        if(frameIndex % 100 == 0) {
                            log.info("[VideoHandleLog] VideoXWorker whole [" + device.getDeviceId() + "] frameindex[" + frameIndex + "] cost[" + (System.currentTimeMillis() - now) + "]");
                            now = System.currentTimeMillis();
                        }
                    }

//                    if(log.isDebugEnabled()){
//                        if(frameIndex % 100 == 0) {
//                            log.debug("[VideoHandleLog] VideoXWorker whole [" + device.getDeviceId() + "] frameindex[" + frameIndex + "] handleIndex[" + handleIndex + "] cost[" + (System.currentTimeMillis() - now) + "]");
//                        }
//                    }

                }
            } finally {
                for (List<VideoFrame> queue : minBatchQueue.values())
                    for (VideoFrame frame : queue)
                        frame.close();
                
                latch.countDown();
                close();
                videoStatus = VideoStatus.EOF;
            }
        }
        
        private final boolean handleNextFrame() {
            long now = System.currentTimeMillis();
            boolean logged = Utils.instance.watchFrameTiktokLevel == -798;
            boolean loggedFrameCost = Utils.instance.watchFrameTiktokLevel == -321;

            if (logged)
                log.info("VideoXWorker step[0] [" + device.getDeviceId() + "]  cost[" + (System.currentTimeMillis() - now) + "]");
            
            boolean rtmpOn = isRtmpOn();
            boolean needHostFrame = rtmpOn, needDeviceFrame = false;
            /**
             开始跑每一帧的数据了
             */
            Map<String, Triple<Processor, AbstractXModelHandler, SenseyexRawFrame>> currentFrameProcessorMap = new HashMap<String, Triple<Processor, AbstractXModelHandler, SenseyexRawFrame>>();
            for (Processor processor : highRateProcessors.values()) {
                AbstractXModelHandler handler = (AbstractXModelHandler) xStoreHandler.getXDynamicHandlerMap().get(processor.getProcessor());
                if (handler == null || frameIndex % (Math.abs(processor.getInterval()) + 1) != 0)
                    continue;
                
                ModelHandlerEntity entity = handler.getHandlerEntity();
                needDeviceFrame |= entity.getCpuModelDup() == 0;
                needHostFrame |= entity.getCpuModelDup() > 0;
                
                Triple<Processor, AbstractXModelHandler, SenseyexRawFrame> triple = new MutableTriple<Processor, AbstractXModelHandler, SenseyexRawFrame>(processor, handler, null);
                currentFrameProcessorMap.put(processor.getProcessor(), triple);
            }
            
            List<Processor> lowRates = new ArrayList<Processor>();
            for (Processor processor : lowRateProcessors.values()) {
                long previousTime = switcherOnWorkerTicker.getOrDefault(processor.getProcessor(), 0L);
                boolean pass = now - previousTime > Objects.requireNonNullElse(processor.getInterval(), 1) * 1000;
                
                if (pass) {
                    switcherOnWorkerTicker.put(processor.getProcessor(), now);
                    needDeviceFrame = true;
                    lowRates.add(processor);
                }
            }
            
            if (logged)
                log.info("VideoXWorker step[1] [" + device.getDeviceId() + "]  cost[" + (System.currentTimeMillis() - now) + "]");

            long startPoint = System.currentTimeMillis();
            VideoGrabFrameStatusOutput oldVideoStatus = videoStatusOutput;

            try (VideoFrame videoFrame = grabberNextFrame(needHostFrame, needDeviceFrame)) {
                videoStatusOutput = VideoGrabFrameStatusOutput.OK;
                videoStatusDetail = "";
                lastErrorCheckTime = null;

                monitor.setVideoStatus(videoStatusOutput);
                monitor.setVideoStatusDetail(videoStatusDetail);
                monitor.setLastErrorCheckTime(lastErrorCheckTime);

                if(!videoStatusOutput.equals(oldVideoStatus)){
                    List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
                    DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
                            .did(device.getDeviceId())
                            .sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
                            .lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                            .processSts("0")
                            .build();
                    deviceStatusesList.add(deviceStatus);
                    DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
                            .eventAction("DeviceStatusSync")
                            .eventName("cognitive")
                            .data(deviceStatusesList)
                            .build();
                    try{
                        device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
                        log.info("device_status_event_output send output {}",deviceStatusEntity);
                    }catch (Exception ex){
                        log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
                    }
                }

                if (!isStreamRemote(device.getRtspSource()) && useJavaCapturedTime) {
                    long sleep = 1000 / device.getVideoRate() - lastExpire;
                    if (sleep > 0)
                        Thread.sleep(sleep);
                }
                
                if (logged)
                    log.info("VideoXWorker step[2] [" + device.getDeviceId() + "]  cost[" + (System.currentTimeMillis() - now) + "]");


                long frameCost = 0L;
                //if(loggedFrameCost){
                    frameCost = System.currentTimeMillis() - now;
                //}

                //monitor.getAndIncrementHandledPullFrame();

                boolean hasHandled = false;
                
                for (Triple<Processor, AbstractXModelHandler, SenseyexRawFrame> triple : currentFrameProcessorMap.values()) {
                    Processor currentProcessor = triple.getLeft();
                    XModelHandler handler = triple.getMiddle();
                    int minBatchSize = 1, minBatchStagger = 0;
                    
                    if (handler instanceof ConfigAccessor) {
                        minBatchSize = Objects.requireNonNullElse(((ConfigAccessor) handler).getMinBatchSize(), 1);
                        minBatchStagger = Objects.requireNonNullElse(((ConfigAccessor) handler).getMinBatchStagger(), 0);
                    }
                    
                    List<VideoFrame> buffer = minBatchQueue.get(currentProcessor.getProcessor());
                    if (buffer == null) {
                        buffer = new ArrayList<VideoFrame>(minBatchSize);
                        minBatchQueue.put(currentProcessor.getProcessor(), buffer);
                    }
                    if(videoFrame.getFrame() == null){
                        continue;
                    }
                    buffer.add(videoFrame.ref());
                    if (buffer.size() < minBatchSize)
                        continue;
                    
                    VideoFrame[] videoFrames = new VideoFrame[minBatchSize];
                    for (int index = 0; index < minBatchSize; index++)
                        videoFrames[index] = buffer.get(index);
                    
                    if (minBatchStagger <= 0)
                        buffer.clear();
                    else {
                        for (int index = 0; index < minBatchStagger; index++)
                            buffer.remove(0);
                        
                        for (int index = 0; index < buffer.size(); index++)
                            buffer.set(index, buffer.get(index).ref());
                    }

                    Map<String, Object> extra = new HashMap<>(Map.of("isApiCalled", isApiCalled));

                    if(frameCost > (Utils.instance.printHandleTime / 2)){
                        log.info("frameCostLong:{},{}", device.getDeviceId(), frameCost);
                    }
                    if(loggedFrameCost) {
                        extra.put("frameDecodeCost", frameCost);
                        extra.put("startFrameTime", startPoint);
                    }

                    SenseyexRawFrame entity = SenseyexRawFrame.builder()
                            .processor(currentProcessor)
                            .videoFrames(videoFrames)
                            .deviceInfra(device)
                            .latch(blocking(rtmpOn || (isApiCalled && handleIndex % 16 == 0), handler))//高频渲染流增加一个栅栏，用来同步
                            .extra(extra)
                            .build();
                    
                    boolean ret = XSenseyexEventHandler.highRateHandleMap.get(device.getDeviceId()).offer(entity);
                    if (!ret) {
                        if (logged)
                            log.info("VideoXWorker step[5] [" + device.getDeviceId() + "]  ret false, cost[" + (System.currentTimeMillis() - now) + "],[" + XSenseyexEventHandler.highRateHandleMap.containsKey(device.getDeviceId()) + "]");
                        
                        entity.setModelResult(null);
                        entity.close();
                        monitor.getAndIncrementFrameUnOffer();
                        continue;
                    } else {
                        hasHandled = true;
                        ((MutableTriple<Processor, AbstractXModelHandler, SenseyexRawFrame>) triple).setRight(entity);
                        monitor.getAndIncrementFrameHandled();
                    }
                }
                
                if (logged)
                    log.info("VideoXWorker step[3] [" + device.getDeviceId() + "]  cost[" + (System.currentTimeMillis() - now) + "]");
                
                if (hasHandled)
                    handleIndex++;
                
                if (CollectionUtils.isNotEmpty(lowRates))
                    xStoreHandler.sendSwitcherOnWorkerEvent(device.getDeviceId(), FrameUtils.ref_frame(videoFrame.getFrame()), now, lowRates);
                
                for (Triple<Processor, AbstractXModelHandler, SenseyexRawFrame> triple : currentFrameProcessorMap.values())
                    if (triple.getRight() != null && triple.getRight().getLatch() != null)
                        triple.getRight().getLatch().await();//如果有栅栏，就等待
                
                //渲染流开始渲染啦
                if (!rtmpOn) {
                    stopRecorder();
                } else {
                    startRecorder();
                    
                    Map<String, List<Drawing>> drawingsMap = new HashMap<String, List<Drawing>>(currentDrawingsMap);
                    for (Triple<Processor, AbstractXModelHandler, SenseyexRawFrame> triple : currentFrameProcessorMap.values()) {
                        if (triple.getRight() == null || triple.getRight().getModelResult() == null || !(triple.getMiddle() instanceof VideoRecorderAccessor))
                            continue;
                        
                        List<Drawing> drawings = new ArrayList<Drawing>();
                        try {
                            drawings.addAll(((VideoRecorderAccessor) triple.getMiddle()).draw(triple.getRight().getModelResult()));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        drawingsMap.put(triple.getLeft().getProcessor(), drawings);
                    }
                    
                    currentDrawingsMap = drawingsMap;
                    
                    Pointer refCpuFrame = FrameUtils.ref_frame(videoFrame.getCpuFrame());
                    boolean ret = frameToRecord.offer(RecordItem.builder().frame(refCpuFrame).drawingsMap(currentDrawingsMap).build());
                    if (!ret)
                        FrameUtils.batch_free_frame(refCpuFrame);
                }
                
                if (logged)
                    log.info("VideoXWorker step[4] [" + device.getDeviceId() + "]  cost[" + (System.currentTimeMillis() - now) + "]");
            } catch (Throwable e) {

                VideoStreamXWorker.this.frameIndex++;
                monitor.getAndIncrementFrameUnHandled();

                if(errorEofTime >= Utils.instance.videoStatusErrorTime){
                    log.warn("[VideoStatusChange] grabberNextFrameErr change videoStatus to error, errorEofTime msg: {},deviceId:{},{}", e.getMessage(), device.getDeviceId(),errorTime);
                    videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
                    videoStatusDetail = e.getMessage();
                    lastErrorCheckTime = new Date();
                }

                if(errorAgainTime >= Utils.instance.videoStatusErrorKestrelAgainTime){
                    log.warn("[VideoStatusChange] grabberNextFrameErr change videoStatus to error, errorAgainTime msg: {},deviceId:{},{}", e.getMessage(), device.getDeviceId(),errorAgainTime);
                    videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
                    videoStatusDetail = e.getMessage();
                    lastErrorCheckTime = new Date();
                }

                if(errorInternalTime >= Utils.instance.videoStatusErrorInternalTime){
                    log.warn("[VideoStatusChange] grabberNextFrameErr change videoStatus to error, errorInternalTime msg: {},deviceId:{},{}", e.getMessage(), device.getDeviceId(),errorInternalTime);
                    videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
                    videoStatusDetail = e.getMessage();
                    lastErrorCheckTime = new Date();
                }

                monitor.setVideoStatus(videoStatusOutput);
                monitor.setVideoStatusDetail(videoStatusDetail);
                monitor.setLastErrorCheckTime(lastErrorCheckTime);

                if(!videoStatusOutput.equals(oldVideoStatus)){
                    List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
                    DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
                            .did(device.getDeviceId())
                            .sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
                            .lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                            .processSts("0")
                            .build();
                    deviceStatusesList.add(deviceStatus);
                    DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
                            .eventAction("DeviceStatusSync")
                            .eventName("cognitive")
                            .data(deviceStatusesList)
                            .build();
                    try{
                        device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
                        log.info("device_status_event_output send output {}",deviceStatusEntity);
                    }catch (Exception ex){
                        log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
                    }
                }

                if("KPLUGIN_E_EOF".equals(e.getMessage())){
                    try { Thread.sleep(3000); } catch (InterruptedException ex) { }
                }
                if("KPLUGIN_E_INTERNAL".equals(e.getMessage())){
                    try { Thread.sleep(1000); } catch (InterruptedException ex) { }
                }
                if("KPLUGIN_E_UNKNOWN".equals(e.getMessage())){
                    try { Thread.sleep(1000); } catch (InterruptedException ex) { }
                }
                if("KPLUGIN_E_AGAIN".equals(e.getMessage())){
                    try { Thread.sleep(1000); } catch (InterruptedException ex) { }
                }

                log.error("[videoHandleLog] [stream] decoder frame err cause:VideoStreamXWorkerErr{},{}" , e.getMessage(), device.getRtspSource());
                if ("KPLUGIN_E_EOF".equals(e.getMessage())) {
                    //2.13-obk clear queue
                    //XSenseyexEventHandler.highRateHandleMap.get(device.getDeviceId()).clear();
                    if (!isStreamEndless(device.getRtspSource())) {
                        //sendEOFMsg();
                        try { Thread.sleep(50000); } catch (InterruptedException ex) { }
                        VideoStreamXWorker.this.videoStatus = VideoStatus.EOF;
                        return rtmpOn;
                    } else {
                        //todo
                        reopen();
                    }
                } else if (StringUtils.startsWith(e.getMessage(), "SOURCE_CHANGED")) {
                    log.info("[videoHandleLog] [stream] Changed the address of the stream,reopen device:{},errMsg:{}, errorTime:{}",
                            device.getRtspSource(), e.getMessage(), errorTime);
                    reopen();
                }else if (errorTime % Utils.instance.xStreamReopenErrorTime == 0)  {
                    log.info("[videoHandleLog] [stream] ErrorTime exceeded {} times, reopen device:{},errMsg:{}, errorTime:{}",
                            Utils.instance.xStreamReopenErrorTime,device.getRtspSource(), e.getMessage(), errorTime);
                    reopen();
                }
            } finally {
                lastExpire = System.currentTimeMillis() - now;

                //todo monitor.setVideoStatus(videoStatus);
//                monitor.setVideoStatus(videoStatusOutput);
//                monitor.setVideoStatusDetail(videoStatusDetail);
//                monitor.setLastErrorCheckTime(lastErrorCheckTime);
                if (frameIndex % 500 == 0) {
                    Iterator<Entry<String, List<VideoFrame>>> its = minBatchQueue.entrySet().iterator();
                    
                    while (its.hasNext()) {
                        Entry<String, List<VideoFrame>> entry = its.next();
                        if (xStoreHandler.getXDynamicHandlerMap().get(entry.getKey()) != null || CollectionUtils.isEmpty(entry.getValue()))
                            continue;
                        
                        for (VideoFrame frame : entry.getValue())
                            frame.close();
                        
                        its.remove();
                    }
                }
            }
            
            return true;
        }
    };

    private void sendEOFMsg() {

        try {
            List<Map<String, Object>> outputList = new ArrayList<>();
            Map<String, Object> outputObject = new HashMap<>();
            outputObject.put("pullFrameEof", device.getDeviceId()); // 假设device已经被正确定义
            outputList.add(outputObject);
            String outputString = JSON.toJSONString(outputList, SerializerFeature.DisableCircularReferenceDetect);

            long receivedTime = System.currentTimeMillis();
            KafkaXWorkerConvertor.KafkaXWorkerConvertorBuilder builder = KafkaXWorkerConvertor.builder()
                    .appName("pullFrameEof")
                    .objectType("")
                    .deviceId(device.getDeviceId())
                    .imageUrl("")
                    .value(outputString.replaceAll("\"", "\\\\\\\\\\\""))
                    .capturedTime(receivedTime)
                    .receivedTime(receivedTime)
                    .frameIndex(-1L)
                    .framePts(-1L);

            String messageStr = builder.build().generateXworkerResult();
            senseyex_raw_event_output.send(MessageBuilder.withPayload(messageStr).setHeader(KafkaHeaders.KEY, device.getDeviceId().getBytes()).build());
            log.info("device_raw_event_output_eof:{}", device.getDeviceId());
        } catch (Exception ex) {
            log.warn("sendEOFMsg send status failed:{},{}", ex.getMessage(),device.getDeviceId());
        }
    }

    private void reopen() {
        try {
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e2) {
            }
            log.info("[videoHandleLog] [stream] video is reopening [" + device.getDeviceId() + "].stop:" + stoped);
            
            if (stoped)
                return;
            
            VideoStreamXWorker.this.frameIndex = 0;
            VideoStreamXWorker.this.errorTime = 0;
            VideoStreamXWorker.this.errorAgainTime = 0;
            VideoStreamXWorker.this.errorInternalTime = 0;
        
            try {
                for (Processor processor : highRateProcessors.values())
                    context.publishEvent(new XworkerStreamClosedEvent(device.getDeviceId(), processor.getProcessor(), XworkerStreamClosedEvent.REOPEN));
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e2) {
            }
            close();
            
            for (List<VideoFrame> queue : minBatchQueue.values())
                for (VideoFrame frame : queue)
                    frame.close();
            
            minBatchQueue.clear();
            
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e2) {
            }
            if (stoped)
                return;

            try {
                log.info("[VideoHandleLog] video reopen wait time {} s", Utils.instance.streamReopenSleepTime );
                Thread.sleep(Utils.instance.streamReopenSleepTime * 1000);
            } catch (InterruptedException e2) {
            }

            open();


            try {
                for (Processor processor : highRateProcessors.values())
                    context.publishEvent(new XworkerStreamStartedEvent(device, processor.getProcessor()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e2) {
            }
            
        } catch (Throwable e1) {
            log.error("[videoHandleLog] [stream] video fail to reopen [" + device.getDeviceId() + "].");
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e2) {
            }
        }
    }
    
    private final Runnable recordRunner = new Runnable() {
        private final ConcurrentHashMap<String, CvScalar> colors = new ConcurrentHashMap<String, CvScalar>();
        
        private final CvFont titleFont = opencv_imgproc.cvFont(2, 2);

        private final CvFont textFont = opencv_imgproc.cvFont(1.5, 2);
        
        @Override
        public void run() {
            int frameRate = device.getVideoRate() == null ? 25 : device.getVideoRate();
            FFmpegFrameRecorder recorder = null;
            
            while (!recorderStoped && !stoped) {
                try {
                    recorder = new FFmpegFrameRecorder(device.getRtmpDestination(), device.getRtspWidth(), device.getRtspHeight(), 0);
                    recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
                    recorder.setFormat("flv");
                    recorder.setVideoBitrate(10000000);
                    recorder.setFrameRate(frameRate);
                    recorder.setInterleaved(false);//设置是否交错存储，默认为 true，表示交错存储。
                    recorder.setVideoOption("crf", Integer.toString(frameRate));
                    recorder.setPixelFormat(0);//编码格式 0 表示原始图像格式。
                    recorder.setGopSize(frameRate);//关键帧间隔  0 表示由编码器自动选择关键帧间隔
                    
                    if (StringUtils.isNotBlank(device.getRtmpOption())) {
                        for (String option : device.getRtmpOption().split(",")) {
                            String[] param = option.split(":");
                            recorder.setVideoOption(param[0], param[1]);
                        }
                    }
                    
                    recorder.start();
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("[videoHandleLog] [stream] can not open deviceid is [" + device.getDeviceId() + "] rtmp [" + device.getRtmpDestination() + "], please check.");
                    try {
                        Thread.sleep(3000);
                    } catch (Exception e1) {
                    }
                }
            }
            
            while (!recorderStoped && !stoped) {
                RecordItem item = null;
                Pointer bufferedFrame = null;
                
                OpenCVFrameConverter.ToIplImage toipl = new OpenCVFrameConverter.ToIplImage();
                OpenCVFrameConverter.ToMat tomat = new OpenCVFrameConverter.ToMat();
                try {
                    item = frameToRecord.poll(1, TimeUnit.SECONDS);
                    if (item == null || item.frame == null)
                        continue;
                    
                    bufferedFrame = FrameUtils.ref_or_cvtcolor_frame(item.frame, KestrelApi.KESTREL_VIDEO_RGB);
                    
                    Frame frame = new Frame();
                    frame.imageWidth = KestrelApi.kestrel_frame_video_width(bufferedFrame);
                    frame.imageHeight = KestrelApi.kestrel_frame_video_height(bufferedFrame);
                    frame.imageStride = KestrelApi.kestrel_frame_video_stride(bufferedFrame, 0);
                    frame.image = new Buffer[]{KestrelApi.kestrel_frame_plane(bufferedFrame, 0).getByteBuffer(0, frame.imageWidth * frame.imageHeight * 3)};
                    frame.imageChannels = 3;
                    frame.imageDepth = 8;
                    
                    IplImage iplImage = toipl.convert(frame);
                    Mat imgSrc = tomat.convert(frame);
                    
                    Iterator<Entry<String, List<Drawing>>> its = item.getDrawingsMap().entrySet().iterator();
                    for (int index = 0; its.hasNext(); index++) {
                        Entry<String, List<Drawing>> entry = its.next();
                        
                        String processor = entry.getKey();
                        CvScalar color = colors.get(processor);
                        if (color == null) {
                            colors.put(processor, opencv_core.CV_RGB(ThreadLocalRandom.current().nextInt(0, 256), ThreadLocalRandom.current().nextInt(0, 256), ThreadLocalRandom.current().nextInt(0, 256)));
                            color = colors.get(processor);
                        }
                        
                        opencv_imgproc.cvPutText(iplImage, processor, opencv_core.cvPoint(0, (index + 1) * 27), titleFont, color);
                        for (Drawing drawing : List.copyOf(entry.getValue())) {
                            try {
                                if (drawing instanceof Rect) {
                                    Rect rect = (Rect)drawing;
                                    // color from Rect
                                    color = rect.getColor() != null ? rect.getColor() : color;
									//add color device
									int colorIndex = getColorSingle(rect.getDeviceID());
									if(colorIndex > 0){
										 int[] listColor = ListColor[colorIndex -1];
										 color = (listColor.length > 0) ? opencv_core.CV_RGB(listColor[0], listColor[1], listColor[2]) : color;
									}
									CvPoint point = opencv_core.cvPoint(rect.getLeft(), rect.getTop());
									opencv_imgproc.cvRectangle(iplImage, point, opencv_core.cvPoint(rect.getLeft() + rect.getWidth(), rect.getTop() + rect.getHeight()), color, rect.getThickness() > 0 ?rect.getThickness(): 2, 8 ,0);
									if(StringUtils.isNotBlank(rect.getText())) {
                                        CvFont _textFont = rect.getTextFont() != null?rect.getTextFont(): textFont;
                                        opencv_imgproc.cvPutText(iplImage, rect.getText(), point, _textFont, color);
                                    }
								}else if(drawing instanceof Line) {
									Line line = (Line)drawing;
                                    color = line.getColor() != null ? line.getColor() : color;
                                    if (line.getThickness() > 1)
                                        opencv_imgproc.cvLine(iplImage, line.getFrom(), line.getTo(), color, line.getThickness(), line.getType(), line.getShift());
                                    else
                                        opencv_imgproc.cvLine(iplImage, line.getFrom(), line.getTo(), color);
                                } else if (drawing instanceof DensityMap) {
                                    DensityMap densityMap = (DensityMap) drawing;
                                    double[][] density = densityMap.getDensity();
                                    
                                    Mat[] mats = new Mat[]{Mat.zeros(densityMap.getHeight(), densityMap.getWidth(), opencv_core.CV_8UC1).asMat(), new Mat(), new Mat()};
                                    for (int w = 0; w < densityMap.getWidth(); w++)
                                        for (int h = 0; h < densityMap.getHeight(); h++)
                                            mats[0].data().put((long) (h * densityMap.getWidth() + w), density[h][w] > 0.05 ? (byte) (160 - (int) (Math.min(density[h][w], 1.0) * 160)) : (byte) 255);
                                    
                                    opencv_imgproc.applyColorMap(mats[0], mats[1], densityMap.getColorMap());
                                    opencv_imgproc.resize(mats[1], mats[2], new Size(frame.imageWidth, frame.imageHeight));
                                    opencv_core.addWeighted(imgSrc, 1.0, mats[2], 0.25, 0.0, imgSrc);
                                    
                                    for (Mat mat : mats)
                                        mat.close();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    
                    recorder.record(frame, avutil.AV_PIX_FMT_RGB24);
                    iplImage.close();
                    imgSrc.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    break;
                } finally {
                    FrameUtils.batch_free_frame(bufferedFrame);
                    if (item != null)
                        FrameUtils.batch_free_frame(item.frame);
                }
            }
            
            try {
                for (RecordItem record = frameToRecord.poll(); record != null; record = frameToRecord.poll())
                    FrameUtils.batch_free_frame(record.frame);
                
                if (recorder != null)
                    recorder.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };
    
    private void startRecorder() {
        recorderStoped = false;
        
        if (recorderThread == null || !recorderThread.isAlive()) {
            recorderThread = new Thread(Utils.cogGroup, recordRunner);
            recorderThread.setName("[" + device.getDeviceId() + "] xworkerRecorder");
            recorderThread.setDaemon(true);
            recorderThread.start();
        }
    }
    
    private void stopRecorder() {
        recorderStoped = true;
        recorderThread = null;
    }
    
    @Override
    public void start() {
        try{
            Initializer.bindDeviceOrNot();

            if (daemonThread == null || !daemonThread.isAlive()) {
                log.info("[videoHandleLog] [stream] xworker stream is starting deviceId[" + device.getDeviceId() + "].");

                LinkedBlockingQueue<SenseyexRawFrame> queue = new LinkedBlockingQueue<SenseyexRawFrame>(16);
                XSenseyexEventHandler.highRateHandleMap.put(device.getDeviceId(), queue);

                open();

                daemonThread = new Thread(Utils.cogGroup, daemonRunner);
                daemonThread.setName("[" + device.getDeviceId() + "] xworkerDaemon");
                daemonThread.setDaemon(true);
                daemonThread.start();
                videoStatusOutput = VideoGrabFrameStatusOutput.OK;
                List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
                DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
                        .did(device.getDeviceId())
                        .sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
                        .lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                        .processSts("0")
                        .build();
                deviceStatusesList.add(deviceStatus);
                DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
                        .eventAction("DeviceStatusSync")
                        .eventName("cognitive")
                        .data(deviceStatusesList)
                        .build();
                try{
                    device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
                    log.info("device_status_event_output send output {}",deviceStatusEntity);
                }catch (Exception ex){
                    log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
                }
            }
        }catch (Exception e){
            videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
            List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
            DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
                    .did(device.getDeviceId())
                    .sts("1")
                    .lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                    .processSts("0")
                    .build();
            deviceStatusesList.add(deviceStatus);
            DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
                    .eventAction("DeviceStatusSync")
                    .eventName("cognitive")
                    .data(deviceStatusesList)
                    .build();
            try{
                device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
                log.info("device_status_event_output send output {}",deviceStatusEntity);
            }catch (Exception ex){
                log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
            }
            throw e;
        }
    }
    
    @Override
    public void stop() {
        try{
            Initializer.bindDeviceOrNot();

            stoped = true;

            try {
                if (!isStreamEndless(device.getRtspSource())) {
                    sendEOFMsg();
                }
                boolean ret = latch.await(30, TimeUnit.SECONDS);
                if (!ret) {
                    new RuntimeException("closing X stream[" + device.getDeviceId() + "] cost more than 30 second, please check or ingore.").printStackTrace();
                    daemonThread.interrupt();
                }

                log.info("[videoHandleLog] [stream] xworker stream is closing deviceId[" + device.getDeviceId() + "].");


            } catch (Exception e) {
            }

            close();

            stopRecorder();

            LinkedBlockingQueue<SenseyexRawFrame> queue = XSenseyexEventHandler.highRateHandleMap.remove(device.getDeviceId());
            for (SenseyexRawFrame frame = queue.poll(); frame != null; frame = queue.poll())
                frame.close();

            List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
            DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
                    .did(device.getDeviceId())
                    .sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
                    .lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                    .processSts("1")
                    .build();
            deviceStatusesList.add(deviceStatus);
            DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
                    .eventAction("DeviceStatusSync")
                    .eventName("cognitive")
                    .data(deviceStatusesList)
                    .build();
            try{
                device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
                log.info("device_status_event_output send output {}",deviceStatusEntity);
            }catch (Exception ex){
                log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
            }
        }catch (Exception e){
            videoStatusOutput = VideoGrabFrameStatusOutput.ERROR;
            List<DeviceStatusEntity.DeviceStatus> deviceStatusesList = new ArrayList<>();
            DeviceStatusEntity.DeviceStatus deviceStatus = DeviceStatusEntity.DeviceStatus.builder()
                    .did(device.getDeviceId())
                    .sts(videoStatusOutput.equals(VideoGrabFrameStatusOutput.OK)? "0" : "1")
                    .lastChkTs(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                    .processSts("1")
                    .build();
            deviceStatusesList.add(deviceStatus);
            DeviceStatusEntity deviceStatusEntity = DeviceStatusEntity.builder()
                    .eventAction("DeviceStatusSync")
                    .eventName("cognitive")
                    .data(deviceStatusesList)
                    .build();
            try{
                device_status_event_output.send(MessageBuilder.withPayload(JSON.toJSONString(deviceStatusEntity)).build());
                log.info("device_status_event_output send output {}",deviceStatusEntity);
            }catch (Exception ex){
                log.warn("device_status_event_output send status failed:{},deviceStatusMsg:{}",ex.getMessage(),deviceStatusEntity);
            }
            if (!isStreamEndless(device.getRtspSource()) && !stoped) {
                sendEOFMsg();
            }
        }
    }
    
    private boolean isRtmpOn() {
        return device != null && StringUtils.isNotBlank(device.getRtmpDestination()) && "0".equals(device.getRtmpOn());
    }
    
    private CountDownLatch blocking(boolean rtmpOn, XModelHandler handler) {
        if (rtmpOn || handler instanceof ConfigAccessor && Boolean.TRUE.equals(((ConfigAccessor) handler).getBlocking()))
            return new CountDownLatch(1);
        else
            return null;
    }
    
    @Getter
    @Builder
    @Accessors(chain = true)
    private static final class RecordItem {
        private Pointer frame;
        private Map<String, List<Drawing>> drawingsMap;
    }
    
    public static class Monitor {
        public ConcurrentLinkedDeque<AtomicLong> expired;
        
        public ConcurrentLinkedDeque<AtomicInteger> handled;
        public ConcurrentLinkedDeque<AtomicInteger> handledFramePull;
        public ConcurrentLinkedDeque<AtomicInteger> unhandled;
        public ConcurrentLinkedDeque<AtomicInteger> sended;
        public AtomicInteger handledTotal;
        public AtomicInteger handledFramePullTotal;
        public AtomicInteger unhandledTotal;
        public AtomicInteger sendedTotal;
        public VideoGrabFrameStatusOutput videoStatus;
        public AtomicInteger frameHandledTotal;
        public AtomicInteger frameUnhandledTotal;
        public AtomicInteger frameUnOfferTotal;

        public String getVideoStatusDetail() {
            return videoStatusDetail;
        }

        public void setVideoStatusDetail(String videoStatusDetail) {
            this.videoStatusDetail = videoStatusDetail;
        }

        public String videoStatusDetail;

        public Date getLastErrorCheckTime() {
            return lastErrorCheckTime;
        }

        public void setLastErrorCheckTime(Date lastErrorCheckTime) {
            this.lastErrorCheckTime = lastErrorCheckTime;
        }

        public Date lastErrorCheckTime;

        
        public Monitor() {
            handled = new ConcurrentLinkedDeque<AtomicInteger>();
            handledFramePull = new ConcurrentLinkedDeque<AtomicInteger>();
            unhandled = new ConcurrentLinkedDeque<AtomicInteger>();
            expired = new ConcurrentLinkedDeque<AtomicLong>();
            sended = new ConcurrentLinkedDeque<AtomicInteger>();
            videoStatus = VideoGrabFrameStatusOutput.OK;
            
            for (int index = 0; index < 12; index++) {
                handled.offer(new AtomicInteger());
                handledFramePull.offer(new AtomicInteger());
                unhandled.offer(new AtomicInteger());
                sended.offer(new AtomicInteger());
                expired.offer(new AtomicLong());
            }
            
            handledTotal = new AtomicInteger();
            handledFramePullTotal= new AtomicInteger();
            unhandledTotal = new AtomicInteger();
            sendedTotal = new AtomicInteger();

            frameHandledTotal = new AtomicInteger();
            frameUnhandledTotal = new AtomicInteger();
            frameUnOfferTotal = new AtomicInteger();
        }

        public void setVideoStatus(VideoGrabFrameStatusOutput videoStatus) {
            this.videoStatus = videoStatus;
        }

        public VideoGrabFrameStatusOutput getVideoStatus() {
            return this.videoStatus;
        }
        
        public int getAndIncrementHandled() {
            handledTotal.getAndIncrement();
            return handled.peekFirst().getAndIncrement();
        }

        public int getAndIncrementHandledPullFrame() {
            handledFramePullTotal.getAndIncrement();
            return handledFramePull.peekFirst().getAndIncrement();
        }

        public int getAndIncrementUnHandled() {
            unhandledTotal.getAndIncrement();
            return unhandled.peekFirst().getAndIncrement();
        }
        
        public int getAndIncrementSended() {
            sendedTotal.getAndIncrement();
            return sended.peekFirst().getAndIncrement();
        }
        
        public int getHandledCount() {
            return handled.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
        }
        public int getFramePullount() {
            return handledFramePull.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
        }
        
        public int getUnhandledCount() {
            return unhandled.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
        }
        
        public int getSendedCount() {
            return sended.stream().map(AtomicInteger::get).reduce(Integer::sum).orElse(0);
        }
        
        public long getExpired() {
            return expired.stream().map(AtomicLong::get).reduce(Long::sum).orElse(0L);
        }

        public void getAndIncrementFrameHandled() {
             frameHandledTotal.getAndIncrement();
        }
        public void getAndIncrementFrameUnHandled() {
            frameUnhandledTotal.getAndIncrement();
        }
        public void getAndIncrementFrameUnOffer() {
            frameUnOfferTotal.getAndIncrement();
        }

        public void reset() {
            handled.pollLast();
            handled.offerFirst(new AtomicInteger());

            handledFramePull.pollLast();
            handledFramePull.offerFirst(new AtomicInteger());
            
            unhandled.pollLast();
            unhandled.offerFirst(new AtomicInteger());
            
            sended.pollLast();
            sended.offerFirst(new AtomicInteger());
            
            expired.pollLast();
            expired.offerFirst(new AtomicLong());
        }
    }
    
    public static int getColorSingle(String deviceID) {
        if (deviceID == null) {
            return 0;
        }
        if (deviceID.isEmpty()) {
            return 0;
        }
        if (colorIndexMap.isEmpty()) {
            colorIndexMap.put(deviceID, 0);
            return 1;
        }
        if (!colorIndexMap.containsKey(deviceID)) {
            colorIndexMap.put(deviceID, 0);
            return 1;
        }
        if (colorIndexMap.get(deviceID) > 0 && colorIndexMap.get(deviceID) % 5 == 0) {
            colorIndexMap.put(deviceID, 1);
        } else {
            colorIndexMap.put(deviceID, colorIndexMap.get(deviceID) + 1);
        }
        //System.out.println(colorIndexMap);
        return colorIndexMap.get(deviceID);
        
    }
}
