package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_area2d_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Memory;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

public class EssosCarplate extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    /**
    [{
        "processor": "essosCarplate",
        "extras": [{
            "type": "buffer",
            "bufferSize": 1,
            "bufferExpire": 10,
            "plateMinLength": 5
        }, {
            "type": "spliter",
            "width": 150,
            "height": 100,
            "overtype": 3,
            "leftPercentage": 80,
            "topPercentage": 0,
            "rightPercentage": 80,
            "bottomPercentage": 100,
            "overrideRate": 50
        }]
    }]
     */
    //<deviceId, List<Pair<plate, time>>>
    protected ConcurrentHashMap<String, LinkedBlockingQueue<Pair<String, Long>>> bufferCounter = new ConcurrentHashMap<String, LinkedBlockingQueue<Pair<String, Long>>>();
    
    private static final String[] DETECTED = new String[] {"detected"};
    
    @Getter
    protected boolean lazyInit = true;    
    
    @Getter
    protected final Integer interval = 7;//高频流的话 检一跳七
    
    private static final Integer PLATEMINLENGTH = null; //如果不空 则车牌最小字符数 
    
    private static final Float TEXTDETECTIONSHRINK = null; //如果不空 则不进行文字检测 直接拿车牌的框缩小该数值之后的框进行文字识别
    
    private static final boolean HARPYFIRST = false; //是否先用检测模型检测一下是否有车 如果有车 就把车的框框也当成ROI
    
    /** 分割参数 {status, widthsplit, heightsplit, overtype, leftPercentage, topPercentage, rightPercentage, bottomPercentage, overrideRate}
     *  status == 0 关闭， status != 0 开启
     *  overtype=0代表无覆盖紧挨网格，overtype=1代表width有覆盖叠加网格，overtype=2代表height有覆盖叠加网格，overtype=3代表width和height都覆盖网格
     *  4个Percentage代表了split的区域，按整个帧中心点开始算，向left方向的占比，向top方向占比。。。。
     *  overtype!=0情况下，overrideRate表示叠加比率，数字越小重叠率越高，运算量越大
     */
    private static final int[] SPLITER = new int[] {0, 200, 200, 0, 80, 0, 80, 100, 50};
    
    @SuppressWarnings({ "unchecked"})
    @Override    
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {        
        if (ArrayUtils.isEmpty(holders) || status != 0)
            return new PointerByReference[1];

        List<Indexer> indexer = new ArrayList<Indexer>();
        int harpyRoiCount = 0;
        
        if(HARPYFIRST) {
            Pointer[] gpuFrames = new Pointer[handlingList.size()];
            for(int index = 0; index < handlingList.size(); index ++) 
                gpuFrames[index] = handlingList.get(index).getModelRequest().getVideoFrames()[0].getGpuFrame();
            
            PointerByReference param_keson = new PointerByReference(KesonUtils.frameToKeson(gpuFrames));
            PointerByReference tempKesonResult = execute(holders[5], param_keson.getValue());
            Map<String, Object> harpyResult = (Map<String, Object>)KesonUtils.kesonToJson(tempKesonResult.getValue());
            KesonUtils.kesonDeepDelete(param_keson, tempKesonResult);
            
            List<Map<String, Object>> harpyList = (List<Map<String, Object>>)harpyResult.getOrDefault("targets", List.of());
            for(Map<String, Object> harpy : harpyList) {
                if(1420 != ((Number)harpy.get("label")).intValue())
                    continue;
                
                int image_id = (int)harpy.get("image_id");
                int id = Integer.MAX_VALUE - (int)harpy.get("id");
                Map<String, Number> roi = (Map<String, Number>)harpy.get("roi");
                int left = roi.get("left").intValue();
                int top = roi.get("top").intValue();
                int width = roi.get("width").intValue();
                int height = roi.get("height").intValue();
                
                indexer.add(Indexer.builder()
                                   .image_id(image_id)
                                   .id(id)
                                   .roiImage(FrameUtils.roi_frame(gpuFrames[image_id], left, top, width, height))
                                   .roi(new Roi(left, top, width, height)).build());
                
                harpyRoiCount ++;
            }
        }
        
        for(int index = 0; index < handlingList.size(); index ++) {
            Processor processor = handlingList.get(index).getModelRequest().getProcessor();
            Pointer frame = handlingList.get(index).getModelRequest().getVideoFrames()[0].getGpuFrame();

			int width  = KestrelApi.kestrel_frame_video_width(frame);
			int height = KestrelApi.kestrel_frame_video_height(frame);
			
            Integer[][][] rois = processor.getRoi();
            if(ArrayUtils.isEmpty(rois)) {
                int[] spliter = ((List<Map<String, Object>>)Objects.requireNonNullElse(processor.getExtras(), List.of())).stream()
                                       .filter(item -> "spliter".equals(item.get("type")))
                                       .findAny()
                                       .map(map -> new int[] {1, (int)map.get("width"), (int)map.get("height"), (int)map.get("overtype"), (int)map.get("leftPercentage"), (int)map.get("topPercentage"), (int)map.get("rightPercentage"), (int)map.get("bottomPercentage"), (int)map.get("overrideRate")})
                                       .orElse(SPLITER);
                
                if(HARPYFIRST || spliter[0] == 0) 
                    rois = new Integer[][][] {new Integer[][] {new Integer[] {0, 0}, new Integer[] {width, 0}, new Integer[] {width, height}, new Integer[] {0, height}}};
                else {
                    rois = splitRoi(width, height, spliter);
                    handlingList.get(index).getModelRequest().getParameter().put("spliter", rois);
                }
            }
            
            for(int jndex = 0; jndex < rois.length; jndex ++) {
                int left = Integer.MAX_VALUE, top = Integer.MAX_VALUE, right = Integer.MIN_VALUE, bottom = Integer.MIN_VALUE;
                for(Integer[] dot : rois[jndex]) {
                    left    = Math.max(Math.min(left  , dot[0]), 0);
                    top     = Math.max(Math.min(top   , dot[1]), 0);
                    right   = Math.min(Math.max(right , dot[0]), width);
                    bottom  = Math.min(Math.max(bottom, dot[1]), height);
                }

                indexer.add(Indexer.builder()
                                   .image_id(index)
                                   .id(jndex)
                                   .roiImage(FrameUtils.roi_frame(frame, left, top, right - left, bottom - top))
                                   .roi(new Roi(left, top, right - left, bottom - top)).build());
            }
        }
        
        if(CollectionUtils.isEmpty(indexer))
            return new PointerByReference[1];
        
        PointerByReference[] plateResult = new PointerByReference[5];
            
        Pointer[] plateFrames = new Pointer[indexer.size()];
        for(int index = 0; index < indexer.size(); index ++)
            plateFrames[index] = indexer.get(index).getRoiImage();
        
        PointerByReference plate_param_keson = new PointerByReference(KesonUtils.frameToKeson(plateFrames));
        plateResult[0] = execute(holders[0], plate_param_keson.getValue());        
        plateResult[1] = execute(holders[1], plateResult[0].getValue());
        plateResult[2] = execute(holders[2], plateResult[0].getValue());
        
        handlingList.get(0).getModelRequest().getParameter().put("indexer", indexer);
        handlingList.get(0).getModelRequest().getParameter().put("plateFrames", plateFrames);
        handlingList.get(0).getModelRequest().getParameter().put("plate_param_keson", plate_param_keson);
        handlingList.get(0).getModelRequest().getParameter().put("harpyRoiCount", harpyRoiCount);
        
        return plateResult;
    }
    
    /** 将批量处理结果，整理后放到每个item中
     */
    @SuppressWarnings({ "unchecked", "unused"})
    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] output_kesons) {
        if(output_kesons[0] == null)
            return ;
        
        List<Indexer> indexer = (List<Indexer>)handlingList.get(0).getModelRequest().getParameter().remove("indexer");
        Pointer[] plateFrames = (Pointer[])handlingList.get(0).getModelRequest().getParameter().remove("plateFrames");
        PointerByReference plate_param_keson = (PointerByReference)handlingList.get(0).getModelRequest().getParameter().remove("plate_param_keson");
        int harpyRoiCount = (int)handlingList.get(0).getModelRequest().getParameter().remove("harpyRoiCount");
        
        PointerByReference[] plateResult = output_kesons;
        
        Map<String, Object>[] rois = new Map[indexer.size()];
        Map<String, Object>[] attributes = new Map[indexer.size()];
        Map<String, Object>[] contents = new Map[indexer.size()];
        
        
        Map<String, Object> attributesDetect = (Map<String, Object>)KesonUtils.kesonToJson(plateResult[2].getValue());
        List<Map<String, Object>> attributeTargets = (List<Map<String, Object>>)attributesDetect.get("targets");
        for(Map<String, Object> attributeTarget : attributeTargets) {
            int attribute_image_id = (int)attributeTarget.get("image_id");
            attributes[attribute_image_id] = (Map<String, Object>)attributeTarget.get("attribute");
        }
        
        Map<String, Object> pointsDetect = (Map<String, Object>)KesonUtils.kesonToJson(plateResult[1].getValue());
        List<Map<String, Object>> pointTargets = (List<Map<String, Object>>)pointsDetect.get("targets");
        
        if(pointTargets.size() > 0) {
            Pointer[] textFrames = new Pointer[indexer.size()];
            for(Map<String, Object> pointTarget : pointTargets) {
                int plate_image_id = (int)pointTarget.get("image_id");
                if(textFrames[plate_image_id] != null)
                    continue;
                
                int left = Integer.MAX_VALUE;
                int top = Integer.MAX_VALUE;
                int right = Integer.MIN_VALUE;
                int bottom = Integer.MIN_VALUE;
                
                List<Map<String, Object>> keyPoints = (List<Map<String, Object>>)pointTarget.getOrDefault("key_points", List.of());
                for(Map<String, Object> keyPoint : keyPoints) {
                    int x = ((Number)keyPoint.get("x")).intValue();
                    int y = ((Number)keyPoint.get("y")).intValue();

                    left   = Math.min(left  , x);
                    right  = Math.max(right , x);
                    top    = Math.min(top   , y);
                    bottom = Math.max(bottom, y);
                }
                
                Roi indexerRoi = indexer.get(plate_image_id).getRoi();
                left   += indexerRoi.x; 
                top    += indexerRoi.y; 
                right  += indexerRoi.x; 
                bottom += indexerRoi.y;
                
                if(HARPYFIRST && harpyRoiCount > 0 ) {
                    if(plate_image_id < harpyRoiCount) {//harpy成功的车牌需要在roi附近
                        boolean inSide = false;
                        for(int index = harpyRoiCount; index < indexer.size(); index ++) {
                            Roi roi = indexer.get(index).getRoi();
                            if((left >= roi.x || right <= roi.x + roi.width) && (top >= roi.y || bottom <= roi.y + roi.height)) {
                                inSide = true;
                                break;
                            }
                        }
                        
                        if(!inSide)
                            continue;
                    }else {//roi检测出来的车牌不能与harpy冲突
                        boolean inSide = false;
                        for(int index = 0; index < harpyRoiCount; index ++) {
                            Roi roi = indexer.get(index).getRoi();
                            if(left >= roi.x && top >= roi.y && right <= roi.x + roi.width && bottom <= roi.y + roi.height && textFrames[index] != null) {
                                inSide = true;
                                break;
                            }
                        }
                        
                        if(inSide)
                            continue;
                    }
                }
                
                Indexer index = indexer.get(plate_image_id);
                
                rois[plate_image_id] = Map.of("left", left, "top", top, "width", right - left, "height", bottom - top);
                textFrames[plate_image_id] = FrameUtils.roi_frame(index.getRoiImage(), left - indexerRoi.x, top - indexerRoi.y, right - left, bottom - top);
            }
            
            if(TEXTDETECTIONSHRINK == null) {
                PointerByReference text_param_keson = new PointerByReference(KesonUtils.frameToKeson(textFrames));
                plateResult[3] = execute(holders[3], text_param_keson.getValue());
                KesonUtils.kesonDeepDelete(text_param_keson);
            }else
                plateResult[3] = buildUpTextRegParam(textFrames);
            
            plateResult[4] = execute(holders[4], plateResult[3].getValue());
            
            Map<String, Object> textDetect = (Map<String, Object>)KesonUtils.kesonToJson(plateResult[4].getValue());
            List<Map<String, Object>> textTargets = (List<Map<String, Object>>)textDetect.get("targets");
            for(Map<String, Object> textTarget : textTargets) {
                int text_image_id = (int)textTarget.get("image_id");
                List<Map<String, Object>> textLines = (List<Map<String, Object>>)textTarget.get("textline");
                
                Float confidence = textLines.stream().map(line -> ((Number)line.get("score")).floatValue()).reduce(Float::sum).get() / textLines.size();
                String contentText = textLines.stream().map(line -> (String)((Map<String, Object>)line.getOrDefault("content", Map.of())).get("utf8")).filter(StringUtils::isNotBlank).collect(Collectors.joining());
                
                contents[text_image_id] = Map.of("score", confidence, "content", Map.of("utf8", contentText));
            }
            
            FrameUtils.batch_free_frame(textFrames);
        }
        
        FrameUtils.batch_free_frame(plateFrames);
        KesonUtils.kesonDeepDelete(plate_param_keson);
        
        Map<String, List<Map<String, Object>>> resultJson = new HashMap<String, List<Map<String, Object>>>();
        resultJson.put("targets", new ArrayList<Map<String, Object>>());
        for(int index = 0; index < indexer.size(); index ++) {         
            if(contents[index] == null)
                continue;
            
            Map<String, Object> resultItem = new HashMap<String, Object>();
            resultJson.get("targets").add(resultItem);

            resultItem.put("content", contents[index]);
            resultItem.put("confidence", contents[index].get("score"));
            resultItem.put("id", indexer.get(index).getId());
            resultItem.put("image_id", indexer.get(index).getImage_id());
            
            resultItem.put("roi", rois[index]);
            resultItem.put("attribute", attributes[index]);
            
            if(index < harpyRoiCount) 
                resultItem.put("isHarpy", true);
        }
        
        PointerByReference finalResult[] = new PointerByReference[] {KesonUtils.stringToKeson(JSON.toJSONString(resultJson))};
        KesonUtils.kesonDeepDelete(plateResult);
        
        PointerByReference output = KesonUtils.mergeRenameAllToFirstKeson(finalResult);
        PointerByReference[] sub_kesons = KesonUtils.splitKesonAndDestroy(output, handlingList.size());
        
        for (int index = 0; index < handlingList.size(); index++) {
            BatchItem item = handlingList.get(index);
            item.setKeson(sub_kesons[index]);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        boolean exist = super.validateOutputValue(modelResult);
        
        Map<String, Object> bufferMap = modelResult.getModelRequest().getProcessor().fetchBufferMap();
        List<Map<String, Object>> targets = (List<Map<String, Object>>)((Map<String, Object>)Objects.requireNonNullElse(modelResult.getDetectResult(), Map.of())).getOrDefault("targets", List.of());
        Iterator<Map<String, Object>> targetIts = targets.iterator();
        while(targetIts.hasNext()) {
            Map<String, Object> target = targetIts.next();
            if(MapUtils.isNotEmpty(bufferMap)) {
                int plateMinLength = (PLATEMINLENGTH != null) ? PLATEMINLENGTH : (Integer)bufferMap.getOrDefault("plateMinLength", 1);
                String content = ((Map<String, Object>)((Map<String, Object>)target.getOrDefault("content", Map.of())).getOrDefault("content", Map.of())).getOrDefault("utf8", StringUtils.EMPTY).toString();
                if(StringUtils.length(content) < plateMinLength) {
                    targetIts.remove();
                    continue;
                }
            }
            
            if(Boolean.TRUE.equals(target.remove("isHarpy")))
                target.put("roi_hits", DETECTED);
        }
        return exist;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> valueResultList = new ArrayList<Map<String, Object>>();
        modelResult.setOutputResult(valueResultList);
        
        List<JSONObject> targets = (List<JSONObject>)((Map<String, Object>)modelResult.getDetectResult()).getOrDefault("targets", List.of());
        for(JSONObject target : targets) {            
            Map<String, Object> valueResult = new HashMap<String, Object>();
            List<Map<String, Object>> attributes = new ArrayList<Map<String, Object>>();
                
            Map<String, Object> content = (Map<String, Object>)target.get("content");
            if(MapUtils.isNotEmpty(content))
                attributes.add(Map.of("key", "plate_content", "value", ((Map<String, Object>)content.get("content")).get("utf8"), "confidence", ((Number)target.get("confidence")).floatValue()));

            Map<String, Object> contentAttribute = (Map<String, Object>)target.get("attribute");
            for(Entry<String, Object> entry : contentAttribute.entrySet()) {
                Entry<String, Number> maxItem = ((Map<String, Number>)entry.getValue()).entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
                attributes.add(Map.of("key", entry.getKey(), "value", maxItem.getKey(), "confidence", maxItem.getValue()));
            }
            
            valueResult.putAll(Map.of("targetType", annotatorName(), "detect", target.get("roi"), "attributes", attributes, "confidence", target.get("confidence"), "plate", ((Map<String, Object>)content.get("content")).get("utf8")));

            Object roiHits = target.get("roi_hits");
            if(roiHits != null && roiHits != SCREEN)
                valueResult.put("rois", roiHits);
            
            valueResultList.add(valueResult);
        }

        try {
            /** 处理buffer */
            postProcessOutputValue(modelResult);
        }finally {
            if(valueResultList.isEmpty())
                modelResult.setOutputResult(null);
            else 
                for(Map<String, Object> valueResult : valueResultList)
                    valueResult.remove("plate");
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    protected void postProcessOutputValue(ModelResult modelResult){
        List<Map<String, Object>> targets = (List<Map<String, Object>>)modelResult.getOutputResult();
        Iterator<Map<String, Object>> targetIts = targets.iterator();
        while(targetIts.hasNext()) {
            Map<String, Object> targetL = targetIts.next();
            String contentL = targetL.get("plate").toString();
            
            boolean contains = false;
            for(Map<String, Object> targetR : targets) {
                if(targetL == targetR)
                    continue;
                
                String contentR = targetR.get("plate").toString();
                /** 如果被区域截取了半个车牌或者区域重叠导致车牌重复检测情况 在这里进行过滤*/
                if(Objects.equals(contentR, contentL) 
                        || StringUtils.startsWith(contentR, contentL)
                        || StringUtils.endsWith(contentR, contentL)) {
                    contains = true;
                    break;
                }
            }
            
            if(contains)
                targetIts.remove();
        }
        
        Processor processor = modelResult.getModelRequest().getProcessor();
        Map<String, Object> bufferMap = processor.fetchBufferMap();
        if(MapUtils.isEmpty(bufferMap))
            return ;
        
        String deviceId = (String)modelResult.getModelRequest().getParameter().get("deviceId");
        if(StringUtils.isBlank(deviceId) || CollectionUtils.isEmpty(Objects.requireNonNullElse(processor, Processor.empty).getExtras())) 
            return;

        LinkedBlockingQueue<Pair<String, Long>> counter = bufferCounter.get(deviceId);
        if(counter == null) {
            counter = new LinkedBlockingQueue<Pair<String, Long>>();
            bufferCounter.put(deviceId, counter);
        }
        
        long now = System.currentTimeMillis();
        int bufferSize = (Integer)bufferMap.getOrDefault("bufferSize", 2);
        int bufferExpire = (Integer)bufferMap.getOrDefault("bufferExpire", bufferSize * 2 * Objects.requireNonNullElse(processor.getInterval(), 1));
        while(!counter.isEmpty() && now - counter.peek().getRight() > bufferExpire * 1000)
            counter.poll();

        targetIts = targets.iterator();
        while(targetIts.hasNext()) {
            Map<String, Object> target = targetIts.next();
            String content = target.get("plate").toString();
            
            counter.add(new MutablePair<String, Long>(content, now));
            long existSize = counter.stream().filter(p -> p.getLeft().equals(content)).count();
            boolean bufferEnough = false;
            if(existSize >= bufferSize) {
                bufferEnough = true;
                
                Iterator<Pair<String, Long>> its = counter.iterator();
                while(its.hasNext()) {
                    Pair<String, Long> p = its.next();
                    if(p.getLeft().equals(content))
                        its.remove();
                }
            }
            
            if(!bufferEnough)
                targetIts.remove();
        }
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        Processor processor = modelResult.getModelRequest().getProcessor();
        Integer[][][] rois = processor.getRoi();
        if(processor.getRoi() == null)
            rois = (Integer[][][])modelResult.getModelRequest().getParameter().get("spliter");
        
        if(ArrayUtils.isNotEmpty(rois)) {
            for(Integer[][] roi : rois) {
                for(int index = 0; index < roi.length - 1; index ++) {
                    result.add(Line.builder().from(new int[] {roi[index][0], roi[index][1]}).to(new int[] {roi[index + 1][0], roi[index + 1][1]}).build());
                }
                result.add(Line.builder().from(new int[] {roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[] {roi[0][0], roi[0][1]}).build());
            }
        }
        
        if(modelResult.getOutputResult() != null)
        for(Map<String, Object> valueResult : (List<Map<String, Object>>)modelResult.getOutputResult()) {
            Map<String, Object> detect = (Map<String, Object>)valueResult.get("detect");
            Map<String, Object> plate_content = ((List<Map<String, Object>>)valueResult.get("attributes")).stream().filter(attribute -> "plate_content".equals(attribute.get("key"))).findAny().orElse(null);
            if(detect == null || plate_content == null)
                continue;
            
            result.add(Rect.builder().processor(annotatorName()).text(plate_content.get("value").toString()).top((int)detect.get("top")).left((int)detect.get("left")).width((int)detect.get("width")).height((int)detect.get("height")).build());
        }
        
        return result;
    }
    
    private final Integer[][][] splitRoi(int width, int height, int[] spliter) {
        int widthCount   = (int)((((float)width)  / spliter[1] - 1) / ((float)spliter[8] / 100) + 1);
        int heightCount  = (int)((((float)height) / spliter[2] - 1) / ((float)spliter[8] / 100) + 1);

        int leftMargin = (int)(width  - ((widthCount - 1)  * (float)spliter[8] / 100 + 1) * spliter[1]) / 2;
        int topMargin  = (int)(height - ((heightCount - 1) * (float)spliter[8] / 100 + 1) * spliter[2]) / 2;
        
        int borderLeft   = (int)((1 - (float)spliter[4] / 100) * width  / 2);
        int bordertop    = (int)((1 - (float)spliter[5] / 100) * height / 2);
        int borderRight  = (int)((1 + (float)spliter[6] / 100) * width  / 2);
        int borderBottom = (int)((1 + (float)spliter[7] / 100) * height / 2);
        
        Integer[][][] rois = new Integer[widthCount * heightCount][][];
        for(int jndex = 0; jndex < widthCount; jndex ++) {
            for(int kndex = 0; kndex < heightCount; kndex ++) {
                Integer[][] roi = new Integer[][] {
                    new Integer[] {leftMargin + jndex * spliter[1] * spliter[8] / 100             , topMargin + kndex * spliter[2] * spliter[8] / 100},
                    new Integer[] {leftMargin + jndex * spliter[1] * spliter[8] / 100 + spliter[1], topMargin + kndex * spliter[2] * spliter[8] / 100},
                    new Integer[] {leftMargin + jndex * spliter[1] * spliter[8] / 100 + spliter[1], topMargin + kndex * spliter[2] * spliter[8] / 100 + spliter[2]},
                    new Integer[] {leftMargin + jndex * spliter[1] * spliter[8] / 100             , topMargin + kndex * spliter[2] * spliter[8] / 100 + spliter[2]}
                };
                
                boolean checked = true;
                for(int index = 0; index < roi.length && checked; index ++)
                    checked &= roi[index][0] >= borderLeft && roi[index][0] <= borderRight && roi[index][1] >= bordertop && roi[index][1] <= borderBottom;
                
                if(checked)
                    rois[jndex * heightCount + kndex] = roi; 
            }
        }
        
        return Arrays.stream(rois).filter(Objects::nonNull).toArray(Integer[][][]::new);
    }
    
    private final PointerByReference execute(ModelHolder pointer, Pointer param_keson) {
        PointerByReference output_keson = new PointerByReference();
        
        long now = System.currentTimeMillis();
        pointer.process(param_keson, output_keson);
        monitor.expired.peekFirst().getAndAdd(System.currentTimeMillis() - now);
        
        return output_keson;
    }

    private PointerByReference buildUpTextRegParam(Pointer[] textFrames) {
        Pointer param_keson = KestrelApi.keson_create_object();
        KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
        
        Pointer targets_array = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_object(param_keson, "targets", targets_array);

        Memory area2dMemory = new Memory(KestrelApi._SIZE_kestrel_area2d_t);
        kestrel_area2d_t area2d_t = new kestrel_area2d_t(area2dMemory);
        
        for(int index = 0; index < textFrames.length; index ++) {
            Pointer target = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_object(target, "id"      , KestrelApi.keson_create_int(0));
            KestrelApi.keson_add_item_to_object(target, "image_id", KestrelApi.keson_create_int(index));
            KestrelApi.keson_add_item_to_array(targets_array, target);
            if(textFrames[index] == null)
                continue;
        
            KestrelApi.keson_add_item_to_object(target, "image"   , KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), textFrames[index]));
            
            Pointer textline_array = KestrelApi.keson_create_array();
            KestrelApi.keson_add_item_to_object(target, "textline", textline_array);
            
            Pointer textline = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(textline_array, textline);
            
            KestrelApi.keson_add_item_to_object(textline, "id", KestrelApi.keson_create_int(0));
            KestrelApi.keson_add_item_to_object(textline, "score", KestrelApi.keson_create_double(1.0));
            
			int width  = KestrelApi.kestrel_frame_video_width(textFrames[index]);
			int height = KestrelApi.kestrel_frame_video_height(textFrames[index]);
            
            area2d_t.top = 0 + (int)(TEXTDETECTIONSHRINK / 2 * height);
            area2d_t.left = 0 + (int)(TEXTDETECTIONSHRINK / 2 * width);
            area2d_t.height = (int)((1 - TEXTDETECTIONSHRINK / 2) * height);
            area2d_t.width = (int)((1 - TEXTDETECTIONSHRINK / 2) * width);
            area2d_t.write();
            
            Pointer keson_area2d = KestrelApi.keson_create_ext_object(KestrelApi.KESON_AREA2D(), area2dMemory);
            KestrelApi.keson_add_item_to_object(textline, "roi", keson_area2d);
        }
        
        return new PointerByReference(param_keson);
    }
    
    @Getter
    @Builder
    private static final class Indexer{
        private int image_id;
        private int id;
        private Roi roi;
        private Pointer roiImage;
    }
    
    @AllArgsConstructor
    public class Roi {
        public int x, y, width, height;
    }
}