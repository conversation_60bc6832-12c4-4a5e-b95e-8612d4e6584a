package com.sensetime.intersense.cognitivesvc.xworker.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult.Model;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStatusRes;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.utils.HostUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.ConfigAccessor;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.Monitor;
import com.sensetime.intersense.cognitivesvc.xworker.handler.XStoreHandler;
import com.sensetime.intersense.cognitivesvc.xworker.zutils.VideoStreamXWorker;
import com.sensetime.lib.clientlib.response.BaseRes;

import io.swagger.v3.oas.annotations.Operation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 心跳接口，switcher把当前这个worker应该有的模型和流发过来。
 */
@RestController(value = "xWorkerStreamProvider")
@RequestMapping(value = "/cognitive/xworker/heart/beat/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="XWorkerHeartBeatProvider",description = "xworker controller")
@Slf4j
public class XWorkerHeartBeatProvider extends BaseProvider {
	
	@Autowired
	private XStoreHandler modelStoreHandler;
	
    @Operation(summary = "模型心跳", method = "POST", hidden = true)
    @RequestMapping(value = "/model", method = RequestMethod.POST)
    public BaseRes<String> model(@RequestBody List<String> ongoingModels) throws Exception {
    	modelStoreHandler.setOngoingModel(ongoingModels);
    	return BaseRes.success("success");
    }
    
    @Operation(summary = "高频流心跳", method = "POST", hidden = true)
    @RequestMapping(value = "/stream", method = RequestMethod.POST)
    public BaseRes<String> stream(@RequestBody Map<String, List<String>> ongoingStreams) throws Exception {
    	modelStoreHandler.setOngoingStream(ongoingStreams);
    	return BaseRes.success("success");
    }
	
    @Operation(summary = "低频流在worker心跳", method = "POST", hidden = true)
    @RequestMapping(value = "/switcher/on/worker", method = RequestMethod.POST)
    public BaseRes<String> switcherOnWorker(@RequestBody List<String> ongoingDeviceIds) throws Exception {
    	modelStoreHandler.setSwitcherOnWorker(ongoingDeviceIds);
    	return BaseRes.success("success");
    }
	
    @Operation(summary = "查询模型处理器",method = "GET", hidden = true)
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public QueryWorkerResult queryModelHandler(@RequestParam(required = false) boolean isMaster) throws Exception {
    	QueryWorkerResult result = new QueryWorkerResult();
    	result.setDeviceType(Initializer.deviceType.toString() + Initializer.cudaType.toString());
		result.setVersion(Utils.VERSION);
		log.info("query isMaster={}", isMaster);
    	if(Initializer.isGpu()) {
			if(isMaster) {
				int gpuMem[] = HostUtils.getGpuMem();
				result.setAvailableGpuMemory(gpuMem[1] - gpuMem[0]);
				result.setTotalGpuMemory(gpuMem[1]);
				result.setCurrentGpuRate(((float)gpuMem[0]) / ((float)gpuMem[1]));
			}else{
				result.setAvailableGpuMemory(1024);
				result.setTotalGpuMemory(-1);
				result.setCurrentGpuRate(-1.0f);
			}
    		result.setIdentity(HostUtils.UUID());
			result.setIdentityFull(HostUtils.UUIDFull());
    		result.setAvgGpuRate(HostUtils.getGpuRateLast5m());
    		result.setGpuId(Initializer.deviceId);
    	}else {
    		//add note cpu 暂时
    		result.setAvailableGpuMemory(1024);
    		result.setTotalGpuMemory(-1);
    		result.setIdentity(QueryWorkerResult.NaN);
			result.setIdentityFull(QueryWorkerResult.NaN);
    		result.setAvgGpuRate(100);
    		result.setGpuId(QueryWorkerResult.NaN);
			result.setCurrentGpuRate(-1.0f);
    	}
		
    	Map<String, List<String>> highMap = modelStoreHandler.getProcessorDeviceMap();
    	Map<String, List<String>> tempHighMap = modelStoreHandler.getTemporaryDeviceMap();

    	Function<String, Model> mapper = annotator -> {
			AbstractXModelHandler handler = (AbstractXModelHandler)modelStoreHandler.getXDynamicHandlerMap().get(annotator);
			if(handler == null)
				return Model.builder().annotatorName(annotator).status("initializing").build();
				
			Monitor monitor = handler.getMonitor();
			
			Map<String, Object> monitorMap = Maps.newLinkedHashMap();
			Map<String, Object> config = new HashMap<String, Object>();
			
			monitorMap.put("handled_in_minite"   , monitor.getHandledCount());
			monitorMap.put("unhandled_in_minite" , monitor.getUnhandledCount());
			monitorMap.put("send_message_in_minite"  , monitor.getSendedCount());
			monitorMap.put("handled_total"      , monitor.handledTotal.get());
			monitorMap.put("unhandled_total"    , monitor.unhandledTotal.get());
			monitorMap.put("send_message_total"  , monitor.sendedTotal.get());
			
			if(handler instanceof ConfigAccessor) {
				ConfigAccessor accessor = (ConfigAccessor)handler;
				if(accessor.getBlocking() != null)
					config.put("blocking", accessor.getBlocking());
				
				if(accessor.getFrameBuffer() != null)
					config.put("frameBuffer", accessor.getFrameBuffer());

				if(accessor.getDecoderFormat() != null)
					config.put("gdecoderFormat", accessor.getDecoderFormat());

				if(accessor.getFrameBufferStrategy() != null)
					config.put("frameBufferStrategy", accessor.getFrameBufferStrategy());

				if(accessor.getNeedContext() != null)
					config.put("needContext", accessor.getNeedContext());

				if(accessor.getNoBatch() != null)
					config.put("noBatch", accessor.getNoBatch());

				if(accessor.getInterval() != null)
					config.put("interval", accessor.getInterval());

				if(accessor.getMinBatchSize() != null)
					config.put("minBatchSize", accessor.getMinBatchSize());

				if(accessor.getMinBatchStagger() != null)
					config.put("minBatchStagger", accessor.getMinBatchStagger());
			}
			List<String> hightDevices = highMap.getOrDefault(annotator, List.of());

			Map<String, VideoStreamXWorker> videoStremMap = modelStoreHandler.getOngoingVideoStreamMap();

			Map<String, Map<String, Object> > monitorDevice = new HashMap<>();
			for (String device : hightDevices){
				VideoStreamXWorker xworker = videoStremMap.get(device);
				Map<String, Object> monitorMapWorker = Maps.newLinkedHashMap();
				if(xworker !=null) {
					monitorMapWorker.put("handled_in_minite", xworker.getMonitor().getHandledCount());
					//monitorMapWorker.put("frame_pull_in_minite", xworker.getMonitor().getFramePullount());
					//monitorMapWorker.put("frame_pull_total", xworker.getMonitor().handledFramePullTotal.get());
					monitorMapWorker.put("unhandled_in_minite", xworker.getMonitor().getUnhandledCount());
					monitorMapWorker.put("send_message_in_minite", xworker.getMonitor().getSendedCount());
					monitorMapWorker.put("send_message_total", xworker.getMonitor().sendedTotal.get());
					monitorMapWorker.put("handled_total", xworker.getMonitor().handledTotal.get());
					monitorMapWorker.put("unhandled_total", xworker.getMonitor().unhandledTotal.get());
					monitorMapWorker.put("video_status", xworker.getMonitor().getVideoStatus());
					monitorMapWorker.put("video_status_detail", xworker.getMonitor().getVideoStatusDetail());
					monitorMapWorker.put("frame_handled_total", xworker.getMonitor().frameHandledTotal.get());
					monitorMapWorker.put("frame_unhandled_total", xworker.getMonitor().frameUnhandledTotal.get());
					monitorMapWorker.put("frame_un_offer_total", xworker.getMonitor().frameUnOfferTotal.get());
					if(xworker.getMonitor().getLastErrorCheckTime() != null){
						monitorMapWorker.put("last_error_check_time",xworker.getMonitor().getLastErrorCheckTime());
					}
				}

				monitorDevice.put(device, monitorMapWorker);
			}

			return Model.builder()
						.annotatorName(annotator)
						.expire(monitor.getExpired())
						.monitor(monitorMap)
					    .monitorDevice(monitorDevice)
						.config(config)
					    .higtRateDevices(highMap.getOrDefault(annotator, List.of()))
						.tempHigtRateDevices(tempHighMap.getOrDefault(annotator, List.of()))
						.status("running")
						.build();
		};
    	
		result.setDynamicModels(modelStoreHandler.getOngoingModels().stream().map(mapper).collect(Collectors.toList()));
		//log.info("[query instance info] heartbeat query result:{}", JSON.toJSONString(result));
		return result;
    }

	@Operation(summary = "查询模型处理器",method = "GET", hidden = true)
	@RequestMapping(value = "/query/videostatus", method = RequestMethod.GET)
	public BaseRes<List<VideoStatusRes>> queryModelHandlerVideostatus() throws Exception {

		List<VideoStatusRes> videoStatusResList = new ArrayList<>();

		Map<String, VideoStreamXWorker> videoStremMap = modelStoreHandler.getOngoingVideoStreamMap();

		videoStremMap.forEach((deviceId, videoStreamXWorker) ->{
			VideoStatusRes videoStatusRes = new VideoStatusRes();
			videoStatusRes.setDeviceId(deviceId);
			videoStatusRes.setDesc(videoStreamXWorker.getVideoStatusDetail());
			if (videoStreamXWorker.getLastErrorCheckTime() != null) {
				videoStatusRes.setLastErrorCheckTs(videoStreamXWorker.getLastErrorCheckTime().toString());
			}
			videoStatusRes.setVideoStatus(videoStreamXWorker.getVideoStatusOutput().toString());

			videoStatusResList.add(videoStatusRes);
		});
		return BaseRes.success(videoStatusResList);
	}

}