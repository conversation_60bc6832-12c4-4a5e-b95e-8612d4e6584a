package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.opencv_core.CvScalar;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
@SuppressWarnings("unused")
public class CrossLine extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {



    protected final ConcurrentHashMap<Long, Map<Integer, Track>> trackingMap = new ConcurrentHashMap<Long, Map<Integer, Track>>();


    protected  final ConcurrentHashMap<Long,  Map<String, Integer>> trackingMapOutCount = new ConcurrentHashMap<Long, Map<String, Integer>>();

    @Getter
    protected final Integer interval = 0;//检一跳三

    @Getter
    protected final Boolean blocking = false;

    @Getter
    protected final Boolean needContext = true;

    @Getter
    protected final boolean queueMap = true;

    @Getter
    protected final String decoderFormat = "rgb24";

    @Getter
    protected final Integer frameBuffer = 40;//显存小的用20, 显存大了稍微提高一些

    @Getter
    protected final String frameBufferStrategy = "smart";



    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        Long[] imageIds = handlingList.stream().map(item -> Utils.keyToContextId(item.getModelRequest().getParameter().get("deviceId"))).toArray(Long[]::new);
        PointerByReference[] sub0_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[0])[0], imageIds);
        PointerByReference[] sub1_kesons = KesonUtils.splitKesonAndDestroy(KesonUtils.tryReformFlockKeson(outputKesons[1])[0], imageIds);

        for (int index = 0; index < handlingList.size(); index++) {
            Pointer array = KestrelApi.keson_create_array();

            if (sub0_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub0_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            if (sub1_kesons[index] != null)
                KestrelApi.keson_add_item_to_array(array, sub1_kesons[index].getValue());
            else
                KestrelApi.keson_add_item_to_array(array, KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", KestrelApi.keson_create_array()));

            handlingList.get(index).setKeson(new PointerByReference(array));
        }

        KesonUtils.kesonDeepDelete(outputKesons);
    }


    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult == null || modelResult.getKeson() == null || modelResult.getKeson().getValue() == null)
            return;

        modelResult.setDetectResult(KesonUtils.kesonToJson(modelResult.getKeson()));
    }

    @SuppressWarnings({"unchecked"})
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return false;

        long source_id = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        float threshold = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getThreshold(), 0f);
        int minSize = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getMinSize(), Integer.MIN_VALUE);
        Pointer kesonTargetsValidate = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 1), "targets");
        int kesonTarget_size_validate = KestrelApi.keson_array_size(kesonTargetsValidate);


        Map<Integer, Track> tracks = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        if(tracks == null)
            return false;


        Processor processor = modelResult.getModelRequest().getProcessor();
        Polygon[] polygons = processor.fetchPolygons();


        Map<Integer, Map<Integer, Map<String, Object>>> labelExtrasMap = (Map<Integer, Map<Integer, Map<String, Object>>>)processor.fetchExtras(function);
        long contextId =Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));


        Pointer kesonTargetsTrack = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 0), "targets");
        int kesonTarget_size_track = KestrelApi.keson_array_size(kesonTargetsTrack);


        /**
         * 开始里每一帧
         */
        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).get("targets");
//        log.info("[shieldFace] target00000 {} ",JSON.toJSONString(targets));
        for (Map<String, Object> target : targets) {
            List<Map<String, Object>> associations = (List<Map<String, Object>>) target.get("associations");
            if (CollectionUtils.isEmpty(associations))
                continue;

            int faceTrackId, bodyTrackId;


            int trackId = ((Number)target.get("track_id")).intValue();
            int label = ((Number)target.get("label")).intValue();
            float quality = (((Number)target.get("confidence")).floatValue());
            long capturedTime = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();




            if (label == 37017) {
                Integer tCount = 0;
                Integer tOutCount = 0;


                faceTrackId = ((Number) target.get("track_id")).intValue();
                bodyTrackId = ((Number) associations.get(0).get("track_id")).intValue();


                log.info("crossfaceTrackId{},{}", faceTrackId,bodyTrackId);

                Track track = tracks.get(faceTrackId );
                if(track == null) {
                    track = Track.builder().label(label).trackId(faceTrackId).associationsId(bodyTrackId + label).previousInStamp(new Long[polygons.length]).firstInStamp(new Long[polygons.length]).keepingInStamp(new Long[polygons.length]).build();
                    tracks.put(faceTrackId, track);
                }
                track.setCurrentPosition((Map<String, Number>)target.get("roi"));
                track.setAssociationsId(bodyTrackId + label);

                tCount = track.getTrackInCount();
                tOutCount = track.getTrackOutCount();

                Polygon[] polygonExcludeList = new Polygon[polygons.length];
                for(int index = 0 ;index < polygons.length; index ++) {
                    Map<String, Object> extra = labelExtrasMap.getOrDefault(index, Map.of()).get(label);
                    if(extra == null)
                        continue;

                    Boolean exclude = (Boolean)extra.getOrDefault("exclude", false);
                    if(exclude){
                        Polygon p = polygons[index];
                        polygonExcludeList[index] = p;
                    }
                }

                for(int index = 0 ;index < polygons.length; index ++) {
                    Map<String, Object> extra = labelExtrasMap.getOrDefault(index, Map.of()).get(label);
                    if(extra == null)
                        continue;

                    Polygon polygon = polygons[index];

                    target.put("threshold", threshold);

                    //前一帧的时间
                    track.getPreviousInStamp()[index] = capturedTime;

                    Number positionType = (Number)extra.getOrDefault("positionType", 0);
                    Boolean exclude = (Boolean)extra.getOrDefault("exclude", false);

                    //利用人脸 中心点作为目标点
                    int currentX  = track.getCurrentPosition().getLeft()  + track.getCurrentPosition().getWidth()   / 2;
                    int currentY  = track.getCurrentPosition().getTop()   + track.getCurrentPosition().getHeight()  / 2;
                    if(positionType.intValue() == 1) //利用头顶顶点作为目标点
                        currentY  = track.getCurrentPosition().getTop();
                    else if(positionType.intValue() == 2) //利用下巴底点作为目标点
                        currentY  = track.getCurrentPosition().getTop()   + track.getCurrentPosition().getHeight();

                    //如果在排除roi里 不计算
                    if(polygonExcludeList[index] !=null  && polygonExcludeList[index].contains(currentX, currentY)) {
                        continue;
                    }

                    List<Number> crossDot = (List<Number>)extra.getOrDefault("crossDot", List.of());
                    if(!crossDot.isEmpty() && track.getPreviousPosition() != null) {
                        int previousX = track.getPreviousPosition().getLeft() + track.getPreviousPosition().getWidth()  / 2;
                        int previousY = track.getPreviousPosition().getTop()  + track.getPreviousPosition().getHeight() / 2;

                        if(positionType.intValue() == 1)//利用头顶顶点作为目标点
                            previousY = track.getPreviousPosition().getTop();
                        else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                            previousY = track.getPreviousPosition().getTop()  + track.getPreviousPosition().getHeight();

                        int cx, cy;
                        if(crossDot.size() != 2) {
                            cx = Arrays.stream(polygon.xpoints).sum() / polygon.npoints;
                            cy = Arrays.stream(polygon.ypoints).sum() / polygon.npoints;
                        }else {
                            cx = crossDot.get(0).intValue();
                            cy = crossDot.get(1).intValue();
                        }

                        String crossDirection = (String)extra.get("crossDirection");
                        for(int dotIndex = 0; dotIndex < polygon.npoints - 1 || (polygon.npoints > 2 && dotIndex == polygon.npoints - 1); dotIndex ++) {
                            int ax = polygon.xpoints[dotIndex];
                            int ay = polygon.ypoints[dotIndex];

                            int bx = (dotIndex == polygon.npoints - 1) ? polygon.xpoints[0] : polygon.xpoints[dotIndex + 1];
                            int by = (dotIndex == polygon.npoints - 1) ? polygon.ypoints[0] : polygon.ypoints[dotIndex + 1];

                            if(!intersect(ax, ay, bx, by, previousX, previousY, currentX, currentY)) {
                                log.info("crossDirection Filter{},{}",currentX, currentY);
                                continue;
                            }

                            boolean previousIntersected = intersect(ax, ay, bx, by, previousX, previousY, cx, cy);
                            boolean targetIntersected   = intersect(ax, ay, bx, by, currentX , currentY , cx, cy);

                            String direction = "unknown";
                            if(previousIntersected && !targetIntersected) {
                                direction = "forward";
                            }else if(!previousIntersected && targetIntersected) {
                                direction = "backward";
                            }

                            //只要正面
                            if(StringUtils.isNotBlank(crossDirection) && !crossDirection.equals(direction))
                                continue;


                            if(extra.containsKey("trigger")) {

                                Map<String, Object> trigger = (Map<String, Object>)extra.get("trigger");

                                int triggerTime = (int)trigger.getOrDefault("triggerTime", 1000);
                                int maxTrackTime = (int)trigger.getOrDefault("maxTrackTime", 0);

                                //在设置的时间内，不计算
                                if(track.getFirstInStamp()[index] != null && track.getFirstInStamp()[index] > 0 && capturedTime - track.getFirstInStamp()[index] <= maxTrackTime){
                                      continue;
                                }

                                if(track.getFirstInStamp()[index] != null && track.getFirstInStamp()[index] > 0 && capturedTime - track.getFirstInStamp()[index] > maxTrackTime){

                                    //和上一帧比，相差在时间范围内 不计数
                                    if(track.getPreviousInStamp()[index] != null && track.getPreviousInStamp()[index] > 0 && capturedTime - track.getPreviousInStamp()[index] < maxTrackTime){
                                        continue;
                                    }
                                }
                            }

                            if(direction.equals("forward")){
                                tCount ++;
                                track.setTrackInCount(tCount + 1);
                            }else if(direction.equals("backward")){
                                tOutCount ++;
                                track.setTrackOutCount(tOutCount + 1);
                            }
//                            if(exclude && direction.equals("forward")){
//                                tCount --;
//                            } else if(exclude && !direction.equals("backward")){
//                                tOutCount--;
//                            }

                            log.info("crossDirection{},{},p-x-{},p-y-{},{},{},{},{}", direction,faceTrackId, previousX, previousY, currentX, currentY,previousIntersected, targetIntersected);

                            log.info("crossDirections{},{}", direction, tCount);

//                            updateTrack(contextId, "in", tCount);
//                            updateTrack(contextId, "out", tOutCount);


                            track.getFirstInStamp()[index] = capturedTime;



                            List<Map<String, Object>> crossAlert = (List<Map<String, Object>>)target.get("crossAlert");
                            if(crossAlert == null) {
                                crossAlert = new ArrayList<Map<String, Object>>();
                                target.put("crossAlert", crossAlert);
                            }

                            Map<String, Object> cross = new HashMap<String, Object>();
                            cross.put("roiIndex", index);
                            cross.put("direction", direction);
                            crossAlert.add(cross);


                        }
                    }



                }
            } else {
                bodyTrackId = ((Number) target.get("track_id")).intValue();
                faceTrackId = ((Number) associations.get(0).get("track_id")).intValue();

                Integer tCount = 0;
                Integer tOutCount = 0;


                Track track = tracks.get(bodyTrackId + label );
                if(track == null) {
                    track = Track.builder().label(label).trackId(bodyTrackId).associationsId(faceTrackId).previousInStamp(new Long[polygons.length]).firstInStamp(new Long[polygons.length]).keepingInStamp(new Long[polygons.length]).build();
                    tracks.put(bodyTrackId + label , track);
                }
                track.setCurrentPosition((Map<String, Number>)target.get("roi"));
                track.setAssociationsId(faceTrackId);

                tCount = track.getTrackInCount();
                tOutCount = track.getTrackOutCount();


                Polygon[] polygonExcludeList = new Polygon[polygons.length];
                for(int index = 0 ;index < polygons.length; index ++) {
                    Map<String, Object> extra = labelExtrasMap.getOrDefault(index, Map.of()).get(label);
                    if(extra == null)
                        continue;

                    Boolean exclude = (Boolean)extra.getOrDefault("exclude", false);
                    if(exclude){
                        Polygon p = polygons[index];
                        polygonExcludeList[index] = p;
                    }
                }


                for(int index = 0 ;index < polygons.length; index ++) {
                    Map<String, Object> extra = labelExtrasMap.getOrDefault(index, Map.of()).get(label);
                    if(extra == null)
                        continue;

                    Polygon polygon = polygons[index];

                    target.put("threshold", threshold);

                    //前一帧的时间
                    track.getPreviousInStamp()[index] = capturedTime;


                    Number positionType = (Number)extra.getOrDefault("positionType", 0);

                    //利用人体 中心点作为目标点
                    int currentX  = track.getCurrentPosition().getLeft()  + track.getCurrentPosition().getWidth()   / 2;
                    int currentY  = track.getCurrentPosition().getTop()   + track.getCurrentPosition().getHeight()  / 2;
                    if(positionType.intValue() == 1) //利用头顶顶点作为目标点
                        currentY  = track.getCurrentPosition().getTop();
                    else if(positionType.intValue() == 2) //利用脚底底点作为目标点
                        currentY  = track.getCurrentPosition().getTop()   + track.getCurrentPosition().getHeight();


                    //如果在排除roi里 不计算
                    if(polygonExcludeList[index] !=null  && polygonExcludeList[index].contains(currentX, currentY)) {
                        continue;
                    }

                    List<Number> crossDot = (List<Number>)extra.getOrDefault("crossDot", List.of());
                    if(!crossDot.isEmpty() && track.getPreviousPosition() != null) {
                        int previousX = track.getPreviousPosition().getLeft() + track.getPreviousPosition().getWidth()  / 2;
                        int previousY = track.getPreviousPosition().getTop()  + track.getPreviousPosition().getHeight() / 2;

                        if(positionType.intValue() == 1)//利用头顶顶点作为目标点
                            previousY = track.getPreviousPosition().getTop();
                        else if(positionType.intValue() == 2) //利用脚底板底点作为目标点
                            previousY = track.getPreviousPosition().getTop()  + track.getPreviousPosition().getHeight();

                        int cx, cy;
                        if(crossDot.size() != 2) {
                            cx = Arrays.stream(polygon.xpoints).sum() / polygon.npoints;
                            cy = Arrays.stream(polygon.ypoints).sum() / polygon.npoints;
                        }else {
                            cx = crossDot.get(0).intValue();
                            cy = crossDot.get(1).intValue();
                        }

                        String crossDirection = (String)extra.get("crossDirection");
                        for(int dotIndex = 0; dotIndex < polygon.npoints - 1 || (polygon.npoints > 2 && dotIndex == polygon.npoints - 1); dotIndex ++) {
                            int ax = polygon.xpoints[dotIndex];
                            int ay = polygon.ypoints[dotIndex];

                            int bx = (dotIndex == polygon.npoints - 1) ? polygon.xpoints[0] : polygon.xpoints[dotIndex + 1];
                            int by = (dotIndex == polygon.npoints - 1) ? polygon.ypoints[0] : polygon.ypoints[dotIndex + 1];

                            if(!intersect(ax, ay, bx, by, previousX, previousY, currentX, currentY)) {
                                log.info("crossDirection Filter bodyTrackId{},{}",currentX, currentY);
                                continue;
                            }

                            boolean previousIntersected = intersect(ax, ay, bx, by, previousX, previousY, cx, cy);
                            boolean targetIntersected   = intersect(ax, ay, bx, by, currentX , currentY , cx, cy);

                            String direction = "unknown";
                            if(previousIntersected && !targetIntersected)
                                direction = "forward";
                            else if(!previousIntersected && targetIntersected)
                                direction = "backward";


                            //只要正面
                            if(StringUtils.isNotBlank(crossDirection) && !crossDirection.equals(direction))
                                continue;


                            if(extra.containsKey("trigger")) {

                                Map<String, Object> trigger = (Map<String, Object>)extra.get("trigger");

                                int triggerTime = (int)trigger.getOrDefault("triggerTime", 1000);
                                int maxTrackTime = (int)trigger.getOrDefault("maxTrackTime", 1000);


                                log.info("crossDirections{},{},{}maxTrackTime,{}",direction, capturedTime,  track.getFirstInStamp()[index],bodyTrackId);
                                //在设置的时间内，不计算
                                if(track.getFirstInStamp()[index] != null && track.getFirstInStamp()[index] > 0 && capturedTime - track.getFirstInStamp()[index] <= maxTrackTime){

                                    log.info("crossDirections{},{},{}maxTrackTime",direction, capturedTime,  track.getFirstInStamp()[index]);
                                    continue;
                                }

                                if(track.getFirstInStamp()[index] != null && track.getFirstInStamp()[index] > 0 && capturedTime - track.getFirstInStamp()[index] > maxTrackTime){

                                    log.info("crossDirections{},{},{}maxTrackTimes",direction, capturedTime,  track.getFirstInStamp()[index]);
                                    //和上一帧比，相差在时间范围内 不计数
                                    if(track.getPreviousInStamp()[index] != null && track.getPreviousInStamp()[index] > 0 && capturedTime - track.getPreviousInStamp()[index] < maxTrackTime){
                                        log.info("crossDirections{},{},{}maxTrackTimeContinue",direction, capturedTime,  track.getFirstInStamp()[index]);
                                        continue;
                                    }
                                }
                            }
                            if(direction.equals("forward")){
                                track.setTrackInCount(tCount + 1);
                            }else if(direction.equals("backward")){
                                track.setTrackOutCount(tOutCount + 1);
                            }

//                            updateTrack(contextId, "in", tCount);
//                            updateTrack(contextId, "out", tOutCount);

                            log.info("crossDirections{},{}bodyTrackId{}", direction, tCount, bodyTrackId);


                            track.getFirstInStamp()[index] = capturedTime;


                            List<Map<String, Object>> crossAlert = (List<Map<String, Object>>)target.get("crossAlert");
                            if(crossAlert == null) {
                                crossAlert = new ArrayList<Map<String, Object>>();
                                target.put("crossAlert", crossAlert);
                            }

                            Map<String, Object> cross = new HashMap<String, Object>();
                            cross.put("roiIndex", index);
                            cross.put("direction", direction);
                            crossAlert.add(cross);


                        }
                    }

                }

            }

        }


        List<Number> dropped_track_ids = targets.stream().map(target -> (Number) target.get("dropped_track_id")).filter(Objects::nonNull).collect(Collectors.toList());
        for (Number dropped_track_id : dropped_track_ids) {
            tracks.remove( dropped_track_id.intValue());

        }

        return false;
    }

    private boolean checkAttributeMask(Map<String, Object> attributeMask) {

        float frot = ((Number) attributeMask.get("st_respirator_agnostic")).floatValue();
        float side = ((Number) attributeMask.get("st_respirator_without")).floatValue();
        float full = ((Number) attributeMask.get("st_respirator_full")).floatValue();
        return (full > side && full > frot);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return;

        Processor processor = modelResult.getModelRequest().getProcessor();

        long source_id = Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(1).getOrDefault("targets", List.of());

        Pointer kesonTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(modelResult.getKeson().getValue(), 1), "targets");
        int kesonTarget_size = KestrelApi.keson_array_size(kesonTargets);

        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();
        for (Map<String, Object> target : targets) {
            Map<String, Object> output = new HashMap<String, Object>();

            output.put("attributes", rawAttributeMapToOutput((Map<String, Map<String, Number>>) target.get("attribute")));
            output.put("label", target.get("label"));
            output.put("position", target.get("position"));
            output.put("confidence", target.get("confidence"));
            output.put("targetRoi", target.get("roi"));
            if (target.containsKey("aligner_confidence")) {
                output.put("aligner_confidence", target.get("aligner_confidence"));
            }
            if (target.containsKey("pedeAttributes")) {
                output.put("pedeAttributes", target.get("pedeAttributes"));
            }
            if (target.containsKey("removed")) {
                output.put("removed", target.get("removed"));
            }
            if (target.containsKey("motion_from_begin")) {
                output.put("motion_from_begin", target.get("motion_from_begin"));
            }
            if (target.containsKey("integrate_quality")) {
                output.put("integrate_quality", target.get("integrate_quality"));
            }
            if (target.containsKey("track_id")) {
                output.put("track_id", target.get("track_id"));
            }
            if (target.containsKey("yaw") && target.containsKey("pitch")) {
                output.put("head_pose", Map.of("yaw", target.get("yaw"), "pitch", target.get("pitch"), "roll", target.get("roll")));
            }
            if (target.containsKey("status")) {
                output.put("status", target.get("status"));
            }
            output.put("source_id", source_id);

            Pointer kesonTarget = Stream.iterate(0, i -> i + 1)
                    .limit(kesonTarget_size)
                    .map(index -> KestrelApi.keson_get_array_item(kesonTargets, index))
                    .filter(t -> KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(t, "id")) == ((Number) target.get("id")).longValue())
                    .findAny()
                    .orElse(null);

            if (kesonTarget != null) {
                PointerByReference targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "image"));
                if (targetImage.getValue() != null) {
                    String targetImagePath = FrameUtils.save_image_as_jpg(targetImage.getValue(), ImageUtils.newFile(annotatorName()),processor.getImgSaveTag());
                    output.put("targetImage", targetImagePath);
                }

                PointerByReference sceneImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(kesonTarget, "scene_frame"));
                if (sceneImage.getValue() != null) {
                    String sceneImagePath = FrameUtils.save_image_as_jpg(sceneImage.getValue(), ImageUtils.newFile(annotatorName()), processor.getImgSaveTag());
                    output.put("sceneImage", sceneImagePath);
                }
            }

            outputResult.add(output);
        }

        modelResult.getModelRequest().getParameter().put("replaceImageUrls", new String[]{FrameUtils.NOIMAGE});
        modelResult.setOutputResult(outputResult);
    }

    @SuppressWarnings("unchecked")
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        List<Map<String, Object>> detectResult = (List<Map<String, Object>>) modelResult.getDetectResult();
        if (CollectionUtils.isEmpty(detectResult))
            return result;

        List<Map<String, Object>> targets = (List<Map<String, Object>>) detectResult.get(0).getOrDefault("targets", List.of());

        long contextId =Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId"));
        Integer tCount =0;
        Integer tOutCount = 0;


        Map<Integer, Track> tracks = trackingMap.get(Utils.keyToContextId(modelResult.getModelRequest().getParameter().get("deviceId")));
        if(tracks == null)
            return result;


        for(Map.Entry<Integer, Track> entry : tracks.entrySet()) {
             Track currentTrack = entry.getValue();
             boolean hasCheck = false;
//            if(currentTrack.getLabel() == FACE) { // 如果当前Track的label是人脸
//                int associationsId = currentTrack.getAssociationsId(); // 获取关联的人体trackId
//                Track associatedTrack = tracks.get(associationsId); // 根据关联id获取对应的人体Track对象
//                // 在这里可以对associatedTrack进行一些处理，例如更新其位置等等
//                if (associatedTrack != null) {
//                    if (associatedTrack.trackInCount > 0) {
//                        tCount += associatedTrack.trackInCount;
//                        hasCheck = true;
//                    }
//                }
//                 if(!hasCheck && currentTrack.trackInCount > 0){
//                    tCount += currentTrack.trackInCount;
//                    hasCheck = true;
//                }
//            }
            if(currentTrack.getLabel() == BODY) {
                int associationsId = currentTrack.getAssociationsId(); // 获取关联的人体trackId
                Track associatedTrack = tracks.get(associationsId); // 根据关联id获取对应的人体Track对象
                // 在这里可以对associatedTrack进行一些处理，例如更新其位置等等
                if( currentTrack.trackInCount > 0){
                    tCount += currentTrack.trackInCount;
                    hasCheck = true;
                }
                if (associatedTrack != null &&  !hasCheck) {
                    if (associatedTrack.trackInCount > 0) {
                        tCount += associatedTrack.trackInCount;
                    }
                }

            }

        }


        CvScalar color = opencv_core.CV_RGB(255, 0,0 );

        for (Map<String, Object> target : targets) {
            int label = ((Number) target.getOrDefault("label", -1)).intValue();
            int trackId = ((Number) target.getOrDefault("track_id", -1)).intValue();
            if (trackId < 0)
                continue;

            if (label == FACE) {

                Map<String, Integer> roi = (Map<String, Integer>) target.get("roi");
                result.add(Rect.builder().processor(annotatorName()).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());

//               if(target.containsKey("crossAlert") && target.get("crossAlert") !=null){
//
//                    List<Map<String, Object>> crossAlert = (List<Map<String, Object>>)target.get("crossAlert");
//
//                    for(Map<String, Object> cross :crossAlert ){
//                        if(cross.containsKey("direction") && cross.get("direction").equals("forward")){
//                            inCount ++ ;
//
//                        }
//                        if(cross.containsKey("direction") && cross.get("direction").equals("backward")){
//                            outCount ++ ;
//                        }
//                    }
//
//               }

                result.add(Rect.builder().color(color).processor(annotatorName()).top(roi.get("top")).left(roi.get("left")).width(roi.get("width")).height(roi.get("height")).build());


            }

        }

        log.info("crossDirectionCounts{},{}", tCount, tOutCount);

        result.add(Rect.builder().color(color).processor(annotatorName()).text("in [" + tCount + "],out [" +tOutCount+ "]").top(100).left(100).width(100).height(100).build());


        Processor processor = modelResult.getModelRequest().getProcessor();

        for (Integer[][] roi : Objects.requireNonNullElse(processor.getRoi(), new Integer[0][][])) {
            for (int index = 0; index < roi.length - 1; index++) {
                result.add(Line.builder().from(new int[]{roi[index][0], roi[index][1]}).to(new int[]{roi[index + 1][0], roi[index + 1][1]}).build());
            }
            result.add(Line.builder().from(new int[]{roi[roi.length - 1][0], roi[roi.length - 1][1]}).to(new int[]{roi[0][0], roi[0][1]}).build());
        }

        return result;
    }

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {
        log.info("Shield Face start context [" + contextId + "].");

        trackingMap.put(contextId, new ConcurrentHashMap<Integer, Track>());

        updateTrack(contextId, "in", 0);
        updateTrack(contextId, "out", 0);



    }

    private void stopContext(String deviceId, long contextId) {


        synchronized (holders[0]) {
            holders[0].controlForRemoveSource(contextId);
        }

        synchronized (holders[1]) {
            holders[1].controlForRemoveSource(contextId);
        }

        log.info("Shield Face stop deviceId [" + deviceId + "].");
    }

    private List<Map<String, Object>> rawAttributeMapToOutput(Map<String, Map<String, Number>> rawAttributeMap) {
        List<Map<String, Object>> outputResult = new ArrayList<Map<String, Object>>();

        for (Entry<String, Map<String, Number>> entry : rawAttributeMap.entrySet()) {
            try {
                Entry<String, Number> maxEntry = entry.getValue().entrySet().stream().max((l, r) -> Float.compare(l.getValue().floatValue(), r.getValue().floatValue())).get();
                outputResult.add(Map.of("key", entry.getKey(), "value", maxEntry.getKey(), "confidence", maxEntry.getValue()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return outputResult;
    }

    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> filter(Processor processor, List<Map<String, Object>> targets, String roiName) {
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        if (CollectionUtils.isEmpty(targets)) {
            return result;
        }
        if (processor.fetchPolygons().length <= 0) {
            return targets;
        }
        for (Map<String, Object> target : targets) {
            float confidence = ((Number) target.getOrDefault("confidence", 1.0f)).floatValue();
            if (confidence < Objects.requireNonNullElse(processor.getThreshold(), Float.MIN_VALUE))
                continue;

            Map<String, Number> roi = (Map<String, Number>) target.get(roiName);
            if (roi == null)
                continue;

            int minSize = Objects.requireNonNullElse(processor.getMinSize(), Integer.MIN_VALUE);
            if (roi.get("height").intValue() < minSize || roi.get("width").intValue() < minSize)
                continue;

            int x = roi.get("left").intValue() + roi.get("width").intValue() / 2;
            int y = roi.get("top").intValue() + roi.get("height").intValue() / 2;

            boolean exist = false;

            for (Polygon polygon : processor.fetchPolygons()) {
                if (!polygon.contains(x, y))
                    continue;

                exist = true;
                break;
            }

            if (!exist)
                continue;

            result.add(target);
        }

        return result;
    }

    private static final boolean checkAngle(List<Map<String, Object>> attributes) {
        if (CollectionUtils.isEmpty(attributes))
            return false;

        boolean checked = attributes.stream()
                .filter(attribute -> {
                    try {
                        float frot = ((Number) attribute.get("st_front")).floatValue();
                        float side = ((Number) attribute.get("st_side")).floatValue();
                        float back = ((Number) attribute.get("st_back")).floatValue();

                        if ((frot > side && frot > back))
                            return true;
                        else if ((side > frot && side > back && frot > back))
                            return true;

                        return false;
                    } catch (Exception e) {
                        return false;
                    }

                })
                .findAny()
                .isPresent();

        return checked;
    }

    private static final boolean checkRespirator(List<Map<String, Object>> attributes) {
        if (CollectionUtils.isEmpty(attributes))
            return false;

        return attributes.stream()
                .map(attribute -> (Number) attribute.getOrDefault("color_type_none", -1))
                .filter(conf -> conf.floatValue() > 0.5f)
                .findAny()
                .isEmpty();
    }

    @SuppressWarnings("unchecked")
    private static Object getExtraValue(List<Map<String, Object>> extras, String key, Object def) {
        return Objects.requireNonNullElse(extras, List.of())
                .stream()
                .filter(extra -> key.equals(((Map<String, Object>) extra).get("type")))
                .map(extra -> ((Map<String, Object>) extra).getOrDefault("value", def))
                .findAny()
                .orElse(def);

    }

    private static final boolean intersect(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4) {
        float bx = x2 - x1;
        float by = y2 - y1;
        float dx = x4 - x3;
        float dy = y4 - y3;
        float b_dot_d_perp = bx * dy - by * dx;

        if (b_dot_d_perp == 0)
            return false;

        float cx = x3 - x1;
        float cy = y3 - y1;
        float t = (cx * dy - cy * dx) / b_dot_d_perp;
        if (t < 0 || t > 1)
            return false;

        float u = (cx * by - cy * bx) / b_dot_d_perp;
        if (u < 0 || u > 1)
            return false;

        return true;
    }

    @Getter
    @Accessors(chain = true)
    @Builder
    private static final class Track{
        private int label;
        private int trackId;
        private String direction;
        private int associationsId;

        private Rect previousPosition;
        private Rect currentPosition;

        private Long[] firstInStamp;
        private Long[] keepingInStamp;

        private Long[] previousInStamp;


        private int trackInCount;
        private int trackOutCount;

        public void setCurrentPosition(Map<String, Number> roi) {
            previousPosition = currentPosition;
            currentPosition = Rect.builder().top(roi.get("top").intValue()).left(roi.get("left").intValue()).width(roi.get("width").intValue()).height(roi.get("height").intValue()).build();
        }
        public void setTrackInCount(int in) {
            trackInCount = in;
        }
        public void setTrackOutCount(int out) {
            trackOutCount = out;
        }

        public void setAssociationsId(int ass) {
            associationsId = ass;
        }

        @Data
        @Accessors(chain = true)
        @Builder
        public static class Rect{
            private int left;
            private int top;
            private int width;
            private int height;
        }
    }

    @SuppressWarnings({ "unchecked", "rawtypes"})
    private static final Function<Object, Map> function = new Function<Object, Map>() {

        @Override
        public Map apply(Object e) {
            List<Map<String, Object>> params = (List<Map<String, Object>>)e;

            Map<Integer, Map<Integer, Map<String, Object>>> result = new HashMap<Integer, Map<Integer, Map<String, Object>>>();
            for(Map<String, Object> param : params) {
                if(!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer)param.getOrDefault("roiIndex", 0);
                Object targetLabel = param.get("targetType");

                Map<Integer, Map<String, Object>> roiParam = result.get(roiIndex);
                if(roiParam == null) {
                    roiParam = new HashMap<Integer, Map<String, Object>>();
                    result.put(roiIndex, roiParam);
                }

                if(targetLabel == null) {
                    roiParam.put(221488,  param);
                    roiParam.put(37017,    param);
                    roiParam.put(1507442, param);
                    roiParam.put(2125610, param);
                }else {
                    if(targetLabel instanceof Number)
                        roiParam.put(((Number)targetLabel).intValue(), param);
                    else if(targetLabel instanceof String)
                        for(String label : ((String) targetLabel).split(","))
                            roiParam.put(Integer.parseInt(label), param);
                }
            }

            for(Map<String, Object> param : params) {
                if("roiIds".equals(param.get("type"))) {
                    result.put(Integer.MAX_VALUE, Map.of(Integer.MAX_VALUE, Map.of("roiIds", param.get("roiIds"))));
                    break;
                }
            }

            return result;
        }
    };

    private static final int FACE = 37017;
    private static final int BODY = 221488;

    private static final int PERSON = 0;
    private static final int PASSER = 1;

    private static final int SELECT_NONE = 0;     // 未被选中输出
    private static final int QUICK_RESPONSE = 1;  // 先前没被筛选过 且跟踪时长达到了快速响应的筛选时间
    private static final int TIME_INTERVAL = 2;   // 距离上次筛选的时长达到了时间间隔周期
    private static final int HIGH_QUALITY = 3;    // 质量分数足够高
    private static final int TIMEOUT = 4;         // 跟踪超过最大跟踪时长
    private static final int TRACKING_FINISH = 5; // 跟踪结束

    private boolean updateTrack(Long contextID ,   String innerKey, Integer innerValue ){


        if(trackingMapOutCount.containsKey(contextID)) {
            Map<String, Integer> innerMap = trackingMapOutCount.get(contextID);
            innerMap.put(innerKey, innerValue);
        } else {
            Map<String, Integer> innerMap = new HashMap<>();
            innerMap.put(innerKey, innerValue);
            trackingMapOutCount.put(contextID, innerMap);
        }
        return true;
    }

    private Integer getTrack(Long contextID ,   String innerKey ){


        if(trackingMapOutCount.containsKey(contextID)) {
            Map<String, Integer> innerMap = trackingMapOutCount.get(contextID);
            if(innerMap.containsKey(innerKey))
               return innerMap.get(innerKey);
        }
        return 0;
    }
}