package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler.VideoRecorderAccessor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class essosBag extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor {

    protected ConcurrentHashMap<String, LinkedBlockingQueue<BufferCounter>> bufferCounter = new ConcurrentHashMap<String, LinkedBlockingQueue<BufferCounter>>();
    private final ConcurrentHashMap<String, Long> trackDiffMap = new ConcurrentHashMap<String, Long>();

    @Override
    protected void postProcessOutputValue(ModelResult modelResult) {
        CognitiveEntity.Processor processor = modelResult.getModelRequest().getProcessor();
        String deviceId = (String) modelResult.getModelRequest().getParameter().get("deviceId");
        if (StringUtils.isBlank(deviceId) || processor == null || CollectionUtils.isEmpty(processor.getExtras()))
            return;

        Map<String, Object> bufferMap = processor.fetchBufferMap();
        if (MapUtils.isEmpty(bufferMap))
            return;

        long now = modelResult.getModelRequest().getVideoFrames()[0].getCapturedTime();
        int bufferSize = (Integer) bufferMap.getOrDefault("bufferSize", 5);
        int bufferExpire = (Integer) bufferMap.getOrDefault("bufferExpire", bufferSize * 2 * Objects.requireNonNullElse(processor.getInterval(), 1));
        Number scale = (Number) bufferMap.get("scale");

        float scaleAbs = Math.abs(scale.floatValue());

        List<Map<String, Object>> outputResult = (List<Map<String, Object>>) modelResult.getOutputResult();
        Map<String, List<Map<String, Object>>> roiOutputResult = new HashMap<String, List<Map<String, Object>>>();
        for (Map<String, Object> valueResult : (List<Map<String, Object>>) modelResult.getOutputResult()) {
            Map<String, Integer> detect = (Map<String, Integer>) valueResult.get("detect");
            if (MapUtils.isEmpty(detect))
                continue;

            String[] rois = (String[]) valueResult.get("rois");
            if (ArrayUtils.isEmpty(rois))
                continue;

            for (String roi : rois) {
                List<Map<String, Object>> roiMap = roiOutputResult.get(roi);
                if (roiMap == null) {
                    roiMap = new ArrayList<Map<String, Object>>();
                    roiOutputResult.put(roi, roiMap);
                }

                roiMap.add(valueResult);
            }
        }

        for (Map.Entry<String, List<Map<String, Object>>> entry : roiOutputResult.entrySet()) {
            LinkedBlockingQueue<BufferCounter> counter = bufferCounter.get(deviceId + "_" + entry.getKey());
            if (counter == null) {
                counter = new LinkedBlockingQueue<BufferCounter>();
                bufferCounter.put(deviceId + "_" + entry.getKey(), counter);
            }

            for (BufferCounter first = counter.peek(); !counter.isEmpty() && (first != null && now - first.getTime() > bufferExpire * 1000); first = counter.peek())
                counter.poll();

            if (Utils.instance.watchFrameTiktokLevel == -176) {
                long min = counter.stream().mapToLong(c -> c.getTime()).min().orElse(-1);
                long max = counter.stream().mapToLong(c -> c.getTime()).max().orElse(-1);

                log.warn("key[" + deviceId + "_" + entry.getKey() + "] now[" + now + "] maxTime[" + max + "] minTime[" + min + "] count[" + counter.size() + "] bufferSize[" + bufferSize + "] bufferExpire[" + bufferExpire + "] scale[" + scale + "]");
            }

            if (scale == null) {
                counter.add(new BufferCounter(now, (Map<String, Integer>) entry.getValue().get(0).get("detect"), 0.0f));

                if (counter.size() >= bufferSize)
                    counter.clear();
                else
                    entry.getValue().clear();
            } else {
                Iterator<Map<String, Object>> its = entry.getValue().iterator();
                while (its.hasNext()) {
                    Map<String, Object> target = its.next();
                    Map<String, Integer> detect = (Map<String, Integer>) target.get("detect");

                    BufferCounter current = new BufferCounter(now, detect, scaleAbs);

                    List<BufferCounter> bingos = counter.stream().filter(previous -> current.in(previous)).collect(Collectors.toList());
                    long diffTimeCount = bingos.stream().mapToLong(b -> b.getTime()).distinct().count();
                    if (diffTimeCount < bufferSize) {
                        counter.add(current);
                        its.remove();
                    } else {
                        counter.removeAll(bingos);
                    }
                }
            }
        }

        boolean logged = Utils.instance.watchFrameTiktokLevel == -321;
        if (logged) {
            log.info("roiOutputResult={}, deviceId={}", JSON.toJSONString(roiOutputResult), deviceId);
        }
        Polygon[] polygons = processor.fetchPolygons();
        Map<Integer, Map<String, Object>> roiIndexExtrasMap = (Map<Integer, Map<String, Object>>) processor.fetchExtras(function);
        Long contextID = Utils.keyToContextId(deviceId);

        // 获取 ROI 是否告警
        Map<String, Boolean> roiTrigger = new HashMap<>();
        List<String> roiIds = ((List<Map<String, Object>>) Objects.requireNonNullElse(
                modelResult.getModelRequest().getProcessor().getExtras(), List.of()))
                .stream()
                .filter(item -> "roiIds".equals(item.get("type")))
                .findAny()
                .map(item -> (List<String>) item.getOrDefault("roiIds", List.of()))
                .orElse(List.of());

        for (int index = 0; index < polygons.length; index++) {
            Map<String, Object> extrasMap = roiIndexExtrasMap.getOrDefault(index, Map.of());

            String roiId = roiIds.isEmpty() ? "none" : roiIds.get(index);

            if (MapUtils.isNotEmpty(extrasMap) && extrasMap.containsKey("trigger") && !roiOutputResult.get(roiId).isEmpty()) {
                String triggerKey = contextID + "_" + index;

                Map<String, Object> trigger = (Map<String, Object>) extrasMap.get("trigger");
                // 获取 trackFreq，确保与数据类型的兼容
                Object trackFreqObj = trigger.getOrDefault("trackFreq", 1000);
                long trackFreq;

                if (trackFreqObj instanceof Long) {
                    trackFreq = (Long) trackFreqObj;
                } else if (trackFreqObj instanceof Integer) {
                    trackFreq = ((Integer) trackFreqObj).longValue();
                } else {
                    // 如果trackFreq不是Integer或Long，使用默认值
                    trackFreq = 1000L;
                }
                long lastTrackTime = trackDiffMap.getOrDefault(triggerKey, 0L);

                if (trackDiffMap.containsKey(triggerKey) && lastTrackTime != 0 &&
                        (now - lastTrackTime < trackFreq)) {
                    log.info("esosBag checkFail {}, {}, {},{}, {}", deviceId, now, lastTrackTime, now - lastTrackTime, trackFreq);
                    roiTrigger.put(roiIds.isEmpty() ? "none" : roiIds.get(index), true);
                } else {
                    log.info("esosBag checkTrue {}, {}, {},{}, {}", deviceId, now, lastTrackTime, now - lastTrackTime, trackFreq);
                    roiTrigger.put(roiIds.isEmpty() ? "none" : roiIds.get(index), false);
                    trackDiffMap.put(triggerKey, now);
                }

                // 如果不存在，则 save 当前时间
                if (!trackDiffMap.containsKey(triggerKey)) {
                    trackDiffMap.put(triggerKey, now);
                }
            }
        }
        if (logged) {
            log.info("roiTrigger={}, deviceId={}", roiTrigger, deviceId);
        }

        // 收集需要删除的 ROI
        List<String> keysToRemove = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : roiOutputResult.entrySet()) {
            if (roiTrigger.getOrDefault(entry.getKey(), false)) {
                keysToRemove.add(entry.getKey());
            }
        }
        // 在循环外删除收集的键
        for (String key : keysToRemove) {
            roiOutputResult.remove(key);
        }
        if (logged) {
            log.info("roiOutputResult process end={}, deviceId={}", roiOutputResult, deviceId);
        }

        outputResult = roiOutputResult.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        if (outputResult.isEmpty()) {
            modelResult.setOutputResult(null);
        } else {
            log.info("outputResult{}", JSON.toJSONString(outputResult));
            modelResult.setOutputResult(outputResult);
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private static final Function<Object, Map> function = e -> {
        Map<Integer, Map<String, Object>> result = new HashMap<Integer, Map<String, Object>>();

        if (e != null)
            for (Map<String, Object> param : (List<Map<String, Object>>) e) {
                if (!"mapping".equals(param.get("type")))
                    continue;

                Integer roiIndex = (Integer) param.get("roiIndex");
                if (roiIndex != null)
                    result.put(roiIndex, param);
            }

        return result;
    };

    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {

        if (e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent) e;
            long contextId = Utils.keyToContextId(event.getInfra().getDeviceId());
            startContext(contextId, event.getInfra());
        } else if (e instanceof XworkerStreamClosedEvent) {
            log.info("e.getMesasge{}", e.getAnnotatorName());
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent) e;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException x) {
            }
            stopContext(event.getDeviceId(), Utils.keyToContextId(event.getDeviceId()));
        }
    }

    private void startContext(long contextId, VideoStreamInfra infra) {

        log.info("essosBag start context [" + infra.getDeviceId() + "]. contextID " + contextId);

        trackDiffMap.put(String.valueOf(contextId), System.currentTimeMillis());
    }

    private void stopContext(String deviceId, long contextId) {

        log.info("essosBag stop contextId [" + contextId + "].");

        String prefix = contextId + "_";
        // 收集要删除的键
        List<String> keysToRemove = new ArrayList<>();
        // 遍历 trackDiffMap，找到所有以 prefix 开头的键
        for (String key : trackDiffMap.keySet()) {
            if (key.startsWith(prefix)) {
                keysToRemove.add(key); // 收集与设备相关的键
            }
        }
        // 删除收集的键
        for (String key : keysToRemove) {
            trackDiffMap.remove(key);
        }
        log.info("essosBag trackDiffMap stop contextId [" + contextId + "].");
    }

    @Data
    @Accessors(chain = true)
    protected static class BufferCounter {
        private long time;

        private float centerX;
        private float centerY;

        private float left;
        private float top;
        private float right;
        private float bottom;


        private float leftScale;
        private float topScale;
        private float rightScale;
        private float bottomScale;

        public BufferCounter(long time, Map<String, Integer> detect, float scale) {
            centerX = detect.get("left") + detect.get("width") / 2;
            centerY = detect.get("top") + detect.get("height") / 2;

            left = centerX - (1 + scale) * detect.get("width") / 2;
            top = centerY - (1 + scale) * detect.get("height") / 2;
            right = centerX + (1 + scale) * detect.get("width") / 2;
            bottom = centerY + (1 + scale) * detect.get("height") / 2;

            leftScale = centerX - (1 - scale) * detect.get("width") / 2;
            topScale = centerY - (1 - scale) * detect.get("height") / 2;
            rightScale = centerX + (1 - scale) * detect.get("width") / 2;
            bottomScale = centerY + (1 - scale) * detect.get("height") / 2;


            this.time = time;
        }

        public boolean in(BufferCounter other) {
            boolean aa = centerX <= other.getRight() && centerX >= other.getLeft() && centerY <= other.getBottom() && centerY >= other.getTop();

            boolean bb = centerX <= other.getRightScale() && centerX >= other.getLeftScale() && centerY <= other.getBottomScale() && centerY >= other.getTopScale();
            return aa || bb;
        }
    }
}
