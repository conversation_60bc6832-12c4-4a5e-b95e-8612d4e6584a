package com.sensetime.intersense.cognitivesvc.xworker.xdynamic.code;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.xworker.handler.AbstractXModelHandler;
import com.sensetime.storage.utils.CommonUtils;
import jakarta.annotation.PreDestroy;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.opencv_core.Mat;

import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler.Detection;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamClosedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.event.XworkerStreamStartedEvent;
import com.sensetime.intersense.cognitivesvc.xworker.handler.DynamicXModelHandler;
import com.sun.jna.ptr.PointerByReference;
import com.sun.jna.Pointer;

import lombok.Getter;

public class Difference extends DynamicXModelHandler implements DynamicXModelHandler.VideoRecorderAccessor{
    
    //<contextid + trackid, sclar>
    protected final ConcurrentHashMap<String, Pair<VideoFrame, Double>> initializedMap = new ConcurrentHashMap<String, Pair<VideoFrame, Double>>();
    
    protected final double  minDiffRate = 7;//如果两针的差分值超过initial初始差分值比率高于这个rate就认为两针比对异常
    
    protected final int     minDiffCount = 3;//这一次比对的异常帧对的数量，超过了就发消息
    
    protected final boolean useSingleFrame = false;//true表示所有帧与视频第一帧进行差分,  false表示每次batch内进行差分比对
    
    protected final int     expireToRefresh = Integer.MAX_VALUE;//useSingleFrame = true 的情况下 多久刷新一次initializedMap
    
    protected final double  expireMaxDiffRate = 2;//useSingleFrame = true 的情况下 刷新初始帧时 新帧与旧帧的差分不高于原始帧差分的比率
    
    @Getter
    protected final Boolean noBatch = true;//本地线程跑
    
    @Getter
    protected final Boolean needContext = true;//本地线程跑
    
    @Getter
    protected final Boolean blocking = true;
    
    @Getter
    protected final Integer interval = 25;//2 秒
    
    @Getter
    protected final Integer minBatchSize = 8;
    
    @Getter
    protected final Integer minBatchStagger = 1;
    
    @Getter
    protected final boolean queueMap = true;
    
    @Override
    protected PointerByReference[] batch_extract_xmodel_asap(List<BatchItem> handlingList) {
        if (status != 0)
            return new PointerByReference[0];
        
        ModelRequest modelRequest = handlingList.get(0).getModelRequest();
        VideoFrame[] videoFrames = modelRequest.getVideoFrames();
        String deviceId = modelRequest.getParameter().get("deviceId").toString();
        
        Detection.Rect[] rois = modelRequest.getProcessor().rectRoi();
        if(ArrayUtils.isEmpty(rois)) 
            return new PointerByReference[0];
        
        int     expireToRefresh   = ((Number)getExtraValue(modelRequest.getProcessor().getExtras(), "expireToRefresh"  , this.expireToRefresh)).intValue();
        double  expireMaxDiffRate = ((Number) getExtraValue(modelRequest.getProcessor().getExtras(), "expireMaxDiffRate", this.expireMaxDiffRate)).doubleValue();
        Boolean useSingleFrame    = (Boolean)getExtraValue(modelRequest.getProcessor().getExtras(), "useSingleFrame"   , this.useSingleFrame);
        
        PointerByReference outputKeson = new PointerByReference(KestrelApi.keson_create_array());
        
        for(int index = 0; index < rois.length; index ++) {
            String key = deviceId + "_" + index;
            
            Pair<VideoFrame, Double> initialPair = initializedMap.get(key);
            if(initialPair == null) {
                Double[] dstScalars = caculateBatch(videoFrames, rois[index]);
                Double avgScalar = Math.abs(Arrays.stream(dstScalars).mapToDouble(item -> item.floatValue()).average().getAsDouble());
                
                String useSingleFramePath = (String)getExtraValue(modelRequest.getProcessor().getExtras(), "useSingleFramePath", "");

                //FrameUtils.decode_load_frame_path(useSingleFramePath);

                VideoFrame frames = null;
                // general保持原来模式
                if(CommonUtils.matchesOsgImageUrl(useSingleFramePath)){
                    byte[] bytes = new byte[0];
                    try {
                        bytes = fileAccessor.readImage(useSingleFramePath);
                    } catch (Exception e) {
                        //throw new RuntimeException(e);
                        e.printStackTrace();
                    }

                    frames = FrameUtils.decode_load_frame_memory(bytes);
                }else {
                    frames = FrameUtils.decode_load_frame_path(useSingleFramePath);
                }


                VideoFrame videoFrame = StringUtils.isBlank(useSingleFramePath) ? videoFrames[0].ref() : frames;
                initializedMap.put(key, new MutablePair<VideoFrame, Double>(videoFrame, avgScalar));
            }else {
                Pointer subArray = KestrelApi.keson_create_array();
                KestrelApi.keson_add_item_to_array(outputKeson.getValue(), subArray);
                
                if(useSingleFrame) {
                    Double dstScalar = caculateSingle(videoFrames[videoFrames.length - 1], initialPair.getLeft(), rois[index]);
                    KestrelApi.keson_add_item_to_array(subArray, KestrelApi.keson_create_double(dstScalar));

                    long expire = videoFrames[videoFrames.length - 1].getCapturedTime() - initialPair.getLeft().getCapturedTime();
                    if(expire > expireToRefresh && dstScalar < expireMaxDiffRate * initialPair.getRight()) {
                        Pair<VideoFrame, Double> previous = initializedMap.put(key, new MutablePair<VideoFrame, Double>(videoFrames[videoFrames.length - 1].ref(), initialPair.getRight()));
                        if(previous != null)
                            FrameUtils.batch_free_frame(previous.getLeft());
                    }
                }else {
                    Double[] dstScalars = caculateBatch(videoFrames, rois[index]);
                    for(Double dstScalar: dstScalars)
                        KestrelApi.keson_add_item_to_array(subArray, KestrelApi.keson_create_double(dstScalar));
                }
            }
        }
        
        return new PointerByReference[] {outputKeson};
    }

    @Override
    protected void batch_handle_extract_result(List<BatchItem> handlingList, PointerByReference[] outputKesons) {
        if(ArrayUtils.isNotEmpty(outputKesons))
            handlingList.get(0).setKeson(outputKesons[0]);
    }
    
    @Override
    public void readModelResult(ModelResult modelResult) {
        if (modelResult.getKeson() != null)
            modelResult.setDetectResult(KesonUtils.kesonToJson(modelResult.getKeson()));
    }
    
    @SuppressWarnings({ "unchecked"})
    @Override
    public boolean validateOutputValue(ModelResult modelResult) {
        if(modelResult.getDetectResult() == null)
            return false;
        
        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();
        
        double minDiffRate  = ((Number)getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "minDiffRate",  this.minDiffRate)).doubleValue();
        int    minDiffCount = ((Number)getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "minDiffCount", this.minDiffCount)).intValue();
        boolean useSingleFrame = (Boolean)getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "useSingleFrame", this.useSingleFrame);
        
        List<List<Double>> array = (List<List<Double>>)modelResult.getDetectResult();
        for(int index = 0; index < array.size(); index ++) {
            List<Double> subArray = (List<Double>)array.get(index);
            Pair<VideoFrame, Double> initialPair = initializedMap.get(deviceId + "_" + index);
            long errorCount = subArray.stream().map(scalar -> scalar / initialPair.getRight()).filter(rate -> rate > minDiffRate).count();
            
            if((useSingleFrame && errorCount <= 0) || (!useSingleFrame && errorCount < minDiffCount))
                array.set(index, null);
        }
        
        return array.stream().filter(Objects::nonNull).count() > 0;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void buildOutputValue(ModelResult modelResult) {
        List<List<Double>> array = (List<List<Double>>)modelResult.getDetectResult();
        List<Map<String, Object>> outputResults = new ArrayList<Map<String, Object>>();
        
        String deviceId = modelResult.getModelRequest().getParameter().get("deviceId").toString();
        Detection.Rect[] rois = modelResult.getModelRequest().getProcessor().rectRoi();
        
        List<String> roiIdStrings = Objects.requireNonNullElse(modelResult.getModelRequest().getProcessor().getExtras(), List.of())
                .stream()
                .filter(extra -> Processor.ROIIDS.equals(((Map<String, Object>)extra).get("type")))
                .map(extra -> (List<String>)((Map<String, Object>)extra).getOrDefault(Processor.ROIIDS, List.of()))
                .findAny()
                .orElse(List.of());
        
        double minDiffRate  = ((Number)getExtraValue(modelResult.getModelRequest().getProcessor().getExtras(), "minDiffRate",  this.minDiffRate)).doubleValue();
        
        for(int index = 0; index < array.size(); index ++) {
            List<Double> subArray = array.get(index);
            if(subArray == null)
                continue;

            Detection.Rect roi = rois[index];
            Pair<VideoFrame, Double> initialPair = initializedMap.get(deviceId + "_" + index);
            List<Double> diffArray = subArray.stream().map(scalar -> scalar / initialPair.getRight()).collect(Collectors.toList());
            long errorCount = subArray.stream().map(scalar -> scalar / initialPair.getRight()).filter(scalar -> scalar > minDiffRate).count();
            
            Map<String, Object> outputResult = new HashMap<String, Object>();
            outputResults.add(outputResult);
            subArray.stream().mapToDouble(d -> d.doubleValue()).sum();
            outputResult.put("deviceId", modelResult.getModelRequest().getParameter().get("deviceId"));
            outputResult.put("detect", Map.of("left", roi.getLeft(), "top", roi.getTop(), "width", roi.getWidth(), "height", roi.getHeight()));
            outputResult.put("targetType", 2587);
            outputResult.put("attributes", List.of(
                    Map.of("key", "diffArray", "value", diffArray, "confidence", 1.0f), 
                    Map.of("key", "diffCount", "value", errorCount, "confidence", 1.0f)));
            outputResult.put("rois", new String[] {roiIdStrings.get(index)});
        }
        
        modelResult.setOutputResult(outputResults);
        postProcessOutputValue(modelResult);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<Drawing> draw(ModelResult modelResult) {
        List<Drawing> result = new ArrayList<Drawing>();
        
        List<Map<String, Object>> outputResults = (List<Map<String, Object>>)Objects.requireNonNullElse(modelResult.getOutputResult(), List.of());
        for(Map<String, Object> output : outputResults) {
            Map<String, Number> detect = (Map<String, Number>)output.get("detect");
            result.add(Rect.builder().processor(annotatorName()).top(detect.get("top").intValue()).left(detect.get("left").intValue()).width(detect.get("width").intValue()).height(detect.get("height").intValue()).build());
        }
        
        return result;
    }
    
    @Override
    public synchronized void onWorkerEvent(XworkerEvent e) {
        if(e instanceof XworkerStreamStartedEvent) {
            XworkerStreamStartedEvent event = (XworkerStreamStartedEvent)e;
            event.toString();
        }else if(e instanceof XworkerStreamClosedEvent) {
            XworkerStreamClosedEvent event = (XworkerStreamClosedEvent)e;
            
            try { Thread.sleep(1000); } catch (InterruptedException x) { }
            
            Iterator<Entry<String, Pair<VideoFrame, Double>>> its = initializedMap.entrySet().iterator();
            while(its.hasNext()) {
                Entry<String, Pair<VideoFrame, Double>> entry = its.next();
                if(entry.getKey().startsWith(event.getDeviceId())) {
                    its.remove();
                    FrameUtils.batch_free_frame(entry.getValue().getLeft());
                }
            }
        }
    }
    
    @PreDestroy
    @Override
    public synchronized void destroy() {        
        super.destroy();
        
        for(Pair<VideoFrame, Double> pair : initializedMap.values())
            FrameUtils.batch_free_frame(pair.getLeft());
        
        initializedMap.clear();
    }
    
    private static Double[] caculateBatch(VideoFrame[] videoFrames, Detection.Rect roi) {
        VideoFrame previousFrames[] = Arrays.stream(videoFrames).limit(videoFrames.length - 1).toArray(VideoFrame[]::new);
        VideoFrame targetFrame = videoFrames[videoFrames.length - 1];
        
        Pointer targetRoiFrame = FrameUtils.roi_frame(targetFrame.getCpuFrame(), roi.getLeft(), roi.getTop(), roi.getWidth(), roi.getHeight());
        Pointer targetRoiGrayFrame = FrameUtils.ref_or_cvtcolor_frame(targetRoiFrame, KestrelApi.KESTREL_VIDEO_GRAY);
        
        Pointer[] previousRoiFrames = Arrays.stream(previousFrames)
                .map(v -> FrameUtils.roi_frame(v.getCpuFrame(), roi.getLeft(), roi.getTop(), roi.getWidth(), roi.getHeight()))
                .toArray(Pointer[]::new);
        Pointer[] previousRoiGrayFrames = Arrays.stream(previousRoiFrames)
                .map(frame -> FrameUtils.ref_or_cvtcolor_frame(frame, KestrelApi.KESTREL_VIDEO_GRAY))
                .toArray(Pointer[]::new);
        
        try {
            Mat targetMat = FrameUtils.grayFrameToMat(targetRoiGrayFrame);
            Mat[] previousMats = Arrays.stream(previousRoiGrayFrames)
                    .map(frame -> FrameUtils.grayFrameToMat(frame))
                    .toArray(Mat[]::new);
            
            Function<Mat, Mat> mapper = previous -> {
                Mat dst = new Mat();
                opencv_core.absdiff(previous, targetMat, dst);
                return dst;
            };
            
            Mat[] dstMats = Arrays.stream(previousMats).map(mapper).toArray(Mat[]::new);
            Double[] dstScalars = Arrays.stream(dstMats).map(dst -> opencv_core.sumElems(dst)).map(dstScalar -> dstScalar.get(0)).toArray(Double[]::new);
            return dstScalars;
        }finally {
            FrameUtils.batch_free_frame(targetRoiFrame, targetRoiGrayFrame);
            FrameUtils.batch_free_frame(previousRoiFrames);
            FrameUtils.batch_free_frame(previousRoiGrayFrames);
        }
    }
    
    private static Double caculateSingle(VideoFrame targetFrame, VideoFrame previousFrame, Detection.Rect roi) {
        Pointer targetRoiFrame = FrameUtils.roi_frame(targetFrame.getFrame(), roi.getLeft(), roi.getTop(), roi.getWidth(), roi.getHeight());
        Pointer targetRoiGrayFrame = FrameUtils.ref_or_cvtcolor_frame(targetRoiFrame, KestrelApi.KESTREL_VIDEO_GRAY);
        
        Pointer initialRoiFrame = FrameUtils.roi_frame(previousFrame.getFrame(), roi.getLeft(), roi.getTop(), roi.getWidth(), roi.getHeight());
        Pointer initialRoiGrayFrame = FrameUtils.ref_or_cvtcolor_frame(initialRoiFrame, KestrelApi.KESTREL_VIDEO_GRAY);
        
        try {
            Mat targetMat = FrameUtils.grayFrameToMat(targetRoiGrayFrame);
            Mat initialMat = FrameUtils.grayFrameToMat(initialRoiGrayFrame);
            
            Mat dst = new Mat();
            opencv_core.absdiff(initialMat, targetMat, dst);
            Double dstScalar = opencv_core.sumElems(dst).get(0);
            return dstScalar;
        }finally {
            FrameUtils.batch_free_frame(targetRoiFrame, targetRoiGrayFrame);
            FrameUtils.batch_free_frame(initialRoiFrame, initialRoiGrayFrame);
        }
    }
    
    @SuppressWarnings("unchecked")
    private static Object getExtraValue(List<Map<String, Object>> extras, String key, Object def) {
        return Objects.requireNonNullElse(extras, List.of())
                      .stream()
                      .filter(extra -> key.equals(((Map<String, Object>)extra).get("type")))
                      .map(extra -> ((Map<String, Object>)extra).getOrDefault("value", def))
                      .findAny()
                      .orElse(def);

    }
}

/**
[{
    "processor": "difference",
    "roi": [
        [
            [973,431],
            [1585,577],
            [1433,1059],
            [855,855]
        ]
    ],
    "interval": 25,//检测1跳N
    "extras":[{
        "type": "roiIds",
        "roiIds": ["A"]
    },{
        "type": "minDiffRate",//如果两针的差分值超过initial初始差分值比率高于这个rate就认为两针比对异常 默认700%
        "value": 7.0
    },{
        "type": "minDiffCount",//这一batch比对的异常帧对的数量，超过了就发消息  默认3次
        "value": 3
    },{
        "type": "useSingleFrame",//true表示所有帧与视频第一帧进行差分,  false表示每次batch(8帧)内进行差分比对
        "value": false
    },{
        "type": "expireToRefresh",//可选，useSingleFrame = true 的情况下 多久刷新一次initial帧值
        "value": 2147483647
    },{
        "type": "expireMaxDiffRate",//可选，useSingleFrame = true 的情况下 刷新initial帧时 新帧与旧帧的差分不高于原始帧差分的比率
        "value": 2.0
    },{
        "type": "useSingleFramePath",//可选，useSingleFrame = true 的情况下 使用fetchframe的场景大图作为初始帧。默认不使用。
        "value": "/images/cognitivesvc/COG.txt"
    },{
        "type": "buffer",//与背包检测的buffer相同，但不包含"scale"
        "bufferSize": 3,
        "bufferExpire": 10
    }]
}]
*/