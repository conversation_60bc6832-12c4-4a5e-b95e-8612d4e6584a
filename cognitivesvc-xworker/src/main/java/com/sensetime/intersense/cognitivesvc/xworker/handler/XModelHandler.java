package com.sensetime.intersense.cognitivesvc.xworker.handler;

import java.util.Map;

import org.springframework.core.Ordered;

import com.google.common.collect.Maps;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.Processor;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

public interface XModelHandler{
    
	/** 开启模型 */
	void initialize();
	
	/** 关闭模型 */
	void destroy();
    
    /**  返回处理类的名称
     *
     * @return
     */
    String annotatorName();
    
    /** 传入一批图片的param keson 图片数量不超过batch_size
     *
     * @param frame
     * @return 模型执行结果
     */
    ModelResult handle(ModelRequest modelParam);
    
    /**
     * handle方法返回的keson读取出来成为java对象
     *
     * @param result_keson
     * @return <对象名，对象体>
     */
    void readModelResult(ModelResult modelResult);
    
    /** 对检测的结果是否符合传入条件进行检查
     * @param detectedResult
     * @param processor
     * @return 是否有有效的结果
     */
    boolean validateOutputValue(ModelResult modelResult);
    
    /** 转换检测结果消息体为最终发送卡夫卡消息体中的属性 
     * @param detectedResult
     * @param modelResult
     * @return 输出到消息体中的数据
     */
    void buildOutputValue(ModelResult modelResult);
    
    /**
     * handle方法返回的keson读取出来成为java对象
     * @param result_keson
     * @return <对象名，对象体>
     */
    void releaseModelResult(ModelResult modelResult);
    
    @Getter
    @Builder
    @Accessors(chain = true)
    public static class ModelRequest{
        /**模型 */
        private XModelHandler handler;
        
    	/**处理器 */
        @Setter
    	private Processor processor;
        
    	/**用数组 表示一次批量 多张图片一次来跑 */
    	private VideoFrame[] videoFrames;
    	
    	/**传递参数 */
        @Builder.Default
        private Map<String, Object> parameter = Maps.newHashMap();
    }
    
    @Getter
    @Setter
    @Builder
    @Accessors(chain = true)
    public static class ModelResult{
    	/**入参帧 */
    	private ModelRequest modelRequest;
    	/**模型原始返回值 */
    	private PointerByReference keson;
    	/**转码返回值 */
    	private Object detectResult;
		/**外输结果 */
    	private Object outputResult;
    }
    
    public static interface KestrelInterceptor extends Ordered{
    	String name();
    	
    	default int getOrder() { return 0; }
    	
    	default void beforeExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {}
    	
    	default void afterExecModel(Map<String, Object> additional, int index, PointerByReference originInputKeson, PointerByReference[] outputKesons, PointerByReference inTo, PointerByReference outOf) {}
    	
    	default void postReadDetectResult(Map<String, Object> additional, PointerByReference keson, Object detectResult) {}
    	
    	default boolean validateOutput(Map<String, Object> additional, Map<String, Object> target) { return true; }

    	default void postProcessOutput(Map<String, Object> additional, Map<String, Object> target, Map<String, Object> value) { }
    }
}
