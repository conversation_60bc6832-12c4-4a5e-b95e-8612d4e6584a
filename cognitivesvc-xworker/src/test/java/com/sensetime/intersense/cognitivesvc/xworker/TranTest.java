package com.sensetime.intersense.cognitivesvc.xworker;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.xworker.vo.CameraCalibrationResponse;
import com.sensetime.intersense.cognitivesvc.xworker.vo.VehicleSpeedResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/12 19:16
 */
@SpringBootTest(classes = SpringBootApplication.class)
@Slf4j
public class TranTest {

    private final String tranHost =  "http://***********:32001";

    @Test
    void callCameraCalibration() throws IOException {
        String url = tranHost + "/tran/camera-calibration";

        ObjectMapper objectMapper = new ObjectMapper();
        ClassPathResource resource = new ClassPathResource("calibration_input.json");
        List<Map<String, Object>> items = objectMapper.readValue(resource.getInputStream(), new TypeReference<>() {});
        JSONObject request = new JSONObject();
        request.put("items", items);
        CameraCalibrationResponse response = RestUtils.restTemplate60000ms.postForObject(url, request, CameraCalibrationResponse.class);
        log.info("callCameraCalibration response: {}", JSON.toJSONString(response));
    }

    @Test
    void callVehicleSpeed() throws IOException {
        String url = tranHost + "/tran/vehicle-speed";

        ObjectMapper objectMapper = new ObjectMapper();
        ClassPathResource resource = new ClassPathResource("speed_input.json");
        Map<String, Object> request = objectMapper.readValue(resource.getInputStream(), new TypeReference<>() {});
        VehicleSpeedResponse response = RestUtils.restTemplate60000ms.postForObject(url, request, VehicleSpeedResponse.class);
        log.info("callVehicleSpeed response: {}", JSON.toJSONString(response));
    }

}
