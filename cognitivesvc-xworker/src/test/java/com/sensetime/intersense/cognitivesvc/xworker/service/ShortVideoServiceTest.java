package com.sensetime.intersense.cognitivesvc.xworker.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ShortVideoService 测试类
 */
@SpringBootTest
@TestPropertySource(properties = {
    "shortvideo.host=http://*************:31891",
    "shortvideo.recordUrl=/v1/records",
    "shortvideo.recordAtOffsetSeconds=120",
    "shortvideo.enableRecordAtOffset=true"
})
class ShortVideoServiceTest {

    @Test
    void testGetRecordIdWithBlankStreamId() {
        ShortVideoService service = new ShortVideoService();
        String recordId = service.getRecordId("");
        assertNull(recordId);
    }

    @Test
    void testGetRecordIdWithNullStreamId() {
        ShortVideoService service = new ShortVideoService();
        String recordId = service.getRecordId(null);
        assertNull(recordId);
    }

    @Test
    void testRecordAtOffsetConfiguration() {
        // 测试时间偏移配置是否正确读取
        // 注意：这个测试只验证配置读取，不进行实际的HTTP调用
        ShortVideoService service = new ShortVideoService();

        // 由于配置了 recordAtOffsetSeconds=120 和 enableRecordAtOffset=true
        // 实际的时间偏移逻辑会在 getRecordInfo 方法中执行
        // 这里只测试方法不会抛出异常
        assertDoesNotThrow(() -> {
            service.getRecordId("");  // 空字符串会直接返回null，不会进行HTTP调用
        });
    }

    // 注意：这个测试需要实际的 shortVideo 服务运行
    // @Test
    // void testGetRecordInfoWithValidRecordStreamId() {
    //     ShortVideoService service = new ShortVideoService();
    //     ShortVideoService.RecordInfo recordInfo = service.getRecordInfo("1");
    //     assertNotNull(recordInfo);
    //     assertNotNull(recordInfo.getRecordId());
    //     assertNotNull(recordInfo.getPrerecordSeconds());
    //     assertNotNull(recordInfo.getAppendRecordSeconds());
    // }

    // @Test
    // void testGetRecordIdWithValidRecordStreamId() {
    //     ShortVideoService service = new ShortVideoService();
    //     String recordId = service.getRecordId("1");
    //     assertNotNull(recordId);
    // }
}
