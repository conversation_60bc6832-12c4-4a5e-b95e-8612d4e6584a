# EOF 错误排查指南

## 问题描述
人多的视频流会报 EOF 错误，人少的视频流不会。

## 原因分析

### 1. 处理负载过重
- **人脸检测时间增加**: 人数越多，检测和识别时间越长
- **GPU内存不足**: 多人场景需要更多GPU内存处理
- **CPU处理瓶颈**: 特征提取和比对计算量增加

### 2. 线程池资源耗尽
- **任务积压**: 处理时间长导致任务队列满
- **线程阻塞**: 长时间处理导致线程池耗尽
- **内存泄漏**: 未及时释放的资源累积

### 3. 网络和流相关
- **拉流超时**: 处理延迟导致流缓冲区溢出
- **解码器超时**: 解码器等待时间过长
- **流重连失败**: EOF后重连机制失效

## 解决方案

### 1. 配置优化
已在 `application-stg.yml` 中添加以下优化配置：

```yaml
# 超时时间增加
video:
  status:
    streamOpenTimeout: 10000  # 从5000增加到10000
    videoFrameNullCount: 60   # 从40增加到60
    streamReopenSleepTime: 5  # 从3增加到5

# GPU内存限制降低
video:
  gpu:
    mem:
      seen:
        rate:
          limit: 0.7          # 从0.8降低到0.7
      nonseen:
        rate:
          limit: 0.7
      xworker:
        rate:
          limit: 0.7

# 线程池优化
senseyex:
  video:
    stability:
      threadPoolNum: 512      # 增加线程池大小
    threadSelectPool: true    # 启用智能线程池
```

### 2. 代码优化
- **shortVideo服务缓存**: 添加30秒缓存避免重复调用
- **超时时间缩短**: 使用4秒超时避免长时间阻塞
- **异步处理**: 减少主线程阻塞时间

### 3. 监控和诊断

#### 检查GPU内存使用
```bash
nvidia-smi
```

#### 检查线程池状态
查看日志中的 ThreadPoolStatus 信息：
```
ThreadPoolStatus: Active Threads: X, Pool Size: Y, Queue Size: Z
```

#### 检查处理时间
查看日志中的 Cost 信息：
```
[VideoHandleLog] [Cost] frame deviceId: xxx, from capture to kafka time cost: XXX ms
```

### 4. 性能调优建议

#### 4.1 硬件层面
- **增加GPU内存**: 如果可能，使用更大显存的GPU
- **增加CPU核心**: 提高并行处理能力
- **优化网络**: 确保网络带宽充足

#### 4.2 算法层面
- **降低检测精度**: 在人多场景下适当降低检测阈值
- **限制检测人数**: 设置最大检测人数上限
- **跳帧处理**: 在高负载时跳过部分帧

#### 4.3 系统层面
- **分流处理**: 将高负载流分配到不同的处理节点
- **负载均衡**: 动态调整流的分配
- **资源隔离**: 为不同类型的流分配独立资源

## 实时监控脚本

### 检查EOF错误频率
```bash
# 查看最近1小时的EOF错误
grep "KPLUGIN_E_EOF" /path/to/logs/*.log | grep "$(date '+%Y-%m-%d %H')" | wc -l

# 查看特定设备的EOF错误
grep "KPLUGIN_E_EOF" /path/to/logs/*.log | grep "deviceId:YOUR_DEVICE_ID"
```

### 检查处理时间
```bash
# 查看处理时间超过2秒的记录
grep "frame long cost" /path/to/logs/*.log | grep "$(date '+%Y-%m-%d')"

# 统计平均处理时间
grep "from capture to kafka time cost" /path/to/logs/*.log | awk '{print $NF}' | sed 's/ms//' | awk '{sum+=$1; count++} END {print "Average:", sum/count, "ms"}'
```

## 应急处理

### 1. 临时禁用shortVideo服务
```yaml
shortvideo:
  enable: false
```

### 2. 降低处理负载
```yaml
# 减少线程池大小
senseyex:
  video:
    stability:
      threadPoolNum: 256

# 增加跳帧
video:
  tracker:
    seen:
      count: 5              # 增加跳帧数
```

### 3. 重启服务
如果问题严重，考虑重启相关服务以清理资源。

## 长期优化建议

1. **实现自适应处理**: 根据检测到的人数动态调整处理策略
2. **添加熔断机制**: 在高负载时自动降级处理
3. **优化算法**: 使用更高效的人脸检测和识别算法
4. **分布式处理**: 将处理负载分散到多个节点
5. **资源预测**: 根据历史数据预测资源需求并提前分配
