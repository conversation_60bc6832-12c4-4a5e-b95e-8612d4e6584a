<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>cognitivesvc-xworker</artifactId>
	<packaging>jar</packaging>

	<parent>
		<groupId>com.sensetime.intersense</groupId>
		<artifactId>cognitivesvc</artifactId>
		<version>2.14.0-SNAPSHOT</version>
		<relativePath>../</relativePath>
	</parent>

	<dependencies>
		<dependency>
			<groupId>com.sensetime.intersense</groupId>
			<artifactId>cognitivesvc-server</artifactId>
			<version>2.14.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<version>3.1.11</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
