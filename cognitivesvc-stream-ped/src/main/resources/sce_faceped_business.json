{"source_id": 496176543, "context_id": 496176543, "streams": [{"name": "video_face_body_track_stream", "source_id": 496176543, "context_id": 496176543, "module_plugins": ["detection.fmd", "multiple_target_tracking.fmd", "slice.fmd", "concat.fmd", "face_body_matching.fmd", "plugin.fmd", "target_filter.fmd", "mergence.fmd", "matching_result_conversion.fmd", "operation.fmd"], "modules": [{"name": "input", "source_id": 496176543, "context_id": 496176543, "type": "Input", "parallel_group": "input", "inputs": [], "outputs": ["images"]}, {"name": "facebody_detector", "source_id": 496176543, "context_id": 496176543, "type": "Detection", "parallel_group": "facebody_detector", "inputs": ["images"], "outputs": ["detected_facebodys"], "config": {"plugin": "hunter", "model": "/usr/cognitivesvc/KM_hunter_facebody_nart_cuda11.0-trt7.1-fp16-T4_b32_4.2.2.model", "max_batch_size": 128, "confidence_threshold": 0.65, "model_key": "face_body_detection"}}, {"name": "body_filter", "source_id": 496176543, "context_id": 496176543, "type": "BodyFilter", "parallel_group": "body_filter", "inputs": ["detected_facebodys"], "outputs": ["detected_facebodys"]}, {"name": "face_body_tracker", "source_id": 496176543, "context_id": 496176543, "type": "MultipleTargetTracking", "parallel_group": "face_body_tracker", "inputs": ["detected_facebodys"], "outputs": ["tracked_facebodys", "dropped_ids"]}, {"name": "face_body_matching", "source_id": 496176543, "context_id": 496176543, "type": "FaceBodyMatching", "parallel_group": "face_body_matching", "inputs": ["tracked_facebodys"], "outputs": ["matching_targets"], "config": {"plugin": "cupid"}}, {"name": "match_result_converse", "source_id": 496176543, "context_id": 496176543, "type": "MatchingResultConversion", "parallel_group": "match_result_converse", "inputs": ["matching_targets", "tracked_facebodys"], "outputs": ["associated_facebodys"]}, {"name": "remove_feature", "source_id": 496176543, "context_id": 496176543, "type": "Operation", "parallel_group": "remove_feature", "inputs": ["associated_facebodys"], "outputs": ["associated_facebodys"], "config": {"operations": [{"cmd": "remove", "args": ["feature"]}]}}, {"name": "output", "source_id": 496176543, "context_id": 496176543, "type": "Output", "parallel_group": "output", "inputs": ["associated_facebodys", "dropped_ids", "images"], "outputs": []}]}, {"name": "video_face_body_analyze_stream", "module_plugins": ["slice.fmd", "concat.fmd", "plugin.fmd", "operation.fmd", "mergence.fmd", "target_selection_287.fmd", "refinement.fmd", "roi_filter.fmd", "face_quality.fmd", "face_quality_filter.fmd", "lightweight_targets_slice.fmd", "face_body_matching_filter.fmd", "multidim_face_quality.fmd", "binary_classification_filter.fmd"], "modules": [{"name": "input", "type": "Input", "parallel_group": "input", "inputs": [], "outputs": ["associated_facebodys", "dropped_ids", "images"]}, {"name": "target_slice", "type": "Slice", "parallel_group": "quality", "inputs": ["associated_facebodys"], "outputs": ["face_targets", "body_targets"], "config": {"by_labels": [37017, 221488]}}, {"name": "face_quality_calculate", "type": "Plugin", "parallel_group": "face_quality_calculate", "inputs": ["face_targets"], "outputs": ["face_quality"], "config": {"plugin": "pageant", "model": "/usr/cognitivesvc/KM_pageant_Pageant_face300_mbn3sigmoid_nart_cuda11.0-trt7.1-fp16-T4_b16_1.1.0.model", "max_batch_size": 16, "model_key": "face_quality"}}, {"name": "body_quality_calculate", "type": "Plugin", "parallel_group": "quality", "inputs": ["body_targets"], "outputs": ["raw_body_quality"], "config": {"plugin": "classifier", "model": "/usr/cognitivesvc/KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.1.model", "max_batch_size": 32, "model_key": "ped_quality"}}, {"name": "body_quality_pack", "type": "Operation", "parallel_group": "quality", "inputs": ["raw_body_quality"], "outputs": ["body_quality"], "config": {"operations": [{"cmd": "move", "args": ["attribute/ped_quality/total", "quality"]}, {"cmd": "remove", "args": ["attribute"]}]}}, {"name": "targets_merge", "type": "Mergence", "parallel_group": "quality", "inputs": ["face_quality", "body_quality", "associated_facebodys"], "outputs": ["merged_targets"]}, {"name": "roi_filter", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parallel_group": "select", "inputs": ["merged_targets"], "outputs": ["filtered_merged_targets"], "config": {"roi_filter": []}}, {"name": "target_selector", "type": "TargetSelection", "parallel_group": "select", "inputs": ["filtered_merged_targets", "dropped_ids"], "outputs": ["target_tracklets"], "config": {"keep_low_quality_target": true, "max_device_memory_usage_per_source": 70, "memory_pool_size": 512, "scene_frame_encoder": {"encoder_plugin": "imagesharp", "force_download": false, "max_ref_frame_size": 25, "scene_frame_encoder_quality": 30}, "selection": [{"label_id": 37017, "roi_expand_ratio": 1.5, "quality_threshold": 0.2, "max_tracklet_item_size": 3, "max_track_time": -1, "quality_step": 0.01}, {"label_id": 221488, "max_track_time": -1, "quality_threshold": 0.3}]}}, {"name": "selected_lightweight_slice", "type": "LightweightTargetsSlice", "parallel_group": "selected_lightweight_slice", "inputs": ["target_tracklets"], "outputs": ["selected_target_tracklets_normal", "selected_target_tracklets_lightweight"], "config": {"target_label_configs": [{"label_id": 37017, "min_width": 32, "min_height": 32}, {"label_id": 37017, "min_width": 24, "min_height": 72}]}}, {"name": "target_slice1", "type": "Slice", "parallel_group": "select", "inputs": ["selected_target_tracklets_normal"], "outputs": ["face_tracklets", "body_tracklets"], "config": {"by_labels": [37017, 221488]}}, {"name": "face_classifier", "type": "BinaryClassificationFilter", "parallel_group": "face_classifier", "inputs": ["face_tracklets"], "outputs": ["face_tracklets"], "config": {"plugin": "classifier", "model": "/usr/cognitivesvc/KM_classifier_FaceNoFace_cls_sz_nart_cuda11.0-trt7.1-int8-T4_b32_1.0.2.model", "max_batch_size": 128, "support_label": 37017, "enable": true, "confidence_threshold": 0.7, "model_key": "face_binary_classifier"}}, {"name": "face_classifier_filtered_lightweight_slice", "type": "LightweightTargetsSlice", "parallel_group": "face_classifier_filtered_lightweight_slice", "inputs": ["face_tracklets"], "outputs": ["face_tracklets", "face_classifier_filtered_targets_lightweight"]}, {"name": "body_classifier", "type": "BinaryClassificationFilter", "parallel_group": "body_classifier", "inputs": ["body_tracklets"], "outputs": ["body_tracklets"], "config": {"plugin": "classifier", "model": "/usr/cognitivesvc/KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.1.model", "max_batch_size": 128, "support_label": 221488, "enable": false, "confidence_threshold": 0.9, "model_key": "ped_binary_classifier"}}, {"name": "body_classifier_filtered_lightweight_slice", "type": "LightweightTargetsSlice", "parallel_group": "body_classifier_filtered_lightweight_slice", "inputs": ["body_tracklets"], "outputs": ["body_tracklets", "body_classifier_filtered_targets_lightweight"]}, {"name": "aligner", "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["face_tracklets"], "outputs": ["face_landmarks"], "config": {"plugin": "aligner", "model": "/usr/cognitivesvc/KM_aligner_deepface_nart_cuda11.0-trt7.1-fp16-T4_b64_2.17.2.model", "max_batch_size": 64}}, {"name": "face_landmarks_pack", "source_id": 496176543, "context_id": 496176543, "type": "Operation", "parallel_group": "target_analysis", "inputs": ["face_landmarks"], "outputs": ["face_landmarks"], "config": {"operations": [{"cmd": "move", "args": ["confidence", "aligner_confidence"]}]}}, {"name": "face_headpose", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["face_landmarks"], "outputs": ["face_headpose"], "config": {"plugin": "headpose", "model": "/usr/cognitivesvc/KM_headpose__nart_cuda11.0-trt7.1-fp16-T4_b64_1.0.1.model", "max_batch_size": 32}}, {"name": "blur_landmark_headpose_merge", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["face_landmarks", "face_headpose", "face_tracklets"], "outputs": ["face_targets_quality_element"]}, {"name": "face_quality_update", "source_id": 496176543, "context_id": 496176543, "type": "FaceQuality", "parallel_group": "target_analysis", "inputs": ["face_targets_quality_element"], "outputs": ["new_face_tracklets"]}, {"name": "face_multidim_quality", "source_id": 496176543, "context_id": 496176543, "type": "MultidimFaceQuality", "parallel_group": "target_analysis", "inputs": ["new_face_tracklets"], "outputs": ["new_face_tracklets"], "config": {"quality_model": {"plugin": "classifier", "model": "/usr/cognitivesvc/KM_classifier_Face_Quality_Security_nart_cuda11.0-trt7.1-int8-T4_b64_3.1.0.model", "max_batch_size": 128, "model_key": "multi_face_quality"}, "clear_model": {"plugin": "classifier", "model": "/usr/cognitivesvc/KM_classifier_Face_Blur_Security_nart_cuda11.0-trt7.1-int8-T4_b64_3.1.0.model", "max_batch_size": 128, "model_key": "multi_face_quality_blur"}, "clear_score_threshold": 0, "angle_score_threshold": 0, "yaw_angle_threshold": 180, "pitch_angle_threshold": 180, "visible_score_threshold": 0, "shine_score_threshold": 0}}, {"name": "face_multidim_quality_filtered_lightweight_slice", "source_id": 496176543, "context_id": 496176543, "type": "LightweightTargetsSlice", "parallel_group": "target_analysis", "inputs": ["new_face_tracklets"], "outputs": ["new_face_tracklets", "face_multidim_quality_filtered_targets_lightweight"]}, {"name": "face_quality_filter", "source_id": 496176543, "context_id": 496176543, "type": "FaceQualityFilter", "parallel_group": "target_analysis", "inputs": ["new_face_tracklets"], "outputs": ["face_tracklets_with_landmarks"], "config": {"quality_threshold": 0.3}}, {"name": "face_quality_filtered_lightweight_slice", "source_id": 496176543, "context_id": 496176543, "type": "LightweightTargetsSlice", "parallel_group": "target_analysis", "inputs": ["face_tracklets_with_landmarks"], "outputs": ["face_quality_filtered_target_tracklets_normal", "face_quality_filtered_target_tracklets_lightweight"]}, {"name": "face_feature_extraction", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["face_tracklets_with_landmarks"], "outputs": ["face_features"], "config": {"plugin": "feature", "model": "/usr/cognitivesvc/KM_feature_face_nart_cuda11.0-trt7.1-int8-T4_b32_2.52.0.model", "max_batch_size": 32}}, {"name": "body_feature_extraction", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["body_tracklets"], "outputs": ["body_features"], "config": {"plugin": "senu", "model": "/usr/cognitivesvc/KM_senu_ped_nart_cuda11.0-trt7.1-int8-T4_b32_1.54.0.model", "max_batch_size": 32}}, {"name": "face_feature_mergence", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["face_features", "face_tracklets_with_landmarks"], "outputs": ["face_tracklets_with_feature"]}, {"name": "faces_refinement", "source_id": 496176543, "context_id": 496176543, "type": "Refinement", "parallel_group": "target_analysis", "inputs": ["face_tracklets_with_feature"], "outputs": ["best_faces"]}, {"name": "body_feature_mergence", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["body_features", "body_tracklets"], "outputs": ["body_tracklets_with_feature"], "config": {"update": true}}, {"name": "body_refinement", "source_id": 496176543, "context_id": 496176543, "type": "Refinement", "parallel_group": "target_analysis", "inputs": ["body_tracklets_with_feature"], "outputs": ["best_bodys"], "config": {}}, {"name": "face_attribute_extraction", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["best_faces"], "outputs": ["face_attributes"], "config": {"plugin": "attribute", "model": "/usr/cognitivesvc/KM_attribute_face_nart_cuda11.0-trt7.1-int8-T4_b64_3.3.1.model", "max_batch_size": 64}}, {"name": "body_attribute_extraction", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["best_bodys"], "outputs": ["body_attributes"], "config": {"plugin": "classifier", "model": "/usr/cognitivesvc/KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.1.model", "max_batch_size": 16}}, {"name": "face_targets_mergence", "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["face_attributes", "best_faces"], "outputs": ["analyzed_face_targets"]}, {"name": "body_targets_mergence", "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["body_attributes", "best_bodys"], "outputs": ["analyzed_body_targets"]}, {"name": "face_body_concat", "type": "Concat", "parallel_group": "target_analysis", "inputs": ["analyzed_face_targets", "analyzed_body_targets"], "outputs": ["analyzed_targets"], "config": {"concat_items": ["targets"]}}, {"name": "face_body_matching_filter", "type": "FaceBodyMatchingFilter", "parallel_group": "face_body_matching_filter", "inputs": ["analyzed_targets", "images"], "outputs": ["analyzed_targets"], "config": {"aligner": {"plugin": "aligner", "model": "/usr/cognitivesvc/KM_aligner_deepface_nart_cuda11.0-trt7.1-fp16-T4_b64_2.17.2.model", "max_batch_size": 128, "model_key": "face_keypoints"}, "feature": {"plugin": "feature", "model": "/usr/cognitivesvc/KM_feature_face_nart_cuda11.0-trt7.1-int8-T4_b32_2.52.0.model", "max_batch_size": 128, "model_key": "face_feature"}}}, {"name": "lightweight_targets_concat", "type": "Concat", "parallel_group": "face_body_matching_filter", "inputs": ["selected_target_tracklets_lightweight", "face_classifier_filtered_targets_lightweight", "body_classifier_filtered_targets_lightweight", "face_multidim_quality_filtered_targets_lightweight", "face_quality_filtered_target_tracklets_lightweight"], "outputs": ["lightweight_targets"], "config": {"concat_items": ["targets"]}}, {"name": "output", "source_id": 496176543, "context_id": 496176543, "type": "Output", "parallel_group": "output", "inputs": ["analyzed_targets", "lightweight_targets"], "outputs": []}]}]}