{"$schema": "http://kestrel.pages.gitlab.bj.sensetime.com/flock_pipeline_docs/schema/v1.6/pipeline.json", "streams": [{"name": "video_face_body_track_stream", "module_plugins": ["detection.fmd", "multiple_target_tracking.fmd", "slice.fmd", "concat.fmd", "face_body_matching.fmd", "plugin.fmd", "target_filter.fmd", "mergence.fmd", "matching_result_conversion.fmd", "operation.fmd"], "modules": [{"name": "input", "type": "Input", "inputs": [], "outputs": ["images"]}, {"name": "facebody_detector", "type": "Detection", "parallel_group": "facebody_detector", "inputs": ["images"], "outputs": ["detected_facebodys"], "config": {"plugin": "hunter", "model": "model/CUDA11.0_T4/KM_hunter_facebody_nart_cuda11.0-trt7.1-fp16-T4_b32_4.2.0.model", "max_batch_size": 128, "confidence_threshold": 0.3, "model_key": "face_body_detection"}}, {"name": "body_filter", "type": "BodyFilter", "parallel_group": "body_filter", "inputs": ["detected_facebodys"], "outputs": ["detected_facebodys"]}, {"name": "face_body_tracker", "type": "MultipleTargetTracking", "parallel_group": "face_body_tracker", "inputs": ["detected_facebodys"], "outputs": ["tracked_facebodys", "dropped_ids"]}, {"name": "face_body_matching", "type": "FaceBodyMatching", "parallel_group": "face_body_matching", "inputs": ["tracked_facebodys"], "outputs": ["matching_targets"], "config": {"plugin": "cupid"}}, {"name": "match_result_converse", "type": "MatchingResultConversion", "parallel_group": "match_result_converse", "inputs": ["matching_targets", "tracked_facebodys"], "outputs": ["associated_facebodys"]}, {"name": "remove_feature", "type": "Operation", "parallel_group": "remove_feature", "inputs": ["associated_facebodys"], "outputs": ["associated_facebodys"], "config": {"operations": [{"cmd": "remove", "args": ["feature"]}]}}, {"name": "output", "type": "Output", "parallel_group": "output", "inputs": ["associated_facebodys", "dropped_ids", "images"], "outputs": []}]}, {"name": "video_face_body_analyze_stream", "module_plugins": ["slice.fmd", "concat.fmd", "plugin.fmd", "operation.fmd", "mergence.fmd", "target_selection.fmd", "refinement.fmd", "roi_filter.fmd", "face_quality.fmd", "face_quality_filter.fmd", "lightweight_targets_slice.fmd", "face_body_matching_filter.fmd", "multidim_face_quality.fmd", "binary_classification_filter.fmd"], "modules": [{"name": "input", "type": "Input", "inputs": [], "outputs": ["associated_facebodys", "dropped_ids", "images"]}, {"name": "target_slice", "type": "Slice", "parallel_group": "target_slice", "inputs": ["associated_facebodys"], "outputs": ["face_targets", "body_targets"], "config": {"by_labels": [37017, 221488]}}, {"name": "face_quality_calculate", "type": "Plugin", "parallel_group": "face_quality_calculate", "inputs": ["face_targets"], "outputs": ["face_quality"], "config": {"plugin": "pageant", "model": "model/CUDA11.0_T4/KM_pageant_88id_nart_cuda11.0-trt7.1-int8-T4_b16_1.0.8.model", "max_batch_size": 128, "model_key": "face_quality"}}, {"name": "body_quality_calculate", "type": "Plugin", "parallel_group": "body_quality_calculate", "inputs": ["body_targets"], "outputs": ["raw_body_quality"], "config": {"plugin": "classifier", "model": "model/CUDA11.0_T4/KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.1.model", "max_batch_size": 128, "model_key": "ped_quality"}}, {"name": "body_quality_pack", "type": "Operation", "parallel_group": "body_quality_pack", "inputs": ["raw_body_quality"], "outputs": ["body_quality"], "config": {"operations": [{"cmd": "move", "args": ["attribute/ped_quality/total", "quality"]}, {"cmd": "remove", "args": ["attribute"]}]}}, {"name": "targets_merge", "type": "Mergence", "parallel_group": "targets_merge", "inputs": ["face_quality", "body_quality", "associated_facebodys"], "outputs": ["merged_targets"]}, {"name": "roi_filter", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parallel_group": "roi_filter", "inputs": ["merged_targets"], "outputs": ["filtered_merged_targets"], "config": {"roi_filter": []}}, {"name": "target_selector", "type": "TargetSelection", "parallel_group": "target_selector", "inputs": ["filtered_merged_targets", "dropped_ids"], "outputs": ["target_tracklets"], "config": {"keep_low_quality_target": true, "max_device_memory_usage_per_source": 70, "scene_frame_encoder": {"encoder_plugin": "imagesharp", "force_download": false, "max_ref_frame_size": 25, "scene_frame_encoder_quality": 30}, "memory_pool_size": 512, "selection": [{"label_id": 37017, "roi_expand_ratio": 1.5, "quality_threshold": 0.2, "max_tracklet_item_size": 3, "quality_step": 0.01, "max_track_time": 120}, {"label_id": 221488, "quality_threshold": 0.3, "max_track_time": 120}]}}, {"name": "selected_lightweight_slice", "type": "LightweightTargetsSlice", "parallel_group": "selected_lightweight_slice", "inputs": ["target_tracklets"], "outputs": ["selected_target_tracklets_normal", "selected_target_tracklets_lightweight"], "config": {"target_label_configs": [{"label_id": 37017, "min_width": 32, "min_height": 32}, {"label_id": 221488, "min_width": 24, "min_height": 72}]}}, {"name": "target_slice1", "type": "Slice", "parallel_group": "target_slice1", "inputs": ["selected_target_tracklets_normal"], "outputs": ["face_tracklets", "body_tracklets"], "config": {"by_labels": [37017, 221488]}}, {"name": "face_classifier", "type": "BinaryClassificationFilter", "parallel_group": "face_classifier", "inputs": ["face_tracklets"], "outputs": ["face_tracklets"], "config": {"plugin": "classifier", "model": "model/CUDA11.0_T4/KM_classifier_FaceNoFace_cls_sz_nart_cuda11.0-trt7.1-int8-T4_b32_1.0.2.model", "max_batch_size": 128, "support_label": 37017, "enable": true, "confidence_threshold": 0.7, "model_key": "face_binary_classifier"}}, {"name": "face_classifier_filtered_lightweight_slice", "type": "LightweightTargetsSlice", "parallel_group": "face_classifier_filtered_lightweight_slice", "inputs": ["face_tracklets"], "outputs": ["face_tracklets", "face_classifier_filtered_targets_lightweight"]}, {"name": "body_classifier", "type": "BinaryClassificationFilter", "parallel_group": "body_classifier", "inputs": ["body_tracklets"], "outputs": ["body_tracklets"], "config": {"plugin": "classifier", "model": "model/CUDA11.0_T4/KM_classifier_Ped_None_Security_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.0.model", "max_batch_size": 128, "support_label": 221488, "enable": false, "confidence_threshold": 0.9, "model_key": "ped_binary_classifier"}}, {"name": "body_classifier_filtered_lightweight_slice", "type": "LightweightTargetsSlice", "parallel_group": "body_classifier_filtered_lightweight_slice", "inputs": ["body_tracklets"], "outputs": ["body_tracklets", "body_classifier_filtered_targets_lightweight"]}, {"name": "aligner", "type": "Plugin", "parallel_group": "aligner", "inputs": ["face_tracklets"], "outputs": ["face_landmarks"], "config": {"plugin": "aligner", "model": "model/CUDA11.0_T4/KM_aligner_occlusion_106_nart_cuda11.0-trt7.1-fp16-T4_b64_1.20.0.model", "max_batch_size": 128, "model_key": "face_aligner_with_occlusion"}}, {"name": "face_landmarks_pack", "type": "Operation", "parallel_group": "face_landmarks_pack", "inputs": ["face_landmarks"], "outputs": ["face_landmarks"], "config": {"operations": [{"cmd": "move", "args": ["confidence", "aligner_confidence"]}]}}, {"name": "face_headpose", "type": "Plugin", "parallel_group": "face_headpose", "inputs": ["face_landmarks"], "outputs": ["face_headpose"], "config": {"plugin": "headpose", "model": "model/CUDA11.0_T4/KM_headpose__nart_cuda11.0-trt7.1-fp16-T4_b64_1.0.1.model", "max_batch_size": 128, "model_key": "face_headpose"}}, {"name": "blur_landmark_headpose_merge", "type": "Mergence", "parallel_group": "blur_landmark_headpose_merge", "inputs": ["face_landmarks", "face_headpose", "face_tracklets"], "outputs": ["face_targets_quality_element"]}, {"name": "face_quality_update", "type": "FaceQuality", "parallel_group": "face_quality_update", "inputs": ["face_targets_quality_element"], "outputs": ["new_face_tracklets"]}, {"name": "face_multidim_quality", "type": "MultidimFaceQuality", "parallel_group": "face_multidim_quality", "inputs": ["new_face_tracklets"], "outputs": ["new_face_tracklets"], "config": {"quality_model": {"plugin": "classifier", "model": "model/CUDA11.0_T4/KM_classifier_Face_Quality_Security_nart_cuda11.0-trt7.1-int8-T4_b64_3.1.0.model", "max_batch_size": 128, "model_key": "multi_face_quality"}, "clear_model": {"plugin": "classifier", "model": "model/CUDA11.0_T4/KM_classifier_Face_Blur_Security_nart_cuda11.0-trt7.1-int8-T4_b64_3.1.0.model", "max_batch_size": 128, "model_key": "multi_face_quality_blur"}, "clear_score_threshold": 0, "angle_score_threshold": 0, "yaw_angle_threshold": 180, "pitch_angle_threshold": 180, "visible_score_threshold": 0, "shine_score_threshold": 0}}, {"name": "face_multidim_quality_filtered_lightweight_slice", "type": "LightweightTargetsSlice", "parallel_group": "face_multidim_quality_filtered_lightweight_slice", "inputs": ["new_face_tracklets"], "outputs": ["new_face_tracklets", "face_multidim_quality_filtered_targets_lightweight"]}, {"name": "face_quality_filter", "type": "FaceQualityFilter", "parallel_group": "face_quality_filter", "inputs": ["new_face_tracklets"], "outputs": ["face_tracklets_with_landmarks"], "config": {"quality_threshold": 0.3}}, {"name": "face_quality_filtered_lightweight_slice", "type": "LightweightTargetsSlice", "parallel_group": "face_quality_filtered_lightweight_slice", "inputs": ["face_tracklets_with_landmarks"], "outputs": ["face_quality_filtered_target_tracklets_normal", "face_quality_filtered_target_tracklets_lightweight"]}, {"name": "face_feature_extraction", "type": "Plugin", "parallel_group": "face_feature_extraction", "inputs": ["face_quality_filtered_target_tracklets_normal"], "outputs": ["face_features"], "config": {"plugin": "feature", "model": "model/CUDA11.0_T4/KM_feature_face_nart_cuda11.0-trt7.1-int8-T4_b32_2.52.0.model", "max_batch_size": 128, "model_key": "face_feature"}}, {"name": "body_feature_extraction", "type": "Plugin", "parallel_group": "body_feature_extraction", "inputs": ["body_tracklets"], "outputs": ["body_features"], "config": {"plugin": "senu", "model": "model/CUDA11.0_T4/KM_senu_ped_nart_cuda11.0-trt7.1-int8-T4_b32_1.54.0.model", "max_batch_size": 128, "model_key": "ped_feature"}}, {"name": "face_feature_mergence", "type": "Mergence", "parallel_group": "face_feature_mergence", "inputs": ["face_features", "face_quality_filtered_target_tracklets_normal"], "outputs": ["face_tracklets_with_feature"]}, {"name": "faces_refinement", "type": "Refinement", "parallel_group": "faces_refinement", "inputs": ["face_tracklets_with_feature"], "outputs": ["best_faces"]}, {"name": "body_feature_mergence", "type": "Mergence", "parallel_group": "body_feature_mergence", "inputs": ["body_features", "body_tracklets"], "outputs": ["body_tracklets_with_feature"], "config": {"update": true}}, {"name": "body_refinement", "type": "Refinement", "parallel_group": "body_refinement", "inputs": ["body_tracklets_with_feature"], "outputs": ["best_bodys"], "config": {}}, {"name": "face_attribute_extraction", "type": "Plugin", "parallel_group": "face_attribute_extraction", "inputs": ["best_faces"], "outputs": ["face_attributes"], "config": {"plugin": "attribute", "model": "model/CUDA11.0_T4/KM_attribute_face_nart_cuda11.0-trt7.1-int8-T4_b64_3.3.1.model", "max_batch_size": 128, "filter": true, "model_key": "face_attribute"}}, {"name": "body_attribute_extraction", "type": "Plugin", "parallel_group": "body_attribute_extraction", "inputs": ["best_bodys"], "outputs": ["body_attributes"], "config": {"plugin": "classifier", "model": "model/CUDA11.0_T4/KM_classifier_Ped_32of32_nart_cuda11.0-trt7.1-int8-T4_b16_2.14.0.model", "max_batch_size": 128, "filter": true, "model_key": "ped_attribute"}}, {"name": "face_targets_mergence", "type": "Mergence", "parallel_group": "face_targets_mergence", "inputs": ["face_attributes", "best_faces"], "outputs": ["analyzed_face_targets"]}, {"name": "body_targets_mergence", "type": "Mergence", "parallel_group": "body_targets_mergence", "inputs": ["body_attributes", "best_bodys"], "outputs": ["analyzed_body_targets"]}, {"name": "face_body_concat", "type": "Concat", "parallel_group": "face_body_concat", "inputs": ["analyzed_face_targets", "analyzed_body_targets"], "outputs": ["analyzed_targets"], "config": {"concat_items": ["targets"]}}, {"name": "face_body_matching_filter", "type": "FaceBodyMatchingFilter", "parallel_group": "face_body_matching_filter", "inputs": ["analyzed_targets", "images"], "outputs": ["analyzed_targets"], "config": {"aligner": {"plugin": "aligner", "model": "model/CUDA11.0_T4/KM_aligner_deepface_nart_cuda11.0-trt7.1-fp16-T4_b64_2.17.2.model", "max_batch_size": 128, "model_key": "face_keypoints"}, "feature": {"plugin": "feature", "model": "model/CUDA11.0_T4/KM_feature_face_nart_cuda11.0-trt7.1-int8-T4_b32_2.52.0.model", "max_batch_size": 128, "model_key": "face_feature"}}}, {"name": "lightweight_targets_concat", "type": "Concat", "parallel_group": "lightweight_targets_concat", "inputs": ["selected_target_tracklets_lightweight", "face_classifier_filtered_targets_lightweight", "body_classifier_filtered_targets_lightweight", "face_multidim_quality_filtered_targets_lightweight", "face_quality_filtered_target_tracklets_lightweight"], "outputs": ["lightweight_targets"], "config": {"concat_items": ["targets"]}}, {"name": "output", "type": "Output", "parallel_group": "output", "inputs": ["analyzed_targets", "lightweight_targets"], "outputs": []}]}]}