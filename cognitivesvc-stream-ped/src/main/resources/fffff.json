{"source_id": 496176543, "context_id": 496176543, "streams": [{"name": "video_face_body_track_stream", "source_id": 496176543, "context_id": 496176543, "module_plugins": ["detection.fmd", "multiple_target_tracking.fmd", "slice.fmd", "concat.fmd", "face_body_matching.fmd", "plugin.fmd", "target_filter.fmd", "mergence.fmd", "matching_result_conversion.fmd"], "modules": [{"name": "input", "source_id": 496176543, "context_id": 496176543, "type": "Input", "parallel_group": "input", "inputs": [], "outputs": ["images"]}, {"name": "face_detector", "type": "Detection", "parallel_group": "detect", "inputs": ["images"], "outputs": ["detected_faces_small"], "config": {"plugin": "hunter", "model": "/usr/cognitivesvc/K<PERSON>_hunter_SmallFace_Gray_nart_cuda11.0-trt7.1-int8-T4_b48_4.15.1.model", "max_batch_size": 64, "confidence_threshold": 0.65}}, {"name": "face_target_tracker", "type": "MultipleTargetTracking", "parallel_group": "face_detect", "inputs": ["detected_faces_small"], "outputs": ["tracked_face_targets", "dropped_face_ids"]}, {"name": "facebody_detector", "source_id": 496176543, "context_id": 496176543, "type": "Detection", "parallel_group": "detect", "inputs": ["images"], "outputs": ["detected_facebodys"], "config": {"plugin": "hunter", "model": "/usr/cognitivesvc/KM_hunter_facebody_nart_cuda11.0-trt7.1-fp16-T4_b32_4.2.2.model", "max_batch_size": 32, "confidence_threshold": 0.65}}, {"name": "face_body_tracker", "source_id": 496176543, "context_id": 496176543, "type": "MultipleTargetTracking", "parallel_group": "target_tracking", "inputs": ["detected_facebodys"], "outputs": ["tracked_bodys", "dropped_body_ids"]}, {"name": "facebody_slice", "source_id": 496176543, "context_id": 496176543, "type": "Slice", "parallel_group": "detect", "inputs": ["tracked_bodys"], "outputs": ["body_targets"], "config": {"by_labels": [221488]}}, {"name": "track_targets_concat", "type": "Concat", "parallel_group": "struct_detect", "inputs": ["tracked_face_targets", "body_targets"], "outputs": ["tracked_facebodys"], "config": {"concat_items": ["targets"]}}, {"name": "dropped_ids_concat", "type": "Concat", "parallel_group": "struct_detect", "inputs": ["dropped_face_ids", "dropped_body_ids"], "outputs": ["dropped_ids"], "config": {"concat_items": ["targets"]}}, {"name": "face_body_matching", "source_id": 496176543, "context_id": 496176543, "type": "FaceBodyMatching", "parallel_group": "target_tracking", "inputs": ["tracked_facebodys"], "outputs": ["matching_targets"], "config": {"plugin": "cupid"}}, {"name": "match_result_converse", "source_id": 496176543, "context_id": 496176543, "type": "MatchingResultConversion", "parallel_group": "target_tracking", "inputs": ["matching_targets", "tracked_facebodys"], "outputs": ["associated_facebodys"]}, {"name": "output", "source_id": 496176543, "context_id": 496176543, "type": "Output", "parallel_group": "output", "inputs": ["associated_facebodys", "dropped_ids"], "outputs": []}]}, {"name": "video_face_body_analyze_stream", "source_id": 496176543, "context_id": 496176543, "module_plugins": ["slice.fmd", "concat.fmd", "plugin.fmd", "operation.fmd", "mergence.fmd", "target_selection_287.fmd", "refinement.fmd", "roi_filter.fmd", "face_quality.fmd", "face_quality_filter.fmd"], "modules": [{"name": "input", "source_id": 496176543, "context_id": 496176543, "type": "Input", "parallel_group": "input", "inputs": [], "outputs": ["associated_facebodys", "dropped_ids"]}, {"name": "target_slice", "source_id": 496176543, "context_id": 496176543, "type": "Slice", "parallel_group": "quality", "inputs": ["associated_facebodys"], "outputs": ["face_targets", "body_targets"], "config": {"by_labels": [37017, 221488]}}, {"name": "face_quality_calculate", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "quality", "inputs": ["face_targets"], "outputs": ["face_quality"], "config": {"plugin": "pageant", "model": "/usr/cognitivesvc/KM_pageant_Pageant_face300_mbn3sigmoid_nart_cuda11.0-trt7.1-fp16-T4_b16_1.1.0.model", "max_batch_size": 16}}, {"name": "body_quality_calculate", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "quality", "inputs": ["body_targets"], "outputs": ["raw_body_quality"], "config": {"plugin": "classifier", "model": "/usr/cognitivesvc/KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.1.model", "max_batch_size": 32}}, {"name": "body_quality_pack", "source_id": 496176543, "context_id": 496176543, "type": "Operation", "parallel_group": "quality", "inputs": ["raw_body_quality"], "outputs": ["body_quality"], "config": {"operations": [{"cmd": "move", "args": ["attribute/ped_quality/total", "quality"]}, {"cmd": "remove", "args": ["attribute"]}]}}, {"name": "targets_merge", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "quality", "inputs": ["face_quality", "body_quality", "associated_facebodys"], "outputs": ["merged_targets"]}, {"name": "roi_filter", "source_id": 496176543, "context_id": 496176543, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parallel_group": "select", "inputs": ["merged_targets"], "outputs": ["filtered_merged_targets"], "config": {"roi_filter": []}}, {"name": "target_selector", "source_id": 496176543, "context_id": 496176543, "type": "TargetSelection", "parallel_group": "select", "inputs": ["filtered_merged_targets", "dropped_ids"], "outputs": ["target_tracklets"], "config": {"max_device_memory_usage_per_source": 70, "memory_pool_size": 512, "scene_frame_encoder": {"encoder_plugin": "imagesharp", "force_download": false, "max_ref_frame_size": 25, "scene_frame_encoder_quality": 30}, "selection": [{"label_id": 37017, "roi_expand_ratio": 1.5, "quality_threshold": 0.2, "max_tracklet_item_size": 3, "quality_step": 0.01}, {"label_id": 221488, "quality_threshold": 0.3}]}}, {"name": "target_slice1", "source_id": 496176543, "context_id": 496176543, "type": "Slice", "parallel_group": "select", "inputs": ["target_tracklets"], "outputs": ["face_tracklets", "body_tracklets"], "config": {"by_labels": [37017, 221488]}}, {"name": "aligner", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["face_tracklets"], "outputs": ["face_landmarks"], "config": {"plugin": "aligner", "model": "/usr/cognitivesvc/KM_aligner_deepface_nart_cuda11.0-trt7.1-fp16-T4_b64_2.17.2.model", "max_batch_size": 64}}, {"name": "face_landmarks_pack", "source_id": 496176543, "context_id": 496176543, "type": "Operation", "parallel_group": "target_analysis", "inputs": ["face_landmarks"], "outputs": ["face_landmarks"], "config": {"operations": [{"cmd": "move", "args": ["confidence", "aligner_confidence"]}]}}, {"name": "face_headpose", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["face_landmarks"], "outputs": ["face_headpose"], "config": {"plugin": "headpose", "model": "/usr/cognitivesvc/KM_headpose__nart_cuda11.0-trt7.1-fp16-T4_b64_1.0.1.model", "max_batch_size": 32}}, {"name": "blur_landmark_headpose_merge", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["face_landmarks", "face_headpose", "face_tracklets"], "outputs": ["face_targets_quality_element"]}, {"name": "face_quality_update", "source_id": 496176543, "context_id": 496176543, "type": "FaceQuality", "parallel_group": "target_analysis", "inputs": ["face_targets_quality_element"], "outputs": ["new_face_tracklets"]}, {"name": "face_quality_filter", "source_id": 496176543, "context_id": 496176543, "type": "FaceQualityFilter", "parallel_group": "target_analysis", "inputs": ["new_face_tracklets"], "outputs": ["face_tracklets_with_landmarks"], "config": {"quality_threshold": 0.3}}, {"name": "face_feature_extraction", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["face_tracklets_with_landmarks"], "outputs": ["face_features"], "config": {"plugin": "feature", "model": "/usr/cognitivesvc/KM_feature_face_nart_cuda11.0-trt7.1-int8-T4_b32_2.52.0.model", "max_batch_size": 32}}, {"name": "body_feature_extraction", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["body_tracklets"], "outputs": ["body_features"], "config": {"plugin": "senu", "model": "/usr/cognitivesvc/KM_senu_ped_nart_cuda11.0-trt7.1-int8-T4_b32_1.54.0.model", "max_batch_size": 32}}, {"name": "face_feature_mergence", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["face_features", "face_tracklets_with_landmarks"], "outputs": ["face_tracklets_with_feature"]}, {"name": "faces_refinement", "source_id": 496176543, "context_id": 496176543, "type": "Refinement", "parallel_group": "target_analysis", "inputs": ["face_tracklets_with_feature"], "outputs": ["best_faces"]}, {"name": "body_feature_mergence", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["body_features", "body_tracklets"], "outputs": ["body_tracklets_with_feature"], "config": {"update": true}}, {"name": "body_refinement", "source_id": 496176543, "context_id": 496176543, "type": "Refinement", "parallel_group": "target_analysis", "inputs": ["body_tracklets_with_feature"], "outputs": ["best_bodys"], "config": {}}, {"name": "face_attribute_extraction", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["best_faces"], "outputs": ["face_attributes"], "config": {"plugin": "attribute", "model": "/usr/cognitivesvc/KM_attribute_face_nart_cuda11.0-trt7.1-int8-T4_b64_3.3.1.model", "max_batch_size": 64}}, {"name": "body_attribute_extraction", "source_id": 496176543, "context_id": 496176543, "type": "Plugin", "parallel_group": "target_analysis", "inputs": ["best_bodys"], "outputs": ["body_attributes"], "config": {"plugin": "classifier", "model": "/usr/cognitivesvc/KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.1.model", "max_batch_size": 16}}, {"name": "face_targets_mergence", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["face_attributes", "best_faces"], "outputs": ["analyzed_face_targets"]}, {"name": "body_targets_mergence", "source_id": 496176543, "context_id": 496176543, "type": "Mergence", "parallel_group": "target_analysis", "inputs": ["body_attributes", "best_bodys"], "outputs": ["analyzed_body_targets"]}, {"name": "face_body_concat", "source_id": 496176543, "context_id": 496176543, "type": "Concat", "parallel_group": "target_analysis", "inputs": ["analyzed_face_targets", "analyzed_body_targets"], "outputs": ["analyzed_targets"], "config": {"concat_items": ["targets"]}}, {"name": "output", "source_id": 496176543, "context_id": 496176543, "type": "Output", "parallel_group": "output", "inputs": ["analyzed_targets"], "outputs": []}]}]}