package com.sensetime.intersense.cognitivesvc.pedestrian.utils;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import org.springframework.core.env.Environment;

import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sun.jna.Pointer;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PedestrianInitializer {
	public static final int PED_MODEL_COUNT = Initializer.isDevice() ? 1 : Integer.parseInt(Utils.getProperty("DUP_MODEL_COUNT", "4"));
	public static final int FACE_MODEL_COUNT = Initializer.isDevice() ? 1 : Integer.parseInt(Utils.getProperty("DUP_MODEL_COUNT", "4"));

	public static final String optInit = Utils.getProperty("optimizeInit");


	private static boolean initialized = false;

	private static Pointer pipeline;



	/** 这些都是真模型holder */
	public static ModelHolder hunter_large_holder;
	public static ModelHolder aligner_106_holder;
	public static ModelHolder feature_holder;

	public static ModelHolder hunter_faceBody_holder;

	public static ModelHolder ped_feature_holder;

	/** 初始化 需要调用
	 */
	public synchronized static void initialize(Environment env) {
		if(initialized)
			return ;
		
		SeekerFaceInitializer.initialize(env);


		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "headpose.kep"  , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "blur.kep"      , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "attribute.kep" , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "hunter.kep"    , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "aligner.kep"   , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "feature.kep"   , "");
		KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "senu.kep"   , "");

		hunter_large_holder    = new ModelHolder("hunter"    , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_large_module")       , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);

		aligner_106_holder     = new ModelHolder("aligner"   , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module")        , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);

		feature_holder         = new ModelHolder("feature"   , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module")            , Initializer.batchSize, FACE_MODEL_COUNT  , judgeInit(true), false, false);



		hunter_faceBody_holder    = new ModelHolder("hunter"    , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("facebody_module")       , Initializer.batchSize, PED_MODEL_COUNT  , judgeInit(true), false, false);

		ped_feature_holder         = new ModelHolder("senu"   , "/usr/cognitivesvc/" + Initializer.modelPathMap.get("senu_feature_module")            , Initializer.batchSize, PED_MODEL_COUNT  , judgeInit(true), false, false);


		initialized = true;
	}


	public static Pointer pipeline(){
		if(pipeline != null)
			return pipeline;
		
		synchronized(PedestrianInitializer.class) {
			if(pipeline != null)
				return pipeline;
			
			log.info("face_flock_pipeline_create : " + faceBodyImageConfig);
			
			pipeline = KestrelApi.flock_pipeline_create(faceBodyImageConfig);
		}
		
		return pipeline;
	}
	
	public static final String faceBodyImageConfig = 
			"{" + 
			"    \"streams\": [" + 
			"        {" + 
			"            \"name\": \"image_face_feature\"," + 
			"            \"modules\": [" + 
			"                {" + 
			"                    \"name\": \"importer\"," + 
			"                    \"type\": \"Input\"," + 
			"                    \"inputs\": []," + 
			"                    \"outputs\": [" + 
			"                        \"decoded_images\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"large_face_detector\"," + 
			"                    \"type\": \"Detection\"," + 
			"                    \"group\": \"detect_module_group\"," + 
			"                    \"module_plugin\": \"detection.fmd\"," + 
			"                    \"is_parallel\": true," + 
			"                    \"inputs\": [" + 
			"                        \"decoded_images\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"large_faces\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"hunter\"," + 
			"                        \"model\": \"model/detection/KM_hunter_largeface_gray_nart_cuda11.0-trt7.1-int8-T4_b64_9.4.0.model\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"common_face_detector\"," + 
			"                    \"type\": \"Redetection\"," + 
			"                    \"group\": \"detect_module_group\"," + 
			"                    \"module_plugin\": \"detection.fmd\"," + 
			"                    \"is_parallel\": true," + 
			"                    \"inputs\": [" + 
			"                        \"large_faces\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"common_faces\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"hunter\"," + 
			"                        \"model\": \"model/detection/KM_hunter_common_nart_cuda11.0-trt7.1-int8-T4_b8_0.0.4.model\"," + 
			"                        \"max_batch_size\": 1" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_concat\"," + 
			"                    \"type\": \"Concat\"," + 
			"                    \"group\": \"detect_module_group\"," + 
			"                    \"module_plugin\": \"concat.fmd\"," + 
			"                    \"inputs\": [" + 
			"                        \"large_faces\"," + 
			"                        \"common_faces\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"detected_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"concat_items\": [" + 
			"                            \"targets\"" + 
			"                        ]" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_aligner\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"is_parallel\": true," + 
			"                    \"module_plugin\": \"plugin.fmd\"," + 
			"                    \"inputs\": [" + 
			"                        \"detected_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"aligned_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"aligner\"," + 
			"                        \"model\": \"model/alignment/KM_aligner_deepface_nart_cuda11.0-trt7.1-fp16-T4_b64_2.17.2.model\"," + 
			"                        \"max_batch_size\": 64" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_feature\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"is_parallel\": true," + 
			"                    \"module_plugin\": \"plugin.fmd\"," + 
			"                    \"inputs\": [" + 
			"                        \"aligned_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"targets_with_feature\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"feature\"," + 
			"                        \"model\": \"model/feature/KM_feature_nart_cuda11.0-trt7.1-int8-T4_b32_2.50.0.model\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"merger\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"group\": \"face_feature\"," + 
			"                    \"module_plugin\": \"mergence.fmd\"," + 
			"                    \"inputs\": [" + 
			"                        \"detected_targets\"," + 
			"                        \"targets_with_feature\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"result\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"exporter\"," + 
			"                    \"type\": \"Output\"," + 
			"                    \"is_parallel\": true," + 
			"                    \"inputs\": [" + 
			"                        \"result\"" + 
			"                    ]," + 
			"                    \"outputs\": []" + 
			"                }" + 
			"            ]" + 
			"        }" + 
			"    ]" + 
			"}" + 
			"";

	private static final boolean judgeInit(boolean isLazy) {
		if(optInit == null)
			return isLazy;

		return !Boolean.valueOf(optInit);
	}
}
