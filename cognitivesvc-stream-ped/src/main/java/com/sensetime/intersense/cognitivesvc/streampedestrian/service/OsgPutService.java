package com.sensetime.intersense.cognitivesvc.streampedestrian.service;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianEventBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.Base64;

@Slf4j
@Service
public class OsgPutService {

    private static OsgPutService instance;

    @PostConstruct
    public void init() {
        instance = this;
    }

    public static String putObject(byte[] bytes, PedestrianEventBuilder.OsgObject bucket) {
        return instance.internalPutObject(bytes, bucket);
    }
    private String internalPutObject(byte[] bytes, PedestrianEventBuilder.OsgObject osgObject) {
        Base64.Encoder encoder = Base64.getEncoder();
        String encode = encoder.encodeToString(bytes);

        PutOsgObjectEntity entity = new PutOsgObjectEntity();
        entity.setBlob(encode);
        entity.setBucket_name(osgObject.getBucketName());

        PutOsgObjectEntity.ObjectInfo objectStorageGatewayObjectInfo = new PutOsgObjectEntity.ObjectInfo();

        PutOsgObjectEntity.Metadata objectStorageGatewayMetaData = new PutOsgObjectEntity.Metadata();
        objectStorageGatewayMetaData.setSize(bytes.length);

        objectStorageGatewayObjectInfo.setMetadata(objectStorageGatewayMetaData);
        entity.setObject_info(objectStorageGatewayObjectInfo);

        ResponseEntity<PutOsgObjectEntity> response = null;
        String postUrl = osgObject.getOsgUrl() + "/v1/" + osgObject.getBucketName() + "/put_object";

        int maxRetries = 3; // 最大重试次数
        int retryCount = 0;
        long retryDelay = 1000; // 重试延迟时间，单位：毫秒

        while (retryCount < maxRetries) {
            try {
                HttpEntity<Object> httpEntity = new HttpEntity<Object>(JSONObject.toJSONString(entity), RestUtils.headers);
                response = RestUtils.get(osgObject.getTimeout(), 500, 100).postForEntity(postUrl, httpEntity, PutOsgObjectEntity.class);
                break; // 如果请求成功，跳出循环
            } catch (RestClientException restClientException) {
                log.error("put object error : " + postUrl + restClientException.getMessage());
            } catch (Exception e) {
                log.error("put object error : unknown error, " + postUrl);
                e.printStackTrace();
            }

            retryCount++;
            if (retryCount >= maxRetries) {
                return null; // 达到最大重试次数，返回null
            }
            try {
                Thread.sleep(retryDelay); // 等待一段时间再重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return null;
            }
        }

        if (response == null) {
            log.error("put object error : response is null after retries");
            return null;
        }

        PutOsgObjectEntity body = response.getBody();
        if (body == null) {
            log.error("put object error : response body is null");
            return null;
        }
        if (body.getObject_info() != null && StringUtils.isNotEmpty(body.getObject_info().getKey())) {
            return body.getObject_info().getKey();
        }
        return null;
    }
}
