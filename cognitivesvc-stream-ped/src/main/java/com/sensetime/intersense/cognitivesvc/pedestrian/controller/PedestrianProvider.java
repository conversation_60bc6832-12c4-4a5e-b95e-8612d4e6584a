package com.sensetime.intersense.cognitivesvc.pedestrian.controller;

import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianFeatureAttributePipeline;
import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianFeaturePipeline;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.service.utils.SeekerPedestrianInitializer;
import com.sensetime.intersense.cognitivesvc.server.entities.PasserFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.PasserPedestrianFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonFaceFeature;
import com.sensetime.intersense.cognitivesvc.server.entities.PersonPedestrianFeature;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PasserPedestrianFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonPedestrianFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.Statement;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping(value = "/cognitive/pedestrian/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="PedestrianProvider",description = "pedestrian provider")
@Slf4j
public class PedestrianProvider {
	
	@Autowired
	private DataSource dataSource;
	
	@Autowired
	private PersonFaceFeatureRepository personFaceMapper;
	
	@Autowired
	private PasserFaceFeatureRepository passerFaceMapper;
	
	@Autowired
	private PersonPedestrianFeatureRepository personBodyMapper;
	
	@Autowired
	private PasserPedestrianFeatureRepository passerBodyMapper;
	
	@Autowired
	private PedestrianFeaturePipeline pedestrianFeaturePipeline;
	
	@Autowired
	private PedestrianFeatureAttributePipeline pedestrianFeatureAttributePipeline;
	
	@Operation(summary = "用图片和库内Id特征比对", method = "POST")
	@RequestMapping(value = "/compareFeatureByImagePathAndId", method = RequestMethod.POST)
	public BaseRes<Float> compareImagePaths(
			  @Parameter(required = true, name = "figureImageUrl", description = "图片_1") @RequestParam String figureImageUrl
			, @Parameter(required = true, name = "personId", description = "personId") @RequestParam String personId
			, @Parameter(required = false, name = "type", description = "比人脸(0)还是比身体(1)") @RequestParam(required = false) Integer type
			, @Parameter(required = false, name = "personType", description = "比库内(0)还是比路人(1)") @RequestParam(required = false) Integer personType) throws Exception {
		if(type == null)
			type = 0;
		
		if(personType == null)
			type = 0;
		
		PedestrianFeaturePipeline.FaceBody[] faceBody = pedestrianFeaturePipeline.extractByPath(figureImageUrl);
		if(ArrayUtils.isEmpty(faceBody))
			throw new BusinessException("3010","image has no face body.");
		
		if(type == 0) {
			PedestrianFeaturePipeline.FaceBody face = Arrays.stream(faceBody).filter(fb -> fb.getLabel() == StreamPedestrianUtils.FACE).findFirst().get();
			if(personType == 0) {
				PersonFaceFeature feature = personFaceMapper.findByPersonUuid(personId).get(0);
				return BaseRes.success(FaissSeeker.compare_feature_normalize((float[])face.getFeature(), FaissSeeker.stringToFeature(feature.getImageFeature()), SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint));
			}else if(personType == 1) {
				PasserFaceFeature feature = passerFaceMapper.findByPersonUuid(personId).get(0);
				return BaseRes.success(FaissSeeker.compare_feature_normalize((float[])face.getFeature(), FaissSeeker.stringToFeature(feature.getImageFeature()), SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint));
			}
		}else if(type == 1) {
			PedestrianFeaturePipeline.FaceBody body = Arrays.stream(faceBody).filter(fb -> fb.getLabel() == StreamPedestrianUtils.BODY).findFirst().get();
			if(personType == 0) {
				PersonPedestrianFeature feature = personBodyMapper.findByPersonUuid(personId).get(0);
				return BaseRes.success(FaissSeeker.compare_feature_normalize((float[])body.getFeature(), FaissSeeker.stringToFeature(feature.getImageFeature()), SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint));
			}else if(personType == 1) {
				PasserPedestrianFeature feature = passerBodyMapper.findByPersonUuid(personId).get(0);
				return BaseRes.success(FaissSeeker.compare_feature_normalize((float[])body.getFeature(), FaissSeeker.stringToFeature(feature.getImageFeature()), SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint));
			}
		}
		
		throw new BusinessException("4001","type or personType is not correct.");
	}
	
	@Operation(summary = "对比两张图片路径，算出评分", method = "POST")
	@RequestMapping(value = "/compareFeatureByImagePaths", method = RequestMethod.POST)
	public BaseRes<Float> compareFeatureByImagePaths(
			  @Parameter(required = true, name = "figureImageUrl_1", description = "图片_1") @RequestParam String figureImageUrl_1
			, @Parameter(required = true, name = "figureImageUrl_2", description = "图片_2") @RequestParam String figureImageUrl_2
			, @Parameter(required = false, name = "type", description = "比人脸(0)还是比身体(1)") @RequestParam(required = false) Integer type) throws Exception {
		if(type == null)
			type = 1;
		
		PedestrianFeaturePipeline.FaceBody[] feature_1 = pedestrianFeaturePipeline.extractByPath(figureImageUrl_1);
		PedestrianFeaturePipeline.FaceBody[] feature_2 = pedestrianFeaturePipeline.extractByPath(figureImageUrl_2);
		if(ArrayUtils.isEmpty(feature_1) || ArrayUtils.isEmpty(feature_2))
			throw new BusinessException("3010","image has no face body.");
		
		if(type == 0) {
			PedestrianFeaturePipeline.FaceBody face1 = Arrays.stream(feature_1).filter(fb -> fb.getLabel() == StreamPedestrianUtils.FACE).findFirst().get();
			PedestrianFeaturePipeline.FaceBody face2 = Arrays.stream(feature_2).filter(fb -> fb.getLabel() == StreamPedestrianUtils.FACE).findFirst().get();

			return BaseRes.success(FaissSeeker.compare_feature_normalize((float[])face1.getFeature(), (float[])face2.getFeature(), SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint));
		}else if(type == 1) {
			PedestrianFeaturePipeline.FaceBody body1 = Arrays.stream(feature_1).filter(fb -> fb.getLabel() == StreamPedestrianUtils.BODY).findFirst().get();
			PedestrianFeaturePipeline.FaceBody body2 = Arrays.stream(feature_2).filter(fb -> fb.getLabel() == StreamPedestrianUtils.BODY).findFirst().get();

			return BaseRes.success(FaissSeeker.compare_feature_normalize((float[])body1.getFeature(), (float[])body2.getFeature(), SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint));
		}else 
			throw new BusinessException("4001","type is not correct.");
	}
	
	@Operation(summary = "从图片base64提取人脸人体特征", method = "POST")
	@RequestMapping(value = "/getPedestrianFeatureByBase64", method = RequestMethod.POST)
	public BaseRes<PedestrianFeaturePipeline.FaceBody[]> getPedestrianFeatureByBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws IOException{
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

		try {
			return getPedestrianFeatureByImagePath(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}
	
	@Operation(summary = "从图片路径提取人脸人体特征", method = "POST")
	@RequestMapping(value = "/getPedestrianFeatureByImagePath", method = RequestMethod.POST)
	public BaseRes<PedestrianFeaturePipeline.FaceBody[]> getPedestrianFeatureByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl){
		PedestrianFeaturePipeline.FaceBody[] faceBodys = pedestrianFeaturePipeline.extractByPath(figureImageUrl);
		for(int index = 0; index < faceBodys.length; index ++) {
			faceBodys[index].setFeature(FaissSeeker.featureToString((float[])faceBodys[index].getFeature()));
			
			PedestrianFeaturePipeline.FaceBody matched = faceBodys[index].getMatchedFaceBody();
			if(matched != null && faceBodys[index].getLabel() == StreamPedestrianUtils.BODY) {
				faceBodys[index].setMatchedFaceBody(null);
				faceBodys[index] = null;
			}
		}
		
		return BaseRes.success(Arrays.stream(faceBodys).filter(Objects::nonNull).toArray(PedestrianFeaturePipeline.FaceBody[]::new));
	}
	
	@Operation(summary = "从图片base64提取人脸人体特征", method = "POST")
	@RequestMapping(value = "/getPedestrianFeatureAttributeByBase64", method = RequestMethod.POST)
	public BaseRes<PedestrianFeatureAttributePipeline.FaceBody[]> getPedestrianFeatureAttributeByBase64(@Parameter(required = true, name = "figureImageBase64", description = "图片base64") @RequestParam String figureImageBase64) throws IOException{
		File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

		try {
			return getPedestrianFeatureAttributeByImagePath(tmpImage.getAbsolutePath());
		}finally {
			tmpImage.delete();
		}
	}
	
	@Operation(summary = "从图片路径提取人脸人体特征", method = "POST")
	@RequestMapping(value = "/getPedestrianFeatureAttributeByImagePath", method = RequestMethod.POST)
	public BaseRes<PedestrianFeatureAttributePipeline.FaceBody[]> getPedestrianFeatureAttributeByImagePath(@Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl){
		PedestrianFeatureAttributePipeline.FaceBody[] faceBodys = pedestrianFeatureAttributePipeline.extractByPath(figureImageUrl);
		for(int index = 0; index < faceBodys.length; index ++) {
			faceBodys[index].setFeature(FaissSeeker.featureToString((float[])faceBodys[index].getFeature()));
			
			PedestrianFeatureAttributePipeline.FaceBody matched = faceBodys[index].getMatchedFaceBody();
			if(matched != null && faceBodys[index].getLabel() == StreamPedestrianUtils.BODY){
				faceBodys[index].setMatchedFaceBody(null);
				faceBodys[index] = null;
			}
		}
		
		return BaseRes.success(Arrays.stream(faceBodys).filter(Objects::nonNull).toArray(PedestrianFeatureAttributePipeline.FaceBody[]::new));
	}
	
	@Operation(summary = "全表重新提取特征",method = "GET")
	@RequestMapping(value = "/reExtractFully", method = RequestMethod.GET)
    public synchronized BaseRes<String> reExtractFully() throws Exception {
		try(Connection conn = dataSource.getConnection()){
			try(Statement stat = conn.createStatement()){
				stat.executeUpdate("update person_pedestrian_feature set image_feature = '', model_version = '';");
				stat.executeUpdate("update passer_pedestrian_feature set image_feature = '';");
			}
		}

		int maxCusor = Objects.requireNonNullElse(personBodyMapper.queryMaxId(), 0);
		for(int cursor = 0; cursor < maxCusor; cursor += 10000) {
			List<PersonPedestrianFeature> pfs = personBodyMapper.querySplit(cursor, cursor + 10000, 1, 0);
			pfs.parallelStream().forEach(person ->{
				try {
					PedestrianFeaturePipeline.FaceBody[] faceBodys = pedestrianFeaturePipeline.extractByPath(person.getAvatarImageUrl());
					String feature = FaissSeeker.featureToString((float[])faceBodys[0].getFeature());
					person.setImageFeature(feature);
					person.setModelVersion(Initializer.modelPathMap.get("senu_feature_module"));
					personBodyMapper.save(person);
				}catch(Exception e) {
					log.warn("person id error : " + person.getPersonUuid(), e);
				}
			}); 
			
			personBodyMapper.flush();
		}
		
    	maxCusor = Objects.requireNonNullElse(passerBodyMapper.queryMaxId(), 0);
		for(int cursor = 0; cursor < maxCusor; cursor += 10000) {
			List<PasserPedestrianFeature> pfs = passerBodyMapper.querySplit(cursor, cursor + 10000, 1, 0);
			pfs.parallelStream().forEach(passer ->{
				try {
					PedestrianFeaturePipeline.FaceBody[] faceBodys = pedestrianFeaturePipeline.extractByPath(passer.getAvatarImageUrl());
					String feature = FaissSeeker.featureToString((float[])faceBodys[0].getFeature());
					passer.setImageFeature(feature);
					passerBodyMapper.save(passer);
				}catch(Exception e) {
					log.warn("person id error : " + passer.getId(), e);
				}
			});
			
			passerBodyMapper.flush();
		}
		
		return BaseRes.success("OK");
    }
	
	@Operation(summary = "检查库内特征与模型不匹配的数量",method = "GET")
	@RequestMapping(value = "/checkFeatureCorrect", method = RequestMethod.GET)
    public synchronized BaseRes<Integer> checkFeatureCorrect() throws Exception {
		int person = personBodyMapper.countByModelVersionNot(Initializer.modelPathMap.get("senu_feature_module"));
		return BaseRes.success(person);
    }
}
