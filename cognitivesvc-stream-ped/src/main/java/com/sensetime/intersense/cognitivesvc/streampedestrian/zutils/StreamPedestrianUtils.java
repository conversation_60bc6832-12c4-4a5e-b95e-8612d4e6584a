package com.sensetime.intersense.cognitivesvc.streampedestrian.zutils;

import java.awt.Polygon;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.IntStream;

import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamPedestrian;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_feature_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.PedestrianValid.PedestrianValidBuilder;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.experimental.Accessors;
@Slf4j
public class StreamPedestrianUtils {
	
	public static final int FACE = 37017;
	public static final int BODY = 221488;

	public static final int PERSON = 0;
	public static final int PASSER = 1;

	public static final int SELECT_NONE = 0;     // 未被选中输出
	public static final int QUICK_RESPONSE = 1;  // 先前没被筛选过 且跟踪时长达到了快速响应的筛选时间
	public static final int TIME_INTERVAL = 2;   // 距离上次筛选的时长达到了时间间隔周期
	public static final int HIGH_QUALITY = 3;    // 质量分数足够高
	public static final int TIMEOUT = 4;         // 跟踪超过最大跟踪时长
	public static final int TRACKING_FINISH = 5; // 跟踪结束

	
	public static float[] origin_feature(Pointer target) {
		Pointer feature_keson = KestrelApi.keson_get_object_item(target, "feature");
		PointerByReference feature_t = new PointerByReference();
		KestrelApi.keson_get_ext_data(feature_keson, feature_t);
		kestrel_feature_t kestrel_feature = new kestrel_feature_t(feature_t.getValue());
		kestrel_feature.read();

		return kestrel_feature.feature.getFloatArray(0, kestrel_feature.dims);
	}
	
	public static float[] normalize_feature(Pointer target) {
		float[] feature = origin_feature(target);

		double square_sum = 0;

		for (int jndex = 0; jndex < feature.length; jndex++)
			square_sum += feature[jndex] * feature[jndex];

		square_sum = Math.sqrt(square_sum);

		for (int jndex = 0; jndex < feature.length; jndex++)
			feature[jndex] /= square_sum;

		return feature;
	}

	public static PedestrianValid isPedestrianValid(VideoStreamPedestrian pedestrian, Tracklet selectTarget) {
		int label = selectTarget.getLabel();

		if(Utils.instance.logged){
			log.info("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}",label, pedestrian.getDeviceId(), selectTarget.getTrack_id());
		}

		PedestrianValidBuilder valid = PedestrianValid.builder();

		float qualityThreshold = Objects.requireNonNullElse(pedestrian.getQualityThreshold(), Utils.instance.imageQuality);
		if(label == FACE) {
			float quality = selectTarget.getQuality();
			if(quality < qualityThreshold){
				valid.qualityValid(false);
				log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, quality not valid :{},qualityThreshold:{} ",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),quality, qualityThreshold);
			}
		}
		if(label == BODY) {
			float pedQualityThreshold = Objects.requireNonNullElse(pedestrian.getPedQualityThreshold(), Utils.instance.imageQuality);
			float quality = selectTarget.getQuality();
			if(quality < pedQualityThreshold){
				valid.qualityValid(false);
				log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, quality not valid :{},qualityThreshold:{} ",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),quality, qualityThreshold);
			}
		}


		Map<String, Number> position = selectTarget.getPosition();
		int faceX = position.get("left").intValue() + position.get("width").intValue()  / 2;
		int faceY = position.get("top").intValue()  + position.get("height").intValue() / 2;
		
		if(label == FACE && pedestrian.getMinFaceSize() != null) 
			if(faceX < pedestrian.getMinFaceSize() || faceY < pedestrian.getMinFaceSize()) 
				valid.faceSizeValid(false);
		else if(label == BODY && pedestrian.getMinBodySize() != null) 
			if(faceX < pedestrian.getMinBodySize() || faceY < pedestrian.getMinBodySize()) 
				valid.bodySizeValid(false);
		
		if(label == FACE) {
			if(pedestrian.getYaw() != null && Math.abs(selectTarget.getYaw()) > pedestrian.getYaw())
			{
				valid.headPoseValid(false);
				log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, Yaw not valid :{},Yaw:{} ",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),selectTarget.getYaw(), pedestrian.getYaw());
			}

			if(pedestrian.getPitch() != null && Math.abs(selectTarget.getPitch()) > pedestrian.getPitch())
			{
				valid.headPoseValid(false);
				log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, Pitch not valid :{},Pitch:{} ",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),selectTarget.getPitch(), pedestrian.getPitch());
			}

			if(pedestrian.getRoll() != null && Math.abs(selectTarget.getRoll()) > pedestrian.getRoll()) {
				valid.headPoseValid(false);
				log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, Roll not valid :{},Roll:{} ",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),selectTarget.getRoll(), pedestrian.getRoll());
			}
		}

		if(label == FACE){
			float integrateQuality = Objects.requireNonNullElse(pedestrian.getProcessFace().getIntegrateQuality(), Utils.instance.integrateQuality);
			if(selectTarget.getIntegrate_quality() > 0){
				if(selectTarget.getIntegrate_quality() < integrateQuality){
					valid.qualityValid(false);
					log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, integrateQuality not valid :{},integrateQuality:{} ",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),selectTarget.getIntegrate_quality(), integrateQuality);
				}
			}
		}

		if(label == FACE){
			float detect_confidence = selectTarget.getConfidence();
			float aligner_confidence = selectTarget.getAligner_confidence();

			if (Utils.instance.shieldFaceSwitch == 0) {
				//log.info("detect_confidence{},{}", detect_confidence, aligner_confidence);
				if (detect_confidence > 0 && aligner_confidence > 0) {
					float qualitySum = detect_confidence * aligner_confidence;
					if(qualitySum < qualityThreshold)
					{
						valid.qualityValid(false);
						log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, confidence not valid :{},detect_confidence:{}, aligner_confidence:{},qualityThreshold:{}",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),qualitySum, detect_confidence,aligner_confidence,qualityThreshold);
					}
				} else {
					if (detect_confidence > 0) {
						if(detect_confidence < qualityThreshold) {
							valid.qualityValid(false);
							log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, confidence not valid :{},detect_confidence:{}, aligner_confidence:{},qualityThreshold:{}",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),detect_confidence, detect_confidence,aligner_confidence,qualityThreshold );
						}
					}
					if (aligner_confidence > 0) {
						if(aligner_confidence < qualityThreshold) {
							valid.qualityValid(false);
							log.warn("pedTrace isPedestrianValid label:{},deviceId:{}, trackID:{}, confidence not valid :{},detect_confidence:{}, aligner_confidence:{},qualityThreshold:{}",label, pedestrian.getDeviceId(), selectTarget.getTrack_id(),aligner_confidence, detect_confidence,aligner_confidence,qualityThreshold );

						}
					}
				}
			}
		}
		
		Polygon polygons[] = pedestrian.queryPolygons();
		if(ArrayUtils.isNotEmpty(polygons)) {
			int[] roiValid = IntStream.range(0, polygons.length).map(index -> polygons[index].contains(faceX, faceY) ? index : -1).filter(index -> index >= 0).toArray();
			valid.roiValid(roiValid);
		}
		
		return valid.build();
	}
	
	/**返回人脸人体的组合 */
	public static <T extends Mergeable> List<Pair<T, T>> mergeTarget(List<T> targets){
		List<T> targetCopy = new ArrayList<T>(targets);
		
		List<Pair<T, T>> result = new ArrayList<Pair<T, T>>();
		for(int index = 0; index < targetCopy.size(); index ++) {
			T target = targetCopy.get(index);
			if(target == null) 
				continue;
			
			MutablePair<T, T> pair = new MutablePair<T, T>();
			result.add(pair);
			
			switch(target.getLabel()) {
				case FACE:
					pair.setLeft(target);
					break;
				case BODY:
					pair.setRight(target);
					break;
				default:
					throw new RuntimeException("should not be here.");
			}
			
			if(CollectionUtils.isNotEmpty(target.getAssociations())) {
				int associationTrackId = target.getAssociations().get(0).getTrack_id();
				long sourceId = target.getSource_id();
				for(int jndex = index + 1; jndex < targetCopy.size(); jndex ++) {
					T otherTarget = targetCopy.get(jndex);
					if(otherTarget != null && otherTarget.getSource_id() == sourceId && otherTarget.getTrack_id() == associationTrackId){
						switch(otherTarget.getLabel()) {
							case FACE:
								pair.setLeft(otherTarget);
								break;
							case BODY:
								pair.setRight(otherTarget);
								break;
							default:
								throw new RuntimeException("should not be here.");
						}
						
						targetCopy.set(jndex, null);
						break;
					}
				}
			}
		}
		
		return result;
	}
	
	/**
	 * <id, pointer>
	 * @param kesonSelect
	 */
	public static Map<Integer, Pointer> selectKesonToMap(Pointer kesonSelect) {
		Map<Integer, Pointer> result = new HashMap<Integer, Pointer>();
		
		Pointer structuredTargets = KestrelApi.keson_get_object_item(kesonSelect, "structured_targets");
		int arr_size = KestrelApi.keson_array_size(structuredTargets);
		
		for(int index = 0 ; index < arr_size; index ++) {
			Pointer structuredTarget = KestrelApi.keson_get_array_item(structuredTargets, index);
			int id = (int)KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(structuredTarget, "id"));

			result.put(id, structuredTarget);
		}
		
		return result;
	}
	
	public static Pointer build_pedestrian_video_param(long sourceId, Pointer gpuFrame) {
		Pointer param_keson = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
		Pointer frames_array = KestrelApi.keson_create_array();
		KestrelApi.keson_add_item_to_object(param_keson, "frames", frames_array);
		
		Pointer image = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_object(image, "source_id", KestrelApi.keson_create_int(sourceId));
		KestrelApi.keson_add_item_to_object(image, "image"   , KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), gpuFrame));
		KestrelApi.keson_add_item_to_array(frames_array, image);
		
		return param_keson;
	}
	
	public static Pointer build_pedestrian_video_param(long[] sourceIds, Pointer[] gpuFrames) {
		Pointer param_keson = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
		Pointer frames_array = KestrelApi.keson_create_array();
		KestrelApi.keson_add_item_to_object(param_keson, "frames", frames_array);
		
		for(int index = 0 ; index < gpuFrames.length && index < sourceIds.length; index ++) {
			Pointer image = KestrelApi.keson_create_object();
			KestrelApi.keson_add_item_to_object(image, "source_id", KestrelApi.keson_create_int(sourceIds[index]));
			KestrelApi.keson_add_item_to_object(image, "image"   , KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), gpuFrames[index]));
			KestrelApi.keson_add_item_to_array(frames_array, image);
		}
		
		return param_keson;
	}
	
	public static interface Mergeable{
		public int getTrack_id();
		
		public long getSource_id();
		
		public int getLabel();
		
		public List<Association> getAssociations();
	}
	
	@Data
	@Builder
	public static class PedestrianValid{
		private Boolean qualityValid;
		private Boolean faceSizeValid;
		private Boolean bodySizeValid;
		private Boolean headPoseValid;
		private int[] roiValid;
		
		public boolean isValid() {
			if(Boolean.FALSE.equals(qualityValid))
				return false;
			
			if(Boolean.FALSE.equals(faceSizeValid))
				return false;
			
			if(Boolean.FALSE.equals(bodySizeValid))
				return false;
			
			if(Boolean.FALSE.equals(headPoseValid))
				return false;
			
			if(roiValid != null && roiValid.length <= 0)
				return false;
			
			return true;
		}
		public String isValidText() {
			if(Boolean.FALSE.equals(qualityValid))
				return "qualityValid";

			if(Boolean.FALSE.equals(faceSizeValid))
				return "faceSizeValid";

			if(Boolean.FALSE.equals(bodySizeValid))
				return "bodySizeValid";

			if(Boolean.FALSE.equals(headPoseValid))
				return "headPoseValid";

			if(roiValid != null && roiValid.length <= 0)
				return "roiValid";

			return "";
		}
	}
	
	@Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Accessors(chain = true)
	public static class SeenTrack implements Mergeable{
		private int                 label;
		private int                 status;
		private int                 track_id;
		private long                source_id;
		private float               confidence;
		private Map<String, Number> roi;
		private List<Association>   associations;
	}
	
	@Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Accessors(chain = true)
	public static class Tracklet implements Mergeable{
		private int                 label;
		private int                 status;
		private int                 track_id;
		private long                source_id;
		private float               confidence;
		private float				aligner_confidence;
		private float               quality;
		private float               integrate_quality;
		private float               yaw;
		private float               pitch;
		private float               roll;
		private Map<String, Number> roi;
		private Map<String, Number> position;
		private float[]             originFeature;
		private float[]             normalizeFeature;
		private Map<String, Object> attribute;
		private List<Association>   associations;
		private long                capture_time;
		private long                selectFrameEndTime;
	}
	
	@Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Accessors(chain = true)
	public static class Association{
		private int   track_id;
		private int   label;
		private float association_score;
	}
}
