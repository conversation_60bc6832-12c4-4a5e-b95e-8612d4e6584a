package com.sensetime.intersense.cognitivesvc.streampedestrian;

import ch.qos.logback.core.util.ExecutorServiceUtil;
import com.sensetime.storage.autoconfigure.StorageProperties;
import jakarta.annotation.PostConstruct;

import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer.DeviceType;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils.FrameDefaultRequired;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoSeenFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.service.VideoFrameFilter;
import com.sensetime.intersense.cognitivesvc.stream.video.service.VideoFrameFilter.VideoChecked;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.PedestrianHandler;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.PedestrianTrackerPipeline;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.VideoIconContainer;
import com.sensetime.intersense.cognitivesvc.streampedestrian.video.filter.PedestrianNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.streampedestrian.video.filter.PedestrianSeenFilter;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.sensetime.storage.factory.FileStorageType.OSG;

@Configuration("streamPedestrianConfiguation")
@EnableScheduling
@ComponentScan
@ConditionalOnExpression("${stream.enabled:true} && ${pedestrian.enabled:true} && ${stream.pedestrian.enabled:true} && ${seeker.enabled:true} && ${seeker.face.enabled:true} && ${seeker.pedestrian.enabled:true}")
@Slf4j
public class CognitivesvcConfiguation{

	@Value("${preMakeDirs}")
	private String preMakeDirs;

//	@Autowired
//	private Utils.Sync sync;

	@Autowired
	private PedestrianTrackerPipeline pedestrianContainer;
	
	@Autowired
	private PedestrianHandler pedestrianHandler;
	
	@Autowired
	private VideoIconContainer videoIconContainer;

	@Autowired
	StorageProperties storageProperties;
	
	@PostConstruct
	public synchronized void initialize() {
		if(VideoNonSeenFilter.existingFilter.contains(PedestrianNonSeenFilter.class))
			return;
		
		VideoNonSeenFilter.existingFilter.add(PedestrianNonSeenFilter.class);
		VideoSeenFilter.existingFilter.add(PedestrianSeenFilter.class);

		PedestrianNonSeenFilter.setPedestrianContainer(pedestrianContainer);
		PedestrianNonSeenFilter.setPedestrianHandler(pedestrianHandler);
		
		PedestrianSeenFilter.setPedestrianContainer(pedestrianContainer);
		PedestrianSeenFilter.setPedestrianHandler(pedestrianHandler);
		PedestrianSeenFilter.setVideoIconContainer(videoIconContainer);
		
		log.warn("\n");
		log.warn("******************************************");
		log.warn("**********init stream pedestrian**********");
		log.warn("*****stream.enabled=false to disable******");
		log.warn("****pedestrian.enabled=false to disable***");
		log.warn("stream.pedestrian.enabled=false to disable");
		log.warn("******seeker.enabled=false to disable*****");
		log.warn("***seeker.face.enabled=false to disable***");
		log.warn("seeker.pedestrian.enabled=false to disable");
		log.warn("******************************************");
		log.warn("\n");


		if(storageProperties.getFileStorageType().equals(OSG)){
			ImageUtils.preMkDirPostContructCompleted = true;
			log.info("******* use osg, no need preMkdirs");
			return;
		}


		List<String> preMakeDirList = Arrays.asList(preMakeDirs.split(","));
//		sync.sync();

		ExecutorService executorService = Executors.newSingleThreadExecutor();
		executorService.submit(new Runnable() {
			@Override
			public void run() {
				try{
					log.info("preMakeDirs stream-ped init...");
					if(ImageUtils.preMkDirPostContructCompleted){
						log.info("preMakeDir Completed already, skip...");
					}
					ImageUtils.mkdirsByHour(preMakeDirList,true);
					ImageUtils.mkdirsByHour(preMakeDirList,false);
				}finally {
					ImageUtils.preMkDirPostContructCompleted = true;
					log.info("[preMakeDirsCron] preMakeDir Completed");
				}
			}
		});

	}
	
	@Bean
	public VideoFrameFilter pedestrianVideoFrameFilter(){
		return (infra, models) -> {
			if(pedestrianContainer.getStreamPeds().containsKey(infra.getDeviceId())) {
				if("0".equals(infra.getRtmpOn()))
					return VideoChecked.ALL;
				else
					return VideoChecked.GPU;
			}else 
				return null;
		};
	}
    
    @Bean
    public FrameDefaultRequired pedstrianFrameDefaultRequired() {
    	return new FrameDefaultRequired() {
			@Override
			public int requiredNonSeenReusedFrameCount(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
						return 16;
					case T4: 
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 40;
				}
			}

			@Override
			public int requiredNonSeenQueueSize(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
						return 256; 
					case T4:
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 768;
				}
			}

			@Override
			public int requiredNonSeenMaxCount(DeviceType deviceType) {
				switch(deviceType) {
					case HOST: 
					case P4: 
						return 16;
					case T4: 
					case A2: 
					case A16: 
					case STPU: 
					default:
						return 30;
				}
			}
		};
    }
}
