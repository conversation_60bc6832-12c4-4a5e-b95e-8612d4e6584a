package com.sensetime.intersense.cognitivesvc.pedestrian.controller;

import com.sensetime.intersense.cognitivesvc.server.entities.FacePedestrianCluster;
import com.sensetime.intersense.cognitivesvc.server.mapper.*;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.hibernate.HibernateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/cognitive/pedestrian/cluster/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="PedestrianClusterProvider",description = "PedestrianClusterProvider")
public class PedestrianClusterProvider {
	
	@Autowired
	private FacePedestrianClusterRepository facePedestrianClusterRepository;

	@Autowired
	private PersonFaceFeatureRepository personFaceFeatureRepository;

	@Autowired
	private PersonPedestrianFeatureRepository personPedestrianFeatureRepository;

	@Autowired
	private PasserFaceFeatureRepository passerFaceFeatureRepository;

    @Autowired
    private PasserPedestrianFeatureRepository passerPedestrianFeatureRepository;

    @Operation(summary = "人脸ID与人体ID绑定查询", method = "GET")
    @RequestMapping(value = "/find", method = RequestMethod.GET)
    public BaseRes<List<FacePedestrianCluster>> find(
            @Parameter(required = false, name = "facePersonId", description = "facePersonId") @RequestParam(required = false) String facePersonId
            , @Parameter(required = false, name = "bodyPersonId", description = "bodyPersonId") @RequestParam(required = false) String bodyPersonId) throws Exception {

        if (facePersonId != null && bodyPersonId != null) {
            return BaseRes.success(facePedestrianClusterRepository.findByFacePersonIdAndPedestrianPersonId(facePersonId, bodyPersonId));
        } else if (facePersonId != null) {
            return BaseRes.success(facePedestrianClusterRepository.findByFacePersonId(facePersonId));
        } else if (bodyPersonId != null) {
            return BaseRes.success(facePedestrianClusterRepository.findByPedestrianPersonId(bodyPersonId));
        } else
            return BaseRes.success(facePedestrianClusterRepository.findAll());
    }

    @SuppressWarnings("rawtypes")
    @Operation(summary = "人脸ID与人体ID绑定设置", method = "POST")
    @RequestMapping(value = "/set", method = RequestMethod.POST)
    public BaseRes<String> set(
            @Parameter(name = "facePersonId", description = "facePersonId") @RequestParam String facePersonId
            , @Parameter(name = "bodyPersonId", description = "bodyPersonId") @RequestParam String bodyPersonId) throws Exception {
        FacePedestrianCluster cluster = FacePedestrianCluster.builder().facePersonId(facePersonId).pedestrianPersonId(bodyPersonId).build();

        List faces = personFaceFeatureRepository.findByPersonUuid(facePersonId);
        if (faces.isEmpty()) {
            faces = passerFaceFeatureRepository.findByPersonUuid(facePersonId);
            if (faces.isEmpty())
                throw new RuntimeException("facePersonId can not be found.");
            else
                cluster.setFacePersonType(1);
        } else
            cluster.setFacePersonType(0);

        List bodys = personPedestrianFeatureRepository.findByPersonUuid(bodyPersonId);
        if (bodys.isEmpty()) {
            bodys = passerPedestrianFeatureRepository.findByPersonUuid(bodyPersonId);
            if (bodys.isEmpty())
                throw new RuntimeException("bodyPersonId can not be found.");
            else
                cluster.setPedestrianPersonType(1);
        } else
            cluster.setPedestrianPersonType(0);
        try {
            facePedestrianClusterRepository.saveAndFlush(cluster);
        } catch (HibernateException e) {

		}catch (Exception ex){

		}

		return BaseRes.success("OK");
    }
}
