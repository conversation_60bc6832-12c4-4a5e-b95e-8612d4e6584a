package com.sensetime.intersense.cognitivesvc.streampedestrian.zutils;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import com.sensetime.intersense.cognitivesvc.pedestrian.utils.FileUtil;
import com.sensetime.intersense.cognitivesvc.server.kestrel.FrameEncoderLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_frame_utils_structLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_packet_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.service.OsgPutService;
import com.sun.jna.Native;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PasserFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PersonFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PasserBodyObject;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PersonBodyObject;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamPedestrian;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.Tracklet;
import com.sun.jna.Pointer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@Slf4j
public class PedestrianTrack implements AutoCloseable{
	public static final IdentifiedInfo UNKNOWNINFO = IdentifiedInfo.builder().identifiedPersonId("-1").identifiedScore(-1f).identifiedPersonType(-1).identifiedPersonAvatar(FrameUtils.NOIMAGE).build();
	
	/** 有值表示识别成功与失败 空表示尚未开始识别 因为都是自建线程来访问本类对象 所以需要打开可视性(volatile)*/
	private volatile Boolean identified;
	
	/** 视频流信息 */
	private VideoStreamInfra device;
	
	/** 人脸人体配置信息 */
	private VideoStreamPedestrian pedestrian;
	
	/** 进入选帧*/
	private volatile Tracklet enter;
	
	/** 追踪选帧*/
	@Builder.Default
	private LinkedList<Tracklet> trackings = new LinkedList<Tracklet>();
	
	/** 离开选帧*/
	private volatile Tracklet away;
	
	/** 结束了整个追踪和处理流程*/
	private volatile boolean trackFinished;
	
	/** 已经识别出来的结果*/
	@Builder.Default
	private volatile List<IdentifiedInfo> identifiedInfos = Lists.newArrayList();
	
	/**人脸人体关联 */
	private volatile PedestrianTrack association;
	
	/**最近一次丢失的人脸人体关联 */
	private volatile PedestrianTrack lostAssociation;
	
	@Setter
	@Builder.Default
	/** 这个track已经处于流的第几帧*/
	private volatile long frameIndex = 0;
	@Setter
	@Builder.Default
	/** 这个track已经处于流的多少pts*/
	private volatile long framePts = 0;
	
	@Builder.Default
	private Map<String, Object> parameters = new HashMap<String, Object>();

	public float[] refineFeature() {
		Object[] intervals = trackings.toArray();
		List<float[]> features = new ArrayList<float[]>(intervals.length + 2);
		
		for(Object interval : intervals)
			features.add((float[])((Tracklet)interval).getOriginFeature());
		
		if(enter != null)
			features.add((float[])enter.getOriginFeature());
		
		if(away != null)
			features.add((float[])away.getOriginFeature());
		
		if(features.isEmpty())
			return null;
		
		float[] newFeature = new float[features.get(0).length];
		for(float[] feature : features)
			for(int index = 0; index < newFeature.length; index ++) 
				newFeature[index] += feature[index];
		
		for(int index = 0; index < newFeature.length; index ++) 
			newFeature[index] = newFeature[index] / features.size();
		
		double square_sum = 0;

		for (int jndex = 0; jndex < newFeature.length; jndex++)
			square_sum += newFeature[jndex] * newFeature[jndex];

		square_sum = Math.sqrt(square_sum);

		for (int jndex = 0; jndex < newFeature.length; jndex++)
			newFeature[jndex] /= square_sum;
		
		return newFeature;
	}
	
	@Override
	public synchronized void close(){
		parameters.remove("sceneImage");
		parameters.remove("targetImage");

		Object sceneFrame = parameters.remove("sceneFrame");
		if(sceneFrame != null) {
			PointerByReference packetRef = new PointerByReference();
			kestrel_packet_t sceneFrameP = (kestrel_packet_t)sceneFrame;
			packetRef.setValue(sceneFrameP.getPointer());

			FrameUtils.free_packet(packetRef);
		}
		
		Object targetFrame = parameters.remove("targetFrame");
		if(targetFrame != null)
			FrameUtils.batch_free_frame((Pointer)targetFrame);
	}

	public synchronized long storeFrame(Pointer trackletKeson, boolean scene, boolean saveTargetImage) {
		close();

		long captureTime = 0;
		Pointer targetImage = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletKeson, "image")).getValue();
		if(saveTargetImage && targetImage != null ) {
			parameters.put("targetFrame", FrameUtils.ref_frame(targetImage));
		}
		captureTime = KestrelApi.kestrel_frame_pts(targetImage);

		Pointer sceneImage  = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletKeson, "encoded_scene_frame")).getValue();
		if(scene && sceneImage != null){
			kestrel_packet_t packet_t = new kestrel_packet_t(sceneImage);
			packet_t.read();
			parameters.put("sceneFrame", FrameUtils.ref_packet(packet_t));
		}


		return captureTime;
	}
	
	public synchronized String targetFrameToPath() {
		String targetImage = (String)parameters.get("targetImage");
		if(targetImage != null)
			return targetImage;
		
		Pointer targetFrame = (Pointer)parameters.remove("targetFrame");
		if(targetFrame != null) {
			targetImage = FrameUtils.save_image_as_jpg(targetFrame, ImageUtils.newFile("tmp_ped"), 0);
			FrameUtils.batch_free_frame(targetFrame);
			parameters.put("previousTargetImage", targetImage);
			parameters.put("targetImage", targetImage);
		}else {
			targetImage = (String)parameters.get("previousTargetImage");
			if(targetImage == null)
				targetImage = FrameUtils.NOIMAGE;
		}
		
		return targetImage;
	}

	public synchronized String sceneFrameToPath() {
		String sceneImage = (String)parameters.get("sceneImage");
		if(sceneImage != null)
			return sceneImage;

		kestrel_packet_t sceneFrame = (kestrel_packet_t)parameters.remove("sceneFrame");
		if(sceneFrame != null) {

			if (sceneFrame.size > 0 && sceneFrame.data != null) {
				byte[] imageRes = null;

				imageRes = sceneFrame.data.getByteArray(0, sceneFrame.size);

				sceneImage = FrameUtils.save_image_packet_byte(imageRes, ImageUtils.newFile("tmp_ped"));

				imageRes = null;

				PointerByReference packetRef = new PointerByReference();
				packetRef.setValue(sceneFrame.getPointer());
				FrameUtils.free_packet(packetRef);

				parameters.put("previousSceneImage", sceneImage);
				parameters.put("sceneImage", sceneImage);
			}

		}else {
			sceneImage = (String)parameters.get("previousSceneImage");
			if(sceneImage == null)
				sceneImage = FrameUtils.NOIMAGE;
		}
		return sceneImage;
	}

//	public  String targetFrameToOsg(PedestrianEventBuilder.OsgObject osgObject ) {
//
//		String captureImageOsg = FrameUtils.NOIMAGE;
//
//		if(Utils.instance.videoSaveOsgType == 999){
//			return captureImageOsg;
//		}
//
//		if ( Utils.instance.videoSaveImageType < 0 && Utils.instance.videoSaveOsgType < 0 )
//			return captureImageOsg;
//
//
//		Pointer targetFrame = (Pointer)parameters.remove("targetFrame");
//
////		if(targetFrame != null) {
////			captureImageOsg =  extractOsgUrl(targetFrame, osgObject, 1);
////		}
//
//		return captureImageOsg;
//	}

	/*private synchronized  String extractOsgUrl(Pointer targetFrame, PedestrianEventBuilder.OsgObject osgObject, int flag) {
		Initializer.bindDeviceOrNot();
		String osgUrl = "";

		try {
			byte[] imageResPointer = null;
			if (Utils.instance.frameEncoderType == 0) {
				//调用kestrel的 keson_encode_to_data
				LongByReference outSize = new LongByReference();
				Pointer resultPointer = FrameEncoderLibrary.INSTANCE.KestrelEncoder(targetFrame, Kestrel_frame_utils_structLibrary.kestrel_frame_enc_format_e.KESTREL_ENC_FMT_JPG, outSize);
				if (resultPointer != null) {
					long size = outSize.getValue();
					imageResPointer = resultPointer.getByteArray(0, (int) size);

					Native.free(Pointer.nativeValue(resultPointer));
				} else {
					log.warn("EncodeFrame failed");
				}
			} else if (Utils.instance.frameEncoderType == 1) {
				//调用image sharp kep 插件encode
				imageResPointer = FrameUtils.encode_image_as_jpg(targetFrame);
			} else if (Utils.instance.frameEncoderType == 2) {
				//调用 vps imageio jna or c++ so
				LongByReference outSize = new LongByReference();
				Pointer resultPointer = KestrelApi.kestrel_frame_encoder(targetFrame, flag, outSize);
				if (resultPointer != null) {
					// 使用result字节数组
					long size = outSize.getValue();
					imageResPointer = resultPointer.getByteArray(0, (int) size);
					// 释放内存
					Native.free(Pointer.nativeValue(resultPointer));
				} else {
					log.error("EncodeFrame failed");
				}
			}else if (Utils.instance.frameEncoderType == 3) {
				log.warn("unSupport type {}", Utils.instance.frameEncoderType);
			} else {//unsupport type
				//imageResPointer = null;
				log.warn("unSupport type {}", Utils.instance.frameEncoderType);
			}
			if (imageResPointer != null && imageResPointer.length > 0 && Utils.instance.videoSaveOsgType != 9999 ) {
				String objectStorageGatewayObjectInfo = OsgPutService.putObject(imageResPointer, osgObject);
				if (objectStorageGatewayObjectInfo != null) {
					osgUrl = osgObject.getBucketName() + "/" + objectStorageGatewayObjectInfo;
				} else {
					log.warn("objectStorageGatewayObjectInfo is null {}", osgObject.getBucketName());
					throw new RuntimeException("objectStorageGatewayObjectInfo is null");
				}
			}

		} catch (Exception e) {
			log.error("osg put error{}", e.getMessage());
		}finally {
			FrameUtils.batch_free_frame(targetFrame);
			//log.info("[VideoHandleLog] [Cost] [into saveImage] sceneFrameToOsg2 batch_free_frame ");
		}
		return osgUrl;
	}*/


	public  synchronized String sceneFrameToOsg(PedestrianEventBuilder.OsgObject osgObject) {

		String osgUrl = "";

		String sceneImageOsg = FrameUtils.NOIMAGE;
		if(Utils.instance.videoSaveOsgType == 999){
			return sceneImageOsg;
		}
		if ( Utils.instance.videoSaveImageType < 0 && Utils.instance.videoSaveOsgType < 0 )
			return sceneImageOsg;

		byte[] imageRes = null;
		kestrel_packet_t sceneFrame = (kestrel_packet_t) parameters.remove("sceneFrame");
		try {
			if (sceneFrame != null) {
				if (sceneFrame.size > 0 && sceneFrame.data != null) {

					imageRes = sceneFrame.data.getByteArray(0, sceneFrame.size);

					if (imageRes != null && Utils.instance.videoSaveOsgType != 9999) {
						String objectStorageGatewayObjectInfo = OsgPutService.putObject(imageRes, osgObject);
						if (objectStorageGatewayObjectInfo != null) {
							osgUrl = osgObject.getBucketName() + "/" + objectStorageGatewayObjectInfo;
						} else {
							log.warn("objectStorageGatewayObjectInfo is null {}", osgObject.getBucketName());
							throw new RuntimeException("objectStorageGatewayObjectInfo is null");
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			imageRes = null;
			if(sceneFrame !=null) {
				PointerByReference packetRef = new PointerByReference();
				packetRef.setValue(sceneFrame.getPointer());
				FrameUtils.free_packet(packetRef);
			}
		}

		return osgUrl;

	}

//	public synchronized String targetFrameToOsgDef(String captureImage, PedestrianEventBuilder.OsgObject osgObject) {
//		String sceneImageOsg = FrameUtils.NOIMAGE;
//
//		if(Utils.instance.videoSaveImageType == 999){
//			return sceneImageOsg;
//		}
//		if ( Utils.instance.videoSaveImageType < 0)
//			return sceneImageOsg;
//
//		if (StringUtils.isBlank(captureImage) || captureImage.equals(FrameUtils.NOIMAGE)) {
//			return sceneImageOsg;
//		}
//		File file = new File(captureImage);
//		//			if(!file.exists()){
//		//				log.warn("objectStorageGatewayObjectInfo file null {},{}", event.getInsightCogFaceImg(), captureImage);
//		//			}
//		String objectStorageGatewayObjectInfo = OsgPutService.putObject(FileUtil.fileToBytes(file), osgObject);
//		if (objectStorageGatewayObjectInfo != null) {
//			sceneImageOsg = osgObject.getBucketName() + "/" + objectStorageGatewayObjectInfo;
//		} else {
//			log.warn("objectStorageGatewayObjectInfo is null {},{}", osgObject.getBucketName(), captureImage);
//		}
//
//		return sceneImageOsg;
//	}

//	public String sceneFrameToOsgDef(String sceneImage, PedestrianEventBuilder.OsgObject osgObject) {
//
//		String captureImageOsg = FrameUtils.NOIMAGE;
//
//		if(Utils.instance.videoSaveImageType == 999){
//			return captureImageOsg;
//		}
//		if ( Utils.instance.videoSaveImageType < 0)
//			return captureImageOsg;
//
//		if (StringUtils.isBlank(sceneImage) || sceneImage.equals(FrameUtils.NOIMAGE)) {
//			return captureImageOsg;
//		}
//		File file = new File(sceneImage);
//		//			if(!file.exists()){
//		//				log.warn("objectStorageGatewayObjectInfo file null {},{}", event.getInsightCogFaceImg(), captureImage);
//		//			}
//		String objectStorageGatewayObjectInfo = OsgPutService.putObject(FileUtil.fileToBytes(file), osgObject);
//		if (objectStorageGatewayObjectInfo != null) {
//			captureImageOsg = osgObject.getBucketName() + "/" + objectStorageGatewayObjectInfo;
//		} else {
//			log.warn("objectStorageGatewayObjectInfo is null {},{}", osgObject.getBucketName(), sceneImage);
//		}
//
//		return captureImageOsg;
//	}
	
	public PedestrianTrack getCurrentAssociation() {
		if(association != null)
			return association;
		
		return lostAssociation;
	}
	
	public Tracklet getCurrentTracklet() {
		if(away != null)
			return away;
		
		if(!trackings.isEmpty())
			return trackings.getFirst();
		
		return enter;
	}
	
	public void setAway(Tracklet away) {
		this.away = away;
		this.trackFinished = true;
		
		PedestrianTrack association = getCurrentAssociation();
		if(association != null) {
			association.setLostAssociation(association.getAssociation());
			association.setAssociation(null);
		}
	}
	
	public void addIdentifiedInfo(List<IdentifiedInfo> infos) {
		List<IdentifiedInfo> newInfos = new ArrayList<IdentifiedInfo>(infos.size() + identifiedInfos.size());
		newInfos.addAll(infos);	
		newInfos.addAll(identifiedInfos);
		
		identifiedInfos = newInfos;
	}
	
	public int trackIndex() {
		int index = trackings.size();
		
		if(enter != null)
			index ++;
		
		if(away != null)
			index ++;
		
		return index;
	}
	
	public static final void hookupTrack(Map<Integer, PedestrianTrack> onGoingTrackMap, PedestrianTrack currentTrack, Tracklet newTracklet, boolean logged) {
		if(CollectionUtils.isEmpty(newTracklet.getAssociations()))
			return ;

		int associationTrackId = newTracklet.getAssociations().get(0).getTrack_id();
		PedestrianTrack associationTrack = onGoingTrackMap.get(associationTrackId);

        //发现人体有关联，人脸丢失关联，手动补充
		if(associationTrack == null && associationTrackId > 0) {

			Tracklet tracklet = Tracklet.builder()
					.label(newTracklet.getAssociations().get(0).getLabel())
					.track_id(associationTrackId)
					.build();

			associationTrack = PedestrianTrack.builder().device(currentTrack.getDevice()).pedestrian(currentTrack.getPedestrian()).enter(tracklet).build();
		}

		//关联log
		if(logged) {
			log.info("pedTrace two hookupTrack label={},track_id={},associationTrackId={},{},associationTrack={},{}",newTracklet.getLabel(), newTracklet.getTrack_id(), associationTrackId, currentTrack.getAssociation() == null , associationTrack !=null,(associationTrack !=null) ?associationTrack.getAssociation() == null:"empty");
		}

		if(associationTrack != null && currentTrack.getAssociation() == null && associationTrack.getAssociation() == null) {
			associationTrack.setAssociation(currentTrack);
			currentTrack.setAssociation(associationTrack);
		}
	}
	
	public static List<IdentifiedInfo> findNearestIdentifiedInfo(PedestrianTrack track) {
		List<IdentifiedInfo> identifiedInfos = track.getIdentifiedInfos();
		if(CollectionUtils.isEmpty(identifiedInfos))
			return Lists.newArrayList();
		
		int index = identifiedInfos.get(identifiedInfos.size() - 1).getTrackIndex();
		
		return identifiedInfos.stream().filter(iden -> iden.getTrackIndex() == index).collect(Collectors.toList());
	}
	
	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	@Accessors(chain = true)
	public static class IdentifiedInfo implements Cloneable{
		
		/** 识别出来的人员编号*/
		private volatile String identifiedPersonId;
		
		/** 识别出来的人员比对得分*/
		private volatile Float identifiedScore;
		
		/** 识别出来的人员标签*/
		private volatile String identifiedPersonTag;
		
		/** 识别出来的人员类型: 0->person, 1->passer */
		private volatile Integer identifiedPersonType;
		
		/** 识别出来的人员中文名*/
		private volatile String identifiedPersonCnName;
		
		/** 识别出来的人员英文名*/
		private volatile String identifiedPersonEnName;
		
		/** 识别出来的人员照片路径*/
		private volatile String identifiedPersonAvatar;
		
		/** 添加陌生人之后 特征暂存此处*/
		private volatile float[] identifiedFeature;
		
		/**第几次识别的结果*/
		private volatile int trackIndex;
		
		@Override
		public IdentifiedInfo clone() {
			try {
				return (IdentifiedInfo)super.clone();
			} catch (CloneNotSupportedException e) {
				throw new RuntimeException(e);
			}
		}
		
		public static IdentifiedInfo of(PersonFaceObject obj, Float score, int trackIndex) {
			return IdentifiedInfo.builder()
						.identifiedPersonCnName(obj.cnName)
						.identifiedPersonEnName(obj.enName)
						.identifiedPersonId(obj.pid)
						.identifiedPersonTag(obj.tag)
						.identifiedPersonType(StreamPedestrianUtils.PERSON)
						.identifiedScore(score)
						.trackIndex(trackIndex)
						.identifiedPersonAvatar(obj.avatar)
						.build();
		}
		
		public static IdentifiedInfo of(PasserFaceObject obj, Float score, int trackIndex) {
			return IdentifiedInfo.builder()
					.identifiedPersonId(obj.pid)
					.identifiedPersonAvatar(obj.avatar)
					.identifiedScore(score)
					.trackIndex(trackIndex)
					.identifiedPersonType(StreamPedestrianUtils.PASSER)
					.build();
		}
		
		public static IdentifiedInfo of(PersonBodyObject obj, Float score, int trackIndex) {
			return IdentifiedInfo.builder()
						.identifiedPersonCnName(obj.cnName)
						.identifiedPersonEnName(obj.enName)
						.identifiedPersonId(obj.pid)
						.identifiedPersonTag(obj.tag)
						.identifiedPersonType(StreamPedestrianUtils.PERSON)
						.identifiedScore(score)
						.trackIndex(trackIndex)
						.identifiedPersonAvatar(obj.avatar)
						.build();
		}
		
		public static IdentifiedInfo of(PasserBodyObject obj, Float score, int trackIndex) {
			return IdentifiedInfo.builder()
					.identifiedPersonId(obj.pid)
					.identifiedPersonAvatar(obj.avatar)
					.identifiedScore(score)
					.trackIndex(trackIndex)
					.identifiedPersonType(StreamPedestrianUtils.PASSER)
					.build();
		}
	}
}
