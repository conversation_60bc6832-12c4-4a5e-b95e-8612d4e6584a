package com.sensetime.intersense.cognitivesvc.pedestrian;

import com.sensetime.intersense.cognitivesvc.pedestrian.handler.FeatureModelHandler;
import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianFeatureModelHandler;
import jakarta.annotation.PostConstruct;

import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.ConfigurableEnvironment;

import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianFeatureAttributePipeline;
import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianFeaturePipeline;
import com.sensetime.intersense.cognitivesvc.pedestrian.utils.PedestrianInitializer;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils.Sync;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

@Configuration("pedestrianConfiguation")
@ComponentScan
@ConditionalOnExpression("${pedestrian.enabled:true} && ${seeker.enabled:true} && ${seeker.pedestrian.enabled:true} && ${seeker.face.enabled:true}")
@Slf4j
@PropertySource("classpath:pedestrian.properties")
public class CognitivesvcConfiguation {

	@Autowired
	private ConfigurableEnvironment env;
	
	@Autowired
	private Sync sync;

	@Value("${preMakeDirs}")
	private String preMakeDirs;
	
	@PostConstruct
	public void initialize(){		
		PedestrianInitializer.initialize(env);
		
		log.warn("\n");
		log.warn("******************************************");
		log.warn("*************init pedestrian**************");
		log.warn("****pedestrian.enabled=false to disable***");
		log.warn("******seeker.enabled=false to disable*****");
		log.warn("***seeker.face.enabled=false to disable***");
		log.warn("seeker.pedestrian.enabled=false to disable");
		log.warn("******************************************");
		log.warn("\n");

//		List<String> preMakeDirList = Arrays.asList(preMakeDirs.split(","));
//		sync.sync();
//		ImageUtils.mkdirsByHour(preMakeDirList,true);
//		ImageUtils.mkdirsByHour(preMakeDirList,false);

	}
	
    @Bean
    public PedestrianFeaturePipeline pedestrianFeaturePipeline() {
    	sync.sync();
    	return new PedestrianFeaturePipeline();
    }
	
    @Bean
    public PedestrianFeatureAttributePipeline pedestrianFeatureAttributePipeline() {
    	sync.sync();
    	return new PedestrianFeatureAttributePipeline();
    }

	@Bean
	public FeatureModelHandler featureModelHandler() {
		PedestrianInitializer.initialize(env);
		return new FeatureModelHandler();
	}

	@Bean
	public PedestrianFeatureModelHandler pedestrianFeatureModelHandler() {
		PedestrianInitializer.initialize(env);
		return new PedestrianFeatureModelHandler();
	}
}
