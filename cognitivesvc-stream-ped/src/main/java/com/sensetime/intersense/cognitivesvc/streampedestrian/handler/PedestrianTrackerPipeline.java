package com.sensetime.intersense.cognitivesvc.streampedestrian.handler;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamPedestrian;
import com.sensetime.intersense.cognitivesvc.server.event.RebalancedEvent;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamPedestrianRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class PedestrianTrackerPipeline{

	public static final String stageOne = "video_face_body_track_stream";

	public static final String stageTwo = "video_face_body_analyze_stream";

	@Getter
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
	 
	private Pointer pipeline;
	
	@Autowired
	private VideoStreamInfraRepository deviceMapper;

	@Autowired
	private VideoStreamPedestrianRepository pedestrianMapper;

	@Getter
	private volatile Map<String, VideoStreamInfra> streamInfras = new HashMap<String, VideoStreamInfra>();

	@Getter
	private volatile Map<String, VideoStreamPedestrian> streamPeds = new HashMap<String, VideoStreamPedestrian>();

	@Getter
	private volatile Map<String, Pair<VideoStreamInfra, VideoStreamPedestrian>> streamPairs = new HashMap<String, Pair<VideoStreamInfra, VideoStreamPedestrian>>();

	@Getter
	private volatile Map<Long, Pair<VideoStreamInfra, VideoStreamPedestrian>> longPairs = new HashMap<Long, Pair<VideoStreamInfra, VideoStreamPedestrian>>();
	
	@Getter
	private final ConcurrentHashMap<Long, Map<Integer, PedestrianTrack>> onGoingTracks = new ConcurrentHashMap<Long, Map<Integer, PedestrianTrack>>();

	private static final String roi_filter =    "[]";

	public void initContext(String deviceId, long contextId) {
		log.info("[initContext] initContext deviceId: {} ,contextId: {}",deviceId,contextId);
		if(onGoingTracks.containsKey(contextId))
			return ;
		
		VideoStreamInfra infra = streamInfras.get(deviceId);
		VideoStreamPedestrian pedestrian = streamPeds.get(deviceId);
		
		PedestrianTrackerContextConfig config = PedestrianTrackerContextConfig.builder()
				.harpyEnabled(Utils.instance.pedestrianHarpyEnabled)
				.faceLargeRoiExpand(Utils.instance.faceLargeRoiExpand)
				.contextId(contextId)
				.video_width(infra.getRtspWidth())
				.video_height(infra.getRtspHeight())
				.face_quality_thresh(Objects.requireNonNullElse(Utils.instance.selectFrameThresh, 0.2f))
				.ped_quality_thresh(Objects.requireNonNullElse(Utils.instance.selectPedFrameThresh, 0.3f))
				.quick_response_time(Objects.requireNonNullElse(pedestrian.getQuickResponseTime(), 1))
				.time_interval(Objects.requireNonNullElse(pedestrian.getTimeInterval(),  pedestrian.getSelectFrameTimeInterVal()))
				.max_track_time(Objects.requireNonNullElse(pedestrian.getMaxTrackTime(), 120))
				.max_tracklet_num(Objects.requireNonNullElse(pedestrian.getMaxTrackletNum(), 512))
				.small_quality_thresh(Objects.requireNonNullElse(pedestrian.getProcessFace().getSmallDetectThresh(), Objects.requireNonNullElse(Utils.instance.smallDetectThresh, 0.3f)))
				.large_quality_thresh(Objects.requireNonNullElse(pedestrian.getProcessFace().getLargeDetectThresh(), Objects.requireNonNullElse(Utils.instance.largeDetectThresh, 0.65f)))
				.face_track_quality_thresh(Objects.requireNonNullElse(pedestrian.getProcessFace().getFaceTrackQualityThresh(), Objects.requireNonNullElse(Utils.instance.smallDetectThresh, 0.3f)))
				.ped_track_quality_thresh(Objects.requireNonNullElse(pedestrian.getProcessFace().getPedTrackQualityThresh(), Objects.requireNonNullElse(Utils.instance.largeDetectThresh, 0.65f)))

				.roi_filter(getSelectRoiFilter(pedestrian.getRoi(), roi_filter))
				.build();
		
		PointerByReference input = KesonUtils.stringToKeson(config.toString());
		PointerByReference out = new PointerByReference();
		log.info("flock_pipeline_control:{}", config);
		try {
			lock.writeLock().lock();
			
			KestrelApi.flock_pipeline_control(pipeline(), KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);

			onGoingTracks.put(contextId, new HashMap<Integer, PedestrianTrack>());
			log.info("flock_pipeline_control_out:{}", KesonUtils.kesonToString(out));
		}finally {
			lock.writeLock().unlock();
			KesonUtils.kesonDeepDelete(input, out);
		}
	}

	public void reInitContext(String deviceId, long contextId) {
		if(!onGoingTracks.containsKey(contextId))
			return ;

		VideoStreamInfra infra = streamInfras.get(deviceId);
		VideoStreamPedestrian pedestrian = streamPeds.get(deviceId);

		PedestrianTrackerContextConfig config = PedestrianTrackerContextConfig.builder()
				.contextId(contextId)
				.video_width(infra.getRtspWidth())
				.video_height(infra.getRtspHeight())
				.face_quality_thresh(Objects.requireNonNullElse(Utils.instance.selectFrameThresh, 0.2f))
				.ped_quality_thresh(Objects.requireNonNullElse(Utils.instance.selectPedFrameThresh, 0.3f))
				.quick_response_time(Objects.requireNonNullElse(pedestrian.getQuickResponseTime(), -1))
				.time_interval(Objects.requireNonNullElse(pedestrian.getTimeInterval(), pedestrian.getSelectFrameTimeInterVal()))
				.max_track_time(Objects.requireNonNullElse(pedestrian.getMaxTrackTime(), 120))
				.max_tracklet_num(Objects.requireNonNullElse(pedestrian.getMaxTrackletNum(), 512))
				.small_quality_thresh(Objects.requireNonNullElse(pedestrian.getProcessFace().getSmallDetectThresh(), Objects.requireNonNullElse(Utils.instance.smallDetectThresh, 0.3f)))
				.large_quality_thresh(Objects.requireNonNullElse(pedestrian.getProcessFace().getLargeDetectThresh(), Objects.requireNonNullElse(Utils.instance.largeDetectThresh, 0.65f)))
				.face_track_quality_thresh(Objects.requireNonNullElse(pedestrian.getProcessFace().getFaceTrackQualityThresh(), Objects.requireNonNullElse(Utils.instance.smallDetectThresh, 0.3f)))
				.ped_track_quality_thresh(Objects.requireNonNullElse(pedestrian.getProcessFace().getPedTrackQualityThresh(), Objects.requireNonNullElse(Utils.instance.largeDetectThresh, 0.65f)))
				.roi_filter(getSelectRoiFilter(pedestrian.getRoi(), roi_filter))
				.build();

		PointerByReference input = KesonUtils.stringToKeson(config.toString());
		log.info("flock_pipeline_control:{}", config);
		PointerByReference out = new PointerByReference();
		try {
			lock.writeLock().lock();

			KestrelApi.flock_pipeline_control(rePipeline(), KestrelApi.FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG, input.getValue(), out);
			log.info("flock_pipeline_control:{}", KesonUtils.kesonToString(out));
			//onGoingTracks.put(contextId, new HashMap<Integer, FaceTrack>());
		}finally {
			lock.writeLock().unlock();
			KesonUtils.kesonDeepDelete(input, out);
		}
	}

	public Pointer rePipeline(){
//		if(pipeline != null)
//			return pipeline;

		synchronized(this) {
//			if(pipeline != null)
//				return pipeline;

			PedestrianTrackerContextConfig config = PedestrianTrackerContextConfig.builder()
					.harpyEnabled(Utils.instance.pedestrianHarpyEnabled)
					.faceLargeRoiExpand(Utils.instance.faceLargeRoiExpand)
					.face_quality_thresh(Utils.instance.selectFrameThresh)
					.ped_quality_thresh(Utils.instance.selectPedFrameThresh)
					.max_tracklet_num(Utils.instance.nonSeenMaxTrackletCount)
					.build();

			//log.info("ped_flock_pipeline_create : " + config.toString());

			pipeline = KestrelApi.flock_pipeline_create(config.toString());
		}

		return pipeline;
	}
	
	public void destroyContext(String deviceId, long contextId) {
		Map<Integer, PedestrianTrack> contextTracks = onGoingTracks.remove(contextId);
		if(contextTracks == null)
			return ;
		
		Pointer input = KesonUtils.buildFlockRemoveInput(contextId, stageOne, stageTwo);
		PointerByReference out = new PointerByReference();
		try {
			lock.writeLock().lock();
			KestrelApi.flock_pipeline_control(pipeline(), KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input, out);
			
			for(PedestrianTrack track : contextTracks.values())
				track.close();
		}finally {
			lock.writeLock().unlock();
			
			KesonUtils.kesonDeepDelete(input, out.getValue());
		}
	}
	
	public Pointer pipeline(){
		if(pipeline != null)
			return pipeline;
		
		synchronized(this) {
			if(pipeline != null)
				return pipeline;
			
			PedestrianTrackerContextConfig config = PedestrianTrackerContextConfig.builder()
					.harpyEnabled(Utils.instance.pedestrianHarpyEnabled)
					.faceLargeRoiExpand(Utils.instance.faceLargeRoiExpand)
					.face_quality_thresh(Utils.instance.selectFrameThresh)
					.ped_quality_thresh(Utils.instance.selectPedFrameThresh)
					.max_tracklet_num(Utils.instance.nonSeenMaxTrackletCount)
					.large_quality_thresh(Objects.requireNonNullElse(Utils.instance.largeDetectThresh, 0.65f))
					.build();
			
			//log.info("ped_flock_pipeline_create : " + config.toString());
			
			pipeline = KestrelApi.flock_pipeline_create(config.toString());

			log.info("ped_flock_pipeline_create_out_pointer : {}",pipeline);
		}
		
		return pipeline;
	}

	@Order(value = RebalancedEvent.NoMatter)
	@EventListener(classes = RebalancedEvent.class)
	protected void onApplicationEvent(RebalancedEvent event){		
		streamPeds = pedestrianMapper.findAll().stream().collect(Collectors.toMap(VideoStreamPedestrian::getDeviceId, Function.identity()));
		streamInfras = deviceMapper.findAll().stream().collect(Collectors.toMap(VideoStreamInfra::getDeviceId, Function.identity()));	
		streamPairs = Stream.of(streamPeds.keySet(), streamInfras.keySet()).flatMap(Set::stream).distinct().filter(deviceId -> streamInfras.containsKey(deviceId) && streamPeds.containsKey(deviceId)).collect(Collectors.toMap(Function.identity(), deviceId -> new MutablePair<VideoStreamInfra, VideoStreamPedestrian>(streamInfras.get(deviceId), streamPeds.get(deviceId))));
		longPairs = Stream.of(streamPeds.keySet(), streamInfras.keySet()).flatMap(Set::stream).distinct().filter(deviceId -> streamInfras.containsKey(deviceId) && streamPeds.containsKey(deviceId)).collect(Collectors.toMap(deviceId -> Utils.keyToContextId(deviceId), deviceId -> new MutablePair<VideoStreamInfra, VideoStreamPedestrian>(streamInfras.get(deviceId), streamPeds.get(deviceId))));
	}
	
	@Data
	@Accessors(chain = true)
	@Builder
	public static class PedestrianTrackerContextConfig{
		
		public Long contextId;
		
		public boolean harpyEnabled;

		public boolean faceLargeRoiExpand;

		@Builder.Default
		public int video_width = 1920;
		
		@Builder.Default
		public int video_height = 1080;
		
		@Builder.Default
		public int max_tracklet_num = 512;

		@Builder.Default
		public int max_tracklet_item_size = 3;
		
		@Builder.Default
		public float face_quality_thresh = 0.2f;

		@Builder.Default
		public float ped_quality_thresh = 0.3f;
		
		@Builder.Default
		public int quick_response_time = -1;

		@Builder.Default
		public float small_quality_thresh = 0.3f;

		@Builder.Default
		public float face_track_quality_thresh = 0.3f;

		@Builder.Default
		public float large_quality_thresh = 0.65f;

		@Builder.Default
		public float ped_track_quality_thresh = 0.65f;
		
		@Builder.Default
		public int time_interval = -1;
		
		@Builder.Default
		public int max_track_time = 120;
		
		@Builder.Default
		public float expand_ratio = 1.5f;

		@Builder.Default
		public Object roi_filter =   "[]";

		private String contextIdString() {
			if(contextId == null)
				return "";
			
			return "\"source_id\": " + contextId + ", \"context_id\": " + contextId + ",";
		}
		
		@Override
		public String toString() {
			if(faceLargeRoiExpand){
				return faceLargeRoiExpandConfig();
			}else if(harpyEnabled)
				return faceHarpyConfig();
			else
				return faceBodyConfig();
		}
		private String faceLargeRoiExpandConfig() {

            return "{" + contextIdString() +
                    "    \"streams\":[" +
                    "        {" +
                    "            \"name\": \"" + stageOne + "\"," + contextIdString() +
                    "            \"module_plugins\":[" +
                    "                \"detection.fmd\"," +
                    "                \"single_target_tracking.fmd\"," +
                    "                \"id_allocation.fmd\"," +
                    "                \"multi_target_tracking_with_horae.fmd\"," +
                    "                \"multiple_target_tracking.fmd\"," +
                    "                \"slice.fmd\"," +
                    "                \"concat.fmd\"," +
                    "                \"face_body_matching.fmd\"," +
                    "                \"plugin.fmd\"," +
                    "                \"target_filter.fmd\"," +
                    "                \"mergence.fmd\"," +
                    "                \"matching_result_conversion.fmd\"," +
                    "                \"roi_expand.fmd\"," +
                    "                \"detection_filter.fmd\"," +
                    "                \"face_validate.fmd\"," +
                    "                \"track_recall.fmd\"" +
                    "            ]," +
                    "            \"modules\":[" +
                    "                {" +
                    "                    \"name\":\"input\","  + contextIdString() +
                    "                    \"type\":\"Input\"," +
                    "                    \"inputs\":[" +
                    "" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"images\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_detector\","  + contextIdString() +
                    "                    \"type\":\"Detection\"," +
                    "                    \"parallel_group\":\"face_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"images\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"detected_faces_small\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"hunter\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module") + "\"," +
                    "                        \"max_batch_size\":16," +
                    "                        \"confidence_threshold\":" + + small_quality_thresh  +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_filter\","  + contextIdString() +
                    "                    \"type\":\"DetectionFilter\"," +
                    "                    \"parallel_group\":\"face_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"detected_faces_small\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"detected_faces_small_filtered\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"roi_filter\":[" +
                    "" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_target_tracker\","  + contextIdString() +
                    "                    \"type\":\"MultipleTargetTracking\"," +
                    "                    \"parallel_group\":\"face_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"detected_faces_small_filtered\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_smallface_targets\"," +
                    "                        \"dropped_face_ids\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"roi_expander\","  + contextIdString() +
                    "                    \"type\":\"RoiExpand\"," +
                    "                    \"parallel_group\":\"face_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"tracked_smallface_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_smallface_targets_expanded\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"roi_expand_ratio\":1" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_detector_large\","  + contextIdString() +
                    "                    \"type\":\"DetectionInROI\"," +
                    "                    \"parallel_group\":\"face_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"tracked_smallface_targets_expanded\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_face_targets_\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"hunter\"," +
                    "                        \"model\":\"/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_large_module") + "\"," +
                    "                        \"max_batch_size\":48," +
                    "                        \"confidence_threshold\":" + large_quality_thresh +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"track_recall\","  + contextIdString() +
                    "                    \"type\":\"TrackRecall\"," +
                    "                    \"parallel_group\":\"face_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"tracked_face_targets_\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_face_targets\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_id_redefine\","  + contextIdString() +
                    "                    \"type\":\"IdAllocation\"," +
                    "                    \"parallel_group\":\"face_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"tracked_face_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_face_targets_uid\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"struct_detector\","  + contextIdString() +
                    "                    \"type\":\"Detection\"," +
                    "                    \"parallel_group\":\"body_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"images\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"struct_detected_targets\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"essos\"," +
                    "                        \"model\":\"/usr/cognitivesvc/"+ Initializer.modelPathMap.get("essos_struct_module") + "\"," +
                    "                        \"max_batch_size\":8" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"struct_filter\","  + contextIdString() +
                    "                    \"type\":\"DetectionFilter\"," +
                    "                    \"parallel_group\":\"body_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"struct_detected_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"struct_detected_targets_filtered\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"roi_filter\":[" +
                    "" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"body_target_tracker\","  + contextIdString() +
                    "                    \"type\":\"MultipleTargetTracking\"," +
                    "                    \"parallel_group\":\"body_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"struct_detected_targets_filtered\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_struct_targets\"," +
                    "                        \"dropped_struct_ids\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"target_slice0\","  + contextIdString() +
                    "                    \"type\":\"Slice\"," +
                    "                    \"parallel_group\":\"body_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"tracked_struct_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_body_targets\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"by_labels\":[" +
                    "                            221488" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"struct_id_redefine\","  + contextIdString() +
                    "                    \"type\":\"IdAllocation\"," +
                    "                    \"parallel_group\":\"body_detect\"," +
                    "                    \"inputs\":[" +
                    "                        \"tracked_body_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_body_targets_uid\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"track_targets_concat\","  + contextIdString() +
                    "                    \"type\":\"Concat\"," +
                    "                    \"inputs\":[" +
                    "                        \"tracked_face_targets_uid\"," +
                    "                        \"tracked_body_targets_uid\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"tracked_targets\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"concat_items\":[" +
                    "                            \"targets\"" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"dropped_ids_concat\","  + contextIdString() +
                    "                    \"type\":\"Concat\"," +
                    "                    \"inputs\":[" +
                    "                        \"dropped_face_ids\"," +
                    "                        \"dropped_struct_ids\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"dropped_ids\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"concat_items\":[" +
                    "                            \"targets\"" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_body_matching\","  + contextIdString() +
                    "                    \"type\":\"FaceBodyMatching\"," +
                    "                    \"parallel_group\":\"matching\"," +
                    "                    \"inputs\":[" +
                    "                        \"tracked_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"matching_targets\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"cupid\"" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"match_result_converse\","  + contextIdString() +
                    "                    \"type\":\"MatchingResultConversion\"," +
                    "                    \"parallel_group\":\"matching\"," +
                    "                    \"inputs\":[" +
                    "                        \"matching_targets\"," +
                    "                        \"tracked_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"associated_facebodys\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"output\","  + contextIdString() +
                    "                    \"type\":\"Output\"," +
                    "                    \"parallel_group\":\"output\"," +
                    "                    \"inputs\":[" +
                    "                        \"associated_facebodys\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "" +
                    "                    ]" +
                    "                }" +
                    "            ]" +
                    "        }," +
                    "        {" +
                    "            \"name\": \"" + stageTwo + "\"," + contextIdString() +
                    "            \"module_plugins\":[" +
                    "                \"slice.fmd\"," +
                    "                \"concat.fmd\"," +
                    "                \"plugin.fmd\"," +
                    "                \"operation.fmd\"," +
                    "                \"mergence.fmd\"," +
                    "                \"abg_target_selection.fmd\"," +
                    "                \"refinement.fmd\"," +
                    "                \"roi_filter.fmd\"," +
                    "                \"face_quality.fmd\"," +
                    "                \"face_quality_filter.fmd\"," +
                    "                \"face_body_matching_filter.fmd\"," +
                    "                \"face_body_case_merge.fmd\"" +
                    "            ]," +
                    "            \"modules\":[" +
                    "                {" +
                    "                    \"name\":\"input\","  + contextIdString() +
                    "                    \"type\":\"Input\"," +
                    "                    \"parallel_group\":\"input\"," +
                    "                    \"inputs\":[" +
                    "" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"associated_facebodys\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"target_slice\","  + contextIdString() +
                    "                    \"type\":\"Slice\"," +
                    "                    \"parallel_group\":\"quality\"," +
                    "                    \"inputs\":[" +
                    "                        \"associated_facebodys\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_targets\"," +
                    "                        \"body_targets\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"by_labels\":[" +
                    "                            37017," +
                    "                            221488" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"confidence_to_quality\","  + contextIdString() +
                    "                    \"type\":\"Operation\"," +
                    "                    \"parallel_group\":\"quality\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_quality\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"operations\":[" +
                    "                            {" +
                    "                                \"cmd\":\"copy\"," +
                    "                                \"args\":[" +
                    "                                    \"confidence\"," +
                    "                                    \"quality\"" +
                    "                                ]" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"body_quality_calculate\","  + contextIdString() +
                    "                    \"type\":\"Plugin\"," +
                    "                    \"parallel_group\":\"quality\"," +
                    "                    \"inputs\":[" +
                    "                        \"body_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"raw_body_quality\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"classifier\"," +
                    "                        \"model\":\"/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_filter_module") + "\"," +
                    "                        \"max_batch_size\":32" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"body_quality_pack\","  + contextIdString() +
                    "                    \"type\":\"Operation\"," +
                    "                    \"parallel_group\":\"quality\"," +
                    "                    \"inputs\":[" +
                    "                        \"raw_body_quality\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"body_quality\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"operations\":[" +
                    "                            {" +
                    "                                \"cmd\":\"move\"," +
                    "                                \"args\":[" +
                    "                                    \"attribute/ped_quality/total\"," +
                    "                                    \"quality\"" +
                    "                                ]" +
                    "                            }," +
                    "                            {" +
                    "                                \"cmd\":\"remove\"," +
                    "                                \"args\":[" +
                    "                                    \"attribute\"" +
                    "                                ]" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"targets_merge\","  + contextIdString() +
                    "                    \"type\":\"Mergence\"," +
                    "                    \"parallel_group\":\"quality\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_quality\"," +
                    "                        \"body_quality\"," +
                    "                        \"associated_facebodys\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"merged_targets\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"target_selector\","  + contextIdString() +
                    "                    \"type\":\"AbgTargetSelection\"," +
                    "                    \"parallel_group\":\"target_selector\"," +
                    "                    \"inputs\":[" +
                    "                        \"merged_targets\"," +
                    "                        \"dropped_ids\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"target_tracklets\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"max_device_memory_usage_per_source\":128," +
                    "                        \"max_roi_ref_frame_size\":512," +
                    "                        \"max_source_tracklet_num\":16," +
                    "                        \"keep_low_quality_target\":true," +
                    "                        \"selection\":[" +
                    "                            {" +
                    "                                \"label_id\":37017," +
                    "                                \"quick_response_time\":" + quick_response_time + "," +
                    "                                \"time_interval\":" + time_interval + "," +
                    "                                \"max_track_time\":" + max_track_time + "," +
                    "                                \"roi_expand_ratio\":1," +
                    "                                \"quality_threshold\":" + face_quality_thresh + "," +
                    "                                \"max_tracklet_num\":" + max_tracklet_num + "," +
                    "                                \"max_tracklet_item_size\":" + max_tracklet_item_size  +
                    "                            }," +
                    "                            {" +
                    "                                \"label_id\":221488," +
                    "                                \"quick_response_time\":" + quick_response_time + "," +
                    "                                \"time_interval\":" + time_interval + "," +
                    "                                \"max_track_time\":" + max_track_time + "," +
                    "                                \"quality_threshold\":" + face_quality_thresh + "," +
					"                                \"max_tracklet_num\":" + max_tracklet_num + "," +
                    "                                \"max_tracklet_item_size\":" + max_tracklet_item_size  +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"target_slice1\","  + contextIdString() +
                    "                    \"type\":\"Slice\"," +
                    "                    \"parallel_group\":\"select\"," +
                    "                    \"inputs\":[" +
                    "                        \"target_tracklets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_tracklets\"," +
                    "                        \"body_tracklets\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"by_labels\":[" +
                    "                            37017," +
                    "                            221488" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"aligner\","  + contextIdString() +
                    "                    \"type\":\"Plugin\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_tracklets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_landmarks\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"aligner\"," +
                    "                        \"model\":\"/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_occlusion_module") + "\"" + "," +

                    "                        \"max_batch_size\":64" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_landmarks_pack\","  + contextIdString() +
                    "                    \"type\":\"Operation\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_landmarks\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_landmarks\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"operations\":[" +
                    "                            {" +
                    "                                \"cmd\":\"move\"," +
                    "                                \"args\":[" +
                    "                                    \"confidence\"," +
                    "                                    \"aligner_confidence\"" +
                    "                                ]" +
                    "                            }" +
                    "                        ]" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_headpose\","  + contextIdString() +
                    "                    \"type\":\"Plugin\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_landmarks\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_headpose\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"headpose\"," +
                    "                        \"model\":\"/usr/cognitivesvc/"+Initializer.modelPathMap.get("headpose_module") + "\"," +
                    "                        \"max_batch_size\":32" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"blur_landmark_headpose_merge\","  + contextIdString() +
                    "                    \"type\":\"Mergence\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_landmarks\"," +
                    "                        \"face_headpose\"," +
                    "                        \"face_tracklets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_targets_quality_element\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_quality_update\","  + contextIdString() +
                    "                    \"type\":\"FaceQuality\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_targets_quality_element\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_tracklets_with_landmarks\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_feature_extraction\","  + contextIdString() +
                    "                    \"type\":\"Plugin\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_tracklets_with_landmarks\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_features\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"feature\"," +
                    "                        \"model\":\"/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module") + "\"," +
                    "                        \"max_batch_size\":32" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"body_feature_extraction\","  + contextIdString() +
                    "                    \"type\":\"Plugin\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"body_tracklets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"body_features\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"senu\"," +
                    "                        \"model\":\"/usr/cognitivesvc/" + Initializer.modelPathMap.get("senu_feature_module") +"\"," +
                    "                        \"max_batch_size\":32" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_feature_mergence\","  + contextIdString() +
                    "                    \"type\":\"Mergence\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_features\"," +
                    "                        \"face_tracklets_with_landmarks\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_tracklets_with_feature\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"body_feature_mergence\","  + contextIdString() +
                    "                    \"type\":\"Mergence\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"body_features\"," +
                    "                        \"body_tracklets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"body_tracklets_with_feature\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"update\":true" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_attribute_extraction\","  + contextIdString() +
                    "                    \"type\":\"Plugin\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_tracklets_with_feature\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"face_attributes\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"attribute\"," +
                    "                        \"model\":\"/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_Attribute_module") + "\"," +
                    "                        \"max_batch_size\":64" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"body_attribute_extraction\","  + contextIdString() +
                    "                    \"type\":\"Plugin\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"body_tracklets_with_feature\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"body_attributes\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"plugin\":\"classifier\"," +
                    "                        \"model\":\"/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_attribute_module")+ "\"," +
                    "                        \"max_batch_size\":16" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_targets_mergence\","  + contextIdString() +
                    "                    \"type\":\"Mergence\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"face_attributes\"," +
                    "                        \"face_tracklets_with_feature\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"analyzed_face_targets\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"body_targets_mergence\","  + contextIdString() +
                    "                    \"type\":\"Mergence\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"body_attributes\"," +
                    "                        \"body_tracklets_with_feature\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"analyzed_body_targets\"" +
                    "                    ]" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"face_body_merge\","  + contextIdString() +
                    "                    \"type\":\"FaceBodyCaseMerge\"," +
                    "                    \"parallel_group\":\"target_analysis\"," +
                    "                    \"inputs\":[" +
                    "                        \"analyzed_face_targets\"," +
                    "                        \"analyzed_body_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "                        \"analyzed_targets\"" +
                    "                    ]," +
                    "                    \"config\":{" +
                    "                        \"track_frame_threshold\":2," +
                    "                        \"max_waiting_list\":10" +
                    "                    }" +
                    "                }," +
                    "                {" +
                    "                    \"name\":\"output\","  + contextIdString() +
                    "                    \"type\":\"Output\"," +
                    "                    \"parallel_group\":\"output\"," +
                    "                    \"inputs\":[" +
                    "                        \"analyzed_targets\"" +
                    "                    ]," +
                    "                    \"outputs\":[" +
                    "" +
                    "                    ]" +
                    "                }" +
                    "            ]" +
                    "        }" +
                    "    ]" +
                    "}";
		}
		private String faceHarpyConfig() {
			return "{" + contextIdString() +
					"    \"streams\": [" +
					"        {" +
					"            \"name\": \"" + stageOne + "\"," + contextIdString() +
					"            \"module_plugins\": [" +
					"                \"detection.fmd\"," +
					"                \"single_target_tracking.fmd\"," +
					"                \"id_allocation.fmd\"," +
					"                \"multi_target_tracking_with_horae.fmd\"," +
					"                \"multiple_target_tracking.fmd\"," +
					"                \"slice.fmd\"," +
					"                \"concat.fmd\"," +
					"                \"face_body_matching.fmd\"," +
					"                \"plugin.fmd\"," +
					"                \"target_filter.fmd\"," +
					"                \"mergence.fmd\"," +
					"                \"matching_result_conversion.fmd\"" +
					"            ]," +
					"            \"modules\": [" +
					"                {" +
					"                    \"name\": \"input\"," +  contextIdString() +
					"                    \"type\": \"Input\"," +
					"                    \"inputs\": []," +
					"                    \"outputs\": [" +
					"                        \"images\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_detector\"," +  contextIdString() +
					"                    \"type\": \"Detection\"," +
					"                    \"parallel_group\": \"face_detect\"," +
					"                    \"inputs\": [" +
					"                        \"images\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"hunter\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module") + "\"," +
					"                        \"max_batch_size\": 16," +
					"                        \"confidence_threshold\": 0.3" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_id_redefine\"," +  contextIdString() +
					"                    \"type\": \"IdAllocation\"," +
					"                    \"parallel_group\": \"face_detect\"," +
					"                    \"inputs\": [" +
					"                        \"face_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_targets_uid\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_target_tracker\"," +  contextIdString() +
					"                    \"type\": \"MultipleTargetTracking\"," +
					"                    \"parallel_group\": \"face_detect\"," +
					"                    \"inputs\": [" +
					"                        \"face_targets_uid\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_face_targets\"," +
					"                        \"dropped_face_ids\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"struct_detector\"," +  contextIdString() +
					"                    \"type\": \"Detection\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"images\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"struct_detected_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"block_frame_num\": 1," +
					"                        \"plugin\": \"harpy\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("harpy_module") + "\"," +
					"                        \"max_batch_size\": 8," +
					"                        \"target_label_configs\": [{\"label_id\": 221488}]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"struct_single_target_tracker\"," +  contextIdString() +
					"                    \"type\": \"SingleTargetTracking\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"struct_detected_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"sot_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"hermes\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hermes_module") + "\"," +
					"                        \"max_batch_size\": 128," +
					"                        \"min_filter_width\": 0," +
					"                        \"min_filter_heigth\": 0," +
					"                        \"filter\": false" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"struct_multi_target_tracker\"," +  contextIdString() +
					"                    \"type\": \"MultiTargetTracking\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"sot_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"temp_struct_targets\"," +
					"                        \"dropped_struct_ids\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"drop_timeout\": -1," +
					"                        \"hermes\": {" +
					"                            \"plugin\": \"hermes\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hermes_module") + "\"," +
					"                            \"max_batch_size\": 128" +
					"                        }," +
					"                        \"horae\": {" +
					"                            \"plugin\": \"horae\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("horae_module") + "\"," +
					"                            \"max_thread_num\": 16," +
					"                            \"min_track_id\": 1000000000," +
					"                            \"max_track_id\": 2000000000" +
					"                        }" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"target_slice0\"," + contextIdString() +
					"                    \"type\": \"Slice\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"temp_struct_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_struct_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"by_labels\": [" +
					"                            221488" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"struct_id_redefine\"," + contextIdString() +
					"                    \"type\": \"IdAllocation\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_struct_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_struct_targets\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"track_targets_concat\"," +  contextIdString() +
					"                    \"type\": \"Concat\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_face_targets\"," +
					"                        \"tracked_struct_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"concat_items\": [" +
					"                            \"targets\"" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"dropped_ids_concat\"," +  contextIdString() +
					"                    \"type\": \"Concat\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"dropped_face_ids\"," +
					"                        \"dropped_struct_ids\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"dropped_ids\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"concat_items\": [" +
					"                            \"targets\"" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_body_matching\"," +  contextIdString() +
					"                    \"type\": \"FaceBodyMatching\"," +
					"                    \"parallel_group\": \"matching\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"matching_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"cupid\"" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"match_result_converse\"," +  contextIdString() +
					"                    \"type\": \"MatchingResultConversion\"," +
					"                    \"parallel_group\": \"matching\"," +
					"                    \"inputs\": [" +
					"                        \"matching_targets\"," +
					"                        \"tracked_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"associated_facebodys\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"output\"," +  contextIdString() +
					"                    \"type\": \"Output\"," +
					"                    \"parallel_group\": \"output\"," +
					"                    \"inputs\": [" +
					"                        \"associated_facebodys\"," +
					"                        \"dropped_ids\"" +
					"                    ]," +
					"                    \"outputs\": []" +
					"                }" +
					"            ]" +
					"        }," +
					"        {" +
					"            \"name\": \"" + stageTwo + "\"," + contextIdString() +
					"            \"module_plugins\": [" +
					"                \"slice.fmd\"," +
					"                \"concat.fmd\"," +
					"                \"plugin.fmd\"," +
					"                \"operation.fmd\"," +
					"                \"mergence.fmd\"," +
					"                \"target_selection.fmd\"," +
					"                \"refinement.fmd\"," +
					"                \"roi_filter.fmd\"," +
					"                \"face_quality.fmd\"," +
					"                \"face_quality_filter.fmd\"" +
					"            ]," +
					"            \"modules\": [" +
					"                {" +
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," +
					"                    \"parallel_group\": \"input\"," +
					"                    \"inputs\": []," +
					"                    \"outputs\": [" +
					"                        \"associated_facebodys\"," +
					"                        \"dropped_ids\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"target_slice\"," + contextIdString() +
					"                    \"type\": \"Slice\"," +
					"                    \"parallel_group\": \"quality\"," +
					"                    \"inputs\": [" +
					"                        \"associated_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_targets\"," +
					"                        \"body_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"by_labels\": [" +
					"                            37017," +
					"                            221488" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_quality_calculate\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"quality\"," +
					"                    \"inputs\": [" +
					"                        \"face_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_quality\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"pageant\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("pageant_module") + "\"," +
					"                        \"max_batch_size\": 16" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_quality_calculate\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"quality\"," +
					"                    \"inputs\": [" +
					"                        \"body_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"raw_body_quality\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"classifier\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_filter_module") + "\"," +
					"                        \"max_batch_size\": 32" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_quality_pack\"," + contextIdString() +
					"                    \"type\": \"Operation\"," +
					"                    \"parallel_group\": \"quality\"," +
					"                    \"inputs\": [" +
					"                        \"raw_body_quality\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_quality\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"operations\": [" +
					"                            {" +
					"                                \"cmd\": \"move\"," +
					"                                \"args\": [" +
					"                                    \"attribute/ped_quality/total\"," +
					"                                    \"quality\"" +
					"                                ]" +
					"                            }," +
					"                            {" +
					"                                \"cmd\": \"remove\"," +
					"                                \"args\": [" +
					"                                    \"attribute\"" +
					"                                ]" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"targets_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"quality\"," +
					"                    \"inputs\": [" +
					"                        \"face_quality\"," +
					"                        \"body_quality\"," +
					"                        \"associated_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"merged_targets\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"target_selector\"," + contextIdString() +
					"                    \"type\": \"TargetSelection\"," +
					"                    \"parallel_group\": \"select\"," +
					"                    \"inputs\": [" +
					"                        \"merged_targets\"," +
					"                        \"dropped_ids\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"target_tracklets\"" +
					"                    ]," +
					"                    \"config\": " + "{" +
					"                       \"max_device_memory_usage_per_source\": 128," +
					"                    	\"max_roi_ref_frame_size\": 512," +
					"                    	\"max_source_tracklet_num\": " + max_tracklet_num + "," +
					"                    	\"keep_low_quality_target\": true," +
					"                    	\"selection\": [" +
					"                    		{" +
					"                    			\"label_id\": "               + 37017 + "," +
					"                    			\"quick_response_time\": "    + quick_response_time + "," +
					"                    			\"time_interval\": "          + time_interval + "," +
					"                    			\"max_track_time\": "         + max_track_time + "," +
					"                    			\"roi_expand_ratio\": "       + expand_ratio + "," +
					"                    			\"quality_threshold\": "      + face_quality_thresh + "," +
					"                    			\"max_tracklet_num\": "       + 512 + "," +
					"                    			\"max_tracklet_item_size\": " + max_tracklet_item_size +
					"                    		}," +
					"                    		{" +
					"                    			\"label_id\": "               + 221488 + "," +
					"                    			\"quick_response_time\": "    + quick_response_time + "," +
					"                    			\"time_interval\": "          + time_interval + "," +
					"                    			\"max_track_time\": "         + max_track_time + "," +
					"                    			\"roi_expand_ratio\": "       + expand_ratio + "," +
					"                    			\"quality_threshold\": "      + face_quality_thresh + "," +
					"                    			\"max_tracklet_num\": "       + 512 + "," +
					"                    			\"max_tracklet_item_size\": " + max_tracklet_item_size +
					"                    		}" +
					"                    	]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"target_slice1\"," + contextIdString() +
					"                    \"type\": \"Slice\"," +
					"                    \"parallel_group\": \"select\"," +
					"                    \"inputs\": [" +
					"                        \"target_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_tracklets\"," +
					"                        \"body_tracklets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"by_labels\": [" +
					"                            37017," +
					"                            221488" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"aligner\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_landmarks\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"aligner\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," +
					"                        \"max_batch_size\": 64" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_landmarks_pack\"," + contextIdString() +
					"                    \"type\": \"Operation\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_landmarks\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_landmarks\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"operations\": [" +
					"                            {" +
					"                                \"cmd\": \"move\"," +
					"                                \"args\": [" +
					"                                    \"confidence\"," +
					"                                    \"aligner_confidence\"" +
					"                                ]" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_headpose\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_landmarks\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_headpose\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"headpose\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," +
					"                        \"max_batch_size\": 32" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_landmarks\"," +
					"                        \"face_headpose\"," +
					"                        \"face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_targets_quality_element\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_quality_update\"," + contextIdString() +
					"                    \"type\": \"FaceQuality\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_targets_quality_element\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"new_face_tracklets\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_quality_filter\"," + contextIdString() +
					"                    \"type\": \"FaceQualityFilter\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"new_face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_tracklets_with_landmarks\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"quality_threshold\":" + face_quality_thresh +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_feature_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets_with_landmarks\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_features\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"feature\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module") + "\"," +
					"                        \"max_batch_size\": 32" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_feature_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"body_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_features\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"senu\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("senu_feature_module") + "\"," +
					"                        \"max_batch_size\": 32" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_feature_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_features\"," +
					"                        \"face_tracklets_with_landmarks\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_tracklets_with_feature\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"faces_refinement\"," + contextIdString() +
					"                    \"type\": \"Refinement\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets_with_feature\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"best_faces\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_feature_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"body_features\"," +
					"                        \"body_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_tracklets_with_feature\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"update\": true" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_refinement\"," + contextIdString() +
					"                    \"type\": \"Refinement\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"body_tracklets_with_feature\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"best_bodys\"" +
					"                    ]," +
					"                    \"config\": {}" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_attribute_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"best_faces\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_attributes\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"attribute\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," +
					"                        \"max_batch_size\": 64" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_attribute_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"best_bodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_attributes\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"classifier\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_attribute_module") + "\"," +
					"                        \"max_batch_size\": 16" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_targets_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"face_attributes\"," +
					"                        \"best_faces\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"analyzed_face_targets\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_targets_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"body_attributes\"," +
					"                        \"best_bodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"analyzed_body_targets\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_body_concat\"," + contextIdString() +
					"                    \"type\": \"Concat\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"analyzed_face_targets\"," +
					"                        \"analyzed_body_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"analyzed_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"concat_items\": [" +
					"                            \"targets\"" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," +
					"                    \"parallel_group\": \"output\"," +
					"                    \"inputs\": [" +
					"                        \"analyzed_targets\"" +
					"                    ]," +
					"                    \"outputs\": []" +
					"                }" +
					"            ]" +
					"        }" +
					"    ]" +
					"}" +
					"";
		}

		private String faceBodyConfig() {
			return "{" + contextIdString() + 
					"    \"streams\": [" + 
					"        {" + 
					"            \"name\": \"" + stageOne + "\"," + contextIdString() +
					"            \"module_plugins\": [" + 
					"                \"detection.fmd\"," + 
					"                \"multiple_target_tracking.fmd\"," + 
					"                \"slice.fmd\"," + 
					"                \"concat.fmd\"," + 
					"                \"face_body_matching.fmd\"," + 
					"                \"plugin.fmd\"," + 
					"                \"target_filter.fmd\"," + 
					"                \"mergence.fmd\"," + 
					"                \"matching_result_conversion.fmd\"" + 
					"            ]," + 
					"            \"modules\": [" + 
					"                {" + 
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," + 
					"                    \"parallel_group\": \"input\"," + 
					"                    \"inputs\": []," + 
					"                    \"outputs\": [" + 
					"                        \"images\"" + 
					"                    ]" + 
					"                }," +
					"                {" +
					"                    \"name\": \"face_detector\"," + contextIdString() +
					"                    \"type\": \"Detection\"," +
					"                    \"parallel_group\": \"face_detect\"," +
					"                    \"inputs\": [\"images\"]," +
					"                    \"outputs\": [\"detected_faces_small\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"hunter\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module_scg") + "\"," +
					"                        \"max_batch_size\": 64," +
					"                        \"confidence_threshold\": " + face_track_quality_thresh   +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_target_tracker\"," + contextIdString() +
					"                    \"type\": \"MultipleTargetTracking\"," +
					"                    \"parallel_group\": \"face_detect\"," +
					"                    \"inputs\": [" +
					"                        \"detected_faces_small\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_face_targets\"," +
					"                        \"dropped_face_ids\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"facebody_detector\"," + contextIdString() +
					"                    \"type\": \"Detection\"," +
					"                    \"parallel_group\": \"detect\"," +
					"                    \"inputs\": [" +
					"                        \"images\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"detected_facebodys\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"hunter\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("facebody_module") + "\"," +
					"                        \"max_batch_size\": 32," +
					"                        \"confidence_threshold\": " + ped_track_quality_thresh +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_body_tracker\"," + contextIdString() +
					"                    \"type\": \"MultipleTargetTracking\"," +
					"                    \"parallel_group\": \"target_tracking\"," +
					"                    \"inputs\": [" +
					"                        \"detected_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_bodys\"," +
					"                        \"dropped_body_ids\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"facebody_slice\"," + contextIdString() +
					"                    \"type\": \"Slice\"," +
					"                    \"parallel_group\": \"detect\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_bodys\"" +
					"                    ]," +
					"                    \"outputs\": [\"body_targets\"]," +
					"                    \"config\": {" +
					"                        \"by_labels\": [" +
				                                 	221488  +
					"                        ]" +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"track_targets_concat\"," + contextIdString() +
					"                    \"type\": \"Concat\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_face_targets\"," +
					"                        \"body_targets\"" +
					"                    ]," +
					"                    \"outputs\": [\"tracked_facebodys\"]," +
					"                    \"config\": {" +
					"                        \"concat_items\": [" +
					"                          \"targets\"" +
					"                        ]" +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"dropped_ids_concat\"," + contextIdString() +
					"                    \"type\": \"Concat\"," +
					"                    \"parallel_group\": \"struct_detect\"," +
					"                    \"inputs\": [" +
					"                        \"dropped_face_ids\"," +
					"                        \"dropped_body_ids\"" +
					"                    ]," +
					"                    \"outputs\": [\"dropped_ids\"]," +
					"                    \"config\": {" +
					"                        \"concat_items\": [" +
					"                          \"targets\"" +
					"                        ]" +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_body_matching\"," + contextIdString() +
					"                    \"type\": \"FaceBodyMatching\"," +
					"                    \"parallel_group\": \"target_tracking\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [\"matching_targets\"]," +
					"                    \"config\": {" +
					"                        \"plugin\":  \"cupid\" " +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"match_result_converse\"," + contextIdString() +
					"                    \"type\": \"MatchingResultConversion\"," +
					"                    \"parallel_group\": \"target_tracking\"," +
					"                    \"inputs\": [" +
					"                        \"matching_targets\"," +
					"                        \"tracked_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"associated_facebodys\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," +
					"                    \"parallel_group\": \"output\"," +
					"                    \"inputs\": [" +
					"                        \"associated_facebodys\"," +
					"                        \"dropped_ids\"" +
					"                    ]," +
					"                    \"outputs\": []" +
					"                }" +
					"            ]" +

					"        }," + 
					"        {" + 
					"            \"name\": \"" + stageTwo + "\"," + contextIdString() +
					"            \"module_plugins\": [" + 
					"                \"slice.fmd\"," + 
					"                \"concat.fmd\"," + 
					"                \"plugin.fmd\"," + 
					"                \"operation.fmd\"," + 
					"                \"mergence.fmd\"," + 
					"                \"target_selection_287.fmd\"," +
					"                \"refinement.fmd\"," + 
					"                \"roi_filter.fmd\"," + 
					"                \"face_quality.fmd\"," +
					"                \"multidim_face_quality.fmd\"," +
					"                \"face_quality_filter.fmd\"" + 
					"            ]," + 
					"            \"modules\": [" + 
					"                {" + 
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," + 
					"                    \"parallel_group\": \"input\"," + 
					"                    \"inputs\": []," + 
					"                    \"outputs\": [" + 
					"                        \"associated_facebodys\"," + 
					"                        \"dropped_ids\"" + 
					"                    ]" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"target_slice\"," + contextIdString() +
					"                    \"type\": \"Slice\"," + 
					"                    \"parallel_group\": \"quality\"," + 
					"                    \"inputs\": [" + 
					"                        \"associated_facebodys\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_targets\"," + 
					"                        \"body_targets\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"by_labels\": [" + 
					"                            37017," + 
					"                            221488" + 
					"                        ]" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_quality_calculate\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"quality\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_targets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_quality\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"pageant\"," + 
					"                        \"model\": \""+ "/usr/cognitivesvc/" + Initializer.modelPathMap.get("pageant_module") +"\"," + 
					"                        \"max_batch_size\": 16" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"body_quality_calculate\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"quality\"," + 
					"                    \"inputs\": [" + 
					"                        \"body_targets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"raw_body_quality\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"classifier\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_filter_module") + "\"," + 
					"                        \"max_batch_size\": 32" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"body_quality_pack\"," + contextIdString() +
					"                    \"type\": \"Operation\"," + 
					"                    \"parallel_group\": \"quality\"," + 
					"                    \"inputs\": [" + 
					"                        \"raw_body_quality\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"body_quality\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"operations\": [" + 
					"                            {" + 
					"                                \"cmd\": \"move\"," + 
					"                                \"args\": [" + 
					"                                    \"attribute/ped_quality/total\"," + 
					"                                    \"quality\"" + 
					"                                ]" + 
					"                            }," + 
					"                            {" + 
					"                                \"cmd\": \"remove\"," + 
					"                                \"args\": [" + 
					"                                    \"attribute\"" + 
					"                                ]" + 
					"                            }" + 
					"                        ]" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"targets_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," + 
					"                    \"parallel_group\": \"quality\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_quality\"," + 
					"                        \"body_quality\"," + 
					"                        \"associated_facebodys\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"merged_targets\"" + 
					"                    ]" + 
					"                }," +

					"                {" +
					"                    \"name\": \"roi_filter\"," + contextIdString() +
					"                    \"type\": \"RoiFilter\"," +
					"                    \"parallel_group\": \"select\"," +
					"                    \"inputs\": [\"merged_targets\"]," +
					"                    \"outputs\": [\"filtered_merged_targets\"]," +
					"                    \"config\": {" +
					"                        \"roi_filter\": "+ roi_filter +
					"                    }" +
					"                }," +

					"                {" + 
					"                    \"name\": \"target_selector\"," + contextIdString() +
					"                    \"type\": \"TargetSelection\"," + 
					"                    \"parallel_group\": \"select\"," + 
					"                    \"inputs\": [" + 
					"                        \"filtered_merged_targets\"," +
					"                        \"dropped_ids\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"target_tracklets\"" + 
					"                    ]," + 
					"                    \"config\": " + "{" +
					"                        \"max_device_memory_usage_per_source\": 70," +
					"                        \"memory_pool_size\": 512," +
					"                        \"scene_frame_encoder\": {" +
					"                            \"encoder_plugin\": \"imagesharp\"," +
					"                            \"force_download\": false," +
					"                            \"max_ref_frame_size\": 25," +
					"                            \"scene_frame_encoder_quality\": 30" +
					"                        }," +
					"                        \"selection\":[" +
					"                            {" +
					"                                \"label_id\":37017," +
					"                                \"roi_expand_ratio\":1.5," +
					"                                \"quality_threshold\":" + face_quality_thresh + "," +
					"                                \"max_tracklet_item_size\":" + max_tracklet_item_size + "," +
					"                                \"quality_step\":" + 0.01  +
					"                            }," +
					"                            {" +
					"                                \"label_id\":221488," +
					"                                \"quality_threshold\":" + ped_quality_thresh  +
					"                            }" +
					"                        ]" +
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"target_slice1\"," + contextIdString() +
					"                    \"type\": \"Slice\"," + 
					"                    \"parallel_group\": \"select\"," + 
					"                    \"inputs\": [" + 
					"                        \"target_tracklets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_tracklets\"," + 
					"                        \"body_tracklets\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"by_labels\": [" + 
					"                            37017," + 
					"                            221488" + 
					"                        ]" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"aligner\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_tracklets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_landmarks\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"aligner\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," + 
					"                        \"max_batch_size\": 64" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_landmarks_pack\"," + contextIdString() +
					"                    \"type\": \"Operation\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_landmarks\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_landmarks\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"operations\": [" + 
					"                            {" + 
					"                                \"cmd\": \"move\"," + 
					"                                \"args\": [" + 
					"                                    \"confidence\"," + 
					"                                    \"aligner_confidence\"" + 
					"                                ]" + 
					"                            }" + 
					"                        ]" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_headpose\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_landmarks\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_headpose\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"headpose\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," + 
					"                        \"max_batch_size\": 32" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_landmarks\"," + 
					"                        \"face_headpose\"," + 
					"                        \"face_tracklets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_targets_quality_element\"" + 
					"                    ]" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_quality_update\"," + contextIdString() +
					"                    \"type\": \"FaceQuality\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_targets_quality_element\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"new_face_tracklets\"" + 
					"                    ]" + 
					"                }," +

					"                {" +
					"                    \"name\": \"face_multidim_quality\"," + contextIdString() +
					"                    \"type\": \"MultidimFaceQuality\"," +
					"                    \"parallel_group\": \"target_analysis\"," +
					"                    \"inputs\": [" +
					"                        \"new_face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [\"new_face_tracklets\"]," +
					"                    \"config\": {" +
					"                        \"quality_model\": {" +
					"                            \"plugin\": \"classifier\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_quality_module") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"multi_face_quality\"" +
					"                         }," +

					"                        \"clear_model\": {" +
					"                            \"plugin\": \"classifier\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_blur_module_scg") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"multi_face_quality_blur\"" +
					"                         }," +
					"                         \"clear_score_threshold\": "       + 0 + "," +
					"                         \"angle_score_threshold\": "       + 0 + "," +
					"                         \"yaw_angle_threshold\": "         + 180 + "," +
					"                         \"pitch_angle_threshold\": "       + 180 + "," +
					"                         \"visible_score_threshold\": "     + 0 + "," +
					"                         \"shine_score_threshold\": "       + 0 +

					"                     }" +
					"                }," +

					"                {" + 
					"                    \"name\": \"face_quality_filter\"," + contextIdString() +
					"                    \"type\": \"FaceQualityFilter\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"new_face_tracklets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_tracklets_with_landmarks\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"quality_threshold\": 0.3" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_feature_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_tracklets_with_landmarks\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_features\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"feature\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module") + "\"," + 
					"                        \"max_batch_size\": 32" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"body_feature_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"body_tracklets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"body_features\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"senu\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("senu_feature_module") + "\"," + 
					"                        \"max_batch_size\": 32" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_feature_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_features\"," + 
					"                        \"face_tracklets_with_landmarks\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_tracklets_with_feature\"" + 
					"                    ]" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"faces_refinement\"," + contextIdString() +
					"                    \"type\": \"Refinement\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_tracklets_with_feature\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"best_faces\"" + 
					"                    ]" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"body_feature_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"body_features\"," + 
					"                        \"body_tracklets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"body_tracklets_with_feature\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"update\": true" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"body_refinement\"," + contextIdString() +
					"                    \"type\": \"Refinement\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"body_tracklets_with_feature\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"best_bodys\"" + 
					"                    ]," + 
					"                    \"config\": {}" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_attribute_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"best_faces\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"face_attributes\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"attribute\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," + 
					"                        \"max_batch_size\": 64" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"body_attribute_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"best_bodys\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"body_attributes\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"plugin\": \"classifier\"," + 
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_attribute_extraction") + "\"," +
					"                        \"max_batch_size\": 16" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_targets_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"face_attributes\"," + 
					"                        \"best_faces\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"analyzed_face_targets\"" + 
					"                    ]" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"body_targets_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"body_attributes\"," + 
					"                        \"best_bodys\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"analyzed_body_targets\"" + 
					"                    ]" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"face_body_concat\"," + contextIdString() +
					"                    \"type\": \"Concat\"," + 
					"                    \"parallel_group\": \"target_analysis\"," + 
					"                    \"inputs\": [" + 
					"                        \"analyzed_face_targets\"," + 
					"                        \"analyzed_body_targets\"" + 
					"                    ]," + 
					"                    \"outputs\": [" + 
					"                        \"analyzed_targets\"" + 
					"                    ]," + 
					"                    \"config\": {" + 
					"                        \"concat_items\": [" + 
					"                            \"targets\"" + 
					"                        ]" + 
					"                    }" + 
					"                }," + 
					"                {" + 
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," + 
					"                    \"parallel_group\": \"output\"," + 
					"                    \"inputs\": [" + 
					"                        \"analyzed_targets\"" + 
					"                    ]," + 
					"                    \"outputs\": []" + 
					"                }" + 
					"            ]" + 
					"        }" + 
					"    ]" + 
					"}" + 
					"";
		}

		private String faceBodyConfigVps() {
			return "{" + contextIdString() +
					"    \"streams\": [" +
					"        {" +
					"            \"name\": \"" + stageOne + "\"," + contextIdString() +
					"            \"module_plugins\": [" +
					"                \"detection.fmd\"," +
					"                \"multiple_target_tracking.fmd\"," +
					"                \"slice.fmd\"," +
					"                \"concat.fmd\"," +
					"                \"face_body_matching.fmd\"," +
					"                \"plugin.fmd\"," +
					"                \"target_filter.fmd\"," +
					"                \"mergence.fmd\"," +
					"                \"matching_result_conversion.fmd\"," +
					"                \"operation.fmd\"" +
					"            ]," +
					"            \"modules\": [" +
					"                {" +
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," +
					"                    \"parallel_group\": \"input\"," +
					"                    \"inputs\": []," +
					"                    \"outputs\": [" +
					"                        \"images\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"facebody_detector\"," + contextIdString() +
					"                    \"type\": \"Detection\"," +
					"                    \"parallel_group\": \"facebody_detector\"," +
					"                    \"inputs\": [\"images\"]," +
					"                    \"outputs\": [\"detected_facebodys\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"hunter\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("facebody_module") + "\"," +
					"                        \"max_batch_size\": 128," +
					"                        \"confidence_threshold\": " + small_quality_thresh  + "," +
					"                        \"model_key\": \"face_body_detection\"" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"body_filter\"," + contextIdString() +
					"                    \"type\": \"BodyFilter\"," +
					"                    \"parallel_group\": \"body_filter\"," +
					"                    \"inputs\": [" +
					"                        \"detected_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"detected_facebodys\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_body_tracker\"," + contextIdString() +
					"                    \"type\": \"MultipleTargetTracking\"," +
					"                    \"parallel_group\": \"face_body_tracker\"," +
					"                    \"inputs\": [" +
					"                        \"detected_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"tracked_facebodys\"," +
					"                        \"dropped_ids\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_body_matching\"," + contextIdString() +
					"                    \"type\": \"FaceBodyMatching\"," +
					"                    \"parallel_group\": \"face_body_matching\"," +
					"                    \"inputs\": [" +
					"                        \"tracked_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [\"matching_targets\"]," +
					"                    \"config\": {" +
					"                        \"plugin\":  \"cupid\" " +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"match_result_converse\"," + contextIdString() +
					"                    \"type\": \"MatchingResultConversion\"," +
					"                    \"parallel_group\": \"match_result_converse\"," +
					"                    \"inputs\": [" +
					"                        \"matching_targets\"," +
					"                        \"tracked_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"associated_facebodys\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"remove_feature\"," + contextIdString() +
					"                    \"type\": \"Operation\"," +
					"                    \"parallel_group\": \"remove_feature\"," +
					"                    \"inputs\": [" +
					"                        \"associated_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"associated_facebodys\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"operations\": [" +
					"                            {" +
					"                                \"cmd\": \"remove\"," +
					"                                \"args\": [" +
					"                                    \"feature\"" +
					"                                ]" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," +
					"                    \"parallel_group\": \"output\"," +
					"                    \"inputs\": [" +
					"                        \"associated_facebodys\"," +
					"                        \"dropped_ids\"," +
					"                        \"images\"" +
					"                    ]," +
					"                    \"outputs\": []" +
					"                }" +
					"            ]" +

					"        }," +
					"        {" +
					"            \"name\": \"" + stageTwo + "\"," + contextIdString() +
					"            \"module_plugins\": [" +
					"                \"slice.fmd\"," +
					"                \"concat.fmd\"," +
					"                \"plugin.fmd\"," +
					"                \"operation.fmd\"," +
					"                \"mergence.fmd\"," +
					"                \"target_selection_287.fmd\"," +
					"                \"refinement.fmd\"," +
					"                \"roi_filter.fmd\"," +
					"                \"face_quality.fmd\"," +
					"                \"face_quality_filter.fmd\"," +
					"                \"lightweight_targets_slice.fmd\"," +
					"                \"face_body_matching_filter.fmd\"," +
					"                \"multidim_face_quality.fmd\"," +
					"                \"binary_classification_filter.fmd\"" +
					"            ]," +
					"            \"modules\": [" +
					"                {" +
					"                    \"name\": \"input\"," + contextIdString() +
					"                    \"type\": \"Input\"," +
					"                    \"parallel_group\": \"input\"," +
					"                    \"inputs\": []," +
					"                    \"outputs\": [" +
					"                        \"associated_facebodys\"," +
					"                        \"dropped_ids\"," +
					"                        \"images\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"target_slice\"," + contextIdString() +
					"                    \"type\": \"Slice\"," +
					"                    \"parallel_group\": \"target_slice\"," +
					"                    \"inputs\": [" +
					"                        \"associated_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_targets\"," +
					"                        \"body_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"by_labels\": [" +
					"                            37017," +
					"                            221488" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_quality_calculate\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_quality_calculate\"," +
					"                    \"inputs\": [" +
					"                        \"face_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_quality\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"pageant\"," +
					"                        \"model\": \""+ "/usr/cognitivesvc/" + Initializer.modelPathMap.get("pageant_module") +"\"," +
					"                        \"max_batch_size\": 16" + "," +
					"                        \"model_key\": \"face_quality\"" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_quality_calculate\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"body_quality_calculate\"," +
					"                    \"inputs\": [" +
					"                        \"body_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"raw_body_quality\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"classifier\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_filter_module") + "\"," +
					"                        \"max_batch_size\": 32" +"," +
					"                        \"model_key\": \"ped_quality\"" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"body_quality_pack\"," + contextIdString() +
					"                    \"type\": \"Operation\"," +
					"                    \"parallel_group\": \"body_quality_pack\"," +
					"                    \"inputs\": [" +
					"                        \"raw_body_quality\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_quality\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"operations\": [" +
					"                            {" +
					"                                \"cmd\": \"move\"," +
					"                                \"args\": [" +
					"                                    \"attribute/ped_quality/total\"," +
					"                                    \"quality\"" +
					"                                ]" +
					"                            }," +
					"                            {" +
					"                                \"cmd\": \"remove\"," +
					"                                \"args\": [" +
					"                                    \"attribute\"" +
					"                                ]" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"targets_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"targets_merge\"," +
					"                    \"inputs\": [" +
					"                        \"face_quality\"," +
					"                        \"body_quality\"," +
					"                        \"associated_facebodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"merged_targets\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"roi_filter\"," + contextIdString() +
					"                    \"type\": \"RoiFilter\"," +
					"                    \"parallel_group\": \"roi_filter\"," +
					"                    \"inputs\": [\"merged_targets\"]," +
					"                    \"outputs\": [\"filtered_merged_targets\"]," +
					"                    \"config\": {" +
					"                        \"roi_filter\": "+ roi_filter +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"target_selector\"," + contextIdString() +
					"                    \"type\": \"TargetSelection\"," +
					"                    \"parallel_group\": \"target_selector\"," +
					"                    \"inputs\": [" +
					"                        \"filtered_merged_targets\"," +
					"                        \"dropped_ids\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"target_tracklets\"" +
					"                    ]," +
					"                    \"config\": " + "{" +
					"                        \"keep_low_quality_target\": true," +
					"                        \"max_device_memory_usage_per_source\": 70," +
					"                        \"memory_pool_size\": 512," +
					"                        \"scene_frame_encoder\": {" +
					"                            \"encoder_plugin\": \"imagesharp\"," +
					"                            \"force_download\": false," +
					"                            \"max_ref_frame_size\": 25," +
					"                            \"scene_frame_encoder_quality\": 30" +
					"                        }," +
					"                        \"selection\":[" +
					"                            {" +
					"                                \"label_id\":37017," +
					"                                \"roi_expand_ratio\":1.5," +
					"                                \"quality_threshold\":" + face_quality_thresh + "," +
					"                                \"max_tracklet_item_size\":" + max_tracklet_item_size + "," +
					"                                \"max_track_time\": "         + max_track_time + "," +
					"                                \"quality_step\":" + 0.01  +
					"                            }," +
					"                            {" +
					"                                \"label_id\":221488," +
					"                                \"max_track_time\": "         + max_track_time + "," +
					"                                \"quality_threshold\":" + ped_quality_thresh  +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +


					"                {" +
					"                    \"name\": \"selected_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"selected_lightweight_slice\"," +
					"                    \"inputs\": [\"target_tracklets\"]," +
					"                    \"outputs\": [" +
					"                        \"selected_target_tracklets_normal\"," +
					"                        \"selected_target_tracklets_lightweight\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"target_label_configs\": [" +
					"                            {" +
					"                               \"label_id\": 37017," +
					"                               \"min_width\": 32," +
					"                               \"min_height\": 32" +
					"                            }," +
					"                            {" +
					"                               \"label_id\": 37017," +
					"                               \"min_width\": 24," +
					"                               \"min_height\": 72" +
					"                            }" +

					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"target_slice1\"," + contextIdString() +
					"                    \"type\": \"Slice\"," +
					"                    \"parallel_group\": \"target_slice1\"," +
					"                    \"inputs\": [" +
					"                        \"selected_target_tracklets_normal\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_tracklets\"," +
					"                        \"body_tracklets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"by_labels\": [" +
					"                            37017," +
					"                            221488" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_classifier\"," + contextIdString() +
					"                    \"type\": \"BinaryClassificationFilter\"," +
					"                    \"parallel_group\": \"face_classifier\"," +
					"                    \"inputs\": [\"face_tracklets\"]," +
					"                    \"outputs\": [\"face_tracklets\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"classifier\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("face_classifier_module_scg") + "\"," +
					"                        \"max_batch_size\": 128," +
					"                        \"support_label\": 37017," +
					"                        \"enable\": true," +
					"                        \"confidence_threshold\": 0.7," +
					"                        \"model_key\": \"face_binary_classifier\"" +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_classifier_filtered_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"face_classifier_filtered_lightweight_slice\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_tracklets\"," +
					"                        \"face_classifier_filtered_targets_lightweight\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"body_classifier\"," + contextIdString() +
					"                    \"type\": \"BinaryClassificationFilter\"," +
					"                    \"parallel_group\": \"body_classifier\"," +
					"                    \"inputs\": [\"body_tracklets\"]," +
					"                    \"outputs\": [\"body_tracklets\"]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"classifier\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_filter_module") + "\"," +
					"                        \"max_batch_size\": 128," +
					"                        \"support_label\": 221488," +
					"                        \"enable\": false," +
					"                        \"confidence_threshold\": 0.9," +
					"                        \"model_key\": \"ped_binary_classifier\"" +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"body_classifier_filtered_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"body_classifier_filtered_lightweight_slice\"," +
					"                    \"inputs\": [" +
					"                        \"body_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_tracklets\"," +
					"                        \"body_classifier_filtered_targets_lightweight\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"aligner\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"aligner\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_landmarks\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"aligner\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module_scg") + "\"," +
					"                        \"max_batch_size\": 64" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_landmarks_pack\"," + contextIdString() +
					"                    \"type\": \"Operation\"," +
					"                    \"parallel_group\": \"face_landmarks_pack\"," +
					"                    \"inputs\": [" +
					"                        \"face_landmarks\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_landmarks\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"operations\": [" +
					"                            {" +
					"                                \"cmd\": \"move\"," +
					"                                \"args\": [" +
					"                                    \"confidence\"," +
					"                                    \"aligner_confidence\"" +
					"                                ]" +
					"                            }" +
					"                        ]" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_headpose\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_headpose\"," +
					"                    \"inputs\": [" +
					"                        \"face_landmarks\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_headpose\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"headpose\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," +
					"                        \"max_batch_size\": 32" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"blur_landmark_headpose_merge\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"blur_landmark_headpose_merge\"," +
					"                    \"inputs\": [" +
					"                        \"face_landmarks\"," +
					"                        \"face_headpose\"," +
					"                        \"face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_targets_quality_element\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_quality_update\"," + contextIdString() +
					"                    \"type\": \"FaceQuality\"," +
					"                    \"parallel_group\": \"face_quality_update\"," +
					"                    \"inputs\": [" +
					"                        \"face_targets_quality_element\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"new_face_tracklets\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_multidim_quality\"," + contextIdString() +
					"                    \"type\": \"MultidimFaceQuality\"," +
					"                    \"parallel_group\": \"face_multidim_quality\"," +
					"                    \"inputs\": [" +
					"                        \"new_face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [\"new_face_tracklets\"]," +
					"                    \"config\": {" +
					"                        \"quality_model\": {" +
					"                            \"plugin\": \"classifier\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_quality_module") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"multi_face_quality\"" +
					"                         }," +

					"                        \"clear_model\": {" +
					"                            \"plugin\": \"classifier\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("classifier_blur_module_scg") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"multi_face_quality_blur\"" +
					"                         }," +
					"                         \"clear_score_threshold\": "       + 0 + "," +
					"                         \"angle_score_threshold\": "       + 0 + "," +
					"                         \"yaw_angle_threshold\": "         + 180 + "," +
					"                         \"pitch_angle_threshold\": "       + 180 + "," +
					"                         \"visible_score_threshold\": "     + 0 + "," +
					"                         \"shine_score_threshold\": "       + 0 +
					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_multidim_quality_filtered_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"face_multidim_quality_filtered_lightweight_slice\"," +
					"                    \"inputs\": [\"new_face_tracklets\"]," +
					"                    \"outputs\": [" +
					"                        \"new_face_tracklets\"," +
					"                        \"face_multidim_quality_filtered_targets_lightweight\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_quality_filter\"," + contextIdString() +
					"                    \"type\": \"FaceQualityFilter\"," +
					"                    \"parallel_group\": \"face_quality_filter\"," +
					"                    \"inputs\": [" +
					"                        \"new_face_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_tracklets_with_landmarks\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"quality_threshold\": 0.3" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_quality_filtered_lightweight_slice\"," + contextIdString() +
					"                    \"type\": \"LightweightTargetsSlice\"," +
					"                    \"parallel_group\": \"face_quality_filtered_lightweight_slice\"," +
					"                    \"inputs\": [\"face_tracklets_with_landmarks\"]," +
					"                    \"outputs\": [" +
					"                        \"face_quality_filtered_target_tracklets_normal\"," +
					"                        \"face_quality_filtered_target_tracklets_lightweight\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_feature_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_feature_extraction\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets_with_landmarks\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_features\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"feature\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module") + "\"," +
					"                        \"max_batch_size\": 32" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_feature_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"body_feature_extraction\"," +
					"                    \"inputs\": [" +
					"                        \"body_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_features\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"senu\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("senu_feature_module") + "\"," +
					"                        \"max_batch_size\": 32" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_feature_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"face_feature_mergence\"," +
					"                    \"inputs\": [" +
					"                        \"face_features\"," +
					"                        \"face_tracklets_with_landmarks\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_tracklets_with_feature\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"faces_refinement\"," + contextIdString() +
					"                    \"type\": \"Refinement\"," +
					"                    \"parallel_group\": \"faces_refinement\"," +
					"                    \"inputs\": [" +
					"                        \"face_tracklets_with_feature\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"best_faces\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_feature_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"body_feature_mergence\"," +
					"                    \"inputs\": [" +
					"                        \"body_features\"," +
					"                        \"body_tracklets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_tracklets_with_feature\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"update\": true" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_refinement\"," + contextIdString() +
					"                    \"type\": \"Refinement\"," +
					"                    \"parallel_group\": \"body_refinement\"," +
					"                    \"inputs\": [" +
					"                        \"body_tracklets_with_feature\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"best_bodys\"" +
					"                    ]," +
					"                    \"config\": {}" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_attribute_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"face_attribute_extraction\"," +
					"                    \"inputs\": [" +
					"                        \"best_faces\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"face_attributes\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"attribute\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," +
					"                        \"max_batch_size\": 64" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_attribute_extraction\"," + contextIdString() +
					"                    \"type\": \"Plugin\"," +
					"                    \"parallel_group\": \"body_attribute_extraction\"," +
					"                    \"inputs\": [" +
					"                        \"best_bodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"body_attributes\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"plugin\": \"classifier\"," +
					"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_attribute_extraction") + "\"," +
					"                        \"max_batch_size\": 16" +
					"                    }" +
					"                }," +
					"                {" +
					"                    \"name\": \"face_targets_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"face_targets_mergence\"," +
					"                    \"inputs\": [" +
					"                        \"face_attributes\"," +
					"                        \"best_faces\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"analyzed_face_targets\"" +
					"                    ]" +
					"                }," +
					"                {" +
					"                    \"name\": \"body_targets_mergence\"," + contextIdString() +
					"                    \"type\": \"Mergence\"," +
					"                    \"parallel_group\": \"body_targets_mergence\"," +
					"                    \"inputs\": [" +
					"                        \"body_attributes\"," +
					"                        \"best_bodys\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"analyzed_body_targets\"" +
					"                    ]" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_body_concat\"," + contextIdString() +
					"                    \"type\": \"Concat\"," +
					"                    \"parallel_group\": \"face_body_concat\"," +
					"                    \"inputs\": [" +
					"                        \"analyzed_face_targets\"," +
					"                        \"analyzed_body_targets\"" +
					"                    ]," +
					"                    \"outputs\": [" +
					"                        \"analyzed_targets\"" +
					"                    ]," +
					"                    \"config\": {" +
					"                        \"concat_items\": [" +
					"                            \"targets\"" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"face_body_matching_filter\"," + contextIdString() +
					"                    \"type\": \"FaceBodyMatchingFilter\"," +
					"                    \"parallel_group\": \"face_body_matching_filter\"," +
					"                    \"inputs\": [" +
					"                        \"analyzed_targets\"," +
					"                        \"images\"" +
					"                    ]," +
					"                    \"outputs\": [\"analyzed_targets\"]," +
					"                    \"config\": {" +
					"                        \"aligner\": {" +
					"                            \"plugin\": \"aligner\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"face_keypoints\"" +
					"                         }," +

					"                        \"feature\": {" +
					"                            \"plugin\": \"feature\"," +
					"                            \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_face_module_scg") + "\"," +
					"                            \"max_batch_size\": 128," +
					"                            \"model_key\": \"face_feature\"" +
					"                         }" +

					"                     }" +
					"                }," +

					"                {" +
					"                    \"name\":\"lightweight_targets_concat\","  + contextIdString() +
					"                    \"type\":\"Concat\"," +
					"                    \"parallel_group\": \"lightweight_targets_concat\"," +
					"                    \"inputs\":[" +
					"                        \"selected_target_tracklets_lightweight\"," +
					"                        \"face_classifier_filtered_targets_lightweight\"," +
					"                        \"body_classifier_filtered_targets_lightweight\"," +
					"                        \"face_multidim_quality_filtered_targets_lightweight\"," +
					"                        \"face_quality_filtered_target_tracklets_lightweight\"" +
					"                    ]," +
					"                    \"outputs\":[" +
					"                        \"lightweight_targets\"" +
					"                    ]," +
					"                    \"config\":{" +
					"                        \"concat_items\":[" +
					"                            \"targets\"" +
					"                        ]" +
					"                    }" +
					"                }," +

					"                {" +
					"                    \"name\": \"output\"," + contextIdString() +
					"                    \"type\": \"Output\"," +
					"                    \"parallel_group\": \"output\"," +
					"                    \"inputs\": [" +
					"                        \"analyzed_targets\"," +
					"                        \"lightweight_targets\"" +
					"                    ]," +
					"                    \"outputs\": []" +
					"                }" +
					"            ]" +
					"        }" +
					"    ]" +
					"}" +
					"";
		}
	}

	@Scheduled(fixedDelay = 30000)
	public void removeOnGoingTracks() throws Exception {

		long currentTime = System.currentTimeMillis();

		boolean logged = Utils.instance.watchFrameTiktokLevel == 654;

		if(onGoingTracks.isEmpty()) {
			log.info("pedTrace trackId removeOnGoingTracks empty");
		}
		ForkJoinPool customThreadPool = new ForkJoinPool(4); // 线程数量可以根据实际情况调整
		try {
			customThreadPool.submit(() ->
					onGoingTracks.entrySet().parallelStream().forEach(entry -> {
						Long key = entry.getKey();
						Map<Integer, PedestrianTrack> subMap = entry.getValue();

						Iterator<Map.Entry<Integer, PedestrianTrack>> subIterator = subMap.entrySet().iterator();
						while (subIterator.hasNext()) {
							Map.Entry<Integer, PedestrianTrack> subEntry = subIterator.next();
							Integer subKey = subEntry.getKey();
							PedestrianTrack track = subEntry.getValue();
							if (track.getAway() != null && (currentTime - track.getAway().getCapture_time() > 60 * 1000 * 1)) {
								if(logged) {
									log.info("pedTrace trackId removeOnGoingTracks remove finish-{}-{}-{}", key, subKey, track.getCurrentAssociation());
								}
								subIterator.remove(); // 使用迭代器的remove方法删除元素
							}
							//log.info("pedTrace trackId removeOnGoingTracks  select-{}-{}-{}", key, subKey, track.getCurrentAssociation());
						}
					})
			).get(); // 等待并行处理完成
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			customThreadPool.shutdown();
		}
	}

	public Object getSelectRoiFilter(String roi, String defRoi){
		if(StringUtils.isBlank(roi)) {
			return defRoi;
		}
		int[][][] rois = JSON.parseObject(roi, int[][][].class);
		if(rois.length <= 0){
			return defRoi;
		}

		JSONArray outputArray = new JSONArray();

		//String[] roisResult = toArrayStringRi(rois);

		JSONObject firstObject = new JSONObject();
		firstObject.put("label_id", 37017);
		firstObject.put("polygons", rois);
		outputArray.add(firstObject);

		JSONObject secondObject = new JSONObject();
		secondObject.put("label_id", 221488);
		secondObject.put("polygons", rois);
		outputArray.add(secondObject);

		return  outputArray;
	}

	public static String[] toArrayStringRi(int[][][] arr) {
		int numRows = arr.length;
		String[] result = new String[numRows];
		for (int i = 0; i < numRows; i++) {
			result[i] = Arrays.deepToString(arr[i]);
		}
		return result;
	}
}
