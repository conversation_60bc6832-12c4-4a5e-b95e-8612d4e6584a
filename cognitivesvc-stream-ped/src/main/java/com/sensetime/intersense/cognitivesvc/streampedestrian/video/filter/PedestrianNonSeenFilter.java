package com.sensetime.intersense.cognitivesvc.streampedestrian.video.filter;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.sensetime.intersense.cognitivesvc.server.utils.*;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.ComsumerContainer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;


import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamPedestrian;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoNonSeenFilter;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.ComsumerContainer.ComsumerParameter;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.PedestrianHandler;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.Tracklet;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.PedestrianTrackerPipeline;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Setter;


/** 处理异步提交过来的帧们
 */
@Order(200)
@Slf4j
public class PedestrianNonSeenFilter extends VideoNonSeenFilter{

	@Setter
	private static PedestrianTrackerPipeline pedestrianContainer;
	
	@Setter
	private static PedestrianHandler pedestrianHandler;

	@Autowired
	private ComsumerContainer comsumerContainer;
	
	@Override
	protected void handleStageOne(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		Map<String, VideoStreamPedestrian> pedestrianMap = pedestrianContainer.getStreamPeds();
		int[] indexArray = Stream.iterate(0, i -> i + 1).limit(devices.length)
				.mapToInt(index -> {
					VideoStreamPedestrian pedestrian = pedestrianMap.get(devices[index].getDeviceId());
					return pedestrian != null ? index : -1;
				})
				.filter(index -> index >= 0)
				.toArray();
		
		if(indexArray.length <= 0)
			return ;
		
		int size = indexArray.length;
		
		Pointer[] gpuFrames = new Pointer[size];
		Long[] sourceIds = new Long[size];
		
		for(int index = 0; index < size; index ++) {
			gpuFrames[index] = videoFrames[indexArray[index]].getGpuFrame();
			sourceIds[index] = Utils.keyToContextId(devices[indexArray[index]].getDeviceId());
		}
		
		PointerByReference input_keson    = new PointerByReference();
		PointerByReference output_stage_1 = new PointerByReference();
		PointerByReference output_stage_2 = new PointerByReference();

		context.get().put("input_keson",    input_keson);
		context.get().put("output_stage_1", output_stage_1);
		context.get().put("output_stage_2", output_stage_2);

		input_keson.setValue(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), KesonUtils.frameToKeson(gpuFrames, sourceIds)));
		
		try {
			pedestrianContainer.getLock().readLock().lock();
			
			int ret = KestrelApi.flock_pipeline_input_and_output(pedestrianContainer.pipeline(), PedestrianTrackerPipeline.stageOne, input_keson.getValue(), output_stage_1);
			if(ret != KestrelApi.KESTREL_OK)
				return ;
		}finally {
			pedestrianContainer.getLock().readLock().unlock();
		}
	}
	
	@Override
	protected void handleStageTwo(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		PointerByReference output_stage_1 = (PointerByReference)context.get().get("output_stage_1");
		if(output_stage_1 == null)
			return ;

		PointerByReference output_stage_2 = new PointerByReference();
		context.get().put("output_stage_2", output_stage_2);
		
		try {
			pedestrianContainer.getLock().readLock().lock();
			
			int ret = KestrelApi.flock_pipeline_input_and_output(pedestrianContainer.pipeline(), PedestrianTrackerPipeline.stageTwo, output_stage_1.getValue(), output_stage_2);
			if(ret != KestrelApi.KESTREL_OK)
				return ;
		}finally {
			pedestrianContainer.getLock().readLock().unlock();
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	protected void handleStageThree(VideoFrame[] videoFrames, VideoStreamInfra[] devices) {
		PointerByReference output_stage_2 = (PointerByReference)context.get().get("output_stage_2");
		if(output_stage_2 == null)
			return ;

		boolean logged = Utils.instance.watchFrameTiktokLevel == 655;
		List<Pair<Long, Integer>> dropIds = new ArrayList<Pair<Long, Integer>>();
		Pointer trackletsKeson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(output_stage_2.getValue(), 0), "targets");
		int tracklet_size = KestrelApi.keson_array_size(trackletsKeson);
		
		Map<Long, VideoFrame> sourceFrameMap = Stream.iterate(0, i -> i + 1)
				.limit(devices.length)
				.collect(Collectors.toMap(index -> Utils.keyToContextId(devices[index].getDeviceId()), index -> videoFrames[index]));
		
		for(int index = 0 ; index < tracklet_size; index ++) {
			Pointer trackletKeson = KestrelApi.keson_get_array_item(trackletsKeson, index);
			Tracklet tracklet = KesonUtils.kesonToType(trackletKeson, Tracklet.class);

			int  trackId  = tracklet.getTrack_id();
			int  status   = tracklet.getStatus();
			long sourceId = tracklet.getSource_id();

			Pair<VideoStreamInfra, VideoStreamPedestrian> pair = pedestrianContainer.getLongPairs().get(sourceId);
			if(pair == null)
				continue;

			Map<Integer, PedestrianTrack> onGoingTrackMap = pedestrianContainer.getOnGoingTracks().get(sourceId);
			PedestrianTrack track = onGoingTrackMap.get(trackId);
			if(track == null) {
				track = PedestrianTrack.builder().device(pair.getLeft()).pedestrian(pair.getRight()).enter(tracklet).build();
				onGoingTrackMap.put(trackId, track);
			}

			track.setFrameIndex(sourceFrameMap.get(sourceId).getFrameIndex());
			track.setFramePts(sourceFrameMap.get(sourceId).getCapturedTime());

			tracklet.setOriginFeature(StreamPedestrianUtils.origin_feature(trackletKeson));
			tracklet.setNormalizeFeature(StreamPedestrianUtils.normalize_feature(trackletKeson));
			Consumer<Pair<PedestrianTrack, ComsumerParameter>> strangerCollect = pedestrianHandler.getStrangerCollector();

			if(status == StreamPedestrianUtils.QUICK_RESPONSE) {
				track.setEnter(tracklet);
			}else if(status == StreamPedestrianUtils.TIME_INTERVAL || status == StreamPedestrianUtils.HIGH_QUALITY || status == StreamPedestrianUtils.TIMEOUT) {
				track.getTrackings().addFirst(tracklet);
			}else if(status == StreamPedestrianUtils.TRACKING_FINISH) {
				//Tracklet previous = track.getCurrentTracklet();
				track.setAway(tracklet);
				dropIds.add(new MutablePair<Long, Integer>(sourceId, trackId));

//				if(previous != null && FaissSeeker.compare_feature(previous.getNormalizeFeature(), tracklet.getNormalizeFeature()) > 0.99f)
//					strangerCollect = null;//认为最后一个选帧和上一个选帧是同一个
			}else {
				log.info("pedTrace track status return {}", trackId);
				continue;//直接返回了
			}
			long captureTime = track.storeFrame(trackletKeson, Objects.requireNonNullElse(track.getPedestrian().getStoreScene(), Utils.instance.imageSceneSave),
					Objects.requireNonNullElse(track.getPedestrian().getStoreTarget(), true));
			tracklet.setCapture_time(captureTime);
			tracklet.setSelectFrameEndTime(System.currentTimeMillis());

			//关联log
			if(logged) {
				PedestrianTrack associationTrack = null;
				if(tracklet.getAssociations() !=null){
					int associationTrackId = tracklet.getAssociations().get(0).getTrack_id();
					associationTrack = onGoingTrackMap.get(associationTrackId);
				}
				log.info("pedTrace one label={},track_id={},association={},status={},associationTrack={},currentTrack={}",tracklet.getLabel(), trackId, tracklet.getAssociations(), status, associationTrack !=null, onGoingTrackMap.get(trackId) !=null);
			}

			PedestrianTrack.hookupTrack(onGoingTrackMap, track, tracklet, logged);

			pedestrianHandler.queueIdendityToComsume(track, status, strangerCollect, pedestrianHandler.getPedestrianMessageSender());

			if(Utils.instance.cogDebug == 0 && tracklet.getLabel() == StreamPedestrianUtils.BODY) {
				try {
					Pointer face = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(trackletKeson, "image")).getValue();
					String fileName = sourceId + "_" + trackId + "_" + "_" + UUID.randomUUID().toString().substring(1, 10);
					FrameUtils.save_image_as_jpg(face, ImageUtils.newFileName("select_face_ped", fileName, "ped_debug"));
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

//		for(Pair<Long, Integer> pair : dropIds) {
//			if(logged) {
//				log.info("pedTrace one label, dropIds:{}", pair.getRight());
//			}
//			//pedestrianContainer.getOnGoingTracks().get(pair.getLeft()).remove(pair.getRight());
//		}

		for(Pair<Long, Integer> pair : dropIds) {// 自身和关联的 status都为5 则删除, 加个map或者定时删除
			Map<Integer, PedestrianTrack> sourceData = pedestrianContainer.getOnGoingTracks().get(pair.getLeft());
			if(sourceData != null) {
				PedestrianTrack currentPed = sourceData.get(pair.getRight());
				if(currentPed != null) {
					List<StreamPedestrianUtils.Association> association = currentPed.getCurrentTracklet().getAssociations();
					if (association !=null ) {
						if(currentPed.getCurrentAssociation() != null && currentPed.getCurrentAssociation().getAway()!=null) {
							sourceData.remove(pair.getRight());
							sourceData.remove(association.get(0).getTrack_id());
							if(logged) {
								log.info("pedTrace trackId status finish{}, remove association{}", pair.getRight(), association.get(0).getTrack_id());
							}
						}
					}
					if(CollectionUtils.isEmpty(association)){
						if(logged) {
							log.info("pedTrace trackId remove status finish{}", pair.getRight());
						}
						sourceData.remove(pair.getRight());
					}
				}
			}
		}

		Pointer dropTrckletBson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(output_stage_2.getValue(), 1), "targets");
		int dropTrackSize = KestrelApi.keson_array_size(dropTrckletBson);

		for(int index = 0; index < dropTrackSize; index ++) {
			Pointer dropTarcklet = KestrelApi.keson_get_array_item(dropTrckletBson, index);
			Map<String, Object> target = (Map<String, Object>)KesonUtils.kesonToJson(dropTarcklet);

			Number sourceId = (Number)target.get("source_id");
			Number trackId = (Number)target.get("track_id");
			Number id = (Number)target.get("id");


			Map<Integer, PedestrianTrack> contextTrackMap = pedestrianContainer.getOnGoingTracks().get(sourceId.longValue());
			PedestrianTrack track = contextTrackMap.get(trackId.intValue());
			if(track != null) {
				if(Utils.instance.dropFaceFlag == 0){
					comsumerContainer.handleTargetDrop(track);
				}
			}
		}
	}
	
	/** 后处理这个帧 */
	@Override
	protected void postHandleStageThree(VideoFrame[] videoFrames, VideoStreamInfra devices[]) {
		PointerByReference input_keson    = (PointerByReference)context.get().get("input_keson");
		PointerByReference output_stage_1 = (PointerByReference)context.get().get("output_stage_1");
		PointerByReference output_stage_2 = (PointerByReference)context.get().get("output_stage_2");
		
		KesonUtils.kesonDeepDelete(input_keson, output_stage_1, output_stage_2);
	}
	
	/** 初始化 */
	@Override
	protected synchronized void initialize(VideoStreamInfra device, boolean initialize) {
		VideoStreamPedestrian ped = pedestrianContainer.getStreamPeds().get(device.getDeviceId());
		if(ped == null)
			return ;
		
		long contextId = Utils.keyToContextId(device.getDeviceId());
		if(initialize){
			pedestrianContainer.reInitContext(device.getDeviceId(), contextId);
		}else {
			pedestrianContainer.initContext(device.getDeviceId(), contextId);
		}

		//todo人体
	}
	
	/** 销毁 */
	@Override
	protected synchronized void destroy(VideoStreamInfra device) {
		try { Thread.sleep(2000); } catch (InterruptedException e) { }
		
		long contextId = Utils.keyToContextId(device.getDeviceId());
		pedestrianContainer.destroyContext(device.getDeviceId(), contextId);
	}
}