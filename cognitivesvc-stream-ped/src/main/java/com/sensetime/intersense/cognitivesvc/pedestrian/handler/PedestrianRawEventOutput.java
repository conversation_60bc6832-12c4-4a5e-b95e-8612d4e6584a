package com.sensetime.intersense.cognitivesvc.pedestrian.handler;

import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.http.MediaType;
import org.springframework.messaging.Message;

@Slf4j
public class PedestrianRawEventOutput implements BaseOutput {

    private final StreamBridge streamBridgeTemplate;

    String PEDESTRIAN_RAW_EVENT_OUTPUT = "pedestrian_raw_event_output-out-0";
    public PedestrianRawEventOutput(StreamBridge streamBridgeTemplate) {
        this.streamBridgeTemplate = streamBridgeTemplate;
    }




    @Override
    public boolean send(Message<?> message, long timeout) {
        if (streamBridgeTemplate != null) {
            boolean send = streamBridgeTemplate.send(PEDESTRIAN_RAW_EVENT_OUTPUT,
                    message, MediaType.TEXT_PLAIN);
            if (send) {
                log.debug("pedestrian_raw_event_output msg send success ");
                return true;
            }
        }

        return false;
    }
}