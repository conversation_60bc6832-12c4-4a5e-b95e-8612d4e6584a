package com.sensetime.intersense.cognitivesvc.streampedestrian.controller;

import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamPedestrian;
import com.sensetime.intersense.cognitivesvc.server.kestrel.FrameEncoderLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_frame_utils_structLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamPedestrianRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.service.OsgPutService;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianEventBuilder;
import com.sensetime.lib.clientlib.response.BaseRes;

import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;
import java.util.concurrent.ExecutorService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController("devicePedestrianProvider")
@RequestMapping(value = "/cognitive/device/pedestrian/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "DevicePedestrianProvider",description = "device pedestrian controller")
@Slf4j
public class DevicePedestrianProvider extends BaseProvider{


	@Autowired
	private ExecutorService cogThreadPool;

	@Autowired
	private VideoStreamPedestrianRepository VideoStreamPedestrianMapper;
	
	@Operation(summary = "视频流pedestrian总数",method = "GET")
	@RequestMapping(value = "/getDevicePedestrianCount", method = RequestMethod.GET)
	public BaseRes<Long> getDevicePedestrianCount() {
		return BaseRes.success(VideoStreamPedestrianMapper.count());
	}
	
	@Operation(summary = "查询视频流pedestrian配置",method = "GET")
	@RequestMapping(value = "/getDevicePedestrian", method = RequestMethod.GET)
	public BaseRes<List<VideoStreamPedestrian>> getDevicePedestrian(@RequestParam(required = false) String deviceId) {
		if(StringUtils.isNotBlank(deviceId))		
			return BaseRes.success(VideoStreamPedestrianMapper.findAllById(Lists.newArrayList(deviceId)));
		else 
			return BaseRes.success(VideoStreamPedestrianMapper.findAll());
	}
	
	@Operation(summary = "添加视频流pedestrian配置", method = "POST")
	@RequestMapping(value = "/addOrUpdateDevicePedestrian", method = RequestMethod.POST)
	public BaseRes<Object> addOrUpdateDevicePedestrian(@RequestBody VideoStreamPedestrian device) throws Exception {
//		if(device.getQuickResponseTime() != null && device.getQuickResponseTime() <= 1)
//			device.setQuickResponseTime(2);
		
		VideoStreamPedestrianMapper.saveAndFlush(device);
		return BaseRes.success("OK");
	}
	
	@Operation(summary = "删除视频流pedestrian配置", method = "POST")
	@RequestMapping(value = "/deleteDevicePedestrian", method = RequestMethod.POST)
	public BaseRes<Object> deleteDevicePedestrian(@RequestParam String deviceId) {
		try{
			VideoStreamPedestrianMapper.deleteById(deviceId);
		}catch (Exception e){
			log.warn("warn deleteDevicePedestrian:{}",e.getMessage());
		}
		return BaseRes.success("OK");
	}
	@Operation(description = "获取流的一帧", method = "GET")
	@RequestMapping(value = "/fetchFrame", method = RequestMethod.GET)
	public BaseRes<String> fetchFrame() throws Exception {


		String sceneImage = "/images/12f5e7829850471a80d0bf6bd03a0973.jpg";
		Pointer cpu_frame = KestrelApi.kestrel_frame_load(sceneImage);

		if(Utils.instance.videoSaveOsgType == 999){
			return null;
		}

		cogThreadPool.execute(() -> {
			//FrameUtils.batch_free_frame(targetFrame);
			extractOsgUrl(cpu_frame, null, 1);
		});

		return null;
	}

	private synchronized  String extractOsgUrl(Pointer targetFrame, PedestrianEventBuilder.OsgObject bucket, int flag) {
		Initializer.bindDeviceOrNot();
		String osgUrl = "";

		try {
			byte[] imageResPointer = null;
			if (Utils.instance.frameEncoderType == 0) {
				//调用kestrel的 keson_encode_to_data
				LongByReference outSize = new LongByReference();
				Pointer resultPointer = FrameEncoderLibrary.INSTANCE.KestrelEncoder(targetFrame, Kestrel_frame_utils_structLibrary.kestrel_frame_enc_format_e.KESTREL_ENC_FMT_JPG, outSize);
				if (resultPointer != null) {
					long size = outSize.getValue();
					imageResPointer = resultPointer.getByteArray(0, (int) size);

					Native.free(Pointer.nativeValue(resultPointer));
				} else {
					log.warn("EncodeFrame failed");
				}
			} else if (Utils.instance.frameEncoderType == 1) {
				//调用image sharp kep 插件encode
				imageResPointer = FrameUtils.encode_image_as_jpg(targetFrame);
			} else if (Utils.instance.frameEncoderType == 2) {
				//调用vps imageio jna or c++ so
				LongByReference outSize = new LongByReference();
				Pointer resultPointer = KestrelApi.kestrel_frame_encoder(targetFrame, flag, outSize);
				if (resultPointer != null) {
					// 使用result字节数组
					long size = outSize.getValue();
					imageResPointer = resultPointer.getByteArray(0, (int) size);
					// 释放内存
					Native.free(Pointer.nativeValue(resultPointer));
				} else {
					log.error("EncodeFrame failed");
				}
			}else if (Utils.instance.frameEncoderType == 3) {
				log.warn("unSupport type {}", Utils.instance.frameEncoderType);
			} else {//unsupport type
				//imageResPointer = null;
				log.warn("unSupport type {}", Utils.instance.frameEncoderType);
			}

			if (imageResPointer != null && imageResPointer.length > 0 && Utils.instance.videoSaveOsgType != 9999 ) {
				String objectStorageGatewayObjectInfo = OsgPutService.putObject(imageResPointer, bucket);
				if (objectStorageGatewayObjectInfo != null) {
					osgUrl = bucket + "/" + objectStorageGatewayObjectInfo;
				} else {
					log.warn("objectStorageGatewayObjectInfo is null {}", bucket);
					throw new RuntimeException("objectStorageGatewayObjectInfo is null");
				}
			}

		} catch (Exception e) {
			log.error("psg put error{}", e.getMessage());
		}finally {
			FrameUtils.batch_free_frame(targetFrame);
			log.info("[VideoHandleLog] [Cost] [into saveImage] sceneFrameToOsg2 batch_free_frame ");
		}
		return osgUrl;
	}

}
