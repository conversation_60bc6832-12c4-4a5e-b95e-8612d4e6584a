package com.sensetime.intersense.cognitivesvc.streampedestrian.handler;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

import com.sensetime.intersense.cognitivesvc.server.entities.FacePedestrianCluster;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianEventBuilder;
import com.sun.jna.Pointer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.HibernateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PasserFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade.PersonFaceObject;
import com.sensetime.intersense.cognitivesvc.seekerface.service.utils.SeekerFaceInitializer;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PasserBodyObject;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade.PersonBodyObject;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.service.utils.SeekerPedestrianInitializer;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.ComsumerContainer.ComsumerParameter;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack.IdentifiedInfo;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.PedestrianValid;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.Tracklet;

import lombok.Getter;

@Component
@Slf4j
@SuppressWarnings("unchecked")
public class PedestrianHandler {
	
	@Autowired
	private SeekerFaceFacade seekerFaceFacade;
	
	@Autowired
	private SeekerPedestrianFacade seekerPedestrianFacade;
	
	@Autowired
	private ExecutorService cogThreadPool;
	
	@Getter
	@Autowired
	private Consumer<Pair<PedestrianTrack, ComsumerParameter>> pedestrianMessageSender;
	
	@Getter
	@Autowired
	private Consumer<Pair<PedestrianTrack, ComsumerParameter>> seenIconUpdater;
	
	@Getter
	@Autowired
	private Consumer<Pair<PedestrianTrack, ComsumerParameter>> strangerCollector;

	@Autowired
	private  ComsumerContainer comsumerContainer;
	
	/** 模型跑出来的特征去异步比对 异步执行消费者操作
	 * @param track 模型跑出来的追踪数据
	 * @param stage 第几阶段
	 * @param comsumers 消费者数组
	 */
	public void queueIdendityToComsume(PedestrianTrack track, int stage, Consumer<Pair<PedestrianTrack, ComsumerParameter>>... comsumers) {		
		Tracklet target;
		switch(stage){
			case StreamPedestrianUtils.TIME_INTERVAL:
			case StreamPedestrianUtils.HIGH_QUALITY:
				target = track.getTrackings().peekFirst();
				break;
			case StreamPedestrianUtils.TIMEOUT:
			case StreamPedestrianUtils.TRACKING_FINISH:
				target = track.getAway();
				break;
			case StreamPedestrianUtils.QUICK_RESPONSE:
			default:
				target = track.getEnter();
				break;
		}
		
		try {
			cogThreadPool.execute(() -> {
				PedestrianValid valid = StreamPedestrianUtils.isPedestrianValid(track.getPedestrian(), target);
				track.getParameters().put("valid", valid);
				if(valid.isValid()) {
					track.getParameters().put("stage", stage);
					List<IdentifiedInfo> identifiedInfos = findoutWhoItIs(track, target);
					if(identifiedInfos.isEmpty()) {
						track.setIdentified(false);
					}else {
						track.setIdentified(true);
						track.addIdentifiedInfo(identifiedInfos);
					}

					for(Consumer<Pair<PedestrianTrack, ComsumerParameter>> comsumer : comsumers) {
						try {
							if(comsumer != null)
								comsumer.accept(new MutablePair<PedestrianTrack, ComsumerParameter>(track, ComsumerParameter.builder().stage(stage).trackIndex(track.trackIndex()).build()));
						}catch(Exception e) {
							e.printStackTrace();
						}
					}
				}else{
					// 发送过滤消息
					//dropPedFlag暂时不用
					if(Utils.instance.dropFaceFlag == 0)
						comsumerContainer.handleTargetDrop(track);
				}

				if(stage == StreamPedestrianUtils.TRACKING_FINISH)
					track.close();//直接关闭可能逻辑上会造成no image出现 不过就这样吧 总比显存泄露强
			});
		}catch(RejectedExecutionException e) {
			e.printStackTrace();
			
			if(stage == StreamPedestrianUtils.TRACKING_FINISH)
				track.close();//直接关闭可能逻辑上会造成no image出现 不过就这样吧 总比显存泄露强
		}
	}
	
	private List<IdentifiedInfo> findoutWhoItIs(PedestrianTrack track, Tracklet target) {
		List<IdentifiedInfo> identifiedInfos = Lists.newArrayList();

		if(Utils.instance.removeSearchEngine)
			return identifiedInfos;

		int label = target.getLabel();
		int trackIndex = track.trackIndex();
		float[] feature = Boolean.TRUE.equals(track.getPedestrian().getRefinement()) ? track.refineFeature() : (float[])target.getNormalizeFeature();
		
		float threshold = label == StreamPedestrianUtils.FACE ? 
				Objects.requireNonNullElse(track.getPedestrian().getFaceFeatureThreshold(), Utils.instance.Lv2Threshold)
		      : Objects.requireNonNullElse(track.getPedestrian().getBodyFeatureThreshold(), Utils.instance.Lv2Threshold);


		
		List<IdentifiedInfo> selfInfos = track.getIdentifiedInfos();
		for(int index = selfInfos.size() - 1; index >= 0; index -- ){
			if(selfInfos.get(index).getIdentifiedFeature() == null)
				continue;
			
			IdentifiedInfo selfInfo = selfInfos.get(index).clone();
			float score = label == StreamPedestrianUtils.FACE ? 
					FaissSeeker.compare_feature_normalize(feature, selfInfo.getIdentifiedFeature(), SeekerFaceInitializer.kSrcPoint, SeekerFaceInitializer.kDstPoint) 
				  : FaissSeeker.compare_feature_normalize(feature, selfInfo.getIdentifiedFeature(), SeekerPedestrianInitializer.pSrcPoint, SeekerPedestrianInitializer.pDstPoint);
			
			if(score >= threshold) {
				selfInfo.setTrackIndex(trackIndex);
				identifiedInfos.add(selfInfo);
				return identifiedInfos;
			}
		}
		
		PersonParam personParam = PersonParam.builder()
					.deptIds(track.getDevice().privileges())
					.count(Objects.requireNonNullElse(track.getPedestrian().getSeekNum(), 1))
					.personGroups(StringUtils.split(track.getPedestrian().getTargetGroup(), ","))
					.threshold(threshold)
					.feature(feature)
					.build();
		
		if(label == StreamPedestrianUtils.FACE) {
			List<Pair<PersonFaceObject, Float>> pairs = seekerFaceFacade.findPerson(personParam);
			for(Pair<PersonFaceObject, Float> pair : pairs)
				identifiedInfos.add(IdentifiedInfo.of(pair.getLeft(), pair.getRight(), trackIndex));
		}else if(label == StreamPedestrianUtils.BODY) {
			List<Pair<PersonBodyObject, Float>> pairs = seekerPedestrianFacade.findPerson(personParam);
			for(Pair<PersonBodyObject, Float> pair : pairs)
				identifiedInfos.add(IdentifiedInfo.of(pair.getLeft(), pair.getRight(), trackIndex));
		}
		
		if(!identifiedInfos.isEmpty())
			return identifiedInfos;
		
		PasserParam passerParam = PasserParam.builder()
				.deptIds(track.getDevice().privileges())
				.count(Objects.requireNonNullElse(track.getPedestrian().getSeekNum(), 1))
				.threshold(threshold)
				.feature(feature)
				.build();
		
		if(label == StreamPedestrianUtils.FACE) {
			List<Pair<PasserFaceObject, Float>> pairs = seekerFaceFacade.findPasser(passerParam);
			for(Pair<PasserFaceObject, Float> pair : pairs)
				identifiedInfos.add(IdentifiedInfo.of(pair.getLeft(), pair.getRight(), trackIndex));
		}else if(label == StreamPedestrianUtils.BODY) {
			List<Pair<PasserBodyObject, Float>> pairs = seekerPedestrianFacade.findPasser(passerParam);
			for(Pair<PasserBodyObject, Float> pair : pairs)
				identifiedInfos.add(IdentifiedInfo.of(pair.getLeft(), pair.getRight(), trackIndex));
		}
		
		return identifiedInfos;
	}



}