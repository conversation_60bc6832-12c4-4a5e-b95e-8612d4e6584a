package com.sensetime.intersense.cognitivesvc.pedestrian.handler;

import com.sensetime.intersense.cognitivesvc.pedestrian.utils.PedestrianInitializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import lombok.*;
import lombok.experimental.Accessors;

public class PedestrianFeatureModelHandler extends AbstractHandler<PedestrianFeatureModelHandler.Feature>{

	public PedestrianFeatureModelHandler() {
		super(false);
		
		this.handlerEntity  = HandlerEntity.builder().build();
		this.pointers = new ModelHolder[] {PedestrianInitializer.hunter_faceBody_holder, PedestrianInitializer.aligner_106_holder, PedestrianInitializer.ped_feature_holder};
	}

	@Override
	protected Feature[] readModelResult(ModelResult modelResult) {
		PointerByReference keson = modelResult.getResult();
		if(keson == null || keson.getValue() == null)
			return new Feature[0];
		
		Pointer targets = KestrelApi.keson_get_object_item(keson.getValue(), "targets");
		int arr_size = KestrelApi.keson_array_size(targets);
		
		Feature[] result = new Feature[arr_size];
		
		for(int index = 0 ; index < arr_size; index ++) {
			result[index] = new Feature();
			Pointer target = KestrelApi.keson_get_array_item(targets, index);
			UtilsReader.readHunterData(target, result[index]);
			result[index].setFeature(UtilsReader.readFeatureData(target));
		}
		
		return result;
	}
	
	@Data
	@EqualsAndHashCode(callSuper=false)
	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Feature extends Detection{
		/** 特征 */
		private float[] feature;
	}
}
