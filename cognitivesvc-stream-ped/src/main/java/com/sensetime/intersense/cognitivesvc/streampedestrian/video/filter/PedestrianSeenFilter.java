package com.sensetime.intersense.cognitivesvc.streampedestrian.video.filter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bytedeco.opencv.opencv_core.CvScalar;
import org.opencv.core.Rect;
import org.springframework.core.annotation.Order;


import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamPedestrian;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawItem;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawMat;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawRect;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawTextCh;
import com.sensetime.intersense.cognitivesvc.stream.video.draw.DrawTextEn;
import com.sensetime.intersense.cognitivesvc.stream.video.filter.VideoSeenFilter;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack.IdentifiedInfo;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.PedestrianHandler;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.PedestrianTrackerPipeline;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.VideoIconContainer;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.ComsumerContainer.ComsumerParameter;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.VideoIconContainer.Icon;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.SeenTrack;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.Tracklet;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Setter;

/** 同步处理提交过来的帧
 */
@Order(200)
public class PedestrianSeenFilter extends VideoSeenFilter{
	
	@Setter
	private static VideoIconContainer videoIconContainer;
	
	@Setter
	private static PedestrianHandler pedestrianHandler;

	@Setter
	private static PedestrianTrackerPipeline pedestrianContainer;
	
	private final PointerByReference inputKeson = new PointerByReference();
	
	private final PointerByReference outputKeson_s1 = new PointerByReference();
	
	private final PointerByReference outputKeson_s2 = new PointerByReference();
	
	private volatile List<DrawItem> previousDrawItems = List.of();
	
	private final Map<Integer, Icon> trackingIcon = new ConcurrentHashMap<Integer, Icon>();
	
	/** 处理帧 */
	@SuppressWarnings("unchecked")
	@Override
	protected void handle(VideoFrame videoFrame, VideoStreamInfra device) {
		VideoStreamPedestrian pedestrian = pedestrianContainer.getStreamPeds().get(device.getDeviceId());
		if(pedestrian == null)
			return ;
		
		if(!Boolean.TRUE.equals(context.get("handleCurrentFrame"))) {
			((List<DrawItem>)context.get("draws")).addAll(previousDrawItems);
			return ;
		}
		
		Long sourceId = Utils.keyToContextId(device.getDeviceId());
		Pointer input = KesonUtils.frameToKeson(new Pointer[] { videoFrame.getGpuFrame()}, new Long[] {sourceId});
		inputKeson.setValue(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), input));
		
		try {
			pedestrianContainer.getLock().readLock().lock();
			
			int ret = KestrelApi.flock_pipeline_input_and_output(pedestrianContainer.pipeline(), PedestrianTrackerPipeline.stageOne, inputKeson.getValue(), outputKeson_s1);
			if(ret != KestrelApi.KESTREL_OK)
				return ;
			
			ret = KestrelApi.flock_pipeline_input_and_output(pedestrianContainer.pipeline(), PedestrianTrackerPipeline.stageTwo, outputKeson_s1.getValue(), outputKeson_s2);
			if(ret != KestrelApi.KESTREL_OK)
				return ;
		}finally {
			pedestrianContainer.getLock().readLock().unlock();
		}
		
		List<Integer> dropIds = new ArrayList<Integer>();
		Long contextId = Utils.keyToContextId(device.getDeviceId());
		Map<Integer, PedestrianTrack> onGoingTrackMap = pedestrianContainer.getOnGoingTracks().get(contextId);
		
		Pointer trackletsKeson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(outputKeson_s2.getValue(), 0), "targets");
		int tracklet_size = KestrelApi.keson_array_size(trackletsKeson);
		for(int index = 0 ; index < tracklet_size; index ++) {
			Pointer trackletKeson = KestrelApi.keson_get_array_item(trackletsKeson, index);
			Tracklet tracklet = KesonUtils.kesonToType(trackletKeson, Tracklet.class);
			
			int  trackId  = tracklet.getTrack_id();
			int  status   = tracklet.getStatus();
			
			Pair<VideoStreamInfra, VideoStreamPedestrian> pair = pedestrianContainer.getLongPairs().get(sourceId);
			if(pair == null)
				continue;

			PedestrianTrack track = onGoingTrackMap.get(trackId);
			if(track == null) {
				track = PedestrianTrack.builder().device(pair.getLeft()).pedestrian(pair.getRight()).enter(tracklet).build();
				onGoingTrackMap.put(trackId, track);
			}
			
			track.setFrameIndex(videoFrame.getFrameIndex());
			track.setFramePts(videoFrame.getCapturedTime());
			
			tracklet.setOriginFeature(StreamPedestrianUtils.origin_feature(trackletKeson));
			tracklet.setNormalizeFeature(StreamPedestrianUtils.normalize_feature(trackletKeson));
			Consumer<Pair<PedestrianTrack, ComsumerParameter>> strangerCollect = pedestrianHandler.getStrangerCollector();
			
			if(status == StreamPedestrianUtils.QUICK_RESPONSE) {
				track.setEnter(tracklet);
			}else if(status == StreamPedestrianUtils.TIME_INTERVAL || status == StreamPedestrianUtils.HIGH_QUALITY || status == StreamPedestrianUtils.TIMEOUT) {
				track.getTrackings().addFirst(tracklet);
			}else if(status == StreamPedestrianUtils.TRACKING_FINISH) {
				Tracklet previous = track.getCurrentTracklet();
				track.setAway(tracklet);
				dropIds.add(trackId);
				
//				if(previous != null && FaissSeeker.compare_feature(previous.getNormalizeFeature(), tracklet.getNormalizeFeature()) > 0.99f)
//					strangerCollect = null;//认为最后一个选帧和上一个选帧是同一个
			}else
				continue;//直接返回了
			
			long captureTime = track.storeFrame(trackletKeson, Objects.requireNonNullElse(track.getPedestrian().getStoreScene(), Utils.instance.imageSceneSave),
					Objects.requireNonNullElse(track.getPedestrian().getStoreTarget(), true));
			tracklet.setCapture_time(captureTime);
			track.getParameters().put("seenTrackingIconMap", trackingIcon);

			PedestrianTrack.hookupTrack(onGoingTrackMap, track, tracklet, false);
			
			pedestrianHandler.queueIdendityToComsume(track, status, strangerCollect, pedestrianHandler.getPedestrianMessageSender(), pedestrianHandler.getSeenIconUpdater());
		}
		
		List<DrawItem> draw_item = (List<DrawItem>)context.get("draws");
		
		List<SeenTrack> targets = new ArrayList<SeenTrack>();
		Pointer targetsKeson = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(outputKeson_s1.getValue(), 0), "targets");
		int target_size = KestrelApi.keson_array_size(targetsKeson);
		for(int index = 0 ; index < target_size; index ++)
			targets.add(KesonUtils.kesonToType(KestrelApi.keson_get_array_item(targetsKeson, index), SeenTrack.class));
		
		List<Pair<SeenTrack, SeenTrack>> mergedTargets = StreamPedestrianUtils.mergeTarget(targets);
		for(Pair<SeenTrack, SeenTrack> mergedTarget : mergedTargets){
			PedestrianTrack track;
			Map<String, Number> position;
			
			/** 计算当有脸有体时候 用人脸的信息和人体的框进行渲染 */
			if(mergedTarget.getLeft() != null && mergedTarget.getRight() != null) {
				position = mergedTarget.getRight().getRoi();
				PedestrianTrack faceTrack = onGoingTrackMap.get(mergedTarget.getLeft().getTrack_id());
				PedestrianTrack bodyTrack = onGoingTrackMap.get(mergedTarget.getRight().getTrack_id());
				
				if(faceTrack != null && bodyTrack != null) {
					if(Boolean.TRUE.equals(faceTrack.getIdentified()))
						track = faceTrack;
					else if(Boolean.TRUE.equals(bodyTrack.getIdentified())) 
						track = bodyTrack;
					else {
						if(faceTrack.getIdentified() != null) 
							track = faceTrack;
						else if(bodyTrack.getIdentified() != null) 
							track = bodyTrack;
						else 
							track = null;
					}
				}else if(faceTrack != null)
					track = faceTrack;
				else if(bodyTrack != null)
					track = bodyTrack;
				else 
					track = null;
			}else if(mergedTarget.getLeft() != null) {
				position = mergedTarget.getLeft().getRoi();
				track = onGoingTrackMap.get(mergedTarget.getLeft().getTrack_id());
			}else if(mergedTarget.getRight() != null) {
				position = mergedTarget.getRight().getRoi();
				track = onGoingTrackMap.get(mergedTarget.getRight().getTrack_id());
			}else {
				throw new RuntimeException("should not be here.");
			}
			
			Rect rect = new Rect(position.get("left").intValue(), position.get("top").intValue(), position.get("width").intValue(), position.get("height").intValue());
			if(track == null || track.getEnter() == null) {
				draw_item.add(new DrawRect(rect, CvScalar.BLUE));
				continue;
			}
			
			Icon icon = trackingIcon.get(track.getEnter().getTrack_id());
			if(icon == null){
				draw_item.add(new DrawRect(rect, CvScalar.BLUE));
				continue;
			}
			
			draw_item.add(new DrawRect(rect, icon.getColor()));
			
			if(StringUtils.isNotBlank(icon.getText())) {
				String text = icon.getText();
				
				if(Boolean.TRUE.equals(track.getIdentified())) {
					IdentifiedInfo identifiedInfo = PedestrianTrack.findNearestIdentifiedInfo(track).get(0);
					
					if(Objects.equals(identifiedInfo.getIdentifiedPersonType(), StreamPedestrianUtils.PASSER)) {
						text = VideoIconContainer.p_uuid.matcher(text).replaceAll(identifiedInfo.getIdentifiedPersonId());
					}else if(Objects.equals(identifiedInfo.getIdentifiedPersonType(), StreamPedestrianUtils.PERSON)) {
						text = VideoIconContainer.p_uuid.matcher(text).replaceAll(identifiedInfo.getIdentifiedPersonId());
						text = VideoIconContainer.p_cn_name.matcher(text).replaceAll(identifiedInfo.getIdentifiedPersonCnName());
						text = VideoIconContainer.p_en_name.matcher(text).replaceAll(identifiedInfo.getIdentifiedPersonEnName());
						text = VideoIconContainer.p_tag.matcher(text).replaceAll(identifiedInfo.getIdentifiedPersonTag());
					}
				}
				
				if(Utils.instance.logged)
					text = track.getEnter().getTrack_id() + "_" + text;
				
				if(VideoIconContainer.isCNChar(text)) 
					draw_item.add(new DrawTextCh(text, icon.getColor(), rect));
				else
					draw_item.add(new DrawTextEn(text, icon.getFont() , icon.getColor(),  rect));
			}
			
			if(icon.getIcon() != null) {
				draw_item.add(new DrawMat(rect, icon.getIcon()));
			}
		}
		
		for(Integer dropId : dropIds) {
			trackingIcon.remove(dropId);
			onGoingTrackMap.remove(dropId);
		}
		
		previousDrawItems = draw_item;
	}
	
	/** 后处理这个gpuframe */
	@Override
	protected void postHandle(VideoFrame videoFrame, VideoStreamInfra device) {
		KesonUtils.kesonDeepDelete(inputKeson, outputKeson_s1, outputKeson_s2);
	}
	
	/** 初始化 */
	@Override
	protected synchronized void initialize(VideoStreamInfra device) {
		VideoStreamPedestrian ped = pedestrianContainer.getStreamPeds().get(device.getDeviceId());
		if(ped == null)
			return ;
		
		long contextId = Utils.keyToContextId(device.getDeviceId());
		pedestrianContainer.initContext(device.getDeviceId(), contextId);
	}
	
	/** 销毁 */
	@Override
	protected synchronized void destroy(VideoStreamInfra device) {
		try { Thread.sleep(2000); } catch (InterruptedException e) { }
		
		long contextId = Utils.keyToContextId(device.getDeviceId());
		pedestrianContainer.destroyContext(device.getDeviceId(), contextId);
	}
}
