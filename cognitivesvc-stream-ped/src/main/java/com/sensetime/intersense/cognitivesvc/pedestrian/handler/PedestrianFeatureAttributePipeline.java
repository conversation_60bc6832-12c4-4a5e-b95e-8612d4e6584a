package com.sensetime.intersense.cognitivesvc.pedestrian.handler;

import java.util.Map;

import com.sensetime.intersense.cognitivesvc.pedestrian.utils.PedestrianInitializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

public class PedestrianFeatureAttributePipeline extends AbstractHandler<PedestrianFeatureAttributePipeline.FaceBody>{

	public PedestrianFeatureAttributePipeline() {
		super(true);
		
		this.handlerEntity  = HandlerEntity.builder().build();
		this.pointers = new ModelHolder[] {
			new ModelHolder("image_face_body_detect",   Utils.instance.pedestrianHarpyEnabled ? faceHarpyConfig : faceBodyConfig, 32, PedestrianInitializer.PED_MODEL_COUNT, true, true, false),
			new ModelHolder("image_face_body_analysis", Utils.instance.pedestrianHarpyEnabled ? faceHarpyConfig : faceBodyConfig, 32, PedestrianInitializer.PED_MODEL_COUNT, true, true, false)
		};
	}

	@SuppressWarnings("unchecked")
	@Override
	protected FaceBody[] readModelResult(ModelResult modelResult) {
		PointerByReference keson = modelResult.getResult();
		if(keson == null || keson.getValue() == null)
			return new FaceBody[0];
		
		Pointer targets = KestrelApi.keson_get_object_item(keson.getValue(), "targets");
		int arr_size = KestrelApi.keson_array_size(targets);
		
		FaceBody[] result = new FaceBody[arr_size];
		for(int index = 0 ; index < arr_size; index ++) {
			result[index] = new FaceBody();
			
			Pointer targetKeson = KestrelApi.keson_get_array_item(targets, index);
			UtilsReader.readHunterData(targetKeson, result[index]);
			result[index].setFeature(UtilsReader.readFeatureData(targetKeson));
			
			Map<String, Object> targetJson = (Map<String, Object>)KesonUtils.kesonToJson(targetKeson);

			int label = ((Number)targetJson.get("label")).intValue();
			if(label == 37017)
				result[index].setAttribute((Map<String, Object>)targetJson.get("attribute"));
			else if(label == 221488) 
				result[index].setAttribute((Map<String, Object>)targetJson.get("attribute_1"));
			
			result[index].setMatchedId(((Number)targetJson.get("matched_id")).intValue());
			result[index].setLabel(label);
		}
		
		for(int index = 0 ; index < arr_size; index ++) {
			if(result[index].matchedId == null)
				continue;
			
			for(int jndex = 0 ; jndex < arr_size; jndex ++)
				if(result[jndex].getId() == result[index].matchedId)
					result[index].matchedFaceBody = result[jndex];
		}
		
		return result;
	}
	
	@Data
	@EqualsAndHashCode(callSuper=false)
	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class FaceBody extends AbstractHandler.Detection{
    	/** 标签 */
    	private int label;
		/** 属性 */
		private Map<String, Object> attribute;
		/** 特征 */
		private Object feature;
		/** 匹配id */
		private Integer matchedId;
		/** 匹配实体 */
		private FaceBody matchedFaceBody;
	}
	
	private static final String faceHarpyConfig = "{" + 
			"    \"streams\": [" + 
			"        {" + 
			"            \"name\": \"image_face_body_detect\"," + 
			"            \"module_plugins\": [" + 
			"                \"detection.fmd\"," + 
			"                \"multiple_target_tracking.fmd\"," + 
			"                \"face_body_matching.fmd\"," + 
			"                \"target_filter.fmd\"," + 
			"                \"matching_result_conversion.fmd\"," + 
			"                \"slice.fmd\"," + 
			"                \"concat.fmd\"," + 
			"                \"plugin.fmd\"," + 
			"                \"operation.fmd\"," + 
			"                \"mergence.fmd\"," + 
			"                \"target_selection.fmd\"," + 
			"                \"refinement.fmd\"," + 
			"                \"roi_filter.fmd\"," + 
			"                \"face_quality.fmd\"," + 
			"                \"face_quality_filter.fmd\"" + 
			"            ]," + 
			"            \"modules\": [" + 
			"                {" + 
			"                    \"name\": \"input\"," + 
			"                    \"type\": \"Input\"," + 
			"                    \"inputs\": []," + 
			"                    \"outputs\": [" + 
			"                        \"images\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_detector\"," + 
			"                    \"type\": \"Detection\"," + 
			"                    \"parallel_group\": \"face_detect\"," + 
			"                    \"inputs\": [" + 
			"                        \"images\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"hunter\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("hunter_small_module") + "\"," + 
			"                        \"max_batch_size\": 16," + 
			"                        \"confidence_threshold\": 0.3" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"struct_detector\"," + 
			"                    \"type\": \"Detection\"," + 
			"                    \"parallel_group\": \"struct_detect\"," + 
			"                    \"inputs\": [" + 
			"                        \"images\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"struct_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"block_frame_num\": 1," + 
			"                        \"plugin\": \"harpy\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("harpy_module") + "\"," + 
			"                        \"max_batch_size\": 8" +  
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"struct_slice\"," + 
			"                    \"type\": \"Slice\"," + 
			"                    \"parallel_group\": \"struct_detect\"," + 
			"                    \"inputs\": [" + 
			"                        \"struct_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"by_labels\": [" + 
			"                            221488" + 
			"                        ]" + 
			"                    }" + 
			"                }," +  
			"                {" + 
			"                    \"name\": \"ttargets_concat\"," + 
			"                    \"type\": \"Concat\"," + 
			"                    \"parallel_group\": \"matching\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_targets\"," + 
			"                        \"body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"facebody_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"concat_items\": [" + 
			"                            \"targets\"" + 
			"                        ]" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_body_matching\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"matching\"," + 
			"                    \"inputs\": [" + 
			"                        \"facebody_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"matched_facebodys\"" + 
			"                    ]," + 
			"                    \"config\": {" + "" +
			"                        \"plugin\": \"cupid\"" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_body_merge\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"matching\"," + 
			"                    \"inputs\": [" + 
			"                        \"facebody_targets\"," + 
			"                        \"matched_facebodys\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"matched_targets\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"facebody_slice\"," + 
			"                    \"type\": \"Slice\"," + 
			"                    \"parallel_group\": \"matching\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"matched_face_targets\"," + 
			"                        \"matched_body_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"by_labels\": [" + 
			"                            37017," + 
			"                            221488" + 
			"                        ]" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_quality_calculate\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_quality\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_face_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"quality_face_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"pageant\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("pageant_module") + "\"," + 
			"                        \"max_batch_size\": 16" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_merge\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"face_quality\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_face_targets\"," + 
			"                        \"quality_face_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_targets_with_quality\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_quality_calculate\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"body_quality\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"quality_body_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"classifier\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_filter_module") + "\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_merge\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"body_quality\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_body_targets\"," + 
			"                        \"quality_body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_targets_with_quality\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"output\"," + 
			"                    \"type\": \"Output\"," + 
			"                    \"parallel_group\": \"output\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_targets_with_quality\"," + 
			"                        \"body_targets_with_quality\"" + 
			"                    ]," + 
			"                    \"outputs\": []" + 
			"                }" + 
			"            ]" + 
			"        }," + 
			"        {" + 
			"            \"name\": \"image_face_body_analysis\"," + 
			"            \"module_plugins\": [" + 
			"                \"detection.fmd\"," + 
			"                \"multiple_target_tracking.fmd\"," + 
			"                \"face_body_matching.fmd\"," + 
			"                \"target_filter.fmd\"," + 
			"                \"matching_result_conversion.fmd\"," + 
			"                \"slice.fmd\"," + 
			"                \"concat.fmd\"," + 
			"                \"plugin.fmd\"," + 
			"                \"operation.fmd\"," + 
			"                \"mergence.fmd\"," + 
			"                \"target_selection.fmd\"," + 
			"                \"refinement.fmd\"," + 
			"                \"roi_filter.fmd\"," + 
			"                \"face_quality.fmd\"," + 
			"                \"face_quality_filter.fmd\"" + 
			"            ]," + 
			"            \"modules\": [" + 
			"                {" + 
			"                    \"name\": \"input\"," + 
			"                    \"type\": \"Input\"," + 
			"                    \"inputs\": []," + 
			"                    \"outputs\": [" + 
			"                        \"face_targets\"," + 
			"                        \"body_targets\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"aligner\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_landmarks\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"aligner\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," + 
			"                        \"max_batch_size\": 64" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_feature_extraction\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_landmarks\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_features\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"feature\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module") + "\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_headpose\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_landmarks\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_headpose\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"headpose\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_attribute_extraction\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_landmarks\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_attributes\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"attribute\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," + 
			"                        \"max_batch_size\": 64" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_all_merge\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_targets\"," + 
			"                        \"face_landmarks\"," + 
			"                        \"face_features\"," + 
			"                        \"face_headpose\"," + 
			"                        \"face_attributes\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_analysis_targets\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_feature_extraction\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"body_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_features\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"senu\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("senu_feature_module") + "\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_attribute_extraction\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"body_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_attributes\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"classifier\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_attribute_module") + "\"," + 
			"                        \"max_batch_size\": 16" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_all_mergence\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"body_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"body_targets\"," + 
			"                        \"body_features\"," + 
			"                        \"body_attributes\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_analysis_targets\"" + 
			"                    ]" +
			"                }," + 
			"                {" + 
			"                    \"name\": \"output\"," + 
			"                    \"type\": \"Output\"," + 
			"                    \"parallel_group\": \"output\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_analysis_targets\"," + 
			"                        \"body_analysis_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": []" + 
			"                }" + 
			"            ]" + 
			"        }" + 
			"    ]" + 
			"}" + 
			"";;
	
	private static final String faceBodyConfig = "{" + 
			"    \"streams\": [" + 
			"        {" + 
			"            \"name\": \"image_face_body_detect\"," + 
			"            \"module_plugins\": [" + 
			"                \"detection.fmd\"," + 
			"                \"multiple_target_tracking.fmd\"," + 
			"                \"face_body_matching.fmd\"," + 
			"                \"target_filter.fmd\"," + 
			"                \"matching_result_conversion.fmd\"," + 
			"                \"slice.fmd\"," + 
			"                \"concat.fmd\"," + 
			"                \"plugin.fmd\"," + 
			"                \"operation.fmd\"," + 
			"                \"mergence.fmd\"," + 
			"                \"target_selection.fmd\"," + 
			"                \"refinement.fmd\"," + 
			"                \"roi_filter.fmd\"," + 
			"                \"face_quality.fmd\"," + 
			"                \"face_quality_filter.fmd\"" + 
			"            ]," + 
			"            \"modules\": [" + 
			"                {" + 
			"                    \"name\": \"input\"," + 
			"                    \"type\": \"Input\"," + 
			"                    \"inputs\": []," + 
			"                    \"outputs\": [" + 
			"                        \"images\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"facebody_detector\"," + 
			"                    \"type\": \"Detection\"," + 
			"                    \"parallel_group\": \"detect\"," + 
			"                    \"inputs\": [" + 
			"                        \"images\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"detected_facebodys\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"hunter\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("facebody_module") + "\"," + 
			"                        \"max_batch_size\": 8," + 
			"                        \"confidence_threshold\": 0.3" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_body_matching\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"matching\"," + 
			"                    \"inputs\": [" + 
			"                        \"detected_facebodys\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"matched_facebodys\"" + 
			"                    ]," + 
			"                    \"config\": {" + "" +
			"                        \"plugin\": \"cupid\"" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_body_merge\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"matching\"," + 
			"                    \"inputs\": [" + 
			"                        \"detected_facebodys\"," + 
			"                        \"matched_facebodys\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"matched_targets\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"facebody_slice\"," + 
			"                    \"type\": \"Slice\"," + 
			"                    \"parallel_group\": \"matching\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"matched_face_targets\"," + 
			"                        \"matched_body_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"by_labels\": [" + 
			"                            37017," + 
			"                            221488" + 
			"                        ]" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_quality_calculate\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_quality\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_face_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"quality_face_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"pageant\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("pageant_module") + "\"," + 
			"                        \"max_batch_size\": 16" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_merge\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"face_quality\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_face_targets\"," + 
			"                        \"quality_face_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_targets_with_quality\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_quality_calculate\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"body_quality\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"quality_body_targets\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"classifier\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_filter_module") + "\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_merge\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"body_quality\"," + 
			"                    \"inputs\": [" + 
			"                        \"matched_body_targets\"," + 
			"                        \"quality_body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_targets_with_quality\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"output\"," + 
			"                    \"type\": \"Output\"," + 
			"                    \"parallel_group\": \"output\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_targets_with_quality\"," + 
			"                        \"body_targets_with_quality\"" + 
			"                    ]," + 
			"                    \"outputs\": []" + 
			"                }" + 
			"            ]" + 
			"        }," + 
			"        {" + 
			"            \"name\": \"image_face_body_analysis\"," + 
			"            \"module_plugins\": [" + 
			"                \"detection.fmd\"," + 
			"                \"multiple_target_tracking.fmd\"," + 
			"                \"face_body_matching.fmd\"," + 
			"                \"target_filter.fmd\"," + 
			"                \"matching_result_conversion.fmd\"," + 
			"                \"slice.fmd\"," + 
			"                \"concat.fmd\"," + 
			"                \"plugin.fmd\"," + 
			"                \"operation.fmd\"," + 
			"                \"mergence.fmd\"," + 
			"                \"target_selection.fmd\"," + 
			"                \"refinement.fmd\"," + 
			"                \"roi_filter.fmd\"," + 
			"                \"face_quality.fmd\"," + 
			"                \"face_quality_filter.fmd\"" + 
			"            ]," + 
			"            \"modules\": [" + 
			"                {" + 
			"                    \"name\": \"input\"," + 
			"                    \"type\": \"Input\"," + 
			"                    \"inputs\": []," + 
			"                    \"outputs\": [" + 
			"                        \"face_targets\"," + 
			"                        \"body_targets\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"aligner\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_landmarks\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"aligner\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("aligner_106_module") + "\"," + 
			"                        \"max_batch_size\": 64" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_feature_extraction\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_landmarks\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_features\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"feature\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("feature_module") + "\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_headpose\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_landmarks\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_headpose\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"headpose\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("headpose_module") + "\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_attribute_extraction\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_landmarks\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_attributes\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"attribute\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("attribute_classify_module") + "\"," + 
			"                        \"max_batch_size\": 64" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"face_all_merge\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"face_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_targets\"," + 
			"                        \"face_landmarks\"," + 
			"                        \"face_features\"," + 
			"                        \"face_headpose\"," + 
			"                        \"face_attributes\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"face_analysis_targets\"" + 
			"                    ]" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_feature_extraction\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"body_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_features\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"senu\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("senu_feature_module") + "\"," + 
			"                        \"max_batch_size\": 32" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_attribute_extraction\"," + 
			"                    \"type\": \"Plugin\"," + 
			"                    \"parallel_group\": \"body_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"body_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_attributes\"" + 
			"                    ]," + 
			"                    \"config\": {" + 
			"                        \"plugin\": \"classifier\"," + 
			"                        \"model\": \"" + "/usr/cognitivesvc/" + Initializer.modelPathMap.get("ped_attribute_module") + "\"," + 
			"                        \"max_batch_size\": 16" + 
			"                    }" + 
			"                }," + 
			"                {" + 
			"                    \"name\": \"body_all_mergence\"," + 
			"                    \"type\": \"Mergence\"," + 
			"                    \"parallel_group\": \"body_analysis\"," + 
			"                    \"inputs\": [" + 
			"                        \"body_targets\"," + 
			"                        \"body_features\"," + 
			"                        \"body_attributes\"" + 
			"                    ]," + 
			"                    \"outputs\": [" + 
			"                        \"body_analysis_targets\"" + 
			"                    ]" +
			"                }," + 
			"                {" + 
			"                    \"name\": \"output\"," + 
			"                    \"type\": \"Output\"," + 
			"                    \"parallel_group\": \"output\"," + 
			"                    \"inputs\": [" + 
			"                        \"face_analysis_targets\"," + 
			"                        \"body_analysis_targets\"" + 
			"                    ]," + 
			"                    \"outputs\": []" + 
			"                }" + 
			"            ]" + 
			"        }" + 
			"    ]" + 
			"}" + 
			"";;
}
