package com.sensetime.intersense.cognitivesvc.pedestrian.controller;

import com.sensetime.intersense.cognitivesvc.pedestrian.handler.FeatureModelHandler;
import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianFeatureModelHandler;
import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianFeaturePipeline;
import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianFeaturePipeline.FaceBody;
import com.sensetime.intersense.cognitivesvc.seekerface.controller.MidfaceSeekerFaceProvider;
import com.sensetime.intersense.cognitivesvc.seekerface.seeker.SeekerFaceFacade;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.controller.MidfaceSeekerPedestrianProvider;
import com.sensetime.intersense.cognitivesvc.seekerpedestrian.seeker.SeekerPedestrianFacade;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.entities.*;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PasserParam;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.PersonParam;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.mapper.FacePedestrianClusterRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonFaceFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.PersonPedestrianFeatureRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.FeatureConversionEncryptionUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.lib.weblib.utils.Baggages;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.HibernateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@RestController("pedestrianMidfaceProvider")
@RequestMapping(value = "/cognitive/pedestrian/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "" +
        "PedestrianMidfaceProvider", description = "pedestrian controller")
@SuppressWarnings("rawtypes")
public class PedestrianMidfaceProvider extends BaseProvider {

    @Autowired
    private FacePedestrianClusterRepository facePedestrianClusterRepository;

    @Autowired
    private PersonFaceFeatureRepository personFaceFeatureMapper;

    @Autowired
    private PersonPedestrianFeatureRepository personPedestrianFeatureRepository;

    @Autowired(required = false)
    private SeekerFaceFacade seekerFaceFacade;

    @Autowired(required = false)
    private SeekerPedestrianFacade seekerPedestrianFacade;

    @Autowired
    private PedestrianFeaturePipeline pedestrianFeaturePipeline;

    @Autowired
    private MidfaceSeekerFaceProvider midfaceSeekerFaceProvider;

    @Autowired
    private MidfaceSeekerPedestrianProvider midfaceSeekerPedestrianProvider;

    @Value("${spring.application.name}")
    private String name;


    @Value("${cognitivesvc.featureBatchLimit:32}")
    private Integer featureBatchLimit;

    @Value("${cognitivesvc.viperEncKey:}")
    public  String viperEncKey;

    @Autowired
    private FeatureModelHandler featureModelHandler;

    @Autowired
    private PedestrianFeatureModelHandler pedestrianFeatureModelHandler;


    @Operation(summary = "从人体图片提取特征", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/retrievePedestrianFeature", method = RequestMethod.GET)
    public BaseRes<Map> retrieveFaceFeature(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = true, name = "personId", description = "人员UUID") @RequestParam String personId,
            @Parameter(required = true, name = "personCnName", description = "人员中文名争") @RequestParam String personCnName,
            @Parameter(required = false, name = "personEnName", description = "人员英文名争") @RequestParam(value = "personEnName", required = false) String personEnName,
            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag) {
        String deptid = Baggages.getDeptId();
        Map<String, Object> data = new HashMap<>();
        data.put("faceModelVersion", Initializer.modelPathMap.get("feature_module"));
        data.put("bodyModelVersion", Initializer.modelPathMap.get("senu_feature_module"));

        FaceBody[] faceBodys = pedestrianFeaturePipeline.extractByPath(figureImageUrl);
        if (ArrayUtils.isEmpty(faceBodys))
            return BaseRes.success(data);

        FaceBody face = null, body = null;
        if (faceBodys[0].getLabel() == 37017) {
            face = faceBodys[0];
            body = faceBodys[0].getMatchedFaceBody();
        } else if (faceBodys[0].getLabel() == 221488) {
            body = faceBodys[0];
            face = faceBodys[0].getMatchedFaceBody();
        }

        midfaceSeekerFaceProvider.deleteByPid(List.of(personId), null, null);
        midfaceSeekerPedestrianProvider.deleteByPid(List.of(personId), null, null);

        try {
            if (face != null) {
                PersonPedestrianFeature pff = new PersonPedestrianFeature();
                pff.setAvatarImageUrl(figureImageUrl);
                pff.setCreateUser(CREATE_MODIFY_USER);
                pff.setPrivilege("*".equals(deptid) ? "0" : deptid);
                pff.setPersonCnName(personCnName);
                if (!StringUtils.isBlank(personEnName)) {
                    pff.setPersonEnName(personEnName);
                }
                pff.setModelVersion(Initializer.modelPathMap.get("senu_feature_module"));
                pff.setLastModTs(new Date());
                pff.setTag(tag);
                pff.setPersonUuid(personId);
                pff.setSts(STS_VALID);
                pff.setCreateTs(new Date());

                String featureString = FaissSeeker.featureToString((float[]) face.getFeature());
                pff.setImageFeature(featureString);

                personPedestrianFeatureRepository.saveAndFlush(pff);
            }

            if (body != null) {
                PersonFaceFeature pff = new PersonFaceFeature();
                pff.setAvatarImageUrl(figureImageUrl);
                pff.setCreateUser(CREATE_MODIFY_USER);
                pff.setPrivilege("*".equals(deptid) ? "0" : deptid);
                pff.setPersonCnName(personCnName);
                if (!StringUtils.isBlank(personEnName)) {
                    pff.setPersonEnName(personEnName);
                }
                pff.setModelVersion(Initializer.modelPathMap.get("feature_module"));
                pff.setLastModTs(new Date());
                pff.setCreateTs(new Date());
                pff.setTag(tag);
                pff.setPersonUuid(personId);
                pff.setSts(STS_VALID);

                String featureString = FaissSeeker.featureToString((float[]) body.getFeature());
                pff.setImageFeature(featureString);

                personFaceFeatureMapper.saveAndFlush(pff);
            }

            if (face != null && body != null) {
                FacePedestrianCluster cluster = new FacePedestrianCluster();

                cluster.setFacePersonId(personId);
                cluster.setPedestrianPersonId(personId);
                cluster.setFacePersonType(0);
                cluster.setPedestrianPersonType(0);

                //facePedestrianClusterRepository.saveAndFlush(cluster);

                try {
                    facePedestrianClusterRepository.saveAndFlush(cluster);
                } catch (HibernateException e) {

                } catch (Exception ex) {

                }
            }
        } catch (Exception e) {
            log.error("error={}", e.getMessage(), e);
            throw new BusinessException(RESP_SERVER_ERROR_CODE, e.getMessage());
        }

        return BaseRes.success(data);
    }

    @Operation(summary = "以图搜人，直接用图片base64", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/comparePedestrianBase64", method = RequestMethod.POST)
    public BaseRes<Object> comparePedestrianBase64(
            @Parameter(required = true, name = "figureImageBase64", description = "图片") @RequestParam("figureImageBase64") String figureImageBase64,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold) throws IOException {

        File tmpImage = ImageUtils.storeImageToMemoryFileSystem(figureImageBase64);

        try {
            return comparePedestrianIdentity(tmpImage.getAbsolutePath(), personType, personGroup, tag, count, threshold);
        } finally {
            tmpImage.delete();
        }
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "以图搜人", method = "GET")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/comparePedestrianIdentity", method = RequestMethod.GET)
    public BaseRes<Object> comparePedestrianIdentity(
            @Parameter(required = true, name = "figureImageUrl", description = "图片路径") @RequestParam String figureImageUrl,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag,逗号分隔") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold) {
        if (count != null && count <= 0)
            count = null;

        FaceBody[] faceBodys = pedestrianFeaturePipeline.extractByPath(figureImageUrl);
        if (ArrayUtils.isEmpty(faceBodys))
            throw new BusinessException("3009", "no body in the image.[" + figureImageUrl + "]");

        String[] deptids = Baggages.getAllDeptIds();

//		if(ArrayUtils.contains(deptids, "*"))
//			deptids = null;

        FaceBody face = null, body = null;
        if (faceBodys[0].getLabel() == 37017) {
            face = faceBodys[0];
            body = faceBodys[0].getMatchedFaceBody();
        } else if (faceBodys[0].getLabel() == 221488) {
            body = faceBodys[0];
            face = faceBodys[0].getMatchedFaceBody();
        }

        List<Map<String, Object>>[] faceBodyTargets = new List[4];

        if ("Target".equals(personType) || StringUtils.isBlank(personType)) {
            if (face != null)
                faceBodyTargets[0] = seekerFaceFacade.findPerson(PersonParam.builder()
                        .feature((float[]) face.getFeature())
                        .count(count)
                        .personGroups(personGroup == null ? null : personGroup.split(","))
                        .tags(tag == null ? null : tag.split(","))
                        .threshold(threshold)
                        .deptIds(deptids)
                        .figureImageUrl(figureImageUrl)
                        .build())
                        .stream()
                        .map(item -> {
                            Map<String, Object> map = new HashMap<String, Object>();
                            map.put("targetType", "Target");
                            map.put("label", "face");
                            map.put("personID", item.getLeft().pid);
                            map.put("score", item.getValue());
                            return map;
                        })
                        .collect(Collectors.toList());

            if (body != null)
                faceBodyTargets[1] = seekerPedestrianFacade.findPerson(PersonParam.builder()
                        .feature((float[]) body.getFeature())
                        .count(count)
                        .personGroups(personGroup == null ? null : personGroup.split(","))
                        .tags(tag == null ? null : tag.split(","))
                        .threshold(threshold)
                        .deptIds(deptids)
                        .build())
                        .stream()
                        .map(item -> {
                            Map<String, Object> map = new HashMap<String, Object>();
                            map.put("targetType", "Target");
                            map.put("label", "body");
                            map.put("personID", item.getLeft().pid);
                            map.put("score", item.getValue());
                            return map;
                        })
                        .collect(Collectors.toList());
        }

        if ("Passer".equals(personType) || StringUtils.isBlank(personType)) {
            if (face != null && CollectionUtils.isEmpty(faceBodyTargets[0])) {
                faceBodyTargets[2] = seekerFaceFacade.findPasser(PasserParam.builder()
                        .feature((float[]) face.getFeature())
                        .count(count)
                        .deptIds(deptids)
                        .threshold(threshold)
                        .build())
                        .stream()
                        .map(item -> {
                            Map<String, Object> map = new HashMap<String, Object>();
                            map.put("personID", item.getLeft().pid);
                            map.put("targetType", "Passer");
                            map.put("label", "face");
                            map.put("score", item.getValue());
                            return map;
                        })
                        .collect(Collectors.toList());
            }

            if (body != null && CollectionUtils.isEmpty(faceBodyTargets[1])) {
                faceBodyTargets[3] = seekerPedestrianFacade.findPasser(PasserParam.builder()
                        .feature((float[]) body.getFeature())
                        .count(count)
                        .deptIds(deptids)
                        .threshold(threshold)
                        .build())
                        .stream()
                        .map(item -> {
                            Map<String, Object> map = new HashMap<String, Object>();
                            map.put("personID", item.getLeft().pid);
                            map.put("targetType", "Passer");
                            map.put("label", "body");
                            map.put("score", item.getValue());
                            return map;
                        })
                        .collect(Collectors.toList());
            }
        }

        return BaseRes.success(faceBodyTargets);
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "以图搜人", method = "POST")
    @Parameters({
            @Parameter(name = "baggage-deptid", description = "deptid", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "baggage-subdeptids", description = "subdeptids", in = ParameterIn.HEADER)
    })
    @RequestMapping(value = "/comparePedestrianStream", method = RequestMethod.POST)
    public BaseRes<Object> comparePedestrianStream(
            @Parameter(required = true, name = "figureImage", description = "图片流") @RequestParam("figureImage") MultipartFile figureImage,
            @Parameter(required = false, name = "personType", description = "用户类型") @RequestParam(value = "personType", required = false) String personType,
            @Parameter(required = false, name = "personGroup", description = "用户群组") @RequestParam(value = "personGroup", required = false) String personGroup,
            @Parameter(required = false, name = "tag", description = "tag,逗号分隔") @RequestParam(value = "tag", required = false) String tag,
            @Parameter(required = false, name = "count", description = "最多搜寻数量") @RequestParam(value = "count", required = false) Integer count,
            @Parameter(required = false, name = "threshold", description = "得分阈值") @RequestParam(value = "threshold", required = false) Float threshold) throws IOException {

        File tmpImage = new File("/tmp/csf_" + ThreadLocalRandom.current().nextLong() + ".jpg");
        figureImage.transferTo(tmpImage);
        try {
            return comparePedestrianIdentity(tmpImage.getAbsolutePath(), personType, personGroup, tag, count, threshold);
        } finally {
            tmpImage.delete();
        }
    }

    @Operation(summary = "批量提特 - 支持人脸人体", method = "POST")
    @RequestMapping(value = "/retrieveBatchFeature", method = RequestMethod.POST)
    public BaseRes<Map<Integer, List<Map<String, Object>>>> retrieveBatchFeature(@RequestBody CognitiveEntity.SenseyexRawFeature message,
                                        @RequestParam(required = false, defaultValue = "false") Boolean ignoreLimit) {

        String[] images = message.getImages();
        validateImages(images,message.getBodyImages(), ignoreLimit);

        Map<Integer, List<Map<String, Object>>> result = new HashMap<>();


        Arrays.stream(message.getProcessors()).forEach(retrieveType -> {
            if (ArrayUtils.isEmpty(images) && retrieveType == 37017) {
                throw new BusinessException("3004", "face images length should not be 0");
            }
            if (ArrayUtils.isEmpty(message.getBodyImages()) && retrieveType == 221488) {
                throw new BusinessException("3004", "body images length should not be 0");
            }
            List<Map<String, Object>> allImageModelResult = processImages(retrieveType, images, message.getBodyImages(), message.getIsEncoder());
            result.put(retrieveType, allImageModelResult);
        });

        return BaseRes.success(result);
    }

    private void validateImages(String[] images, String[] bodyImages,Boolean ignoreLimit) {
        if (ArrayUtils.isEmpty(images) && ArrayUtils.isEmpty(bodyImages) ) {
            throw new BusinessException("3004", "length should not be 0");
        }
        if (!ignoreLimit && images != null && images.length > featureBatchLimit) {
            throw new BusinessException("3011", "length over limit");
        }
        if (!ignoreLimit && bodyImages != null && bodyImages.length > featureBatchLimit) {
            throw new BusinessException("3011", "length over limit");
        }
    }

    private List<Map<String, Object>> processImages(Integer retrieveType, String[] images, String[] bodyImages,Boolean isEncoder) {
        List<Map<String, Object>> allImageModelResult = new ArrayList<>();

        if (retrieveType == 37017) {
            processFaceImages(images, allImageModelResult, isEncoder);
        } else if (retrieveType == 221488) {
            processBodyImages(bodyImages, allImageModelResult, isEncoder);
        }

        return allImageModelResult;
    }

    private void processFaceImages(String[] images, List<Map<String, Object>> allImageModelResult, Boolean isEncoder) {
        // 使用线程安全的集合来存储结果
        ConcurrentLinkedQueue<Map<String, Object>> concurrentResults = new ConcurrentLinkedQueue<>();

        // 创建一个固定大小的线程池
        ExecutorService executorService = Executors.newFixedThreadPool(images.length); //可以根据实际情况调整

        // 创建任务
        List<Callable<Map<String, Object>>> tasks = Arrays.stream(images).parallel().map(figureImageUrl -> (Callable<Map<String, Object>>) () -> {
            try {
                return processSingleImage(figureImageUrl, isEncoder);
            } catch (Exception e) {
                log.error("Error processing face image: {}, error: {}", figureImageUrl, e.getMessage());
                return null; // 返回 null 以便后续处理
            }
        }).collect(Collectors.toList());

        try {
            // 提交任务并获取结果
            List<Future<Map<String, Object>>> futures = executorService.invokeAll(tasks);

            // 收集结果
            for (Future<Map<String, Object>> future : futures) {
                Map<String, Object> result = future.get();
                if (result != null) { // 只添加非 null 的结果
                    concurrentResults.add(result);
                }
            }
        } catch (Exception e) {
            log.error("Error processing images", e);
        } finally {
            executorService.shutdown();
        }

        // 将线程安全集合中的结果添加到原始结果列表中
        allImageModelResult.addAll(concurrentResults);
    }

    private Map<String, Object> processSingleImage(String figureImageUrl, Boolean isEncoder) {
        Map<String, Object> dataSingle = new HashMap<>();
        dataSingle.put("modelVersion", Initializer.modelPathMap.get("feature_module"));

        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -599;

        long step1 = 0L;
        if (loggedForCost) {
            step1 = new Date().getTime();
        }
        try {
            FeatureModelHandler.Feature features[] = featureModelHandler.extractByPath(figureImageUrl);
            if (loggedForCost) {
                log.info("[VideoHandleLog] [Cost] retrieveBatchFeatureCost output write cost {}, images:{}", new Date().getTime() - step1, figureImageUrl);
            }
            String featureString = extractFeatureString(features, figureImageUrl);
            if (isEncoder) {
                Base64.Encoder encoder = Base64.getEncoder();
                String featureBlob = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeader(
                        FeatureConversionEncryptionUtils.encodeBlob(featureString, viperEncKey)));

                dataSingle.put("feature", featureBlob);
            } else {
                dataSingle.put("feature", featureString);
            }
        } catch (Exception e) {
            log.error("error={},image={}", e.getMessage(), figureImageUrl);
            throw new BusinessException(RESP_SERVER_ERROR_CODE, e.getMessage());
        }

        Map<String, Object> data = new HashMap<>();
        data.put(figureImageUrl, dataSingle);

        return data;
    }

    private String extractFeatureString(FeatureModelHandler.Feature[] features, String figureImageUrl) {
        if (ArrayUtils.isNotEmpty(features)) {
            FeatureModelHandler.Feature feature = Arrays.stream(features)
                    .max(Comparator.comparing(r -> (int) (r.getHunter().getHeight() * r.getHunter().getWidth())))
                    .orElseThrow(() -> new BusinessException("3001", "no face in the image.[" + figureImageUrl + "]"));

            return FaissSeeker.featureToString(feature.getFeature());
        } else if (Utils.instance.retrieveType == 1) {
            return FaissSeeker.featureToString(new float[256]);
        } else {
            throw new RuntimeException("retrieveType is not 0 or 1.image.[" + figureImageUrl + "]");
        }
    }

    private String extractBodyFeatureString(PedestrianFeatureModelHandler.Feature[] features, String figureImageUrl) {
        if (ArrayUtils.isNotEmpty(features)) {
            PedestrianFeatureModelHandler.Feature feature = Arrays.stream(features)
                    .max(Comparator.comparing(r -> (int) (r.getHunter().getHeight() * r.getHunter().getWidth())))
                    .orElseThrow(() -> new BusinessException("3001", "no face in the image.[" + figureImageUrl + "]"));

            return FaissSeeker.featureToString(feature.getFeature());
        } else if (Utils.instance.retrieveType == 1) {
            return FaissSeeker.featureToString(new float[256]);
        } else {
            throw new RuntimeException("retrieveType is not 0 or 1.");
        }
    }

    private void processBodyImages(String[] bodyImages, List<Map<String, Object>> allImageModelResult, Boolean isEncoder) {
        // 使用线程安全的集合来存储结果
        ConcurrentLinkedQueue<Map<String, Object>> concurrentResults = new ConcurrentLinkedQueue<>();

        // 创建一个固定大小的线程池
        ExecutorService executorService = Executors.newFixedThreadPool(bodyImages.length); // 20 是一个示例值，可以根据实际情况调整

        // 创建任务
        List<Callable<Map<String, Object>>> tasks = Arrays.stream(bodyImages).map(figureImageUrl -> (Callable<Map<String, Object>>) () -> {
            return processSingleBodyImage(figureImageUrl, isEncoder);
        }).collect(Collectors.toList());

        try {
            // 提交任务并获取结果
            List<Future<Map<String, Object>>> futures = executorService.invokeAll(tasks);

            // 收集结果
            for (Future<Map<String, Object>> future : futures) {
                concurrentResults.add(future.get());
            }
        } catch (Exception e) {
            log.error("Error processing images", e);
        } finally {
            executorService.shutdown();
        }

        // 将线程安全集合中的结果添加到原始结果列表中
        allImageModelResult.addAll(concurrentResults);
    }

    private Map<String, Object> processSingleBodyImage(String figureImageUrl, Boolean isEncoder) {
        Map<String, Object> dataSingle = new HashMap<>();
        dataSingle.put("modelVersion", Initializer.modelPathMap.get("senu_feature_module"));

        Map<String, Object> data = new HashMap<>();

        FaceBody[] faceBodys = pedestrianFeaturePipeline.extractByPath(figureImageUrl);
        if (ArrayUtils.isEmpty(faceBodys))
            return data;

        FaceBody face = null, body = null;
        if (faceBodys[0].getLabel() == 37017) {
            body = faceBodys[0].getMatchedFaceBody();
        } else if (faceBodys[0].getLabel() == 221488) {
            body = faceBodys[0];
        }

        if (body != null) {
            String featureString = FaissSeeker.featureToString((float[]) body.getFeature());
            if (isEncoder) {
                Base64.Encoder encoder = Base64.getEncoder();
                String featureBlob = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeaderBody(
                        FeatureConversionEncryptionUtils.encodeBlob(featureString, viperEncKey)));

                dataSingle.put("feature", featureBlob);
            } else {
                dataSingle.put("feature", featureString);
            }
        }
        data.put(figureImageUrl, dataSingle);

        return data;
    }

}
