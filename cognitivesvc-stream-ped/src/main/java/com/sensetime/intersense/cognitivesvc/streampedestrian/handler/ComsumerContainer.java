package com.sensetime.intersense.cognitivesvc.streampedestrian.handler;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianRawEventOutput;
import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import com.sensetime.intersense.cognitivesvc.stream.video.VideoStreamNonSeen;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.HibernateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import com.sensetime.intersense.cognitivesvc.server.entities.FacePedestrianCluster;
import com.sensetime.intersense.cognitivesvc.server.mapper.FacePedestrianClusterRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.strangerface.entity.StrangerFaceObject;
import com.sensetime.intersense.cognitivesvc.strangerface.service.StrangerFaceSeekerFacade;
import com.sensetime.intersense.cognitivesvc.strangerpedestrian.entity.StrangerPedestrianObject;
import com.sensetime.intersense.cognitivesvc.strangerpedestrian.service.StrangerPedestrianSeekerFacade;
import com.sensetime.intersense.cognitivesvc.streampedestrian.handler.VideoIconContainer.Icon;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianEventBuilder;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack.IdentifiedInfo;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.Tracklet;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Configuration("comsumerPedestrianContainer")
@Slf4j
public class ComsumerContainer {
	
	@Autowired
	private VideoIconContainer videoIconContainer;
	
/*	@Autowired
	private MessageChannel pedestrian_raw_event_output;*/

	@Autowired
	BaseOutput pedestrian_raw_event_output;
	
	@Autowired
	private StrangerFaceSeekerFacade strangerFaceSeekerFacade;
	
	@Autowired
	private StrangerPedestrianSeekerFacade strangerPedestrianSeekerFacade;
	
	@Autowired
	private FacePedestrianClusterRepository facePedestrianClusterRepository;


	@Value("${cognitivesvc.viperEncKey:}")
	public  String viperEncKey;

	@Value("${sensexperience.osgBucket:face}")
	private String bucketName;

	@Value("${sensexperience.url:}")
	private String osgUrl;

	@Value("${sensexperience.timeout:3000}")
	private Integer timeout;


	/** 陌生人收集器 */
	@SuppressWarnings("unchecked")
	@Bean
	public Consumer<Pair<PedestrianTrack, ComsumerParameter>> strangerCollector(){
		return pair -> {
			if("none".equals(Utils.instance.runningType))
				return;

			if(Utils.instance.removeSearchEngine)
				return;

			PedestrianTrack track = pair.getLeft();
			int trackIndex = pair.getRight().getTrackIndex();
			
			List<IdentifiedInfo> infos = track.getIdentifiedInfos().stream().filter(iden -> iden.getTrackIndex() == trackIndex).collect(Collectors.toList());
			if(!infos.isEmpty())
				return;
			
			int trackId = track.getEnter().getTrack_id();
			
			IdentifiedInfo faceInfo = null, bodyInfo = null;
			PedestrianTrack faceTrack = null, bodyTrack = null;
			
			int label = track.getCurrentTracklet().getLabel();
			if(label == StreamPedestrianUtils.FACE) {
				faceTrack = track;
				bodyTrack = track.getCurrentAssociation();
				
				String imagePath = faceTrack.targetFrameToPath();
				Tracklet tracklet = faceTrack.getCurrentTracklet();
				
				StrangerFaceObject obj = StrangerFaceObject.builder()
						.feature((float[])tracklet.getNormalizeFeature())
						.createTs(new Date())
						.imagePath(imagePath)
						.trackId(trackId)
						.infra(faceTrack.getDevice())
						.quality(tracklet.getQuality())
						.build();
				
				try {
					String age = ((Map<String, Object>)(tracklet.getAttribute().get("st_age_value"))).get("st_age_value").toString();
					obj.setAge(age);
					
					float female = ((Number)(((Map<String, Object>)(tracklet.getAttribute().get("gender_code"))).get("female"))).floatValue();
					float male = ((Number)(((Map<String, Object>)(tracklet.getAttribute().get("gender_code"))).get("male"))).floatValue();
					if(female > 0.5)
						obj.setSex("1");
					else if(male > 0.5)
						obj.setSex("0");
					else
						obj.setSex("-1");
				}catch(Exception e) { }
				
				String facePersonId = strangerFaceSeekerFacade.strangerIsHere(obj);
				log.info("[strangerCollector] strangerFaceSeekerFacade facePersonId:{}",facePersonId);

				if(StringUtils.isNotBlank(facePersonId)) {
					faceInfo = IdentifiedInfo.builder()
							.identifiedPersonId(facePersonId)
							.identifiedPersonType(StreamPedestrianUtils.PASSER)
							.identifiedPersonTag(faceTrack.getDevice().getDeviceTag())
							.identifiedPersonAvatar(imagePath)
							.identifiedScore(1.0f)
							.identifiedFeature(obj.getFeature())
							.trackIndex(trackIndex)
							.build();
					
					faceTrack.addIdentifiedInfo(List.of(faceInfo));
					faceTrack.setIdentified(true);
					
					if(bodyTrack != null) {
						List<IdentifiedInfo> bodyInfos = PedestrianTrack.findNearestIdentifiedInfo(bodyTrack);
						if(!CollectionUtils.isEmpty(bodyInfos))
							bodyInfo = bodyInfos.get(0);
					}
				}
			}else if(label == StreamPedestrianUtils.BODY) {
				bodyTrack= track;
				faceTrack= track.getCurrentAssociation();
				
				String imagePath = bodyTrack.targetFrameToPath();
				Tracklet tracklet = bodyTrack.getCurrentTracklet();
				
				StrangerPedestrianObject obj = StrangerPedestrianObject.builder()
						.feature((float[])tracklet.getNormalizeFeature())
						.imagePath(imagePath)
						.trackId(trackId)
						.infra(bodyTrack.getDevice())
						.build();
				
				try {
					Map<String, Object> attribute = tracklet.getAttribute();
					int lower = ((Number)((Map<String, Object>)attribute.get("age_lower_limit")).get("age_lower_limit")).intValue();
					int upper = ((Number)((Map<String, Object>)attribute.get("age_up_limit")).get("age_up_limit")).intValue();
					obj.setAge(String.valueOf((lower + upper) / 2));
					
					float female = ((Number)(((Map<String, Object>)(tracklet.getAttribute().get("gender_code"))).get("female"))).floatValue();
					float male = ((Number)(((Map<String, Object>)(tracklet.getAttribute().get("gender_code"))).get("male"))).floatValue();
					if(female > 0.5)
						obj.setSex("1");
					else if(male > 0.5)
						obj.setSex("0");
					else
						obj.setSex("-1");
				}catch(Exception e){ }
				
				String bodyPersonId = strangerPedestrianSeekerFacade.strangerIsHere(obj);
				log.info("[strangerCollector] strangerPedestrianSeekerFacade bodyPersonId:{}",bodyPersonId);

				if(StringUtils.isNotBlank(bodyPersonId)) {
					bodyInfo = IdentifiedInfo.builder()
							.identifiedPersonId(bodyPersonId)
							.identifiedPersonType(StreamPedestrianUtils.PASSER)
							.identifiedPersonTag(track.getDevice().getDeviceTag())
							.identifiedPersonAvatar(imagePath)
							.identifiedScore(1.0f)
							.trackIndex(trackIndex)
							.identifiedFeature(obj.getFeature())
							.build();

					bodyTrack.addIdentifiedInfo(List.of(bodyInfo));
					bodyTrack.setIdentified(true);
					
					if(faceTrack != null){
						List<IdentifiedInfo> faceInfos = PedestrianTrack.findNearestIdentifiedInfo(faceTrack);
						if(!CollectionUtils.isEmpty(faceInfos))
							faceInfo = faceInfos.get(0);
					}
				}
			}
			
			if(faceInfo != null && bodyInfo != null){
				try {
					facePedestrianClusterRepository.saveAndFlush(FacePedestrianCluster.builder()
							.facePersonId(faceInfo.getIdentifiedPersonId())
							.pedestrianPersonId(bodyInfo.getIdentifiedPersonId())
							.facePersonType(faceInfo.getIdentifiedPersonType())
							.pedestrianPersonType(bodyInfo.getIdentifiedPersonType())
							.build());
				}catch (HibernateException ex) {

				}
				catch(Exception e) {
					//nothing to do, 因为重复了就不管了
				}
			}
		};
	}
	
	/** 渲染流和后台流公用的消费器 用来发送消息*/
	@Bean
	public Consumer<Pair<PedestrianTrack, ComsumerParameter>> pedestrianMessageSender(){
		return pair -> {
			PedestrianTrack track = pair.getLeft();
			int trackIndex = pair.getRight().getTrackIndex();
			
			Tracklet target = track.getCurrentTracklet();
			Integer stage = (Integer)track.getParameters().get("stage");
			if(stage == null || target == null) {
				log.warn("*********danger!!! stage or target can not be null.************");
				return ;
			}
			
			String eventAction;
			Boolean nosend = false;
			if((stage == StreamPedestrianUtils.QUICK_RESPONSE)){
				eventAction = "imageCapture";
				log.warn("********* imageCapture no send ************");
				if(Utils.instance.faceRecognitionMode == 0){
					nosend = true;
				}
			}
			else if(stage == StreamPedestrianUtils.TIME_INTERVAL || stage == StreamPedestrianUtils.HIGH_QUALITY || stage == StreamPedestrianUtils.TIMEOUT){
				eventAction = "intervalCapture";
				log.warn("********* intervalCapture no send ************");
				if(Utils.instance.faceRecognitionMode == 0){
					nosend = true;
				}
			}
			else if(stage == StreamPedestrianUtils.TRACKING_FINISH)
				eventAction = "patternDetected";
			else {
				log.warn("*********danger!!! stage is not correct.************");
				return ;
			}
			long captureTime = target.getCapture_time();
			// 防止同一ms有人脸和人体输出
			if(track.getCurrentTracklet().getLabel() == StreamPedestrianUtils.BODY){
				captureTime = captureTime + 1;
			}
			PedestrianEventBuilder.OsgObject osgObject = PedestrianEventBuilder.OsgObject
					.builder()
					.bucketName(bucketName)
					.osgUrl(osgUrl)
					.timeout(timeout)
					.build();

			Map<String,String> map = PedestrianEventBuilder.buildEvent(PedestrianEventBuilder.Event
					.builder()
					.eventName("senseyePedestrianEvent")
					.eventAction(eventAction)
					.trackIndex(trackIndex)
					.capturedTime(Utils.dateFormat_MS.get().format(new Date(captureTime)))
					.track(track)
					.viperEncKey(viperEncKey)
					.osgObject(osgObject)
					.build());

			if(Utils.instance.pedSaveCluster && !(map.get("facePersonId").equals("-1") || map.get("pedPersonId").equals("-1"))){
				try{
					facePedestrianClusterRepository.saveAndFlush(FacePedestrianCluster.builder()
							.facePersonId(map.get("facePersonId"))
							.pedestrianPersonId(map.get("pedPersonId") )
							.facePersonType(Integer.parseInt(map.get("facePersonType")))
							.pedestrianPersonType(Integer.parseInt(map.get("pedPersonType")))
							.build());
				}catch (HibernateException ex) {

			    }catch (Exception e){

				}
			}
			if(nosend) return;

			if(Utils.instance.kafkaHeaderKeyMode){
				pedestrian_raw_event_output.send(MessageBuilder.withPayload(map.get("message")).setHeader(KafkaHeaders.KEY, track.getDevice().getDeviceId().getBytes()).build());
			}else{
				pedestrian_raw_event_output.send(MessageBuilder.withPayload(map.get("message")).build());
			}

			VideoStreamNonSeen.Monitor monitorMap = VideoStreamNonSeen.getMonitorMap().get(track.getDevice().getDeviceId());
			if(monitorMap !=null) {
				monitorMap.getAndIncrementSended();
			}
		};
	}

	@SuppressWarnings("unchecked")
	public void handleTargetDrop(PedestrianTrack track) {

		String eventAction = "patternDetected";

		Tracklet target = track.getCurrentTracklet();
//		Integer stage = (Integer)track.getParameters().get("stage");
//		if(stage == null || target == null) {
//			log.warn("*********danger!!! stage or target can not be null.************");
//			return ;
//		}

		PedestrianEventBuilder.OsgObject osgObject = PedestrianEventBuilder.OsgObject
				.builder()
				.bucketName(bucketName)
				.osgUrl(osgUrl)
				.timeout(timeout)
				.build();

		Map<String,String> buildEventResult = PedestrianEventBuilder.buildEvent(PedestrianEventBuilder.Event
				.builder()
				.eventName("senseyePedestrianEvent")
				.eventAction(eventAction)
				.trackIndex(track.trackIndex())
				.capturedTime(Utils.dateFormat_MS.get().format(new Date(target.getCapture_time())))
				.track(track)
				.dropFlag("Filter")
				.osgObject(osgObject)
				.viperEncKey(viperEncKey)
				.build());

//		if(	!(map.get("facePersonId").equals("-1") || map.get("pedPersonId").equals("-1"))){
//			try{
//				facePedestrianClusterRepository.saveAndFlush(FacePedestrianCluster.builder()
//						.facePersonId(map.get("facePersonId"))
//						.pedestrianPersonId(map.get("pedPersonId") )
//						.facePersonType(Integer.parseInt(map.get("facePersonType")))
//						.pedestrianPersonType(Integer.parseInt(map.get("pedPersonType")))
//						.build());
//			}catch (HibernateException ex) {
//
//			}catch (Exception e){
//
//			}
//		}

		if(Utils.instance.kafkaHeaderKeyMode){
			pedestrian_raw_event_output.send(MessageBuilder.withPayload(buildEventResult.get("message")).setHeader(KafkaHeaders.KEY, track.getDevice().getDeviceId().getBytes()).build());
		}else{
			pedestrian_raw_event_output.send(MessageBuilder.withPayload(buildEventResult.get("message")).build());
		}

		VideoStreamNonSeen.Monitor monitorMap = VideoStreamNonSeen.getMonitorMap().get(track.getDevice().getDeviceId());
		if(monitorMap !=null) {
			monitorMap.getAndIncrementSended();
		}

	}
	
	/** 渲染流 图标的同步器 当人员被识别后 将人员的图标放注入map */
	@SuppressWarnings("unchecked")
	@Bean
	public Consumer<Pair<PedestrianTrack, ComsumerParameter>> seenIconUpdater(){
		return pair -> {
			PedestrianTrack track = pair.getLeft();
			
			Icon icon = videoIconContainer.findIcon(track);
			Map<Integer, Icon> iconMap = (Map<Integer, Icon>)(track.getParameters().get("seenTrackingIconMap"));
			
			if(icon != null && iconMap != null)
				iconMap.put(track.getEnter().getTrack_id(), icon);
		};
	}
	
	@Builder
	@Getter
	public static class ComsumerParameter{
		private int trackIndex;
		private int stage;
	}
}
