package com.sensetime.intersense.cognitivesvc.pedestrian.handler;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_area2d_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_feature_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler.Detection;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler.Detection.Rect;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

public class UtilsReader{
    
	public static void readHunterData(Pointer target, Detection detection) {
		detection.setImageId((int)KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "image_id")));
		detection.setId((int)KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "id")));
		detection.setConfidence(KestrelApi.keson_get_double(KestrelApi.keson_get_object_item(target, "confidence")));
		
		Pointer roi_keson = KestrelApi.keson_get_object_item(target, "roi");
		kestrel_area2d_t roi = new kestrel_area2d_t();
		KestrelApi.keson_get_ext_data(roi_keson, new PointerByReference(roi.getPointer()));
		roi.read();
		
		detection.setHunter(Rect.builder().left(roi.left).top(roi.top).width(roi.width).height(roi.height).build());
	}
	
	public static float[] readFeatureData(Pointer target) {
		Pointer feature_keson = KestrelApi.keson_get_object_item(target, "feature");
		PointerByReference feature_t = new PointerByReference();
		KestrelApi.keson_get_ext_data(feature_keson, feature_t);
		kestrel_feature_t feature = new kestrel_feature_t(feature_t.getValue());
		feature.read();
		
		float[] result = feature.feature.getFloatArray(0, feature.dims);
		
		double square_sum = 0;
		
		for(int jndex = 0 ; jndex < result.length; jndex ++)
			square_sum += result[jndex] * result[jndex];
		
		square_sum = Math.sqrt(square_sum);
		
		for(int jndex = 0; jndex < result.length; jndex ++) 
			result[jndex] /= square_sum;
		
		return result;
	}
}