package com.sensetime.intersense.cognitivesvc.pedestrian.utils;

import com.sensetime.intersense.cognitivesvc.pedestrian.handler.PedestrianRawEventOutput;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration("pedestrianKafkaConfiguration")
@ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "yes")
@PropertySource("classpath:pedestrian-stream.properties")
public class KafkaConfiguration {

//    public static interface KafkaSender {
//        String FACE_RAW_EVENT_OUTPUT = "pedestrian_raw_event_output";
//
//        @Output(FACE_RAW_EVENT_OUTPUT)
//        MessageChannel pedestrian_raw_event_output();
//    }

    @Bean("pedestrian_raw_event_output")
    public PedestrianRawEventOutput senseyeRawEventOutput(StreamBridge streamBridgeTemplate) {
        return new PedestrianRawEventOutput(streamBridgeTemplate);
    }
}
