package com.sensetime.intersense.cognitivesvc.streampedestrian.zutils;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import com.sensetime.intersense.cognitivesvc.pedestrian.utils.FileUtil;
import com.sensetime.intersense.cognitivesvc.server.entities.FacePedestrianCluster;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.FeatureConversionEncryptionUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.PedestrianTrack.IdentifiedInfo;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.PedestrianValid;
import com.sensetime.intersense.cognitivesvc.streampedestrian.zutils.StreamPedestrianUtils.Tracklet;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
public class PedestrianEventBuilder {

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	@Accessors(chain = true)
	public static class Event{
		private String eventName;
		private String eventAction;
		private PedestrianTrack track;
		private int trackIndex;
		private String capturedTime;
		private String dropFlag;
		private String viperEncKey;
		private OsgObject osgObject;

	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	@Accessors(chain = true)
	public static class OsgObject{
		private String bucketName;
		private String osgUrl;
		private Integer timeout;

	}
	
	public static Map<String,String> buildEvent(Event event) {
		Map<String,String> result = new HashMap<>();
		String faceAttribute = "";
		String bodyAttribute = "";
		String scene = "";
		String rect = "";
		String roiId = "";
		String targets = "";
		String extra = "";
		String hasAssociation ="";
		PedestrianTrack track = event.track;
		Tracklet tracklet = track.getCurrentTracklet();
		Map<String, Object> varibles = new HashMap<String, Object>();

		String facePersonId = "-1";
		String pedPersonId = "-1";
		Integer facePersonType = 0;
		Integer pedPersonType = 0;
		Map<String,String> targetInfo = toTarget(event.getTrackIndex(), tracklet, track);
		targets = targetInfo.get("target");
		if(track.getIdentifiedInfos().size() > 0){
			if(tracklet.getLabel() == StreamPedestrianUtils.BODY){
				pedPersonId = targetInfo.get("personId");
				pedPersonType = Integer.parseInt(targetInfo.get("personType"));
			}else{
				facePersonId = targetInfo.get("personId");
				facePersonType = Integer.parseInt(targetInfo.get("personType"));
			}
		}
		if(MapUtils.isNotEmpty(tracklet.getAttribute())) {
			if(tracklet.getLabel() == StreamPedestrianUtils.FACE && track.getPedestrian().getRunFaceAttrModel() == 0){
				faceAttribute = ",\"faceAttributes\":" + JSONObject.toJSONString(tracklet.getAttribute());

			} else if(tracklet.getLabel() == StreamPedestrianUtils.BODY && track.getPedestrian().getRunPedAttrModel() == 0)
				bodyAttribute = ",\"bodyAttributes\":" + JSONObject.toJSONString(tracklet.getAttribute());
		}
		
		PedestrianTrack associationItem = track.getCurrentAssociation();
		if(associationItem != null) {
			hasAssociation += ", \"hasAssociation\":"  + "\"enterAssociation\"";
			Tracklet associationTracklet = associationItem.getEnter();
			Map<String,String> targetInfoAssociate = toTarget(null, associationTracklet, associationItem);
			targets += "," + targetInfoAssociate.get("target") ;
			if(associationTracklet.getLabel() == StreamPedestrianUtils.BODY){
				pedPersonId = targetInfoAssociate.get("personId");
				pedPersonType = Integer.parseInt(targetInfoAssociate.get("personType"));
			}else{
				facePersonId = targetInfoAssociate.get("personId");
				facePersonType = Integer.parseInt(targetInfoAssociate.get("personType"));
			}
			if(MapUtils.isNotEmpty(associationTracklet.getAttribute())) {
				hasAssociation += ", \"hasAssociationAttrubute\":"  + tracklet.getLabel();
				if(tracklet.getLabel() != StreamPedestrianUtils.FACE && track.getPedestrian().getRunFaceAttrModel() == 0)
					faceAttribute = ",\"faceAttributes\":" + JSONObject.toJSONString(associationTracklet.getAttribute());
				else if(tracklet.getLabel() != StreamPedestrianUtils.BODY && track.getPedestrian().getRunPedAttrModel() == 0)
					bodyAttribute = ",\"bodyAttributes\":" + JSONObject.toJSONString(associationTracklet.getAttribute());
			}
		}

		PedestrianValid valid = (PedestrianValid)(track.getParameters().get("valid"));
		try{
			Map<String, Object> qualityInfo = new HashMap<>();
			qualityInfo.put("quality", tracklet.getQuality());
			qualityInfo.put("integrate_quality", tracklet.getIntegrate_quality());
			qualityInfo.put("yaw",tracklet.getYaw());
			qualityInfo.put("pitch",tracklet.getPitch());
			qualityInfo.put("roll",tracklet.getRoll());
			qualityInfo.put("confidence",tracklet.getConfidence());

			Base64.Encoder encoder = Base64.getEncoder();
			String featureBlob = "";
			if(tracklet.getLabel() == StreamPedestrianUtils.FACE){
				featureBlob = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeader(
						FeatureConversionEncryptionUtils.encodeBlob(FaissSeeker.featureToString(tracklet.getOriginFeature()), event.getViperEncKey())));
			} else if(tracklet.getLabel() == StreamPedestrianUtils.BODY){
				featureBlob = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeaderBody(
						FeatureConversionEncryptionUtils.encodeBlob(FaissSeeker.featureToString(tracklet.getOriginFeature()), event.getViperEncKey())));
			}

			qualityInfo.put("feature", featureBlob);
			qualityInfo.put("featureBlob", featureBlob);
			varibles.put("target", qualityInfo);
			varibles.put("processors", track.getPedestrian().getProcessors());

			if(valid !=null && StringUtils.isNotBlank(event.getDropFlag())) {
				varibles.put("reason", valid.isValidText());
			}
			varibles.put("association", "[" + targets + "]");

			varibles.put("currentFrameTime", track.getFramePts());
			varibles.put("selectFrameEndTime", tracklet.getSelectFrameEndTime());

			extra = ", \"extra\":" + JSONObject.toJSONString(varibles);
		}catch (Exception e){
			e.printStackTrace();
		}

		//String captureImageOsg = track.targetFrameToOsg(event.getOsgObject());
		//String sceneImageOsg = track.sceneFrameToOsg(event.getOsgObject());

		String captureImage = track.targetFrameToPath();
		String sceneImage = track.sceneFrameToPath();

//		if (StringUtils.isBlank(captureImageOsg) || captureImageOsg.equals(FrameUtils.NOIMAGE)) {
//			captureImageOsg = track.targetFrameToOsgDef(captureImage, event.getOsgObject());
//		}
//		if (StringUtils.isBlank(sceneImageOsg) || sceneImageOsg.equals(FrameUtils.NOIMAGE)) {
//			sceneImageOsg = track.sceneFrameToOsgDef(sceneImage, event.getOsgObject());
//		}


		if(StringUtils.isNotBlank(sceneImage))
			scene = ", \"image\":{\"url\":\"" + sceneImage + "\", \"width\":\"" + track.getDevice().getRtspWidth() + "\", \"height\":\"" + track.getDevice().getRtspHeight() + "\", \"frameIndex\":" + track.getFrameIndex() + ", \"framePts\":" + track.getFramePts() + "}";
		
		String infoType = tracklet.getLabel() == 37017 ? "faceInfo" : "bodyInfo";
		
		if(tracklet.getPosition() != null) 
			rect = ", \"detect\" : {\"left\":" + tracklet.getPosition().get("left").intValue() + ", \"top\":" + tracklet.getPosition().get("top").intValue() + ", \"right\":" + (tracklet.getPosition().get("left").intValue() + tracklet.getPosition().get("width").intValue()) + ", \"bottom\":" + (tracklet.getPosition().get("top").intValue() + tracklet.getPosition().get("height").intValue()) + "}";

		try {
			if(valid != null && ArrayUtils.isNotEmpty(valid.getRoiValid()) && StringUtils.isNotBlank(track.getPedestrian().getRoiIds())) {
				List<String> ids = JSON.parseArray(track.getPedestrian().getRoiIds(), String.class);
				roiId = Arrays.stream(valid.getRoiValid()).filter(index -> index >= 0 && index < ids.size()).mapToObj(index -> ids.get(index)).collect(Collectors.joining("\",\""));
				roiId = ",\"roiId\": [\"" + roiId + "\"]";
			}
		}catch(Exception e) {
			e.printStackTrace();
		}
		String message =
				"{" + 
				"	\"eventName\": \"" + event.eventName + "\"," + 
				"	\"eventAction\": \"" + event.eventAction + "\"," + 
				"	\"data\": {" + 
				"		\"camera\": {" + 
				"			\"referId\": \"" + track.getDevice().getDeviceId() + "\"," + 
				"			\"type\": 1," + 
				"			\"tag\": \"" + track.getDevice().getDeviceTag() + "\"" +
				"		}," +
				"		\"dropFlag\": \"" + (StringUtils.isNotBlank(event.getDropFlag()) ? event.getDropFlag() : "") + "\"," +
				"		\"capturedTime\": \"" + event.getCapturedTime() + "\"," +
				"		\"label\": \"" + tracklet.getLabel() + "\"," + 
				"		\"detectTaskInfos\": [{" + 
				"			\"recognisedInfos\": [{" + 
				"				\"similars\": [" + targets + "]" +
				"			}]" + 
				"		}]," + 
				"		\"" + infoType + "\": {" + 
				"			\"image\": {" + 
				"				\"url\": \"" + captureImage + "\"" +
				"			}" + rect +
				"		}" 
				+ scene + faceAttribute + bodyAttribute + roiId + extra + hasAssociation +
				"	}" + 
				"}";
		
		if(Utils.instance.logged) {			
			log.info(
					"\n"
				    + "***********************************************************************************************************\n" 
					+ message.replaceAll("	", "") 
				    + "\n"
				    + "event.captureImage[" + captureImage + "] :[" + new File(captureImage).exists() + "].\n"
				    + "event.sceneImage[" + sceneImage + "]   :[" + new File(sceneImage).exists() + "].\n"
				    + "***********************************************************************************************************\n"
				    );
			log.info("pedTrace facePersonId:{},pedPersonId:{},facePersonType:{},pedPersonType:{}",facePersonId,pedPersonId,facePersonType,pedPersonType);

		}

		result.put("message",message);
		result.put("facePersonId",facePersonId);
		result.put("pedPersonId",pedPersonId);
		result.put("facePersonType",Integer.toString(facePersonType));
		result.put("pedPersonType",Integer.toString(pedPersonType));

		return result;
	}
	
	private static final Map<String,String> toTarget(Integer trackIndex, Tracklet tracklet, PedestrianTrack track) {
		List<IdentifiedInfo> newInfos = trackIndex != null 
				? track.getIdentifiedInfos().stream().filter(info -> info.getTrackIndex() == trackIndex).collect(Collectors.toList())
				: PedestrianTrack.findNearestIdentifiedInfo(track);
		
		if(CollectionUtils.isEmpty(newInfos))
			newInfos = List.of(PedestrianTrack.UNKNOWNINFO);
		
		int realIndex = newInfos.get(0).getTrackIndex();
		String personType ;
		
		if(Objects.equals(newInfos.get(0).getIdentifiedPersonType(), StreamPedestrianUtils.PERSON))
			personType = "Target";
		else if(Objects.equals(newInfos.get(0).getIdentifiedPersonType(), StreamPedestrianUtils.PASSER))
			personType = "Passer";
		else 
			personType = "Stranger";

		String target = newInfos.stream().map(info ->
						"{" +
								"\"trackIndex\": \"" + realIndex + "\"," +
								"\"targetType\": \"" + tracklet.getLabel() + "\"," +
								"\"score\": \"" + info.getIdentifiedScore() + "\"," +
								"\"target\": {" +
								"\"trackid\": \"" + tracklet.getTrack_id() + "\"," +
								"\"personId\": \"" + info.getIdentifiedPersonId() + "\"," +
								"\"personType\": \"" + personType + "\"," +
								"\"url\": \"" + info.getIdentifiedPersonAvatar() + "\"" +
								"}" +
								"}")
				.collect(Collectors.joining(","));

		Map<String,String> map = new HashMap<>();
		map.put("target",target);
		map.put("personId", newInfos.get(0).getIdentifiedPersonId());
		map.put("personType", String.valueOf(newInfos.get(0).getIdentifiedPersonType()));
		return map;
	}
}
