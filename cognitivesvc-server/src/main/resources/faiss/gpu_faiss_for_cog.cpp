#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>
#include <math.h>

#include <faiss/gpu/GpuIndex.h>
#include <faiss/gpu/GpuIndexFlat.h>
#include <faiss/gpu/GpuIndexIVFFlat.h>
#include <faiss/gpu/StandardGpuResources.h>
#include <faiss/gpu/utils/DeviceUtils.h>
#include <faiss/gpu/GpuCloner.h>
#include <faiss/impl/AuxIndexStructures.h>
#include <faiss/Index.h>

extern "C"
{
    int gpu_id = 0;

    typedef struct {
		long  *I = nullptr; ///< 序号
		float *D = nullptr; ///< 得分
	} search_result;

	typedef struct {
		faiss::gpu::GpuIndexFlatIP  *indexIP  = nullptr;
		faiss::gpu::GpuIndexIVFFlat *indexIVF = nullptr;

		faiss::gpu::StandardGpuResources *resource = nullptr;
		faiss::gpu::GpuIndexConfig       *config = nullptr;
	} search_index;

	search_index *create_index_l2(int dimension, int feature_num, float* features){
		search_index *index = new search_index;

		index->resource = new faiss::gpu::StandardGpuResources();
		index->resource->setTempMemory(feature_num * dimension * 4);

		index->config = new faiss::gpu::GpuIndexFlatConfig();
		index->config->device = gpu_id;

		index->indexIP = new faiss::gpu::GpuIndexFlatIP(index->resource, dimension , *((faiss::gpu::GpuIndexFlatConfig*)(index->config)));
		index->indexIP->add(feature_num, features);

		return index;
	}

	search_index *create_index_ivf(int dimension, int feature_num, float* features){
		search_index *index = new search_index;

		index->resource = new faiss::gpu::StandardGpuResources();
		index->resource->setTempMemory(feature_num * dimension * 4);

		index->config = new faiss::gpu::GpuIndexIVFFlatConfig();
		index->config->device = gpu_id;

		index->indexIVF = new faiss::gpu::GpuIndexIVFFlat(index->resource, dimension, (int)sqrt(feature_num), faiss::METRIC_INNER_PRODUCT, *((faiss::gpu::GpuIndexIVFFlatConfig*)(index->config)));

		index->indexIVF->train(feature_num, features);
		index->indexIVF->add(feature_num, features);

		return index;
	}

	search_result * search(search_index* index, int search_num, int feature_num, float* feature){
		long  *I = new long[feature_num * search_num];
		float *D = new float[feature_num * search_num];

		if(index->indexIVF != nullptr){
			index->indexIVF->search(feature_num, feature, search_num, D, I);
		}else if(index->indexIP != nullptr){
			index->indexIP->search(feature_num, feature, search_num, D, I);
		}

		search_result *result = new search_result;
		result->D = D;
		result->I = I;

		return result;
	}

	void add_item(search_index *index, int feature_num, float* feature){
		if(index->indexIVF != nullptr)
			index->indexIVF->add(feature_num, feature);
		else if(index->indexIP != nullptr)
			index->indexIP->add(feature_num, feature);
	}

	void remove_item(search_index *index, int remove_num, long* remove_ids){
		faiss::IDSelectorBatch batch(remove_num, remove_ids);
		faiss::gpu::GpuClonerOptions option;

		if(index->indexIVF != nullptr){
			faiss::Index * cpu_index = faiss::gpu::index_gpu_to_cpu(index->indexIVF);
			cpu_index->remove_ids(batch);

			delete index->indexIVF;
			index->indexIVF = (faiss::gpu::GpuIndexIVFFlat *)faiss::gpu::index_cpu_to_gpu(index->resource, gpu_id, cpu_index, &option);
		}else if(index->indexIP != nullptr){
			faiss::Index * cpu_index = faiss::gpu::index_gpu_to_cpu(index->indexIP);
			cpu_index->remove_ids(batch);

			delete index->indexIP;
			index->indexIP = (faiss::gpu::GpuIndexFlatIP *)faiss::gpu::index_cpu_to_gpu(index->resource, gpu_id, cpu_index, &option);
		}
	}

	void destroy_index(search_index *index){
		if(index->resource != nullptr)
			delete index->resource;

		if(index->config != nullptr)
			delete index->config;

		if(index->indexIP != nullptr)
			delete index->indexIP;

		if(index->indexIVF != nullptr)
			delete index->indexIVF;

		delete index;
	}

	void destroy_result(search_result *result){
		delete[] result->I;
		delete[] result->D;

		delete result;
	}

	void set_gpu_id(int id){
	    std::cout << "***********using GPU faiss***********\n";
		gpu_id = id;
	}

	int get_gpu_id(){
		return gpu_id;
	}
}
