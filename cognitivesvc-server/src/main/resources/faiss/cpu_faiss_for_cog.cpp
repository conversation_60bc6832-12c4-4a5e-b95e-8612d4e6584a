#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>
#include <math.h>

#include <faiss/IndexFlat.h>
#include <faiss/IndexIVFFlat.h>
#include <faiss/impl/AuxIndexStructures.h>

extern "C"
{
    int gpu_id = 0;
    
    typedef struct {
		long  *I = nullptr; // 序号
		float *D = nullptr; // 得分
	} search_result;

	typedef struct {
		faiss::IndexFlatIP  *indexIP  = nullptr;
		faiss::IndexIVFFlat *indexIVF = nullptr;
	} search_index;

	search_index *create_index_l2(int dimension, int feature_num, float* features){
		search_index *index = new search_index;

		index->indexIP = new faiss::IndexFlatIP(dimension);
		index->indexIP->add(feature_num, features);

		return index;
	}

	search_index *create_index_ivf(int dimension, int feature_num, float* features){
		search_index *index = new search_index;

		index->indexIP = new faiss::IndexFlatIP(dimension);
		index->indexIVF  = new faiss::IndexIVFFlat(index->indexIP, dimension, (int)sqrt(feature_num), faiss::METRIC_INNER_PRODUCT);
		index->indexIVF->verbose = true;

		index->indexIVF->train(feature_num, features);
		index->indexIVF->add(feature_num, features);

		return index;
	}

	search_result * search(search_index* index, int search_num, int feature_num, float* feature){
		long  *I = new long[feature_num * search_num];
		float *D = new float[feature_num * search_num];

		if(index->indexIVF != nullptr){
			index->indexIVF->search(feature_num, feature, search_num, D, I);
		}else if(index->indexIP != nullptr){
			index->indexIP->search(feature_num, feature, search_num, D, I);
		}

		search_result *result = new search_result;
		result->D = D;
		result->I = I;

		return result;
	}

	void add_item(search_index *index, int feature_num, float* feature){
		if(index->indexIVF != nullptr)
			index->indexIVF->add(feature_num, feature);
		else if(index->indexIP != nullptr)
			index->indexIP->add(feature_num, feature);
	}

	void remove_item(search_index *index, int remove_num, long* remove_ids){
		faiss::IDSelectorBatch batch(remove_num, remove_ids);

		if(index->indexIVF != nullptr)
			index->indexIVF->remove_ids(batch);
		else if(index->indexIP != nullptr)
			index->indexIP->remove_ids(batch);
	}

	void destroy_index(search_index *index){
		if(index->indexIP != nullptr)
			delete index->indexIP;

		if(index->indexIVF != nullptr)
			delete index->indexIVF;

		delete index;
	}

	void destroy_result(search_result *result){
		delete[] result->I;
		delete[] result->D;

		delete result;
	}

	void set_gpu_id(int id){
		std::cout << "***********using CPU faiss***********\n";
	}

	int get_gpu_id(){
		return -1;
	}
}