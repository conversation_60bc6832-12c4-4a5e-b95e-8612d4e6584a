package com.sensetime.intersense.cognitivesvc.server.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@Entity
@Table(name = "${db.face_pedestrian_cluster.table.name:face_pedestrian_cluster}")
@NoArgsConstructor
@AllArgsConstructor
public class FacePedestrianCluster {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

	@Column(name = "face_person_id")
	private String facePersonId;

	@Column(name = "face_person_type")
	private Integer facePersonType;

	@Column(name = "pedestrian_person_id")
	private String pedestrianPersonId;

	@Column(name = "pedestrian_person_type")
	private Integer pedestrianPersonType;
}
