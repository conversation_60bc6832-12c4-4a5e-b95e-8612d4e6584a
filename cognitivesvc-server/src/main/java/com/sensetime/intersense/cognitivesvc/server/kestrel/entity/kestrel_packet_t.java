package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;

/**
 * <i>native declaration : include/kestrel_packet.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_packet_t extends Structure {
	/** C type : uint8_t* */
	public Pointer data;
	public int size;
	public int stream_id;
	public long pts;
	public long dts;
	public int flags;
	/** C type : Pointer */
	public Pointer buffer;
	public kestrel_packet_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("data", "size", "stream_id", "pts", "dts", "flags", "buffer");
	}
	/**
	 * @param data C type : uint8_t*<br>
	 * @param buffer C type : kestrel_buffer
	 */
	public kestrel_packet_t(Pointer data, int size, int stream_id, long pts, long dts, int flags, Pointer buffer) {
		super();
		this.data = data;
		this.size = size;
		this.stream_id = stream_id;
		this.pts = pts;
		this.dts = dts;
		this.flags = flags;
		this.buffer = buffer;
	}
	public kestrel_packet_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_packet_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_packet_t implements Structure.ByValue {
		
	};
}
