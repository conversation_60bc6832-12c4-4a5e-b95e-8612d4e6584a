package com.sensetime.intersense.cognitivesvc.server.properties;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "vaxtor")
@Setter
@Getter
public class VaxtorConfigProperties {


    /**
     * 是否启用第三方车牌识别
     */
    private Boolean enableVaxtorPlateRecognition;

    /**
     * 第三方ocr数据文件位置
     */
    private String ocrDataPath;
    /**
     * 车牌识别国家
     */
    private List<String> countryList;

    /**
     * 识别processor的数量
     */
    private Integer detectorCount;
    /**
     * 每个processor 用于缓存识别目标的队列长度
     */
    private Integer queueSize;

    /**
     * 车牌识别过程，小图放大比例；数组，如果有多个值，会按先后顺序多次进行放大后检测车牌
     */
    private Double[] scaleFactors = {1.0};

    /**
     * 初始化processor的配置
     */
    private InitializeConfig initConf;

    private MMCConfig mmcConfig;

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InitializeConfig {
        /**
         * OCR operational mode: 1:synchronous, 2:ashynchronous (must provide callback function and multiplate configuration)
         */
        private Long operMode = 1l;

        /**
         * The time in seconds that the OCR waits the recurrence of the same license plate number
         */
        private Long samePlateDelay = 0l;
        /**
         * 最小字符高度
         * Min is 12px max is 70px. Optimal character height is average from 25 to 35px in height
         */
        private Long minCharHeight = 14l;
        /**
         * 最大字符高度
         */
        private Long maxCharHeight = 60l;
        /**
         * OCR algorithm complexity (1:low, 2:normal, 3:high)
         */
        private Long ocrComplexity = 1l;
    }


    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MMCConfig {
        /**
         * * analytic_type:
         * *   分析类型,可选值1-3
         * *   1 - 仅分析车辆品牌
         * *   2 - 分析车辆品牌和型号
         * *   3 - 分析车辆品牌、型号和颜色
         */
        private Long analyticType = 3l;        // 分析类型:品牌+型号+颜色

        /**
         * * analytics_quality:
         * *   分析质量,可选值1-3
         * *   1 - 低质量(快速)
         * *   2 - 中等质量
         * *   3 - 高质量(较慢)
         */
        private Long analyticsQuality = 1l;     // 分析质量:低(快速)

        /**
         * * min_global_confidence:
         * *   最小全局置信度,范围0-100
         * *   建议值:20-30
         */
        private Long min_globalConfidence = 20l;// 最小全局置信度

    }

}
