package com.sensetime.intersense.cognitivesvc.server.vaxtor.jna;

import com.sun.jna.NativeLong;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : header/vaxtor_types.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class tEventPlateInfo extends Structure {
    /**
     * ID of the OCR instance that reported the plate number<br>
     * C type : type_id_ocr
     */
    public NativeLong _ocr_id;
    /**
     * Source image timestamp. It can be either provide by the user or interanally by the OCR<br>
     * C type : vx_int64
     */
    public long _time_stamp_frame;
    /**
     * Relative time counter provided by user, required for instant speed calculation<br>
     * C type : vx_int64
     */
    public long _time_counter;
    /**
     * Plate results, (see tPlateInfo)<br>
     * C type : tPlateInfo
     */
    public tPlateInfo _plate_info;
    /**
     * Extra analytics<br>
     * C type : tAnalytics
     */
    public tAnalytics _analytics;
    /**
     * Source (input) image as provided to the OCR, this will be NULL if the source image reporting is disabled<br>
     * C type : tImageInfo
     */
    public tImageInfo _source_image;
    public tEventPlateInfo() {
        super();
    }
    protected List<String> getFieldOrder() {
        return Arrays.asList("_ocr_id", "_time_stamp_frame", "_time_counter", "_plate_info", "_analytics", "_source_image");
    }
    /**
     * @param _ocr_id ID of the OCR instance that reported the plate number<br>
     * C type : type_id_ocr<br>
     * @param _time_stamp_frame Source image timestamp. It can be either provide by the user or interanally by the OCR<br>
     * C type : vx_int64<br>
     * @param _time_counter Relative time counter provided by user, required for instant speed calculation<br>
     * C type : vx_int64<br>
     * @param _plate_info Plate results, (see tPlateInfo)<br>
     * C type : tPlateInfo<br>
     * @param _analytics Extra analytics<br>
     * C type : tAnalytics<br>
     * @param _source_image Source (input) image as provided to the OCR, this will be NULL if the source image reporting is disabled<br>
     * C type : tImageInfo
     */
    public tEventPlateInfo(NativeLong _ocr_id, long _time_stamp_frame, long _time_counter, tPlateInfo _plate_info, tAnalytics _analytics, tImageInfo _source_image) {
        super();
        this._ocr_id = _ocr_id;
        this._time_stamp_frame = _time_stamp_frame;
        this._time_counter = _time_counter;
        this._plate_info = _plate_info;
        this._analytics = _analytics;
        this._source_image = _source_image;
    }
    public tEventPlateInfo(Pointer peer) {
        super(peer);
    }
    public static class ByReference extends tEventPlateInfo implements Structure.ByReference {

    };
    public static class ByValue extends tEventPlateInfo implements Structure.ByValue {

    };
}
