package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;

/**
 * <i>native declaration : include/kestrel_feature.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_feature_t extends Structure {
	public int version;
	public int dims;
	/** C type : float* */
	public Pointer feature;
	/** C type : Pointer */
	public Pointer buffer;
	public kestrel_feature_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("version", "dims", "feature", "buffer");
	}
	/**
	 * @param feature C type : float*<br>
	 * @param buffer C type : kestrel_buffer
	 */
	public kestrel_feature_t(int version, int dims, Pointer feature, Pointer buffer) {
		super();
		this.version = version;
		this.dims = dims;
		this.feature = feature;
		this.buffer = buffer;
	}
	public kestrel_feature_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_feature_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_feature_t implements Structure.ByValue {
		
	};
}
