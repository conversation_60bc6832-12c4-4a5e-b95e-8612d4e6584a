package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * JNA Wrapper for library <b>kestrel_codec</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_codecLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_codecLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_codecLibrary INSTANCE = (Kestrel_codecLibrary)Native.load(Kestrel_codecLibrary.JNA_LIBRARY_NAME, Kestrel_codecLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_codec.h</i><br>
	 * enum values
	 */
	public static interface kestrel_media_type_e {
		/** <i>native declaration : include/kestrel_codec.h:18</i> */
		public static final int KESTREL_MEDIA_TYPE_UNKNOWN = -1;
		/** <i>native declaration : include/kestrel_codec.h:19</i> */
		public static final int KESTREL_MEDIA_TYPE_VIDEO = 0;
		/** <i>native declaration : include/kestrel_codec.h:20</i> */
		public static final int KESTREL_MEDIA_TYPE_AUDIO = 1;
	};
	/**
	 * <i>native declaration : include/kestrel_codec.h</i><br>
	 * enum values
	 */
	public static interface kestrel_codec_id_e {
		/** <i>native declaration : include/kestrel_codec.h:27</i> */
		public static final int KESTREL_CODEC_ID_NONE = 0;
		/** <i>native declaration : include/kestrel_codec.h:30</i> */
		public static final int KESTREL_CODEC_ID_MPEG1VIDEO = 1;
		/**
		 * < preferred ID for MPEG-1/2 video decoding<br>
		 * <i>native declaration : include/kestrel_codec.h:31</i>
		 */
		public static final int KESTREL_CODEC_ID_MPEG2VIDEO = 2;
		/** <i>native declaration : include/kestrel_codec.h:32</i> */
		public static final int KESTREL_CODEC_ID_H261 = 3;
		/** <i>native declaration : include/kestrel_codec.h:33</i> */
		public static final int KESTREL_CODEC_ID_H263 = 4;
		/** <i>native declaration : include/kestrel_codec.h:34</i> */
		public static final int KESTREL_CODEC_ID_RV10 = 5;
		/** <i>native declaration : include/kestrel_codec.h:35</i> */
		public static final int KESTREL_CODEC_ID_RV20 = 6;
		/** <i>native declaration : include/kestrel_codec.h:36</i> */
		public static final int KESTREL_CODEC_ID_MJPEG = 7;
		/** <i>native declaration : include/kestrel_codec.h:37</i> */
		public static final int KESTREL_CODEC_ID_MJPEGB = 8;
		/** <i>native declaration : include/kestrel_codec.h:38</i> */
		public static final int KESTREL_CODEC_ID_LJPEG = 9;
		/** <i>native declaration : include/kestrel_codec.h:39</i> */
		public static final int KESTREL_CODEC_ID_SP5X = 10;
		/** <i>native declaration : include/kestrel_codec.h:40</i> */
		public static final int KESTREL_CODEC_ID_JPEGLS = 11;
		/** <i>native declaration : include/kestrel_codec.h:41</i> */
		public static final int KESTREL_CODEC_ID_MPEG4 = 12;
		/** <i>native declaration : include/kestrel_codec.h:42</i> */
		public static final int KESTREL_CODEC_ID_RAWVIDEO = 13;
		/** <i>native declaration : include/kestrel_codec.h:43</i> */
		public static final int KESTREL_CODEC_ID_MSMPEG4V1 = 14;
		/** <i>native declaration : include/kestrel_codec.h:44</i> */
		public static final int KESTREL_CODEC_ID_MSMPEG4V2 = 15;
		/** <i>native declaration : include/kestrel_codec.h:45</i> */
		public static final int KESTREL_CODEC_ID_MSMPEG4V3 = 16;
		/** <i>native declaration : include/kestrel_codec.h:46</i> */
		public static final int KESTREL_CODEC_ID_WMV1 = 17;
		/** <i>native declaration : include/kestrel_codec.h:47</i> */
		public static final int KESTREL_CODEC_ID_WMV2 = 18;
		/** <i>native declaration : include/kestrel_codec.h:48</i> */
		public static final int KESTREL_CODEC_ID_H263P = 19;
		/** <i>native declaration : include/kestrel_codec.h:49</i> */
		public static final int KESTREL_CODEC_ID_H263I = 20;
		/** <i>native declaration : include/kestrel_codec.h:50</i> */
		public static final int KESTREL_CODEC_ID_FLV1 = 21;
		/** <i>native declaration : include/kestrel_codec.h:51</i> */
		public static final int KESTREL_CODEC_ID_SVQ1 = 22;
		/** <i>native declaration : include/kestrel_codec.h:52</i> */
		public static final int KESTREL_CODEC_ID_SVQ3 = 23;
		/** <i>native declaration : include/kestrel_codec.h:53</i> */
		public static final int KESTREL_CODEC_ID_DVVIDEO = 24;
		/** <i>native declaration : include/kestrel_codec.h:54</i> */
		public static final int KESTREL_CODEC_ID_HUFFYUV = 25;
		/** <i>native declaration : include/kestrel_codec.h:55</i> */
		public static final int KESTREL_CODEC_ID_CYUV = 26;
		/** <i>native declaration : include/kestrel_codec.h:56</i> */
		public static final int KESTREL_CODEC_ID_H264 = 27;
		/** <i>native declaration : include/kestrel_codec.h:57</i> */
		public static final int KESTREL_CODEC_ID_INDEO3 = 28;
		/** <i>native declaration : include/kestrel_codec.h:58</i> */
		public static final int KESTREL_CODEC_ID_VP3 = 29;
		/** <i>native declaration : include/kestrel_codec.h:59</i> */
		public static final int KESTREL_CODEC_ID_THEORA = 30;
		/** <i>native declaration : include/kestrel_codec.h:60</i> */
		public static final int KESTREL_CODEC_ID_ASV1 = 31;
		/** <i>native declaration : include/kestrel_codec.h:61</i> */
		public static final int KESTREL_CODEC_ID_ASV2 = 32;
		/** <i>native declaration : include/kestrel_codec.h:62</i> */
		public static final int KESTREL_CODEC_ID_FFV1 = 33;
		/** <i>native declaration : include/kestrel_codec.h:63</i> */
		public static final int KESTREL_CODEC_ID_4XM = 34;
		/** <i>native declaration : include/kestrel_codec.h:64</i> */
		public static final int KESTREL_CODEC_ID_VCR1 = 35;
		/** <i>native declaration : include/kestrel_codec.h:65</i> */
		public static final int KESTREL_CODEC_ID_CLJR = 36;
		/** <i>native declaration : include/kestrel_codec.h:66</i> */
		public static final int KESTREL_CODEC_ID_MDEC = 37;
		/** <i>native declaration : include/kestrel_codec.h:67</i> */
		public static final int KESTREL_CODEC_ID_ROQ = 38;
		/** <i>native declaration : include/kestrel_codec.h:68</i> */
		public static final int KESTREL_CODEC_ID_INTERPLAY_VIDEO = 39;
		/** <i>native declaration : include/kestrel_codec.h:69</i> */
		public static final int KESTREL_CODEC_ID_XAN_WC3 = 40;
		/** <i>native declaration : include/kestrel_codec.h:70</i> */
		public static final int KESTREL_CODEC_ID_XAN_WC4 = 41;
		/** <i>native declaration : include/kestrel_codec.h:71</i> */
		public static final int KESTREL_CODEC_ID_RPZA = 42;
		/** <i>native declaration : include/kestrel_codec.h:72</i> */
		public static final int KESTREL_CODEC_ID_CINEPAK = 43;
		/** <i>native declaration : include/kestrel_codec.h:73</i> */
		public static final int KESTREL_CODEC_ID_WS_VQA = 44;
		/** <i>native declaration : include/kestrel_codec.h:74</i> */
		public static final int KESTREL_CODEC_ID_MSRLE = 45;
		/** <i>native declaration : include/kestrel_codec.h:75</i> */
		public static final int KESTREL_CODEC_ID_MSVIDEO1 = 46;
		/** <i>native declaration : include/kestrel_codec.h:76</i> */
		public static final int KESTREL_CODEC_ID_IDCIN = 47;
		/** <i>native declaration : include/kestrel_codec.h:77</i> */
		public static final int KESTREL_CODEC_ID_8BPS = 48;
		/** <i>native declaration : include/kestrel_codec.h:78</i> */
		public static final int KESTREL_CODEC_ID_SMC = 49;
		/** <i>native declaration : include/kestrel_codec.h:79</i> */
		public static final int KESTREL_CODEC_ID_FLIC = 50;
		/** <i>native declaration : include/kestrel_codec.h:80</i> */
		public static final int KESTREL_CODEC_ID_TRUEMOTION1 = 51;
		/** <i>native declaration : include/kestrel_codec.h:81</i> */
		public static final int KESTREL_CODEC_ID_VMDVIDEO = 52;
		/** <i>native declaration : include/kestrel_codec.h:82</i> */
		public static final int KESTREL_CODEC_ID_MSZH = 53;
		/** <i>native declaration : include/kestrel_codec.h:83</i> */
		public static final int KESTREL_CODEC_ID_ZLIB = 54;
		/** <i>native declaration : include/kestrel_codec.h:84</i> */
		public static final int KESTREL_CODEC_ID_QTRLE = 55;
		/** <i>native declaration : include/kestrel_codec.h:85</i> */
		public static final int KESTREL_CODEC_ID_TSCC = 56;
		/** <i>native declaration : include/kestrel_codec.h:86</i> */
		public static final int KESTREL_CODEC_ID_ULTI = 57;
		/** <i>native declaration : include/kestrel_codec.h:87</i> */
		public static final int KESTREL_CODEC_ID_QDRAW = 58;
		/** <i>native declaration : include/kestrel_codec.h:88</i> */
		public static final int KESTREL_CODEC_ID_VIXL = 59;
		/** <i>native declaration : include/kestrel_codec.h:89</i> */
		public static final int KESTREL_CODEC_ID_QPEG = 60;
		/** <i>native declaration : include/kestrel_codec.h:90</i> */
		public static final int KESTREL_CODEC_ID_PNG = 61;
		/** <i>native declaration : include/kestrel_codec.h:91</i> */
		public static final int KESTREL_CODEC_ID_PPM = 62;
		/** <i>native declaration : include/kestrel_codec.h:92</i> */
		public static final int KESTREL_CODEC_ID_PBM = 63;
		/** <i>native declaration : include/kestrel_codec.h:93</i> */
		public static final int KESTREL_CODEC_ID_PGM = 64;
		/** <i>native declaration : include/kestrel_codec.h:94</i> */
		public static final int KESTREL_CODEC_ID_PGMYUV = 65;
		/** <i>native declaration : include/kestrel_codec.h:95</i> */
		public static final int KESTREL_CODEC_ID_PAM = 66;
		/** <i>native declaration : include/kestrel_codec.h:96</i> */
		public static final int KESTREL_CODEC_ID_FFVHUFF = 67;
		/** <i>native declaration : include/kestrel_codec.h:97</i> */
		public static final int KESTREL_CODEC_ID_RV30 = 68;
		/** <i>native declaration : include/kestrel_codec.h:98</i> */
		public static final int KESTREL_CODEC_ID_RV40 = 69;
		/** <i>native declaration : include/kestrel_codec.h:99</i> */
		public static final int KESTREL_CODEC_ID_VC1 = 70;
		/** <i>native declaration : include/kestrel_codec.h:100</i> */
		public static final int KESTREL_CODEC_ID_WMV3 = 71;
		/** <i>native declaration : include/kestrel_codec.h:101</i> */
		public static final int KESTREL_CODEC_ID_LOCO = 72;
		/** <i>native declaration : include/kestrel_codec.h:102</i> */
		public static final int KESTREL_CODEC_ID_WNV1 = 73;
		/** <i>native declaration : include/kestrel_codec.h:103</i> */
		public static final int KESTREL_CODEC_ID_AASC = 74;
		/** <i>native declaration : include/kestrel_codec.h:104</i> */
		public static final int KESTREL_CODEC_ID_INDEO2 = 75;
		/** <i>native declaration : include/kestrel_codec.h:105</i> */
		public static final int KESTREL_CODEC_ID_FRAPS = 76;
		/** <i>native declaration : include/kestrel_codec.h:106</i> */
		public static final int KESTREL_CODEC_ID_TRUEMOTION2 = 77;
		/** <i>native declaration : include/kestrel_codec.h:107</i> */
		public static final int KESTREL_CODEC_ID_BMP = 78;
		/** <i>native declaration : include/kestrel_codec.h:108</i> */
		public static final int KESTREL_CODEC_ID_CSCD = 79;
		/** <i>native declaration : include/kestrel_codec.h:109</i> */
		public static final int KESTREL_CODEC_ID_MMVIDEO = 80;
		/** <i>native declaration : include/kestrel_codec.h:110</i> */
		public static final int KESTREL_CODEC_ID_ZMBV = 81;
		/** <i>native declaration : include/kestrel_codec.h:111</i> */
		public static final int KESTREL_CODEC_ID_AVS = 82;
		/** <i>native declaration : include/kestrel_codec.h:112</i> */
		public static final int KESTREL_CODEC_ID_SMACKVIDEO = 83;
		/** <i>native declaration : include/kestrel_codec.h:113</i> */
		public static final int KESTREL_CODEC_ID_NUV = 84;
		/** <i>native declaration : include/kestrel_codec.h:114</i> */
		public static final int KESTREL_CODEC_ID_KMVC = 85;
		/** <i>native declaration : include/kestrel_codec.h:115</i> */
		public static final int KESTREL_CODEC_ID_FLASHSV = 86;
		/** <i>native declaration : include/kestrel_codec.h:116</i> */
		public static final int KESTREL_CODEC_ID_CAVS = 87;
		/** <i>native declaration : include/kestrel_codec.h:117</i> */
		public static final int KESTREL_CODEC_ID_JPEG2000 = 88;
		/** <i>native declaration : include/kestrel_codec.h:118</i> */
		public static final int KESTREL_CODEC_ID_VMNC = 89;
		/** <i>native declaration : include/kestrel_codec.h:119</i> */
		public static final int KESTREL_CODEC_ID_VP5 = 90;
		/** <i>native declaration : include/kestrel_codec.h:120</i> */
		public static final int KESTREL_CODEC_ID_VP6 = 91;
		/** <i>native declaration : include/kestrel_codec.h:121</i> */
		public static final int KESTREL_CODEC_ID_VP6F = 92;
		/** <i>native declaration : include/kestrel_codec.h:122</i> */
		public static final int KESTREL_CODEC_ID_TARGA = 93;
		/** <i>native declaration : include/kestrel_codec.h:123</i> */
		public static final int KESTREL_CODEC_ID_DSICINVIDEO = 94;
		/** <i>native declaration : include/kestrel_codec.h:124</i> */
		public static final int KESTREL_CODEC_ID_TIERTEXSEQVIDEO = 95;
		/** <i>native declaration : include/kestrel_codec.h:125</i> */
		public static final int KESTREL_CODEC_ID_TIFF = 96;
		/** <i>native declaration : include/kestrel_codec.h:126</i> */
		public static final int KESTREL_CODEC_ID_GIF = 97;
		/** <i>native declaration : include/kestrel_codec.h:127</i> */
		public static final int KESTREL_CODEC_ID_DXA = 98;
		/** <i>native declaration : include/kestrel_codec.h:128</i> */
		public static final int KESTREL_CODEC_ID_DNXHD = 99;
		/** <i>native declaration : include/kestrel_codec.h:129</i> */
		public static final int KESTREL_CODEC_ID_THP = 100;
		/** <i>native declaration : include/kestrel_codec.h:130</i> */
		public static final int KESTREL_CODEC_ID_SGI = 101;
		/** <i>native declaration : include/kestrel_codec.h:131</i> */
		public static final int KESTREL_CODEC_ID_C93 = 102;
		/** <i>native declaration : include/kestrel_codec.h:132</i> */
		public static final int KESTREL_CODEC_ID_BETHSOFTVID = 103;
		/** <i>native declaration : include/kestrel_codec.h:133</i> */
		public static final int KESTREL_CODEC_ID_PTX = 104;
		/** <i>native declaration : include/kestrel_codec.h:134</i> */
		public static final int KESTREL_CODEC_ID_TXD = 105;
		/** <i>native declaration : include/kestrel_codec.h:135</i> */
		public static final int KESTREL_CODEC_ID_VP6A = 106;
		/** <i>native declaration : include/kestrel_codec.h:136</i> */
		public static final int KESTREL_CODEC_ID_AMV = 107;
		/** <i>native declaration : include/kestrel_codec.h:137</i> */
		public static final int KESTREL_CODEC_ID_VB = 108;
		/** <i>native declaration : include/kestrel_codec.h:138</i> */
		public static final int KESTREL_CODEC_ID_PCX = 109;
		/** <i>native declaration : include/kestrel_codec.h:139</i> */
		public static final int KESTREL_CODEC_ID_SUNRAST = 110;
		/** <i>native declaration : include/kestrel_codec.h:140</i> */
		public static final int KESTREL_CODEC_ID_INDEO4 = 111;
		/** <i>native declaration : include/kestrel_codec.h:141</i> */
		public static final int KESTREL_CODEC_ID_INDEO5 = 112;
		/** <i>native declaration : include/kestrel_codec.h:142</i> */
		public static final int KESTREL_CODEC_ID_MIMIC = 113;
		/** <i>native declaration : include/kestrel_codec.h:143</i> */
		public static final int KESTREL_CODEC_ID_RL2 = 114;
		/** <i>native declaration : include/kestrel_codec.h:144</i> */
		public static final int KESTREL_CODEC_ID_ESCAPE124 = 115;
		/** <i>native declaration : include/kestrel_codec.h:145</i> */
		public static final int KESTREL_CODEC_ID_DIRAC = 116;
		/** <i>native declaration : include/kestrel_codec.h:146</i> */
		public static final int KESTREL_CODEC_ID_BFI = 117;
		/** <i>native declaration : include/kestrel_codec.h:147</i> */
		public static final int KESTREL_CODEC_ID_CMV = 118;
		/** <i>native declaration : include/kestrel_codec.h:148</i> */
		public static final int KESTREL_CODEC_ID_MOTIONPIXELS = 119;
		/** <i>native declaration : include/kestrel_codec.h:149</i> */
		public static final int KESTREL_CODEC_ID_TGV = 120;
		/** <i>native declaration : include/kestrel_codec.h:150</i> */
		public static final int KESTREL_CODEC_ID_TGQ = 121;
		/** <i>native declaration : include/kestrel_codec.h:151</i> */
		public static final int KESTREL_CODEC_ID_TQI = 122;
		/** <i>native declaration : include/kestrel_codec.h:152</i> */
		public static final int KESTREL_CODEC_ID_AURA = 123;
		/** <i>native declaration : include/kestrel_codec.h:153</i> */
		public static final int KESTREL_CODEC_ID_AURA2 = 124;
		/** <i>native declaration : include/kestrel_codec.h:154</i> */
		public static final int KESTREL_CODEC_ID_V210X = 125;
		/** <i>native declaration : include/kestrel_codec.h:155</i> */
		public static final int KESTREL_CODEC_ID_TMV = 126;
		/** <i>native declaration : include/kestrel_codec.h:156</i> */
		public static final int KESTREL_CODEC_ID_V210 = 127;
		/** <i>native declaration : include/kestrel_codec.h:157</i> */
		public static final int KESTREL_CODEC_ID_DPX = 128;
		/** <i>native declaration : include/kestrel_codec.h:158</i> */
		public static final int KESTREL_CODEC_ID_MAD = 129;
		/** <i>native declaration : include/kestrel_codec.h:159</i> */
		public static final int KESTREL_CODEC_ID_FRWU = 130;
		/** <i>native declaration : include/kestrel_codec.h:160</i> */
		public static final int KESTREL_CODEC_ID_FLASHSV2 = 131;
		/** <i>native declaration : include/kestrel_codec.h:161</i> */
		public static final int KESTREL_CODEC_ID_CDGRAPHICS = 132;
		/** <i>native declaration : include/kestrel_codec.h:162</i> */
		public static final int KESTREL_CODEC_ID_R210 = 133;
		/** <i>native declaration : include/kestrel_codec.h:163</i> */
		public static final int KESTREL_CODEC_ID_ANM = 134;
		/** <i>native declaration : include/kestrel_codec.h:164</i> */
		public static final int KESTREL_CODEC_ID_BINKVIDEO = 135;
		/** <i>native declaration : include/kestrel_codec.h:165</i> */
		public static final int KESTREL_CODEC_ID_IFF_ILBM = 136;
		/** <i>native declaration : include/kestrel_codec.h:167</i> */
		public static final int KESTREL_CODEC_ID_KGV1 = 137;
		/** <i>native declaration : include/kestrel_codec.h:168</i> */
		public static final int KESTREL_CODEC_ID_YOP = 138;
		/** <i>native declaration : include/kestrel_codec.h:169</i> */
		public static final int KESTREL_CODEC_ID_VP8 = 139;
		/** <i>native declaration : include/kestrel_codec.h:170</i> */
		public static final int KESTREL_CODEC_ID_PICTOR = 140;
		/** <i>native declaration : include/kestrel_codec.h:171</i> */
		public static final int KESTREL_CODEC_ID_ANSI = 141;
		/** <i>native declaration : include/kestrel_codec.h:172</i> */
		public static final int KESTREL_CODEC_ID_A64_MULTI = 142;
		/** <i>native declaration : include/kestrel_codec.h:173</i> */
		public static final int KESTREL_CODEC_ID_A64_MULTI5 = 143;
		/** <i>native declaration : include/kestrel_codec.h:174</i> */
		public static final int KESTREL_CODEC_ID_R10K = 144;
		/** <i>native declaration : include/kestrel_codec.h:175</i> */
		public static final int KESTREL_CODEC_ID_MXPEG = 145;
		/** <i>native declaration : include/kestrel_codec.h:176</i> */
		public static final int KESTREL_CODEC_ID_LAGARITH = 146;
		/** <i>native declaration : include/kestrel_codec.h:177</i> */
		public static final int KESTREL_CODEC_ID_PRORES = 147;
		/** <i>native declaration : include/kestrel_codec.h:178</i> */
		public static final int KESTREL_CODEC_ID_JV = 148;
		/** <i>native declaration : include/kestrel_codec.h:179</i> */
		public static final int KESTREL_CODEC_ID_DFA = 149;
		/** <i>native declaration : include/kestrel_codec.h:180</i> */
		public static final int KESTREL_CODEC_ID_WMV3IMAGE = 150;
		/** <i>native declaration : include/kestrel_codec.h:181</i> */
		public static final int KESTREL_CODEC_ID_VC1IMAGE = 151;
		/** <i>native declaration : include/kestrel_codec.h:182</i> */
		public static final int KESTREL_CODEC_ID_UTVIDEO = 152;
		/** <i>native declaration : include/kestrel_codec.h:183</i> */
		public static final int KESTREL_CODEC_ID_BMV_VIDEO = 153;
		/** <i>native declaration : include/kestrel_codec.h:184</i> */
		public static final int KESTREL_CODEC_ID_VBLE = 154;
		/** <i>native declaration : include/kestrel_codec.h:185</i> */
		public static final int KESTREL_CODEC_ID_DXTORY = 155;
		/** <i>native declaration : include/kestrel_codec.h:186</i> */
		public static final int KESTREL_CODEC_ID_V410 = 156;
		/** <i>native declaration : include/kestrel_codec.h:187</i> */
		public static final int KESTREL_CODEC_ID_XWD = 157;
		/** <i>native declaration : include/kestrel_codec.h:188</i> */
		public static final int KESTREL_CODEC_ID_CDXL = 158;
		/** <i>native declaration : include/kestrel_codec.h:189</i> */
		public static final int KESTREL_CODEC_ID_XBM = 159;
		/** <i>native declaration : include/kestrel_codec.h:190</i> */
		public static final int KESTREL_CODEC_ID_ZEROCODEC = 160;
		/** <i>native declaration : include/kestrel_codec.h:191</i> */
		public static final int KESTREL_CODEC_ID_MSS1 = 161;
		/** <i>native declaration : include/kestrel_codec.h:192</i> */
		public static final int KESTREL_CODEC_ID_MSA1 = 162;
		/** <i>native declaration : include/kestrel_codec.h:193</i> */
		public static final int KESTREL_CODEC_ID_TSCC2 = 163;
		/** <i>native declaration : include/kestrel_codec.h:194</i> */
		public static final int KESTREL_CODEC_ID_MTS2 = 164;
		/** <i>native declaration : include/kestrel_codec.h:195</i> */
		public static final int KESTREL_CODEC_ID_CLLC = 165;
		/** <i>native declaration : include/kestrel_codec.h:196</i> */
		public static final int KESTREL_CODEC_ID_MSS2 = 166;
		/** <i>native declaration : include/kestrel_codec.h:197</i> */
		public static final int KESTREL_CODEC_ID_VP9 = 167;
		/** <i>native declaration : include/kestrel_codec.h:198</i> */
		public static final int KESTREL_CODEC_ID_AIC = 168;
		/** <i>native declaration : include/kestrel_codec.h:199</i> */
		public static final int KESTREL_CODEC_ID_ESCAPE130 = 169;
		/** <i>native declaration : include/kestrel_codec.h:200</i> */
		public static final int KESTREL_CODEC_ID_G2M = 170;
		/** <i>native declaration : include/kestrel_codec.h:201</i> */
		public static final int KESTREL_CODEC_ID_WEBP = 171;
		/** <i>native declaration : include/kestrel_codec.h:202</i> */
		public static final int KESTREL_CODEC_ID_HNM4_VIDEO = 172;
		/** <i>native declaration : include/kestrel_codec.h:203</i> */
		public static final int KESTREL_CODEC_ID_HEVC = 173;
		/** <i>native declaration : include/kestrel_codec.h:205</i> */
		public static final int KESTREL_CODEC_ID_FIC = 174;
		/** <i>native declaration : include/kestrel_codec.h:206</i> */
		public static final int KESTREL_CODEC_ID_ALIAS_PIX = 175;
		/** <i>native declaration : include/kestrel_codec.h:207</i> */
		public static final int KESTREL_CODEC_ID_BRENDER_PIX = 176;
		/** <i>native declaration : include/kestrel_codec.h:208</i> */
		public static final int KESTREL_CODEC_ID_PAF_VIDEO = 177;
		/** <i>native declaration : include/kestrel_codec.h:209</i> */
		public static final int KESTREL_CODEC_ID_EXR = 178;
		/** <i>native declaration : include/kestrel_codec.h:210</i> */
		public static final int KESTREL_CODEC_ID_VP7 = 179;
		/** <i>native declaration : include/kestrel_codec.h:211</i> */
		public static final int KESTREL_CODEC_ID_SANM = 180;
		/** <i>native declaration : include/kestrel_codec.h:212</i> */
		public static final int KESTREL_CODEC_ID_SGIRLE = 181;
		/** <i>native declaration : include/kestrel_codec.h:213</i> */
		public static final int KESTREL_CODEC_ID_MVC1 = 182;
		/** <i>native declaration : include/kestrel_codec.h:214</i> */
		public static final int KESTREL_CODEC_ID_MVC2 = 183;
		/** <i>native declaration : include/kestrel_codec.h:215</i> */
		public static final int KESTREL_CODEC_ID_HQX = 184;
		/** <i>native declaration : include/kestrel_codec.h:216</i> */
		public static final int KESTREL_CODEC_ID_TDSC = 185;
		/** <i>native declaration : include/kestrel_codec.h:217</i> */
		public static final int KESTREL_CODEC_ID_HQ_HQA = 186;
		/** <i>native declaration : include/kestrel_codec.h:218</i> */
		public static final int KESTREL_CODEC_ID_HAP = 187;
		/** <i>native declaration : include/kestrel_codec.h:219</i> */
		public static final int KESTREL_CODEC_ID_DDS = 188;
		/** <i>native declaration : include/kestrel_codec.h:220</i> */
		public static final int KESTREL_CODEC_ID_DXV = 189;
		/** <i>native declaration : include/kestrel_codec.h:221</i> */
		public static final int KESTREL_CODEC_ID_SCREENPRESSO = 190;
		/** <i>native declaration : include/kestrel_codec.h:222</i> */
		public static final int KESTREL_CODEC_ID_RSCC = 191;
		/** <i>native declaration : include/kestrel_codec.h:223</i> */
		public static final int KESTREL_CODEC_ID_AVS2 = 192;
		/** <i>native declaration : include/kestrel_codec.h:225</i> */
		public static final int KESTREL_CODEC_ID_Y41P = 0x8000;
		/** <i>native declaration : include/kestrel_codec.h:226</i> */
		public static final int KESTREL_CODEC_ID_AVRP = (0x8000 + 1);
		/** <i>native declaration : include/kestrel_codec.h:227</i> */
		public static final int KESTREL_CODEC_ID_012V = (0x8000 + 2);
		/** <i>native declaration : include/kestrel_codec.h:228</i> */
		public static final int KESTREL_CODEC_ID_AVUI = (0x8000 + 3);
		/** <i>native declaration : include/kestrel_codec.h:229</i> */
		public static final int KESTREL_CODEC_ID_AYUV = (0x8000 + 4);
		/** <i>native declaration : include/kestrel_codec.h:230</i> */
		public static final int KESTREL_CODEC_ID_TARGA_Y216 = (0x8000 + 5);
		/** <i>native declaration : include/kestrel_codec.h:231</i> */
		public static final int KESTREL_CODEC_ID_V308 = (0x8000 + 6);
		/** <i>native declaration : include/kestrel_codec.h:232</i> */
		public static final int KESTREL_CODEC_ID_V408 = (0x8000 + 7);
		/** <i>native declaration : include/kestrel_codec.h:233</i> */
		public static final int KESTREL_CODEC_ID_YUV4 = (0x8000 + 8);
		/** <i>native declaration : include/kestrel_codec.h:234</i> */
		public static final int KESTREL_CODEC_ID_AVRN = (0x8000 + 9);
		/** <i>native declaration : include/kestrel_codec.h:235</i> */
		public static final int KESTREL_CODEC_ID_CPIA = (0x8000 + 10);
		/** <i>native declaration : include/kestrel_codec.h:236</i> */
		public static final int KESTREL_CODEC_ID_XFACE = (0x8000 + 11);
		/** <i>native declaration : include/kestrel_codec.h:237</i> */
		public static final int KESTREL_CODEC_ID_SNOW = (0x8000 + 12);
		/** <i>native declaration : include/kestrel_codec.h:238</i> */
		public static final int KESTREL_CODEC_ID_SMVJPEG = (0x8000 + 13);
		/** <i>native declaration : include/kestrel_codec.h:239</i> */
		public static final int KESTREL_CODEC_ID_APNG = (0x8000 + 14);
		/** <i>native declaration : include/kestrel_codec.h:240</i> */
		public static final int KESTREL_CODEC_ID_DAALA = (0x8000 + 15);
		/** <i>native declaration : include/kestrel_codec.h:241</i> */
		public static final int KESTREL_CODEC_ID_CFHD = (0x8000 + 16);
		/** <i>native declaration : include/kestrel_codec.h:242</i> */
		public static final int KESTREL_CODEC_ID_TRUEMOTION2RT = (0x8000 + 17);
		/** <i>native declaration : include/kestrel_codec.h:243</i> */
		public static final int KESTREL_CODEC_ID_M101 = (0x8000 + 18);
		/** <i>native declaration : include/kestrel_codec.h:244</i> */
		public static final int KESTREL_CODEC_ID_MAGICYUV = (0x8000 + 19);
		/** <i>native declaration : include/kestrel_codec.h:245</i> */
		public static final int KESTREL_CODEC_ID_SHEERVIDEO = (0x8000 + 20);
		/** <i>native declaration : include/kestrel_codec.h:246</i> */
		public static final int KESTREL_CODEC_ID_YLC = (0x8000 + 21);
		/** <i>native declaration : include/kestrel_codec.h:247</i> */
		public static final int KESTREL_CODEC_ID_PSD = (0x8000 + 22);
		/** <i>native declaration : include/kestrel_codec.h:248</i> */
		public static final int KESTREL_CODEC_ID_PIXLET = (0x8000 + 23);
		/** <i>native declaration : include/kestrel_codec.h:249</i> */
		public static final int KESTREL_CODEC_ID_SPEEDHQ = (0x8000 + 24);
		/** <i>native declaration : include/kestrel_codec.h:250</i> */
		public static final int KESTREL_CODEC_ID_FMVC = (0x8000 + 25);
		/** <i>native declaration : include/kestrel_codec.h:251</i> */
		public static final int KESTREL_CODEC_ID_SCPR = (0x8000 + 26);
		/** <i>native declaration : include/kestrel_codec.h:252</i> */
		public static final int KESTREL_CODEC_ID_CLEARVIDEO = (0x8000 + 27);
		/** <i>native declaration : include/kestrel_codec.h:253</i> */
		public static final int KESTREL_CODEC_ID_XPM = (0x8000 + 28);
		/** <i>native declaration : include/kestrel_codec.h:254</i> */
		public static final int KESTREL_CODEC_ID_AV1 = (0x8000 + 29);
		/** <i>native declaration : include/kestrel_codec.h:255</i> */
		public static final int KESTREL_CODEC_ID_BITPACKED = (0x8000 + 30);
		/** <i>native declaration : include/kestrel_codec.h:256</i> */
		public static final int KESTREL_CODEC_ID_MSCC = (0x8000 + 31);
		/** <i>native declaration : include/kestrel_codec.h:257</i> */
		public static final int KESTREL_CODEC_ID_SRGC = (0x8000 + 32);
		/** <i>native declaration : include/kestrel_codec.h:258</i> */
		public static final int KESTREL_CODEC_ID_SVG = (0x8000 + 33);
		/** <i>native declaration : include/kestrel_codec.h:259</i> */
		public static final int KESTREL_CODEC_ID_GDV = (0x8000 + 34);
		/** <i>native declaration : include/kestrel_codec.h:260</i> */
		public static final int KESTREL_CODEC_ID_FITS = (0x8000 + 35);
		/** <i>native declaration : include/kestrel_codec.h:261</i> */
		public static final int KESTREL_CODEC_ID_IMM4 = (0x8000 + 36);
		/** <i>native declaration : include/kestrel_codec.h:262</i> */
		public static final int KESTREL_CODEC_ID_PROSUMER = (0x8000 + 37);
		/** <i>native declaration : include/kestrel_codec.h:263</i> */
		public static final int KESTREL_CODEC_ID_MWSC = (0x8000 + 38);
		/** <i>native declaration : include/kestrel_codec.h:264</i> */
		public static final int KESTREL_CODEC_ID_WCMV = (0x8000 + 39);
		/** <i>native declaration : include/kestrel_codec.h:265</i> */
		public static final int KESTREL_CODEC_ID_RASC = (0x8000 + 40);
	};
}
