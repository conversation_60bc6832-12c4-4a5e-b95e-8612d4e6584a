package com.sensetime.intersense.cognitivesvc.server.utils;

import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_packet_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;

//import java.util.List;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.LinkedBlockingQueue;

@Slf4j
public class NvJpgEncoders {

	private static Pointer imagesharp_annotator;
	//private static final LinkedBlockingQueue<BatchItem> encodingQueue = new LinkedBlockingQueue<BatchItem>(Initializer.batchSize + 8);

	public static byte[] encodeFrame(Pointer frame, String filePath) {
		initialize();

		//BatchItem item = new BatchItem(frame);
		try {
//			encodingQueue.put(item);
//			item.getLatch().await();
			Pointer[] packets = encodeBatchFrame(frame);

			Pointer packet = packets[0];
			if (packet == null)
				return new byte[0];

			kestrel_packet_t packet_t = null;
			try {
				packet_t = new kestrel_packet_t(KestrelApi.keson_get_ext_data(packet).getValue());
				packet_t.read();
				//return new byte[0];
				if (packet_t.size > 0) {
					byte[] arr = packet_t.data.getByteArray(0, packet_t.size);
					packet_t = null;
					if (arr != null && arr.length > 0) {
						FileOutputStream output = new FileOutputStream(filePath);
						output.write(arr);
						output.close();
					}
					return new byte[0];
				} else {
					packet_t = null;
					return new byte[0];
				}
			} finally {
				KesonUtils.kesonDeepDelete(packets);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return new byte[0];


	}

	public static void encodeFrame2(Pointer frame, String filePath) {
		initialize();
		try {
			Pointer[] packets = encodeBatchFrame(frame);
			if (packets[0] == null) {
				return;
			}
			try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
				 BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath))) {
				Pointer packet = packets[0];
				kestrel_packet_t packet_t = null;
				try {
					packet_t = new kestrel_packet_t(KestrelApi.keson_get_ext_data(packet).getValue());
					packet_t.read();
					if (packet_t.size > 0) {
						byte[] arr = packet_t.data.getByteArray(0, packet_t.size);
						packet_t = null;
						if (arr != null && arr.length > 0) {
							baos.write(arr);
						}
					}
				} finally {
					KesonUtils.kesonDeepDelete(packet);
					if (packet_t != null) packet_t.clear();
				}
				baos.writeTo(bos);
				baos.flush();
				bos.flush();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static Pointer[] encodeBatchFrame(Pointer... frames) {
		Initializer.bindDeviceOrNot();

		//long now = 0;
//		if (Utils.instance.watchFrameTiktokLevel == -567)
//			now = System.currentTimeMillis();

		Pointer[] rgbFrames = new Pointer[frames.length];
		for (int index = 0; index < frames.length; index++)
			rgbFrames[index] = FrameUtils.ref_or_cvtcolor_buffered_frame(frames[index], KestrelApi.KESTREL_VIDEO_BGR);

		Pointer input = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_object(input, "id", KestrelApi.keson_create_int(0));

		Pointer images_array = KestrelApi.keson_create_array();
		KestrelApi.keson_add_item_to_object(input, "images", images_array);

		for (int index = 0; index < rgbFrames.length; index++) {
			Pointer image = KestrelApi.keson_create_object();
			KestrelApi.keson_add_item_to_array(images_array, image);

			KestrelApi.keson_add_item_to_object(image, "id", KestrelApi.keson_create_int(index));
			KestrelApi.keson_add_item_to_object(image, "image", KestrelApi.keson_create_ext_frame(rgbFrames[index]));
		}

		Pointer[] result = new Pointer[rgbFrames.length];
		PointerByReference out = new PointerByReference();
		synchronized (imagesharp_annotator) {
		KestrelApi.kestrel_annotator_process(imagesharp_annotator, input, out);
		}

//		if (Utils.instance.watchFrameTiktokLevel == -567)
//			log.info("nvencoder encode count:[" + frames.length + "] costTime:[" + (System.currentTimeMillis() - now) + "]");

		Pointer packetItems = KestrelApi.keson_get_object_item(out.getValue(), "packets");
		int size = KestrelApi.keson_array_size(packetItems);
		for (int index = 0; index < size; index++) {
			Pointer packetItem = KestrelApi.keson_get_array_item(packetItems, index);
			int id = (int) KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(packetItem, "id"));
			result[id] = KestrelApi.keson_detach_item_from_object(packetItem, "packet");
		}

		KesonUtils.kesonDeepDelete(input);
		KesonUtils.kesonDeepDelete(out);
		FrameUtils.batch_free_frame(rgbFrames);
		//KesonUtils.kesonDeepDelete(packetItems);
		//KesonUtils.kesonDeepDelete(result);
		return result;
	}

//	private static Thread encodeThread = new Thread(Utils.cogGroup, () -> {
//		Initializer.bindDeviceOrNot();
//
//		while (true) {
//			try {
//				drainBatchFrame();
//			} catch (Exception e) {
//				try {
//					Thread.sleep(100);
//				} catch (InterruptedException e1) {
//				}
//				e.printStackTrace();
//			}
//		}
//	});

//	private static void drainBatchFrame() {
//		List<BatchItem> handlingList = Utils.drainFromQueue(encodingQueue, Initializer.batchSize, 2, 5);
//		if (handlingList.isEmpty()) {
//			try {
//				Thread.sleep(40);
//			} catch (InterruptedException e) {
//			}
//			return;
//		}
//
//		Pointer[] frames = new Pointer[handlingList.size()];
//		for (int index = 0; index < handlingList.size(); index++)
//			frames[index] = handlingList.get(index).getFrame();
//
//		Pointer[] packets = encodeBatchFrame(frames);
//
//		for (int index = 0; index < handlingList.size(); index++) {
//			BatchItem item = handlingList.get(index);
//			item.setPacket_keson(packets[index]);
//			item.getLatch().countDown();
//		}
//	}

	private static void initialize() {
		if (imagesharp_annotator != null)
			return;

		synchronized (NvJpgEncoder.class) {
			if (imagesharp_annotator != null)
				return;

			Initializer.bindDeviceOrNot();
			String config = "{\"type\":\"encode\", \"quality\":" + 75 + "}";
			log.info("loading model [imagesharp], config is " + config);
			imagesharp_annotator = KestrelApi.kestrel_annotator_open("imagesharp", config);
		}

//		encodeThread.setDaemon(true);
//		encodeThread.setPriority(Thread.NORM_PRIORITY + 2);
//		encodeThread.setName("Nvidia-JPEG-Encoder");
//		encodeThread.start();
	}

//	@Data
//	private static class BatchItem {
//		private Pointer frame;
//		private CountDownLatch latch;
//		private Pointer packet_keson;
//
//		public BatchItem(Pointer frame) {
//			this.frame = frame;
//			this.latch = new CountDownLatch(1);
//		}
//	}
}