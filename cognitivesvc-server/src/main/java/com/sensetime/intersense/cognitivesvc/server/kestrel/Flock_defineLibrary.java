package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * JNA Wrapper for library <b>flock_define</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Flock_defineLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "flock";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Flock_defineLibrary.JNA_LIBRARY_NAME);
	public static final Flock_defineLibrary INSTANCE = (Flock_defineLibrary)Native.load(Flock_defineLibrary.JNA_LIBRARY_NAME, Flock_defineLibrary.class);
	/**
	 * <i>native declaration : include/flock_define.h</i><br>
	 * enum values
	 */
	public static interface flock_err_code_e {
		public static final int CV_E_FLOCK_ERROR = -15728641; // Flock内部错误（-15728641）
		public static final int CV_E_PIPELINE_UNINITIALIZED = CV_E_FLOCK_ERROR + 1; // Pipeline未初始化（-15728640）
		public static final int CV_E_STREAM_UNINITIALIZED = CV_E_PIPELINE_UNINITIALIZED + 1; // 处理流未初始化（-15728639）
		public static final int CV_E_PIPELINE_NOT_START = CV_E_STREAM_UNINITIALIZED + 1; // Pipeline未启动（-15728638）
		public static final int CV_E_STREAM_NOT_OPEN = CV_E_PIPELINE_NOT_START + 1; // 处理流未打开（-15728637）
		public static final int CV_E_IO_NOT_READY = CV_E_STREAM_NOT_OPEN + 1; // 输入输出未就绪（-15728636）

	};
	/**
	 * <i>native declaration : include/flock_define.h</i><br>
	 * enum values
	 */
	public static interface flock_ctrl_cmd_e {
		/** <i>native declaration : include/flock_define.h:20</i> */
		public static final int FLOCK_CTRL_CMD_UNKNOWN = -1;
		/** <i>native declaration : include/flock_define.h:22</i> */
		public static final int FLOCK_CTRL_CMD_NOTICE = 0;
		/** <i>native declaration : include/flock_define.h:23</i> */
		public static final int FLOCK_CTRL_CMD_ADD_SOURCE = 1;
		/** <i>native declaration : include/flock_define.h:24</i> */
		public static final int FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG = 2;
		/** <i>native declaration : include/flock_define.h:25</i> */
		public static final int FLOCK_CTRL_CMD_RESET_SOURCE = 3;
		/** <i>native declaration : include/flock_define.h:26</i> */
		public static final int FLOCK_CTRL_CMD_REMOVE_SOURCE = 4;
		/** <i>native declaration : include/flock_define.h:27</i> */
		public static final int FLOCK_CTRL_CMD_CHECK_SOURCE_EXISTENCE = 5;
		/** <i>native declaration : include/flock_define.h:29</i> */
		public static final int FLOCK_CTRL_CMD_COUNT = 6;
	};
	/** <i>native declaration : include/flock_define.h</i> */
	public static final int CV_E_FLOCK_LIB = (int)0x7f0;
}
