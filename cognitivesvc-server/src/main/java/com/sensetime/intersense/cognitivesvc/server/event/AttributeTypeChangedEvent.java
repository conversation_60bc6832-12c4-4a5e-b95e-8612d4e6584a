package com.sensetime.intersense.cognitivesvc.server.event;

import org.springframework.context.ApplicationEvent;

public class AttributeTypeChangedEvent extends ApplicationEvent {
	
	private static final long serialVersionUID = 1471716663850066957L;
	
	private int newType;
	
	private int oldType;
	
	public AttributeTypeChangedEvent(int newType, int oldType) {
		super(Thread.currentThread());
		
		this.newType = newType;
		this.oldType = oldType;
	}

	public int getNewType() {
		return newType;
	}

	public int getOldType() {
		return oldType;
	}

}
