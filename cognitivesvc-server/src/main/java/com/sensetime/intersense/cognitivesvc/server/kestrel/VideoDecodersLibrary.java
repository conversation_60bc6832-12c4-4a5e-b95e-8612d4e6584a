package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;

/**
 * JNA Wrapper for library <b>OpencvDecoderLibrary</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */

public interface VideoDecodersLibrary extends Library {

    public static final String JNA_LIBRARY_NAME = "video_decoders";

    public static final VideoDecodersLibrary INSTANCE = (VideoDecodersLibrary) Native.load(VideoDecodersLibrary.JNA_LIBRARY_NAME, VideoDecodersLibrary.class);
    /**
     * @param  frame pointer <br>
     * @return the pointer of opencv decoder<br>
     * Original signature : <code>byteEncodeFrame(bool)</code><br>
     * <i>native declaration : frame_encoder.h:20</i>
     * @brief Create a Opencv Decoder object<br>
     * <br>
     */

    Pointer PullFrame(String rtsp);

}
