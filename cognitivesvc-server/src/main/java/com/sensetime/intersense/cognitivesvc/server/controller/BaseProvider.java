package com.sensetime.intersense.cognitivesvc.server.controller;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.context.WebApplicationContext;

@Scope(scopeName = WebApplicationContext.SCOPE_REQUEST)
public class BaseProvider {
    protected Gson gson = new Gson();

    /**
     * 功能说明:
     * 取值范围:
     * 依赖属性:
     * 最后修改时间:
     */
    @Autowired
    protected Environment env;

    /**
     * 功能说明:
     * 取值范围:
     * 依赖属性:
     * 最后修改时间:
     */
    protected HttpHeaders httpHeaders= new HttpHeaders();

    public BaseProvider() {
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
    }


    protected String RESP_OK_CODE = "0000";
    protected String RESP_OK_MSG = "success";
    protected String RESP_SERVER_ERROR_CODE = "9900";
    protected String RESP_SERVER_ERROR_MSG = "server error";
    protected String RESP_PE_RANGE_ERROR_CODE = "4001";
    protected String RESP_PE_THRESHOLD_RANGE = "threshold must be double between 0.0 and 1.0";
    protected String RESP_BE_NO_FEATURE_CODE = "0001";
    protected String RESP_BE_NO_FEATURE_MSG = "no feature";
    
    protected String CREATE_MODIFY_USER = "cognitivesvc";
    
    protected Integer STS_VALID = 0;
    protected Integer STS_INVALID = 1;
    protected Integer STS_DELETED = 3;
    
    /**
     * 功能说明:
     * 最后修改时间:
     * @return
     */
    public JsonObject getRespJson() {
        JsonObject respJson = new JsonObject();
        respJson.addProperty("code", RESP_OK_CODE);
        respJson.addProperty("msg", RESP_OK_MSG);
        respJson.add("data", new JsonObject());
        return respJson;
    }
}
