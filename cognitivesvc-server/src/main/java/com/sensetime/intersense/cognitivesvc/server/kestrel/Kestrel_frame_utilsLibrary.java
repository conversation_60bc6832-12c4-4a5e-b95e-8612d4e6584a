package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_area2d_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_frame_transform_param_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_pixel_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_point2df_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_size2d_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.FloatByReference;
import com.sun.jna.ptr.PointerByReference;
import java.nio.ByteBuffer;
import java.nio.FloatBuffer;

/**
 * JNA Wrapper for library <b>kestrel_frame_utils</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/">Olivier Chafik</a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_frame_utilsLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_aux";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_frame_utilsLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_frame_utilsLibrary INSTANCE = (Kestrel_frame_utilsLibrary)Native.load(Kestrel_frame_utilsLibrary.JNA_LIBRARY_NAME, Kestrel_frame_utilsLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_frame_utils.h</i><br>
	 * enum values
	 */
	public static interface kestrel_flip_type_e {
		/** <i>native declaration : include/kestrel_frame_utils.h:280</i> */
		public static final int KESTREL_FLIP_BOTH = -1;
		/** <i>native declaration : include/kestrel_frame_utils.h:281</i> */
		public static final int KESTREL_FLIP_VERTICAL = 0;
		/** <i>native declaration : include/kestrel_frame_utils.h:282</i> */
		public static final int KESTREL_FLIP_HORIZONTAL = 1;
	};
	/**
	 * @note Support JP[E]G/BMP/PNG.<br>
	 * Original signature : <code>Pointer kestrel_frame_load(const char*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:20</i>
	 */
	Pointer kestrel_frame_load(String filename);
	/**
	 * @note Support JP[E]G/BMP/PNG.<br>
	 * Original signature : <code>Pointer kestrel_frame_load_from_memory(uint8_t*, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:30</i>
	 */
	Pointer kestrel_frame_load_from_memory(ByteBuffer buf, int len);
	/**
	 * @note Support JP[E]G/BMP/PNG.<br>
	 * Original signature : <code>int kestrel_frame_save(kestrel_frame, const char*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:40</i>
	 */
	int kestrel_frame_save(Pointer in, String filename);
	/**
	 * @note Support encode JP[E]G/BMP/PNG.<br>
	 * Original signature : <code>int kestrel_frame_encode(kestrel_frame, kestrel_frame_enc_format_e, kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:51</i>
	 */
	int kestrel_frame_encode(Pointer in, int enc, Pointer buffer);
	/**
	 * @brief Crop frame ROI to output frame<br>
	 * @param[in] in Input frame going to crop<br>
	 * @param[out] out Target frame, If `*out` is NULL, a new frame will be return, else<br>
	 * `*out` should has enough capacity for output frame, `*out` must release by kestrel_frame_free()<br>
	 * @param[in] area Area of ROI, if area is out of frame, fill the outer with black pixels<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support RGB/BGR/NV12/NV21/YU12/GRAY<br>
	 * Original signature : <code>int kestrel_frame_crop(kestrel_frame, kestrel_frame*, kestrel_area2d_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:63</i>
	 */
	int kestrel_frame_crop(Pointer in, PointerByReference out, kestrel_area2d_t.ByValue area);
	/**
	 * @brief batch crop frame ROI to output frame<br>
	 * @param[in] in Input frames going to crop, an array of (kestrel_frame), must have the same<br>
	 * size/fmt/...<br>
	 * @param[in] batch batch number<br>
	 * @param[out] out Target frames, an array of (kestrel_frame). Array size = batch number.<br>
	 * Either allocate all output frames or set all array element to null, in which case this api will<br>
	 * allocate all the output frames, user need to free them<br>
	 * @param[in] area pinter to array of kestrel_area2d_t, array size = batch number<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note currently only support cambricon<br>
	 * Original signature : <code>int kestrel_frame_crop_batch(const kestrel_frame*, size_t, kestrel_frame*, const kestrel_area2d_t*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:78</i>
	 */
	int kestrel_frame_crop_batch(PointerByReference in, long batch, PointerByReference out, kestrel_area2d_t area);
	/**
	 * @brief Resize frame according to `size`<br>
	 * @param[in] in Input frame going to resize<br>
	 * @param[out] out Target frame, If `*out` is NULL, a new frame will be return, else<br>
	 * `*out` should has enough capacity for output frame, `*out` must release by kestrel_frame_free()<br>
	 * @param[in] size Destination size(width, height)<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support RGB/BGR/NV12/NV21/YU12/GRAY<br>
	 * Original signature : <code>int kestrel_frame_resize(kestrel_frame, kestrel_frame*, kestrel_size2d_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:91</i>
	 */
	int kestrel_frame_resize(Pointer in, PointerByReference out, kestrel_size2d_t.ByValue size);
	/**
	 * @brief Scale frame according to `scale`<br>
	 * @param[in] in Input frame going to scale<br>
	 * @param[out] out Target frame, If `*out` is NULL, a new frame will be return, else<br>
	 * `*out` should has enough capacity for output frame, `*out` must release by kestrel_frame_free()<br>
	 * @param[in] scale Scale ratio in float<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support RGB/BGR/NV12/NV21/YU12/GRAY<br>
	 * Original signature : <code>int kestrel_frame_scale(kestrel_frame, kestrel_frame*, float)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:103</i>
	 */
	int kestrel_frame_scale(Pointer in, PointerByReference out, float scale);
	/**
	 * @brief Rotate frame according given kestrel_orientation_e<br>
	 * @param[in] in Input frame going to rotate<br>
	 * @param[out] out Target frame, If `*out` is NULL, a new frame will be return, else<br>
	 * `*out` should has enough capacity for output frame, `*out` must release by kestrel_frame_free()<br>
	 * @param[in] type Ratate orientation<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support RGB/BGR/NV12/NV21/YU12/GRAY<br>
	 * Original signature : <code>int kestrel_frame_rotate(kestrel_frame, kestrel_frame*, kestrel_orientation_e)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:115</i>
	 */
	int kestrel_frame_rotate(Pointer in, PointerByReference out, int type);
	/**
	 * @brief Color space convert according given kestrel_video_format_e<br>
	 * @param[in] in Input frame going to convert<br>
	 * @param[out] out Target frame, If `*out` is NULL, a new frame will be return, else<br>
	 * `*out` should has the same size as input frame, `*out` must release by kestrel_frame_free()<br>
	 * @param[in] type Color space convert type<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support RGB/BGR/NV12/NV21/YU12/GRAY<br>
	 * Original signature : <code>int kestrel_frame_cvt_color(kestrel_frame, kestrel_frame*, kestrel_video_format_e)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:128</i>
	 */
	int kestrel_frame_cvt_color(Pointer in, PointerByReference out, int type);
	/**
	 * @brief Get similarity transform matrix<br>
	 * @param[in] src Source points<br>
	 * @param[in] dst Destination points<br>
	 * @param[out] mat Output similarity transform matrix<br>
	 * @param[out] inverse_mat Output inverse similarity transform matrix<br>
	 * Original signature : <code>void kestrel_find_similarity_transform(const kestrel_point2df_t[3], const kestrel_point2df_t[3], float[3][3], float[3][3])</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:138</i>
	 */
	void kestrel_find_similarity_transform(kestrel_point2df_t src[], kestrel_point2df_t dst[], FloatByReference mat, FloatByReference inverse_mat);
	/**
	 * @brief Get warp affine transform matrix<br>
	 * @param[in] src Source points<br>
	 * @param[in] dst Destination points<br>
	 * @param[out] mat Output warp affine transform matrix<br>
	 * @param[out] inverse_mat Output inverse warp affine transform matrix<br>
	 * Original signature : <code>void kestrel_find_affine_transform(const kestrel_point2df_t[3], const kestrel_point2df_t[3], float[3][3], float[3][3])</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:150</i>
	 */
	void kestrel_find_affine_transform(kestrel_point2df_t src[], kestrel_point2df_t dst[], FloatByReference mat, FloatByReference inverse_mat);
	/**
	 * @brief Get rigid transform matrix<br>
	 * @param[in] src Source points<br>
	 * @param[in] dst Destination points<br>
	 * @param[in] num Points num<br>
	 * @param[out] mat Output rigid transform matrix<br>
	 * Original signature : <code>void kestrel_find_rigid_transform(const kestrel_point2df_t[], const kestrel_point2df_t[], int32_t, float[3][3])</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:161</i>
	 */
	void kestrel_find_rigid_transform(kestrel_point2df_t src[], kestrel_point2df_t dst[], int num, FloatByReference mat);
	/**
	 * @brief Calculates an affine matrix of 2D rotation.<br>
	 * @param[in] angle Rotation angle in degrees. Positive values mean clockwise  (the coordinate<br>
	 * origin is assumed to be the top-left corner)<br>
	 * @param[in] scale Isotropic scale factor.<br>
	 * @param[in] cx x of the rotation in the source image.<br>
	 * @param[in] cy y of the rotation in the source image.<br>
	 * @param[out] mat Output affine matrix<br>
	 * Original signature : <code>void kestrel_get_rotation_matrix2D(float, float, float, float, float[2][3])</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:174</i>
	 */
	void kestrel_get_rotation_matrix2D(float angle, float scale, float cx, float cy, FloatBuffer mat);
	/**
	 * @brief Applies an affine transformation to an image.<br>
	 * @param[in] in Input frame going to transform<br>
	 * @param[out] out pointer to target kestrel_frame, created by user, make sure `*out` has<br>
	 * enough capacity for output frame<br>
	 * @param[in] mat \f$2 \times 3\f$ transformation matrix<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support RGB/BGR/NV12/NV21/YU12/GRAY<br>
	 * Original signature : <code>int kestrel_frame_warpaffine_with_border(kestrel_frame, kestrel_frame, float[2][3], uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:186</i>
	 */
	int kestrel_frame_warpaffine_with_border(Pointer in, Pointer out, FloatBuffer mat, byte border_value);
	/**
	 * Original signature : <code>int kestrel_frame_warpaffine(kestrel_frame, kestrel_frame, float[2][3])</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:188</i>
	 */
	int kestrel_frame_warpaffine(Pointer in, Pointer out, FloatBuffer mat);
	/**
	 * @brief Applies an perspective transformation to an image.<br>
	 * @param[in] in Input frame going to transform<br>
	 * @param[out] out pointer to target kestrel_frame, created by user, make sure `*out` has enough<br>
	 * capacity for output frame<br>
	 * @param[in] mat \f$3 \times 3\f$ transformation matrix<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support RGB/BGR/NV12/NV21/YU12/GRAY<br>
	 * Original signature : <code>int kestrel_frame_warpperspective_with_border(kestrel_frame, kestrel_frame, float[3][3], uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:200</i>
	 */
	int kestrel_frame_warpperspective_with_border(Pointer in, Pointer out, FloatBuffer mat, byte border_value);
	/**
	 * Original signature : <code>int kestrel_frame_warpperspective(kestrel_frame, kestrel_frame, float[3][3])</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:203</i>
	 */
	int kestrel_frame_warpperspective(Pointer in, Pointer out, FloatBuffer mat);
	/**
	 * @brief Equalizes the histogram of a grayscale image.<br>
	 * @param[in] src Input frame going to equalize hist<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be return, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must release by kestrel_frame_free()<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support GRAY<br>
	 * Original signature : <code>int kestrel_frame_equalize_hist(kestrel_frame, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:214</i>
	 */
	int kestrel_frame_equalize_hist(Pointer src, PointerByReference dst);
	/**
	 * @brief Perform gamma correction on an image.<br>
	 * @param[in] src Input frame going to perform gamma correction<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be return, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must release by kestrel_frame_free()<br>
	 * @param[in] gamma Non negative real number, gamma larger than 1 make the shadows darker, while<br>
	 * gamma smaller than 1 make dark regions lighter.<br>
	 * @param[in] gain The constant multiplier.<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Support GRAY<br>
	 * Original signature : <code>int kestrel_frame_adjust_gamma(kestrel_frame, kestrel_frame*, float, float)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:228</i>
	 */
	int kestrel_frame_adjust_gamma(Pointer src, PointerByReference dst, float gamma, float gain);
	/**
	 * @brief Adjust brightness<br>
	 * @param[in] src Input frame<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be return, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must release by kestrel_frame_free()<br>
	 * @param[in] factor How much to adjust the brightness. Can be any non negative number.<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports BGR/RGB<br>
	 * Original signature : <code>int kestrel_frame_adjust_brightness(kestrel_frame, kestrel_frame*, float)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:240</i>
	 */
	int kestrel_frame_adjust_brightness(Pointer src, PointerByReference dst, float factor);
	/**
	 * @brief Adjust contrast<br>
	 * @param[in] src Input frame<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be return, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must release by kestrel_frame_free()<br>
	 * @param[in] factor How much to adjust the contrast. Can be any non negative number.<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports BGR/RGB<br>
	 * Original signature : <code>int kestrel_frame_adjust_contrast(kestrel_frame, kestrel_frame*, float)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:252</i>
	 */
	int kestrel_frame_adjust_contrast(Pointer src, PointerByReference dst, float factor);
	/**
	 * @brief Adjust hue<br>
	 * @param[in] src Input frame<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be return, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must release by kestrel_frame_free()<br>
	 * @param[in] factor How much to adjust hue. Must be in the intervel [-0.5, 0.5].<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports BGR/RGB<br>
	 * Original signature : <code>int kestrel_frame_adjust_hue(kestrel_frame, kestrel_frame*, float)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:264</i>
	 */
	int kestrel_frame_adjust_hue(Pointer src, PointerByReference dst, float factor);
	/**
	 * @brief Adjust saturation<br>
	 * @param[in] src Input frame<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be return, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must release by kestrel_frame_free()<br>
	 * @param[in] factor How much to adjust the saturation. Can be any non negative number.<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports BGR/RGB<br>
	 * Original signature : <code>int kestrel_frame_adjust_saturation(kestrel_frame, kestrel_frame*, float)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:276</i>
	 */
	int kestrel_frame_adjust_saturation(Pointer src, PointerByReference dst, float factor);
	/**
	 * @brief Flip an image.<br>
	 * @param[in] src Input frame<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be return, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must release by kestrel_frame_free()<br>
	 * @param[in] flip_type, flip type<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_flip(kestrel_frame, kestrel_frame*, kestrel_flip_type_e)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:294</i>
	 */
	int kestrel_frame_flip(Pointer src, PointerByReference dst, int flip_type);
	/**
	 * @brief Pad an image.<br>
	 * @param[in] src Input frame<br>
	 * @param[out] dst Target frame, created by user, `*dst` should have enough capacity for output<br>
	 * frame<br>
	 * @param[in] border_type supports KESTREL_BORDER_CONSTANT/REPLICATE/REFLECT/REFLECT101<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_pad(kestrel_frame, kestrel_frame, kestrel_border_type_e, uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:306</i>
	 */
	int kestrel_frame_pad(Pointer src, Pointer dst, int border_type, byte border_value);
	/**
	 * @brief Dilates an frame by using a specific structuring element<br>
	 * @param[in] src input frame<br>
	 * @param[in] kernel_size size of structuring element<br>
	 * @param[in] kernel_data data of structuring element, in row major order<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be returned, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must be release by<br>
	 * kestrel_frame_free()<br>
	 * @param[in] border_type only support KESTREL_BORDER_CONSTANT<br>
	 * @param[in] border_value border value<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_dilate(kestrel_frame, kestrel_size2d_t, uint8_t*, kestrel_frame*, kestrel_border_type_e, uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:323</i>
	 */
	int kestrel_frame_dilate(Pointer src, kestrel_size2d_t.ByValue kernel_size, Pointer kernel_data, PointerByReference dst, int border_type, byte border_value);
	/**
	 * @brief Dilates an frame by using a specific structuring element<br>
	 * @param[in] src input frame<br>
	 * @param[in] kernel_size size of structuring element<br>
	 * @param[in] kernel_data data of structuring element, in row major order<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be returned, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must be release by<br>
	 * kestrel_frame_free()<br>
	 * @param[in] border_type only support KESTREL_BORDER_CONSTANT<br>
	 * @param[in] border_value border value<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_erode(kestrel_frame, kestrel_size2d_t, uint8_t*, kestrel_frame*, kestrel_border_type_e, uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:341</i><br>
	 * @deprecated use the safer methods {@link #kestrel_frame_erode(kestrel_frame.Kestrel_frameLibrary.kestrel_frame, kestrel_size2d_t.ByValue, java.nio.ByteBuffer, com.sun.jna.ptr.PointerByReference, int, byte)} and {@link #kestrel_frame_erode(com.sun.jna.Pointer, kestrel_size2d_t.ByValue, com.sun.jna.Pointer, com.sun.jna.ptr.PointerByReference, int, byte)} instead
	 */
	@Deprecated 
	int kestrel_frame_erode(Pointer src, kestrel_size2d_t.ByValue kernel_size, Pointer kernel_data, Pointer dst, int border_type, byte border_value);
	/**
	 * @brief Dilates an frame by using a specific structuring element<br>
	 * @param[in] src input frame<br>
	 * @param[in] kernel_size size of structuring element<br>
	 * @param[in] kernel_data data of structuring element, in row major order<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be returned, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must be release by<br>
	 * kestrel_frame_free()<br>
	 * @param[in] border_type only support KESTREL_BORDER_CONSTANT<br>
	 * @param[in] border_value border value<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_erode(kestrel_frame, kestrel_size2d_t, uint8_t*, kestrel_frame*, kestrel_border_type_e, uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:341</i>
	 */
	int kestrel_frame_erode(Pointer src, kestrel_size2d_t.ByValue kernel_size, ByteBuffer kernel_data, PointerByReference dst, int border_type, byte border_value);
	/**
	 * @brief Dilates an frame by using a specific structuring element<br>
	 * @param[in] src input frame<br>
	 * @param[in] kernel_size size of structuring element<br>
	 * @param[in] kernel_data data of structuring element, in row major order<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be returned, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must be release by<br>
	 * kestrel_frame_free()<br>
	 * @param[in] border_type only support KESTREL_BORDER_CONSTANT<br>
	 * @param[in] border_value border value<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_erode(kestrel_frame, kestrel_size2d_t, uint8_t*, kestrel_frame*, kestrel_border_type_e, uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:341</i>
	 */
	int kestrel_frame_erode(Pointer src, kestrel_size2d_t.ByValue kernel_size, Pointer kernel_data, PointerByReference dst, int border_type, byte border_value);
	/**
	 * @brief Calculates the weighted sum of two frames<br>
	 * @param[in] src1 first input frame<br>
	 * @param[in] alpha weight of first frame<br>
	 * @param[in] src2 second input frame<br>
	 * @param[in] beta weight of second frame<br>
	 * @param[in] gamma scalar added to each sum<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be returned, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must be release by<br>
	 * kestrel_frame_free()<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_add_weighted(kestrel_frame, float, kestrel_frame, float, float, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:359</i>
	 */
	int kestrel_frame_add_weighted(Pointer src1, float alpha, Pointer src2, float beta, float gamma, PointerByReference dst);
	/**
	 * @brief Applies an adaptive threshold to an gray image<br>
	 * @param[in] src input frame<br>
	 * @param[in] max_value value assigned to the pixels for which the condition is satisfied<br>
	 * @param[in] adaptive_method adaptive thresholding algorithm to use:<br>
	 *            0: ADAPTIVE_THRESH_MEAN_C, 1: ADAPTIVE_THRESH_GAUSSIAN_C<br>
	 * @param[in] threshold_type thresholding type, 0: THRESH_BINARY, 1: THRESH_BINARY_INV<br>
	 * @param[in] block_size size of a pixel neighborhood that is used to calculate a threshold value<br>
	 * for the pixel, greater than 1 and up to 15.<br>
	 * @param[in] delta constant subtracted from the mean or weighted mean.<br>
	 * @param[in] border_type ways to deal with border, only KESTREL_BORDER_REPLICATE is supported<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be returned, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must be release by<br>
	 * kestrel_frame_free()<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY<br>
	 * Original signature : <code>int kestrel_frame_adaptive_threshold(kestrel_frame, double, int, int, int, double, kestrel_border_type_e, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:380</i>
	 */
	int kestrel_frame_adaptive_threshold(Pointer src, double max_value, int adaptive_method, int threshold_type, int block_size, double delta, int border_type, PointerByReference dst);
	
	/**
	 * @brief Box Filter<br>
	 * @param[in] src input frame<br>
	 * @param[in] kernel_size size of filter<br>
	 * @param[in] normalize 0: don't normalize, non zero: normalize<br>
	 * @param[in] border_type ways to deal with border, only supports<br>
	 * KESTREL_BORDER_REFLECT/KESTREL_BORDER_REFLECT101<br>
	 * @param[out] dst Target frame, If `*dst` is NULL, a new frame will be returned, else<br>
	 * `*dst` should has enough capacity for output frame, `*dst` must be release by<br>
	 * kestrel_frame_free()<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_box_filter(kestrel_frame, kestrel_size2d_t, int, kestrel_border_type_e, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:398</i>
	 */
	int kestrel_frame_box_filter(Pointer src, kestrel_size2d_t.ByValue kernel_size, int normalize, int border_type, PointerByReference dst);
	
	/**
	 * @brief Calculates mean and standard deviation<br>
	 * @param[in] src input frame<br>
	 * @param[out] mean mean value of each channel<br>
	 * @param[out] stddev standard deviation value of each channel<br>
	 * @param[in] mask_stride stride of mask, 0 if no mask needed<br>
	 * @param[in] mask mask data, NULL if no mask needed<br>
	 * @param[in] channel_wise none zero: channel wise, 0: uniform<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note Supports GRAY/BGR/RGB/BGRA/ARGB<br>
	 * Original signature : <code>int kestrel_frame_mean_stddev(kestrel_frame, float*, float*, int, const uint8_t*, int)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:413</i>
	 */
	int kestrel_frame_mean_stddev(Pointer src, FloatBuffer mean, FloatBuffer stddev, int mask_stride, byte mask[], int channel_wise);
	/**
	 * @brief find homography<br>
	 * @param[in] num_points the number of src and dst points<br>
	 * @param[in] src src keypoints data, each points has 2 float values (x, y)<br>
	 * @param[in] dst dst keypoints data, each points has 2 float values (x, y)<br>
	 * @param[in] is_affine whether to use affine transform, 0: no, none zero: yes<br>
	 * @param[out] mat output transform matrix<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * Original signature : <code>int kestrel_find_homography(int, const float*, const float*, int, float[3][3])</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:426</i>
	 */
	int kestrel_find_homography(int num_points, float src[], float dst[], int is_affine, FloatBuffer mat);
	/**
	 * @deprecate since 1.4.0, instead of kestrel_frame_transform_to_tensor()<br>
	 * @brief Transform image to input tensor<br>
	 * @param[in,out] t Input tensor<br>
	 * @param[in] offset The offset of tensor, each image should be transformed to different position<br>
	 * @param[in] tensor_color Input tensor type: bgr/rgb/gray<br>
	 * @param[in] frame Origin frame<br>
	 * @param[in] means Means to substract<br>
	 * @param[in] stds Stds to divide<br>
	 * @param[in] paddings Default pixel filled in tensor, when frame is smaller than tensor, the<br>
	 * outer will be filled with paddings<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note the input tensor has only 3 kinds of type:<br>
	 *    GRAY_TENSOR needs RGB/BGR/GRAY frame,<br>
	 *    BGR_TENSOR and RGB_TENSOR need RGB/BGR frame<br>
	 * Original signature : <code>int kestrel_frame_to_tensor(kestrel_tensor, int32_t, kestrel_tensor_color_e, kestrel_frame, kestrel_pixel_t, kestrel_pixel_t, kestrel_pixel_t)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:446</i>
	 */
	int kestrel_frame_to_tensor(Pointer t, int offset, int tensor_color, Pointer frame, kestrel_pixel_t.ByValue means, kestrel_pixel_t.ByValue stds, kestrel_pixel_t.ByValue paddings);
	/**
	 * @deprecate since 1.4.0, instead of kestrel_frame_transform_to_tensor()<br>
	 * @brief Crop, resize and transform image to input tensor<br>
	 * @param[in,out] tensor Input tensor<br>
	 * @param[in] tensor_color Input tensor type: bgr/rgb/gray<br>
	 * @param[in] batch Number of frames and rois<br>
	 * @param[in] frames Origin frames<br>
	 * @param[in] rois Regions of interest in origin frames<br>
	 * @param[in] resize_param Type of resize: resize type: KEEP_RATIO/AUTO_RATIO<br>
	 * @param[in] means Means to substract<br>
	 * @param[in] stds Stds to divide<br>
	 * @param[in] paddings Fill the useless part of tensor with paddings, if NULL, fill with<br>
	 * (0, 0, 0)<br>
	 * @param[in,out] mem_cache Cache pool will dynamic(trigger: memory type or size) allocate in<br>
	 * this function. `mem_cache` must be released by kestrel_buffer_free()<br>
	 * @param[in,out] scale_height pointer to a `float*` object where the result will be stored, or a<br>
	 * null pointer (source_height / target_height)<br>
	 * @param[in,out] scale_width pointer to a `float*` object where the result will be stored, or a<br>
	 * null pointer (source_height / target_height) (source_width / target_width)<br>
	 * target_frame_size, buffer cnt must be larger than batch size for gpu resize: only use memory in<br>
	 * frame, buffer_size must be larger than (88 * batch size), buffer cnt must be larger than 1<br>
	 * @return int return KESTREL_OK for succeed, otherwise error code<br>
	 * @note the input tensor has only 3 kinds of type:<br>
	 *    GRAY_TENSOR needs RGB/BGR/GRAY frame.<br>
	 *    BGR_TENSOR and RGB_TENSOR need RGB/BGR frame<br>
	 * Original signature : <code>int kestrel_frame_transform_to_tensor_batch(kestrel_tensor, kestrel_tensor_color_e, size_t, kestrel_frame*, const kestrel_area2d_t*, kestrel_resize_param_e, kestrel_pixel_t, kestrel_pixel_t, const kestrel_pixel_t[], kestrel_buffer*, float[], float[])</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:478</i>
	 */
	int kestrel_frame_transform_to_tensor_batch(Pointer tensor, int tensor_color, long batch, PointerByReference frames, kestrel_area2d_t rois, int resize_param, kestrel_pixel_t.ByValue means, kestrel_pixel_t.ByValue stds, kestrel_pixel_t paddings[], PointerByReference mem_cache, FloatByReference scale_height, FloatByReference scale_width);
	/**
	 * Original signature : <code>int kestrel_frame_transform_to_tensor(kestrel_frame_transform_param_t*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:485</i>
	 */
	int kestrel_frame_transform_to_tensor(kestrel_frame_transform_param_t param);
	/**
	 * @note This API is just for debuging & testing.<br>
	 * Original signature : <code>int kestrel_tensor_to_frames(kestrel_tensor, kestrel_tensor_color_e, size_t, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:496</i>
	 */
	int kestrel_tensor_to_frames(Pointer tensor, int tensor_color, long index, PointerByReference frame);
	
	/**
	 * https://github.com/numpy/numpy/blob/master/numpy/lib/format.py<br>
	 * Original signature : <code>int kestrel_tensor_to_npy(kestrel_tensor, const char*)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:506</i>
	 */
	int kestrel_tensor_to_npy(Pointer tensor, String filename);
	/**
	 * @note This API is just for debuging & testing, only support dtype `float32, uint8`<br>
	 * Original signature : <code>kestrel_tensor kestrel_tensor_from_npy(const char*, const char*, kestrel_mem_type_e)</code><br>
	 * <i>native declaration : include/kestrel_frame_utils.h:515</i>
	 */
	Pointer kestrel_tensor_from_npy(String filename, String tensor_name, int mem_type);
}
