package com.sensetime.intersense.cognitivesvc.server.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "${db.cognitive_lock.table.name:cognitive_lock}")
@Data
@Builder
@Accessors(chain = true)
public class CognitiveLock {

    @Id
    @Column(name = "lock_key")
    private String lockKey;
    @Column(name = "seed")
    private String seed;
    @Column(name = "create_ts")
    private Date createTs;
    @Column(name = "update_ts")
    private Date updateTs;
}
