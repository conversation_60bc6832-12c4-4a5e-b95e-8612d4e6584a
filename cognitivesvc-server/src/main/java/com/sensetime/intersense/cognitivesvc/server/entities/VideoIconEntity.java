package com.sensetime.intersense.cognitivesvc.server.entities;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

public class VideoIconEntity{

	@NoArgsConstructor
	@AllArgsConstructor
	@Data
	@Builder
	@EqualsAndHashCode
	public static class Key implements Serializable{
		private static final long serialVersionUID = -69595539133592269L;
		
		@Schema(description = "人员tag")
		@Column(name = "person_tag")
		private String  personTag;
		
		@Schema(description = "所在的device的id")
		@Column(name = "device_id")
		private String  deviceId;
	}
	
	@NoArgsConstructor
	@AllArgsConstructor
	@Entity
	@Table(name = "video_icon")
	@Data
	@EqualsAndHashCode(callSuper = false)
	@Accessors(chain = true)
	@Schema(title = "视频渲染配置", description = "视频渲染配置")
	@IdClass(Key.class)
	public static class VideoIcon{
		@Schema(description = "人员tag")
		@Column(name = "person_tag")
	    @Id
		private String  personTag;
		
		@Schema(description = "所在的device的id")
		@Column(name = "device_id")
	    @Id
		private String  deviceId;
		
		@Column(name = "rgb")
		@Schema(description = "rgb色")
		private String  rgb;
		
		@Column(name = "text")
		@Schema(description = "文字(英文)")
	    private String  text;
		
		@Column(name = "font_size")
		@Schema(description = "文字大小")
	    private Integer  fontSize;
		
		@Column(name = "show_attr")
		@Schema(description = "是否显示情绪状态特征")
	    private Integer  showAttr;
		
		@Column(name = "font_thick")
		@Schema(description = "文字线条粗细")
	    private Integer  fontThick;
		
		@Column(name = "icon_image_base64")
		@Schema(description = "图标base64")
	    private String  iconImageBase64;
		
		@Column(name = "update_ts")
		@Schema(description = "更新时间")
		private Date updateTs;
	}
}

