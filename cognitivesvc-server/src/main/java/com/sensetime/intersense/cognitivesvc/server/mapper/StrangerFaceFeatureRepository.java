package com.sensetime.intersense.cognitivesvc.server.mapper;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import com.sensetime.intersense.cognitivesvc.server.entities.StrangerFaceFeature;
import org.springframework.stereotype.Repository;

@Repository
public interface StrangerFaceFeatureRepository extends JpaRepositoryImplementation<StrangerFaceFeature, Integer> {
	public List<StrangerFaceFeature> findByCreateTsBetweenOrderByCreateTsDesc(Date startTime, Date endTime);
}
