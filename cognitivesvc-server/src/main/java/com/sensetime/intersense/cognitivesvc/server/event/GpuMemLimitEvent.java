package com.sensetime.intersense.cognitivesvc.server.event;

import org.springframework.context.ApplicationEvent;

public class GpuMemLimitEvent extends ApplicationEvent{

	private static final long serialVersionUID = 5960982695905580412L;
	
	private float gpuMemRate;
	
	public float getGpuMemRate() {
		return gpuMemRate;
	}

	public GpuMemLimitEvent(float gpuMemRate) {
		super(GpuMemLimitEvent.class);
		this.gpuMemRate = gpuMemRate;
	}
}
