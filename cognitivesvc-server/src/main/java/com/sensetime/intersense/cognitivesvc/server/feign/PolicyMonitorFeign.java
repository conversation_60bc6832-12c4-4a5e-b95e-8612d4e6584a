package com.sensetime.intersense.cognitivesvc.server.feign;


import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Created by lijinxing on 2018/4/25.
 */
@FeignClient(value = "policyMonitor", configuration = {})
public interface PolicyMonitorFeign {

    @RequestMapping("/monitorTargetDefinition/queryListOfDictionary")
    BaseRes queryListOfDictionary(@Valid @RequestBody TargetDefinitionQueryReq req);

    @Data
    @Schema(title = "查询大分类和小分类信息", description = "查询大分类和小分类信息请求参数")
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public class TargetDefinitionQueryReq {
        @Schema(description = "属性大分类英文名称", name = "targetTypeList")
        private List<String> targetTypeList;

        @Schema(description = "模型来源 1.spring  2.kestrel", name = "modelSource")
        private List<Integer> modelSource;
    }


    @Data
    public class TagetListRes {
        String targetType;
        List<TagetDictionaryBean> TagetDictionarys;
    }

    @Data
    public class TagetDictionaryBean {
        String targetId;
        String targetName;
        String targetCnName;
        Integer modelSource;
        List<TargetOptionsBean> targetOptions;
    }

    @Data
    public class TargetOptionsBean {
        String targetCnOption;
        String targetEnOption;
        String targetOptionIdentifier;
    }

}
