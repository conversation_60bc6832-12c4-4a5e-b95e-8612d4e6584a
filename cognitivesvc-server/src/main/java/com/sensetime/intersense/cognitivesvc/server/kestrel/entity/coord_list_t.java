package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class coord_list_t extends Structure {
	/**
	 * < 跨线roi顶点坐标<br>
	 * C type : kestrel_point2d_t[1024]
	 */
	public kestrel_point2d_t[] coords = new kestrel_point2d_t[1024];
	public int coord_num;
	public coord_list_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("coords", "coord_num");
	}
	/**
	 * @param coords < 跨线roi顶点坐标<br>
	 * C type : kestrel_point2d_t[1024]
	 */
	public coord_list_t(kestrel_point2d_t coords[], int coord_num) {
		super();
		if ((coords.length != this.coords.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.coords = coords;
		this.coord_num = coord_num;
	}
	public coord_list_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends coord_list_t implements Structure.ByReference {
		
	};
	public static class ByValue extends coord_list_t implements Structure.ByValue {
		
	};
}