package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_head_target_t extends Structure {
	/** < 所在帧的时间戳 */
	public long timestamp;
	/** < 人头对应的帧在整个处理的帧数组中的索引位置 */
	public long frame_index;
	/** < 通道号 */
	public long context_id;
	/** < 跟踪id */
	public int track_id;
	/**
	 * < 人头点在视频帧中的位置<br>
	 * C type : kestrel_point2df_t
	 */
	public kestrel_point2df_t pt_position;
	/**
	 * < 人头/体框在视频帧中的位置<br>
	 * C type : kestrel_area2d_t
	 */
	public kestrel_area2d_t rect_position;
	/** C type : kestrel_point2df_t */
	public kestrel_point2df_t direction;
	/** <人速度 */
	public float speed;
	public crowd_head_target_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("timestamp", "frame_index", "context_id", "track_id", "pt_position", "rect_position", "direction", "speed");
	}
	/**
	 * @param timestamp < 所在帧的时间戳<br>
	 * @param frame_index < 人头对应的帧在整个处理的帧数组中的索引位置<br>
	 * @param context_id < 通道号<br>
	 * @param track_id < 跟踪id<br>
	 * @param pt_position < 人头点在视频帧中的位置<br>
	 * C type : kestrel_point2df_t<br>
	 * @param rect_position < 人头/体框在视频帧中的位置<br>
	 * C type : kestrel_area2d_t<br>
	 * @param direction C type : kestrel_point2df_t<br>
	 * @param speed <人速度
	 */
	public crowd_head_target_t(long timestamp, long frame_index, long context_id, int track_id, kestrel_point2df_t pt_position, kestrel_area2d_t rect_position, kestrel_point2df_t direction, float speed) {
		super();
		this.timestamp = timestamp;
		this.frame_index = frame_index;
		this.context_id = context_id;
		this.track_id = track_id;
		this.pt_position = pt_position;
		this.rect_position = rect_position;
		this.direction = direction;
		this.speed = speed;
	}
	public crowd_head_target_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_head_target_t implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_head_target_t implements Structure.ByValue {
		
	};
}