package com.sensetime.intersense.cognitivesvc.server.properties;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.config.SslConfigs;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaConnectionDetails;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.cloud.stream.binder.kafka.properties.KafkaBinderConfigurationProperties;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Primary
public class CustomKafkaBinderConfigurationProperties extends KafkaBinderConfigurationProperties {
    @Value("${spring.cloud.stream.kafka.binder.enableSSl:true}")
    private Boolean enableSSl;
    @Value("${spring.cloud.stream.kafka.binder.enableTruststore:true}")
    private Boolean enableTruststore;

    public CustomKafkaBinderConfigurationProperties(KafkaProperties kafkaProperties, ObjectProvider<KafkaConnectionDetails> connectionDetailsProvider) {
        super(kafkaProperties,connectionDetailsProvider);
    }

    private void reBuildSsl(Map properties) {
        if (!enableSSl) {
            properties.remove(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG);
            properties.remove(SaslConfigs.SASL_MECHANISM);
            properties.remove(SaslConfigs.SASL_JAAS_CONFIG);
        }
        if (!enableTruststore) {
            properties.remove(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG);
            properties.remove(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG);
            properties.remove(SslConfigs.SSL_KEYSTORE_LOCATION_CONFIG);
            properties.remove(SslConfigs.SSL_KEYSTORE_PASSWORD_CONFIG);
        }
    }

    public void setConfiguration(Map<String, String> configuration) {
        reBuildSsl(configuration);
        super.setConfiguration(configuration);
    }

}