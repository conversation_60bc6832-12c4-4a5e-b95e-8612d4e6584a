package com.sensetime.intersense.cognitivesvc.server.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class DeviceInfoEntity {

    private String eventName;
    private String eventAction;
    private List<DeviceInfo> data;


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class DeviceInfo{

        private String videoRate;
        private String rtspWidth;
        private String rtspHeight;
        private String did;
        private String lastChkTs;
    }

}
