package com.sensetime.intersense.cognitivesvc.server.utils;

import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.ModelHolder;
import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils.Kesonable;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.storage.service.FileAccessor;
import com.sensetime.storage.utils.CommonUtils;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.IntStream;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
/**
 * 模型的抽象类，把跑模型抽象出来，模型加载放在实现类中
 */
@Slf4j
public abstract class AbstractHandler<T extends AbstractHandler.Detection>{
	
	@Autowired
	protected ExecutorService cogThreadPool;

	@Autowired
	FileAccessor fileAccessor;
    
    protected Thread[] modelThreads;
    
    protected Queue<ModelResult> extractingQueue;
    
    protected Semaphore extractingSemaphore;
    
    protected CountDownLatch latch;
    
    protected HandlerEntity handlerEntity;
    
    @Getter
    protected ModelHolder pointers[];
	
	protected final Semaphore[] semaphores = new Semaphore[] {new Semaphore(1), new Semaphore(1)};
	
	protected final boolean isFlock;
    
    protected AbstractHandler(boolean isFlock) {
    	this.isFlock = isFlock;
    }

    protected ModelResult handle(ModelRequest modelRequest) {
        if (handlerEntity.getStatus() != 0 || modelThreads == null)
            return ModelResult.EMPTY;

        ModelResult result = ModelResult.builder().request(modelRequest).build();
        if(modelRequest.isAsap()) {
        	List<ModelResult> handlingList = List.of(result);
        	PointerByReference[] results;
        	
        	if(extractSemaphore().tryAcquire()) {
        		try {
        			results = batch_extract_xmodel_asap(handlingList);
        		}catch (Exception e) {
                    e.printStackTrace();
                    results = new PointerByReference[0];
                }finally {
                	 extractSemaphore().release();
                }
        	}else {
        		results = new PointerByReference[0];
        	}
        	
            batch_handle_extract_result(handlingList, results);
        }else {        	
            try {
                if(extractQueue().offer(result))
                    for (boolean ret = result.getLatch().await(1, TimeUnit.SECONDS); !ret && handlerEntity.getStatus() == 0; ret = result.getLatch().await(1, TimeUnit.SECONDS));
            } catch (Exception e) {
            	e.printStackTrace();
            }
        }
        
        return result;
    }
    
    @PostConstruct
    protected synchronized void startHandle() {
        if (handlerEntity.getStatus() == 0)
        	return ;
        
        handlerEntity.setStatus(0);
    	
        modelThreads = new Thread[2];
        latch = new CountDownLatch(modelThreads.length);
        
        for (int index = 0; index < modelThreads.length; index++) {
            Thread xModelThread = new Thread(Utils.cogGroup, () -> this.run());
            xModelThread.setDaemon(true);
            xModelThread.setPriority(Thread.NORM_PRIORITY + 2);
            xModelThread.setName("FaceModel[" + getClass().getSimpleName().replaceAll("ModelHandler", "") + "-No." + index + "]");
            xModelThread.start();
            
            modelThreads[index] = xModelThread;
        }
    }
    
    @PreDestroy
    protected synchronized void stopHandle() {
    	if (handlerEntity.getStatus() != 0)
        	return ;
        
        handlerEntity.setStatus(1);
        
        try {
			boolean ret = latch.await(30, TimeUnit.SECONDS);
			if(!ret)
				new RuntimeException(getClass().getSimpleName() + " await more than 30 second.").printStackTrace();
			
			Thread.sleep(500);
		} catch (InterruptedException e1) {}
        
        modelThreads = null;
    }
    
    protected Queue<ModelResult> extractQueue(){
    	if(extractingQueue == null)
    		synchronized(this) {
    			if(extractingQueue == null)
    				extractingQueue = new LinkedBlockingQueue<ModelResult>(Math.max(64, handlerEntity.getBatchSize() * 4));
    		}
    	
    	return extractingQueue;
    }
    
    protected Semaphore extractSemaphore(){
    	if(extractingSemaphore != null)
    		return extractingSemaphore;
    	
		synchronized(this) {
			if(extractingSemaphore == null)
				extractingSemaphore = new Semaphore(Math.max(64, handlerEntity.getBatchSize() * 4));
		}
    	
    	return extractingSemaphore;
    }
    
    protected void run() {
		Initializer.bindDeviceOrNot();
        
        while (handlerEntity.getStatus() == 0 || !extractQueue().isEmpty()) {
            PointerByReference output_kesons[] = null;
            List<ModelResult> handlingList = null;

            long now = 0;
            boolean logged = Utils.instance.watchFrameTiktokLevel == -789;
            if(logged)
            	now = System.currentTimeMillis();
            
            try {
            	semaphores[0].acquire();
                try {
            		handlingList = Utils.drainFromQueue(extractQueue(), handlerEntity.getBatchSize(), handlerEntity.getDrainPollCount(), handlerEntity.getDrainTimeout());
                    if (handlingList.isEmpty()) {
                    	Thread.sleep(40);
                        continue;
                    }
                    
                    output_kesons = batch_extract_xmodel_asap(handlingList);
                } finally {
                	if (!handlingList.isEmpty())
                		semaphores[1].acquireUninterruptibly();
                	
                	semaphores[0].release();
                }
                
                try {
                    batch_handle_extract_result(handlingList, output_kesons);
                }finally {
                	semaphores[1].release();
                	
                	for (int index = 0; index < handlingList.size(); index++)
                        handlingList.get(index).getLatch().countDown();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }finally {
            	if(logged && handlingList.size() > 0)
            		log.info("[VideoHandleLog] [Cost] model handled [" + getClass().getSimpleName() + "] batch[" + handlingList.size() + "] cost[" + (System.currentTimeMillis() - now) + "]");
            }
        }
        
        try { Thread.sleep(5000); } catch (InterruptedException e) { }
        
        if(CollectionUtils.isNotEmpty(extractQueue()) && semaphores[0].tryAcquire())
            for(ModelResult item = extractQueue().poll(); item != null; item = extractQueue().poll())
            	FrameUtils.batch_free_frame(item.getRequest().getFrames());
        
        latch.countDown();
    }
    
    /** 准备输入数据 */
    protected PointerByReference prepareInput(List<ModelResult> handlingList) {
    	if(isFlock) {
    		Pointer[] frames    = new Pointer[handlingList.size()];
        	Integer[] sourceIds = new Integer[handlingList.size()];
        	
    		for (int index = 0; index < handlingList.size(); index++) {
    			sourceIds[index] = index;
    			frames[index] = handlingList.get(index).getRequest().getFrames()[0];
    		}
            
    		return new PointerByReference(KestrelApi.keson_add_item_to_array(KestrelApi.keson_create_array(), KesonUtils.frameToKeson(frames, sourceIds)));
    	}else {
        	Pointer[] gpuFrames = handlingList.stream().map(item -> item.getRequest().getFrames()[0]).toArray(Pointer[]::new);
            return new PointerByReference(KesonUtils.frameToKeson(gpuFrames));
    	}
    }
    
    /** 按照 函数包含的模型 顺序的跑一边 */
    protected PointerByReference[] batch_extract_xmodel_asap(List<ModelResult> handlingList) {
        if (ArrayUtils.isEmpty(pointers) || handlerEntity.getStatus() != 0)
            return new PointerByReference[0];
        
        PointerByReference   param_keson  = prepareInput(handlingList);
        PointerByReference[] result_keson = IntStream.range(0, pointers.length).mapToObj(index -> new PointerByReference()).toArray(PointerByReference[]::new);
        
        for (int index = 0; index < pointers.length; index ++) {
        	int paramIndex = handlerEntity.getInputIndexMap().getOrDefault(index, index - 1);
        	Pointer input = paramIndex >= 0 ? result_keson[paramIndex].getValue() : param_keson.getValue();
            
        	/** 执行模型 获取数据*/
            pointers[index].process(input, result_keson[index]);
            
            ScaleUpTarget scaleUp = handlerEntity.getScaleUp();
            if(scaleUp != null && scaleUp.getModelIndex() == index && Math.abs(scaleUp.getScaleRate()) >= 0.001)
            	KesonUtils.scaleAllTargetUp(result_keson[index].getValue(), scaleUp.getScaleRate());
        }
        
        KesonUtils.kesonDeepDelete(param_keson);
        return result_keson;
    }
    
    /** 将批量处理结果 整理后放到每个中 */
	protected void batch_handle_extract_result(List<ModelResult> handlingList, PointerByReference[] outputKesons) {
        PointerByReference[] sub_kesons ;
        if(isFlock) {
    		Pointer frames[] = handlingList.stream().map(item -> item.getRequest().getFrames()[0]).toArray(Pointer[]::new);
    		sub_kesons = KesonUtils.splitFlockKesonAndDestroy(KesonUtils.mergeRenameAllToFirstFlockKeson(KesonUtils.tryReformFlockKeson(outputKesons)), frames);
        }else {
        	PointerByReference output = KesonUtils.mergeRenameAllToFirstKeson(outputKesons);
        	sub_kesons = KesonUtils.splitKesonAndDestroy(output, handlingList.size());
        }
        
        for (int index = 0; index < handlingList.size(); index++) {
        	ModelResult item = handlingList.get(index);
        	item.setResult(sub_kesons[index]);
        	
        	Consumer<ModelResult> consumer = item.getRequest().getConsumer();
        	if(consumer != null) {
        		try {
            		consumer.accept(item);
        		}catch(Exception e) {
        			e.printStackTrace();
        		}
        	}
        }
    }
    
	/** 提取特征 先检查是CPU OR GPU */
	public T[] extractByPath(String image_path){
		VideoFrame frames = null;
		// general保持原来模式
		if(CommonUtils.matchesOsgImageUrl(image_path)){
			byte[] bytes = new byte[0];
			try {
				bytes = fileAccessor.readImage(image_path);
			} catch (Exception e) {
				e.printStackTrace();
			}

			frames = FrameUtils.decode_up_down_load_from_memory(bytes);
		}else {
			frames = FrameUtils.decode_up_down_load_frame_path(image_path);
		}

		try {
			return extractByFrame(frames.getGpuFrame());
		}finally {
			FrameUtils.batch_free_frame(frames);
		}
	}

	/** 提取特征 先检查是CPU OR GPU ,功能参考上面的by path方法 */
	public T[] extractByBytes(byte[] bytes){
		VideoFrame frames = FrameUtils.decode_up_down_load_from_memory(bytes);
		try {
			return extractByFrame(frames.getGpuFrame());
		}finally {
			FrameUtils.batch_free_frame(frames);
		}
	}


	public T[] extractByPathCatch(String imagePath) throws Exception {
		VideoFrame frames = null;
		try {
			// 解码并加载帧
			if(CommonUtils.matchesOsgImageUrl(imagePath)){
				byte[] bytes = new byte[0];
				try {
					bytes = fileAccessor.readImage(imagePath);
				} catch (Exception e) {
					e.printStackTrace();
				}

				frames = FrameUtils.decode_up_down_load_from_memory(bytes);
			}else {
				frames = FrameUtils.decode_up_down_load_frame_path(imagePath);
			}

			// 检查 VideoFrame 是否为 null
			if (frames == null) {
				throw new RuntimeException("VideoFrame is null for image path: " + imagePath);
			}

			// 检查 GPU 帧是否为 null
			Pointer gpuFrame = frames.getGpuFrame();
			if (gpuFrame == null) {
				throw new RuntimeException("GPU frame is null for image path: " + imagePath);
			}

			// 提取 GPU 帧
			return extractByFrame(gpuFrame);
		} catch (Exception e) {
			// 处理异常，例如记录日志
			log.error("Error extracting frames from path: " + imagePath, e);
			throw new RuntimeException("Error extracting frames from path: " + imagePath, e);
		} finally {
			// 确保资源被释放
			if (frames != null) {
				FrameUtils.batch_free_frame(frames);
			}
		}
	}


	/** 同步提取特征 */
	public T[] extractByFrame(Pointer frame){
		ModelRequest request = new ModelRequest(frame);
		if(!Initializer.isGpu())
			request.setAsap(true);
		
		ModelResult result = handle(request);
		try {
			T[] data = readModelResult(result);
			Arrays.sort(data, (l, r) -> r.getHunter().getWidth() * r.getHunter().getHeight() - l.getHunter().getWidth() * l.getHunter().getHeight());
			return data;
		}finally {
			releaseModelResult(result);
		}
	}
	
	/** 异步提取特征 */
	public boolean extractAsyncByPath(String image_path, Consumer<T[]> consumer){
		//VideoFrame frames = FrameUtils.decode_up_down_load_frame_path(image_path);

		VideoFrame frames = null;
		// general保持原来模式
		if(CommonUtils.matchesOsgImageUrl(image_path)){
			byte[] bytes = new byte[0];
			try {
				bytes = fileAccessor.readImage(image_path);
			} catch (Exception e) {
				e.printStackTrace();
			}

			frames = FrameUtils.decode_up_down_load_from_memory(bytes);
		}else {
			frames = FrameUtils.decode_up_down_load_frame_path(image_path);
		}

		
		try {
			return extractAsyncByFrame(frames.getGpuFrame(), consumer);
		}finally {
			FrameUtils.batch_free_frame(frames);
		}
	}
	
	/** 异步提取特征 */
	public boolean extractAsyncByFrame(Pointer frame, Consumer<T[]> consumer){
		if(consumer == null || frame == null) {
            log.error("there should be a consumer in async extraction.");
            throw new BusinessException("9900","there should be a consumer in async extraction.");
        }
			
		Consumer<ModelResult> consumerWraper = result -> {
			try {
				T[] data = null;
				try {
					data = readModelResult(result);
					Arrays.sort(data, (l, r) -> r.getHunter().getWidth() * r.getHunter().getHeight() - l.getHunter().getWidth() * l.getHunter().getHeight());
				}finally {
					consumer.accept(data);
				}
			}catch(Exception e){
				e.printStackTrace();
			}finally {
				releaseModelResult(result);
				FrameUtils.batch_free_frame(result.getRequest().getFrames());
			}
		};
		
		boolean ret = true;
		ModelRequest modelRequest = new ModelRequest(consumerWraper, FrameUtils.ref_frame(frame));
		
		if(Initializer.isDevice()) {
			ModelResult result = ModelResult.builder().request(modelRequest).build();
			ret = extractQueue().offer(result);
			if(!ret)
				consumerWraper.accept(result);
		}else {
			try {
				modelRequest.setAsap(true);
				cogThreadPool.submit(() -> handle(modelRequest));
			}catch(Exception e) {
				ret = false;
				consumerWraper.accept(ModelResult.builder().request(modelRequest).build());
			}
		}
		
		return ret;
	}
    
    protected void releaseModelResult(ModelResult modelResult) {
    	if(modelResult == null)
    		return ;
    	
    	KesonUtils.kesonDeepDelete(modelResult.getResult());
    }
    
    protected abstract T[] readModelResult(ModelResult modelResult) ;

	@Getter
	@Builder
    @Accessors(chain = true)
    public static class HandlerEntity {
		//下一个模型需要上面哪个模型的输入作为输出
		@Builder.Default
		private Map<Integer, Integer> inputIndexMap = Map.of();
    	//批量个数(optional)
    	@Builder.Default
    	private Integer  batchSize = Initializer.batchSize;
    	//扩大比例
    	@Builder.Default
    	private ScaleUpTarget scaleUp = null;
    	//惰性初始化
    	@Builder.Default
    	private boolean lazyInit = true;
    	//公交车延迟
    	@Builder.Default
    	private int drainPollCount = 4;
    	//公交车延迟
    	@Builder.Default
    	private int drainTimeout = 5;
        @Setter
        @Builder.Default
        protected volatile int status = 1;
    }
	
	@Getter
	@Builder
    @Accessors(chain = true)
	public static class ScaleUpTarget{
		/** 要针对第几号模型的结果进行scaleup */
		@Builder.Default
		private int modelIndex = 0;
		
		/** 扩大比率 正数为扩大 负数为收缩 */
		private float scaleRate;
	}
    
    @Getter
    @Accessors(chain = true)
    public static class ModelRequest {
    	/**用数组 表示一次批量 多张图片一次来跑 */
    	private Pointer frames[];
    	
    	/**传递参数 */
    	@Setter
        private boolean asap;
    	
    	/**传递参数 */
		@Setter
        private Consumer<ModelResult> consumer;
        
		public ModelRequest(Pointer... frames) {
        	if(ArrayUtils.isEmpty(frames)) {
                log.error("length should not be 0.");
                throw new BusinessException("3004","length should not be 0.");
            }
        	
        	this.frames = frames;
        }
		
        public ModelRequest(Consumer<ModelResult> consumer, Pointer... frames) {
        	this(frames);
        	this.consumer = consumer;
        }
    }
    
    @Builder
    @Accessors(chain = true)
    public static class ModelResult {
    	/**入参帧 */
    	@Getter
    	private ModelRequest request;
    	
    	/**模型原始返回值 */
    	@Setter
    	@Getter
    	private PointerByReference result;
        
    	@Getter
    	@Builder.Default
        private CountDownLatch latch = new CountDownLatch(1);
		
		public static final ModelResult EMPTY = ModelResult.builder().build();
    }
    
    @Data
	@Accessors(chain = true)
	@NoArgsConstructor
	@AllArgsConstructor
	public static abstract class Detection implements Comparable<Detection>, Kesonable{
		/** 脸索引 */
    	private int id;
    	/** 图片索引 */
    	private int imageId;
		/** 特征 */
    	private Rect hunter;
		/** 置信度*/
		private double confidence;
		
		@Override
		public int compareTo(Detection o) {
			return Integer.compare(this.imageId, o.imageId) == 0 ? Integer.compare(this.id, o.id) : Integer.compare(this.imageId, o.imageId);
		}
		
		@Data
		@Accessors(chain = true)
		@NoArgsConstructor
		@Builder
		@AllArgsConstructor
		public static class Rect{
			private int left;
			private int top;
			private int width;
			private int height;
			
			public Dot toDot(){
				return Dot.builder().left(left).top(top).right(left + width).bottom(top + height).build();
			}
		}
		
		@Data
		@Accessors(chain = true)
		@NoArgsConstructor
		@Builder
		@AllArgsConstructor
		public static class Dot{
			private int left;
			private int top;
			private int right;
			private int bottom;
			
			public Rect toRect(){
				return Rect.builder().left(left).top(top).width(right - left).height(bottom - top).build();
			}
		}
    }
}
