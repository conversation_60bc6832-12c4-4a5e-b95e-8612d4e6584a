package com.sensetime.intersense.cognitivesvc.server.vaxtor.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlateOutput {
    private int cxtId; // 上下文ID
    private String plateNumberAscii; // 车牌号码（ASCII格式）
    private String plateCountry; // 车牌国家
    private String vehicleMake; // 车辆品牌
    private String vehicleModel; // 车辆型号
    private String vehicleColor; // 车辆颜色
    private String vehicleClass; // 车辆类别
    private double ocrTime; // OCR处理时间
    private PlateBoundingBox plateBB; // 车牌边界框

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class PlateBoundingBox {
        private int x; // 边界框左上角X坐标
        private int y; // 边界框左上角Y坐标
        private int width; // 边界框宽度
        private int height; // 边界框高度

        @Override
        public String toString() {
            return String.format("x=%d, y=%d, width=%d, height=%d", x, y, width, height);
        }
    }
} 