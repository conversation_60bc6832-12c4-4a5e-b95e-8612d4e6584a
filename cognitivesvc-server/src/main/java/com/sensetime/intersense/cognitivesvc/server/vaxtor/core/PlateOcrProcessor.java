package com.sensetime.intersense.cognitivesvc.server.vaxtor.core;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.properties.VaxtorConfigProperties;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateInput;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateOutput;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateRequest;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.Vaxtor_ocrLibrary;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 车牌OCR处理器
 * 负责管理多个检测器实例和处理请求队列
 */
@Slf4j
public class PlateOcrProcessor {
    private static final Vaxtor_ocrLibrary OCR_LIB = Vaxtor_ocrLibrary.INSTANCE;
    
    private final ExecutorService executorService;
    private final BlockingQueue<PlateRequest> requestQueue;
    private final List<PlateOcrDetector> detectors;
    private volatile boolean running = true;
/*

    // 车辆颜色标准化映射表
    private static final Map<String, String> PLATE_COLOR_MAP = Map.ofEntries(
        Map.entry("Black", "BLACK"),
        Map.entry("White", "WHITE"),
        Map.entry("Grey", "GRAY"),
        Map.entry("Red", "RED"),
        Map.entry("Blue", "BLUE"),
        Map.entry("Yellow", "YELLOW"),
        Map.entry("Beige", "YELLOW"),
        Map.entry("Golden", "YELLOW"),
        Map.entry("Orange", "ORANGE"),
        Map.entry("Brown", "BROWN"),
        Map.entry("Green", "GREEN"),
        Map.entry("Violet", "PURPLE")
    );
*/

    /**
     * 初始化处理器
     * @param detectorCount 检测器数量
     * @param queueSize 请求队列大小
     */
    public PlateOcrProcessor(int detectorCount, int queueSize,String ocrDataPath,List<String> countryList) {
        this.executorService = Executors.newFixedThreadPool(detectorCount);
        this.requestQueue = new ArrayBlockingQueue<>(queueSize);
        this.detectors = new ArrayList<>();
        
        initializeDetectors(detectorCount,ocrDataPath,countryList);
        startProcessing();
        
        log.info(">>> Processor initialized with " + detectorCount + " detectors");
    }

    public PlateOcrProcessor(VaxtorConfigProperties properties) {
        this.executorService = Executors.newFixedThreadPool(properties.getDetectorCount());
        this.requestQueue = new ArrayBlockingQueue<>(properties.getQueueSize());
        this.detectors = new ArrayList<>();

        log.info(">>> Processor initialized with VaxtorConfigProperties={}" + JSON.toJSONString(properties));
        initializeDetectors(properties);
        startProcessing();

        log.info(">>> Processor initialized with " + properties.getDetectorCount() + " detectors");
    }


    /**
     * 初始化指定数量的检测器
     */
    private void initializeDetectors(VaxtorConfigProperties properties) {
        for (int i = 0; i < properties.getDetectorCount(); i++) {
            try {
                PlateOcrDetector detector = new PlateOcrDetector(i,properties);
                detectors.add(detector);
                log.info(">>> Detector " + i + " created successfully");
            } catch (Exception e) {
                log.error(">>> Failed to create detector " + i + ": " + e.getMessage());
            }
        }
    }

    /**
     * 初始化指定数量的检测器
     * @param count 检测器数量
     */
    private void initializeDetectors(int count,String ocrDataPath,List<String> countryList) {
        for (int i = 0; i < count; i++) {
            try {
                PlateOcrDetector detector = new PlateOcrDetector(i,ocrDataPath,countryList);
                detectors.add(detector);
                log.info(">>> Detector " + i + " created successfully");
            } catch (Exception e) {
                log.error(">>> Failed to create detector " + i + ": " + e.getMessage());
            }
        }
    }

    /**
     * 启动请求处理线程
     */
    private void startProcessing() {
        for (PlateOcrDetector detector : detectors) {
            executorService.submit(() -> processRequests(detector));
        }
    }
    
    /**
     * 处理请求队列中的任务
     * @param detector 用于处理请求的检测器
     */
    private void processRequests(PlateOcrDetector detector) {
        while (running) {
            try {
                // 从队列中获取请求并处理
                PlateRequest request = requestQueue.take();
                PlateOutput result = detector.process(request.getInput());
                request.getResultFuture().complete(result);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error(">>> Request processing failed: " + e.getMessage());
            }
        }
    }

    /**
     * 提交识别请求
     * @param input 输入图像数据
     * @return 包含识别结果的Future对象
     */
    public CompletableFuture<PlateOutput> submitRequest(PlateInput input) {
        CompletableFuture<PlateOutput> future = new CompletableFuture<>();
        PlateRequest request = new PlateRequest(input, future);
        
        try {
            requestQueue.put(request);
        } catch (InterruptedException e) {
            future.completeExceptionally(e);
            Thread.currentThread().interrupt();
        }
        
        return future;
    }

    /**
     * 关闭处理器并释放资源
     */
    public void shutdown() {
        running = false;
        executorService.shutdown();
        detectors.forEach(PlateOcrDetector::terminate);
        log.info(">>> Processor shutdown completed");
    }
} 