package com.sensetime.intersense.cognitivesvc.server.entities;

import java.util.Date;



import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SP;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "${db.passer_face_feature.table.name:passer_face_feature}")
@Data
@Accessors(chain = true)
public class PasserFaceFeature implements SP{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "avatar_image_url")
    private String avatarImageUrl;
    @Column(name = "image_feature")
    private String imageFeature;
    @Column(name = "image_quality")
    private Float imageQuality;
    @Column(name = "person_uuid")
    private String personUuid;
    @Column(name = "group_id")
    private String groupId;
    @Column(name = "track_id")
    private String trackId;
    @Column(name = "sts")
    private Integer sts;
    @Column(name = "create_ts")
    private Date createTs;
    @Column(name = "last_mod_ts")
    private Date lastModTs;
    @Column(name = "privilege")
    private String privilege;

}
