package com.sensetime.intersense.cognitivesvc.server.event.send;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.http.MediaType;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class imageDataEventOutput implements BaseOutput {

    private final StreamBridge streamBridgeTemplate;

    String IMAGE_DATA_EVENT_OUTPUT = "image_data_event_output-out-0";
    public imageDataEventOutput(StreamBridge streamBridgeTemplate) {
        this.streamBridgeTemplate = streamBridgeTemplate;
    }

    @Override
    public boolean send(Message<?> message, long timeout) {
        if (streamBridgeTemplate != null) {
            boolean send = streamBridgeTemplate.send(IMAGE_DATA_EVENT_OUTPUT,
                    message, MediaType.TEXT_PLAIN);
            if (send) {
                log.debug("image_data_event_output msg send success ");
                return true;
            }
        }

        return false;
    }
}