package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;

/**
 * JNA Wrapper for library <b>OpencvDecoderLibrary</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */

public interface FrameEncoderLibrary extends Library {

    public static final String JNA_LIBRARY_NAME = "frame_encoder";

    public static final FrameEncoderLibrary INSTANCE = (FrameEncoderLibrary) Native.load(FrameEncoderLibrary.JNA_LIBRARY_NAME, FrameEncoderLibrary.class);
    /**
     * @param  frame pointer <br>
     * @return the pointer of opencv decoder<br>
     * Original signature : <code>byteEncodeFrame(bool)</code><br>
     * <i>native declaration : frame_encoder.h:20</i>
     * @brief Create a Opencv Decoder object<br>
     * <br>
     */

    Pointer EncodeFrame(Pointer frame, int flag, LongByReference outSize);
    /**
     * @param frame opencv decoder pointer<br>
     * @param format    rtsp stream or vide file<br>
     * @param outSize    rtsp stream or vide file<br>
     * @return true if ok , else false<br>
     * Original signature : <code>bool OpenStream(OpencvVideoDecoder*, const char*)</code><br>
     * <i>native declaration : clip/decoder_wrapper.h:30</i>
     * @brief Open a rtsp stream or video file<br>
     * <br>
     */
    Pointer KestrelEncoder(Pointer frame, int format, LongByReference outSize);
}
