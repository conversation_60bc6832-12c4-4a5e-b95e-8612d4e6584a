package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.*;
import com.sun.jna.ptr.PointerByReference;

/**
 * JNA Wrapper for library <b>OpencvDecoderLibrary</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */

public interface OpencvDecoderLibrary extends Library {

    public static final String JNA_LIBRARY_NAME = "opencv_decoder";
    public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(OpencvDecoderLibrary.JNA_LIBRARY_NAME);
    public static final OpencvDecoderLibrary INSTANCE = (OpencvDecoderLibrary) Native.load(OpencvDecoderLibrary.JNA_LIBRARY_NAME, OpencvDecoderLibrary.class);

    /**
     * @param gpu if use gpu to decode or not<br>
     * @return the pointer of opencv decoder<br>
     * Original signature : <code>OpencvVideoDecoder* CreateOpencvDecoder(bool)</code><br>
     * <i>native declaration : clip/decoder_wrapper.h:20</i>
     * @brief Create a Opencv Decoder object<br>
     * <br>
     */
    OpencvDecoderLibrary.OpencvVideoDecoder CreateOpencvDecoder(boolean gpu);

    /**
     * @param decoder opencv decoder pointer<br>
     * @param String    rtsp stream or vide file<br>
     * @return true if ok , else false<br>
     * Original signature : <code>bool OpenStream(OpencvVideoDecoder*, const char*)</code><br>
     * <i>native declaration : clip/decoder_wrapper.h:30</i>
     * @brief Open a rtsp stream or video file<br>
     * <br>
     */
    boolean OpenStream(OpencvDecoderLibrary.OpencvVideoDecoder decoder, String rtsp);
    /**
     * @param decoder opencv decoder pointer<br>
     * @param String    rtsp stream or vide file<br>
     * @return true if ok , else false<br>
     * Original signature : <code>bool OpenStream(OpencvVideoDecoder*, const char*)</code><br>
     * <i>native declaration : clip/decoder_wrapper.h:30</i>
     * @brief Open a rtsp stream or video file<br>
     * <br>
     */
    Pointer GetFrame(OpencvDecoderLibrary.OpencvVideoDecoder decoder);
    int GetFrame(OpencvDecoderLibrary.OpencvVideoDecoder decoder, PointerByReference f_out);
    /**
     * @param decoder <br>
     * @return STUDIO_API<br>
     * Original signature : <code>void Close(OpencvVideoDecoder*)</code><br>
     * <i>native declaration : clip/decoder_wrapper.h:49</i>
     * @brief release decoder<br>
     * <br>
     */
    void Close(OpencvDecoderLibrary.OpencvVideoDecoder decoder);

    public static class kestrel_frame extends PointerType {
        public kestrel_frame(Pointer address) {
            super(address);
        }

        public kestrel_frame() {
            super();
        }
    }

    public static class OpencvVideoDecoder extends PointerType {
        public OpencvVideoDecoder(Pointer address) {
            super(address);
        }

        public OpencvVideoDecoder() {
            super();
        }
    }

    ;
}
