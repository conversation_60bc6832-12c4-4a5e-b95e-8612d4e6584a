package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_density_result_t extends Structure {
	/**
	 * <人群密度图，尺寸为160*90<br>
	 * C type : float*
	 */
	public Pointer density_map;
	/** C type : kestrel_size2d_t */
	public kestrel_size2d_t density_map_size;
	/**
	 * < 人头定位点(x,y)<br>
	 * C type : kestrel_point2df_t*
	 */
	public Pointer head_loc_array;
	/** <人头数量 */
	public int head_num;
	/** C type : count_roi_result_list_t */
	public count_roi_result_list_t count_roi_results;
	public crowd_density_result_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("density_map", "density_map_size", "head_loc_array", "head_num", "count_roi_results");
	}
	/**
	 * @param density_map <人群密度图，尺寸为160*90<br>
	 * C type : float*<br>
	 * @param density_map_size C type : kestrel_size2d_t<br>
	 * @param head_loc_array < 人头定位点(x,y)<br>
	 * C type : kestrel_point2df_t*<br>
	 * @param head_num <人头数量<br>
	 * @param count_roi_results C type : count_roi_result_list_t
	 */
	public crowd_density_result_t(Pointer density_map, kestrel_size2d_t density_map_size, Pointer head_loc_array, int head_num, count_roi_result_list_t count_roi_results) {
		super();
		this.density_map = density_map;
		this.density_map_size = density_map_size;
		this.head_loc_array = head_loc_array;
		this.head_num = head_num;
		this.count_roi_results = count_roi_results;
	}
	public crowd_density_result_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_density_result_t implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_density_result_t implements Structure.ByValue {
		
	};
}