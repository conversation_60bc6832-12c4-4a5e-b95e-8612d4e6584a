package com.sensetime.intersense.cognitivesvc.server.entities.sfd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InsightDbIndex {
    String uuid;
    String default_opq_model;
    String object_type;
    int feature_version;
    boolean cache_raw_feature;
    String status;
    String size;
    String creation_time;
    String last_seq_id;
    String last_retrained_seq_id;
    String worker_id;
    String capacity;
}
