package com.sensetime.intersense.cognitivesvc.server.vaxtor.jna;

import com.sun.jna.Callback;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
/**
 * JNA Wrapper for library <b>vaxtor_types</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Vaxtor_typesLibrary extends Library {
    String JNA_LIBRARY_NAME = "vaxtor_types";
    NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Vaxtor_typesLibrary.JNA_LIBRARY_NAME);
    Vaxtor_typesLibrary INSTANCE = Native.loadLibrary(Vaxtor_typesLibrary.JNA_LIBRARY_NAME, Vaxtor_typesLibrary.class);
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAX_STRING_BUFFER = (int)128;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_RESERVED = (int)0x00000000;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_GRAMMAR_STRICT = (int)0x00000002;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_MIN_NUM_CHARS = (int)0x00000003;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_MAX_NUM_CHARS = (int)0x00000004;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_MIN_CHARS_HEIGHT = (int)0x00000005;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_MAX_CHARS_HEIGHT = (int)0x00000006;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_MAX_SAMEPLATE_DIST = (int)0x00000007;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_MIN_SAMEPLATE_TIMEOUT = (int)0x00000008;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_COUNTRY_CLASS = (int)0x00000009;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_VEHICLE_CLASS = (int)0x0000000A;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_OCR = (int)0x0000000B;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_PLATE_DETECTOR = (int)0x0000000C;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_PLATE_CROPPER = (int)0x0000000D;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_MAKE_MODEL = (int)0x0000000E;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_VEHICLE_COLOR = (int)0x0000000F;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_VEHICLE_DETECTOR = (int)0x00000010;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    public static final int VAXADV_SEN_VEHICLE_CROPPER = (int)0x00000011;
    /** <i>native declaration : header/vaxtor_types.h</i> */
    interface event_plate_info extends Callback {
        void apply(tEventPlateInfo event_plate_info, Pointer user_info);
    };
}
