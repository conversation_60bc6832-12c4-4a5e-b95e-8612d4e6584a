package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;

/**
 * JNA Wrapper for library <b>kestrel_error</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_errorLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_errorLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_errorLibrary INSTANCE = (Kestrel_errorLibrary)Native.load(Kestrel_errorLibrary.JNA_LIBRARY_NAME, Kestrel_errorLibrary.class);
	/**
	 * @return Error message string<br>
	 * Original signature : <code>char* kestrel_error_info(k_err)</code><br>
	 * <i>native declaration : include/kestrel_error.h:12</i>
	 */
	String kestrel_error_info(int errcode);
}
