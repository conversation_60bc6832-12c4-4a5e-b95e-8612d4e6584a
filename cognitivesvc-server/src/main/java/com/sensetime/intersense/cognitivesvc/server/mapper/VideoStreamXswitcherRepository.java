package com.sensetime.intersense.cognitivesvc.server.mapper;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamXswitcher;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
@Repository
@Tag(name = "VideoStreamXswitcherRepository", description = "video_stream_xswtitcher data jpa")
public interface VideoStreamXswitcherRepository extends JpaRepositoryImplementation<VideoStreamXswitcher, Integer>{
	
	public List<VideoStreamXswitcher> findByDeviceIdIn(Collection<String> deviceIds);
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByDeviceIdIn(Collection<String> deviceIds);
	
	@Query(value = "select x.deviceId from VideoStreamXswitcher x group by x.deviceId")
	public List<String> queryDeviceIds();
	
	@Transactional
	@Query("update VideoStreamXswitcher i set i.dispatchDesc = ?1")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int updateDispatchDesc(String dispatchDesc);
	
	@Transactional
	@Query("update VideoStreamXswitcher i set i.dispatchDesc = ?1 where i.type in (1, 2, 3) and i.deviceId = ?2")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int updateDispatchDescByDeviceId(String dispatchDesc, String whereDeviceId);
}
