package com.sensetime.intersense.cognitivesvc.server.utils;

import java.util.concurrent.ThreadLocalRandom;

import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NvJpgDecoder {
	
	private static Pointer[] imagesharp_annotators = new Pointer[Utils.instance.nvCoderCount];
	
	/**用nvdecoder解码jpg文件
	 */
	public static Pointer decode_image_as_jpg(String image_path) {
		initialize();
		
		Pointer input = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_object(input, "id", KestrelApi.keson_create_int(0));
		
		Pointer images_array = KestrelApi.keson_create_array();
		KestrelApi.keson_add_item_to_object(input, "image_paths", images_array);
		
		Pointer image = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_array(images_array, image);
		KestrelApi.keson_add_item_to_object(image, "path", KestrelApi.keson_create_string(image_path));
		
		int annotatorIndex = ThreadLocalRandom.current().nextInt(imagesharp_annotators.length);
		PointerByReference out = new PointerByReference();
		synchronized(imagesharp_annotators[annotatorIndex]) {
			KestrelApi.kestrel_annotator_process(imagesharp_annotators[annotatorIndex], input, out);
		}
		
		Pointer images = KestrelApi.keson_get_object_item(out.getValue(), "images");
		if(KestrelApi.keson_array_size(images) <= 0) {
			KesonUtils.kesonDeepDelete(out);
			return null;
		}
		
		PointerByReference frame = new PointerByReference();
		KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(images, 0), "image"), frame);
		
		Pointer result = FrameUtils.ref_frame(frame.getValue());
		KesonUtils.kesonDeepDelete(input);
		KesonUtils.kesonDeepDelete(out);
		
		return result;
	}
	
	private static void initialize() {
		Initializer.bindDeviceOrNot();
		if(imagesharp_annotators[0] != null)
			return ;
		
		synchronized(NvJpgDecoder.class) {
			if(imagesharp_annotators[0] != null)
				return ;
			
			Initializer.bindDeviceOrNot();

			String config = "{\"type\":\"decode\"}";
			log.info("loading model [imagesharp], config is " + config);
			
			for(int index = 0; index < imagesharp_annotators.length; index ++)
				imagesharp_annotators[index] = KestrelApi.kestrel_annotator_open("imagesharp", config);
		}
	}
}
