package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;
/**
 * JNA Wrapper for library <b>kestrel_crowd_oversea</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_crowd_overseaLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_crowd_max";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_crowd_overseaLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_crowd_overseaLibrary INSTANCE = (Kestrel_crowd_overseaLibrary)Native.load(Kestrel_crowd_overseaLibrary.JNA_LIBRARY_NAME, Kestrel_crowd_overseaLibrary.class);
	/**
	 * <i>native declaration : include/crowd_oversea_define.h</i><br>
	 * enum values
	 */
	public static interface crowd_direct_e {
		/**
		 * 同心<br>
		 * <i>native declaration : include/crowd_oversea_define.h:103</i>
		 */
		public static final int HOMOCENTRIC = 0;
		/** <i>native declaration : include/crowd_oversea_define.h:104</i> */
		public static final int SYNTROPIC = 1;
		/** <i>native declaration : include/crowd_oversea_define.h:105</i> */
		public static final int LEFT = 2;
		/** <i>native declaration : include/crowd_oversea_define.h:106</i> */
		public static final int RIGHT = 3;
		/** <i>native declaration : include/crowd_oversea_define.h:107</i> */
		public static final int AWAY = 4;
		/** <i>native declaration : include/crowd_oversea_define.h:108</i> */
		public static final int TOWARD = 5;
	};
	/**
	 * <i>native declaration : include/crowd_oversea_define.h</i><br>
	 * enum values
	 */
	public static interface crowd_alarm {
		/**
		 * <正常，无报警发生<br>
		 * <i>native declaration : include/crowd_oversea_define.h:129</i>
		 */
		public static final int NORMAL = 0;
		/**
		 * <相距过近;过近行为需满足一定时间跨度才会触发该类报警<br>
		 * <i>native declaration : include/crowd_oversea_define.h:130</i>
		 */
		public static final int OVERCLOSE = 1;
	};
	/**
	 * <i>native declaration : include/crowd_oversea_define.h</i><br>
	 * enum values
	 */
	public static interface crowd_queue_status {
		/**
		 * 刚开始排队<br>
		 * <i>native declaration : include/crowd_oversea_define.h:158</i>
		 */
		public static final int START = 0;
		/**
		 * <排队中<br>
		 * <i>native declaration : include/crowd_oversea_define.h:159</i>
		 */
		public static final int ONGOING = 1;
		/**
		 * 通过，排队结束<br>
		 * <i>native declaration : include/crowd_oversea_define.h:160</i>
		 */
		public static final int PASS = 2;
	};
	/** <i>native declaration : include/crowd_oversea_define.h</i> */
	public static final int COORD_NUM = (int)1024;
	/** <i>native declaration : include/crowd_oversea_define.h</i> */
	public static final int CROWD_FLOCK_NUM = (int)1024;
	/**
	 * @return 返回的schema字符串<br>
	 * Original signature : <code>char* crowd_get_analyzer_config_schema()</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:19</i>
	 */
	String crowd_get_analyzer_config_schema();
	/**
	 * @return 调用成功则，返回正确的句柄，否则返回空指针<br>
	 * Original signature : <code>crowd_analyzer_handle crowd_analyzer_create(const char*)</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:26</i>
	 */
	Pointer crowd_analyzer_create(String config);
	/**
	 * @return 返回的schema字符串<br>
	 * Original signature : <code>char* crowd_get_context_config_schema()</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:31</i>
	 */
	String crowd_get_context_config_schema();
	/**
	 * @note 当有多路视频同时处理时，同一块卡上不同的视频流的ctx_id不能重复<br>
	 * Original signature : <code>k_err crowd_analyzer_create_context(crowd_analyzer_handle, int64_t, const char*)</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:40</i>
	 */
	int crowd_analyzer_create_context(Pointer handle, long ctx_id, String context_config);
	/**
	 * 区分视频流,输出结果的顺序和视频帧的顺序保持一致<br>
	 * Original signature : <code>k_err crowd_analyzer_update_batch(crowd_analyzer_handle, kestrel_frame[], int64_t[], int32_t, crowd_analyze_result_t**, int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:54</i><br>
	 */
	int crowd_analyzer_update_batch(Pointer handle, Pointer batch_frame, Pointer ctx_id, int frame_num, PointerByReference result, IntByReference result_num);
	/**
	 * 区分视频流,输出结果的顺序和视频帧的顺序保持一致<br>
	 * Original signature : <code>k_err crowd_analyzer_update_batch(crowd_analyzer_handle, kestrel_frame[], int64_t[], int32_t, crowd_analyze_result_t**, int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:54</i>
	 */
	int crowd_analyzer_update_batch(Pointer handle, Pointer batch_frame, Pointer ctx_id, int frame_num, Pointer result, IntByReference result_num);
	/**
	 * @return 调用成功返回KESTREL_OK,否则返回对应的错误码<br>
	 * Original signature : <code>k_err crowd_get_labeled_person_info(crowd_analyzer_handle, int64_t, char*, int*)</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:65</i><br>
	 */
	int crowd_get_labeled_person_info(Pointer handle, long ctx_id, Pointer result, IntByReference gen_status);
	/**
	 * @param result_num 结果的条数<br>
	 * Original signature : <code>void crowd_analyzer_release_update_result(crowd_analyze_result_t*, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:72</i>
	 */
	void crowd_analyzer_release_update_result(Pointer result, int result_num);
	/**
	 * @param[in] ctx_id 视频流分析上下文序号<br>
	 * Original signature : <code>void crowd_analyzer_free_context(crowd_analyzer_handle, int64_t)</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:78</i>
	 */
	void crowd_analyzer_free_context(Pointer handle, long ctx_id);
	/**
	 * @param[in] handle 人群分析句柄<br>
	 * Original signature : <code>void crowd_analyzer_destroy(crowd_analyzer_handle)</code><br>
	 * <i>native declaration : include/kestrel_crowd_oversea.h:83</i>
	 */
	void crowd_analyzer_destroy(Pointer handle);
}