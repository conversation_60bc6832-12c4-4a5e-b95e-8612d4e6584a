package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.ptr.IntByReference;
import java.nio.IntBuffer;
/**
 * J<PERSON> Wrapper for library <b>kestrel_atomic</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */
public interface Kestrel_atomicLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "xxxxxxx";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_atomicLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_atomicLibrary INSTANCE = (Kestrel_atomicLibrary)Native.load(Kestrel_atomicLibrary.JNA_LIBRARY_NAME, Kestrel_atomicLibrary.class);
	/**
	 * @return The previous value of the atomic<br>
	 * Original signature : <code>int32_t kestrel_atomic_add(volatile int32_t*, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:14</i><br>
	 * @deprecated use the safer methods {@link #kestrel_atomic_add(java.nio.IntBuffer, int)} and {@link #kestrel_atomic_add(com.sun.jna.ptr.IntByReference, int)} instead
	 */
	@Deprecated 
	int kestrel_atomic_add(IntByReference a, int value);
	/**
	 * @return The previous value of the atomic<br>
	 * Original signature : <code>int32_t kestrel_atomic_add(volatile int32_t*, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:14</i>
	 */
	int kestrel_atomic_add(IntBuffer a, int value);
	/**
	 * @return The previous value of the atomic<br>
	 * Original signature : <code>int32_t kestrel_atomic_inc(volatile int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:20</i><br>
	 * @deprecated use the safer methods {@link #kestrel_atomic_inc(java.nio.IntBuffer)} and {@link #kestrel_atomic_inc(com.sun.jna.ptr.IntByReference)} instead
	 */
	@Deprecated 
	int kestrel_atomic_inc(IntByReference a);
	/**
	 * @return The previous value of the atomic<br>
	 * Original signature : <code>int32_t kestrel_atomic_inc(volatile int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:20</i>
	 */
	int kestrel_atomic_inc(IntBuffer a);
	/**
	 * @return The previous value of the atomic<br>
	 * Original signature : <code>int32_t kestrel_atomic_dec(volatile int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:26</i><br>
	 * @deprecated use the safer methods {@link #kestrel_atomic_dec(java.nio.IntBuffer)} and {@link #kestrel_atomic_dec(com.sun.jna.ptr.IntByReference)} instead
	 */
	@Deprecated 
	int kestrel_atomic_dec(IntByReference a);
	/**
	 * @return The previous value of the atomic<br>
	 * Original signature : <code>int32_t kestrel_atomic_dec(volatile int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:26</i>
	 */
	int kestrel_atomic_dec(IntBuffer a);
	/**
	 * @return The previous value of the atomic<br>
	 * Original signature : <code>void kestrel_atomic_set(volatile int32_t*, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:33</i><br>
	 * @deprecated use the safer methods {@link #kestrel_atomic_set(java.nio.IntBuffer, int)} and {@link #kestrel_atomic_set(com.sun.jna.ptr.IntByReference, int)} instead
	 */
	@Deprecated 
	void kestrel_atomic_set(IntByReference a, int newvalue);
	/**
	 * @return The previous value of the atomic<br>
	 * Original signature : <code>void kestrel_atomic_set(volatile int32_t*, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:33</i>
	 */
	void kestrel_atomic_set(IntBuffer a, int newvalue);
	/**
	 * @return The value of the atomic<br>
	 * Original signature : <code>int32_t kestrel_atomic_get(volatile int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:39</i><br>
	 * @deprecated use the safer methods {@link #kestrel_atomic_get(java.nio.IntBuffer)} and {@link #kestrel_atomic_get(com.sun.jna.ptr.IntByReference)} instead
	 */
	@Deprecated 
	int kestrel_atomic_get(IntByReference a);
	/**
	 * @return The value of the atomic<br>
	 * Original signature : <code>int32_t kestrel_atomic_get(volatile int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_atomic.h:39</i>
	 */
	int kestrel_atomic_get(IntBuffer a);
}
