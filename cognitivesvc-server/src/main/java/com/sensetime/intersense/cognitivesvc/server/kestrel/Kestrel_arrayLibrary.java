package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_array_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

/**
 * JNA Wrapper for library <b>kestrel_array</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_arrayLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_arrayLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_arrayLibrary INSTANCE = (Kestrel_arrayLibrary)Native.load(Kestrel_arrayLibrary.JNA_LIBRARY_NAME, Kestrel_arrayLibrary.class);
	/**
	 * @note This function allocate a kestrel_array,<br>
	 * Original signature : <code>kestrel_array kestrel_array_alloc(uint8_t, size_t, size_t)</code><br>
	 * <i>native declaration : include/kestrel_array.h:32</i>
	 */
	kestrel_array_t kestrel_array_alloc(byte keson_code, long element_size, long element_count);
	/**
	 * @brief create a kestrel array with data<br>
	 * Original signature : <code>kestrel_array kestrel_array_make(uint8_t, size_t, size_t, void*, kestrel_buf_finalizer, void*)</code><br>
	 * <i>native declaration : include/kestrel_array.h:36</i>
	 */
	kestrel_array_t kestrel_array_make(byte keson_code, long element_size, long element_count, Pointer data, Pointer finalizer, Pointer ud);
	/**
	 * are going to modify its payload data.<br>
	 * Original signature : <code>kestrel_array kestrel_array_ref(kestrel_array)</code><br>
	 * <i>native declaration : include/kestrel_array.h:46</i>
	 */
	kestrel_array_t kestrel_array_ref(kestrel_array_t in);
	/**
	 * Original signature : <code>kestrel_array kestrel_array_duplicate(kestrel_array)</code><br>
	 * <i>native declaration : include/kestrel_array.h:49</i>
	 */
	kestrel_array_t kestrel_array_duplicate(kestrel_array_t in);
	/**
	 * @param[in,out] array kestrel_array pointer going to be freed<br>
	 * Original signature : <code>void kestrel_array_free(kestrel_array*)</code><br>
	 * <i>native declaration : include/kestrel_array.h:54</i>
	 */
	void kestrel_array_free(PointerByReference array);
}
