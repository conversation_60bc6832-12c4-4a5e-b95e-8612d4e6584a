package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * JNA Wrapper for library <b>kestrel_error_def</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_error_defLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_error_defLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_error_defLibrary INSTANCE = (Kestrel_error_defLibrary)Native.load(Kestrel_error_defLibrary.JNA_LIBRARY_NAME, Kestrel_error_defLibrary.class);
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int KESTREL_OK = (int)0;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int KESTREL_ERR = (int)0x4B;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int KESTREL_E_CORE = (int)0x00;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_GLOBAL_LIB = (int)0x7ff;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_GLOBAL_MODULE = (int)0xf;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_MODULE = (int)0xe;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_CONNECTION_MODULE = (int)0xd;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALIDARG = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffff) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HANDLE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffe) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_OUTOFMEMORY = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffd) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffc) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_DELNOTFOUND = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffb) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALID_PIXEL_FORMAT = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffa) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_FILE_NOT_FOUND = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff9) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALID_FILE_FORMAT = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff8) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_UNSUPPORTED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfc18) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_FILE_EXPIRE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff7) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_READ_FILE_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff6) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_VERIFY_REG_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff5) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALID_AUTH = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff3) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALID_APPID = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff2) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_AUTH_EXPIRE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff1) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_UUID_MISMATCH = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff0) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_AUTH_CONNECT_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffef) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_AUTH_TIMEOUT = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffee) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_AUTH_INVALID = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffed) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_LICENSE_IS_NOT_ACTIVABLE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffec) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ACTIVE_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffeb) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ACTIVE_CODE_INVALID = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffea) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PRODUCT_VERSION_MISMATCH = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe9) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PLATFORM_NOTSUPPORTED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe8) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_UNZIP_FAILED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe7) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SUBMODULE_NON_EXIT = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe6) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_NO_NEED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe5) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe4) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_CODE_INVALID = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe3) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_CONNECT_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe2) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_RET_UNRECOGNIZED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe1) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HW_AUTH_INIT_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe0) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HW_AUTH_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffdf) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_VERIFY_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffde) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_VERIFY_EXPIRE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffdd) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_NO_LICENSE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffdc) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HW_REG_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffdb) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PRODUCT_VERSION_FAILED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffda) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_LIB_EXIST = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffd9) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_CONNECTION_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xd) & 0xf) << 16 | ((0xffff) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_LIB = (int)0x1;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_LICENSE_LIB = (int)0x7F;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_MODULE_ONLINEAUTH_MODULE = (int)0x1;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_LICENSE_SENTINEL_MODULE = (int)0x0;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_UDID = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0xffff) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_FORMAT_HEADER = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0xfffe) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_INVALID_URL = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0xfffd) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HEARTBEAT_CONNECT_FAIL = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffff) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HEARTBEAT_INVALID_RESPONSE = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffe) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_PARSE_XML = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8000) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_CONNECT_FAIL = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8001) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_V2C_MISSING = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8002) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_INVALID_RESPONSE = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8003) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_FINGERPRINT_MISMATCH = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8004) & 0xffff));
}
