package com.sensetime.intersense.cognitivesvc.server.entities.sfd;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 多数据库检索响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FaceRecognitionMultiDbResponse {
    
    private List<ResultItem> results;
    private List<SearchResult> search_results;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResultItem {
        private Integer code;
        private String error;
        private String status;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchResult {
        private String db_id;
        private List<SimilarResult> similar_results;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SimilarResult {
        private Item item;
        private Double score;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        private String id;
        private String seq_id;
        private Object feature;
        private String image_id;
        private String extra_info;
        private Object meta_data;
        private String key;
    }
}
