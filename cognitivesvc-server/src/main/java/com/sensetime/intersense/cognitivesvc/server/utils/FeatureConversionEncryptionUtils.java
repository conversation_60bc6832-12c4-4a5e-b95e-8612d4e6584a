package com.sensetime.intersense.cognitivesvc.server.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.gson.Gson;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.UUID;

public class FeatureConversionEncryptionUtils {
    static int magicNumber = 0x31464f53;
    static int version = 25400;
    static int versionBody = 15600;
    static int flag = 4;
    static int dim = 256;
    static int objectType = 0;

    static int reserved1 = 0;
    static int reserved2 = 0;
    /*
    将加密信息添加头文件
     */
    public static byte[] addHeader(byte[] featureBytes){
        int dataLen = featureBytes.length;
        ByteBuffer buffer = ByteBuffer.allocate(32+featureBytes.length);
        buffer.order(ByteOrder.LITTLE_ENDIAN); // 设置为小端字节顺序
        buffer.putInt(magicNumber);
        buffer.putInt(version);
        buffer.putInt(dataLen);
        buffer.putInt(dim);
        buffer.putInt(objectType);
        buffer.putInt(flag);
        buffer.putInt(reserved1);
        buffer.putInt(reserved2);
        buffer.put(featureBytes);
        return buffer.array();
    }

    public static byte[] addHeaderBody(byte[] featureBytes){
        int dataLen = featureBytes.length;
        ByteBuffer buffer = ByteBuffer.allocate(32+featureBytes.length);
        buffer.order(ByteOrder.LITTLE_ENDIAN); // 设置为小端字节顺序
        buffer.putInt(magicNumber);
        buffer.putInt(versionBody);
        buffer.putInt(dataLen);
        buffer.putInt(dim);
        buffer.putInt(objectType);
        buffer.putInt(flag);
        buffer.putInt(reserved1);
        buffer.putInt(reserved2);
        buffer.put(featureBytes);
        return buffer.array();
    }

    /**
     * 加密特征值
     * @param feature 特征值
     * @return
     * @throws Exception
     */
    public static byte[] encodeBlob(String feature,   String  key ) {
       // Gson gson = new Gson();
        SecureRandom secureRandom = new SecureRandom();
        byte[] iv = new byte[16]; // IV 大小通常是 16 字节
        secureRandom.nextBytes(iv);
        iv = new byte[]{82, 37, 109, 83, 71, 23, 20, 65, 37, 122, 12, 21, 54, 82, 74, 24}; // 先写死试试
        float[] featureFloat = stringToFeature(feature); // 将string特征值转换为float[]
        featureNormalize(featureFloat); // 归一化处理
//        System.out.println("origin Raw: "+gson.toJson(featureFloat));
        byte[] messageBuf = new byte[featureFloat.length*4];

        putFloat32(messageBuf,  featureFloat);

        if (messageBuf.length % 16 != 0) {
            throw new RuntimeException("ET2 padding not supported");
        }
        byte[] value= new byte[0];// 加密值
        try {
            value = cbcEncryption(key, iv, messageBuf);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        byte[] encryptionMessage = new byte[iv.length+value.length]; // 将IV向量和加密之后数据合并
        System.arraycopy(iv, 0, encryptionMessage, 0, iv.length);
        System.arraycopy(value, 0, encryptionMessage, iv.length, value.length);
//        String hexString = toHexString(encryptionMessage);
//        System.out.println("Encoded Blob (Hex): " + hexString);
        return encryptionMessage;
    }

    private static String toHexString(byte[] array) {
        BigInteger bi = new BigInteger(1, array);
        return String.format("%0" + (array.length << 1) + "X", bi);
    }
    // 特征值转 float[]
    public static float[] stringToFeature(String feature) {
        if (feature == null)
            return null;
        try {
            FloatBuffer floatBuffer = ByteBuffer.wrap(Base64.getDecoder().decode(URLDecoder.decode(feature, "utf-8"))).asFloatBuffer();
            float result[] = new float[floatBuffer.capacity()];
            floatBuffer.get(result);
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 归一化
     *
     * @param feature
     */
    public static void featureNormalize(float[] feature) {
        double length = 0;
        int featLen = feature.length;

        for (int i = 0; i < featLen; i++) {
            // 计算特征向量的平方和
            length += (double) feature[i] * feature[i];
        }

        // 计算 Euclidean 范数
        length = Math.sqrt(length);

        if (length == 0) {
            return;
        }

        for (int i = 0; i < featLen; i++) {
            // 归一化特征向量
            feature[i] = feature[i] / (float) length;
        }
    }

    public static byte[] float32sToBytes(float[] fs) {
        ByteBuffer buf = ByteBuffer.allocate(fs.length * 4);
        buf.order(ByteOrder.LITTLE_ENDIAN);
        for (float f : fs) {
            buf.putFloat(f);
        }
        return buf.array();
    }

    public static float[] bytesToFloat32(byte[] bs) {
        ByteBuffer buf = ByteBuffer.wrap(bs);
        buf.order(ByteOrder.LITTLE_ENDIAN);
        float[] fs = new float[bs.length / 4];
        for (int i = 0; i < fs.length; i++) {
            fs[i] = buf.getFloat();
        }
        return fs;
    }

    public static int putFloat32(byte[] buf,  float[] fs) {
        int off = 0;
        for (float f : fs) {
            ByteBuffer.wrap(buf, off, 4).order(ByteOrder.LITTLE_ENDIAN).putFloat(f);
            off += 4;
        }
        return off;
    }

    public static int putFloat16From32(byte[] buf,  float[] fs) {
        int off = 0;
        for (float f : fs) {
            short x = newFloat16(f);
            ByteBuffer.wrap(buf, off, 2).order(ByteOrder.LITTLE_ENDIAN).putShort(x);
            off += 2;
        }
        return off;
    }

    public static int persistedBlobSize(int dim, int flag) {
        int flen = dim * 4;
        if ((flag & 1) != 0) {
            flen /= 2;
        }
        if ((flag & 4) != 0) {
            flen += 16;
        }
        return flen;
    }

    public static short newFloat16(float f) {
        int i = Float.floatToRawIntBits(f);
        int sign = (i >> 31) & 0x1;
        int exp = (i >> 23) & 0xff;
        int exp16 = (short) (exp - 127 + 15);
        int frac = (i >> 13) & 0x3ff;

        if (exp == 0) {
            exp16 = 0;
        } else if (exp == 0xff) {
            exp16 = 0x1f;
        } else {
            if (exp16 > 0x1e) {
                exp16 = 0x1f;
                frac = 0;
            } else if (exp16 < 0x01) {
                exp16 = 0;
                frac = 0;
            }
        }

        short f16 = (short) ((sign << 15) | (exp16 << 10) | frac);
        return f16;
    }

    // AES加密
    public static byte[] cbcEncryption(String key, byte[] iv, byte[] feature) throws Exception {
        // AES-128 key (16 bytes)
        byte[] keyBytes = key.getBytes();
        SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");

        // 向量值
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);

        // Encrypt the data
        byte[] encryptedData = cipher.doFinal(feature);
        return encryptedData;
    }

    public static void main(String[] args) throws Exception {
        String uuidStr = UUID.randomUUID().toString().replace("-", "");
        String message = "vYdCGL0QJZO922PGvbtrGj0V70g9uRNdPQSAhTz%2FW4c%2BF1ASvYTHFD1RVG09gAX2vYtBLr1QXXk9%2FZDoPYdCGDsTCm4%2BFuY8PICBcD0oIBS94iRvvihDWz215%2FQ8OcPCvYPzaDx2H9q8IPWVPK4NEL0WwvU8Rts9PRQBYTySE3o9AfPdO%2B%2FJBzz7owG9H9tavIht973aAv07uySMPctEKTuQ%2BT88INJOvS%2BRITrNm%2BY9pykgPb01ujwfB669qwTvPVw3cjucRhu95DWdPaBFMLx%2Ffs87S63%2FvbOQN7uoieq99SjnPdQ5Rz4Tl4s9iKLiPV109b0%2FI6E9oaX6Padvrz4QbCI%2BC5lgPNR%2F1j01FEg9dZK9vQG%2B8j11KOc87XFKvPhUUL4QSNu8yMkku3FwYD16Qjc85OYCvR9OPb2O6BG9ifIIvbf5Irtrpqu7efuovSZ4vL5gE0A7iPsUPYyzm7vxtu%2B9JK4cPMZxZz1t%2Fmg9WVKYvUYHkTs8G3%2B6Rts9PcoGp72QJZM%2BFjXXPM1VVzcewR88edhhvdqQGj0If5s81Qz0vAdlYL35kdI9HRnHvVYnLr3vpcC9SpPEOsSmyD0buP49abjEvIrXWD1L0UY9XiVZO78job2ZhIg90b5DvYUw6j0rS329c%2BtkPUkPsz2DVKc9rr11PYEOjTz8MB68iFxTvYLHiT0fTj29%2FxT5vWh7Qb0NC849y9FGvcgYwDtYojO9Qgh8ves81TyfB649WS9QvRdzWbuVzAG878kHvLnDwj18vTs9ERyHvB09Djy1N4%2B85J9zvNBdeTxIySS9YObtPbwbfzyVPuS7bMDmPX6H2z0Pu709SDwHPL65yzv7FeO%2BG08nPe8Yo7xyIMW8G%2F%2BMvPgNwb1K2lO9cxe4vToKUT2%2BLK49d%2Bp6PD65y71uIa%2B8v%2FdNvISjzb18dq07GCO%2BPb4srrrRDd68ZOYCu2Ete7152GE9QnJSPLhi%2Bb0NC8460vvFPbf5Ir1T8rm9C9%2FvPK5TnzxP0Fw948vHOwI6bL3wv%2Fu86oxwvT7dEr0umi69nK%2FxPUnjXz2n%2FM29TDscvCMqC7z1TC49qGajvVx%2BAb22Ucq9Vb1YvOTmAr0eNAK8y%2FSOPCmA3jzY6MI864Nju5e56L2UJKk96J6JPW6Lhb3Be169OGL5PAGtTruF8vM9ZiOEPOR8LL0Nh0c9NrugPXxTZTyTl4u9Orq2PeqMcL0lyFc9TOuBPWM%2Bqrw2mFk9MKtcvQXPq72dGce5lLHGvJk9%2BT0KFU89Yz6qvRafrTz20D88gAX2PThi%2Bbz2Zmm8FjXXuzePTD0j2nA7u0fTvaIP0L0%2BLK69pERGvXyZ9D2Ofjs9aMHQvINDAw%3D%3D";
//        String message = "PmgLOj10UkG81M8mPZFraz1s0YS8gXV7vQBvkD4G2Oy9vvuLPdAryL2C9aE9Hw%2BsvMnlWb23NPY9frBePUGTEr10DGk%2BDQ3mPGZoKDyRa2u9vZ5SvBpsTr2G6mK7jgJbPEhlMz2Zd9k8g15mvRC8zj0PgoG8qOIYvWeidTzQcaE9LjQTPWeidT3YWyK9uR3guk8UZ70%2FHnc9ZtDsPTrA8ryQDjE9EHb2vfTd8j0DtbQ8j6VtvMyfzD1ZuFy9Lr%2FDvVeshr3si6u%2BPIbwuehz%2Fr2IEzo91tr9vgakirzGE4Q6WziCvAKeU7sDxyo9ZosUPN5%2Bpj3o%2F687rlb%2FvMwUGz1Tt8W8yXyUOarLAjsoVmc%2BCwIPvl2KMbycvf28%2BnXEvOmuTD0bPdc79Ua2PeF%2B8rvQTrW8oYRHPD7Yn7zO8Xs9ZvPYvYMqBLunp8o9MBz9vhGOV715xyi8lr1mvbnMfb0EUtu8yx%2BmvSn5eb3HcL68kWtrPYDG37z86l8%2BLAVQPIDG3z01KR89%2FTA4PR2Phr3a8qo8e2o6PK0%2Fnb2Spbk8nbJyvYp2X71prkw8DII1PgJHBL2A%2B0E9g%2FuNvSazVb2WMbU9LyiIPcBYxbzdrR29%2BrudvWbz2D2pJ%2FA81PISPQMHFzwCr8k9ndVevT%2FNFD1MfOA749ChvUfZgjwj%2BOK8XfL1vEqT9T0xV0q97V00vM5C3r3XINW9A6Q%2BPaaQaTvYODa9kLzOvhQl3j2abE680jefPdGr7r0d1V67n75JvJm9sb1OiLY9Op4Gvab5LjzdijG9k5ouPaRhpr3QcaG8uR3gvIMYjT0asic8kY5XvZa9ZrzIZTO90JSNvcjN972EUts9U7fFPR1smr26ElW9J2HyPcN7%2Fb1x3aY9eK%2FGvQLBPzyL9oS43%2F7MOpN3Qr3MWfQ8mGB4PI4lRzyTMWk9ESWTPSIy5L2Qdva9BA0DPVPasT0lEEM9sD%2FpvcKHiD2n7aM9Ini8vSxLKLzXZq28HWyavWc5sT0OAls9JRBDPI5IM70d%2BEu9ZQruO3mBT72Td0K8qxDbu%2Bjcw71nXJ29bcX5u0dwvr1zxpC8%2FBjWPQNeZr22HZQ8FYMYu91nRTx0dS09cVH1vJSOoz2EZFG7P0FkvWfoTr3Iqwu8g4FSOyviZDwzy%2BU91k9MvQfe171TLBQ97NGEvOLcK7zVw5u7FGu3vIylITzAEu28h1MnvSmzoTzRiQI8hIc9PO2jDbzZuFy9qbOhvXL1BzvRiQK9prNVuzXXvLwX97M9jrD4O7v7QL2EZFE8ZZafPOUK7r0Vgxg9hIc9vSBJ%2Bb1p9CS9lRpUvH3e1TuqywK9qHlTO640EzsQMR49IGzmO0k2vA%3D%3D";
        String key = "2NVp7Lc60V0644j9";
        Base64.Encoder encoder = Base64.getEncoder();
        String feature = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeader(
                FeatureConversionEncryptionUtils.encodeBlob(message, key)));
        System.out.println(feature);
        System.out.println(uuidStr);
        String a = "25b7cc0c7719491abdab499f276c3001000000000000000e";
        System.out.println(a.length());
        System.out.println(uuidStr.length());
        String s = uuidStr + IdWorker.getIdStr().substring(0, 16);
        System.out.println(s.length());
        System.out.println(s);

    }

}

