package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * JNA Wrapper for library <b>kestrel_frame_utils_struct</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_frame_utils_structLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_frame_utils_structLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_frame_utils_structLibrary INSTANCE = (Kestrel_frame_utils_structLibrary)Native.load(Kestrel_frame_utils_structLibrary.JNA_LIBRARY_NAME, Kestrel_frame_utils_structLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_frame_utils_struct.h</i><br>
	 * enum values
	 */
	public static interface kestrel_resize_param_e {
		/**
		 * <keep ratio resize*<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:7</i>
		 */
		public static final int KESTREL_KEEP_RATIO_RESIZE = 0;
		/**
		 * <auto calculate resize ratio by dst row and dst col*<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:8</i>
		 */
		public static final int KESTREL_AUTO_RATIO_RESIZE = 1;
	};
	/**
	 * <i>native declaration : include/kestrel_frame_utils_struct.h</i><br>
	 * enum values
	 */
	public static interface kestrel_orientation_e {
		/**
		 * < Rotate frame clockwise 90 degree<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:13</i>
		 */
		public static final int KESTREL_CLOCKWISE_90 = 0;
		/**
		 * < Rotate frame clockwise 180 degree<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:14</i>
		 */
		public static final int KESTREL_CLOCKWISE_180 = 1;
		/**
		 * < Rotate frame clockwise 270 degree<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:15</i>
		 */
		public static final int KESTREL_CLOCKWISE_270 = 2;
	};
	/**
	 * <i>native declaration : include/kestrel_frame_utils_struct.h</i><br>
	 * enum values
	 */
	public static interface kestrel_tensor_color_e {
		/**
		 * < Unknown tensor param<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:22</i>
		 */
		public static final int KESTREL_UNKNOWN_TENSOR = -1;
		/**
		 * < BGR tensor<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:23</i>
		 */
		public static final int KESTREL_BGR_TENSOR = 0;
		/**
		 * < RGB tensor<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:24</i>
		 */
		public static final int KESTREL_RGB_TENSOR = 1;
		/**
		 * < GRAY tensor<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:25</i>
		 */
		public static final int KESTREL_GRAY_TENSOR = 2;
	};
	/**
	 * <i>native declaration : include/kestrel_frame_utils_struct.h</i><br>
	 * enum values
	 */
	public static interface kestrel_frame_enc_format_e {
		/**
		 * < Unknow encode format<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:32</i>
		 */
		public static final int KESTREL_ENC_FMT_UNKNOWN = 0;
		/**
		 * < JPG encode<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:33</i>
		 */
		public static final int KESTREL_ENC_FMT_JPG = 1;
		/**
		 * < BMP encode<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:34</i>
		 */
		public static final int KESTREL_ENC_FMT_BMP = 2;
		/**
		 * < PNG encode<br>
		 * <i>native declaration : include/kestrel_frame_utils_struct.h:35</i>
		 */
		public static final int KESTREL_ENC_FMT_PNG = 3;
	};
	/**
	 * <i>native declaration : include/kestrel_frame_utils_struct.h</i><br>
	 * enum values
	 */
	public static interface kestrel_border_type_e {
		/** <i>native declaration : include/kestrel_frame_utils_struct.h:42</i> */
		public static final int KESTREL_BORDER_CONSTANT = 0;
		/** <i>native declaration : include/kestrel_frame_utils_struct.h:43</i> */
		public static final int KESTREL_BORDER_REPLICATE = 1;
		/** <i>native declaration : include/kestrel_frame_utils_struct.h:44</i> */
		public static final int KESTREL_BORDER_REFLECT = 2;
		/** <i>native declaration : include/kestrel_frame_utils_struct.h:45</i> */
		public static final int KESTREL_BORDER_REFLECT101 = 3;
	};
	/** <i>native declaration : include/kestrel_frame_utils_struct.h</i> */
	public static final int KESTREL_FRAME_TO_TENSOR = (int)0;
	/** <i>native declaration : include/kestrel_frame_utils_struct.h</i> */
	public static final int KESTREL_RESIZE_TO_TENSOR = (int)1;
	/** <i>native declaration : include/kestrel_frame_utils_struct.h</i> */
	public static final int KESTREL_AFFINE_TO_TENSOR = (int)2;
	/** <i>native declaration : include/kestrel_frame_utils_struct.h</i> */
	public static final int KESTREL_PERSPECTIVE_TO_TENSOR = (int)3;
	/** <i>native declaration : include/kestrel_frame_utils_struct.h</i> */
	public static final int KESTREL_TO_TENSOR_MASK = (int)0xF;
	/** <i>native declaration : include/kestrel_frame_utils_struct.h</i> */
	public static final int KESTREL_CHANNEL_NORM = (int)(1 << 4);
	/** <i>native declaration : include/kestrel_frame_utils_struct.h</i> */
	public static final int KESTREL_HISTOGRAM = (int)(1 << 5);
}
