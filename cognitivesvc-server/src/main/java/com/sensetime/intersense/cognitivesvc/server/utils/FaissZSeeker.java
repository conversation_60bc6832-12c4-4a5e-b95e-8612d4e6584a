package com.sensetime.intersense.cognitivesvc.server.utils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.sql.DataSource;

import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.DP;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SP;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@SuppressWarnings("rawtypes")
@Scope("singleton")
@Setter
@Getter
public class FaissZSeeker extends FaissSeeker<FaissZSeeker.SpFeature, FaissZSeeker.DpFeature> implements InitializingBean, DisposableBean{

	@Autowired
	private DataSource dataSource;

	@Setter
	protected volatile float[] pSrcPoint = new float[] {-1.0f, -0.8f, -0.6f, -0.4f, -0.2f, 0.0f, 0.2f, 0.4f, 0.6f, 0.8f, 1.0f };
	
	@Setter
	protected volatile float[] pDstPoint = new float[] { 0.0f,  0.1f,  0.2f,  0.3f,  0.4f, 0.5f, 0.6f, 0.7f, 0.8f, 0.9f, 1.0f };
	
	protected volatile boolean closed = false;
	
	protected volatile Integer dims;
	
	protected String tableName;
	
	protected Connection connection;
	
	protected List<Pair<String, Class>> columnNames = new ArrayList<Pair<String, Class>>();
	
	@Override
	protected String name() { return getClass().getSimpleName() + "-" + beanName; }
	
	@Override
	protected DpFeature convert(SpFeature sp) {
		return DpFeature.builder()
						.id(sp.getId())
						.attributes(sp.getAttributes())
						.feature(stringToFeature(sp.getAttributes().get("image_feature").toString()))
						.build();
	}

	@Override
	protected Integer queryMaxId() {
		if(closed)
			return -1;
		
		try {
			try(PreparedStatement stat = connection.prepareStatement("select max(p.id) from " + tableName + " p")){
				try(ResultSet rs = stat.executeQuery()){
					rs.next();
					return rs.getInt(1);
				}
			}
		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			
			try {				
				if(closed)
					connection = null;
				else
					connection = dataSource.getConnection();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			
			throw new RuntimeException(e);
		}
	}

	@Override
	protected long countSplit(int totalSplitNum, int currentSplitNum) {
		if(closed)
			return -1;
		
		try {
			try(PreparedStatement stat = connection.prepareStatement("select count(p.id) from " + tableName + " p where p.id % " + totalSplitNum + " = " + currentSplitNum + " and p.sts = 0")){
				try(ResultSet rs = stat.executeQuery()){
					rs.next();
					return rs.getInt(1);
				}
			}
		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			
			try {				
				if(closed)
					connection = null;
				else
					connection = dataSource.getConnection();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			
			throw new RuntimeException(e);
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	protected List<SpFeature> querySplit(int start, int end, int totalSplitNum, int currentSplitNum) {
		if(closed)
			return List.of();
		
		try {
			try(PreparedStatement stat = connection.prepareStatement("select * from " + tableName + " p where p.id >= " + start + " and p.id < " + end + " and p.id % " + totalSplitNum + " = " + currentSplitNum + " and p.sts = 0 order by p.id asc")){
				try(ResultSet rs = stat.executeQuery()){
					List<SpFeature> result = new ArrayList<SpFeature>();
					while(rs.next()) {
						Map<String, Object> attributes = new HashMap<String, Object>();
						for(Pair<String, Class> pair : columnNames)
							attributes.put(pair.getLeft(), rs.getObject(pair.getLeft(), pair.getRight()));
						
						result.add(SpFeature.builder().id(rs.getInt("id")).attributes(attributes).build());
					}
					
					if(dims == null && !result.isEmpty()) {//只执行一次 获取库中第一条特征的维度
						float[] feature = stringToFeature(result.get(0).getAttributes().get("image_feature").toString());
						dims = feature.length;
					}
					
					return result;
				}
			}
		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			
			try {				
				if(closed)
					connection = null;
				else
					connection = dataSource.getConnection();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			
			throw new RuntimeException(e);
		}
	}

	@Override
	public synchronized void afterPropertiesSet() throws Exception {
		if(!columnNames.isEmpty())
			return ;
		
		connection = dataSource.getConnection();
		DatabaseMetaData databaseMetaData = connection.getMetaData();
		Set<String> tableNames = new HashSet<String>();
		
		try(ResultSet tables = databaseMetaData.getTables(null , null, "%", null)){
			while(tables.next())
				tableNames.add(tables.getString("TABLE_NAME"));
		}
		
		if(!tableNames.contains(tableName))
			throw new RuntimeException("Table[" + tableName + "] does not exist.");
		
		try(Statement stat = connection.createStatement()){
			try(ResultSet rs = stat.executeQuery("select * from " + tableName + " limit 1")){
				ResultSetMetaData metaData = rs.getMetaData();
				
				for(int index = 1 ; index <= metaData.getColumnCount(); index ++)				
					columnNames.add(new MutablePair<String, Class>(metaData.getColumnName(index), Class.forName(metaData.getColumnClassName(index))));
			}
		}
		try {
			columnNames.stream().filter(column -> "id".equals(column.getLeft()) && (column.getRight() == Integer.class || column.getRight() == Long.class)).findAny().get();
			columnNames.stream().filter(column -> "sts".equals(column.getLeft()) && (column.getRight() == Integer.class || column.getRight() == Long.class)).findAny().get();
			columnNames.stream().filter(column -> "image_feature".equals(column.getLeft()) && column.getRight() == String.class).findAny().get();
		}catch(Exception e) {
			e.printStackTrace();
			
			columnNames = new ArrayList<Pair<String, Class>>();
		}
	}

	@Override
	protected int dims() { return Objects.requireNonNullElse(dims, 256); }
	
	@Override
	public void destroy() {
		closed = true;
		
		try {
			if(connection != null)
				connection.close();
		}catch(Exception e) { }
	}
	
	@Override
	protected float normalize_feature_score(float score) { 
    	if(score <= pSrcPoint[0])
    		return pDstPoint[0];
    	
    	if(score >= pSrcPoint[pSrcPoint.length - 1])
    		return pDstPoint[pDstPoint.length - 1];
    	
    	for(int index = 0 ; index < pSrcPoint.length; index ++ )
    		if(pSrcPoint[index] > score)
    			return (score - pSrcPoint[index - 1]) * (pDstPoint[index] - pDstPoint[index - 1]) / (pSrcPoint[index] - pSrcPoint[index - 1]) + pDstPoint[index - 1];

    	if(Float.isNaN(score))
    		return 0f;
    	
    	throw new RuntimeException("should not be here , score : [" + score + "]");
	}
	
	@Setter
	@Getter
	@Builder
	public static class SpFeature implements SP{
		private Integer id;
		private Map<String, Object> attributes;
	}
	
	@Setter
	@Getter
	@Builder
	public static class DpFeature implements DP{
		public Integer id;
		public float[] feature;
		private Map<String, Object> attributes;
	}
}
