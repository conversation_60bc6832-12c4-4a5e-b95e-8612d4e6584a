package com.sensetime.intersense.cognitivesvc.server.controller;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SeekParam;
import com.sensetime.intersense.cognitivesvc.server.utils.*;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissZSeeker.DpFeature;
import com.sensetime.intersense.cognitivesvc.server.utils.SeekerSpliter.SplitEntity;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

@RestController(value = "zSeekerProvider")
@RequestMapping(value = "/cognitive/zseeker/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "ZSeekerProvider", description = "zseeker")
@ConditionalOnExpression("${zseeker.enabled:false}")
public class ZSeekerProvider {
	
	private static final String prefix = "z_seeker_";
	
	@Value("${database.name}")
	private String dataBaseName;
	
	@Autowired
	private DataSource dataSource;

	@Autowired
	private Broadcaster broadcaster;

	@Autowired
	private ApplicationContext applicationContext;
	
	@Autowired
	private SeekerSpliter seekerSpliter;

    @Operation(summary = "已经开启搜索功能的表", method = "GET")
    @RequestMapping(value = "/seekers", method = RequestMethod.GET)
    public BaseRes<String[]> seekers() throws Exception {
        return BaseRes.success(Arrays.stream(applicationContext.getBeanNamesForType(FaissZSeeker.class)).map(name -> name.replaceAll(prefix, "")).toArray(String[]::new));
    }

    @SuppressWarnings({"unchecked"})
    @Operation(summary = "根据表名和特征进行一比多<广播>", method = "POST")
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public BaseRes<List<Map<String, Object>>> query(@RequestParam String tableName, @RequestParam String feature, @RequestParam(required = false) Integer count, @RequestParam(required = false) Float threshold) {
        SplitEntity splitEntity = seekerSpliter.getSplitEntity();
        if (splitEntity.getTotalSplitNum() <= 0)
            return query0(tableName, feature, count, threshold);

        Map<String, Object> request = Map.of("tableName", tableName, "feature", feature, "count", Objects.requireNonNullElse(count, 1), "threshold", Objects.requireNonNullElse(threshold, 0.5f));
        List<List<Map<String, Object>>> response = broadcaster.postForObject(seekerSpliter.getInstanceTargets(), "/cognitive/zseeker/query/one", request, BaseRes.class)
                .stream()
                .map(res -> (List<Map<String, Object>>) res.getData())
                .collect(Collectors.toList());

        List<Map<String, Object>> result = response.stream().flatMap(List::stream).sorted((l, r) -> -Float.compare((Float) l.get("score"), (Float) r.get("score"))).limit(Objects.requireNonNullElse(count, 1)).collect(Collectors.toList());
        return BaseRes.success(result);
    }

    @Operation(summary = "根据表名创建搜索功能<广播>", method = "POST")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public void create(@RequestParam String tableName) {
        broadcaster.postForObject("", "/cognitive/zseeker/create/one", Map.of("tableName", tableName), String.class);
    }

    @Operation(summary = "根据表名关闭搜索功能<广播>", method = "POST")
    @RequestMapping(value = "/dispose", method = RequestMethod.POST)
    public void dispose(@RequestParam String tableName) {
        broadcaster.postForObject("", "/cognitive/zseeker/dispose/one", Map.of("tableName", tableName), String.class);
    }

    @Operation(summary = "设置比对得分归一化参数<广播>", method = "POST")
    @RequestMapping(value = "/normalize/points", method = RequestMethod.POST)
    public void normalizePoints(@RequestParam String tableName, @RequestParam float[] pSrcPoint, @RequestParam float[] pDstPoint) {
        broadcaster.postForObject("", "/cognitive/normalize/points/one", Map.of("tableName", tableName, "pSrcPoint", pSrcPoint, "pDstPoint", pDstPoint), String.class);
    }

    @SuppressWarnings("rawtypes")
    @Operation(summary = "数据库内可以设置搜索功能表", method = "GET")
    @RequestMapping(value = "/tables", method = RequestMethod.GET)
    public BaseRes<Set<String>> tables() throws Exception {
        Set<String> tableNames = new HashSet<String>();

        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData databaseMetaData = connection.getMetaData();

            try (ResultSet tables = databaseMetaData.getTables(dataBaseName, null, null, new String[]{"TABLE"})) {
                while (tables.next())
                    tableNames.add(tables.getString("TABLE_NAME"));
            }
        }

        tableNames.removeAll(List.of("stranger_pedestrian_feature", "stranger_face_feature", "person_pedestrian_feature", "person_face_feature", "passer_pedestrian_feature", "passer_face_feature"));

        for (Iterator<String> its = tableNames.iterator(); its.hasNext(); ) {
            String tableName = its.next();
            List<Pair<String, Class>> columnNames = new ArrayList<Pair<String, Class>>();

            try (Connection connection = dataSource.getConnection()) {
                try (Statement stat = connection.createStatement()) {
                    try (ResultSet rs = stat.executeQuery("select * from " + tableName + " limit 1")) {
                        ResultSetMetaData metaData = rs.getMetaData();

                        for (int index = 1; index <= metaData.getColumnCount(); index++)
                            columnNames.add(new MutablePair<String, Class>(metaData.getColumnName(index), Class.forName(metaData.getColumnClassName(index))));
                    }
                }

                try {
                    columnNames.stream().filter(column -> "id".equals(column.getLeft()) && (column.getRight() == Integer.class || column.getRight() == Long.class)).findAny().get();
                    columnNames.stream().filter(column -> "image_feature".equals(column.getLeft()) && column.getRight() == String.class).findAny().get();
                    columnNames.stream().filter(column -> "sts".equals(column.getLeft()) && (column.getRight() == Integer.class || column.getRight() == Long.class)).findAny().get();
                } catch (Exception e) {
                    columnNames = new ArrayList<Pair<String, Class>>();
                }
            }

            if (columnNames.isEmpty())
                its.remove();
        }

        return BaseRes.success(tableNames);
    }

    @Operation(summary = "源表重命名成新表, 并源表名重建空表", method = "POST")
    @RequestMapping(value = "/rename/create/table", method = RequestMethod.POST)
    public void renameCreateTable(@RequestParam String srcTable, @RequestParam String dstTable) throws Exception {
        Set<String> tableNames = new HashSet<String>();
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData databaseMetaData = connection.getMetaData();

            try (ResultSet tables = databaseMetaData.getTables(null, null, "%", null)) {
                while (tables.next())
                    tableNames.add(tables.getString("TABLE_NAME"));
            }

            if (!tableNames.contains(srcTable))
                throw new RuntimeException("srcTable[" + srcTable + "] not exists.");

            if (tableNames.contains(dstTable))
                throw new RuntimeException("dstTable[" + dstTable + "] already exists.");

            try (Statement stat = connection.createStatement()) {
                stat.executeUpdate("rename table " + srcTable + " to " + dstTable);
                stat.executeUpdate("create table " + srcTable + " like " + dstTable);
            }
        }
    }

    @Operation(summary = "根据表名和特征进行一比多<单点>", method = "GET", hidden = true)
    @RequestMapping(value = "/query/one", method = RequestMethod.GET)
    public BaseRes<List<Map<String, Object>>> query0(@RequestParam String tableName, @RequestParam String feature, @RequestParam(required = false) Integer count, @RequestParam(required = false) Float threshold) {
        FaissZSeeker handler = (FaissZSeeker) applicationContext.getBean(prefix + tableName);
        List<Pair<DpFeature, Float>> list = handler.find(SeekParam.builder().count(Objects.requireNonNullElse(count, 1)).threshold(Objects.requireNonNullElse(threshold, 0.5f)).feature(FaissSeeker.stringToFeature(feature)).build());
        return BaseRes.success(list.stream()
                .map(pair -> {
                    Map<String, Object> attribute = pair.getLeft().getAttributes();
                    attribute.put("score", pair.getRight());
                    return attribute;
                })
                .collect(Collectors.toList()));
    }

    @Operation(summary = "根据表名创建搜索功能<单点>", method = "POST", hidden = true)
    @RequestMapping(value = "/create/one", method = RequestMethod.POST)
    public synchronized String create0(@RequestBody Map<String, String> attribute) {
        String tableName = attribute.get("tableName");
        if (applicationContext.containsBean(prefix + tableName))
            return Utils.instance.getSeed();

        DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
        BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(FaissZSeeker.class);
        beanDefinitionBuilder.addPropertyValue("tableName", tableName);
        defaultListableBeanFactory.registerBeanDefinition(prefix + tableName, beanDefinitionBuilder.getBeanDefinition());

        applicationContext.getBean(prefix + tableName);

        return Utils.instance.getSeed();
    }

    @Operation(summary = "根据表名关闭搜索功能<单点>", method = "POST", hidden = true)
    @RequestMapping(value = "/dispose/one", method = RequestMethod.POST)
    public synchronized String dispose0(@RequestBody Map<String, String> attribute) {
        String tableName = attribute.get("tableName");
        if (!applicationContext.containsBean(prefix + tableName))
            return Utils.instance.getSeed();

        DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
        defaultListableBeanFactory.removeBeanDefinition(prefix + tableName);

        return Utils.instance.getSeed();
    }

    @Operation(summary = "设置比对得分归一化参数<单点>", method = "POST", hidden = true)
    @RequestMapping(value = "/normalize/points/one", method = RequestMethod.POST)
    public synchronized String normalizePoints0(@RequestBody Map<String, Object> attribute) {
        String tableName = (String) attribute.get("tableName");
        if (!applicationContext.containsBean(prefix + tableName))
            return Utils.instance.getSeed();

        FaissZSeeker seeker = (FaissZSeeker) applicationContext.getBean(prefix + tableName);
        seeker.setPSrcPoint((float[]) attribute.get("pSrcPoint"));
        seeker.setPDstPoint((float[]) attribute.get("pDstPoint"));

        return Utils.instance.getSeed();
    }
}
