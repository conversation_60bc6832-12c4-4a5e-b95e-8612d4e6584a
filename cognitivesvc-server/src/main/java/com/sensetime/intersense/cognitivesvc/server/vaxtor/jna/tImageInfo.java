package com.sensetime.intersense.cognitivesvc.server.vaxtor.jna;

import com.sun.jna.NativeLong;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : header/vaxtor_types.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class tImageInfo extends Structure {
    /**
     * in pixels<br>
     * C type : vx_int32
     */
    public NativeLong _width;
    /**
     * in pixels<br>
     * C type : vx_int32
     */
    public NativeLong _height;
    /**
     * in bytes<br>
     * C type : vx_int32
     */
    public NativeLong _size;
    /**
     * see image formats supported above<br>
     * C type : vx_int32
     */
    public NativeLong _format;
    /**
     * Image data<br>
     * C type : byte*
     */
    public Pointer _image;
    public tImageInfo() {
        super();
    }
    protected List<String> getFieldOrder() {
        return Arrays.asList("_width", "_height", "_size", "_format", "_image");
    }
    /**
     * @param _width in pixels<br>
     * C type : vx_int32<br>
     * @param _height in pixels<br>
     * C type : vx_int32<br>
     * @param _size in bytes<br>
     * C type : vx_int32<br>
     * @param _format see image formats supported above<br>
     * C type : vx_int32<br>
     * @param _image Image data<br>
     * C type : byte*
     */
    public tImageInfo(NativeLong _width, NativeLong _height, NativeLong _size, NativeLong _format, Pointer _image) {
        super();
        this._width = _width;
        this._height = _height;
        this._size = _size;
        this._format = _format;
        this._image = _image;
    }
    public tImageInfo(Pointer peer) {
        super(peer);
    }
    public static class ByReference extends tImageInfo implements Structure.ByReference {

    };
    public static class ByValue extends tImageInfo implements Structure.ByValue {

    };
}
