package com.sensetime.intersense.cognitivesvc.server.entities.sfd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DbInfo {
    //    {
    //      "name": "string",
    //      "db_id": "string",
    //      "default_opq_model": "string",
    //      "object_type": "string",
    //      "feature_version": 0,
    //      "indexes": [
    //        {
    //          "uuid": "string",
    //          "default_opq_model": "string",
    //          "object_type": "string",
    //          "feature_version": 0,
    //          "cache_raw_feature": true,
    //          "status": "INITED",
    //          "size": "string",
    //          "creation_time": "2024-01-24T07:14:22.802Z",
    //          "last_seq_id": "string",
    //          "last_retrained_seq_id": "string",
    //          "worker_id": "string",
    //          "capacity": "string"
    //        }
    //      ],
    //      "description": "string",
    //      "creation_time": "2024-01-24T07:14:22.802Z",
    //      "max_size": "string",
    //      "size": "string"
    //    }

    String name;
    String db_id;
    String default_opq_model;
    String object_type;
    int feature_version;
    List<SfdIndex> indexes;
    String description;
    String creation_time;
    String max_size;
    String size;

}
