package com.sensetime.intersense.cognitivesvc.server.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "sfd")
public class SfdProperties {
    
    private String staticFaceDbServiceHost;
    private String face1N;
    private String dbList;
    private String faceMultiWrapper; // New property for multi-database search endpoint
    private String apiWrapperHost; // New property for API wrapper host URL
    private DbProperties db = new DbProperties();
    private Integer httpRetryCnt = 3;
    private String sfdEncKey;
    
    @Data
    public static class DbProperties {
        private String id;
        private String name = "studioDefaultSfdDb";
        private Integer featureVersion;
        private String strangerName;
    }
} 