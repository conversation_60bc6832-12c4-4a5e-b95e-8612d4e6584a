package com.sensetime.intersense.cognitivesvc.server.entities.sfd;


import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.List;

@Data
@Builder
public class AlertFeatureReq {

    private String col_id;

    private Integer top_k;

    private Float min_score;

    private Boolean return_raw_feature;

    private List<ReqFeature> features;

    @Tolerate
    public AlertFeatureReq(){}
}
