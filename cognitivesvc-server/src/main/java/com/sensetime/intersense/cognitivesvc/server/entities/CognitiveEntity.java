package com.sensetime.intersense.cognitivesvc.server.entities;

import java.awt.Polygon;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler.Detection.Rect;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.springframework.integration.annotation.Default;

public class CognitiveEntity {
	
    public static interface SP{
    	public Integer getId();
    }
    
    public static interface DP{
    	public Integer getId();
    	
    	public float[] getFeature();
    	
    	public void setFeature(float[] feature);
    }
	
	@Getter
	@Setter
	@Accessors(chain = true)
	@NoArgsConstructor
	@AllArgsConstructor
	@SuperBuilder
	public static class SeekParam{

		/**
		 * 要搜寻的特征(必填)
		 */
		public float[] feature;
		/**
		 * 搜寻数量
		 */
		public Integer count; 
		/**
		 * 搜寻特征相似度的阈值0~1
		 */
		public Float threshold; 
	}

	@Getter
	@Setter
	@Accessors(chain = true)
	@NoArgsConstructor
	@AllArgsConstructor
	@SuperBuilder
	public static class PersonParam extends SeekParam{
		/**
		 * 搜寻限定的人员组
		 */
		public String[] personGroups;  
		/**
		 * 搜寻限定的人员tag
		 */
		public String[] tags;
		/**
		 * deptIds
		 */
		public String[] deptIds;
        
        /**
         * 是否使用group缓存,默认 使用
         */
        public Boolean useGroupCache;

        public Boolean findLocal = false;
		
		public String figureImageUrl;


	}
	
	@Getter
	@Setter
	@Accessors(chain = true)
	@NoArgsConstructor
	@AllArgsConstructor
	@SuperBuilder
	public static class PasserParam extends SeekParam{
		/**
		 * 归档group的id
		 */
		public String groupId;
		/**
		 * deptIds
		 */
		public String[] deptIds;

		public Boolean findLocal = false;
	}
	
	@Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Accessors(chain = true)
	public static class CompareFeatureParam{
		private String featureBase64;
		private float[] featureFloatArray;
		private String personType;
		private String personGroup;
		private String personTag;
		private Integer count;
		private Float threshold;
	}
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class ModelHandlerEntity {
    	@Schema(description = "模型类名")
    	private String annotatorName; 
    	@Schema(description = "组件路径")
    	private String[] pluginPaths;
    	@Schema(description = "模型路径")
    	private String[] annotatorPaths;
    	@Schema(description = "额外文件")
    	private String[] attacheds;
    	@Schema(description = "动态编译类名(optional)")
    	private String   javaClassName;
    	@Schema(description = "动态代码(optional)")
    	private String   javaCode;
    	@Schema(description = "flockconfig(optional)")
    	private String   flockConfig;
    	@Schema(description = "大于零表示该能力是cpu且有该值个副本")
    	private Integer cpuModelDup;
    	@Schema(description = "预估模型占用显存(optional)")
    	private Integer  estimateGpuMemory;
    	@Schema(description = "预估模型份数(optional)")
    	private Integer  estimateCount;
    	@Schema(description = "批量个数(optional)")
    	private Integer  batchSize;
    	@Schema(description = "希望模型部署在哪些pod上(非强制)")
    	private String[] preferredIdentity;
    	@Schema(description = "模型部署在哪些pod上(强制)")
    	private String[] requiredIdentity;
    	@Schema(description = "不希望模型部署在哪些pod上(强制)")
    	private String[] rejectedIdentity;
    	@Schema(description = "亲和性组，相同组名的模型优先部署相同显卡(非强制)")
    	private String   affinityGroup;
    	@Schema(description = "相同group的模型独占一张卡")
    	private String   monopolizedGroup;
    	@Schema(description = "一些可选的额外信息用来描述算法行为")
    	private Map<String, Object>  additional;
    	@Schema(description = "update_ts")
    	private Date     updateTs;
    	
    	public static ModelHandlerEntity ofSwitcherGpuEntity(XDynamicModel model, List<String> envMonopolizedIdentities, Set<String> ongoingIdentities) {
    		if(model == null)
    			return null;
    		
    		ModelHandlerEntity entity = new ModelHandlerEntity();
    		entity.annotatorName = model.getAnnotatorName();
    		
    		if(StringUtils.isNotBlank(model.getPluginPaths()))
    			entity.pluginPaths = model.getPluginPaths().split(",");
    		else 
    			entity.pluginPaths = new String[0];
    		
    		if(StringUtils.isNotBlank(model.getAnnotatorPaths()))
    			entity.annotatorPaths = model.getAnnotatorPaths().split(",");
    		else 
    			entity.annotatorPaths = new String[0];

    		entity.attacheds = StringUtils.split(model.getAttached(), ",");
    		entity.preferredIdentity = StringUtils.split(model.getPreferredIdentity(), ",");
    		
    		entity.javaClassName = model.getJavaClassName();
    		entity.javaCode = model.getJavaCode();
    		entity.flockConfig = model.getFlockConfig();
    		entity.estimateGpuMemory = model.getEstimateGpuMemory();
    		entity.estimateCount = model.getEstimateCount();
    		entity.batchSize = model.getBatchSize();
    		entity.affinityGroup = model.getAffinityGroup();
    		entity.monopolizedGroup = model.getMonopolizedGroup();
    		entity.updateTs = model.getUpdateTs();
    		entity.cpuModelDup = model.getCpuModelDup();
    		
    		if(StringUtils.isNotBlank(model.getAdditional()))
    			entity.additional = JSON.parseObject(model.getAdditional());
    		
    		String[] modelMonopolizedIdentities = StringUtils.split(model.getMonopolizedIdentity(), ",");
    		if(ArrayUtils.isEmpty(modelMonopolizedIdentities)) {
				if(CollectionUtils.isEmpty(envMonopolizedIdentities)) {
	    			entity.requiredIdentity = StringUtils.split(model.getRequiredIdentity(), ",");
					entity.rejectedIdentity = StringUtils.split(model.getRejectedIdentity(), ",");
				}else {
					String[] requiredArray = StringUtils.split(model.getRequiredIdentity(), ",");
					if(ArrayUtils.isNotEmpty(requiredArray)) {
						Set<String> requiredIdentitySet = Sets.newHashSet(requiredArray);
						for(String identity : envMonopolizedIdentities)
							requiredIdentitySet.remove(identity);
						entity.requiredIdentity = requiredIdentitySet.toArray(String[]::new);
					}
					
					String[] rejectedArray = StringUtils.split(model.getRequiredIdentity(), ",");
					if(ArrayUtils.isEmpty(rejectedArray)) {
						entity.rejectedIdentity = envMonopolizedIdentities.toArray(String[]::new);
					}else {
						Set<String> rejectedIdentitySet = new HashSet<String>(envMonopolizedIdentities);
		    			for(String rejected : rejectedArray)
		    				rejectedIdentitySet.add(rejected);
		    			entity.rejectedIdentity = rejectedIdentitySet.toArray(String[]::new);
					}
				}
    		}else {
    			entity.requiredIdentity = modelMonopolizedIdentities;
    			
    			Set<String> rejectedIdentitySet = new HashSet<String>(ongoingIdentities);
    			for(String monopolizedIdentity : modelMonopolizedIdentities) 
    				rejectedIdentitySet.remove(monopolizedIdentity);
    			
        		entity.rejectedIdentity = rejectedIdentitySet.toArray(String[]::new);
    		}
    		
    		return entity;
    	}
    	
    	public static ModelHandlerEntity ofWorkerEntity(XDynamicModel model) {
    		if(model == null)
    			return null;
    		
    		ModelHandlerEntity entity = new ModelHandlerEntity();
    		entity.annotatorName = model.getAnnotatorName();
    		
    		if(StringUtils.isNotBlank(model.getPluginPaths()))
    			entity.pluginPaths = model.getPluginPaths().split(",");
    		else 
    			entity.pluginPaths = new String[0];
    		
    		if(StringUtils.isNotBlank(model.getAnnotatorPaths()))
    			entity.annotatorPaths = model.getAnnotatorPaths().split(",");
    		else 
    			entity.annotatorPaths = new String[0];
    		
    		entity.javaClassName = model.getJavaClassName();
    		entity.javaCode = model.getJavaCode();
    		entity.flockConfig = model.getFlockConfig();
    		entity.estimateGpuMemory = model.getEstimateGpuMemory();
    		entity.estimateCount = model.getEstimateCount();
    		entity.batchSize = model.getBatchSize();
    		entity.affinityGroup = model.getAffinityGroup();
    		entity.updateTs = model.getUpdateTs();
    		entity.cpuModelDup = model.getCpuModelDup();
    		
    		if(StringUtils.isNotBlank(model.getAdditional()))
    			entity.additional = JSON.parseObject(model.getAdditional());
    		
    		if(StringUtils.isNotBlank(model.getAttached()))
    			entity.attacheds = model.getAttached().split(",");
    		
    		return entity;
    	}
    	
    	public boolean isEqual(ModelHandlerEntity entity) {
    		return Arrays.equals(this.pluginPaths, entity.pluginPaths)
    				&& Arrays.equals(this.annotatorPaths, entity.annotatorPaths)
    				&& Arrays.equals(this.attacheds, entity.attacheds)
    				&& StringUtils.equals(this.javaClassName, entity.javaClassName)
    				&& StringUtils.equals(this.javaCode, entity.javaCode)
    				&& StringUtils.equals(this.flockConfig, entity.flockConfig)
    				&& Objects.equals(entity.batchSize, this.batchSize);
    	}
    }
    
	@Data
	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static final class VideoStreamXswitcherInput{
		private Integer id;
		private String  deviceId;
		private Integer type;
		private List<Map<String, Object>>  processors;
	    
	    public VideoStreamXswitcher toVideoStreamXswitcher() {
	    	VideoStreamXswitcher.VideoStreamXswitcherBuilder builder = VideoStreamXswitcher.builder();
	    	builder.id(id);
	    	builder.deviceId(deviceId);
	    	builder.type((int)type);
	    	builder.processors(JSON.toJSONString(processors));
	    	return builder.build();
	    }
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Accessors(chain = true)
	public static final class XSwitcher{
		private boolean isHighRate;
		private boolean isMultiplex;
		private Map<String, Processor> processors;
		private String dispatchDesc;
		
		public XSwitcher(VideoStreamXswitcher xstream, Map<String,Integer> processorSaveImgMap) {
			this.isHighRate = xstream.getType() == 1 || xstream.getType() == 2 || xstream.getType() == 3;
			this.isMultiplex = xstream.getType() == 3 || xstream.getType() == 4;
			this.dispatchDesc = xstream.getDispatchDesc();
			try {
				if(StringUtils.isNotBlank(xstream.getProcessors())) {
					processors = JSON.parseArray(xstream.getProcessors(), Processor.class)
									.stream()
									.sorted((l, r) -> l.getProcessor().compareTo(r.getProcessor()))
									.collect(Collectors.toMap(p -> p.getProcessor(), p -> p));

					processors.values().forEach(e ->{
						if(processorSaveImgMap != null && !processorSaveImgMap.isEmpty()) {
							e.setImgSaveTag(processorSaveImgMap.getOrDefault(e.getProcessor(), 0));
						}
					});
				}
			}catch(Exception e) {
				processors = Map.of();
				e.printStackTrace();
			}
		}
		
		@Override
		public XSwitcher clone() {
			return new XSwitcher(isHighRate, isMultiplex, new HashMap<String, Processor>(processors), dispatchDesc);
		}
	}
	
	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static final class Processor{
		public static final Processor empty = new Processor();

		@Getter@Setter
		private int imgSaveTag;
		
		@Getter @Setter
		private String processor;
		@Getter @Setter
		private Integer interval;//低频代表N秒检测1帧，高频代表检1帧跳N帧
		@Getter @Setter
		private Float threshold;
		@Getter @Setter
		private Integer minSize;
		@Getter @Setter
		private Integer maxSize;
		@Getter @Setter
		private Integer[][][] roi;
		@Getter @Setter
		private List<Map<String, Object>> extras;

		@Getter @Setter
		private List<Map<String, Object>> targetOptions;

		@Getter @Setter
		private Integer modelSource;

		@Getter @Setter
		private Integer modelType;

		@Getter @Setter
		private Integer priority;

		@Getter @Setter
		private Integer faceRegSwitch;

		@Getter @Setter
		private   Map<String, Object> labeledPerson;

		@Getter @Setter
		private  Integer[] crowdFunction;

	    private transient Polygon polygons[];
	    private transient Object extrasBuf;

		@Getter @Setter
		Map<String, Object> params;
	    
	    public Rect[] rectRoi(){
	    	if(ArrayUtils.isEmpty(roi))
	    		return new Rect[0];
	    	
	    	Rect[] result = new Rect[roi.length];
	    	
	    	for(int index = 0; index < roi.length; index ++) {
		    	int left   = Arrays.stream(roi[index]).mapToInt(r -> r[0]).min().getAsInt();
		    	int top    = Arrays.stream(roi[index]).mapToInt(r -> r[1]).min().getAsInt();
		    	int right  = Arrays.stream(roi[index]).mapToInt(r -> r[0]).max().getAsInt();
		    	int bottom = Arrays.stream(roi[index]).mapToInt(r -> r[1]).max().getAsInt();
		    	
		    	result[index] = Rect.builder().left(left).top(top).width(right - left).height(bottom - top).build();
	    	}
	    	
	    	return result;
	    }
	    
	    public Polygon[] fetchPolygons() {
	    	if(this.polygons != null)
		    	return this.polygons;
	    	
	    	if(ArrayUtils.isEmpty(roi))
	    		return this.polygons = new Polygon[0];
	    	
	    	Polygon polygons[] = new Polygon[roi.length];
            for (int index = 0; index < roi.length; index ++) {
    			int pointNum = roi[index].length;
    			int[] xs = new int[pointNum];
    			int[] ys = new int[pointNum];
    			
                for (int jndex = 0; jndex < roi[index].length; jndex ++) {
                    xs[jndex] = roi[index][jndex][0];
                    ys[jndex] = roi[index][jndex][1];
                }
                
                polygons[index] = new Polygon(xs, ys, pointNum);
            }
            
	    	return this.polygons = polygons;
	    }
	    
	    @SuppressWarnings({ "unchecked", "rawtypes" })
		public Object fetchExtras(Function function) {
	    	if(extrasBuf == null)
	    		if(function != null)
	    			extrasBuf = function.apply(extras);
	    	
	    	return extrasBuf;
	    }
	    
	    public Map<String, Object> fetchBufferMap(){
	    	if(extras == null)
	    		return null;
	    	
	    	return extras.stream()
		    			 .filter(item -> Processor.BUFFER.equals(item.get("type")))
		    			 .findAny()
		    			 .orElse(null);
	    }
	    
	    public static final String ROIIDS = "roiIds";
	    public static final String BUFFER = "buffer";
	}
    
	@Data
	@NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class QuerySwitcherResult {
		private boolean incharge;
    	private String gpuId;
    	private String identity;
		private String identityFull;
    	private String deviceType;
    	private String version;
    	private List<Map<String, Object>> lowRateDevices;

		private Map<String, Map<String, Object> >  lowMonitorDevices;
    	
    	public static final QuerySwitcherResult ERROR = new QuerySwitcherResult();
	}
	
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class QueryWorkerResult {
		public static final QueryWorkerResult ERROR = new QueryWorkerResult(0, 0, 100);
		
		public static final String NaN = "NaN";
		
    	@Setter
    	private List<Model> dynamicModels;
    	@Getter @Setter
    	private Integer availableGpuMemory;
    	@Getter @Setter
    	private Integer totalGpuMemory;
    	@Getter @Setter
    	private Integer avgGpuRate;
    	@Getter @Setter
    	private String version;
    	@Getter @Setter
    	private String identity;
		@Getter @Setter
		private String identityFull;
    	@Getter @Setter
    	private String gpuId;
    	@Getter @Setter
    	private String deviceType;
		@Getter @Setter
		private Float currentGpuRate;
    	
    	public QueryWorkerResult(Integer availableGpuMemory, Integer totalGpuMemory, Integer avgGpuRate) {
			this.availableGpuMemory = availableGpuMemory;
			this.totalGpuMemory = totalGpuMemory;
			this.avgGpuRate = avgGpuRate;
		}
    	
    	public List<Model> getDynamicModels(){
    		return dynamicModels == null ? Lists.newArrayList() : dynamicModels;
    	}
    	
    	public void addDynamicModel(String annotatorName) {
    		if(dynamicModels == null)
    			dynamicModels = new ArrayList<Model>();
    		
    		dynamicModels.add(Model.builder().annotatorName(annotatorName).build());
    	}
    	
    	public void removeDynamicModel(String annotatorName) {
    		if(dynamicModels == null)
    			return ;
    		
    		Iterator<Model> it = dynamicModels.iterator();
    		while(it.hasNext()) {
    			if(it.next().getAnnotatorName().equals(annotatorName)) {
    				it.remove();
    				break;
    			}
    		}
    	}

    	public boolean hasInitializing() {
    		if(CollectionUtils.isEmpty(dynamicModels))
    			return false;

    		return dynamicModels.stream().filter(m -> "initializing".equals(m.getStatus())).findAny().isPresent();
    	}
    	
    	public boolean gpuInstance() {
    		return StringUtils.isNotBlank(identity) && !QueryWorkerResult.NaN.equals(identity);
    	}
    	
		@Getter
        @NoArgsConstructor
        @AllArgsConstructor
        @Accessors(chain = true)
    	@Builder
    	public static class Model{
    		private String  annotatorName;
    		@Builder.Default
    		private String status = "initializing";
    		@Builder.Default
    		private Long   expire = -1l;
    		@Builder.Default
    		private Map<String, Object> monitor = Map.of();
    		@Builder.Default
    		private Map<String, Object> config  = Map.of();
    		@Builder.Default
    		private List<String> higtRateDevices = List.of();
    		@Builder.Default
    		private List<String> tempHigtRateDevices = List.of();

			@Builder.Default
			private Map<String, Map<String, Object> > monitorDevice = Map.of();
		}
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class SenseyexRawImage {
        private String              deviceId;
        private String[]            processors;//可以为null 就是全部都跑
        private String[]            images;
        private Long                capturedTime;
    	private Map<String, Object> extra;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class SenseyexRawVideo {
        private String              deviceId;
        private String[]            processors;//可以为null 就是全部都跑
        private String            	video;
        private Long                capturedTime;
    	private Map<String, Object> extra;

		private  String rtmpDestination;
		private  String rtmpOn;

    }

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	@Accessors(chain = true)
	public static class SenseyexRawFeature {

		private Integer[]           processors;//可以为null 就是全部都跑
		private String[]            images;
		private String[]            bodyImages;
		private Boolean             isEncoder;

	}
}
