package main

/*
#include <stdlib.h>

typedef struct {
    void* demuxerCtx;
    void* decodeCtx;
    void* fp;
} DecoderResult;
*/
import "C"

import (
	"fmt"
	log "github.com/sirupsen/logrus"
	"gitlab.sz.sensetime.com/viper/gosdkwrapper/common"
	"gitlab.sz.sensetime.com/viper/gosdkwrapper/hal/image"
	"gitlab.sz.sensetime.com/viper/gosdkwrapper/kestrel"
	"gitlab.sz.sensetime.com/viper/gosdkwrapper/kestrel/kcommon"
	"gitlab.sz.sensetime.com/viper/gosdkwrapper/kestrel/keson"
	"gitlab.sz.sensetime.com/viper/gosdkwrapper/kestrel/kutil"
	"gitlab.sz.sensetime.com/viper/gosdkwrapper/monitor"
	"unsafe"
)

import (
	// import implementations
	_ "gitlab.sz.sensetime.com/viper/gosdkwrapper/hal"
	// _ "gitlab.sz.sensetime.com/viper/gosdkwrapper/hal/video/atlas" // nolint
	"gitlab.sz.sensetime.com/viper/gosdkwrapper/hal/video/decode"
)

//export DecoderHello
func DecoderHello() {
	fmt.Println("Hello from Go!")
}

//export DemuxerInit
func DemuxerInit(rtspUrl string) {

	demuxer := decode.Demuxers["joy4"]
	demuxer.Init(decode.DemuxerConfig{})
	defer func() {
		demuxer.Close()
	}()

	demuxerCtx := demuxer.Create()

	_, err := demuxerCtx.Init(rtspUrl)

	fmt.Println("err", err)
	defer func() {
		demuxerCtx.Close()
	}()

}

//export CreateDecoder
func CreateDecoder(path *C.char) C.DecoderResult {

	goFilePath := "rtsp://**********:8554/performance/crossLineBody1"

	rt := kcommon.DefaultGPURuntimeSetter

	demuxer, ok := decode.Demuxers["joy4"]
	if !ok || demuxer == nil {
		log.Fatalf("demuxer: %s not found", "demuxerName")
	}
	if err := demuxer.Init(decode.DemuxerConfig{}); err != nil {
		log.Fatal("demuxer init fail with err", err)
	}

	defer func() {
		if err := demuxer.Close(); err != nil {
			log.Fatal(err)
		}
	}()

	fmt.Println("decode.Decoders", goFilePath)
	// decoder, ok := decode.Decoders["cuda"]
	//decoder, ok := decode.Decoders["cuda"]
	decoder, ok := decode.Decoders["cuda"]
	if !ok {
		log.Fatal("FrameGen need ", "", " decoder")
	}
	decoderCfg := decode.DecoderConfig{
		PixelFormat:           keson.KestrelPixel2CommonPixel(kestrel.PixelFormatRGB),
		ExplicitFramePoolSize: int(DefaultConfig.framePoolSize),
		NvidiaConfig: decode.NvidiaDecoderConfig{
			NVDecoderBufferSize:  6,
			CUVIDNoTimestampMode: false,
		},
	}
	if err := decoder.Init(decoderCfg, &rt); err != nil {
		log.Fatal(err)
	}

	mo, ok := monitor.DeviceMonitors[monitor.DefaultDevice]
	if !ok || mo == nil {
		log.Fatal("get DeviceMonitor fail")
	}

	demuxerCtx := demuxer.Create()
	cds, err := demuxerCtx.Init(goFilePath)
	if err != nil {
		log.Fatal("create demuxer context with err: ", err)
	}
	defer func() {
		if err := demuxerCtx.Close(); err != nil {
			log.Fatal(err)
		}
	}()

	cdIndex := decode.FindBestVideoStream(cds)

	decodeCtx := decoder.Create(&decoderCfg)
	if err := decodeCtx.Init(cds[cdIndex]); err != nil {
		log.Fatal(err)
	}
	defer func() {

		if err := decodeCtx.Close(); err != nil {
			log.Fatal(err)
		}
	}()

	fp := kutil.NewFrameGetter("FramePool", common.DeviceGPU)
	defer func() {
		if err := fp.Release(); err != nil {
			log.Fatal(err)
		}
	}()

	return C.DecoderResult{
		demuxerCtx: unsafe.Pointer(&demuxerCtx),
		decodeCtx:  unsafe.Pointer(&decodeCtx),
		fp:         unsafe.Pointer(&fp),
	}
}

type Config struct {
	chanBufSize   int
	framePoolSize uint
}

var DefaultConfig = &Config{
	chanBufSize:   1,
	framePoolSize: 50,
}

func CompressFrame(f kestrel.Frame, release bool, encoder image.Encoder) ([]byte, error) {
	if f == nil {
		return nil, nil
	}
	if release {
		defer f.Release()
	}

	kf := keson.Frame{Frame: f}
	return encoder.Encode(&kf)
}
func main() {

	//r := rand.New(rand.NewSource(time.Now().UnixNano()))

	//format.RegisterAll()
	//deviceName := monitor.CUDA
	//
	//ks := kcommon.DefaultKestrelEnvSetter
	//ks.LicFilePath = "STUDIO.lic"
	//ks.ProductName = "sensestudio"
	//ks.DeviceLibName = deviceName + ".kep"
	//_ = ks.Init()
	//defer func() {
	//	_ = ks.Deinit()
	//}()
	//
	//rt := &kcommon.DefaultGPURuntimeSetter
	//rt.DeviceName = deviceName
	//_ = rt.Before()
	//defer func() {
	//	_ = rt.After()
	//}()
	//
	//kestrel.SetLogLevel(kestrel.DEBUG)
	//CreateDecoder()
	//
	//filenameH264 := "rtsp://**********:8554/performance/crossLineBody1"
	////filenameH265 := "rtsp://10.151.5.2:8554/performance/fH265"
	//
	//frameGen := kutil.NewFrameGen(filenameH264, DefaultConfig.framePoolSize, DefaultConfig.chanBufSize)
	//defer frameGen.Close()
	//
	////"type": "imageio",
	////	"imageio": {
	////	"Flags": 0,
	////		"Format": "jpg"
	////}
	//
	////imageEncode := image.EncoderConfig{
	////	Type: "imageio",
	////	ImageIO: image.ImageIOEnConfig{
	////		Flags:  1,
	////		Format: "jpg",
	////	},
	////}
	////encoders, _ := image.NewEncoder(imageEncode)
	//
	//for frame := range frameGen.FrameChan {
	//
	//	now := time.Now()
	//
	//	// 转换为Unix时间戳，单位为秒
	//	seconds := now.Unix()
	//
	//	// 转换为毫秒时间戳
	//	milliseconds := seconds * 1000
	//
	//	// 加上当前时间的纳秒部分，转换为毫秒
	//	milliseconds += int64(now.Nanosecond()) / 1e6
	//
	//	//CompressFrame(frame, false, encoders)
	//	fmt.Println("imageSave", frame != nil)
	//	err := frame.SaveToFile("./images/" + strconv.FormatInt(milliseconds, 10) + ".jpg")
	//	frame.Release()
	//	if err != nil {
	//		fmt.Println(err)
	//		continue
	//	}
	//}

}
