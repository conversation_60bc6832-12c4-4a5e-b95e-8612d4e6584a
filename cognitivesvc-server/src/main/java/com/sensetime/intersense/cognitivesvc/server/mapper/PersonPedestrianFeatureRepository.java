package com.sensetime.intersense.cognitivesvc.server.mapper;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.sensetime.intersense.cognitivesvc.server.entities.PersonPedestrianFeature;
@Repository
public interface PersonPedestrianFeatureRepository extends JpaRepositoryImplementation<PersonPedestrianFeature, Integer>{
	@Query(value = "select *  from person_pedestrian_feature p where p.id >= ?1 and p.id < ?2 and p.id % ?3 = ?4 and p.sts = 0 order by p.id asc", nativeQuery = true)
	public List<PersonPedestrianFeature> querySplit(int start, int end, int totalSplitNum, int currentSplitNum);
	
	@Query(value = "select count(p.id) from person_pedestrian_feature p where p.id % ?1 = ?2 and p.sts = 0", nativeQuery = true)
	public long countSplit(int totalSplitNum, int currentSplitNum);
	
	@Query(value = "select p.id from PersonPedestrianFeature p where p.personUuid in ?1")
	public List<Integer> queryIdsByPersonUuids(List<String> personUuids);

	@Query(value = "select max(p.id) from PersonPedestrianFeature p")
	public Integer queryMaxId();
	
	@Query(value = "select p.id from PersonPedestrianFeature p")
	public List<Integer> queryIds();
	
	@Query(value = "select p.id from PersonPedestrianFeature p where p.privilege in ?1")
	public List<Integer> queryIdsByDeptids(List<String> deptids);
	
	public List<PersonPedestrianFeature> findByPersonUuid(String personUuid);
	
	public int countByModelVersionNot(String modelVersion);
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByPersonUuidIn(List<String> personUuids);
}
