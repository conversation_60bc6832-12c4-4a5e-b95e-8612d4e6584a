package com.sensetime.intersense.cognitivesvc.server.vaxtor.demo;

import com.sensetime.intersense.cognitivesvc.server.vaxtor.core.PlateOcrProcessor;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateInput;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateOutput;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
public class PlateOcrDemo {

    public static void main(String[] args) {

        String OCR_DATA_PATH = "/sensetime/javacvDemo/src/main/java/com/sensetime/intersense/cognitivesvc/server/vaxtor_c/ocr_data.bin";



        // 创建处理器实例 (4个检测器, 队列大小为100)
        PlateOcrProcessor processor = new PlateOcrProcessor(2, 10,OCR_DATA_PATH, Arrays.asList("Ireland", "Singapore"));

        try {
            // 处理单张图片示例
            processSingleImage(processor, "/sensetime/javacvDemo/src/main/java/com/sensetime/intersense/cognitivesvc/server/vaxtor_c/test/images/04D78222.jpg", "bgr");

            // 批量处理示例
            processBatchImages(processor, "/sensetime/javacvDemo/src/main/java/com/sensetime/intersense/cognitivesvc/server/vaxtor_c/test/images", "bgr");

        } finally {
            // 关闭处理器
            processor.shutdown();
        }
    }

    private static void processSingleImage(PlateOcrProcessor processor, String imagePath, String pixformat) {
        try {
            // 读取图片数据
            byte[] imageData = Files.readAllBytes(Paths.get(imagePath));

            // 创建输入对象
            PlateInput input = new PlateInput();
            input.setImageData(imageData);
            input.setImageSize(imageData.length);
            input.setPixformat(pixformat);


            // 提交请求并等待结果
            CompletableFuture<PlateOutput> future = processor.submitRequest(input);
            PlateOutput result = future.get();

            // 处理结果
            if (result != null) {
                log.info("Processing " + imagePath);
                printPlateInfo(result);
            }

        } catch (IOException | InterruptedException | ExecutionException e) {
            log.error("Failed to process image: " + e.getMessage(), e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private static void processBatchImages(PlateOcrProcessor processor, String folderPath, String pixformat) {
        try {
            Files.list(Paths.get(folderPath))
                    .filter(path -> path.toString().toLowerCase().endsWith(".jpg") ||
                            path.toString().toLowerCase().endsWith(".jpeg"))
                    .forEach(path -> {
                        try {
                            byte[] imageData = Files.readAllBytes(path);

                            PlateInput input = new PlateInput();
                            input.setImageData(imageData);
                            input.setImageSize(imageData.length);
                            input.setPixformat(pixformat);


                            // 异步处理每张图片
                            processor.submitRequest(input)
                                    .thenAccept(result -> {
                                        if (result != null) {
                                            log.info("Processing " + path);
                                            printPlateInfo(result);
                                        }
                                    })
                                    .exceptionally(e -> {
                                        log.error("Failed to process " + path + ": " + e.getMessage(), e);
                                        return null;
                                    });

                        } catch (IOException e) {
                            log.error("Failed to read image " + path + ": " + e.getMessage(), e);
                        }
                    });

        } catch (IOException e) {
            log.error("Failed to list directory: " + e.getMessage(), e);
        }
    }

    private static void printPlateInfo(PlateOutput output) {
        if (output == null) {
            log.warn("no plate info found");
        } else {
            log.info("============ Plate Information ============");
            log.info("Plate Number: " + output.getPlateNumberAscii());
            log.info("Country: " + output.getPlateCountry());
            log.info("Vehicle Make: " + output.getVehicleMake());
            log.info("Vehicle Model: " + output.getVehicleModel());
            log.info("Vehicle Color: " + output.getVehicleColor());
            log.info("Vehicle Class: " + output.getVehicleClass());
            log.info("=========================================");
        }
    }
} 