package com.sensetime.intersense.cognitivesvc.server.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Author: Cirmons
 * @Date: 2024-04-17
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecModelResultEntity {

    @Schema(description = "图片url", name = "图片url")
    private String image;

    @Schema(description = "图片跑模型的结果，一个图片可能有多个目标", name = "图片跑模型的结果，一个图片可能有多个目标")
    private List<Map<String, Object>> result;

    @JsonIgnore
    @Schema(description = "算法annotatorName", name = "算法annotatorName")
    private String processor;


}
