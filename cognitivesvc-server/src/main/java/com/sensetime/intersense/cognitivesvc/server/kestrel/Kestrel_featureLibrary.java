package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_feature_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.FloatByReference;
import com.sun.jna.ptr.PointerByReference;
import java.nio.FloatBuffer;

/**
 * J<PERSON> Wrapper for library <b>kestrel_feature</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_featureLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_featureLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_featureLibrary INSTANCE = (Kestrel_featureLibrary)Native.load(Kestrel_featureLibrary.JNA_LIBRARY_NAME, Kestrel_featureLibrary.class);
	/**
	 * @return Created feature with any type or NULL<br>
	 * Original signature : <code>kestrel_feature kestrel_feature_alloc(size_t)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:26</i>
	 */
	kestrel_feature_t kestrel_feature_alloc(long dims);
	/**
	 * Original signature : <code>kestrel_feature kestrel_feature_make(size_t, float*, kestrel_buf_finalizer, void*)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:29</i><br>
	 * @deprecated use the safer methods {@link #kestrel_feature_make(com.ochafik.lang.jnaerator.runtime.NativeSize, java.nio.FloatBuffer, kestrel_buffer.Kestrel_bufferLibrary.kestrel_buf_finalizer, com.sun.jna.Pointer)} and {@link #kestrel_feature_make(com.ochafik.lang.jnaerator.runtime.NativeSize, com.sun.jna.ptr.FloatByReference, kestrel_buffer.Kestrel_bufferLibrary.kestrel_buf_finalizer, com.sun.jna.Pointer)} instead
	 */
	@Deprecated 
	kestrel_feature_t kestrel_feature_make(long dims, FloatByReference data, Pointer finalizer, Pointer ud);
	/**
	 * Original signature : <code>kestrel_feature kestrel_feature_make(size_t, float*, kestrel_buf_finalizer, void*)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:29</i>
	 */
	kestrel_feature_t kestrel_feature_make(long dims, FloatBuffer data, Pointer finalizer, Pointer ud);
	/**
	 * careful if you are going to modify its payload data.<br>
	 * Original signature : <code>kestrel_feature kestrel_feature_ref(kestrel_feature)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:40</i>
	 */
	kestrel_feature_t kestrel_feature_ref(kestrel_feature_t in);
	/**
	 * Original signature : <code>kestrel_feature kestrel_feature_duplicate(kestrel_feature)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:43</i>
	 */
	kestrel_feature_t kestrel_feature_duplicate(kestrel_feature_t in);
	/**
	 * @param[in,out] feature Feature pointer going to be freed<br>
	 * Original signature : <code>void kestrel_feature_free(kestrel_feature*)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:48</i>
	 */
	void kestrel_feature_free(PointerByReference feature);
	/**
	 * @return Feature dimension or -1<br>
	 * Original signature : <code>int32_t kestrel_feature_dimension(kestrel_feature)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:54</i>
	 */
	int kestrel_feature_dimension(kestrel_feature_t feature);
	/**
	 * @return Feature magnitude or 0.0F<br>
	 * Original signature : <code>float kestrel_feature_magnitude(kestrel_feature)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:60</i>
	 */
	float kestrel_feature_magnitude(kestrel_feature_t feature);
	/**
	 * @note Comparing two features with different version, it returns normally, but output a warning<br>
	 * Original signature : <code>float kestrel_feature_distance(kestrel_feature, kestrel_feature)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:69</i>
	 */
	float kestrel_feature_distance(kestrel_feature_t feature1, kestrel_feature_t feature2);
	/**
	 * it<br>
	 * Original signature : <code>float kestrel_feature_distance_normalized(kestrel_feature, kestrel_feature)</code><br>
	 * <i>native declaration : include/kestrel_feature.h:80</i>
	 */
	float kestrel_feature_distance_normalized(kestrel_feature_t feature1, kestrel_feature_t feature2);
}
