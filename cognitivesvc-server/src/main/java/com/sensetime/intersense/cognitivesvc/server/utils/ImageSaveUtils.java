package com.sensetime.intersense.cognitivesvc.server.utils;

import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.InetAddress;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ImageSaveUtils {
    private static String currentHost;
    static {
        try {
            InetAddress address = InetAddress.getLocalHost();
            currentHost = address.getHostAddress();
        }catch (Exception e){
        }
    }
    public final Map<String, BufferedWriter> writers;
    public static final ImageSaveUtils instance = new ImageSaveUtils();

    ImageSaveUtils() {
        this.writers = new ConcurrentHashMap<>();
    }

    public static ImageSaveUtils getInstance() {
        return instance;
    }

    public void createWriter(String model, String hour, String day) {



        String pathsFile = newFileWithPathImage(day, hour, model, false);

        if(pathsFile.isBlank()){
            return;
        }
        try {
            File myFile = new File(pathsFile);
            if(!myFile.exists()){
                myFile.createNewFile();
            }

        } catch (IOException e) {
            System.out.println("An error occurred.");
            e.printStackTrace();
        }

        if (pathsFile != null) {
            Path paths = Path.of(pathsFile);
            try {
                BufferedWriter writer = writers.get(pathsFile);
                if (writer == null) {
                    writer = Files.newBufferedWriter(paths, StandardOpenOption.APPEND);
                    writers.put(pathsFile, writer);
                }

                // 可以根据需求添加更多的初始化写入操作
            } catch (IOException e) {
                System.err.println("Error creating file writer: " + e.getMessage() + pathsFile);
            }
        }
    }

    public String newFileWithPathImage(String day,String hour, String model, boolean flag) {
        // 根据当前小时、模型和其他参数来生成文件路径
        String pathParent= Utils.instance.savePath +"/" + model +"/"+ day;
        File myFileP = new File(pathParent);
        if(!myFileP.exists()){
            myFileP.mkdirs();
        }

        return pathParent + "/" + hour + "_" + currentHost + ".txt";
    }

    public static void writeToFile(String data, String model) {
        //BufferedWriter writer = writers.get(model);
        //if (writer != null) {
            try {

                FileWriter writer = new FileWriter(model, true);

                  // 使用append方式将JSON字符串追加到文件
                   writer.append(data);
                   writer.append("\n"); // 可选，添加换行符以便每个JSON对象占据一行

                   writer.flush();
                // 可以根据需求添加更多的写入语句
            } catch (IOException e) {
                System.err.println("Error appending data to file: " + e.getMessage());

            }
       // }
    }
    public  void writeToFileNio(String data, String model) {
        BufferedWriter writer = writers.get(model);
        if (writer != null) {
            try {
                writer.write(data);
                writer.newLine();
                writer.flush();
                // 可以根据需求添加更多的写入语句
            } catch (IOException e) {
                System.err.println("Error appending data to file: " + e.getMessage());

            }
        }
    }

    public void closeWriters() {
        for (BufferedWriter writer : writers.values()) {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    System.err.println("Error closing file writer: " + e.getMessage());
                }
            }
        }
    }
}

