package com.sensetime.intersense.cognitivesvc.server.utils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.kestrel.DecoderEngineLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_codecLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.OpencvDecoderLibrary;
import com.sensetime.lib.weblib.exception.BusinessException;
import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.LongByReference;
import org.apache.commons.lang3.StringUtils;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils.FramePoolUtils;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;


@Slf4j
public class VideoStream{
	public static enum VideoStatus{ EOF, UNKNOWN, OK }

	public static enum VideoGrabFrameStatusOutput{OK, ERROR};

	/** <key, <>> */
	protected static final Map<String, MultiplexDecodeHolder> multiplexDecoderMap = new HashMap<String, MultiplexDecodeHolder>();

	protected final Pointer demuxer_in;

	/** 当前正在解码帧 */
	protected final PointerByReference kestrel_decode_frame = new PointerByReference();

	/** 根据环境来区别是gpu还是cpu帧 */
	protected final PointerByReference kestrel_reload_frame = new PointerByReference();

	/** 分离器 */
	protected Pointer demuxer;



	protected volatile  String random;


	private  static final  Map<String, Long> firstCurrentMap = new HashMap<String, Long>();


	private static final Map<String, Long> firstFrameMap  = new HashMap<String, Long>();


	/** 独用解码器*/
	protected PointerByReference privateDecoder;

	/** h265独用解码器*/
	protected OpencvDecoderLibrary.OpencvVideoDecoder h265PrivateDecoder;

	/** 是否是h265 */
	protected volatile boolean h265CodeC;

	/** 是否使用外部设置的h265配置 */
	protected volatile boolean useExternalH265Setting = false;

	/** 复用解码器*/
	protected MultiplexDecodeHolder.MultiplexDecoder multiplexDecoder;

	/** 各自线程的运行标记 */
	protected volatile boolean stoped;

	/** 这路视频流的设备 */
	@Getter @Setter
	protected volatile VideoStreamInfra device;

	/** 标记位 解码方式 */
	@Getter @Setter
	protected boolean useDeviceDecoder = true;

	/** 标记位 是否为多路复用解码器 若开启则只解码关键帧 */
	@Getter @Setter
	protected boolean useMultiplexDecoder = false;

	/** 标记位 是否使用系统时间作为帧的pts */
	@Getter @Setter
	protected boolean useJavaCapturedTime = true;

	@Getter @Setter
	protected boolean useSelfCalCapturedTime = true;

	/**  取帧出错次数 */
	protected int errorTime = 0;

	protected int errorEofTime = 0;

	protected int errorInternalTime = 0;

	protected int errorAgainTime = 0;

	/**  取帧次数 */
	@Getter
	protected int frameIndex = 0;

	/**  视频所在id */
	protected int chosenStreamId = -1;

	public static  final Map<String, Map<String, Object>> chosenStreamInfoMap = new HashMap<String, Map<String, Object>>();;
	/**  上一帧的source */
	protected String lastFrameSource;

	@Setter
	protected long maxLimitFrameIndex = Long.MAX_VALUE;

	/**  视频是否有帧在十秒内解码成功 */
	@Getter @Setter
	protected volatile VideoStatus videoStatus = VideoStatus.OK;

	@Getter @Setter
	protected volatile VideoGrabFrameStatusOutput videoStatusOutput = VideoGrabFrameStatusOutput.OK;

	@Getter @Setter
	protected volatile String videoStatusDetail;

	@Getter @Setter
	protected volatile Date lastErrorCheckTime;


	public VideoStream(VideoStreamInfra device) {
		this.device = device;
		demuxer_in = KestrelApi.keson_parse("{\"id\": " + Utils.keyToContextId(device.getDeviceId()) + "}");
	}

	/** 整个视频流开始工作  这是第一步 */
	public void start() { open(); }

	/** 关闭整个视频流处理 */
	public void stop() { close(); }

	public void changeStart() {  }

	/** 获取下一帧 */
	public VideoFrame grabberNextFrame(boolean needHost, boolean needDevice) {
		long now = 0;
        boolean logged = Utils.instance.watchFrameTiktokLevel == -799;

        if(logged)
        	now = System.currentTimeMillis();

		if(!Objects.equals(lastFrameSource, device.realRtspSource())){
			String last = lastFrameSource;
			lastFrameSource = device.realRtspSource();
			throw new RuntimeException("SOURCE_CHANGED [" + device.getDeviceId() + "] FROM[" + last + "] TO[" + lastFrameSource + "]");
		}
		long framePts = 0;

		if(logged){
			log.info("h265 pull frame check {}",  h265CodeC);
		}
		if(h265CodeC){
			if (demuxer == null ) {
				errorTime++;
				log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: demuxer == null || (privateDecoder == null && multiplexDecoder == null)", device.getDeviceId(), device.getRtspSource());
				//try { Thread.sleep(3000); } catch (InterruptedException e) { }
				throw new RuntimeException("KPLUGIN_E_EOF");
			}
			String str = device.realRtspSource() + "random" + random;

			// 将字符串转换为 UTF-8 编码的字节数组
			byte[] utf8Bytes = str.getBytes(StandardCharsets.UTF_8);
			int length = utf8Bytes.length; // 获取字节数组的长度

			// 分配内存并设置字符串
			Pointer memory = new Pointer(Native.malloc(length + 1));
			memory.write(0, utf8Bytes, 0, length);
			memory.setByte(length, (byte) 0); // 确保以 null 结尾


			IntByReference errCode = new IntByReference();
			Pointer FrameCgo =  KestrelApi.PullFrame(memory, length, errCode);

			// 释放内存
			Native.free(Pointer.nativeValue(memory));

			kestrel_decode_frame.setValue(FrameUtils.ref_frame(FrameCgo));
			FrameUtils.batch_free_frame(FrameCgo);
			str = "";
			utf8Bytes= null;
			length = 0;
			memory = null;
			//KestrelApi.ReleaseFrame(FrameCgo);
			if(errCode.getValue() != 0) {
				if (errCode.getValue() == KestrelApi.KPLUGIN_E_EOF) {
					errorEofTime++;
					errorTime++;
					log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: KPLUGIN_E_EOF)", device.getDeviceId(), device.getRtspSource());
					try { Thread.sleep(3000); } catch (InterruptedException e) { }
					throw new RuntimeException("KPLUGIN_E_EOF");
				} else if (errCode.getValue() == KestrelApi.KPLUGIN_E_INTERNAL) {
					errorInternalTime++;
					errorTime++;
					log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: KPLUGIN_E_INTERNAL)", device.getDeviceId(), device.getRtspSource());
					//try { Thread.sleep(1000); } catch (InterruptedException e) { }
					throw new RuntimeException("KPLUGIN_E_INTERNAL");
				} else {
					errorInternalTime++;
					errorAgainTime++;
					errorTime++;
					log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: KPLUGIN_E_UNKNOWN)", device.getDeviceId(), device.getRtspSource());
					//try { Thread.sleep(1000); } catch (InterruptedException e) { }
					throw new RuntimeException("KPLUGIN_E_UNKNOWN");
				}
			}
//          kestrel_decode_frame.setValue(FrameUtils.ref_frame(frame.getValue()));
//			String path = FrameUtils.save_image_as_jpg(frame.getValue(), ImageUtils.newFileWithMkdir("fetchFrame"));
//			log.info("save path4444 {}", path);
//          FrameUtils.batch_free_frame(frame);
			//int width = KestrelApi.kestrel_frame_video_width(kestrel_decode_frame.getValue());
//			int height = KestrelApi.kestrel_frame_video_height(kestrel_decode_frame.getValue());
//			log.info("h265 frame width:{},height:{}", width, height);

		}else {
			if (demuxer == null || (privateDecoder == null && multiplexDecoder == null)) {
				errorTime++;
				log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: demuxer == null || (privateDecoder == null && multiplexDecoder == null)", device.getDeviceId(), device.getRtspSource());
				//try { Thread.sleep(3000); } catch (InterruptedException e) { }
				throw new RuntimeException("KPLUGIN_E_EOF");
			}

			PointerByReference frame_keson = new PointerByReference();
			int ret = KestrelApi.KPLUGIN_E_AGAIN;
			if (multiplexDecoder != null)
				ret = grabberMultiplexFrame(frame_keson);
			else if (privateDecoder != null)
				ret = grabberPrivateFrame(frame_keson);
			else
				ret = KestrelApi.KPLUGIN_E_EOF;

			if (ret != KestrelApi.KPLUGIN_OK) {
				KesonUtils.kesonDeepDelete(frame_keson);

				if (ret == KestrelApi.KPLUGIN_E_AGAIN) {
					errorAgainTime++;
					errorTime++;
					log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: KPLUGIN_E_AGAIN", device.getDeviceId(), device.getRtspSource());
					//try { Thread.sleep(1000); } catch (InterruptedException e) { }
					throw new RuntimeException("KPLUGIN_E_AGAIN");
				} else if (ret == KestrelApi.KPLUGIN_E_INTERNAL) {
					errorInternalTime++;
					errorTime++;
					log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: KPLUGIN_E_INTERNAL)", device.getDeviceId(), device.getRtspSource());
					//try { Thread.sleep(1000); } catch (InterruptedException e) { }
					throw new RuntimeException("KPLUGIN_E_INTERNAL");
				} else if (ret == KestrelApi.KPLUGIN_E_EOF) {
					errorEofTime++;
					errorTime++;
					log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: KPLUGIN_E_EOF)", device.getDeviceId(), device.getRtspSource());
					//try { Thread.sleep(3000); } catch (InterruptedException e) { }
					throw new RuntimeException("KPLUGIN_E_EOF");
				} else {
					errorInternalTime++;
					errorAgainTime++;
					errorTime++;
					log.error("[VideoHandleLog] decode frame error deviceID:{},rtsp:{}, cause: KPLUGIN_E_UNKNOWN)", device.getDeviceId(), device.getRtspSource());
					//try { Thread.sleep(1000); } catch (InterruptedException e) { }
					throw new RuntimeException("KPLUGIN_E_UNKNOWN");
				}
			}

			PointerByReference frame = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(frame_keson.getValue(), "image"));
			kestrel_decode_frame.setValue(FrameUtils.ref_frame(frame.getValue()));


			Map<String, Object> imageFrmae = (Map<String, Object>) ((Map<String, Object>) KesonUtils.kesonToJson(frame_keson)).get("image");
			if (imageFrmae != null && !imageFrmae.isEmpty() && imageFrmae.containsKey("pts")) {
				framePts = Long.parseLong(imageFrmae.get("pts").toString());
			}
			KesonUtils.kesonDeepDelete(frame_keson);

		}
		//h264 end

		int type = KestrelApi.kestrel_frame_mem_type(kestrel_decode_frame.getValue());
		if(useDeviceDecoder && type != KestrelApi.KESTREL_MEM_DEVICE)
			FrameUtils.batch_free_frame(kestrel_decode_frame);
		else if(!useDeviceDecoder && type != KestrelApi.KESTREL_MEM_HOST)
			FrameUtils.batch_free_frame(kestrel_decode_frame);


		VideoFrame videoFrame = VideoFrame.builder().frameIndex(frameIndex).build();

		if(kestrel_decode_frame.getValue() == null) {
			errorInternalTime ++;
			//errorTime++;
			FrameUtils.batch_free_frame(kestrel_decode_frame);
			if(errorInternalTime % Utils.instance.videoFrameNullCount == 0) {
				log.error("[VideoHandleLog] unable to get frames for {} times,error info deviceID:{},rtsp:{}, cause: KPLUGIN_E_INTERNAL)",
						Utils.instance.videoFrameNullCount, device.getDeviceId(), device.getRtspSource());
				//ffmpeg propose
				ajustVideo(device.getRtspSource(), device.getDeviceId());
				try { Thread.sleep(1000); } catch (InterruptedException e) { }
				throw new RuntimeException("KPLUGIN_E_INTERNAL");
			}
			return videoFrame;
		}

        frameIndex ++;
        errorTime = 0;
		errorInternalTime = 0;
		errorAgainTime = 0;
		errorEofTime = 0;


		if(!needHost && !needDevice) {
			FrameUtils.batch_free_frame(kestrel_decode_frame);
			return videoFrame;
		}else if(needHost && needDevice) {
			if(useDeviceDecoder) {
				kestrel_reload_frame.setValue(FrameUtils.ref_or_download_frame(kestrel_decode_frame.getValue()));
				videoFrame.gpuFrame = kestrel_decode_frame.getValue();
				videoFrame.cpuFrame = kestrel_reload_frame.getValue();
			}else {
				if(Initializer.isDevice()) {
					FramePoolUtils.useBufferKey("Decoder");
					kestrel_reload_frame.setValue(FrameUtils.ref_or_upload_buffered_frame(kestrel_decode_frame.getValue()));
					videoFrame.gpuFrame = kestrel_reload_frame.getValue();
					videoFrame.cpuFrame = kestrel_decode_frame.getValue();
				}else {
					videoFrame.cpuFrame = kestrel_decode_frame.getValue();
					videoFrame.gpuFrame = FrameUtils.ref_frame(videoFrame.cpuFrame);
				}
			}
		}else if(!needHost && needDevice) {
			if(useDeviceDecoder) {
				videoFrame.gpuFrame = kestrel_decode_frame.getValue();
				//kestrel_reload_frame.setValue(FrameUtils.ref_or_download_frame(kestrel_decode_frame.getValue()));
				//videoFrame.cpuFrame = kestrel_reload_frame.getValue();
			}else {
				if(Initializer.isDevice()) {
					FramePoolUtils.useBufferKey("Decoder");
					kestrel_reload_frame.setValue(FrameUtils.ref_or_upload_buffered_frame(kestrel_decode_frame.getValue()));
					videoFrame.gpuFrame = kestrel_reload_frame.getValue();
					//videoFrame.cpuFrame = kestrel_decode_frame.getValue();
					FrameUtils.batch_free_frame(kestrel_decode_frame);
				}else {
					videoFrame.cpuFrame = kestrel_decode_frame.getValue();
					videoFrame.gpuFrame = FrameUtils.ref_frame(videoFrame.cpuFrame);
				}
			}
		}else if(needHost && !needDevice){
			if(useDeviceDecoder) {
				kestrel_reload_frame.setValue(FrameUtils.ref_or_download_frame(kestrel_decode_frame.getValue()));
				videoFrame.cpuFrame = kestrel_reload_frame.getValue();
				FrameUtils.batch_free_frame(kestrel_decode_frame);
			}else {
				videoFrame.cpuFrame = kestrel_decode_frame.getValue();
			}
		}

		kestrel_decode_frame.setValue(null);
		kestrel_reload_frame.setValue(null);

		videoStatus = VideoStatus.OK;

		if(Utils.instance.useSelfCalCapturedTime && framePts > 0){
			try {
				Map<String, Object> chosenStreamInfo = chosenStreamInfoMap.get(device.getDeviceId());
				Map<String, Object> timeBaseMap = (chosenStreamInfo !=null) ? (Map<String, Object>) chosenStreamInfo.get("time_base"): null;

				long firstCurrentMillions = getFirstCurrentMillions(System.currentTimeMillis(), device.getDeviceId());
				long firstFrame = getFirstFrame(videoFrame.getCapturedTime(), device.getDeviceId());

				long lostFame = videoFrame.getCapturedTime() - firstFrame;

				int den = (timeBaseMap != null) ? (int) timeBaseMap.get("den") : 90000;
				int num = (timeBaseMap != null) ? (int) timeBaseMap.get("num") : 1;

				double base = num / (double) den;

				double stepTime = base * lostFame * 1000;

				double selfCalTime = firstCurrentMillions + stepTime;
                if(logged) {
					log.info("setCapturedTime,{},{}, device:{},{},{},{},{}", System.currentTimeMillis(), videoFrame.getCapturedTime(), device.getDeviceId(), chosenStreamInfo, firstCurrentMillions, firstFrame, framePts);
				}
				videoFrame.setCapturedTime((long) selfCalTime);

			}catch (Exception e){
				log.error("setCapturedTimeErr:{}", e.getMessage());
			}

		}else if (useJavaCapturedTime){
			videoFrame.setCapturedTime(System.currentTimeMillis());
		}

//		Pointer FrameCgo =  KestrelApi.PullFrame(device.realRtspSource(),demuxerCtx, decoderCtx, fpCtx);
//
//		log.info("FrameCgo{}", FrameCgo);
//		KestrelApi.ReleaseFrame(FrameCgo);
		if(logged)
    		log.info("Video [" + device.getDeviceId() + "] multiplex[" + (multiplexDecoder != null) + "] cost[" + (System.currentTimeMillis() - now) + "]");

		return videoFrame;
	}

	private void fetchFrameAgain(String rtspSource, String deviceId) {
		VideoFrame videoFrame = null;
		try {
			videoFrame = FrameUtils.fetch_up_down_load_frame_path(rtspSource);
			String path = FrameUtils.save_image_as_jpg(videoFrame.getFrame(), ImageUtils.newFileWithMkdir("fetchFrame"),0);
			if(path.equals(FrameUtils.NOIMAGE)){
				log.warn("ffmpegFetchAgainErr, image_path:{},rtspSource:{},deviceId:{}", path, rtspSource, deviceId);
			}
			log.info("ffmpegFetchAgain, image_path:{},rtspSource:{},deviceId:{}", path, rtspSource, deviceId);
		} catch (Exception e){
			log.error("PullFrameFetch, error:{}", e.getMessage());
		} finally {
			FrameUtils.batch_free_frame(videoFrame);
		}

	}
	public void ajustVideo(String rtspSource, String deviceId) {
		log.info("ffmpegFetchAgainStart,rtspSource:{},deviceId:{}", rtspSource, deviceId);

		FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(device.getRtspSource());
		grabber.setOption("rtsp_transport","tcp");
		grabber.setOption("stimeout", "3000000");
		grabber.setOption("timeout","2000000");
		try {
			grabber.start();
			log.info("ffmpegFetchAgainEnd finalVideo [" + device.getDeviceId() + "] RtspWidth:[" + device.getRtspWidth() + "], RtspHeight[" + device.getRtspHeight() + "]");
		} catch (Exception e) {
			log.error("ffmpegFetchAgainErr can not open stream [" + device.getDeviceId() + "], [" + device.getRtspSource() + "].",e);

		}finally {
			try { grabber.close(); } catch (Exception e) { }
		}
	}

	// 0  全部走 opencv h265
	// 1  h265走 h265的，h264走h264的
	// 2、全部走 kestrel decoder
	public void setH265CodeC(boolean h265CodeC) {
	    this.h265CodeC = h265CodeC;
	    this.useExternalH265Setting = true;
	}

	protected boolean selectH265(String rtsp, Pointer chosen_stream){


		int useH265 = Utils.instance.useH265;
		if(!isStreamEndless(rtsp) && !Utils.instance.offline_flag){//离线文件默认264
			return false;
		}
		if(StringUtils.isNotBlank(device.getProcessors()) && device.getDeviceUseH265() > 0){
			useH265 = device.getDeviceUseH265();
		}
		if (useH265 == 0) {
			return true;
		} else if (useH265 == 1) {
			//流是h264还是走原来，
			return Kestrel_codecLibrary.kestrel_codec_id_e.KESTREL_CODEC_ID_HEVC == checkCodecType(chosen_stream);
		} else if (useH265== 2) {
			return false;
		}
		return false;
	}

	private int checkCodecType(Pointer chosen_stream) {

		Map<String, Object> codecparMap = (Map<String, Object>) ((Map<String, Object>) KesonUtils.kesonToJson(chosen_stream)).get("codecpar");


		return ((Number) codecparMap.get("codec_id")).intValue();

	}

	/** 打开decoder */
	@SuppressWarnings("unchecked")
	protected synchronized void open() {
		if(demuxer != null)
    		return ;

		Initializer.bindDeviceOrNot();

		System.setProperty("jna.library.path", "/usr/cognitivesvc/");
		log.info("demuxer kestrel_annotator_open ,rtspURL: {}", device.realRtspSource());

		//5000000
		String annotatorConfig = "{ \"filename\": \"" + device.realRtspSource() + "\", \"options\" : {\"rtsp_flags\" : \"prefer_tcp\", \"stimeout\" : \"" + Utils.instance.streamOpenTimeout * 1000 + "\"}}";
		log.info("[VideoOpenLog] open stream device id: {}, config: {}", device.getDeviceId(), annotatorConfig);

		demuxer = KestrelApi.kestrel_annotator_open(Initializer.demuxerName, annotatorConfig);

		PointerByReference k_stream_infos = new PointerByReference();
		log.info("demuxer startup ,rtspURL: {}", device.realRtspSource());
		int ret = KestrelApi.kestrel_annotator_startup(demuxer, KestrelApi.keson_parse("{\"id\": " + Utils.keyToContextId(device.getDeviceId()) + "}"), k_stream_infos);
		log.info("demuxer startup end ,rtspURL: {}, ret:{}", device.realRtspSource(), ret);
		if (ret != 0)
			throw new RuntimeException("unable to open stream,cause: can not create demuxer, please check. + deviceID :"
					+ device.getDeviceId() + " rtsp: " + device.getRtspSource() + "response:" + ret);


		lastFrameSource = device.realRtspSource();
		chosenStreamId = -1;
		Pointer chosen_stream = null;

		for (int index = 0; index < KestrelApi.keson_array_size(k_stream_infos.getValue()); ++index) {
			Pointer k_stream_info = KestrelApi.keson_get_array_item(k_stream_infos.getValue(), index);
			Pointer k_stream_type = KestrelApi.keson_get_object_item(k_stream_info, "type");
			long stream_type = KestrelApi.keson_get_int(k_stream_type);

			if (stream_type == KestrelApi.KESTREL_VIDEO_FRAME) {
				chosenStreamId = index;
				chosen_stream = k_stream_info;
				break;
			}
		}

		if (chosen_stream == null || chosenStreamId < 0) {
			close();
			KesonUtils.kesonDeepDelete(k_stream_infos);
			throw new RuntimeException("[" + device.realRtspSource() + "] has no video stream.");
		}

		Map<String, Object> chosenStreamMap = (Map<String, Object>)KesonUtils.kesonToJson(chosen_stream);
		
		// 先尝试使用avg_frame_rate
		JSONObject avgFrameRate = (JSONObject)chosenStreamMap.get("avg_frame_rate");
		int num = 0;
		int den = 0; // 初始化为0，以判断是否需要使用r_frame_rate
		
		boolean useAvgFrameRate = false;
		if (avgFrameRate != null) {
			Object numObj = avgFrameRate.get("num");
			Object denObj = avgFrameRate.get("den");
			
			if (numObj != null && denObj != null) {
				num = (Integer) numObj;
				den = (Integer) denObj;
				if (!(num == 0 && den == 0)) {
					useAvgFrameRate = true;
				}
			}
		}
		// 如果avg_frame_rate无效或值为0，使用r_frame_rate
		if (!useAvgFrameRate) {
			JSONObject rFrameRate = (JSONObject)chosenStreamMap.get("r_frame_rate");
			if (rFrameRate != null) {
				num = (Integer) rFrameRate.get("num");
				Integer denValue = (Integer) rFrameRate.get("den");
				if (denValue != null && denValue != 0) {
					den = denValue;
				}
			}
		}
		// 确保分母不为0
		if (den == 0) {
			log.warn("Frame rate denominator is zero, defaulting to den=1");
			den = 1;
		}
		int frameRate = Math.round((float) num / den);

		String chosen_stream_config_string = KesonUtils.kesonToString(chosen_stream);
		log.info("create decoder using config : " + chosen_stream_config_string);
		if(frameRate>=100 || frameRate<=5){
//			使用ffmpeg 拉去一遍
			log.info("chosen_stream frameRate is error use FFmpegFrameGrabber ");
			FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(device.getRtspSource());
			grabber.setOption("rtsp_transport","tcp");
			grabber.setOption("stimeout", String.valueOf(Utils.instance.streamOpenTimeout * 1000));
			grabber.setOption("timeout", String.valueOf((Utils.instance.streamOpenTimeout -1000) * 1000));
			try {
				grabber.start();
				log.info("detectVideo [" + device.getDeviceId() + "] RtspWidth:[" + grabber.getImageWidth() + "], RtspHeight[" + grabber.getImageHeight() + "], VideoRate[" + (int)grabber.getVideoFrameRate() + "]");
				frameRate = (int)grabber.getVideoFrameRate();
				if (frameRate>=100 || frameRate<=5){
//					帧率还是有问题，使用默认帧率
					frameRate = 25;
				}
			} catch (Exception e) {
				throw new RuntimeException("[" + device.realRtspSource() + "] FFmpegFrameGrabber get videoRate  error");
			}finally {
				try { grabber.close(); } catch (Exception e) { }
			}
		}
		chosenStreamMap.put("frameRate",frameRate);
		
		// 只有在没有外部设置时才自动选择H265
		if (!useExternalH265Setting) {
		    h265CodeC = selectH265(device.realRtspSource(), chosen_stream);
		}
		log.info("open stream codec type h265: {}, devices:{},rtsp:{},frameRate:{},notRemote:{},format:{}", h265CodeC, device.getDeviceId(), device.getRtspSource(),frameRate, !isStreamEndless(device.realRtspSource()), device.getDecoderFormat());
		
		if(h265CodeC){
//			demuxer = null;
//			chosenStreamId = -1;
			try {

				random = UUID.randomUUID().toString().replaceAll("-", "");
				String str = device.realRtspSource() + "random"+ random;  // 待传递的字符串
				byte[] utf8Bytes = str.getBytes(StandardCharsets.UTF_8); // 将字符串转换为UTF-8编码的字节数组
				int length = utf8Bytes.length; // 获取字节数组的长度

				int buffer = Utils.instance.nvDecoderFramePoolSize;
				int nvBuffer = Utils.instance.nvDecoderBuffSize;
				String format = "nv12";
//				if (device.getFrameBuffer() != null && device.getFrameBuffer() >0)
//					buffer = device.getFrameBuffer();

				if (StringUtils.isNotBlank(device.getDecoderFormat()))
					format = device.getDecoderFormat();

				 KestrelApi.kestrel_create_construct_params(utf8Bytes ,length, format, nvBuffer, buffer, "");

				 KestrelApi.kestrel_frame_createDemuxer(utf8Bytes ,length);
				 KestrelApi.kestrel_frame_createDecoderRev( utf8Bytes ,length);
				 KestrelApi.kestrel_frame_create_pool(utf8Bytes ,length);
//				 chosenStreamInfoMap.put(device.getDeviceId(), ((Map<String, Object>) KesonUtils.kesonToJson(chosen_stream)));

			}catch (Exception e){
				e.printStackTrace();
			}
//			if(h265PrivateDecoder == null || !checkStream) {
//				close();
//				throw new RuntimeException("can not open decoder, please check.");
//			}

		}else {
			if (useMultiplexDecoder && useDeviceDecoder) {
				Map<String, Object> codecparMap = (Map<String, Object>) ((Map<String, Object>) KesonUtils.kesonToJson(chosen_stream)).get("codecpar");
				String key = ((Number) codecparMap.get("width")).intValue() + "_" + ((Number) codecparMap.get("height")).intValue();

				MultiplexDecodeHolder holder = multiplexDecoderMap.get(key);
				if (holder == null) {
					synchronized (multiplexDecoderMap) {
						holder = multiplexDecoderMap.get(key);
						if (holder == null) {
							holder = new MultiplexDecodeHolder(chosen_stream);
							multiplexDecoderMap.put(key, holder);
						}
					}
				}

				multiplexDecoder = holder.openStream(this);
			} else {
				int buffer = Utils.instance.nonSeenReusedFrameCount;
				String strategy = Utils.instance.mempoolStrategy;

				if (device.getFrameBuffer() != null)
					buffer = device.getFrameBuffer();

				if (StringUtils.isNotBlank(device.getFrameBufferStrategy()))
					strategy = device.getFrameBufferStrategy();

				privateDecoder = createDecoderHandle(chosen_stream, Long.parseLong(Initializer.deviceId), device.getDecoderFormat(), buffer, strategy, useDeviceDecoder, device.getDeviceId());
			}
			KesonUtils.kesonDeepDelete(k_stream_infos);

			if(multiplexDecoder == null && privateDecoder == null) {
				close();
				throw new RuntimeException("can not open decoder, please check.");
			}
		}
		chosenStreamInfoMap.put(device.getDeviceId(), chosenStreamMap);
    }

	/** 关闭decoder */
	protected synchronized void close(){
		FrameUtils.batch_free_frame(kestrel_decode_frame, kestrel_reload_frame);
		PointerByReference tub = new PointerByReference();
		if(demuxer != null) {
			tub.setValue(demuxer);
			KestrelApi.kestrel_annotator_close(tub);
		}

		if(multiplexDecoder != null) {
	    	multiplexDecoder.closeStream(this);

	    	multiplexDecoder.getParent().tryDeflate();//关闭流的来做
		}else if(privateDecoder != null){
			tub.setValue(privateDecoder.getValue());
			KestrelApi.kestrel_annotator_close(tub);
		}

		demuxer = null;
		privateDecoder = null;
		multiplexDecoder = null;
		chosenStreamId = -1;
		if(h265CodeC && h265PrivateDecoder != null){
			KestrelApi.kestrel_close_opencv_frame(h265PrivateDecoder);
			h265PrivateDecoder = null;
		}


		closeCurrentMillions(device.getDeviceId());
		closeFirstFrame(device.getDeviceId());

		if(h265CodeC) {
			String str = device.realRtspSource() + "random" + random;  // 待传递的字符串
			byte[] utf8Bytes = str.getBytes(StandardCharsets.UTF_8); // 将字符串转换为UTF-8编码的字节数组
			int length = utf8Bytes.length; // 获取字节数组的长度

			KestrelApi.ReleaseDecoder(utf8Bytes, length);
		}

		h265CodeC = false;
	}

	protected int grabberPrivateFrame(PointerByReference frame_keson) {
		PointerByReference packet_keson = new PointerByReference();
		int ret = KestrelApi.KPLUGIN_OK;

		for(int limit = 100; limit >= 0; -- limit) {
			KestrelApi.kestrel_annotator_process(demuxer, demuxer_in, packet_keson);

	        long stream_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(packet_keson.getValue(), "stream_id"));
	        if (stream_id != (long) chosenStreamId) {
	        	ret = KestrelApi.KPLUGIN_E_AGAIN;
	        	KesonUtils.kesonDeepDelete(packet_keson);
	        	continue;
	        }
	        long flags = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(packet_keson.getValue(), "flags"));
	        if(flags == 4) {
				log.error("decoder grabberPrivateFrame error {}", "EOF");
	        	//KesonUtils.kesonDeepDelete(packet_keson);
	        	//return KestrelApi.KPLUGIN_E_EOF;
				//参考c++ 由decoder返回EOF
	        }

			ret = KestrelApi.kestrel_annotator_process(privateDecoder.getValue(), packet_keson.getValue(), frame_keson);

			//log.info("packet_keson{}", KesonUtils.kesonToJson(packet_keson));
			KesonUtils.kesonDeepDelete(packet_keson);

			if(ret == KestrelApi.KPLUGIN_OK) {
				break;
			}else {
	        	if(ret == KestrelApi.KPLUGIN_E_AGAIN) {
    				KesonUtils.kesonDeepDelete(frame_keson);
    	        	continue;
	        	}else
    				return ret;
			}
		}

		return ret;
	}

	protected int grabberMultiplexFrame(PointerByReference frame_keson) {
		synchronized(multiplexDecoder) { synchronized(multiplexDecoder) {//使用两层加锁是为了运行时可以更换解码器
			LinkedList<MultiplexDecodeHolder.MultiplexItem> multiplexQueue = multiplexDecoder.getMultiplexQueue();
			Iterator<MultiplexDecodeHolder.MultiplexItem> its = multiplexQueue.iterator();
			while(its.hasNext()) {
				MultiplexDecodeHolder.MultiplexItem item = its.next();
				if(item.getFrameKeson() == null)
					break;

				if(Objects.equals(item.getDeviceId(), device.getDeviceId())) {
					frame_keson.setValue(item.getFrameKeson());
					its.remove();

					if(Utils.instance.watchFrameTiktokLevel == -909)
						log.info("queue FASTOUT[" + device.getDeviceId() + "] pts[" + item.getPts() + "] queuesize[" + multiplexQueue.size() + "]");
				}
			}
		}}

		if(frame_keson.getValue() != null)
			return KestrelApi.KPLUGIN_OK;

		PointerByReference packet_keson = new PointerByReference();
		int ret = KestrelApi.KPLUGIN_OK;

		for(int limit = 1024; limit >= 0; -- limit) {
			KestrelApi.kestrel_annotator_process(demuxer, demuxer_in, packet_keson);

	        long stream_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(packet_keson.getValue(), "stream_id"));
	        if (stream_id != chosenStreamId) {
	        	ret = KestrelApi.KPLUGIN_E_AGAIN;
	        	KesonUtils.kesonDeepDelete(packet_keson);
	        	try { Thread.sleep(1); } catch (InterruptedException e) { }
	        	continue;
	        }

	        long flags = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(packet_keson.getValue(), "flags"));
	        if(flags == 4)
	        	return KestrelApi.KPLUGIN_E_EOF;
	        else if(flags != 1) {
        		ret = KestrelApi.KPLUGIN_E_AGAIN;
        		KesonUtils.kesonDeepDelete(packet_keson);
        		continue;
        	}

	        if(frameIndex <= 10) { //前五个包全部丢掉
	        	frameIndex ++ ;
	        	ret = KestrelApi.KPLUGIN_E_AGAIN;
        		KesonUtils.kesonDeepDelete(packet_keson);
        		continue;
	        }

	        PointerByReference packet = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(packet_keson.getValue(), "packet"));
			MultiplexDecodeHolder.MultiplexItem currentItem = MultiplexDecodeHolder.MultiplexItem.builder().deviceId(device.getDeviceId()).build();
        	synchronized(multiplexDecoder) { synchronized(multiplexDecoder) {//使用两层加锁是为了运行时可以更换解码器
        		long now = System.currentTimeMillis();
        		KestrelApi.kestrel_packet_set_pts(packet.getValue(), now);
        		KestrelApi.kestrel_packet_set_dts(packet.getValue(), now);
        		KestrelApi.kestrel_packet_set_stream_id(packet.getValue(), (int)Utils.keyToContextId(device.getDeviceId()));
        		currentItem.setPts(now);

        		LinkedList<MultiplexDecodeHolder.MultiplexItem> multiplexQueue = multiplexDecoder.getMultiplexQueue();
        		multiplexQueue.addLast(currentItem);

        		if(Utils.instance.watchFrameTiktokLevel == -909)
    				log.info("queue IN[" + device.getDeviceId() + "] pts[" + currentItem.getPts() + "] queuesize[" + multiplexQueue.size() + "]");

        		if(Utils.instance.watchFrameTiktokLevel == -909)
    				log.info("decoder IN pts[" + currentItem.getPts() + "] stream_id[" + Utils.keyToContextId(device.getDeviceId()) + "]");
        		ret = KestrelApi.kestrel_annotator_process(multiplexDecoder.getMultiplexDecoder().getValue(), packet_keson.getValue(), frame_keson);
				KesonUtils.kesonDeepDelete(packet_keson);

    			if(ret == KestrelApi.KPLUGIN_OK) {
    				MultiplexDecodeHolder.MultiplexItem firstNullItem = multiplexQueue.stream().filter(item -> Objects.isNull(item.getFrameKeson())).findFirst().get();
		    		PointerByReference frame = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(frame_keson.getValue(), "image"));

		    		if(Utils.instance.watchFrameTiktokLevel == -909)
						log.info("decoder OUT pts[" + KestrelApi.kestrel_frame_pts(frame.getValue()) + "] plane[" + KestrelApi.kestrel_frame_plane_origin(frame.getValue(), 0) + "] stream_id[" + KestrelApi.kestrel_frame_stream_id(frame.getValue()) + "]");

					if(KestrelApi.kestrel_frame_pts(frame.getValue()) == firstNullItem.getPts()) {
						firstNullItem.setFrameKeson(frame_keson.getValue());
					}else {
						//如果为不相等 说明顺序出问题了 刷新整个解码器
				    	log.warn("****************************************************************");
				    	log.warn("*******multiplex decoder is out of order, flush and reseting.***");
				    	log.warn("****************************************************************");

				    	multiplexDecoder.resetDecoder(device.getDeviceId());
    	            	continue;
					}

    				boolean found = false;
    				Iterator<MultiplexDecodeHolder.MultiplexItem> its = multiplexQueue.iterator();
    				while(its.hasNext()) {
    					MultiplexDecodeHolder.MultiplexItem item = its.next();

    					if(item.isClosed()) {
    						its.remove();

    						if(item.getFrameKeson() != null)
    							KesonUtils.kesonDeepDelete(item.getFrameKeson());

    						if(Utils.instance.watchFrameTiktokLevel == -909)
    							log.info("queue CLOSEOUT[" + device.getDeviceId() + "] pts[" + item.getPts() + "] queuesize[" + multiplexQueue.size() + "]");
    					}else if(Objects.equals(item.getDeviceId(), device.getDeviceId())) {
    						if(item.getFrameKeson() != null) {
    							found = true;
        						frame_keson.setValue(item.getFrameKeson());
        						its.remove();

        						if(Utils.instance.watchFrameTiktokLevel == -909)
        							log.info("queue OUT[" + device.getDeviceId() + "] pts[" + item.getPts() + "] queuesize[" + multiplexQueue.size() + "]");
    						}

    						break;
    					}
    				}

    				if(found)
    					break;
    			}else if(ret == KestrelApi.KPLUGIN_E_AGAIN){
    				KesonUtils.kesonDeepDelete(frame_keson);
    	        	continue;
    			}else {
    				return ret;
    			}
        	}}
		}

		return ret;
	}

	protected static PointerByReference createDecoderHandle(Pointer chosen_stream, long gpuId, String format, int maxBufferSize, String bufferStrategy, boolean useDeviceDecoder, String deviceID) {
		Pointer k_param = KestrelApi.keson_get_object_item(chosen_stream, "param");
		if(k_param == null) {
			k_param = KestrelApi.keson_create_object();
			KestrelApi.keson_add_item_to_object(chosen_stream, "param", k_param);
		}

		if(StringUtils.isNotBlank(format))
			KestrelApi.keson_add_item_to_object(k_param, "format", KestrelApi.keson_create_string(format));

		if(useDeviceDecoder && Initializer.isGpu()) {
			KestrelApi.keson_add_item_to_object(k_param, "gpu_device", KestrelApi.keson_create_int(gpuId));

			if(maxBufferSize > 0) {
				Pointer frame_pool = KestrelApi.keson_create_object();
				KestrelApi.keson_add_item_to_object(k_param, "frame_pool", frame_pool);

				KestrelApi.keson_add_item_to_object(frame_pool, "max_size", KestrelApi.keson_create_int(maxBufferSize));
				KestrelApi.keson_add_item_to_object(frame_pool, "pre_allocated_size", KestrelApi.keson_create_int(maxBufferSize));

				if(StringUtils.isNotBlank(bufferStrategy))
					KestrelApi.keson_add_item_to_object(frame_pool, "strategy", KestrelApi.keson_create_string(bufferStrategy));
			}
		}

		String chosen_stream_config_string = KesonUtils.kesonToString(chosen_stream);
		log.info("create decoder using config : " + chosen_stream_config_string);



//		chosenStreamInfoMap.put(deviceID, ((Map<String, Object>) KesonUtils.kesonToJson(chosen_stream)));

		return new PointerByReference(KestrelApi.kestrel_annotator_open(Initializer.decoderName, chosen_stream_config_string));
	}

	public static boolean isStreamRemote(String source) { return source.startsWith("rtsp:") || source.startsWith("rtmp:") || source.startsWith("http"); }

	public static boolean isStreamEndless(String source) { return isStreamRemote(source) || source.endsWith("testDebugCog.mp4"); }

	/**保存帧的结构 */
	@Getter
	@Builder
	public static class VideoFrame implements AutoCloseable{
		protected Pointer cpuFrame;
		protected Pointer gpuFrame;

		/** 该帧是这路流的第几帧*/
		@Builder.Default
		protected long frameIndex = -1;

		@Builder.Default
		protected long frameDecodeCost = 0;

		@Getter @Setter
		private String rFrameRate ;

		public Pointer getFrame() {
			if(gpuFrame != null)
				return gpuFrame;

			return cpuFrame;
		}

		public VideoFrame ref() {
			return VideoFrame.builder()
							 .gpuFrame(FrameUtils.ref_frame(gpuFrame))
							 .cpuFrame(FrameUtils.ref_frame(cpuFrame))
							 .frameIndex(frameIndex)
							 .build();
		}

		public VideoFrame ref(long frameIndexPara) {
			return VideoFrame.builder()
							 .gpuFrame(FrameUtils.ref_frame(gpuFrame))
							 .cpuFrame(FrameUtils.ref_frame(cpuFrame))
							 .frameIndex(frameIndexPara)
							 .build();
		}


		@Override
		public synchronized void close() {
			FrameUtils.batch_free_frame(cpuFrame, gpuFrame);

			cpuFrame = null;
			gpuFrame = null;
		}

		public long getCapturedTime() {
			return KestrelApi.kestrel_frame_pts(Objects.requireNonNullElse(gpuFrame, cpuFrame));
		}

		public void setCapturedTime(long pts) {
			if(cpuFrame != null)
				KestrelApi.kestrel_frame_set_pts(cpuFrame, pts);

			if(gpuFrame != null)
				KestrelApi.kestrel_frame_set_pts(gpuFrame, pts);
		}
		public void setFrameDecodeCost(long pts) {
			frameDecodeCost = pts;
		}
	}

	@Getter
	@Setter
	public static final class MultiplexDecodeHolder {
		private final Pointer chosen_stream;
		private final List<MultiplexDecoder> multiplexDecoders = new ArrayList<MultiplexDecoder>();

		public MultiplexDecodeHolder(Pointer chosenStream) {
			chosen_stream = KestrelApi.keson_duplicate(chosenStream, 1);

			KestrelApi.keson_delete_item_from_object(chosen_stream, "start_time");
			KestrelApi.keson_add_item_to_object(chosen_stream, "start_time", KestrelApi.keson_create_int(0));

			Pointer k_codecpar = KestrelApi.keson_get_object_item(chosen_stream, "codecpar");
			KestrelApi.keson_delete_item_from_object(k_codecpar, "video_delay");
			KestrelApi.keson_add_item_to_object(k_codecpar, "video_delay", KestrelApi.keson_create_int(0));

			Pointer k_avg_frame_rate = KestrelApi.keson_get_object_item(chosen_stream, "avg_frame_rate");
			KestrelApi.keson_delete_item_from_object(k_avg_frame_rate, "num");
			KestrelApi.keson_add_item_to_object(k_avg_frame_rate, "num", KestrelApi.keson_create_int(1));
			KestrelApi.keson_delete_item_from_object(k_avg_frame_rate, "den");
			KestrelApi.keson_add_item_to_object(k_avg_frame_rate, "den", KestrelApi.keson_create_int(1));

			Pointer k_r_frame_rate = KestrelApi.keson_get_object_item(chosen_stream, "r_frame_rate");
			KestrelApi.keson_delete_item_from_object(k_r_frame_rate, "num");
			KestrelApi.keson_add_item_to_object(k_r_frame_rate, "num", KestrelApi.keson_create_int(1));
			KestrelApi.keson_delete_item_from_object(k_r_frame_rate, "den");
			KestrelApi.keson_add_item_to_object(k_r_frame_rate, "den", KestrelApi.keson_create_int(1));
		}

		public synchronized MultiplexDecoder openStream(VideoStream videoStream) {
			MultiplexDecoder targetDecoder = null;

			for(int index = 0; index < multiplexDecoders.size(); index ++)
				if(multiplexDecoders.get(index).getMultiplexOngoing().size() < Utils.instance.multiplexDecoderDuplicate)
					targetDecoder = multiplexDecoders.get(index);

			if(targetDecoder == null) {
				log.info("multiplex decoder is inflating.");

				PointerByReference decoder = createDecoderHandle(chosen_stream, Long.parseLong(Initializer.deviceId), Utils.instance.multiplexDecodeFormat, Utils.instance.multiplexDecoderFrameCount, Utils.instance.multiplexDecoderStrategy, true, videoStream.device.getDeviceId());
				targetDecoder = MultiplexDecoder.builder().parent(this).chosen_stream(chosen_stream).multiplexDecoder(decoder).multiplexOngoing(new ArrayList<VideoStream>()).multiplexQueue(new LinkedList<MultiplexItem>()).build();
				multiplexDecoders.add(targetDecoder);
			}

			targetDecoder.getMultiplexOngoing().add(videoStream);

			return targetDecoder;
		}

		public synchronized void tryDeflate() {
			int idle = multiplexDecoders.stream().mapToInt(h -> Utils.instance.multiplexDecoderDuplicate - h.getMultiplexOngoing().size()).sum();
			if(idle < Utils.instance.multiplexDecoderDuplicate)
				return ;

			int targetNum = Stream.iterate(0, i -> i + 1).limit(multiplexDecoders.size())
				.min((l, r) -> Integer.compare(multiplexDecoders.get(l).getMultiplexOngoing().size(), multiplexDecoders.get(r).getMultiplexOngoing().size()))
				.orElse(-1);

			if(targetNum < 0)
				return ;

			log.info("multiplex decoder is deflating.");

			MultiplexDecoder multiplexDecoder = multiplexDecoders.remove(targetNum);
			synchronized(multiplexDecoder) {
				for(VideoStream stream : multiplexDecoder.getMultiplexOngoing())
					stream.multiplexDecoder = openStream(stream);

				for(MultiplexItem item : multiplexDecoder.getMultiplexQueue())
					if(item.getFrameKeson() != null)
						KesonUtils.kesonDeepDelete(item.getFrameKeson());

				KestrelApi.kestrel_annotator_close(multiplexDecoder.getMultiplexDecoder());
			}
		}
		
		@Getter
		@Setter
		@Builder
		public static final class MultiplexDecoder{
			private MultiplexDecodeHolder     parent;
			private Pointer                   chosen_stream;
			private PointerByReference        multiplexDecoder;
			private List<VideoStream>         multiplexOngoing;
			private LinkedList<MultiplexItem> multiplexQueue;

			public synchronized void resetDecoder(String deviceID) {
				KestrelApi.kestrel_annotator_close(multiplexDecoder);

				Iterator<MultiplexDecodeHolder.MultiplexItem> its = multiplexQueue.iterator();
		    	while(its.hasNext()) {
		    		MultiplexDecodeHolder.MultiplexItem item = its.next();

					its.remove();

					if(item.getFrameKeson() != null)
						KesonUtils.kesonDeepDelete(item.getFrameKeson());
		    	}

		    	multiplexDecoder = createDecoderHandle(chosen_stream, Long.parseLong(Initializer.deviceId), Utils.instance.multiplexDecodeFormat, Utils.instance.multiplexDecoderFrameCount, Utils.instance.multiplexDecoderStrategy, true, deviceID);
			}

			public synchronized void closeStream(VideoStream stream) {
				multiplexOngoing.remove(stream);

				Iterator<MultiplexItem> its = multiplexQueue.iterator();
				while(its.hasNext()) {
					MultiplexItem item = its.next();
					if(!Objects.equals(item.getDeviceId(), stream.getDevice().getDeviceId()))
						continue;

					if(item.getFrameKeson() != null) {
						KesonUtils.kesonDeepDelete(item.getFrameKeson());
						its.remove();
					}else
						item.setClosed(true);
				}
			}
		}

		@Getter
		@Setter
		@Builder
		public static final class MultiplexItem {
			private volatile String  deviceId;
			private volatile Pointer frameKeson;
			private volatile long    pts;

			@Builder.Default
			private volatile boolean isClosed = false;
		}
	}

	public long getFirstFrame(long pts, String deviceID){

		if(!firstFrameMap.isEmpty() && firstFrameMap.containsKey(deviceID)){

			return firstFrameMap.get(deviceID);
		}
		firstFrameMap.put(deviceID, pts);
		return pts;
	}
	public long getFirstCurrentMillions(long pts, String deviceID){

		if(!firstCurrentMap.isEmpty() && firstCurrentMap.containsKey(deviceID) ){
			   return firstCurrentMap.get(deviceID);
		}
		firstCurrentMap.put(deviceID , pts);
		return pts;
	}
	public long closeCurrentMillions(String deviceID){
		if(!firstCurrentMap.isEmpty() && firstCurrentMap.containsKey(deviceID) ){
			log.info("closeCurrentMillions {}", deviceID);
			 firstCurrentMap.remove(deviceID);
		}
		return 0;
	}
	public long closeFirstFrame(String deviceID){
		if(!firstFrameMap.isEmpty() && firstFrameMap.containsKey(deviceID)){
            log.info("closeFirstFrame {}", deviceID);
			return firstFrameMap.remove(deviceID);
		}
		return 0;
	}
}
