package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;


/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_mem</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */
public interface Kestrel_memLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_memLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_memLibrary INSTANCE = (Kestrel_memLibrary)Native.load(Kestrel_memLibrary.JNA_LIBRARY_NAME, Kestrel_memLibrary.class);
	/**
	 * @{<br>
	 * Original signature : <code>void* kestrel_mem_alloc(kestrel_plugin_instance, size_t, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:10</i>
	 */
	Pointer kestrel_mem_alloc(Pointer ins, long size, int policy);
	/**
	 * Original signature : <code>void* kestrel_mem_calloc(kestrel_plugin_instance, size_t, size_t, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:13</i>
	 */
	Pointer kestrel_mem_calloc(Pointer ins, long nmemb, long size, int policy);
	/**
	 * Original signature : <code>void* kestrel_mem_realloc(kestrel_plugin_instance, void*, size_t, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:16</i>
	 */
	Pointer kestrel_mem_realloc(Pointer ins, Pointer ptr, long size, int policy);
	/**
	 * Original signature : <code>int kestrel_mem_set(kestrel_plugin_instance, void*, uint8_t, size_t)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:19</i>
	 */
	int kestrel_mem_set(Pointer ins, Pointer ptr, byte value, long size);
	/**
	 * Original signature : <code>int kestrel_mem_copy(kestrel_plugin_instance, void*, kestrel_plugin_instance, void*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:22</i>
	 */
	int kestrel_mem_copy(Pointer s_ins, Pointer src, Pointer d_ins, Pointer dst, long size);
	/**
	 * Original signature : <code>int kestrel_mem_copy_async(kestrel_plugin_instance, void*, kestrel_plugin_instance, void*, size_t, kestrel_event*)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:26</i>
	 */
	int kestrel_mem_copy_async(Pointer s_ins, Pointer src, Pointer d_ins, Pointer dst, long size, PointerByReference evt);
	/**
	 * Original signature : <code>int kestrel_mem_copy2d(kestrel_plugin_instance, void*, size_t, kestrel_plugin_instance, void*, size_t, size_t, size_t)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:31</i>
	 */
	int kestrel_mem_copy2d(Pointer s_ins, Pointer src, long src_stride, Pointer d_ins, Pointer dst, long dst_stride, long linesize, long height);
	/**
	 * Original signature : <code>int kestrel_mem_copy2d_async(kestrel_plugin_instance, void*, size_t, kestrel_plugin_instance, void*, size_t, size_t, size_t, kestrel_event*)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:36</i>
	 */
	int kestrel_mem_copy2d_async(Pointer s_ins, Pointer src, long src_stride, Pointer d_ins, Pointer dst, long dst_stride, long linesize, long height, PointerByReference evt);
	/**
	 * Original signature : <code>int kestrel_mem_await(kestrel_event)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:41</i>
	 */
	int kestrel_mem_await(Pointer e);
	/**
	 * Original signature : <code>int kestrel_mem_map(kestrel_plugin_instance, void*, void**)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:44</i>
	 */
	int kestrel_mem_map(Pointer ins, Pointer src, PointerByReference dst);
	/**
	 * Original signature : <code>int kestrel_mem_unmap(kestrel_plugin_instance, void*)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:47</i>
	 */
	int kestrel_mem_unmap(Pointer ins, Pointer ptr);
	/**
	 * Original signature : <code>int kestrel_mem_free(kestrel_plugin_instance, void*)</code><br>
	 * <i>native declaration : include/kestrel_mem.h:50</i>
	 */
	int kestrel_mem_free(Pointer ins, Pointer ptr);
}
