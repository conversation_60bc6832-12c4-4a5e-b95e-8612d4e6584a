package com.sensetime.intersense.cognitivesvc.server.entities;

import com.sensetime.intersense.cognitivesvc.server.utils.AbstractHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QualityRes {

    private float score;
    private AbstractHandler.Detection.Rect detect;

    private float id;

    private double quality;

    private double confidence;

    private double alignerConfidence;

}
