package com.sensetime.intersense.cognitivesvc.server.kestrel.utils;

import java.util.concurrent.ThreadLocalRandom;

import com.sensetime.intersense.cognitivesvc.server.utils.KesonUtils;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ModelHolder implements AutoCloseable{
	public final Pointer[] pointers;
	private final int       batchSize;
	private final String    key;
	public  String    value;
	private final boolean   isFlock;
	private final boolean   tryReuse;
	
	public ModelHolder(String key, String value, int batchSize, int count, boolean lazy, boolean isFlock, boolean tryReuse) {
		this.key       = key;
		this.value     = value;
		this.isFlock   = isFlock;
		this.batchSize = batchSize;
		this.pointers  = new Pointer[Math.max(1, count)];
		this.tryReuse  = tryReuse;
		
		if(!lazy)
			open();
	}
	
	public int process(Pointer in, PointerByReference out) {
		Pointer pointer = getPointer();
		if(pointer == null) {
			synchronized(new Object()) {//这么干的目的是让其他线程 尽可能快的感知到数组内值的变化
				pointer = getPointer();
			}
		}
		int ret = 0;
		synchronized(pointer) {
			if(isFlock)
				ret = KestrelApi.flock_pipeline_input_and_output(pointer, key, in, out);
			else 
				ret = KestrelApi.kestrel_annotator_process(pointer, in, out);
		}
		if(ret != 0){
			log.error("[kestrel process] process error code:{}",ret);
		}
		return ret;
	}


	public PointerByReference controlForRemoveSource(long contextId) {
		if(!isFlock || pointers[0] == null)
			return new PointerByReference();
		
		Pointer input = KesonUtils.buildFlockRemoveInput(contextId, key);
		PointerByReference out = new PointerByReference();
		
		for(int index = 0; index < pointers.length; index++) {			
			synchronized(this){
				KestrelApi.flock_pipeline_control(pointers[index], KestrelApi.FLOCK_CTRL_CMD_REMOVE_SOURCE, input, out);
			}
		}
		
		KesonUtils.kesonDeepDelete(input);
		return out;
	}
	
	private synchronized void open() {
		if(pointers[0] != null)
			return;
		
		Initializer.bindDeviceOrNot();
		
		if(isFlock) {
			log.info("Flock count[" + pointers.length + "] config[" + value + "].");
		}else {
			log.info("Model count[" + pointers.length + "] [" + value + "] batchSize[" + batchSize + "] ");
		}
		
		for(int index = 0; index < pointers.length; index++) {
			if(isFlock) {
				pointers[index] = KestrelApi.flock_pipeline_create(value);
				if(pointers[index] == null)
					throw new RuntimeException("initializing Flock error config[" + value + "].");
			}else {
				pointers[index] = KestrelApi.kestrel_annotator_open_or_reuse(key, value, batchSize, tryReuse);
				if(pointers[index] == null)
					throw new RuntimeException("initializing Model error [" + key + "], [" + value + "]");
			}
		}
	}

	@Override
	public synchronized void close(){
		if(pointers[0] == null)
			return;
		
		for(int index = 0; index < pointers.length; index++) {
			if(pointers[index] != null) {
				if(isFlock) 
					KestrelApi.flock_pipeline_destroy(pointers[index]);
				else
					KestrelApi.kestrel_annotator_close_or_release(new PointerByReference(pointers[index]));
				
				pointers[index] = null;
			}
		}
	}
	
	private Pointer getPointer() {
		if(pointers[0] == null)
			synchronized(pointers) {
				if(pointers[0] == null) 
					open();
			}
		
		return pointers[ThreadLocalRandom.current().nextInt(pointers.length)];
	}
}
