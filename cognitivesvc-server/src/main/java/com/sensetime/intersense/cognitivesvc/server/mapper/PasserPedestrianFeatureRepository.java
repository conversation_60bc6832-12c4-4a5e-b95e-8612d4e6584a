package com.sensetime.intersense.cognitivesvc.server.mapper;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.sensetime.intersense.cognitivesvc.server.entities.PasserPedestrianFeature;
@Repository
public interface PasserPedestrianFeatureRepository extends JpaRepositoryImplementation<PasserPedestrianFeature, Integer>{
	
	@Query(value = "select *  from passer_pedestrian_feature p where p.id >= ?1 and p.id < ?2 and p.id % ?3 = ?4 and p.sts = 0 order by p.id asc", nativeQuery = true)
	public List<PasserPedestrianFeature> querySplit(int start, int end, int totalSplitNum, int currentSplitNum);
	
	@Query(value = "select count(p.id) from passer_pedestrian_feature p where p.id % ?1 = ?2 and p.sts = 0", nativeQuery = true)
	public long countSplit(int totalSplitNum, int currentSplitNum);
	
	@Query(value = "select p.id from PasserPedestrianFeature p where p.personUuid in ?1")
	public List<Integer> queryIdsByPersonUuids(List<String> personUuids);

	@Query(value = "select max(p.id) from PasserPedestrianFeature p")
	public Integer queryMaxId();
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByPersonUuidIn(List<String> personUuids);
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByCreateTsBetween(Date startTime, Date endTime);
	
	public List<PasserPedestrianFeature> findByPersonUuid(String personUuid);
}
