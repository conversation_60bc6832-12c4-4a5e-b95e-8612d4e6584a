package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import java.nio.ByteBuffer;
/**
 * J<PERSON> Wrapper for library <b>kestrel_dl</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_dlLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_core";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_dlLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_dlLibrary INSTANCE = (Kestrel_dlLibrary)Native.load(Kestrel_dlLibrary.JNA_LIBRARY_NAME, Kestrel_dlLibrary.class);
	/**
	 * https://docs.microsoft.com/en-us/windows/win32/api/libloaderapi/nf-libloaderapi-loadlibrarya<br>
	 * Original signature : <code>void* kestrel_dlopen(const char*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:16</i><br>
	 * @deprecated use the safer methods {@link #kestrel_dlopen(java.lang.String)} and {@link #kestrel_dlopen(com.sun.jna.Pointer)} instead
	 */
	@Deprecated 
	Pointer kestrel_dlopen(Pointer filename);
	/**
	 * https://docs.microsoft.com/en-us/windows/win32/api/libloaderapi/nf-libloaderapi-loadlibrarya<br>
	 * Original signature : <code>void* kestrel_dlopen(const char*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:16</i>
	 */
	Pointer kestrel_dlopen(String filename);
	/**
	 * @note On Unix ref: dlopen() with RTLD_NOLOAD. On Windows ref: GetModuleHandle()<br>
	 * Original signature : <code>void* kestrel_dlhandle(const char*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:24</i><br>
	 * @deprecated use the safer methods {@link #kestrel_dlhandle(java.lang.String)} and {@link #kestrel_dlhandle(com.sun.jna.Pointer)} instead
	 */
	@Deprecated 
	Pointer kestrel_dlhandle(Pointer filename);
	/**
	 * @note On Unix ref: dlopen() with RTLD_NOLOAD. On Windows ref: GetModuleHandle()<br>
	 * Original signature : <code>void* kestrel_dlhandle(const char*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:24</i>
	 */
	Pointer kestrel_dlhandle(String filename);
	/**
	 * https://docs.microsoft.com/en-us/windows/win32/api/libloaderapi/nf-libloaderapi-getprocaddress<br>
	 * Original signature : <code>void* kestrel_dlsym(void*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:33</i><br>
	 * @deprecated use the safer methods {@link #kestrel_dlsym(com.sun.jna.Pointer, java.lang.String)} and {@link #kestrel_dlsym(com.sun.jna.Pointer, com.sun.jna.Pointer)} instead
	 */
	@Deprecated 
	Pointer kestrel_dlsym(Pointer handle, Pointer symbol);
	/**
	 * https://docs.microsoft.com/en-us/windows/win32/api/libloaderapi/nf-libloaderapi-getprocaddress<br>
	 * Original signature : <code>void* kestrel_dlsym(void*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:33</i>
	 */
	Pointer kestrel_dlsym(Pointer handle, String symbol);
	/**
	 * @return On success, returns 0.<br>
	 * Original signature : <code>int32_t kestrel_dlclose(void*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:40</i>
	 */
	int kestrel_dlclose(Pointer handle);
	/**
	 * @return<br>
	 * Original signature : <code>char* kestrel_dlerror()</code><br>
	 * <i>native declaration : include/kestrel_dl.h:45</i>
	 */
	Pointer kestrel_dlerror();
	/**
	 * @brief the space previously allocated by `kestrel_dlerror`<br>
	 * Original signature : <code>void kestrel_dlerror_free(char*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:49</i><br>
	 * @deprecated use the safer methods {@link #kestrel_dlerror_free(java.nio.ByteBuffer)} and {@link #kestrel_dlerror_free(com.sun.jna.Pointer)} instead
	 */
	@Deprecated 
	void kestrel_dlerror_free(Pointer err);
	/**
	 * @brief the space previously allocated by `kestrel_dlerror`<br>
	 * Original signature : <code>void kestrel_dlerror_free(char*)</code><br>
	 * <i>native declaration : include/kestrel_dl.h:49</i>
	 */
	void kestrel_dlerror_free(ByteBuffer err);
}
