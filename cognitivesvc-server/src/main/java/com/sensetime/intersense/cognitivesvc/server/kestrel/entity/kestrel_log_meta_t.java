package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_log.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_log_meta_t extends Structure {
	/**
	 * @see kestrel_log_level_e<br>
	 * < Log level<br>
	 * C type : kestrel_log_level_e
	 */
	public int level;
	/**
	 * < Log label<br>
	 * C type : char[15 + 1]
	 */
	public byte[] label = new byte[15 + 1];
	public int line_no;
	/** C type : const char* */
	public Pointer filename;
	/** C type : const char* */
	public Pointer funcname;
	public kestrel_log_meta_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("level", "label", "line_no", "filename", "funcname");
	}
	/**
	 * @param level @see kestrel_log_level_e<br>
	 * < Log level<br>
	 * C type : kestrel_log_level_e<br>
	 * @param label < Log label<br>
	 * C type : char[15 + 1]<br>
	 * @param filename C type : const char*<br>
	 * @param funcname C type : const char*
	 */
	public kestrel_log_meta_t(int level, byte label[], int line_no, Pointer filename, Pointer funcname) {
		super();
		this.level = level;
		if ((label.length != this.label.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.label = label;
		this.line_no = line_no;
		this.filename = filename;
		this.funcname = funcname;
	}
	public kestrel_log_meta_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_log_meta_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_log_meta_t implements Structure.ByValue {
		
	};
}
