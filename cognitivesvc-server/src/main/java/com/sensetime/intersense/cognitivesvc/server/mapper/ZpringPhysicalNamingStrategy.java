package com.sensetime.intersense.cognitivesvc.server.mapper;

import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy;
import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.PhysicalNamingStrategy;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Locale;
import java.util.Objects;

@Component
public class ZpringPhysicalNamingStrategy  extends CamelCaseToUnderscoresNamingStrategy
        implements EnvironmentAware
//        implements EnvironmentAware
{

        @Setter
    private Environment environment;
//    @Autowired
//    private Environment environment;

    public Identifier toPhysicalTableName(Identifier name, JdbcEnvironment jdbcEnvironment) {
            String nameStr = name.getText();

            if(StringUtils.startsWith(nameStr, "${") && StringUtils.endsWith(nameStr, "}")) {
                String[] params = nameStr.substring(2, nameStr.length() - 1).split(":");
                nameStr = environment.getProperty(params[0]);

                if(nameStr == null && params.length > 1)
                    nameStr = params[1];
            }


        return super.toPhysicalTableName(Identifier.toIdentifier(Objects.requireNonNullElse(nameStr, name.getText())), jdbcEnvironment);
    }



}
