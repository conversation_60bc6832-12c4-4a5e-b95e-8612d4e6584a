package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;

import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.ptr.PointerByReference;
import java.util.Arrays;
import java.util.List;

/**
 * <i>native declaration : include/kestrel_frame_utils_struct.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_frame_transform_param_t extends Structure {
	/**
	 * input tensor<br>
	 * C type : kestrel_tensor
	 */
	public Pointer tensor;
	/**
	 * @see kestrel_tensor_color_e<br>
	 * Input tensor type: bgr/rgb/gray ref<br>
	 * `kestrel_tensor_color_e`<br>
	 * C type : kestrel_tensor_color_e
	 */
	public int tensor_color;
	/** preprocess combination */
	public int operators;
	/** number of frames and rois */
	public long batch;
	/**
	 * frames<br>
	 * C type : kestrel_frame*
	 */
	public PointerByReference frames;
	/**
	 * rois on frames<br>
	 * C type : kestrel_area2d_t*
	 */
	public Pointer rois;
	/**
	 * resized frame<br>
	 * C type : kestrel_size2d_t*
	 */
	public Pointer dst_sizes;
	/**
	 * warpaffine/perspective transformation matrix<br>
	 * C type : float[3][3]*
	 */
	public Pointer mats;
	/**
	 * means to substract<br>
	 * C type : kestrel_pixel_t
	 */
	public kestrel_pixel_t means;
	/**
	 * stds to divide<br>
	 * C type : kestrel_pixel_t
	 */
	public kestrel_pixel_t stds;
	/**
	 * default pixel filled in tensor, when frame is smaller than<br>
	 * tensor, the outer will be filled with paddings<br>
	 * C type : kestrel_pixel_t*
	 */
	public kestrel_pixel_t.ByReference paddings;
	/**
	 * cache pool will dynamic(trigger: memory type or size) allocate<br>
	 * in this function. `buffer` must be released by<br>
	 * kestrel_buffer_free()<br>
	 * C type : kestrel_buffer*
	 */
	public PointerByReference buffer;
	public kestrel_frame_transform_param_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("tensor", "tensor_color", "operators", "batch", "frames", "rois", "dst_sizes", "mats", "means", "stds", "paddings", "buffer");
	}
	public kestrel_frame_transform_param_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_frame_transform_param_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_frame_transform_param_t implements Structure.ByValue {
		
	};
}
