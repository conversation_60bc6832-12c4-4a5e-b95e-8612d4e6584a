package com.sensetime.intersense.cognitivesvc.server.kestrel;


import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;
import java.nio.ByteBuffer;
/**
 * JNA Wrapper for library <b>kestrel_model</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_modelLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_modelLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_modelLibrary INSTANCE = (Kestrel_modelLibrary)Native.load(Kestrel_modelLibrary.JNA_LIBRARY_NAME, Kestrel_modelLibrary.class);
	/**
	 * @return KESTREL_OK for succeed, otherwise return error code<br>
	 * Original signature : <code>int kestrel_model_load(const char*, kestrel_model*)</code><br>
	 * <i>native declaration : include/kestrel_model.h:29</i>
	 */
	int kestrel_model_load(String model_file, PointerByReference model);
	/**
	 * @return Model handle which equal with input, and ref count has increased<br>
	 * Original signature : <code>kestrel_model kestrel_model_ref(kestrel_model)</code><br>
	 * <i>native declaration : include/kestrel_model.h:35</i>
	 */
	Pointer kestrel_model_ref(Pointer model);
	/**
	 * @return KESTREL_OK for succeed, otherwise return error code<br>
	 * Original signature : <code>int kestrel_model_map_from_memory(uint8_t*, size_t, kestrel_model_map_from_memory_finalizer_callback*, void*, kestrel_model*)</code><br>
	 * <i>native declaration : include/kestrel_model.h:45</i>
	 */
	int kestrel_model_map_from_memory(ByteBuffer data, long data_size, Pointer finalizer, Pointer finalizer_ud, PointerByReference model);
	/**
	 * @return Version number in format major * 10^4 + minor * 10^2 + patch, -1 for error<br>
	 * Original signature : <code>int32_t kestrel_model_version(kestrel_model)</code><br>
	 * <i>native declaration : include/kestrel_model.h:53</i>
	 */
	int kestrel_model_version(Pointer m);
	/**
	 * @return Model type string, reference, no need to free<br>
	 * Original signature : <code>char* kestrel_model_type(kestrel_model)</code><br>
	 * <i>native declaration : include/kestrel_model.h:59</i>
	 */
	Pointer kestrel_model_type(Pointer m);
	/**
	 * hash, and an extra byte for '\0'), NULL for error.<br>
	 * Original signature : <code>char* kestrel_model_oid(kestrel_model)</code><br>
	 * <i>native declaration : include/kestrel_model.h:66</i>
	 */
	Pointer kestrel_model_oid(Pointer m);
	/**
	 * delimiter.<br>
	 * Original signature : <code>size_t kestrel_model_file_size(kestrel_model, const char*)</code><br>
	 * <i>native declaration : include/kestrel_model.h:77</i>
	 */
	long kestrel_model_file_size(Pointer m, String file);
	/**
	 * `public/` are public files only, all other files are private.<br>
	 * Original signature : <code>int kestrel_model_get_file(kestrel_model, const char*, uint8_t*, size_t*)</code><br>
	 * <i>native declaration : include/kestrel_model.h:93</i>
	 */
	int kestrel_model_get_file(Pointer m, String file, Pointer buf, LongByReference buf_len);
	/**
	 * @note Shibboleth checking is ineffective now.<br>
	 * Original signature : <code>int kestrel_model_run_cb_with_file(kestrel_model, const char*, kestrel_shibboleth_fx, kestrel_model_file_cb, void*)</code><br>
	 * <i>native declaration : include/kestrel_model.h:116</i>
	 */
	int kestrel_model_run_cb_with_file(Pointer m, String file, Pointer shibboleth, Pointer cb, Pointer ud);
	/**
	 * only when the reference count is reduced to 0<br>
	 * Original signature : <code>int kestrel_model_unload(kestrel_model*)</code><br>
	 * <i>native declaration : include/kestrel_model.h:126</i>
	 */
	int kestrel_model_unload(PointerByReference m);
	/**
	 * should be called equal number of times to release it<br>
	 * Original signature : <code>char* kestrel_model_register(kestrel_model)</code><br>
	 * <i>native declaration : include/kestrel_model.h:139</i><br>
	 * use the safer methods {@link #kestrel_model_register(kestrel_model.Kestrel_modelLibrary.kestrel_model)} and {@link #kestrel_model_register(com.sun.jna.Pointer)} instead
	 */
	Pointer kestrel_model_register(Pointer m);
	/**
	 * @return KESTREL_OK for successful, otherwise return error code.<br>
	 * Original signature : <code>int kestrel_model_unregister(const char*)</code><br>
	 * <i>native declaration : include/kestrel_model.h:146</i>
	 */
	int kestrel_model_unregister(String oid);
	/**
	 * @return KESTREL_OK for successful, otherwise return error code.<br>
	 * Original signature : <code>int kestrel_model_force_unregister(const char*)</code><br>
	 * <i>native declaration : include/kestrel_model.h:153</i>
	 */
	int kestrel_model_force_unregister(String oid);
	/**
	 * automatically, to unregister all models<br>
	 * Original signature : <code>void kestrel_model_unregister_all()</code><br>
	 * <i>native declaration : include/kestrel_model.h:159</i>
	 */
	void kestrel_model_unregister_all();
}
