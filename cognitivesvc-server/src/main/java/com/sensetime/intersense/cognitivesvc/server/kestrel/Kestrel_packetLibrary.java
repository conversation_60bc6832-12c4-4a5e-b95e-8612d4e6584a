package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_packet_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import java.nio.ByteBuffer;

/**
 * JNA Wrapper for library <b>kestrel_packet</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_packetLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_packetLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_packetLibrary INSTANCE = (Kestrel_packetLibrary)Native.load(Kestrel_packetLibrary.JNA_LIBRARY_NAME, Kestrel_packetLibrary.class);
	/** <i>native declaration : include/kestrel_packet.h</i> */
	public static final int KESTREL_PKT_FLAG_KEY = (int)0x0001;
	/** <i>native declaration : include/kestrel_packet.h</i> */
	public static final int KESTREL_PKT_FLAG_DISPOSABLE = (int)0x0010;
	/**
	 * @return A packet following provided parameters, should be freed using kestrel_packet_free().<br>
	 * Original signature : <code>kestrel_packet kestrel_packet_alloc(int32_t, int32_t, int64_t, int64_t, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_packet.h:53</i>
	 */
	kestrel_packet_t kestrel_packet_alloc(int size, int stream_id, long pts, long dts, int flags);
	/**
	 * kestrel_packet_free().<br>
	 * Original signature : <code>kestrel_packet kestrel_packet_make(uint8_t*, int32_t, int32_t, int64_t, int64_t, int32_t, kestrel_buf_finalizer, void*)</code><br>
	 * <i>native declaration : include/kestrel_packet.h:69</i>
	 */
	kestrel_packet_t kestrel_packet_make(ByteBuffer data, int size, int stream_id, long pts, long dts, int flags, Pointer finalizer, Pointer ud);
	/**
	 * packet data.<br>
	 * Original signature : <code>kestrel_packet kestrel_packet_ref(kestrel_packet)</code><br>
	 * <i>native declaration : include/kestrel_packet.h:80</i>
	 */
	kestrel_packet_t kestrel_packet_ref(kestrel_packet_t in);
	/**
	 * @return Reference count of packet.<br>
	 * Original signature : <code>int32_t kestrel_packet_get_ref_cnt(kestrel_packet)</code><br>
	 * <i>native declaration : include/kestrel_packet.h:86</i>
	 */
	int kestrel_packet_get_ref_cnt(kestrel_packet_t in);
	/**
	 * @param[in,out] packet Packet which is going to be freed.<br>
	 * Original signature : <code>void kestrel_packet_free(kestrel_packet*)</code><br>
	 * <i>native declaration : include/kestrel_packet.h:91</i>
	 */
	void kestrel_packet_free(PointerByReference in);
}
