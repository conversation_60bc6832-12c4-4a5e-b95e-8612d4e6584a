package com.sensetime.intersense.cognitivesvc.server.kestrel;


import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_range_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_tensor_meta_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
/**
 * J<PERSON> Wrapper for library <b>kestrel_tensor</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_tensorLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_tensorLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_tensorLibrary INSTANCE = (Kestrel_tensorLibrary)Native.load(Kestrel_tensorLibrary.JNA_LIBRARY_NAME, Kestrel_tensorLibrary.class);
	/** <i>native declaration : include/kestrel_tensor.h</i> */
	public static final int TENSOR_MAX_NDIMS = (int)(8);
	/**
	 * @return Contiguous tensor shape<br>
	 * Original signature : <code>kestrel_tensor_meta_t kestrel_tensor_make_shape(kestrel_data_type_e, size_t, const size_t*, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:38</i>
	 */
	kestrel_tensor_meta_t.ByValue kestrel_tensor_make_shape(int elem_type, long dims_num, long dims[], int flags);
	/**
	 * @return Tensor structure pointer<br>
	 * Original signature : <code>Pointer kestrel_tensor_make(const char*, const kestrel_tensor_meta_t*, void*, kestrel_mem_type_e, kestrel_buf_finalizer, void*)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:55</i>
	 */
	Pointer kestrel_tensor_make(String name, kestrel_tensor_meta_t shape, Pointer data, int type, Pointer finalizer, Pointer finalizer_ud);
	/**
	 * @return Tensor structure pointer<br>
	 * Original signature : <code>Pointer kestrel_tensor_alloc(const char*, const kestrel_tensor_meta_t*, kestrel_mem_type_e)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:65</i>
	 */
	Pointer kestrel_tensor_alloc(String name, kestrel_tensor_meta_t shape, int type);
	/**
	 * @return Buffer memory type<br>
	 * Original signature : <code>kestrel_mem_type_e kestrel_tensor_mem_type(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:72</i>
	 */
	int kestrel_tensor_mem_type(Pointer tensor);
	/**
	 * @return<br>
	 * Original signature : <code>kestrel_tensor_meta_t* kestrel_tensor_meta(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:78</i>
	 */
	kestrel_tensor_meta_t kestrel_tensor_meta(Pointer tensor);
	/**
	 * @return C string pointed<br>
	 * Original signature : <code>char* kestrel_tensor_name(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:84</i>
	 */
	Pointer kestrel_tensor_name(Pointer tensor);
	/**
	 * Original signature : <code>kestrel_data_type_e kestrel_tensor_element_type(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:87</i>
	 */
	int kestrel_tensor_element_type(Pointer tensor);
	/**
	 * @return if NULL, return 0;<br>
	 * Original signature : <code>size_t kestrel_tensor_ndimension(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:92</i>
	 */
	long kestrel_tensor_ndimension(Pointer tensor);
	/**
	 * @return if NULL, return 1;<br>
	 * Original signature : <code>size_t kestrel_tensor_dimension(kestrel_tensor, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:97</i>
	 */
	long kestrel_tensor_dimension(Pointer tensor, int index);
	/**
	 * @return if NULL, return 1;<br>
	 * Original signature : <code>size_t kestrel_tensor_stride(kestrel_tensor, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:102</i>
	 */
	long kestrel_tensor_stride(Pointer tensor, int index);
	/**
	 * @return if NULL, return 0;<br>
	 * Original signature : <code>size_t kestrel_tensor_nelement(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:107</i>
	 */
	long kestrel_tensor_nelement(Pointer tensor);
	/**
	 * @return if NULL, return 0;<br>
	 * Original signature : <code>size_t kestrel_tensor_nbytes(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:112</i>
	 */
	long kestrel_tensor_nbytes(Pointer tensor);
	/**
	 * @return KESTREL_OK for succeed, otherwise return error code<br>
	 * Original signature : <code>int kestrel_tensor_copy(kestrel_tensor, kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:119</i>
	 */
	int kestrel_tensor_copy(Pointer src, Pointer dst);
	/**
	 * @return Pointer of tensor data, NULL for error<br>
	 * Original signature : <code>Pointer kestrel_tensor_duplicate(kestrel_tensor, kestrel_mem_type_e)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:126</i>
	 */
	Pointer kestrel_tensor_duplicate(Pointer src, int dst_mem_type);
	/**
	 * @return Ref tensor structure pointer<br>
	 * Original signature : <code>Pointer kestrel_tensor_ref(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:132</i>
	 */
	Pointer kestrel_tensor_ref(Pointer t);
	/**
	 * @return Reference count of frame.<br>
	 * Original signature : <code>size_t kestrel_tensor_ref_cnt(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:138</i>
	 */
	long kestrel_tensor_ref_cnt(Pointer t);
	/**
	 * data<br>
	 * Original signature : <code>Pointer kestrel_tensor_roi(kestrel_tensor, int32_t, const kestrel_range_t[])</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:149</i>
	 */
	Pointer kestrel_tensor_roi(Pointer t, int range_num, kestrel_range_t ranges[]);
	/**
	 * @brief Init tensor value.<br>
	 * Original signature : <code>int kestrel_tensor_reset(kestrel_tensor, int8_t)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:154</i>
	 */
	int kestrel_tensor_reset(Pointer t, byte val);
	/**
	 * @return KESTREL_OK for succeed, otherwise return error code<br>
	 * Original signature : <code>int kestrel_tensor_reshape(kestrel_tensor, const kestrel_tensor_meta_t*)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:161</i>
	 */
	int kestrel_tensor_reshape(Pointer t, kestrel_tensor_meta_t shape);
	/**
	 * @return KESTREL_OK for succeed, otherwise return error code<br>
	 * Original signature : <code>int kestrel_tensor_reshape_as(kestrel_tensor, kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:168</i>
	 */
	int kestrel_tensor_reshape_as(Pointer t, Pointer other);
	/**
	 * @note indices the count of indices equal `shape->dims_num`,<br>
	 * Original signature : <code>size_t kestrel_tensor_calc_offset_ex(kestrel_tensor, const size_t*)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:176</i>
	 */
	long kestrel_tensor_calc_offset_ex(Pointer tensor, long indices[]);
	/**
	 * @return Offset of specified position data, in unit of the element<br>
	 * Original signature : <code>size_t kestrel_tensor_calc_offset(kestrel_tensor, size_t, size_t, size_t, size_t)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:186</i>
	 */
	long kestrel_tensor_calc_offset(Pointer tensor, long n, long c, long h, long w);
	/**
	 * an element type pointer, NULL for error<br>
	 * Original signature : <code>void* kestrel_tensor_data_at(kestrel_tensor, size_t, size_t, size_t, size_t)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:197</i>
	 */
	Pointer kestrel_tensor_data_at(Pointer t, long n, long c, long h, long w);
	/**
	 * an element type pointer, NULL for error<br>
	 * Original signature : <code>void* kestrel_tensor_data_at_ex(kestrel_tensor, size_t)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:205</i>
	 */
	Pointer kestrel_tensor_data_at_ex(Pointer t, long offset);
	/**
	 * @note if tensor is NULL, return 0.<br>
	 * Original signature : <code>int32_t kestrel_tensor_is_contiguous(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:212</i>
	 */
	int kestrel_tensor_is_contiguous(Pointer t);
	/**
	 * contiguous tensor by copying data.<br>
	 * Original signature : <code>Pointer kestrel_tensor_contiguous(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:219</i>
	 */
	Pointer kestrel_tensor_contiguous(Pointer t);
	/**
	 * @return Pointer of tensor data, NULL for error;<br>
	 * Original signature : <code>void* kestrel_tensor_raw_pointer(kestrel_tensor)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:225</i>
	 */
	Pointer kestrel_tensor_raw_pointer(Pointer t);
	/**
	 * @param[in,out] tensor Pointer to tensor going to release.<br>
	 * Original signature : <code>void kestrel_tensor_free(kestrel_tensor*)</code><br>
	 * <i>native declaration : include/kestrel_tensor.h:230</i>
	 */
	void kestrel_tensor_free(PointerByReference tensor);
}
