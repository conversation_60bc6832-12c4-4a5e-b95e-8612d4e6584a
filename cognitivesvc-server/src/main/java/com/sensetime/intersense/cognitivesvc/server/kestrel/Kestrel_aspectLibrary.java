package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_aspect</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_aspectLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_aspectLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_aspectLibrary INSTANCE = (Kestrel_aspectLibrary)Native.load(Kestrel_aspectLibrary.JNA_LIBRARY_NAME, Kestrel_aspectLibrary.class);
	/** <i>native declaration : include/kestrel_aspect.h</i> */
	public static final char KESTREL_ASPECT_TYPE_BEGIN = (char)'B';
	/** <i>native declaration : include/kestrel_aspect.h</i> */
	public static final char KESTREL_ASPECT_TYPE_END = (char)'E';
	/** <i>native declaration : include/kestrel_aspect.h</i> */
	public static final char KESTREL_ASPECT_TYPE_INSTANT = (char)'i';
	/** <i>native declaration : include/kestrel_aspect.h</i> */
	public static final char KESTREL_ASPECT_SCOPE_GLOBAL = (char)'g';
	/** <i>native declaration : include/kestrel_aspect.h</i> */
	public static final char KESTREL_ASPECT_SCOPE_PROCESS = (char)'p';
	/** <i>native declaration : include/kestrel_aspect.h</i> */
	public static final char KESTREL_ASPECT_SCOPE_THREAD = (char)'t';
	/** <i>native declaration : include/kestrel_aspect.h</i> */
	public static final char KESTREL_ASPECT_SCOPE_DEFAULT = (char)'t';
	/**
	 * @{<br>
	 * Original signature : <code>void kestrel_aspect_meta(const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_aspect.h:19</i><br>
	 * @deprecated use the safer methods {@link #kestrel_aspect_meta(java.lang.String, java.lang.String)} and {@link #kestrel_aspect_meta(com.sun.jna.Pointer, com.sun.jna.Pointer)} instead
	 */
	@Deprecated 
	void kestrel_aspect_meta(Pointer key, Pointer value);
	/**
	 * @{<br>
	 * Original signature : <code>void kestrel_aspect_meta(const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_aspect.h:19</i>
	 */
	void kestrel_aspect_meta(String key, String value);
	/**
	 * @param[in] pointcut_tag Cut point tag<br>
	 * Original signature : <code>void kestrel_pointcut(char, char, const char*, uint64_t, const char*)</code><br>
	 * <i>native declaration : include/kestrel_aspect.h:28</i><br>
	 * @deprecated use the safer methods {@link #kestrel_pointcut(byte, byte, java.lang.String, long, java.lang.String)} and {@link #kestrel_pointcut(byte, byte, com.sun.jna.Pointer, long, com.sun.jna.Pointer)} instead
	 */
	@Deprecated 
	void kestrel_pointcut(byte type, byte scope, Pointer module_tag, long module_id, Pointer pointcut_tag);
	/**
	 * @param[in] pointcut_tag Cut point tag<br>
	 * Original signature : <code>void kestrel_pointcut(char, char, const char*, uint64_t, const char*)</code><br>
	 * <i>native declaration : include/kestrel_aspect.h:28</i>
	 */
	void kestrel_pointcut(byte type, byte scope, String module_tag, long module_id, String pointcut_tag);
}
