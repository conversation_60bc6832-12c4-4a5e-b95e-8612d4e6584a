package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;

import com.sun.jna.Callback;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_io.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_io_handler_t extends Structure {
	/** C type : init_fx_callback* */
	public kestrel_io_handler_t.init_fx_callback init_fx;
	/** C type : read_fx_callback* */
	public kestrel_io_handler_t.read_fx_callback read_fx;
	/** C type : write_fx_callback* */
	public kestrel_io_handler_t.write_fx_callback write_fx;
	/** C type : seek_fx_callback* */
	public kestrel_io_handler_t.seek_fx_callback seek_fx;
	/** C type : tell_fx_callback* */
	public kestrel_io_handler_t.tell_fx_callback tell_fx;
	/** C type : flush_fx_callback* */
	public kestrel_io_handler_t.flush_fx_callback flush_fx;
	/** <i>native declaration : include/kestrel_io.h</i> */
	public interface init_fx_callback extends Callback {
		Pointer apply(Pointer cfg);
	};
	/** <i>native declaration : include/kestrel_io.h</i> */
	public interface kestrel_bool_callback extends Callback {
		int apply(Pointer ud);
	};
	/** <i>native declaration : include/kestrel_io.h</i> */
	public interface read_fx_callback extends Callback {
		long apply(Pointer buf, long size, Pointer ud);
	};
	/** <i>native declaration : include/kestrel_io.h</i> */
	public interface write_fx_callback extends Callback {
		long apply(Pointer buf, long size, Pointer ud);
	};
	/** <i>native declaration : include/kestrel_io.h</i> */
	public interface seek_fx_callback extends Callback {
		int apply(Pointer ud, long offset, int whence);
	};
	/** <i>native declaration : include/kestrel_io.h</i> */
	public interface tell_fx_callback extends Callback {
		long apply(Pointer ud);
	};
	/** <i>native declaration : include/kestrel_io.h</i> */
	public interface flush_fx_callback extends Callback {
		int apply(Pointer ud);
	};
	public kestrel_io_handler_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("init_fx", "read_fx", "write_fx", "seek_fx", "tell_fx", "flush_fx");
	}
	/**
	 * @param init_fx C type : init_fx_callback*<br>
	 * @param read_fx C type : read_fx_callback*<br>
	 * @param write_fx C type : write_fx_callback*<br>
	 * @param seek_fx C type : seek_fx_callback*<br>
	 * @param tell_fx C type : tell_fx_callback*<br>
	 * @param flush_fx C type : flush_fx_callback*
	 */
	public kestrel_io_handler_t(kestrel_io_handler_t.init_fx_callback init_fx, kestrel_io_handler_t.read_fx_callback read_fx, kestrel_io_handler_t.write_fx_callback write_fx, kestrel_io_handler_t.seek_fx_callback seek_fx, kestrel_io_handler_t.tell_fx_callback tell_fx, kestrel_io_handler_t.flush_fx_callback flush_fx) {
		super();
		this.init_fx = init_fx;
		this.read_fx = read_fx;
		this.write_fx = write_fx;
		this.seek_fx = seek_fx;
		this.tell_fx = tell_fx;
		this.flush_fx = flush_fx;
	}
	public kestrel_io_handler_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_io_handler_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_io_handler_t implements Structure.ByValue {
		
	};
}
