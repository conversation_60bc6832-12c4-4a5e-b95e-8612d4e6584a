package com.sensetime.intersense.cognitivesvc.server.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-07
 */
@Data
@Accessors(chain = true)
@Entity
@Table(name = "${db.cognitive_param.table.name:cognitive_param}")
@NoArgsConstructor
@AllArgsConstructor
public class CognitiveParam{
	
	@Id
	@Column(name = "s_key")
	private String sKey;
	@Column(name = "s_value")
    private String sValue;
	@Column(name = "s_desc")
    private String sDesc;
}
