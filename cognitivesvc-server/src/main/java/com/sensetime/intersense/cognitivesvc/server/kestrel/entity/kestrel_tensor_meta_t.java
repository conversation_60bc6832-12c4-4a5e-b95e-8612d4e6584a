package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;

import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_tensor.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_tensor_meta_t extends Structure {
	/**
	 * @see kestrel_struct.Kestrel_structLibrary#kestrel_data_type_e<br>
	 * C type : kestrel_data_type_e
	 */
	public int elem_type;
	public int flags;
	public long dims_num;
	/** C type : size_t[(8)] */
	public long[] dims = new long[8];
	/** C type : size_t[(8)] */
	public long[] strides = new long[8];
	public kestrel_tensor_meta_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("elem_type", "flags", "dims_num", "dims", "strides");
	}
	/**
	 * @param elem_type @see kestrel_struct.Kestrel_structLibrary#kestrel_data_type_e<br>
	 * C type : kestrel_data_type_e<br>
	 * @param dims C type : size_t[(8)]<br>
	 * @param strides C type : size_t[(8)]
	 */
	public kestrel_tensor_meta_t(int elem_type, int flags, long dims_num, long dims[], long strides[]) {
		super();
		this.elem_type = elem_type;
		this.flags = flags;
		this.dims_num = dims_num;
		if ((dims.length != this.dims.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.dims = dims;
		if ((strides.length != this.strides.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.strides = strides;
	}
	public kestrel_tensor_meta_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_tensor_meta_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_tensor_meta_t implements Structure.ByValue {
		
	};
}
