package com.sensetime.intersense.cognitivesvc.server.entities;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "直接视频流设备", description = "直接视频流设备")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FrameParamReq {


    @Schema(description = "视频rtsp源")
    private String rtspSource;


    @Schema(description = "检测1帧跳N帧")
    private Integer frameSkip;

    @Schema(description = "head range number")
    private int range = 0;

    @Schema(description = "save dir")
    private String saveDir;


    @Schema(description = "videoId")
    private String videoId;

    @Schema(description = "imageLibraryId")
    private String imageLibraryId;


}
