package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Callback;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.ptr.IntByReference;
import java.util.Arrays;
import java.util.List;

/**
 * <i>native declaration : include/kestrel_bson.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_bson_binary_ext_handler_t extends Structure {
	/** C type : raw_decoder_fx_callback* */
	public kestrel_bson_binary_ext_handler_t.raw_decoder_fx_callback raw_decoder_fx;
	/** C type : duplicate_fx_callback* */
	public kestrel_bson_binary_ext_handler_t.duplicate_fx_callback duplicate_fx;
	/** C type : after_decode_fx_callback* */
	public kestrel_bson_binary_ext_handler_t.after_decode_fx_callback after_decode_fx;
	/** C type : readable_fx_callback* */
	public kestrel_bson_binary_ext_handler_t.readable_fx_callback readable_fx;
	/** <i>native declaration : include/kestrel_bson.h</i> */
	public interface raw_decoder_fx_callback extends Callback {
		Pointer apply(Pointer reader, byte subtype, IntByReference len);
	};
	/** <i>native declaration : include/kestrel_bson.h</i> */
	public interface kestrel_bool_callback extends Callback {
		int apply(Pointer writer, byte subtype, Pointer data, int len);
	};
	/** <i>native declaration : include/kestrel_bson.h</i> */
	public interface kestrel_bool_callback2 extends Callback {
		int apply(byte subtype, Pointer data, int len);
	};
	/** <i>native declaration : include/kestrel_bson.h</i> */
	public interface duplicate_fx_callback extends Callback {
		Pointer apply(byte subtype, Pointer data, IntByReference len);
	};
	/** <i>native declaration : include/kestrel_bson.h</i> */
	public interface after_decode_fx_callback extends Callback {
		Pointer apply(byte subtype, Pointer data, IntByReference len);
	};
	/** <i>native declaration : include/kestrel_bson.h</i> */
	public interface readable_fx_callback extends Callback {
		Pointer apply(byte subtype, Pointer data, int len);
	};
	public kestrel_bson_binary_ext_handler_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("raw_decoder_fx", "duplicate_fx", "after_decode_fx", "readable_fx");
	}
	/**
	 * @param raw_decoder_fx C type : raw_decoder_fx_callback*<br>
	 * @param duplicate_fx C type : duplicate_fx_callback*<br>
	 * @param after_decode_fx C type : after_decode_fx_callback*<br>
	 * @param readable_fx C type : readable_fx_callback*
	 */
	public kestrel_bson_binary_ext_handler_t(kestrel_bson_binary_ext_handler_t.raw_decoder_fx_callback raw_decoder_fx, kestrel_bson_binary_ext_handler_t.duplicate_fx_callback duplicate_fx, kestrel_bson_binary_ext_handler_t.after_decode_fx_callback after_decode_fx, kestrel_bson_binary_ext_handler_t.readable_fx_callback readable_fx) {
		super();
		this.raw_decoder_fx = raw_decoder_fx;
		this.duplicate_fx = duplicate_fx;
		this.after_decode_fx = after_decode_fx;
		this.readable_fx = readable_fx;
	}
	public kestrel_bson_binary_ext_handler_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_bson_binary_ext_handler_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_bson_binary_ext_handler_t implements Structure.ByValue {
		
	};
}
