package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_quotas.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_quota_item_t extends Structure {
	/** C type : char[64] */
	public byte[] name = new byte[64];
	public int value;
	public int free_value;
	public kestrel_quota_item_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("name", "value", "free_value");
	}
	/** @param name C type : char[64] */
	public kestrel_quota_item_t(byte name[], int value, int free_value) {
		super();
		if ((name.length != this.name.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.name = name;
		this.value = value;
		this.free_value = free_value;
	}
	public kestrel_quota_item_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_quota_item_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_quota_item_t implements Structure.ByValue {
		
	};
}
