package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_mempool</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */
public interface Kestrel_mempoolLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_mempoolLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_mempoolLibrary INSTANCE = (Kestrel_mempoolLibrary)Native.load(Kestrel_mempoolLibrary.JNA_LIBRARY_NAME, Kestrel_mempoolLibrary.class);
	/** <i>native declaration : include/kestrel_mempool.h</i> */
	public static final int KESTREL_MP_FLAG_DEFAULT = (int)0x0;
	/** <i>native declaration : include/kestrel_mempool.h</i> */
	public static final int KESTREL_MP_FLAG_THREAD_SAFE = (int)0x01;
	/**
	 * @return A memory pool handle, NULL for error.<br>
	 * Original signature : <code>kestrel_mempool kestrel_mempool_alloc(kestrel_mem_type_e, size_t, size_t, uint32_t)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:34</i>
	 */
	Pointer kestrel_mempool_alloc(int type, long pool_capacity, long granularity, int flags);
	/**
	 * @return Buffer memory type<br>
	 * Original signature : <code>kestrel_mem_type_e kestrel_mempool_mem_type(kestrel_mempool)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:41</i>
	 */
	int kestrel_mempool_mem_type(Pointer mempool);
	/**
	 * @return Memory pool capacity in bytes.<br>
	 * Original signature : <code>size_t kestrel_mempool_capacity(kestrel_mempool)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:47</i>
	 */
	long kestrel_mempool_capacity(Pointer mempool);
	/**
	 * @return Memory pool granularity.<br>
	 * Original signature : <code>size_t kestrel_mempool_granularity(kestrel_mempool)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:53</i>
	 */
	long kestrel_mempool_granularity(Pointer mempool);
	/**
	 * @return Memory pool usage in bytes<br>
	 * Original signature : <code>size_t kestrel_mempool_usage(kestrel_mempool)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:59</i>
	 */
	long kestrel_mempool_usage(Pointer mempool);
	/**
	 * @return Memory pool idle in bytes.<br>
	 * Original signature : <code>size_t kestrel_mempool_idle(kestrel_mempool)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:65</i>
	 */
	long kestrel_mempool_idle(Pointer mempool);
	/**
	 * @return Max chunk size.<br>
	 * Original signature : <code>size_t kestrel_mempool_max_chunk(kestrel_mempool)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:71</i>
	 */
	long kestrel_mempool_max_chunk(Pointer mempool);
	/**
	 * kestrel_buffer_free().<br>
	 * Original signature : <code>kestrel_buffer kestrel_mempool_get_buffer(kestrel_mempool, size_t)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:80</i>
	 */
	Pointer kestrel_mempool_get_buffer(Pointer pool, long size);
	/**
	 * @return frame when pool has enough memory, or will return NULL<br>
	 * Original signature : <code>Pointer kestrel_mempool_get_frame(kestrel_mempool, kestrel_video_format_e, int32_t, int32_t, const int32_t*, const int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:95</i>
	 */
	Pointer kestrel_mempool_get_frame(Pointer pool, int fmt, int w, int h, int strides[], int padded_height[]);
	/**
	 * @note After invoking, mempool is set to `NULL`.<br>
	 * Original signature : <code>void kestrel_mempool_free(kestrel_mempool*)</code><br>
	 * <i>native declaration : include/kestrel_mempool.h:103</i>
	 */
	void kestrel_mempool_free(PointerByReference mempool);
}
