package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_queue_roi_result extends Structure {
	public int roi_id;
	/**
	 * < roi区域,暂只支持一个roi一个排队<br>
	 * C type : kestrel_point2d_t[1024]
	 */
	public kestrel_point2d_t[] coords = new kestrel_point2d_t[1024];
	public int coord_num;
	/**
	 * 排队的目标列表，按照顺序从队头至队i尾<br>
	 * C type : crowd_queue_target_list_t
	 */
	public crowd_queue_target_list_t all_targets;
	public crowd_queue_roi_result() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("roi_id", "coords", "coord_num", "all_targets");
	}
	/**
	 * @param coords < roi区域,暂只支持一个roi一个排队<br>
	 * C type : kestrel_point2d_t[1024]<br>
	 * @param all_targets 排队的目标列表，按照顺序从队头至队i尾<br>
	 * C type : crowd_queue_target_list_t
	 */
	public crowd_queue_roi_result(int roi_id, kestrel_point2d_t coords[], int coord_num, crowd_queue_target_list_t all_targets) {
		super();
		this.roi_id = roi_id;
		if ((coords.length != this.coords.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.coords = coords;
		this.coord_num = coord_num;
		this.all_targets = all_targets;
	}
	public crowd_queue_roi_result(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_queue_roi_result implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_queue_roi_result implements Structure.ByValue {
		
	};
}