package com.sensetime.intersense.cognitivesvc.server.vaxtor.jna;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.NativeLong;
import com.sun.jna.Pointer;
import com.sun.jna.WString;
import com.sun.jna.ptr.NativeLongByReference;
import java.nio.ByteBuffer;

/**
 * JNA Wrapper for library <b>vaxtor_utils</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Vaxtor_utilsLibrary extends Library {
    public static final String JNA_LIBRARY_NAME = "vaxtor_utils";
    public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Vaxtor_utilsLibrary.JNA_LIBRARY_NAME);
    public static final Vaxtor_utilsLibrary INSTANCE = (Vaxtor_utilsLibrary)Native.loadLibrary(Vaxtor_utilsLibrary.JNA_LIBRARY_NAME, Vaxtor_utilsLibrary.class);
    /**
     * Original signature : <code>long utilCompressImage(long, const byte*, long, long, long, long, long, byte*, long*, long*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:53</i><br>
     * @deprecated use the safer methods {@link #utilCompressImage(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, java.nio.ByteBuffer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference)} and {@link #utilCompressImage(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference)} instead
     */
    @Deprecated
    NativeLong utilCompressImage(NativeLong src_format, Pointer input_image, NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong quality, Pointer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height);
    /**
     * Original signature : <code>long utilCompressImage(long, const byte*, long, long, long, long, long, byte*, long*, long*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:53</i>
     */
    NativeLong utilCompressImage(NativeLong src_format, byte input_image[], NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong quality, ByteBuffer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height);
    /**
     * Original signature : <code>long utilCompressImageEx(long, const byte*, long, long, long, long, long, byte*, long*, long*, const byte*, size_t)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:93</i><br>
     * @deprecated use the safer methods {@link #utilCompressImageEx(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, java.nio.ByteBuffer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference, byte[], com.ochafik.lang.jnaerator.runtime.NativeSize)} and {@link #utilCompressImageEx(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.Pointer, com.ochafik.lang.jnaerator.runtime.NativeSize)} instead
     */
    @Deprecated
    NativeLong utilCompressImageEx(NativeLong src_format, Pointer input_image, NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong quality, Pointer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height, Pointer comment, NativeLong comment_size);
    /**
     * Original signature : <code>long utilCompressImageEx(long, const byte*, long, long, long, long, long, byte*, long*, long*, const byte*, size_t)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:93</i>
     */
    NativeLong utilCompressImageEx(NativeLong src_format, byte input_image[], NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong quality, ByteBuffer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height, byte comment[], NativeLong comment_size);
    /**
     * Original signature : <code>long utilBlurPlate(long, const byte*, long, long, long, long, long, const long*, long, byte*, long*, long*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:134</i><br>
     * @deprecated use the safer methods {@link #utilBlurPlate(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong[], com.sun.jna.NativeLong, java.nio.ByteBuffer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference)} and {@link #utilBlurPlate(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference)} instead
     */
//    @Deprecated
//    NativeLong utilBlurPlate(NativeLong src_format, Pointer input_image, NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong output_quality, NativeLongByReference region_rect, NativeLong blur_level, Pointer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height);
    /**
     * Original signature : <code>long utilBlurPlate(long, const byte*, long, long, long, long, long, const long*, long, byte*, long*, long*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:134</i>
     */
    NativeLong utilBlurPlate(NativeLong src_format, byte input_image[], NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong output_quality, NativeLong region_rect[], NativeLong blur_level, ByteBuffer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height);
    /**
     * Original signature : <code>long utilResizeImage(long, const byte*, long, long, long, long*, byte*, long, long, long*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:174</i><br>
     * @deprecated use the safer methods {@link #utilResizeImage(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.ptr.NativeLongByReference, java.nio.ByteBuffer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.ptr.NativeLongByReference)} and {@link #utilResizeImage(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.ptr.NativeLongByReference)} instead
     */
    @Deprecated
    NativeLong utilResizeImage(NativeLong src_format, Pointer src_image, NativeLong src_length, NativeLong src_width, NativeLong src_height, NativeLongByReference dst_format, Pointer dst_image, NativeLong dst_width, NativeLong dst_height, NativeLongByReference dst_length);
    /**
     * Original signature : <code>long utilResizeImage(long, const byte*, long, long, long, long*, byte*, long, long, long*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:174</i>
     */
    NativeLong utilResizeImage(NativeLong src_format, byte src_image[], NativeLong src_length, NativeLong src_width, NativeLong src_height, NativeLongByReference dst_format, ByteBuffer dst_image, NativeLong dst_width, NativeLong dst_height, NativeLongByReference dst_length);
    /**
     * Original signature : <code>long utilCompressImageWithWatermark(long, const byte*, long, long, long, long, long, const char*, long, long, long, long, byte*, long*, long*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:216</i><br>
     * @deprecated use the safer methods {@link #utilCompressImageWithWatermark(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, java.lang.String, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, java.nio.ByteBuffer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference)} and {@link #utilCompressImageWithWatermark(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference)} instead
     */
    @Deprecated
    NativeLong utilCompressImageWithWatermark(NativeLong src_format, Pointer input_image, NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong quality, Pointer str_text_overlay, NativeLong rgb_text_color, NativeLong rgb_background_color, NativeLong text_position, NativeLong font_size, Pointer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height);
    /**
     * Original signature : <code>long utilCompressImageWithWatermark(long, const byte*, long, long, long, long, long, const char*, long, long, long, long, byte*, long*, long*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:216</i>
     */
    NativeLong utilCompressImageWithWatermark(NativeLong src_format, byte input_image[], NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong quality, String str_text_overlay, NativeLong rgb_text_color, NativeLong rgb_background_color, NativeLong text_position, NativeLong font_size, ByteBuffer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height);
    /**
     * Original signature : <code>long utilCompressImageWithWatermarkEx(long, const byte*, long, long, long, long, long, const char*, long, long, long, long, byte*, long*, long*, const byte*, size_t)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:266</i><br>
     * @deprecated use the safer methods {@link #utilCompressImageWithWatermarkEx(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, java.lang.String, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, java.nio.ByteBuffer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference, byte[], com.ochafik.lang.jnaerator.runtime.NativeSize)} and {@link #utilCompressImageWithWatermarkEx(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.Pointer, com.ochafik.lang.jnaerator.runtime.NativeSize)} instead
     */
    @Deprecated
    NativeLong utilCompressImageWithWatermarkEx(NativeLong src_format, Pointer input_image, NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong quality, Pointer str_text_overlay, NativeLong rgb_text_color, NativeLong rgb_background_color, NativeLong text_position, NativeLong font_size, Pointer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height, Pointer comment, NativeLong comment_size);
    /**
     * Original signature : <code>long utilCompressImageWithWatermarkEx(long, const byte*, long, long, long, long, long, const char*, long, long, long, long, byte*, long*, long*, const byte*, size_t)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:266</i>
     */
    NativeLong utilCompressImageWithWatermarkEx(NativeLong src_format, byte input_image[], NativeLong image_length, NativeLong src_width, NativeLong src_height, NativeLong code_rotation, NativeLong quality, String str_text_overlay, NativeLong rgb_text_color, NativeLong rgb_background_color, NativeLong text_position, NativeLong font_size, ByteBuffer dest_image, NativeLongByReference dest_width, NativeLongByReference dest_height, byte comment[], NativeLong comment_size);
    /**
     * Original signature : <code>long utilGetImageRegionRGB(const tImageInfo*, const long*, tImageInfo*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:305</i><br>
     * @deprecated use the safer methods {@link #utilGetImageRegionRGB(vaxtor_types.tImageInfo, com.sun.jna.NativeLong[], vaxtor_types.tImageInfo)} and {@link #utilGetImageRegionRGB(vaxtor_types.tImageInfo, com.sun.jna.ptr.NativeLongByReference, vaxtor_types.tImageInfo)} instead
     */
    @Deprecated
    NativeLong utilGetImageRegionRGB(tImageInfo input_image, NativeLongByReference region_rect, tImageInfo region_image);
    /**
     * Original signature : <code>long utilGetImageRegionRGB(const tImageInfo*, const long*, tImageInfo*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:305</i>
     */
    NativeLong utilGetImageRegionRGB(tImageInfo input_image, NativeLong region_rect[], tImageInfo region_image);
    /**
     * Original signature : <code>long utilLevenshteinDistance(const wchar_t*, const wchar_t*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:325</i><br>
     * @deprecated use the safer methods {@link #utilLevenshteinDistance(com.sun.jna.WString, com.sun.jna.WString)} and {@link #utilLevenshteinDistance(com.ochafik.lang.jnaerator.runtime.CharByReference, com.ochafik.lang.jnaerator.runtime.CharByReference)} instead
     */
    @Deprecated
    NativeLong utilLevenshteinDistance(Pointer str1, Pointer str2);
    /**
     * Original signature : <code>long utilLevenshteinDistance(const wchar_t*, const wchar_t*)</code><br>
     * <i>native declaration : header/vaxtor_utils.h:325</i>
     */
    NativeLong utilLevenshteinDistance(WString str1, WString str2);
}
