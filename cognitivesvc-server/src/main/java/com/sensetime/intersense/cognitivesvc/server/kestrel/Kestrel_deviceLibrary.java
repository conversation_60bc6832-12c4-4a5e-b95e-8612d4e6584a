package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import java.nio.IntBuffer;


/**
 * JNA Wrapper for library <b>kestrel_device</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_deviceLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_deviceLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_deviceLibrary INSTANCE = (Kestrel_deviceLibrary)Native.load(Kestrel_deviceLibrary.JNA_LIBRARY_NAME, Kestrel_deviceLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_device.h</i><br>
	 * enum values
	 */
	public static interface kestrel_mem_type_e {
		/** <i>native declaration : include/kestrel_device.h:26</i> */
		public static final int KESTREL_MEM_UNKNOWN = -1;
		/** <i>native declaration : include/kestrel_device.h:28</i> */
		public static final int KESTREL_MEM_HOST = 0;
		/** <i>native declaration : include/kestrel_device.h:30</i> */
		public static final int KESTREL_MEM_DEVICE = 1;
	};
	/**
	 * <i>native declaration : include/kestrel_device.h</i><br>
	 * enum values
	 */
	public static interface kestrel_device_attr_e {
		/** <i>native declaration : include/kestrel_device.h:35</i> */
		public static final int KESTREL_DEVICE_MEMORY_MAP_SUPPORTED = 0;
	};
	/**
	 * @return KESTREL_OK for succeed, other for error<br>
	 * Original signature : <code>int kestrel_device_bind(const char*, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_device.h:42</i>
	 */
	int kestrel_device_bind(String device, Pointer config);
	/**
	 * Original signature : <code>void kestrel_device_unbind()</code><br>
	 * <i>native declaration : include/kestrel_device.h:48</i>
	 */
	void kestrel_device_unbind();
	/**
	 * @note same as `kestrel_device_bind`<br>
	 * Original signature : <code>int kestrel_device_attach(kestrel_plugin_instance)</code><br>
	 * <i>native declaration : include/kestrel_device.h:53</i>
	 */
	int kestrel_device_attach(Pointer dev);
	/**
	 * @return User defined device handle, NULL for error<br>
	 * Original signature : <code>kestrel_plugin_instance kestrel_device_get_handle()</code><br>
	 * <i>native declaration : include/kestrel_device.h:58</i>
	 */
	Pointer kestrel_device_get_handle();
	/**
	 * @return Host device handle, NULL for error<br>
	 * Original signature : <code>kestrel_plugin_instance kestrel_device_get_host()</code><br>
	 * <i>native declaration : include/kestrel_device.h:63</i>
	 */
	Pointer kestrel_device_get_host();
	/**
	 * @return Device index if successful, or -1 for fail<br>
	 * Original signature : <code>kestrel_device_index kestrel_device_get_id(kestrel_plugin_instance)</code><br>
	 * <i>native declaration : include/kestrel_device.h:68</i>
	 */
	int kestrel_device_get_id(Pointer dev);
	/**
	 * @note Returned string do not need to free<br>
	 * Original signature : <code>char* kestrel_device_get_name(kestrel_plugin_instance)</code><br>
	 * <i>native declaration : include/kestrel_device.h:74</i>
	 */
	String kestrel_device_get_name(Pointer dev);
	/**
	 * @return Device type if successful, or KESTREL_MEM_UNKNOWN for fail<br>
	 * Original signature : <code>kestrel_mem_type_e kestrel_device_get_type(kestrel_plugin_instance)</code><br>
	 * <i>native declaration : include/kestrel_device.h:79</i>
	 */
	int kestrel_device_get_type(Pointer dev);
	/**
	 * supported attributes are: @see kestrel_device_attr_e<br>
	 * Original signature : <code>int kestrel_device_get_attribute(kestrel_plugin_instance, kestrel_device_attr_e, int*)</code><br>
	 * <i>native declaration : include/kestrel_device.h:88</i>
	 */
	int kestrel_device_get_attribute(Pointer dev, int attr, IntBuffer value);
}
