package com.sensetime.intersense.cognitivesvc.server.vaxtor.jna;

import com.sun.jna.Pointer;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.NativeLong;
import com.sun.jna.WString;
import com.sun.jna.ptr.FloatByReference;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.NativeLongByReference;
import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
/**
 * JNA Wrapper for library <b>vaxtor_ocr</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Vaxtor_ocrLibrary extends Library {
    public static final String JNA_LIBRARY_NAME = "vaxtor_ocr";
    public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Vaxtor_ocrLibrary.JNA_LIBRARY_NAME);
    public static final Vaxtor_ocrLibrary INSTANCE = (Vaxtor_ocrLibrary)Native.loadLibrary(Vaxtor_ocrLibrary.JNA_LIBRARY_NAME, Vaxtor_ocrLibrary.class);
    /**
     * Original signature : <code>__attribute__((dllexport)) void ocrGetErrorDescription(long, char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:64</i><br>
     * @deprecated use the safer methods {@link #ocrGetErrorDescription(com.sun.jna.NativeLong, java.nio.ByteBuffer)} and {@link #ocrGetErrorDescription(com.sun.jna.NativeLong, com.sun.jna.Pointer)} instead
     */
    @Deprecated
    void ocrGetErrorDescription(NativeLong code, Pointer err_description);
    /**
     * Original signature : <code>__attribute__((dllexport)) void ocrGetErrorDescription(long, char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:64</i>
     */
    void ocrGetErrorDescription(NativeLong code, ByteBuffer err_description);
    /**
     * Original signature : <code>__attribute__((dllexport)) void ocrSetNewPlateResultsCallback(Vaxtor_typesLibrary.event_plate_info, void*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:82</i>
     */
    void ocrSetNewPlateResultsCallback(Vaxtor_typesLibrary.event_plate_info on_new_plate_info, Pointer user_info);
    /**
     * Original signature : <code>__attribute__((dllexport)) void ocrSetFullPathDataFiles(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:99</i><br>
     * @deprecated use the safer methods {@link #ocrSetFullPathDataFiles(java.lang.String)} and {@link #ocrSetFullPathDataFiles(com.sun.jna.Pointer)} instead
     */
    @Deprecated
    void ocrSetFullPathDataFiles(Pointer ocr_data_file);
    /**
     * Original signature : <code>__attribute__((dllexport)) void ocrSetFullPathDataFiles(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:99</i>
     */
    void ocrSetFullPathDataFiles(String ocr_data_file);
    /**
     * Original signature : <code>long ocrGetCodesCountriesAvailable(long*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:116</i>
     */
    NativeLong ocrGetCodesCountriesAvailable(NativeLongByReference array_country_codes);
    /**
     * Original signature : <code>long ocrInitialize(long, long*, long, long*, long, long, long, long, long, long, long, long, long, long, long, long, long, long, long, long, long)</code><br>
     * @param list_usa_states_codes DEPRECATED => set NULL<br>
     * @param list_num_usa_states DEPRECATED => set 0<br>
     * @param same_plate_delay DEPRECATED => set 0<br>
     * @param same_plate_max_chars_distance DEPRECATED => set 0<br>
     * @param min_char_height DEPRECATED => set 0<br>
     * @param max_char_height DEPRECATED => set 0<br>
     * @param min_global_confidence DEPRECATED => set 0<br>
     * @param min_character_confidence DEPRECATED => set 0<br>
     * @param grammar_strict DEPRECATED => set 0<br>
     * @param min_num_plate_characters DEPRECATED => set 0<br>
     * @param max_num_plate_characters DEPRECATED => set 0<br>
     * @param max_slop_angle DEPRECATED => set 0<br>
     * @param background_mode DEPRECATED => set 0<br>
     * @param ocr_complexity DEPRECATED => set 0<br>
     * @param find_plate_depth DEPRECATED => set 0<br>
     * @param detect_multiline_plate DEPRECATED => set 0<br>
     * @param enable_plates_finder_extra DEPRECATED => set 0<br>
     * <i>native declaration : header/vaxtor_ocr.h:138</i>
     */
    NativeLong ocrInitialize(NativeLong oper_mode, NativeLongByReference list_countries_codes, NativeLong list_num_countries, NativeLongByReference list_usa_states_codes, NativeLong list_num_usa_states, NativeLong same_plate_delay, NativeLong same_plate_max_chars_distance, NativeLong min_char_height, NativeLong max_char_height, NativeLong min_global_confidence, NativeLong min_character_confidence, NativeLong grammar_strict, NativeLong min_num_plate_characters, NativeLong max_num_plate_characters, NativeLong max_slop_angle, NativeLong background_mode, NativeLong ocr_complexity, NativeLong find_plate_depth, NativeLong detect_multiline_plate, NativeLong enable_plates_finder_extra, NativeLong reserved);
    /**
     * Original signature : <code>long ocrInitializeEx(long, long*, long, long, long, long, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:180</i>
     */
    NativeLong ocrInitializeEx(NativeLong oper_mode, NativeLongByReference list_countries_codes, NativeLong list_num_countries, NativeLong same_plate_delay, NativeLong min_char_height, NativeLong max_char_height, NativeLong ocr_complexity, NativeLong reserved);
    /**
     * Original signature : <code>long ocrConfigAdvancedOptions(type_id_ocr, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:207</i>
     */
    NativeLong ocrConfigAdvancedOptions(NativeLong id, NativeLong param_code, NativeLong param_value);
    /**
     * Original signature : <code>long ocrGetDefaultAdvancedOptionsParam(long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:226</i>
     */
    NativeLong ocrGetDefaultAdvancedOptionsParam(NativeLong param_code);
    /**
     * Original signature : <code>long ocrConfigureMultiPlateReader(type_id_ocr, long, long, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:249</i>
     */
    NativeLong ocrConfigureMultiPlateReader(NativeLong id, NativeLong min_num_occurrences, NativeLong max_num_occurrences, NativeLong mp_recognition_timeout, NativeLong mp_index_return_plate_sample);
    /**
     * Original signature : <code>long ocrConfigureDwellingTime(type_id_ocr, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:275</i>
     */
    NativeLong ocrConfigureDwellingTime(NativeLong id, NativeLong dwelling_timeout);
    /**
     * Original signature : <code>long ocrAddPlateException(type_id_ocr, const wchar_t*, const wchar_t*, bool)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:300</i><br>
     * @deprecated use the safer methods {@link #ocrAddPlateException(com.sun.jna.NativeLong, com.sun.jna.WString, com.sun.jna.WString, byte)} and {@link #ocrAddPlateException(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.Pointer, byte)} instead
     */
    @Deprecated
    NativeLong ocrAddPlateException(NativeLong id, Pointer str_plate_source, Pointer str_plate_replacement, byte b_enabled);
    /**
     * Original signature : <code>long ocrAddPlateException(type_id_ocr, const wchar_t*, const wchar_t*, bool)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:300</i>
     */
    NativeLong ocrAddPlateException(NativeLong id, WString str_plate_source, WString str_plate_replacement, byte b_enabled);
    /**
     * Original signature : <code>long ocrShutdown(type_id_ocr)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:318</i>
     */
    NativeLong ocrShutdown(NativeLong id);
    /**
     * Original signature : <code>long ocrFindPlatesGray8(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:337</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesGray8(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesGray8(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesGray8(NativeLong id, Pointer src_image, NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesGray8(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:337</i>
     */
    NativeLong ocrFindPlatesGray8(NativeLong id, byte src_image[], NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesRGB(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:356</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesRGB(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesRGB(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesRGB(NativeLong id, Pointer src_image, NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesRGB(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:356</i>
     */
    NativeLong ocrFindPlatesRGB(NativeLong id, byte src_image[], NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesYUY2(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:375</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesYUY2(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesYUY2(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesYUY2(NativeLong id, Pointer src_image, NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesYUY2(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:375</i>
     */
    NativeLong ocrFindPlatesYUY2(NativeLong id, byte src_image[], NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesUYVY(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:394</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesUYVY(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesUYVY(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesUYVY(NativeLong id, Pointer src_image, NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesUYVY(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:394</i>
     */
    NativeLong ocrFindPlatesUYVY(NativeLong id, byte src_image[], NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesI420(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:413</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesI420(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesI420(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesI420(NativeLong id, Pointer src_image, NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesI420(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:413</i>
     */
    NativeLong ocrFindPlatesI420(NativeLong id, byte src_image[], NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesNV12(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:432</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesNV12(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesNV12(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesNV12(NativeLong id, Pointer src_image, NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesNV12(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:432</i>
     */
    NativeLong ocrFindPlatesNV12(NativeLong id, byte src_image[], NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesNV21(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:451</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesNV21(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesNV21(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesNV21(NativeLong id, Pointer src_image, NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesNV21(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:451</i>
     */
    NativeLong ocrFindPlatesNV21(NativeLong id, byte src_image[], NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesBAYER_RGGB(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:470</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesBAYER_RGGB(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesBAYER_RGGB(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesBAYER_RGGB(NativeLong id, Pointer src_image, NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesBAYER_RGGB(type_id_ocr, const byte*, long, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:470</i>
     */
    NativeLong ocrFindPlatesBAYER_RGGB(NativeLong id, byte src_image[], NativeLong image_width, NativeLong image_height, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesJPEG(type_id_ocr, const byte*, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:488</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesJPEG(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesJPEG(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesJPEG(NativeLong id, Pointer src_image, NativeLong image_length, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesJPEG(type_id_ocr, const byte*, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:488</i>
     */
    NativeLong ocrFindPlatesJPEG(NativeLong id, byte src_image[], NativeLong image_length, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesBMP24(type_id_ocr, const byte*, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:506</i><br>
     * @deprecated use the safer methods {@link #ocrFindPlatesBMP24(com.sun.jna.NativeLong, byte[], com.sun.jna.NativeLong, long, long)} and {@link #ocrFindPlatesBMP24(com.sun.jna.NativeLong, com.sun.jna.Pointer, com.sun.jna.NativeLong, long, long)} instead
     */
    @Deprecated
    NativeLong ocrFindPlatesBMP24(NativeLong id, Pointer src_image, NativeLong image_length, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrFindPlatesBMP24(type_id_ocr, const byte*, long, vx_int64, vx_int64)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:506</i>
     */
    NativeLong ocrFindPlatesBMP24(NativeLong id, byte src_image[], NativeLong image_length, long time_stamp, long time_counter);
    /**
     * Original signature : <code>long ocrForceAlprResult(type_id_ocr, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:521</i>
     */
    NativeLong ocrForceAlprResult(NativeLong id, NativeLong clean_duplicated);
    /**
     * Original signature : <code>long ocrInitialized(type_id_ocr)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:534</i>
     */
    NativeLong ocrInitialized(NativeLong id);
    /**
     * Original signature : <code>long ocrAddPolygonROI(type_id_ocr, long, const long*, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:557</i><br>
     * @deprecated use the safer methods {@link #ocrAddPolygonROI(com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.NativeLong[], com.sun.jna.NativeLong, com.sun.jna.NativeLong)} and {@link #ocrAddPolygonROI(com.sun.jna.NativeLong, com.sun.jna.NativeLong, com.sun.jna.ptr.NativeLongByReference, com.sun.jna.NativeLong, com.sun.jna.NativeLong)} instead
     */
    @Deprecated
    NativeLong ocrAddPolygonROI(NativeLong id, NativeLong roi_id, NativeLongByReference xy_points, NativeLong num_points, NativeLong roi_type);
    /**
     * Original signature : <code>long ocrAddPolygonROI(type_id_ocr, long, const long*, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:557</i>
     */
    NativeLong ocrAddPolygonROI(NativeLong id, NativeLong roi_id, NativeLong xy_points[], NativeLong num_points, NativeLong roi_type);
    /**
     * Original signature : <code>long ocrRemovePolygonROI(type_id_ocr, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:574</i>
     */
    NativeLong ocrRemovePolygonROI(NativeLong id, NativeLong roi_id);
    /**
     * Original signature : <code>long ocrFilterPlateResultByVehicleDirectionExtended(type_id_ocr, long, long, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:594</i>
     */
    NativeLong ocrFilterPlateResultByVehicleDirectionExtended(NativeLong id, NativeLong moving_away, NativeLong moving_approaching, NativeLong moving_unknown, NativeLong stopped);
    /**
     * Original signature : <code>long ocrConfigAnalyticISpeed(type_id_ocr, double, double, double, double, double, double, double, double, double, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:626</i>
     */
    NativeLong ocrConfigAnalyticISpeed(NativeLong id, double sensor_width_mm, double sensor_height_mm, double min_focal_length_mm, double max_focal_length_mm, double zoom_level, double camera_height, double camera_lane_distance, double camera_tilt_angle, double speed_factor, NativeLong minimum_tracking_time, NativeLong speed_result_units);
    /**
     * Original signature : <code>long ocrGetISpeedCalibrationParams(type_id_ocr, float*, float*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:656</i><br>
     * @deprecated use the safer methods {@link #ocrGetISpeedCalibrationParams(com.sun.jna.NativeLong, java.nio.FloatBuffer, java.nio.FloatBuffer)} and {@link #ocrGetISpeedCalibrationParams(com.sun.jna.NativeLong, com.sun.jna.ptr.FloatByReference, com.sun.jna.ptr.FloatByReference)} instead
     */
    @Deprecated
    NativeLong ocrGetISpeedCalibrationParams(NativeLong id, FloatByReference param1, FloatByReference param2);
    /**
     * Original signature : <code>long ocrGetISpeedCalibrationParams(type_id_ocr, float*, float*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:656</i>
     */
    NativeLong ocrGetISpeedCalibrationParams(NativeLong id, FloatBuffer param1, FloatBuffer param2);
    /**
     * Original signature : <code>long ocrSetISpeedCalibrationParams(type_id_ocr, float, float)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:679</i>
     */
    NativeLong ocrSetISpeedCalibrationParams(NativeLong id, float param1, float param2);
    /**
     * Original signature : <code>long ocrConfigAnalyticsMMCEx(type_id_ocr, long, const tInitMMC*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:701</i>
     */
    NativeLong ocrConfigAnalyticsMMCEx(NativeLong id, NativeLong internal_analytic, tInitMMC init_info);
    /**
     * Original signature : <code>long ocrAnalyzeMMCAlone(type_id_ocr, const tImageInfo*, const int*, long, tAnalytics*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:726</i><br>
     * @deprecated use the safer methods {@link #ocrAnalyzeMMCAlone(com.sun.jna.NativeLong, vaxtor_types.tImageInfo, int[], com.sun.jna.NativeLong, vaxtor_types.tAnalytics)} and {@link #ocrAnalyzeMMCAlone(com.sun.jna.NativeLong, vaxtor_types.tImageInfo, com.sun.jna.ptr.IntByReference, com.sun.jna.NativeLong, vaxtor_types.tAnalytics)} instead
     */
    @Deprecated
    NativeLong ocrAnalyzeMMCAlone(NativeLong id, tImageInfo ptr_image, IntByReference ptr_plate_rectangle, NativeLong average_characters_height, tAnalytics ptr_analytics_result);
    /**
     * Original signature : <code>long ocrAnalyzeMMCAlone(type_id_ocr, const tImageInfo*, const int*, long, tAnalytics*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:726</i>
     */
    NativeLong ocrAnalyzeMMCAlone(NativeLong id, tImageInfo ptr_image, int ptr_plate_rectangle[], NativeLong average_characters_height, tAnalytics ptr_analytics_result);
    /**
     * Original signature : <code>long ocrApplyImageRotation(type_id_ocr, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:748</i>
     */
    NativeLong ocrApplyImageRotation(NativeLong id, NativeLong rotation_code);
    /**
     * Original signature : <code>long ocrApplyImageResizing(type_id_ocr, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:770</i>
     */
    NativeLong ocrApplyImageResizing(NativeLong id, NativeLong new_width, NativeLong new_height);
    /**
     * Original signature : <code>long ocrGetNumPlatesRead(type_id_ocr)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:787</i>
     */
    NativeLong ocrGetNumPlatesRead(NativeLong id);
    /**
     * Original signature : <code>__attribute__((dllexport)) double ocrGetReadTime(type_id_ocr)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:801</i>
     */
    double ocrGetReadTime(NativeLong id);
    /**
     * Original signature : <code>long ocrGetPlateInfo(type_id_ocr, long, tPlateInfo*, tAnalytics*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:819</i>
     */
    NativeLong ocrGetPlateInfo(NativeLong id, NativeLong index_plate, tPlateInfo ptr_plate_info, tAnalytics ptr_analytics);
    /**
     * Original signature : <code>long ocrGetCountryStateCode(const char*, const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:838</i><br>
     * @deprecated use the safer methods {@link #ocrGetCountryStateCode(java.lang.String, java.lang.String)} and {@link #ocrGetCountryStateCode(com.sun.jna.Pointer, com.sun.jna.Pointer)} instead
     */
    @Deprecated
    NativeLong ocrGetCountryStateCode(Pointer str_country, Pointer str_state);
    /**
     * Original signature : <code>long ocrGetCountryStateCode(const char*, const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:838</i>
     */
    NativeLong ocrGetCountryStateCode(String str_country, String str_state);
    /**
     * Original signature : <code>long ocrGetCountryStateAcronym(const char*, const char*, char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:858</i><br>
     * @deprecated use the safer methods {@link #ocrGetCountryStateAcronym(java.lang.String, java.lang.String, java.nio.ByteBuffer)} and {@link #ocrGetCountryStateAcronym(com.sun.jna.Pointer, com.sun.jna.Pointer, com.sun.jna.Pointer)} instead
     */
    @Deprecated
    NativeLong ocrGetCountryStateAcronym(Pointer str_country, Pointer str_state, Pointer str_acronym);
    /**
     * Original signature : <code>long ocrGetCountryStateAcronym(const char*, const char*, char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:858</i>
     */
    NativeLong ocrGetCountryStateAcronym(String str_country, String str_state, ByteBuffer str_acronym);
    /**
     * Original signature : <code>long ocrGetCountryName(int, char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:875</i><br>
     * @deprecated use the safer methods {@link #ocrGetCountryName(int, java.nio.ByteBuffer)} and {@link #ocrGetCountryName(int, com.sun.jna.Pointer)} instead
     */
    @Deprecated
    NativeLong ocrGetCountryName(int cnt_code, Pointer str_name);
    /**
     * Original signature : <code>long ocrGetCountryName(int, char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:875</i>
     */
    NativeLong ocrGetCountryName(int cnt_code, ByteBuffer str_name);
    /**
     * Original signature : <code>long ocrDebugFileName(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:883</i><br>
     * @deprecated use the safer methods {@link #ocrDebugFileName(java.lang.String)} and {@link #ocrDebugFileName(com.sun.jna.Pointer)} instead
     */
    @Deprecated
    NativeLong ocrDebugFileName(Pointer str_file_name);
    /**
     * Original signature : <code>long ocrDebugFileName(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:883</i>
     */
    NativeLong ocrDebugFileName(String str_file_name);
    /**
     * Original signature : <code>long ocrGetVersion(char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:900</i><br>
     * @deprecated use the safer methods {@link #ocrGetVersion(java.nio.ByteBuffer)} and {@link #ocrGetVersion(com.sun.jna.Pointer)} instead
     */
    @Deprecated
    NativeLong ocrGetVersion(Pointer version);
    /**
     * Original signature : <code>long ocrGetVersion(char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:900</i>
     */
    NativeLong ocrGetVersion(ByteBuffer version);
    /**
     * Original signature : <code>long ocrGetMaxNumInstances()</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:913</i>
     */
    NativeLong ocrGetMaxNumInstances();
    /**
     * Original signature : <code>long ocrGetFeatureLicenseStatus(long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:927</i>
     */
    NativeLong ocrGetFeatureLicenseStatus(NativeLong code_feature);
    /**
     * Original signature : <code>long ocrGetFeatureDurationStatus(long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:942</i>
     */
    NativeLong ocrGetFeatureDurationStatus(NativeLong code_feature);
    /**
     * Original signature : <code>long ocrGetC2VSize()</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:956</i>
     */
    NativeLong ocrGetC2VSize();
    /**
     * Original signature : <code>long ocrGetC2V(char*, size_t)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:973</i><br>
     * @deprecated use the safer methods {@link #ocrGetC2V(java.nio.ByteBuffer, com.ochafik.lang.jnaerator.runtime.NativeSize)} and {@link #ocrGetC2V(com.sun.jna.Pointer, com.ochafik.lang.jnaerator.runtime.NativeSize)} instead
     */
    @Deprecated
    NativeLong ocrGetC2V(Pointer c2v, NativeLong c2v_len);
    /**
     * Original signature : <code>long ocrGetC2V(char*, size_t)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:973</i>
     */
    NativeLong ocrGetC2V(ByteBuffer c2v, NativeLong c2v_len);
    /**
     * Original signature : <code>long ocrSetV2C(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:987</i><br>
     * @deprecated use the safer methods {@link #ocrSetV2C(java.lang.String)} and {@link #ocrSetV2C(com.sun.jna.Pointer)} instead
     */
    @Deprecated
    NativeLong ocrSetV2C(Pointer v2c);
    /**
     * Original signature : <code>long ocrSetV2C(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:987</i>
     */
    NativeLong ocrSetV2C(String v2c);
    /**
     * Original signature : <code>long ocrAddClientIdentity(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1003</i><br>
     * @deprecated use the safer methods {@link #ocrAddClientIdentity(java.lang.String)} and {@link #ocrAddClientIdentity(com.sun.jna.Pointer)} instead
     */
    @Deprecated
    NativeLong ocrAddClientIdentity(Pointer identity);
    /**
     * Original signature : <code>long ocrAddClientIdentity(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1003</i>
     */
    NativeLong ocrAddClientIdentity(String identity);
    /**
     * Original signature : <code>long ocrClearClientIdentities()</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1016</i>
     */
    NativeLong ocrClearClientIdentities();
    /**
     * Original signature : <code>long ocrInitializeLogSystem(const char*, bool, bool, int)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1018</i><br>
     * @deprecated use the safer methods {@link #ocrInitializeLogSystem(java.lang.String, byte, byte, int)} and {@link #ocrInitializeLogSystem(com.sun.jna.Pointer, byte, byte, int)} instead
     */
    @Deprecated
    NativeLong ocrInitializeLogSystem(Pointer path, byte console, byte daily, int level);
    /**
     * Original signature : <code>long ocrInitializeLogSystem(const char*, bool, bool, int)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1018</i>
     */
    NativeLong ocrInitializeLogSystem(String path, byte console, byte daily, int level);
    /**
     * Original signature : <code>long ocrGetRegionId()</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1032</i>
     */
    NativeLong ocrGetRegionId();
    /**
     * Original signature : <code>long ocrFilterPlateResultByVehicleDirection(type_id_ocr, long, long, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1039</i>
     */
    NativeLong ocrFilterPlateResultByVehicleDirection(NativeLong id, NativeLong moving_away, NativeLong moving_approaching, NativeLong moving_unknown);
    /**
     * Original signature : <code>long ocrSetRobustnessFalsePositives(type_id_ocr, long)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1046</i>
     */
    NativeLong ocrSetRobustnessFalsePositives(NativeLong id, NativeLong status);
    /**
     * Original signature : <code>long ocrConfigAnalyticsMMC(type_id_ocr, const tInitMMC*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1053</i>
     */
    NativeLong ocrConfigAnalyticsMMC(NativeLong id, tInitMMC init_info);
    /**
     * Original signature : <code>long ocrGetLicenseStatus(long*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1062</i>
     */
    NativeLong ocrGetLicenseStatus(NativeLongByReference arr_status);
    /**
     * Original signature : <code>long ocrGetWorldCountryStateCode(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1069</i><br>
     * @deprecated use the safer methods {@link #ocrGetWorldCountryStateCode(java.lang.String)} and {@link #ocrGetWorldCountryStateCode(com.sun.jna.Pointer)} instead
     */
    @Deprecated
    NativeLong ocrGetWorldCountryStateCode(Pointer str_world_code);
    /**
     * Original signature : <code>long ocrGetWorldCountryStateCode(const char*)</code><br>
     * <i>native declaration : header/vaxtor_ocr.h:1069</i>
     */
    NativeLong ocrGetWorldCountryStateCode(String str_world_code);
}
