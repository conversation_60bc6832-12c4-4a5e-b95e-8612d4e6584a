package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;


/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_bson_schema</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_bson_schemaLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_bson_schemaLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_bson_schemaLibrary INSTANCE = (Kestrel_bson_schemaLibrary)Native.load(Kestrel_bson_schemaLibrary.JNA_LIBRARY_NAME, Kestrel_bson_schemaLibrary.class);
	/**
	 * @return Schame KesON node, should be freed using kestrel_bson_delete().<br>
	 * Original signature : <code>kestrel_bson_schema_build(const char*)</code><br>
	 * <i>native declaration : include/kestrel_bson_schema.h:13</i>
	 */
	Pointer kestrel_bson_schema_build(String schema_str);
	/**
	 * @return KESTREL_OK for succeed, otherwise failure.<br>
	 * Original signature : <code>int kestrel_bson_schema_validate(kestrel_bson, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson_schema.h:20</i>
	 */
	int kestrel_bson_schema_validate(Pointer schema, Pointer in);
	/**
	 * validation error<br>
	 * Original signature : <code>int kestrel_bson_schema_validate_ex(kestrel_bson, kestrel_bson, kestrel_bson*)</code><br>
	 * <i>native declaration : include/kestrel_bson_schema.h:30</i>
	 */
	int kestrel_bson_schema_validate_ex(Pointer schema, Pointer in, Pointer error_array);
}
