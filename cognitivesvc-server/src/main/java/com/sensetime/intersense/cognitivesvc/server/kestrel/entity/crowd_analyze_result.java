package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_analyze_result extends Structure {
	/** < 视频流上下文ID */
	public long context_id;
	/** < 当前帧在整个处理的帧数组中的索引位置 */
	public long frame_index;
	/** < 对应视频帧的时间戳 */
	public long timestamp;
	/**
	 * <全图的人群信息<br>
	 * C type : crowd_density_result_t
	 */
	public crowd_density_result_t global_density_result;
	/**
	 * < 跨线ROI分析结果<br>
	 * C type : crowd_cross_roi_result_list_t
	 */
	public crowd_cross_roi_result_list_t cross_roi_result_list;
	/**
	 * < 滞留ROI分析结果<br>
	 * C type : crowd_roi_result_list_t
	 */
	public crowd_roi_result_list_t retention_roi_result_list;
	/**
	 * < 逆行ROI分析结果<br>
	 * C type : crowd_roi_result_list_t
	 */
	public crowd_roi_result_list_t retrograde_roi_result_list;
	/**
	 * < 入侵ROI分析结果<br>
	 * C type : crowd_roi_result_list_t
	 */
	public crowd_roi_result_list_t intrusion_roi_result_list;
	/**
	 * <聚集ROI分析结果<br>
	 * C type : crowd_movement_roi_result_list_t
	 */
	public crowd_movement_roi_result_list_t congregate_roi_result_list;
	/**
	 * <分散ROI分析结果<br>
	 * C type : crowd_movement_roi_result_list_t
	 */
	public crowd_movement_roi_result_list_t scatter_roi_result_list;
	/**
	 * <人群速度ROI分析结果<br>
	 * C type : crowd_roi_result_list_t
	 */
	public crowd_roi_result_list_t speed_roi_result_list;
	/**
	 * <人群社交距离ROI分析结果<br>
	 * C type : crowd_close_roi_result_list_t
	 */
	public crowd_close_roi_result_list close_roi_result_list;
	/**
	 * <排队ROI分析结果<br>
	 * C type : crowd_queue_roi_result_list_t
	 */
	public crowd_queue_roi_result_list queue_roi_result_list;
	public crowd_analyze_result() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("context_id", "frame_index", "timestamp", "global_density_result", "cross_roi_result_list", "retention_roi_result_list", "retrograde_roi_result_list", "intrusion_roi_result_list", "congregate_roi_result_list", "scatter_roi_result_list", "speed_roi_result_list", "close_roi_result_list", "queue_roi_result_list");
	}
	public crowd_analyze_result(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_analyze_result implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_analyze_result implements Structure.ByValue {
		
	};
}