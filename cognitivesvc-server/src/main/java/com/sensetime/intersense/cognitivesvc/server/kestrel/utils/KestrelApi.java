package com.sensetime.intersense.cognitivesvc.server.kestrel.utils;

import java.io.File;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.lang3.StringUtils;

import com.sensetime.intersense.cognitivesvc.server.kestrel.*;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.*;
import com.sensetime.intersense.cognitivesvc.server.utils.HostUtils;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.DoubleByReference;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KestrelApi {
	public static final long _SIZE_kestrel_area2d_t     = Native.getNativeSize(kestrel_area2d_t.class, new kestrel_area2d_t());
	
	public static final long _SIZE_crowd_analyze_result = Native.getNativeSize(crowd_analyze_result.class, new crowd_analyze_result());
	
	public static final long _SIZE_kestrel_point2df_t   = Native.getNativeSize(kestrel_point2df_t.class, new kestrel_point2df_t());   
    
    public static final long _SIZE_crowd_roi_result_t   = Native.getNativeSize(crowd_roi_result_t.class, new crowd_roi_result_t());
    
    public static final long _SIZE_crowd_head_target_t  = Native.getNativeSize(crowd_head_target_t.class, new crowd_head_target_t());
    
    public static final long _SIZE_crowd_movement_roi_result_t  = Native.getNativeSize(crowd_movement_roi_result_t.class, new crowd_movement_roi_result_t());
    
    public static final long _SIZE_crowd_close_roi_result_t  = Native.getNativeSize(crowd_close_roi_result.class, new crowd_close_roi_result());
    
    public static final long _SIZE_crowd_queue_roi_result_t  = Native.getNativeSize(crowd_queue_roi_result.class, new crowd_queue_roi_result());
    
    public static final long _SIZE_crowd_queue_target_t  = Native.getNativeSize(crowd_queue_target_t.class, new crowd_queue_target_t());
    
    public static final long _SIZE_crowd_cross_roi_result_t   = Native.getNativeSize(crowd_cross_roi_result_t.class, new crowd_cross_roi_result_t());

	
	private static final Map<Long, Beholder> modelReusedMap = new HashMap<Long, Beholder>();

	private static final Map<String, Integer> pluginReusedMap = new HashMap<String, Integer>();

	public static final byte KESON_FRAME() {
		return Kestrel_bson_extLibrary.KESON_FRAME;
	}

	public static final byte KESON_AREA2D() {
		return Kestrel_bson_extLibrary.KESON_AREA2D;
	}
	
	public static final String kestrel_device_get_name(String deviceName) {
		return Kestrel_deviceLibrary.INSTANCE.kestrel_device_get_name(Kestrel_deviceLibrary.INSTANCE.kestrel_device_get_handle());
	}

	public static final int kestrel_device_bind(String deviceName, String deviceid) {
		return Kestrel_deviceLibrary.INSTANCE.kestrel_device_bind(deviceName, keson_parse("{\"id\": " + deviceid + "}"));
	}

	public static final synchronized String kestrel_plugin_version(String pluginName) {
		Pointer plugin = Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_find(pluginName);
		if(plugin == null)
			return null;
		
		return Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_version(plugin);
	}

	public static final Pointer kestrel_annotator_open(String pluginName, String annotatorConfig) {
		Pointer plugin = Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_find(pluginName);
		if(plugin == null)
			throw new RuntimeException("[" + pluginName + "] plugin does not exist.");
		
		return Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_create_by_name(pluginName, keson_parse(annotatorConfig));
	}

	public static final int kestrel_annotator_startup(Pointer annotator, Pointer in, PointerByReference out) {
		return Kestrel_annotatorLibrary.INSTANCE.kestrel_annotator_startup(annotator, in, out);
	}

	public static final int kestrel_annotator_process(Pointer annotator, Pointer in, PointerByReference out) {
		return Kestrel_annotatorLibrary.INSTANCE.kestrel_annotator_process(annotator, in, out);
	}
	public static final int kestrel_frame_save(Pointer in, String fileName) {
		return Kestrel_frame_utilsLibrary.INSTANCE.kestrel_frame_save( in, fileName);
	}

	public static final int kestrel_frame_encode(Pointer in, int enc, Pointer buffer) {
		return Kestrel_frame_utilsLibrary.INSTANCE.kestrel_frame_encode( in, enc, buffer);
	}

	public static final Kestrel_bufferLibrary.Pointer kestrel_buffer_alloc(long size, int type) {
		return Kestrel_bufferLibrary.INSTANCE.kestrel_buffer_alloc(size, type);
	}

	public static final int kestrel_annotator_terminate(Pointer annotator, Pointer in, PointerByReference out) {
		return Kestrel_annotatorLibrary.INSTANCE.kestrel_annotator_terminate(annotator, in, out);
	}

	public static final void kestrel_annotator_close(PointerByReference annotator) {
		Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_destroy(annotator);
	}

	public static final void kestrel_log_set_level(int level) {
		Kestrel_logLibrary.INSTANCE.kestrel_log_set_level(level);
	}
	
	public static final String kestrel_error_info(int errcode) {
		return Kestrel_errorLibrary.INSTANCE.kestrel_error_info(errcode);
	}
	
	public static final int kestrel_init(String product) {
		return Kestrel_envLibrary.INSTANCE.kestrel_init(product);
	}

	public static final int kestrel_license_add(String license, String signed_code) {
		return Kestrel_licenseLibrary.INSTANCE.kestrel_license_add(license, signed_code);
	}

	public static final Pointer keson_parse(String string) {
		ByteBuffer buffer = ByteBuffer.wrap(string.getBytes());
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_decode_from_data(buffer, buffer.capacity(), KESTREL_BSON_FMT_JSON_RELAXED);
	}
	
	public static final Pointer keson_create_object() {
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_create_document();
	}

	public static final Pointer keson_add_item_to_object(Pointer document, String key, Pointer item) {
		if(document == null)
			return null;
		
		Kestrel_bsonLibrary.INSTANCE.kestrel_bson_add_document_item(document, key, item);
		return document;
	}

	public static final Pointer keson_create_string(String string) {
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_create_cstring(string);
	}

	public static final Pointer keson_create_int(long val) {
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_create_int64(val);
	}

	public static final Pointer keson_create_double(double val) {
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_create_float64(val);
	}

	public static final Pointer keson_delete_item_from_object(Pointer document, String key) {
		if(document == null)
			return null;
		
		Kestrel_bsonLibrary.INSTANCE.kestrel_bson_delete_document_item(document, key);
		return document;
	}

	public static final Pointer keson_create_bool(int bool) {
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_create_boolean(bool);
	}

	public static final Pointer keson_create_array() {
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_create_array();
	}

	public static final int keson_get_ext_data(Pointer node, PointerByReference data) {
		if(node != null)
			return Kestrel_bson_extLibrary.INSTANCE.kestrel_bson_get_ext_data(node, data);
		
		return -1;
	}
	
	public static final int kestrel_bson_get_ext_type(Pointer node) {
		if(node == null)
			return -1;
		
		return Kestrel_bson_extLibrary.INSTANCE.kestrel_bson_get_ext_type(node);
	}

	public static final PointerByReference keson_get_ext_data(Pointer node) {
		PointerByReference data = new PointerByReference();
		if(node != null)
			Kestrel_bson_extLibrary.INSTANCE.kestrel_bson_get_ext_data(node, data);
		return data;
	}

	public static final Pointer keson_create_ext_object(byte type, Pointer data) {
		return Kestrel_bson_extLibrary.INSTANCE.kestrel_bson_create_ext_data(type, data);
	}

	public static final Pointer keson_create_ext_frame(Pointer data) {
		return Kestrel_bson_extLibrary.INSTANCE.kestrel_bson_create_ext_data(KESON_FRAME(), data);
	}

	public static final Pointer keson_create_ext_area2d(Pointer data) {
		return Kestrel_bson_extLibrary.INSTANCE.kestrel_bson_create_ext_data(KESON_AREA2D(), data);
	}
	
	public static final Pointer keson_child(Pointer item) {
		if(item == null)
			return null;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_child(item);
	}
	
	public static final Pointer keson_child_next(Pointer item) {
		if(item == null)
			return null;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_next(item);
	}
	
	public static final Pointer keson_child_prev(Pointer item) {
		if(item == null)
			return null;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_prev(item);
	}

	public static final String keson_key(Pointer item) {
		if(item == null)
			return null;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_key(item);
	}

	public static final Pointer keson_get_object_item(Pointer document, String key) {
		if(document == null)
			return null;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_get_document_item(document, key);
	}

	public static final int keson_array_size(Pointer array) {
		if(array == null)
			return 0;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_child_number(array);
	}

	public static final int keson_type(Pointer item) {
		if(item == null)
			return -1;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_type(item);
	}

	public static final Pointer keson_get_array_item(Pointer array, int index) {
		if(array == null)
			return null;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_get_array_item(array, index);
	}

	public static final long keson_get_int(Pointer item) {
		int type = Kestrel_bsonLibrary.INSTANCE.kestrel_bson_type(item);
		if(KESTREL_BSON_INT32 == type) {
			IntByReference val = new IntByReference();
			Kestrel_bsonLibrary.INSTANCE.kestrel_bson_get_int32(item, val);
			return val.getValue();
		}else if(KESTREL_BSON_INT64 == type) {
			LongByReference val = new LongByReference();
			Kestrel_bsonLibrary.INSTANCE.kestrel_bson_get_int64(item, val);
			return val.getValue();
		}else 
			return Long.MIN_VALUE;
	}

	public static final double keson_get_double(Pointer item) {
		int type = Kestrel_bsonLibrary.INSTANCE.kestrel_bson_type(item);
		if(KESTREL_BSON_FLOAT64 == type) {
			DoubleByReference val = new DoubleByReference();
			Kestrel_bsonLibrary.INSTANCE.kestrel_bson_get_float64(item, val);
			return val.getValue();
		}else 
			return Double.MIN_VALUE;
	}

	public static final Pointer keson_detach_from_array(Pointer array, int which) {
		if(array == null)
			return null;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_detach_array_item(array, which);
	}

	public static final Pointer keson_detach_item_from_object(Pointer document, String key) {
		if(document == null)
			return null;
		
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_detach_document_item(document, key);
	}

	public static final Pointer keson_add_item_to_array(Pointer array, Pointer... items) {
		if(array == null)
			return null;
		
		for(Pointer item : items)
			Kestrel_bsonLibrary.INSTANCE.kestrel_bson_add_array_item(array, item);
		
		return array;
	}
	
	public static final int keson_encode_to_data(Pointer keson, PointerByReference data, LongByReference len, int fmt) {
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_encode_to_data(keson, data, len, fmt);
	}

	public static final void keson_deep_delete(PointerByReference tub) {
		Kestrel_bsonLibrary.INSTANCE.kestrel_bson_delete(tub);
	}

	public static final Pointer keson_duplicate(Pointer keson, int recurse) {
		return Kestrel_bsonLibrary.INSTANCE.kestrel_bson_duplicate(keson, recurse);
	}

	public static final int kestrel_frame_video_width(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_video_width(frame);
	}

	public static final int kestrel_frame_jpeg4(Pointer data, String filePath) {
		return JpegEncoderLibrary.INSTANCE.EncodeJPEG( data,  filePath);
	}
	public static final Pointer kestrel_frame_encoder(Pointer data, int filePath, LongByReference outSize) {
		return FrameEncoderLibrary.INSTANCE.EncodeFrame( data,  filePath, outSize);
	}

	public static final OpencvDecoderLibrary.OpencvVideoDecoder kestrel_create_opencv_decoder(boolean gpu) {
		return OpencvDecoderLibrary.INSTANCE.CreateOpencvDecoder( gpu);
	}
	public static final boolean kestrel_create_opencv_stream(OpencvDecoderLibrary.OpencvVideoDecoder decoder, String rtsp) {
		return OpencvDecoderLibrary.INSTANCE.OpenStream(decoder, rtsp);
	}
	public static final Pointer kestrel_get_opencv_frame(OpencvDecoderLibrary.OpencvVideoDecoder decoder) {
		return OpencvDecoderLibrary.INSTANCE.GetFrame(decoder);
	}
	public static final int kestrel_get_opencv_frame(OpencvDecoderLibrary.OpencvVideoDecoder decoder,PointerByReference f_out) {
		return OpencvDecoderLibrary.INSTANCE.GetFrame(decoder, f_out);
	}
	public static final void kestrel_close_opencv_frame(OpencvDecoderLibrary.OpencvVideoDecoder decoder) {
		OpencvDecoderLibrary.INSTANCE.Close(decoder);
	}
//	public static final void kestrel_frame_createDecoder(String filePath,PointerByReference demuxerCtx, PointerByReference decodeCtx, PointerByReference fp) {
//		 DecoderEngineLibrary.INSTANCE.CreateDecoder2( filePath,  demuxerCtx, decodeCtx, fp);
//	}
	public static final Pointer kestrel_frame_createDecoder(byte[] filePath, int length ) {
		 return DecoderEngineLibrary.INSTANCE.CreateDecoder( filePath, length);
	}
	public static final Pointer kestrel_frame_createDecoderRev(byte[] filePath, int length ) {
		return DecoderEngineLibrary.INSTANCE.CreateDecoderRev( filePath, length);
	}

	public static final void kestrel_create_construct_params(byte[] filePath, int length, String format,int nvBuffer,int buffer, String extra  ) {
		 DecoderEngineLibrary.INSTANCE.CreateConstruct( filePath, length, format, nvBuffer, buffer, extra);
	}
	public static final Pointer kestrel_frame_createDemuxer(byte[] filePath, int length ) {
		return DecoderEngineLibrary.INSTANCE.CreateDemuxer( filePath, length);
	}
	public static final Pointer kestrel_frame_createCodec(byte[] filePath, int length ,Pointer demuxerCtx) {
		return DecoderEngineLibrary.INSTANCE.CreateCodec( filePath, length, demuxerCtx);
	}
	public static final Pointer kestrel_frame_create_pool(byte[] filePath, int length ) {
		return DecoderEngineLibrary.INSTANCE.CreateFramePool(filePath, length);
	}
	public static final DecoderEngineLibrary.DecoderResult3 kestrel_frame_createDecoder3(String filePath) {
		return DecoderEngineLibrary.INSTANCE.CreateDecoder3( filePath);
	}
	public static final void ReleaseDecoder(byte[] filePath,int length) {
		 DecoderEngineLibrary.INSTANCE.ReleaseDecoder( filePath, length);
	}


	public static final Pointer PullFrame(Pointer filePath,int length, IntByReference errCode) {
	    return	DecoderEngineLibrary.INSTANCE.PullFrame( filePath, length, errCode);
	}
	public static final void ReleaseFrame(Pointer FrameCgo) {
			DecoderEngineLibrary.INSTANCE.ReleaseFrame( FrameCgo);
	}
	public static final Pointer PullFrame2(Pointer demuxerCtx, Pointer decodeCtx) {
		return	DecoderEngineLibrary.INSTANCE.PullFrame2( demuxerCtx, decodeCtx);
	}

	public static final Pointer CreateDemuxer(byte[] filePath,int length) {
		return	DecoderEngineLibrary.INSTANCE.CreateDemuxer(filePath, length);
	}
	public static final Pointer CreateDecoder2(Pointer demuxer) {
		return	DecoderEngineLibrary.INSTANCE.CreateDecoder2(demuxer);
	}


	public static final int kestrel_frame_video_height(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_video_height(frame);
	}

	public static final int kestrel_frame_video_stride(Pointer frame, int plane) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_video_stride(frame, plane);
	}

	public static final Pointer kestrel_frame_plane(Pointer frame, int plane) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_plane(frame, plane);
	}
	
	public static final long kestrel_frame_plane_origin(Pointer frame, int plane) {
		Pointer ptr = Kestrel_frameLibrary.INSTANCE.kestrel_frame_plane_origin(frame, plane);
		return Pointer.nativeValue(ptr);
	}
	
	public static final boolean kestrel_frame_is_same_source(Pointer frame1, Pointer frame2) {
		if(frame1 == null || frame2 == null)
			return false;
		
		return Pointer.nativeValue(Kestrel_frameLibrary.INSTANCE.kestrel_frame_plane_origin(frame1, 0)) == Pointer.nativeValue(Kestrel_frameLibrary.INSTANCE.kestrel_frame_plane_origin(frame2, 0));
	}
	
	public static final int kestrel_frame_stream_id(Pointer frame) {
		return (int)Kestrel_frameLibrary.INSTANCE.kestrel_frame_stream_id(frame);
	}
	
	public static final int kestrel_frame_plane_num(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_plane_num(frame);
	}
	
	public static final int kestrel_frame_video_format(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_video_format(frame);
	}
	
	public static final int kestrel_frame_set_pts(Pointer frame, long pts) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_set_pts(frame, pts);
	}
	
	public static long kestrel_frame_pts(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_pts(frame);
	}
	
	public static void kestrel_packet_set_dts(Pointer packet, long dts) {
		packet.write(24, new long[] {dts}, 0, 1);
	}
	
	public static void kestrel_packet_set_pts(Pointer packet, long pts) {
		packet.write(16, new long[] {pts}, 0, 1);
	}
	
	public static void kestrel_packet_set_stream_id(Pointer packet, int stream_id) {
		packet.write(12, new int[] {stream_id}, 0, 1);
	}

	public static final void kestrel_frame_free(PointerByReference tub) {
		Kestrel_frameLibrary.INSTANCE.kestrel_frame_free(tub);
		tub.setValue(null);
	}

	public static final Pointer kestrel_frame_roi(Pointer frame, kestrel_area2d_t.ByValue roi) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_roi(frame, roi);
	}

	public static final int kestrel_frame_mem_type(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_mem_type(frame);
	}
	
	public static final int kestrel_frame_scale(Pointer in, PointerByReference out, float scale) {
		return Kestrel_frame_utilsLibrary.INSTANCE.kestrel_frame_scale(in, out, scale);
	}

	public static final Pointer kestrel_frame_ref(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_ref(frame);
	}
	public static final kestrel_packet_t kestrel_packet_ref(kestrel_packet_t frame) {
		return Kestrel_packetLibrary.INSTANCE.kestrel_packet_ref(frame);
	}
	public static final void kestrel_packet_free(PointerByReference frame) {
		 Kestrel_packetLibrary.INSTANCE.kestrel_packet_free(frame);
	}



	public static final int kestrel_frame_download(Pointer src, PointerByReference dst) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_download(src, dst);
	}

	public static final int kestrel_frame_upload(Pointer src, PointerByReference dst) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_upload(src, dst);
	}

	public static final int kestrel_frame_get_ref_cnt(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_get_ref_cnt(frame);
	}

	public static final long kestrel_frame_size(Pointer frame) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_size(frame);
	}

	public static final Pointer kestrel_frame_alloc(int type, int fmt, int w, int h, int[] strides, int[] padded_height) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_alloc(type, fmt, w, h, strides, padded_height);
	}

	public static final Pointer kestrel_frame_pool_alloc(int mem_type, int fmt, int width, int height, int align, long capacity){
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_pool_alloc(mem_type, fmt, width, height, align, capacity);
	}

	public static final Pointer kestrel_frame_pool_get(Pointer fp) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_pool_get(fp);
	}
	
	public static final Pointer kestrel_frame_duplicate(Pointer src) {
		return Kestrel_frameLibrary.INSTANCE.kestrel_frame_duplicate(src);
	};

	public static final int kestrel_frame_cvt_color(Pointer in, PointerByReference out, int type) {
		return Kestrel_frame_utilsLibrary.INSTANCE.kestrel_frame_cvt_color(in, out, type);
	}

	public static final Pointer kestrel_frame_load(String filename) {
		return Kestrel_frame_utilsLibrary.INSTANCE.kestrel_frame_load(filename);
	}
	
	public static final Pointer kestrel_frame_load_from_memory(byte[] image) {
		ByteBuffer buffer = ByteBuffer.wrap(image);
		return Kestrel_frame_utilsLibrary.INSTANCE.kestrel_frame_load_from_memory(buffer, image.length);
	}

	public static final void kestrel_model_load(String model_file, PointerByReference model) {
		Kestrel_modelLibrary.INSTANCE.kestrel_model_load(model_file, model);
	}

	public static final void kestrel_model_get_file(Pointer m, String file, Pointer buf, LongByReference file_size) {
		Kestrel_modelLibrary.INSTANCE.kestrel_model_get_file(m, file, buf, file_size);
	}

	public static final long kestrel_model_file_size(Pointer m, String file) {
		return Kestrel_modelLibrary.INSTANCE.kestrel_model_file_size(m, file);
	}

	public static final void kestrel_model_unload(PointerByReference m) {
		Kestrel_modelLibrary.INSTANCE.kestrel_model_unload(m);
	}

	public static final Pointer flock_pipeline_create(String pipeline_json) {
		Pointer pipeline = FlockLibrary.INSTANCE.flock_pipeline_create(pipeline_json);
		FlockLibrary.INSTANCE.flock_pipeline_start(pipeline);
		return pipeline;
	}
	public static final Pointer flock_pipeline_create_single(String pipeline_json) {
		Pointer pipeline = FlockLibrary.INSTANCE.flock_pipeline_create(pipeline_json);
		return pipeline;
	}
	
	public static final int flock_pipeline_start(Pointer pipeline) {
		return FlockLibrary.INSTANCE.flock_pipeline_start(pipeline);
	}
	
	public static final int flock_pipeline_control(Pointer pipeline, int cmd, Pointer params, PointerByReference result) {
		return FlockLibrary.INSTANCE.flock_pipeline_control(pipeline, cmd, params, result);
	}
	
	public static final int flock_pipeline_stop(Pointer pipeline) {
		return FlockLibrary.INSTANCE.flock_pipeline_stop(pipeline);
	}
	
	public static final void flock_pipeline_destroy(Pointer pipeline) {
		FlockLibrary.INSTANCE.flock_pipeline_stop(pipeline);
		FlockLibrary.INSTANCE.flock_pipeline_destroy(pipeline);
	}

	public static final void flock_module_env_init() {
		FlockLibrary.INSTANCE.flock_module_env_init();
	}

	public static final int flock_pipeline_input(Pointer pipeline, String stream_name, Pointer input) {
		return FlockLibrary.INSTANCE.flock_pipeline_input(pipeline, stream_name, input);
	}

	public static final int flock_pipeline_try_input(Pointer pipeline, String stream_name, Pointer input) {
		return FlockLibrary.INSTANCE.flock_pipeline_try_input(pipeline, stream_name, input);
	}

	public static final int flock_pipeline_output(Pointer pipeline, String stream_name, PointerByReference output) {
		return FlockLibrary.INSTANCE.flock_pipeline_output(pipeline, stream_name, output);
	}

	public static final int flock_pipeline_try_output(Pointer pipeline, String stream_name, PointerByReference output) {
		return FlockLibrary.INSTANCE.flock_pipeline_try_output(pipeline, stream_name, output);
	}
	
	public static final int flock_pipeline_input_and_output(Pointer pipeline, String stream_name, Pointer input, PointerByReference output) {
		int ret = 0;
		
		ret = KestrelApi.flock_pipeline_input(pipeline, stream_name, input);
		if(ret != 0){
			log.error("[KestrelApi] flock_pipeline_input error code:{}",ret);
			return ret;
		}

		return KestrelApi.flock_pipeline_output(pipeline, stream_name, output);
	}
	
	public static final int flock_pipeline_input_and_try_output(Pointer pipeline, String stream_name, Pointer input, PointerByReference output) {
		int ret = 0;
		
		ret = KestrelApi.flock_pipeline_input(pipeline, stream_name, input);
		if(ret != 0)
			return ret;
		
		return KestrelApi.flock_pipeline_try_output(pipeline, stream_name, output);
	}
	
	/** cogx加载模型 用来复用模型句柄*/
	public static synchronized Pointer kestrel_annotator_open_or_reuse(String plugin, String annotatorPath, int maxBatchSize, boolean tryReuse) {
		File file = new File(annotatorPath);
		if(!file.exists())
			throw new RuntimeException(annotatorPath + " does not exist.");
		
		String annotatorName = file.getName();
		long fileSize = file.length();
		
		for(Entry<Long, Beholder> entry : modelReusedMap.entrySet()) {
			Beholder holder = entry.getValue();
			if(tryReuse
				&& Objects.equals(annotatorName, holder.getAnnotatorName())
				&& Objects.equals(plugin, holder.getPlugin())
				&& holder.getFileSize() == fileSize 
				&& holder.getMaxBatchSize() == maxBatchSize) {
				
				holder.getRefCount().getAndIncrement();
				
				log.info("Reused model [" + annotatorPath + "], maxBatchSize [" + maxBatchSize + "] usedCount [" + holder.getRefCount().get() + "]");
				return holder.getPointer();
			}
		}
		
		int[] memIntSrc = HostUtils.getGpuMem();
		Pointer pointer = kestrel_annotator_open(plugin, "{\"model\":\"" + annotatorPath + "\",\"max_batch_size\":" + maxBatchSize + "}");
		int[] memIntDst = HostUtils.getGpuMem();
		
		log.info("Loaded model[" + annotatorPath + "] done. GPU MEM [" + memIntSrc[0] + ", "+ memIntSrc[1] + " -> " + memIntDst[0] + "," + memIntDst[1] + "]");
		modelReusedMap.put(Pointer.nativeValue(pointer), Beholder.builder().plugin(plugin).annotatorName(annotatorName).fileSize(fileSize).maxBatchSize(maxBatchSize).pointer(pointer).build());
		return pointer;
	}
	
	public static synchronized void kestrel_annotator_close_or_release(PointerByReference annotator) {
		Long address = Pointer.nativeValue(annotator.getValue());
		Beholder holder = modelReusedMap.get(address);

		holder.getRefCount().getAndDecrement();
		
		if(holder.getRefCount().get() > 0) {
			log.info("releasing annotator [" + holder.getAnnotatorName() + "] usedCount [" + holder.getRefCount().get() + "]");
		}else {
			log.info("unloading annotator [" + holder.getAnnotatorName() + "] , batchSize [" + holder.getMaxBatchSize() + "].");
			kestrel_annotator_close(annotator);
			modelReusedMap.remove(address);
		}
	}
	
	public static synchronized String kestrel_plugin_load(String file, String signature) {
		File kep = new File(file);
		if(!kep.exists())
			throw new RuntimeException(file + ", not exist, please check.");
		
		String predictPluginName = kep.getName().split("\\.")[0];
		if(pluginReusedMap.containsKey(predictPluginName)) {
			pluginReusedMap.put(predictPluginName, pluginReusedMap.get(predictPluginName) + 1);
			log.info(predictPluginName + " reused version is [" + kestrel_plugin_version(predictPluginName) + "]");
			return predictPluginName;
		}
		
		if(!file.startsWith("/usr/cognitivesvc/")) {
			File defaultKep = new File("/usr/cognitivesvc/" + predictPluginName + ".kep");
			if(defaultKep.exists()) {
				log.info("defaultKep[" + defaultKep.getAbsolutePath() + "] exist, use it and ignore input[" + file + "].");
				file = defaultKep.getAbsolutePath();
			}
		}
		
		String pluginName = kep.getName().split("\\.")[0];
		Pointer plugin = Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_find(pluginName);
		if(plugin == null) {
			log.info("kestrel_plugin_find plugin null {}", pluginName);
			plugin = Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_load(file);
		}
		
		kestrel_plugin_t plugin_t = new kestrel_plugin_t(plugin);
		plugin_t.read();
		pluginName = plugin_t.plugin_name;
		
		if(StringUtils.isBlank(pluginName))
			throw new RuntimeException(file + ", plugin load error, please check.");
		
		log.info(pluginName + " created version is [" + kestrel_plugin_version(pluginName) + "], path" + file);
		
		if(pluginReusedMap.containsKey(pluginName)) {
			pluginReusedMap.put(predictPluginName, pluginReusedMap.get(predictPluginName) + 1);
		}else {
			pluginReusedMap.put(predictPluginName, 1);
		}
		
		return pluginName;
	}
	
	/** 卸载 */
	public static synchronized void kestrel_plugin_unload(String pluginName) {
		if(pluginReusedMap.containsKey(pluginName)) {
			pluginReusedMap.put(pluginName, pluginReusedMap.get(pluginName) - 1);
			
			if(pluginReusedMap.get(pluginName) <= 0) {
				log.info("unloading plugin[" + pluginName + "] thoroughlys.");
				Pointer plugin = Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_find(pluginName);
				if(plugin != null) {
					log.info("unloading plugin[" + pluginName + "] find thoroughly.");
					Kestrel_pluginLibrary.INSTANCE.kestrel_plugin_unload(plugin);
				}
				
				pluginReusedMap.remove(pluginName);
			}else {
				log.info("unloading plugin[" + pluginName + "], counter remaining [" + pluginReusedMap.get(pluginName) + "]");
			}
		}else {
			throw new RuntimeException("no such plugin [" + pluginName + "].");
		}
	}
	
	/** <i>native declaration : include/kestrel_frame.h:19</i> */
	public static final int KESTREL_FLUSH_FRAME = -2;
	/** <i>native declaration : include/kestrel_frame.h:21</i> */
	public static final int KESTREL_UNKNOWN_FRAME = -1;
	/** <i>native declaration : include/kestrel_frame.h:23</i> */
	public static final int KESTREL_VIDEO_FRAME = 0;
	/** <i>native declaration : include/kestrel_frame.h:25</i> */
	public static final int KESTREL_AUDIO_FRAME = 1;
	
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KESTREL_E_PLUGIN = (int)0x1;
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KPLUGIN_OK = (int)0;
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KPLUGIN_E_AGAIN = (int)(0x80000000 | ((0x4B) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0x0001) & 0xffff));
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KPLUGIN_E_EOF = (int)(0x80000000 | ((0x4B) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0x0002) & 0xffff));
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KPLUGIN_E_INTERNAL = (int)(0x80000000 | ((0x4B) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0x0003) & 0xffff));
	
	/** <i>native declaration : include/kestrel_device.h:26</i> */
	public static final int KESTREL_MEM_UNKNOWN = -1;
	/** <i>native declaration : include/kestrel_device.h:28</i> */
	public static final int KESTREL_MEM_HOST = 0;
	/** <i>native declaration : include/kestrel_device.h:30</i> */
	public static final int KESTREL_MEM_DEVICE = 1;
	
	
	/** <i>native declaration : include/kestrel_log.h:17</i> */
	public static final int KESTREL_LL_TRACE = 0;
	/** <i>native declaration : include/kestrel_log.h:19</i> */
	public static final int KESTREL_LL_DEBUG = 1;
	/** <i>native declaration : include/kestrel_log.h:21</i> */
	public static final int KESTREL_LL_INFO = 2;
	/** <i>native declaration : include/kestrel_log.h:23</i> */
	public static final int KESTREL_LL_WARNING = 3;
	/** <i>native declaration : include/kestrel_log.h:25</i> */
	public static final int KESTREL_LL_ERROR = 4;
	/** <i>native declaration : include/kestrel_log.h:27</i> */
	public static final int KESTREL_LL_ESSENTIAL = 999;
	
	/** <i>native declaration : include/kestrel/kestrel_frame.h:33</i> */
	public static final int KESTREL_VIDEO_NONE = Kestrel_structLibrary.KESTREL_INVALID_FOURCC;
	/** <i>native declaration : include/kestrel/kestrel_frame.h:40</i> */
	public static final int KESTREL_VIDEO_GRAY = (('G') | (('R') << 8) | (('E') << 16) | (('Y') << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:42</i> */
	public static final int KESTREL_VIDEO_BGR = (('B') | (('G') << 8) | (('R') << 16) | ((24) << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:44</i> */
	public static final int KESTREL_VIDEO_BGRA = (('B') | (('G') << 8) | (('R') << 16) | (('A') << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:46</i> */
	public static final int KESTREL_VIDEO_RGB = (('R') | (('G') << 8) | (('B') << 16) | ((24) << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:48</i> */
	public static final int KESTREL_VIDEO_ARGB = (('A') | (('R') << 8) | (('G') << 16) | (('B') << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:50</i> */
	public static final int KESTREL_VIDEO_YU12 = (('Y') | (('U') << 8) | (('1') << 16) | (('2') << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:52</i> */
	public static final int KESTREL_VIDEO_NV12 = (('N') | (('V') << 8) | (('1') << 16) | (('2') << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:54</i> */
	public static final int KESTREL_VIDEO_NV21 = (('N') | (('V') << 8) | (('2') << 16) | (('1') << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:56</i> */
	public static final int KESTREL_VIDEO_GRAY16LE = (('Y') | (('1') << 8) | ((0) << 16) | ((16) << 24));
	/** <i>native declaration : include/kestrel/kestrel_frame.h:58</i> */
	public static final int KESTREL_VIDEO_GRAY16BE = ((16) | ((0) << 8) | (('1') << 16) | (('Y') << 24));
	
	/** <i>native declaration : include/kestrel_bson.h:16</i> */
	public static final int KESTREL_BSON_FMT_RAW = 0;
	/** <i>native declaration : include/kestrel_bson.h:17</i> */
	public static final int KESTREL_BSON_FMT_JSON_CANONICAL = 1;
	/** <i>native declaration : include/kestrel_bson.h:18</i> */
	public static final int KESTREL_BSON_FMT_JSON_RELAXED = 2;
	/** <i>native declaration : include/kestrel_bson.h:22</i> */
	public static final int KESTREL_BSON_INVALID = 0x00;
	/** <i>native declaration : include/kestrel_bson.h:23</i> */
	public static final int KESTREL_BSON_FLOAT64 = 0x01;
	/** <i>native declaration : include/kestrel_bson.h:24</i> */
	public static final int KESTREL_BSON_STRING = 0x02;
	/** <i>native declaration : include/kestrel_bson.h:25</i> */
	public static final int KESTREL_BSON_DOCUMENT = 0x03;
	/** <i>native declaration : include/kestrel_bson.h:26</i> */
	public static final int KESTREL_BSON_ARRAY = 0x04;
	/** <i>native declaration : include/kestrel_bson.h:27</i> */
	public static final int KESTREL_BSON_BINARY = 0x05;
	/**
	 * Deprecated<br>
	 * <i>native declaration : include/kestrel_bson.h:28</i>
	 */
	public static final int KESTREL_BSON_UNDEFINED = 0x06;
	/** <i>native declaration : include/kestrel_bson.h:29</i> */
	public static final int KESTREL_BSON_OBJECTID = 0x07;
	/** <i>native declaration : include/kestrel_bson.h:30</i> */
	public static final int KESTREL_BSON_BOOLEAN = 0x08;
	/** <i>native declaration : include/kestrel_bson.h:31</i> */
	public static final int KESTREL_BSON_DATE = 0x09;
	/** <i>native declaration : include/kestrel_bson.h:32</i> */
	public static final int KESTREL_BSON_NULL = 0x0A;
	/** <i>native declaration : include/kestrel_bson.h:33</i> */
	public static final int KESTREL_BSON_REGEX = 0x0B;
	/**
	 * Deprecated<br>
	 * <i>native declaration : include/kestrel_bson.h:34</i>
	 */
	public static final int KESTREL_BSON_DBPOINTER = 0x0C;
	/**
	 * Unimplemented<br>
	 * <i>native declaration : include/kestrel_bson.h:35</i>
	 */
	public static final int KESTREL_BSON_JSCODE = 0x0D;
	/**
	 * Deprecated<br>
	 * <i>native declaration : include/kestrel_bson.h:36</i>
	 */
	public static final int KESTREL_BSON_SYMBOL = 0x0E;
	/**
	 * Deprecated<br>
	 * <i>native declaration : include/kestrel_bson.h:37</i>
	 */
	public static final int KESTREL_BSON_CODEWS = 0x0F;
	/** <i>native declaration : include/kestrel_bson.h:38</i> */
	public static final int KESTREL_BSON_INT32 = 0x10;
	/** <i>native declaration : include/kestrel_bson.h:39</i> */
	public static final int KESTREL_BSON_TIMESTAMP = 0x11;
	/** <i>native declaration : include/kestrel_bson.h:40</i> */
	public static final int KESTREL_BSON_INT64 = 0x12;
	/**
	 * Unimplemented<br>
	 * <i>native declaration : include/kestrel_bson.h:41</i>
	 */
	public static final int KESTREL_BSON_FLOAT128 = 0x13;
	/** <i>native declaration : include/kestrel_bson.h:42</i> */
	public static final int KESTREL_BSON_MAXKEY = 0x7F;
	/** <i>native declaration : include/kestrel_bson.h:43</i> */
	public static final int KESTREL_BSON_MINKEY = 0xFF;
	
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int KESTREL_OK = (int)0;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int KESTREL_ERR = (int)0x4B;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int KESTREL_E_CORE = (int)0x00;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_GLOBAL_LIB = (int)0x7ff;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_GLOBAL_MODULE = (int)0xf;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_MODULE = (int)0xe;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_CONNECTION_MODULE = (int)0xd;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALIDARG = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffff) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HANDLE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffe) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_OUTOFMEMORY = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffd) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffc) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_DELNOTFOUND = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffb) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALID_PIXEL_FORMAT = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffa) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_FILE_NOT_FOUND = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff9) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALID_FILE_FORMAT = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff8) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_UNSUPPORTED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfc18) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_FILE_EXPIRE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff7) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_READ_FILE_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff6) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_VERIFY_REG_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff5) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALID_AUTH = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff3) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_INVALID_APPID = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff2) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_AUTH_EXPIRE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff1) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_UUID_MISMATCH = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfff0) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_AUTH_CONNECT_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffef) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_AUTH_TIMEOUT = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffee) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_AUTH_INVALID = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffed) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_LICENSE_IS_NOT_ACTIVABLE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffec) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ACTIVE_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffeb) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ACTIVE_CODE_INVALID = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffea) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PRODUCT_VERSION_MISMATCH = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe9) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PLATFORM_NOTSUPPORTED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe8) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_UNZIP_FAILED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe7) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SUBMODULE_NON_EXIT = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe6) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_NO_NEED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe5) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe4) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_CODE_INVALID = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe3) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_ONLINE_ACTIVATE_CONNECT_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe2) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_RET_UNRECOGNIZED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe1) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HW_AUTH_INIT_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffe0) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HW_AUTH_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffdf) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_VERIFY_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffde) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_VERIFY_EXPIRE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffdd) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_NO_LICENSE = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffdc) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HW_REG_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffdb) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PRODUCT_VERSION_FAILED = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffda) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_LIB_EXIST = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffd9) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_CONNECTION_FAIL = (int)(0x80000000 | ((0x7ff) & 0x7ff) << 20 | ((0xd) & 0xf) << 16 | ((0xffff) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_LIB = (int)0x1;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_LICENSE_LIB = (int)0x7F;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_MODULE_ONLINEAUTH_MODULE = (int)0x1;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_LICENSE_SENTINEL_MODULE = (int)0x0;
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_UDID = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0xffff) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_FORMAT_HEADER = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0xfffe) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_PROTECTOR_INVALID_URL = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0xfffd) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HEARTBEAT_CONNECT_FAIL = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xffff) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_HEARTBEAT_INVALID_RESPONSE = (int)(0x80000000 | ((0x1) & 0x7ff) << 20 | ((0xf) & 0xf) << 16 | ((0xfffe) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_PARSE_XML = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8000) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_CONNECT_FAIL = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8001) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_V2C_MISSING = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8002) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_INVALID_RESPONSE = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8003) & 0xffff));
	/** <i>native declaration : include/kestrel_error_def.h</i> */
	public static final int CV_E_SENTINEL_FINGERPRINT_MISMATCH = (int)(0x80000000 | ((0x7F) & 0x7ff) << 20 | ((0x0) & 0xf) << 16 | ((0x8004) & 0xffff));
	
	/** <i>native declaration : include/flock_define.h:20</i> */
	public static final int FLOCK_CTRL_CMD_UNKNOWN = -1;
	/** <i>native declaration : include/flock_define.h:22</i> */
	public static final int FLOCK_CTRL_CMD_NOTICE = 0;
	/** <i>native declaration : include/flock_define.h:23</i> */
	public static final int FLOCK_CTRL_CMD_ADD_SOURCE = 1;
	/** <i>native declaration : include/flock_define.h:24</i> */
	public static final int FLOCK_CTRL_CMD_UPDATE_SOURCE_CONFIG = 2;
	/** <i>native declaration : include/flock_define.h:25</i> */
	public static final int FLOCK_CTRL_CMD_RESET_SOURCE = 3;
	/** <i>native declaration : include/flock_define.h:26</i> */
	public static final int FLOCK_CTRL_CMD_REMOVE_SOURCE = 4;
	/** <i>native declaration : include/flock_define.h:27</i> */
	public static final int FLOCK_CTRL_CMD_CHECK_SOURCE_EXISTENCE = 5;
	/** <i>native declaration : include/flock_define.h:29</i> */
	public static final int FLOCK_CTRL_CMD_COUNT = 6;
	
	@Setter
	@Getter
	@Builder
	private static final class Beholder{
		private String plugin;
		private String annotatorName;
		private long fileSize;
		private int maxBatchSize;
		private Pointer pointer;
		
		@Builder.Default
		private AtomicInteger refCount = new AtomicInteger(1);
	}
}
