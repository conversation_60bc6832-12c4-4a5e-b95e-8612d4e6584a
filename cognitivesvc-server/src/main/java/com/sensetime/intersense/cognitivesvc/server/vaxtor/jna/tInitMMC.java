package com.sensetime.intersense.cognitivesvc.server.vaxtor.jna;

import com.sun.jna.NativeLong;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : header/vaxtor_types.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class tInitMMC extends Structure {
    /**
     * 0=>disable, 1=>enable<br>
     * C type : vx_int32
     */
    public NativeLong analytic_type;
    /**
     * DEPRECATED (set to 0)<br>
     * C type : vx_int32
     */
    public NativeLong analytics_quality;
    /**
     * DEPRECATED (set to 0)<br>
     * C type : vx_int32
     */
    public NativeLong min_global_confidence;
    public tInitMMC() {
        super();
    }
    protected List<String> getFieldOrder() {
        return Arrays.asList("analytic_type", "analytics_quality", "min_global_confidence");
    }
    /**
     * @param analytic_type 0=>disable, 1=>enable<br>
     * C type : vx_int32<br>
     * @param analytics_quality DEPRECATED (set to 0)<br>
     * C type : vx_int32<br>
     * @param min_global_confidence DEPRECATED (set to 0)<br>
     * C type : vx_int32
     */
    public tInitMMC(NativeLong analytic_type, NativeLong analytics_quality, NativeLong min_global_confidence) {
        super();
        this.analytic_type = analytic_type;
        this.analytics_quality = analytics_quality;
        this.min_global_confidence = min_global_confidence;
    }
    public tInitMMC(Pointer peer) {
        super(peer);
    }
    public static class ByReference extends tInitMMC implements Structure.ByReference {

    };
    public static class ByValue extends tInitMMC implements Structure.ByValue {

    };
}
