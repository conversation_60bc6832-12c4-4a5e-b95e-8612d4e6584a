package com.sensetime.intersense.cognitivesvc.server.entities;

import java.util.Date;
import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import org.apache.commons.lang3.StringUtils;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Entity
@Table(name = "${db.x_dynamic_model.table.name:x_dynamic_model}")
@Data
@Accessors(chain = true)
@Schema(title = "X动态模型", description = "X动态模型")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XDynamicModel {
	
	@Id
	@Column(name = "annotator_name")
	@Schema(description = "模型类名")
	private String  annotatorName; 
	
	@Column(name = "plugin_paths")
	@Schema(description = "组件路径,逗号分隔")
	private String  pluginPaths;
	
	@Column(name = "annotator_paths")
	@Schema(description = "模型路径,逗号分隔")
	private String  annotatorPaths;
	
	@Column(name = "update_ts")
	@Schema(description = "最后更新时间")
    private Date    updateTs;
	
	@Column(name = "sts")
	@Schema(description = "状态，0开启，非0关闭")
	private Integer sts;
	
	@Column(name = "java_class_name")
	@Schema(description = "动态编译类名(optional)")
	private String  javaClassName;
	
	@Column(name = "java_code")
	@Schema(description = "动态代码(optional)")
	private String  javaCode;
	
	@Column(name = "flock_config")
	@Schema(description = "flockConfig(optional)")
	private String  flockConfig;
	
	@Column(name = "attached")
	@Schema(description = "额外文件(optional)")
	private String  attached;
	
	@Column(name = "estimate_gpu_memory")
	@Schema(description = "预估模型占用显存(optional)")
	private Integer estimateGpuMemory;
	
	@Column(name = "estimate_count")
	@Schema(description = "预估模型份数(optional)")
	private Integer estimateCount;
	
	@Column(name = "batch_size")
	@Schema(description = "批量个数(optional)")
	private Integer batchSize;
	
	@Column(name = "affinity_group")
	@Schema(description = "亲和性组，相同组名的模型优先部署相同显卡(非强制)")
	private String  affinityGroup;
	
	@Column(name = "preferred_identity")
	@Schema(description = "希望模型部署在哪些pod上(非强制)")
	private String  preferredIdentity;
	
	@Column(name = "required_identity")
	@Schema(description = "部署在哪些pod上(强制)")
	private String  requiredIdentity;
	
	@Column(name = "rejected_identity")
	@Schema(description = "不可以部署在哪些pod上(强制)")
	private String  rejectedIdentity;
	
	@Column(name = "monopolized_group")
	@Schema(description = "相同group的模型独占一张卡")
	private String  monopolizedGroup;
	
	@Column(name = "monopolized_identity")
	@Schema(description = "独占信息")
	private String  monopolizedIdentity;
	
	@Column(name = "cpu_model_dup")
	@Schema(description= "大于零表示该能力是cpu且有该值个副本")
	private Integer cpuModelDup;
	
	@Column(name = "additional")
	@Schema(description= "一些可选的额外信息用来描述算法行为")
	private String  additional;
	
	@Column(name = "runtime")
	@Schema(description= "模型运行状况描述")
	private String runtime;

	@Column(name = "model_source")
	@Schema(description= "模型来源")
	private Integer modelSource;

	@Column(name = "gpu_model")
	@Schema(description= "是否gpu model")
	private Integer gpuModel;

	@Column(name = "gpu_mem_usage")
	@Schema(description= "gpu 显存")
	private Integer gpuMemUsage;

	@Column(name = "model_cn_name")
	@Schema(description= "模型中文名字")
	private String modelCnName;

	@Column(name = "model_type")
	@Schema(description= "模型类型")
	private Integer modelType;


	@Column(name = "version")
	@Schema(description= "模型版本")
	private String version;

	@Column(name = "commit_id")
	@Schema(description= "模型版本")
	private String commitId;

	@Column(name = "max_stream_count")
	@Schema(description= "pod流限制")
	private int maxStreamCount;

	@Column(name = "img_save_tag")
	@Schema(description= "是否存图 0 存图 1 不存图")
	private int imgSaveTag;

	@Column(name = "fmd_paths")
	@Schema(description= "插件路径,逗号分隔")
	private String  fmdPaths;

	@Column(name = "check_force")
	@Schema(description= "导入包是否强制校验")
	private int  checkForce;


	public static boolean compareAll(XDynamicModel thiz, XDynamicModel that) {
		return StringUtils.equals(thiz.pluginPaths,that.pluginPaths)
				&& StringUtils.equals(thiz.annotatorPaths, that.annotatorPaths)
				&& StringUtils.equals(thiz.javaClassName, that.javaClassName)
				&& StringUtils.equals(thiz.javaCode, that.javaCode)
				&& Objects.equals(thiz.batchSize, that.batchSize);
	}
	
	public boolean check() {
		boolean hasError = StringUtils.isBlank(annotatorName)
				|| StringUtils.isBlank(pluginPaths)
				|| StringUtils.isBlank(annotatorPaths);
		
		return !hasError;
	}
}
