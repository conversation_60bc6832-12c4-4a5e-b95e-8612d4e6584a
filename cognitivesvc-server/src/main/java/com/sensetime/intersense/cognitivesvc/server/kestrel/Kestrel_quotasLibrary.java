package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_quota_item_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.NativeLongByReference;
import java.nio.IntBuffer;

/**
 * JNA Wrapper for library <b>kestrel_quotas</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_quotasLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "quotas";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_quotasLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_quotasLibrary INSTANCE = (Kestrel_quotasLibrary)Native.load(Kestrel_quotasLibrary.JNA_LIBRARY_NAME, Kestrel_quotasLibrary.class);
	/** <i>native declaration : include/kestrel_quotas.h</i> */
	public static final int QUOTA_ITEM_NAME_LEN = (int)64;
	/**
	 * Original signature : <code>int kestrel_quota_init(const char*)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:25</i>
	 */
	int kestrel_quota_init(String product_name);
	/**
	 * Original signature : <code>int kestrel_quota_set_online_func(online_comm_t)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:28</i>
	 */
	int kestrel_quota_set_online_func(Pointer comm_function);
	/**
	 * Original signature : <code>void kestrel_quota_set_udid_getter(getter kestrel_udid_getter)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:31</i>
	 */
	void kestrel_quota_set_udid_getter(Pointer kestrel_udid_getter1);
	/**
	 * Original signature : <code>void kestrel_quota_reset_udid_getter()</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:34</i>
	 */
	void kestrel_quota_reset_udid_getter();
	/**
	 * Original signature : <code>void kestrel_quota_set_product_version_getter(getter kestrel_product_version_getter)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:37</i>
	 */
	void kestrel_quota_set_product_version_getter(Pointer kestrel_product_version_getter1);
	/**
	 * Original signature : <code>void kestrel_quota_reset_product_version_getter()</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:40</i>
	 */
	void kestrel_quota_reset_product_version_getter();
	/**
	 * Original signature : <code>int kestrel_quota_add_license(const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:43</i>
	 */
	int kestrel_quota_add_license(String license_str, String signed_code);
	/**
	 * Original signature : <code>int kestrel_quota_set_server(const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:46</i>
	 */
	int kestrel_quota_set_server(String master_url, String slave_url);
	/**
	 * Original signature : <code>void kestrel_quota_set_server_name_check(int32_t)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:49</i>
	 */
	void kestrel_quota_set_server_name_check(int check);
	/**
	 * Original signature : <code>int kestrel_quota_request(const kestrel_quota_item_t*, int, int)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:52</i>
	 */
	int kestrel_quota_request(kestrel_quota_item_t quotas_array, int quota_array_len, int async);
	/**
	 * Original signature : <code>int kestrel_quota_current(kestrel_quota_item_t**, int*, int*, unsigned long*)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:56</i>
	 */
	int kestrel_quota_current(kestrel_quota_item_t.ByReference quotas_array[], IntBuffer quota_array_len, IntBuffer free_active, NativeLongByReference time_stamp);
	/**
	 * Original signature : <code>int kestrel_quota_current(kestrel_quota_item_t**, int*, int*, unsigned long*)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:56</i>
	 */
	int kestrel_quota_current(kestrel_quota_item_t.ByReference quotas_array[], IntByReference quota_array_len, IntByReference free_active, NativeLongByReference time_stamp);
	/**
	 * Original signature : <code>void kestrel_quota_release(kestrel_quota_item_t**)</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:60</i>
	 */
	void kestrel_quota_release(kestrel_quota_item_t.ByReference quotas_array[]);
	/**
	 * Original signature : <code>int kestrel_quota_heartbeat()</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:63</i>
	 */
	int kestrel_quota_heartbeat();
	/**
	 * Original signature : <code>void kestrel_quota_deinit()</code><br>
	 * <i>native declaration : include/kestrel_quotas.h:66</i>
	 */
	void kestrel_quota_deinit();
}
