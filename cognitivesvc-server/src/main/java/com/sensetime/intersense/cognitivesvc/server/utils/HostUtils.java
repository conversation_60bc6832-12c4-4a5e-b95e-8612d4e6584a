package com.sensetime.intersense.cognitivesvc.server.utils;

import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.QueryWorkerResult;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;

@Slf4j
@Component
@EnableScheduling
public class HostUtils {
	public static volatile List<Integer> gpuRates = new ArrayList<Integer>();
	
	public static int getGpuRateLast5m() {
		if(!Initializer.isGpu())
			return 0;
		
		if(gpuRates.size() <= 0) 
			return 0;
		
		return gpuRates.stream().reduce(Integer::sum).get() / gpuRates.size();
	}
	
	public static final int getAvailableGpuMem() {
		if(!Initializer.isGpu())
			return -1;
		
		return getGpuMem()[0];
	}

	public static final int getAvailableNoUseGpuMem() {
		if(!Initializer.isGpu())
			return -1;

		return getGpuMem()[1] - getGpuMem()[0];
	}
	
	public static final int getTotalGpuMem() {
		if(!Initializer.isGpu())
			return -1;
		
		return getGpuMem()[1];
	}
	
	public static final float getGpuMemRate() {
		if(!Initializer.isGpu())
			return -1;
		
		int mem[] = getGpuMem();
		return ((float)mem[0]) / ((float)mem[1]);
	}
	
	public static final int[] getGpuMem() {
		int result[] = {-1, -1};
		
		String[] command = new String[] {"/bin/sh", "-c", "nvidia-smi --id=" + Initializer.deviceId + " | grep MiB | awk '{print $9,$11}';"};
		
		if(Initializer.isGpu())
			try {
				String text = runLinux(command);
		
				String[] mems = text.replaceAll("MiB", "").replaceAll("\n", "").split(" ");
				result = new int[]{Integer.parseInt(mems[0]), Integer.parseInt(mems[1])};

				log.info("getGpuMem run:{}", Arrays.toString(result));
			} catch (Exception e) {
				log.error("getGpuMem exception:{}", e.getMessage());
			}

		// obk. nvidia-smi 加日志
		log.info("getGpuMem nvidia-smi-log:{}", Arrays.toString(result));

		return result;
	}

	public static final String getGpuIsError() {
		String result = "OK";

		
		String[] command = new String[] {"/bin/sh", "-c", "nvidia-smi --id=" + Initializer.deviceId + " | grep MiB | awk '{print $5}';"};

		if(Initializer.isGpu())
			try {
				String text = runLinux(command);
				// 出现ERR 字样
				if(text.toUpperCase(Locale.ROOT).contains("ERR")) {
					log.error("nvidia error! ! !");
					return "ERR!";
				}
			} catch (Exception e) {
				log.error("getGpuMem exception:{}", e.getMessage());
			}

		log.info("getGpuIsError nvidia-smi-log:{}", result);

		return result;
	}
	
	public static final String runLinux(String[] command) {
		try {
			Process ps = Runtime.getRuntime().exec(command);
			try (InputStream in = ps.getInputStream()){
				return IOUtils.toString(in, Charset.defaultCharset());
			}
		} catch (Exception e) {
			e.printStackTrace();
			return StringUtils.EMPTY;
		}
	}
	
	private static String gpuUUID;
	private static String gpuUUIDFull;
	
	public static final String UUID() {
		if(gpuUUID != null) 
			return gpuUUID;
		
		if(!Initializer.isGpu())
			return QueryWorkerResult.NaN;
		
		String[] command = new String[] {"/bin/sh", "-c", "nvidia-smi -L;"};
		
		try {
			String text = runLinux(command);
			gpuUUID = Arrays.stream(text.split("\n"))
							.filter(gpu -> gpu.indexOf("GPU " + Initializer.deviceId) >= 0)
							.map(uuid -> {
								int index = uuid.indexOf("UUID:") + 10;
								return uuid.substring(index, index + 8);
							})
							.findAny()
							.get();

			gpuUUIDFull = Arrays.stream(text.split("\n"))
					.filter(gpu -> gpu.indexOf("GPU " + Initializer.deviceId) >= 0)
					.map(uuid -> {
						int index = uuid.indexOf("UUID:") + 10;
						return uuid.substring(index, index + 36);
					})
					.findAny()
					.get();
			
		} catch (Exception e) {
			e.printStackTrace();
		}

		log.info("nvidia uuid nvidia-smi-log:{}", gpuUUIDFull);

		return gpuUUID;
	}

	public static final String UUIDFull() {
		return gpuUUIDFull !=null ? gpuUUIDFull : gpuUUID;
	}


	@Scheduled(cron = "${intersense.cronGpuRate:0/30 * * * * ?}")
	public void calculateGpuRate() {
		try {
            if (!Initializer.isGpu())
                return;
            
            String[] command = new String[]{"/bin/sh", "-c", "nvidia-smi --id=" + Initializer.deviceId + " | grep % | awk '{print $13}';"};
            
            String text = runLinux(command);
            Integer gpu = Integer.parseInt(text.replaceAll("%", "").replaceAll("\n", ""));
            
            List<Integer> new_gpus = new ArrayList<Integer>(gpuRates);
            new_gpus.add(gpu);
            
            if (new_gpus.size() > 20)
                new_gpus.remove(0);
            
            gpuRates = new_gpus;
        } catch( Exception e){
		    log.error("calculateGpuRate error!",e);
        }

		log.info("calculateGpuRate  nvidia-smi-log:{}", gpuRates);
	}
}
