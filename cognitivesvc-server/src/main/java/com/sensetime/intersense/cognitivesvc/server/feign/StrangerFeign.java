package com.sensetime.intersense.cognitivesvc.server.feign;

import com.sensetime.lib.clientlib.response.BaseRes;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * Create by <PERSON> in 2018/7/17 下午4:01
 */
@FeignClient(value = "person", configuration = {})
public interface StrangerFeign {
    @RequestMapping(value = "/stranger/create", produces = MediaType.APPLICATION_JSON_VALUE)
    BaseRes<CreateRes> createStranger(@RequestBody CreateStrangerReq req);

    @RequestMapping(value = "/stranger/delete", produces = MediaType.APPLICATION_JSON_VALUE)
    BaseRes<String> deleteStranger(@RequestBody DeleteStrangerReq req);
    
    @Data
    public static class CreateStrangerReq{
        
        @NotBlank(message = "4001")
        @Schema(description = "头像路径", name = "imageURI", required = true)
        private String imageURI;

        @Schema(description = "设备类型", name = "deviceSource")
        private String deviceSource;

        @Schema(description = "抓拍时间", name = "captureTime")
        private String captureTime;
        
    	@Schema(description = "描述", name = "desc")
        private String desc;

        @Schema(description = "用户类型,stranger:1", name = "personType")
        private String personType;

        @Schema(description = "性别(男：0，女：1)", name = "personSex")
        private String personSex;

        @Schema(description = "年龄", name = "personAge")
        private String personAge;

        @Schema(description = "陌生人来源", name = "personTag")
        private String personTag;

        private String remark;
    }
    
    @Data
    public static class DeleteStrangerReq {

        private List<String> uuids;

        private String startTime;

        private String endTime;

        private String beforeDays;
    }

    @Data
    public static class CreateRes {
        private String uuid;
        private String privilege;
    }

}
