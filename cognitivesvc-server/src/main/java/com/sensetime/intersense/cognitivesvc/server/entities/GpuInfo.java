package com.sensetime.intersense.cognitivesvc.server.entities;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(title = "gpuInfo--T4/A2", description = "gpuInfo--T4/A2")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GpuInfo {
    private String identity;
    private String status;
    private String availableGpuMemory;
    private String totalGpuMemory;
    private String usedGpuMemory;
    private String usedGpuMemoryRate;
    private String version;
    private String deviceType;
    private List<String> runningPod;
}
