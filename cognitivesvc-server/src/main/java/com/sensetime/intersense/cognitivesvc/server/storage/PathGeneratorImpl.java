package com.sensetime.intersense.cognitivesvc.server.storage;

import com.sensetime.storage.service.IPathGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.ImageUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.UUID;


@Service
@Slf4j
public class PathGeneratorImpl implements IPathGenerator{


    // imagePath: /images/sensepass/20220222/10/234/
    // name: 20240105_2_24091224f6e74ce095adffde89f2f220.jpeg
    // 2 无意义 jpeg ==  jpg 改为 20240105_24091224f6e74ce095adffde89f2f220.jpg

    @Override
    public String generateRandomNameAndPath(String bucket_name, String extension) {
        //String dateDirectory = DateFormatUtils.format(new Date(), DateUtil.NO_TARGET_SHORT_FORMAT);
        String nameUUID = UUID.randomUUID().toString().replace("-", "");
        StringBuffer nameBuffer = new StringBuffer()
                .append(nameUUID);

        int num = Math.floorMod(nameUUID.hashCode(), 512);
        String imagePath = new StringBuffer().append("/").append("images").append("/")
                .append("cognitivesvc").append("/").append(bucket_name).append("/")
                .append(ImageUtils.dateFormatter.get().format(new Date())).append("/").append(num).toString();


        String name = nameBuffer.toString();
        String path = new StringBuffer().append(imagePath).append("/").append(name)
                .append(".").append(extension).toString();

        return path;
    }
}
