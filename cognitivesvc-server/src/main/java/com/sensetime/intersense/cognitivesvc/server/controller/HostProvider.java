package com.sensetime.intersense.cognitivesvc.server.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sensetime.intersense.cognitivesvc.server.CognitivesvcConfiguation;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveParam;
import com.sensetime.intersense.cognitivesvc.server.entities.GpuInfo;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.mapper.CognitiveParamRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.VideoStreamInfraRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.*;
import com.sensetime.lib.clientlib.response.BaseRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@RestController("hostProvider")
@RequestMapping(value = "/cognitive/", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "HostProvider", description = "host controller")
@Slf4j
public class HostProvider extends BaseProvider {

	@Autowired
	private CognitiveParamRepository mapper;
	@Autowired
	private VideoStreamInfraRepository deviceMapper;
	private CognitivesvcConfiguation cognitivesvcConfiguation;
	@Autowired
	private Utils.Sync sync;
	@Autowired
	private ExecutorService cogThreadPool;

	public HostProvider(CognitivesvcConfiguation cognitivesvcConfiguation) {
		this.cognitivesvcConfiguation = cognitivesvcConfiguation;
	}

	@Operation(summary = "设置参数", method = "POST")
	@RequestMapping(value = "/param/set", method = RequestMethod.POST)
	public BaseRes<Integer> set(@RequestParam String key, @RequestParam String value) throws Exception {
		CognitiveParam param = mapper.findById(key).get();
		param.setSValue(value);
		mapper.saveAndFlush(param);
		//如果是模式切换。流都要重启
		//新旧数据check
		if(key.equals("faceRecognitionMode")) {
			if(Utils.instance.scgFacePipeline){
				return BaseRes.success(1);
			}
			//立刻同步
			sync.sync();
			cogThreadPool.execute(() -> {
				try {
					cognitivesvcConfiguation.rebalanceChange(true, null);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}

			});
		}

		return BaseRes.success(1);
	}

    @Operation(summary = "更新pipeline", method = "POST")
    @RequestMapping(value = "/pipelineChange", method = RequestMethod.POST)
    public BaseRes<Integer> pipelineChange(@RequestBody Map<String, Object> param) {

		String deviceId = (String) param.getOrDefault("deviceId","");
		if(!deviceId.isBlank()) {
			System.out.println(param);
		}

        cognitivesvcConfiguation.rebalanceChange(true, Map.of("deviceId", deviceId));
        return BaseRes.success(1);
    }

    @Operation(summary = "获取参数", method = "GET")
    @RequestMapping(value = "/param/get", method = RequestMethod.GET)
    public BaseRes<Map<String, String>> get() {
        Map<String, String> params = mapper.findAll().stream()
                .collect(Collectors.toMap(CognitiveParam::getSKey, CognitiveParam::getSValue));
        return BaseRes.success(params);
    }

    @Operation(summary = "/host/gpu/memory/rate", method = "GET")
    @RequestMapping(value = "/host/gpu/memory/rate", method = RequestMethod.GET)
    public String gpuMemoryRate() throws Exception {
        return String.valueOf(HostUtils.getGpuMemRate());
    }

    @Operation(summary = "/host/gpu/memory/available", method = "GET")
    @RequestMapping(value = "/host/gpu/memory/available", method = RequestMethod.GET)
    public String gpuAvailMem() throws Exception {
        return String.valueOf(HostUtils.getAvailableGpuMem());
    }

    @Operation(summary = "/host/gpu/memory/total", method = "GET")
    @RequestMapping(value = "/host/gpu/memory/total", method = RequestMethod.GET)
    public String gpuTotalMem() throws Exception {
        return String.valueOf(HostUtils.getTotalGpuMem());
    }

    @Operation(summary = "/host/gpu/rate", method = "GET")
    @RequestMapping(value = "/host/gpu/rate", method = RequestMethod.GET)
    public String gpuRate() throws Exception {
        return String.valueOf(HostUtils.getGpuRateLast5m());
    }

    @Operation(summary = "/host/gpu/rate/now", method = "GET")
    @RequestMapping(value = "/host/gpu/rate/now", method = RequestMethod.GET)
    public String gpuRateNow() throws Exception {
        if (HostUtils.gpuRates.size() <= 0)
            return String.valueOf(0);
        else
            return String.valueOf(HostUtils.gpuRates.get(HostUtils.gpuRates.size() - 1));
    }

    @Operation(summary = "/host/gpu/uuid", method = "GET")
    @RequestMapping(value = "/host/gpu/uuid", method = RequestMethod.GET)
    public String uuid() throws Exception {
        return HostUtils.UUID();
    }

    @Operation(summary = "/host/gpu/getGpuIsError", method = "GET")
    @RequestMapping(value = "/host/gpu/getGpuIsError", method = RequestMethod.GET)
    public String getGpuIsError() throws Exception {
        return HostUtils.getGpuIsError();
    }

    @Operation(summary = "/host/gpu/info", method = "GET")
    @RequestMapping(value = "/host/gpu/info", method = RequestMethod.GET)
    public GpuInfo getGpuInfo() throws Exception {
        GpuInfo gpuInfo = new GpuInfo();
        gpuInfo.setStatus(HostUtils.getGpuIsError());
        gpuInfo.setIdentity(HostUtils.UUID());
        gpuInfo.setTotalGpuMemory(String.valueOf(HostUtils.getTotalGpuMem()));
        gpuInfo.setUsedGpuMemory(String.valueOf(HostUtils.getAvailableGpuMem()));
        gpuInfo.setAvailableGpuMemory(String.valueOf(HostUtils.getAvailableNoUseGpuMem()));
        gpuInfo.setUsedGpuMemoryRate(String.valueOf(HostUtils.getGpuMemRate()));
        gpuInfo.setDeviceType(Initializer.deviceType.toString() + Initializer.cudaType.toString());
        gpuInfo.setRunningPod(new ArrayList<>());
        return gpuInfo;
    }

    @Operation(summary = "保存图片", method = "POST")
    @RequestMapping(value = "/host/save/image/base64", method = RequestMethod.POST)
    public BaseRes<String> saveImageByBase64(@RequestBody @Payload SaveImage saveImage) throws Exception {
        File file = new File("/images/" + saveImage.getFileName() + ".jpg");
        if (file.exists())
            file.delete();

        byte[] data = ImageUtils.base64ToBytes(saveImage.getFigureImageBase64());

        try (FileOutputStream output = new FileOutputStream(file)) {
            output.write(data);
            output.flush();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return BaseRes.success();
    }


	@Operation(summary = "保存图片", method = "POST")
	@RequestMapping(value = "/host/test/save/image", method = RequestMethod.POST)
	public BaseRes<String> testsaveimage(@RequestParam String fileName, @RequestParam int count) throws Exception {
		String file = "/images/cognitivesvc/testImages/" + fileName + ".jpg";
		VideoStream.VideoFrame videoFrame = FrameUtils.decode_up_down_load_Buffered_frame_path(file);
		long start = System.currentTimeMillis();
		try{
			//String outputDir = "/images/cognitivesvc/testImages/output/" +  ;
			for (int i = 0; i < count; i++) {
				try{
					String outputfileName = UUID.randomUUID().toString().substring(1, 32);

					String type = fileName.split("/.")[0];

					File out = ImageUtils.newFileName("testImages/output/" + type  , outputfileName, "");
					KestrelApi.kestrel_frame_save(videoFrame.getFrame(), out.getAbsolutePath());

				}catch (Exception e){
					e.printStackTrace();
				}

			}
		}finally {
			if(videoFrame.getGpuFrame() != null) FrameUtils.batch_free_frame(videoFrame.getGpuFrame());
			if(videoFrame.getCpuFrame() != null) FrameUtils.batch_free_frame(videoFrame.getCpuFrame());
		}
		long end = System.currentTimeMillis();
		return BaseRes.success(String.valueOf(end - start));
	}
	@Operation(summary = "保存图片", method = "GET")
	@RequestMapping(value = "/host/test/save/image/preMkdir", method = RequestMethod.POST)
	public BaseRes<Boolean> preMkdir() throws Exception {
		List<String> path = new ArrayList<>();
		path.add("/testImages/output/");
		ImageUtils.mkdirsByHour(path,true);

		return BaseRes.success(true);
	}


	@Operation(summary = "保存图片", method = "POST")
	@RequestMapping(value = "/host/test/save/image/nobatch", method = RequestMethod.POST)
	public BaseRes<JSONObject> testsaveimage(@RequestParam String fileName) throws Exception {
		//String file = "/images/cognitivesvc/testImages/" + fileName + ".jpg";
		String file = fileName;
		VideoStream.VideoFrame videoFrame = FrameUtils.decode_up_down_load_Buffered_frame_path(file);
		long start = System.currentTimeMillis();
		JSONObject object = new JSONObject();
		String outPath = "";
		try{
			//String outputDir = "/images/cognitivesvc/testImages/output/" +  ;
			//for (int i = 0; i < count; i++) {
			try{
				String outputfileName = UUID.randomUUID().toString().substring(1, 32);

				String type = fileName.split("/.")[0];
				// todo 改掉
				File out = ImageUtils.newFile_returnFile("/testImages/output/" + type);

				//FrameUtils.save_image_as_jpg(videoFrame.getGpuFrame(), out);
				//KestrelApi.kestrel_frame_save(videoFrame.getFrame(), out.getAbsolutePath());
				outPath = out.getAbsolutePath();

				FrameUtils.encode_jpeg(videoFrame.getGpuFrame(), outPath);

			}catch (Exception e){
				e.printStackTrace();
			}

			//}
		}finally {
			if(videoFrame.getGpuFrame() != null) FrameUtils.batch_free_frame(videoFrame.getGpuFrame());
			if(videoFrame.getCpuFrame() != null) FrameUtils.batch_free_frame(videoFrame.getCpuFrame());
		}
		long end = System.currentTimeMillis();
		object.put("cost", String.valueOf(end - start));
		object.put("imgPath", outPath);
		return BaseRes.success(object);
	}

	@Operation(summary = "硬链接", method = "POST")
	@RequestMapping(value = "/host/test/link/image", method = RequestMethod.POST)
	public BaseRes<String> testLinkfile(@RequestParam String path, @RequestParam int startIndex, @RequestParam int count, @RequestParam String targetPath) throws Exception {
		long start = System.currentTimeMillis();
		File path1 = new File(targetPath);
		if(!path1.exists())
			path1.mkdirs();

		try{
			//String outputDir = "/images/cognitivesvc/testImages/output/" +  ;
			for (int i = 0; i < count; i++) {
				try{
					int fileIndex = (startIndex + i);
					String file = path + "/" + fileIndex + ".jpg";
					String outputfileName = targetPath + "/" + UUID.randomUUID().toString().substring(1, 32) + ".jpg";
					//File out = ImageUtils.newFileName("testImages/output/link", outputfileName);
					Files.createLink(Paths.get(outputfileName), Paths.get(file));
				}catch (Exception e){
					e.printStackTrace();
				}

			}
		}finally {
		}
		long end = System.currentTimeMillis();
		return BaseRes.success(String.valueOf(end - start));
	}

	@Operation(summary = "硬链接", method = "POST")
	@RequestMapping(value = "/host/test/link/image/nobatch", method = RequestMethod.POST)
	public BaseRes<String> testLinkfile(@RequestParam String path, @RequestParam String targetPath) throws Exception {
		long start = 0l;
		try{
			//String outputDir = "/images/cognitivesvc/testImages/output/" +  ;
			//for (int i = 0; i < count; i++) {
			try{
				//int fileIndex = (startIndex + i);
				String file = path;
				String outputfileName = UUID.randomUUID().toString().substring(1, 32);
				String outAllPath = "";
				if(StringUtils.isNotBlank(targetPath)){
					File path1 = new File(targetPath);
					if(!path1.exists())
						path1.mkdirs();
					outAllPath = targetPath + "/" + outputfileName + ".jpg";
				}else{
					File out = ImageUtils.newFileName("testImages/output/link", outputfileName,"");
					outAllPath = out.getAbsolutePath();
				}
				start = System.currentTimeMillis();
				Files.createLink(Paths.get(outAllPath),Paths.get(file));
			}catch (Exception e){
				e.printStackTrace();
			}

			//}
		}finally {
		}
		long end = System.currentTimeMillis();
		return BaseRes.success(String.valueOf(end - start));
	}

    @Getter
    public static class SaveImage {
        private String fileName;
        private String figureImageBase64;
    }
}
