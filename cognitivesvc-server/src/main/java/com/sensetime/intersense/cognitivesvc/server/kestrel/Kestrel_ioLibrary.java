package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_io_handler_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.DoubleByReference;
import com.sun.jna.ptr.FloatByReference;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;
import java.nio.ByteBuffer;

/**
 * JNA Wrapper for library <b>kestrel_io</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_ioLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_bson";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_ioLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_ioLibrary INSTANCE = (Kestrel_ioLibrary)Native.load(Kestrel_ioLibrary.JNA_LIBRARY_NAME, Kestrel_ioLibrary.class);
	/** <i>native declaration : include/kestrel_io.h</i> */
	public static final int KESTREL_IO_H = (int)1;
	/** <i>native declaration : include/kestrel_io.h</i> */
	public static final int KESTREL_IO_SEEK_CUR = (int)1;
	/** <i>native declaration : include/kestrel_io.h</i> */
	public static final int KESTREL_IO_SEEK_END = (int)2;
	/** <i>native declaration : include/kestrel_io.h</i> */
	public static final int KESTREL_IO_SEEK_SET = (int)0;
	/**
	 * Memory buffer IO handler, cfg should be a file path.<br>
	 * Original signature : <code>Pointer kestrel_io_create(kestrel_io_handler_t*, void*)</code><br>
	 * <i>native declaration : include/kestrel_io.h:41</i>
	 */
	Pointer kestrel_io_create(kestrel_io_handler_t io_handler, Pointer cfg);
	/**
	 * Original signature : <code>size_t kestrel_io_read(kestrel_io, uint8_t*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_io.h:44</i>
	 */
	long kestrel_io_read(Pointer io, ByteBuffer buf, long size);
	/**
	 * Original signature : <code>size_t kestrel_io_write(kestrel_io, const uint8_t*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_io.h:47</i>
	 */
	long kestrel_io_write(Pointer io, byte buf[], long size);
	/**
	 * Original signature : <code>int32_t kestrel_io_seek(kestrel_io, int64_t, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_io.h:50</i>
	 */
	int kestrel_io_seek(Pointer io, long offset, int whence);
	/**
	 * Original signature : <code>int64_t kestrel_io_tell(kestrel_io)</code><br>
	 * <i>native declaration : include/kestrel_io.h:53</i>
	 */
	long kestrel_io_tell(Pointer io);
	/**
	 * Original signature : <code>int32_t kestrel_io_flush(kestrel_io)</code><br>
	 * <i>native declaration : include/kestrel_io.h:56</i>
	 */
	int kestrel_io_flush(Pointer io);
	/**
	 * Original signature : <code>void kestrel_io_destroy(kestrel_io)</code><br>
	 * <i>native declaration : include/kestrel_io.h:59</i>
	 */
	void kestrel_io_destroy(Pointer io);
	/**
	 * encode bson helper functions<br>
	 * Original signature : <code>kestrel_bool kestrel_io_write_int32(kestrel_io, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_io.h:63</i>
	 */
	int kestrel_io_write_int32(Pointer io, int data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_read_int32(kestrel_io, int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_io.h:66</i>
	 */
	int kestrel_io_read_int32(Pointer io, IntByReference data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_write_int64(kestrel_io, int64_t)</code><br>
	 * <i>native declaration : include/kestrel_io.h:69</i>
	 */
	int kestrel_io_write_int64(Pointer io, long data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_read_int64(kestrel_io, int64_t*)</code><br>
	 * <i>native declaration : include/kestrel_io.h:72</i>
	 */
	int kestrel_io_read_int64(Pointer io, LongByReference data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_write_uint64(kestrel_io, uint64_t)</code><br>
	 * <i>native declaration : include/kestrel_io.h:75</i>
	 */
	int kestrel_io_write_uint64(Pointer io, long data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_read_uint64(kestrel_io, uint64_t*)</code><br>
	 * <i>native declaration : include/kestrel_io.h:78</i>
	 */
	int kestrel_io_read_uint64(Pointer io, LongByReference data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_write_float32(kestrel_io, float)</code><br>
	 * <i>native declaration : include/kestrel_io.h:81</i>
	 */
	int kestrel_io_write_float32(Pointer io, float data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_read_float32(kestrel_io, float*)</code><br>
	 * <i>native declaration : include/kestrel_io.h:84</i>
	 */
	int kestrel_io_read_float32(Pointer io, FloatByReference data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_write_float64(kestrel_io, double)</code><br>
	 * <i>native declaration : include/kestrel_io.h:87</i>
	 */
	int kestrel_io_write_float64(Pointer io, double data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_io_read_float64(kestrel_io, double*)</code><br>
	 * <i>native declaration : include/kestrel_io.h:90</i>
	 */
	int kestrel_io_read_float64(Pointer io, DoubleByReference data);
	/**
	 * @return `KESTREL_TRUE` if success else `KESTREL_FALSE`<br>
	 * Original signature : <code>kestrel_bool kestrel_io_write_string(kestrel_io, char*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_io.h:98</i>
	 */
	int kestrel_io_write_string(Pointer io, ByteBuffer str, long len);
	/**
	 * @return `KESTREL_TRUE` if success else `KESTREL_FALSE`<br>
	 * Original signature : <code>kestrel_bool kestrel_io_read_string(kestrel_io, char*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_io.h:106</i>
	 */
	int kestrel_io_read_string(Pointer io, ByteBuffer str, long len);
	/**
	 * @return `KESTREL_TRUE` if success else `KESTREL_FALSE`<br>
	 * Original signature : <code>kestrel_bool kestrel_io_write_cstring(kestrel_io, const char*)</code><br>
	 * <i>native declaration : include/kestrel_io.h:113</i>
	 */
	int kestrel_io_write_cstring(Pointer io, String str);
	/**
	 * @return `KESTREL_TRUE` if success else `KESTREL_FALSE`<br>
	 * Original signature : <code>kestrel_bool kestrel_io_read_cstring(kestrel_io, char**)</code><br>
	 * <i>native declaration : include/kestrel_io.h:121</i>
	 */
	int kestrel_io_read_cstring(Pointer io, PointerByReference str);
}
