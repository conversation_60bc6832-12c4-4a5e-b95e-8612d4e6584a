package com.sensetime.intersense.cognitivesvc.server.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class DeviceStatusEntity {

    private String eventName;
    private String eventAction;
    private List<DeviceStatus> data;


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class DeviceStatus{

        private String did;
        private String sts;
        private String lastChkTs;
        // 0 解析中，1 不在解析中
        private String processSts;
    }

}
