package com.sensetime.intersense.cognitivesvc.server.utils;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.Objects;
import java.util.function.Consumer;

import org.apache.commons.lang3.ArrayUtils;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_area2d_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;

public class KesonUtils {	
	/** 把一批帧变成输入数据 */
	public static Pointer frameToKeson(Pointer... frames) {
		return frameToKeson(frames, null, null, null);
	}
	
	/** 把一批帧变成输入数据 */
	public static Pointer frameToKeson(Pointer[] frames, Number[] imageIds) {
		return frameToKeson(frames, imageIds, null, null);
	}
	
	/** 把一批帧变成输入数据 */
	public static Pointer frameToKeson(Pointer[] frames, Memory[] area2ds, Consumer<Pointer> consumer) {
		for(int index = 0; index < area2ds.length && index < frames.length; index ++) {
			if(area2ds[index] != null)
				continue;
			
			area2ds[index] = new Memory(KestrelApi._SIZE_kestrel_area2d_t);
			kestrel_area2d_t area2d_t = new kestrel_area2d_t(area2ds[index]);
        	
            area2d_t.top = 0;
            area2d_t.left = 0;
            area2d_t.width = KestrelApi.kestrel_frame_video_width(frames[index]);
            area2d_t.height = KestrelApi.kestrel_frame_video_height(frames[index]);
            
            area2d_t.write();
		}
		
		return frameToKeson(frames, null, area2ds, consumer);
	}
	
	/** 把一批帧变成输入数据 */
	public static Pointer frameToKeson(Pointer[] frames, Number[] imageIds, Memory[] area2ds, Consumer<Pointer> consumer) {
		Pointer param_keson = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
		
		Pointer targets_array = KestrelApi.keson_create_array();
		KestrelApi.keson_add_item_to_object(param_keson, "targets", targets_array);
		
		for(int index = 0; index < frames.length; index ++) {
			Pointer target = KestrelApi.keson_create_object();
			KestrelApi.keson_add_item_to_array(targets_array, target);
			
			KestrelApi.keson_add_item_to_object(target, "id"      ,  KestrelApi.keson_create_int(index));			
			Number imageId = ArrayUtils.get(imageIds, index);
			if(imageId != null) {
				KestrelApi.keson_add_item_to_object(target, "uuid"      , KestrelApi.keson_create_string(imageId.toString()));
				KestrelApi.keson_add_item_to_object(target, "channel_id", KestrelApi.keson_create_int(imageId.longValue()));
				KestrelApi.keson_add_item_to_object(target, "source_id",  KestrelApi.keson_create_int(imageId.longValue()));
				KestrelApi.keson_add_item_to_object(target, "image_id",   KestrelApi.keson_create_int(imageId.longValue()));
			}else {
				KestrelApi.keson_add_item_to_object(target, "uuid"      , KestrelApi.keson_create_string(String.valueOf(index)));
				KestrelApi.keson_add_item_to_object(target, "channel_id", KestrelApi.keson_create_int(index));
				KestrelApi.keson_add_item_to_object(target, "source_id",  KestrelApi.keson_create_int(index));
				KestrelApi.keson_add_item_to_object(target, "image_id",   KestrelApi.keson_create_int(index));
			}
			
			Memory area2d = ArrayUtils.get(area2ds, index);
			if(area2d != null)
				KestrelApi.keson_add_item_to_object(target, "roi", KestrelApi.keson_create_ext_object(KestrelApi.KESON_AREA2D(), area2d));
			
			Pointer frame = ArrayUtils.get(frames, index);
			if(frame != null)
				KestrelApi.keson_add_item_to_object(target, "image"   , KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), frame));
			
			if(consumer != null)
				consumer.accept(target);
		}
		
		return param_keson;
	}

	public static Pointer frameToKesonTarget( Pointer[] targets,Consumer<Pointer> consumer) {
		Pointer param_keson = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));

		Pointer targets_array = KestrelApi.keson_create_array();
		KestrelApi.keson_add_item_to_object(param_keson, "targets", targets_array);

		for(int index = 0; index < targets.length; index ++) {

			Pointer target = ArrayUtils.get(targets, index);
			if(target != null) {
				KestrelApi.keson_add_item_to_array(targets_array, target);
			}

			if(consumer != null)
				consumer.accept(target);
		}

		return param_keson;
	}

	/** 把一批结果拆散到各个结果里 */
	public static PointerByReference[] splitKesonAndDestroy(PointerByReference keson, int origin_length) {
		if(origin_length <= 0) {
			KesonUtils.kesonDeepDelete(keson);
			return new PointerByReference[0];
		}
		
		return splitKesonAndDestroy(keson, new Number[origin_length]);
	}
	
	/** 把一批结果拆散到各个结果里 */
	public static PointerByReference[] splitKesonAndDestroy(PointerByReference keson, int origin_length, String keyName) {
		if(origin_length <= 0) {
			KesonUtils.kesonDeepDelete(keson);
			return new PointerByReference[0];
		}
		
		return splitKesonAndDestroy(keson, new Number[origin_length], keyName);
	}
	
	/** 把一批结果拆散到各个结果里 */
	public static PointerByReference[] splitKesonAndDestroy(PointerByReference keson, Number[] imageIds) {
		return splitKesonAndDestroy(keson, imageIds, "image_id");
	}
	
	/** 把一批结果拆散到各个结果里 */
	public static PointerByReference[] splitKesonAndDestroy(PointerByReference keson, Number[] imageIds, String keyName) {
		if(imageIds.length <= 0) {
			KesonUtils.kesonDeepDelete(keson);
			return new PointerByReference[0];
		}
		
		PointerByReference[] result = new PointerByReference[imageIds.length];
		if(keson == null || keson.getValue() == null)
			return result;
		
		Pointer targets = KestrelApi.keson_get_object_item(keson.getValue(), "targets");
		int arr_size = KestrelApi.keson_array_size(targets);	
		
		for(int index = arr_size - 1 ; index >= 0 ; index --) {
			Pointer target = KestrelApi.keson_detach_from_array(targets, index);
			Pointer imageIdKeson = KestrelApi.keson_get_object_item(target, keyName);
			if(imageIdKeson == null) {
				KesonUtils.kesonDeepDelete(target);
				continue;
			}
			
			int imageId = IntStream.iterate(0, i -> i + 1).limit(imageIds.length)
					.filter(i -> Objects.requireNonNullElse(imageIds[i], i).longValue() == KestrelApi.keson_get_int(imageIdKeson))
					.findAny().orElse(-1);
			
			if(imageId == -1){
				KesonUtils.kesonDeepDelete(target);
				continue;
			}
			
			if(result[imageId] == null) {
				result[imageId] = new PointerByReference();
				Pointer param_keson = KestrelApi.keson_create_object();
				KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
				Pointer targets_array = KestrelApi.keson_create_array();
				KestrelApi.keson_add_item_to_object(param_keson, "targets", targets_array);
				result[imageId].setValue(param_keson);
			}
			
			KestrelApi.keson_add_item_to_array(KestrelApi.keson_get_object_item(result[imageId].getValue(), "targets"), target);
		}

        KesonUtils.kesonDeepDelete(keson);
		return result;
	}
	
	/** 把一批结果拆散到各个结果里 */
	public static PointerByReference[] splitFlockKesonAndDestroy(PointerByReference keson, Pointer originFrames[]) {
		PointerByReference[] result = new PointerByReference[originFrames.length];
		if(keson == null || keson.getValue() == null)
			return result;
		 
		Pointer targets = KestrelApi.keson_get_object_item(keson.getValue(), "targets");
		int arr_size = KestrelApi.keson_array_size(targets);	
		
		for(int index = arr_size - 1 ; index >= 0 ; index --) {
			Pointer target = KestrelApi.keson_detach_from_array(targets, index);
			Pointer target_image = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(target, "image")).getValue();
			
			int imageId = IntStream.iterate(0, i -> i + 1).limit(originFrames.length)
					.filter(i -> KestrelApi.kestrel_frame_is_same_source(originFrames[i], target_image)).findAny().orElse(-1);
			
			if(imageId < 0) {//默认情况下 如果目标里面没有帧数据 就删除这个目标
				KesonUtils.kesonDeepDelete(target);
				continue;
			}

			if(result[imageId] == null) {
				result[imageId] = new PointerByReference();
				Pointer param_keson = KestrelApi.keson_create_object();
				KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
				Pointer targets_array = KestrelApi.keson_create_array();
				KestrelApi.keson_add_item_to_object(param_keson, "targets", targets_array);
				result[imageId].setValue(param_keson);
			}

			KestrelApi.keson_add_item_to_array(KestrelApi.keson_get_object_item(result[imageId].getValue(), "targets"), target);
		}

        KesonUtils.kesonDeepDelete(keson);
		return result;
	}
	/** 把一批结果拆散到各个结果里 */
	public static PointerByReference[] splitFlockKesonAndDestroyImage(PointerByReference keson, Pointer originFrames[]) {
		PointerByReference[] result = new PointerByReference[originFrames.length];
		if(keson == null || keson.getValue() == null)
			return result;

		Pointer targets = KestrelApi.keson_get_object_item(keson.getValue(), "targets");
		int arr_size = KestrelApi.keson_array_size(targets);

		for(int index = arr_size - 1 ; index >= 0 ; index --) {
			Pointer target = KestrelApi.keson_detach_from_array(targets, index);
			//Pointer target_image = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(target, "scene_frame")).getValue();

//			int imageId = IntStream.iterate(0, i -> i + 1).limit(originFrames.length)
//					.filter(i -> KestrelApi.kestrel_frame_is_same_source(originFrames[i], target_image)).findAny().orElse(-1);
//
//			if(imageId < 0) {//默认情况下 如果目标里面没有帧数据 就删除这个目标
//				KesonUtils.kesonDeepDelete(target);
//				continue;
//			}
			long dropped_track_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(target, "dropped_track_id"));
			if(dropped_track_id >=0 ){
				KesonUtils.kesonDeepDelete(target);
				continue;
			}
			int imageId = 0;

			if(result[imageId] == null) {
				result[imageId] = new PointerByReference();
				Pointer param_keson = KestrelApi.keson_create_object();
				KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
				Pointer targets_array = KestrelApi.keson_create_array();
				KestrelApi.keson_add_item_to_object(param_keson, "targets", targets_array);
				result[imageId].setValue(param_keson);
			}

			KestrelApi.keson_add_item_to_array(KestrelApi.keson_get_object_item(result[imageId].getValue(), "targets"), target);
		}

		KesonUtils.kesonDeepDelete(keson);
		return result;
	}

	/** 将所有目标属性都移动到数组第一个KESON*/
	public static PointerByReference mergeRenameAllToFirstKeson(PointerByReference... kesons) {
		if(ArrayUtils.isEmpty(kesons))
			return new PointerByReference();
		
		if(kesons.length == 1)
			return kesons[0];
		
        Pointer first_targets = KestrelApi.keson_get_object_item(kesons[0].getValue(), "targets");
        int first_targets_length = KestrelApi.keson_array_size(first_targets);
        
		for (int index = 1; index < kesons.length; index ++) {
			if(kesons[index] == null || kesons[index].getValue() == null)
				continue;
			
            Pointer other_targets = KestrelApi.keson_get_object_item(kesons[index].getValue(), "targets");
            int other_targets_length = KestrelApi.keson_array_size(other_targets);
            
            for(int jndex = 0; jndex < first_targets_length; jndex ++) {
            	Pointer first_target = KestrelApi.keson_get_array_item(first_targets, jndex);
            	long first_target_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(first_target, "id"));
            	long first_target_imageid = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(first_target, "image_id"));            	
            	
            	for(int kndex = 0; kndex < other_targets_length; kndex ++) {
            		Pointer other_target = KestrelApi.keson_get_array_item(other_targets, kndex);
                	long other_target_id = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(other_target, "id"));
                	long other_target_imageid = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(other_target, "image_id"));
                	if(first_target_id != other_target_id || first_target_imageid != other_target_imageid)
                		continue;
                	
                	List<String> childNames = new ArrayList<String>();
                	for(Pointer other_target_child = KestrelApi.keson_child(other_target); other_target_child != null; other_target_child = KestrelApi.keson_child_next(other_target_child))
                		childNames.add(KestrelApi.keson_key(other_target_child));
                	
                	for(String childName : childNames) {
                		Pointer attributeToMerge = KestrelApi.keson_detach_item_from_object(other_target, childName);
                		
                		Pointer item = KestrelApi.keson_get_object_item(first_target, childName);
            			if(item != null)
    						KestrelApi.keson_add_item_to_object(first_target, childName + "_" + index, attributeToMerge);
            			else
            				KestrelApi.keson_add_item_to_object(first_target, childName, attributeToMerge);
                	}
            		
            		break;
                }
            }

	    	KesonUtils.kesonDeepDelete(kesons[index]);
        }
		
		return kesons[0];
	}
	
	/** 将所有目标属性都移动到数组第一个KESON*/
	public static PointerByReference mergeRenameAllToFirstFlockKeson(PointerByReference... kesons) {
		if(ArrayUtils.isEmpty(kesons))
			return new PointerByReference();
		
		if(kesons.length == 1)
			return kesons[0];
		
        Pointer first_targets = KestrelApi.keson_get_object_item(kesons[0].getValue(), "targets");
        int first_targets_length = KestrelApi.keson_array_size(first_targets);
        
		for (int index = 1; index < kesons.length; index ++) {
			if(kesons[index] == null || kesons[index].getValue() == null)
				continue;
			
            Pointer other_targets = KestrelApi.keson_get_object_item(kesons[index].getValue(), "targets");
            int other_targets_length = KestrelApi.keson_array_size(other_targets);
            
            for(int jndex = 0; jndex < first_targets_length; jndex ++) {
            	Pointer first_target = KestrelApi.keson_get_array_item(first_targets, jndex);
            	Pointer first_target_image = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(first_target, "image")).getValue();
            	long first_target_imageid  = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(first_target, "image_id"));            	
            	
            	for(int kndex = 0; kndex < other_targets_length; kndex ++) {
            		Pointer other_target = KestrelApi.keson_get_array_item(other_targets, kndex);
            		Pointer other_target_image = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(other_target, "image")).getValue();
                	long other_target_imageid = KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(other_target, "image_id"));
                	if(!KestrelApi.kestrel_frame_is_same_source(first_target_image, other_target_image) || first_target_imageid != other_target_imageid)
                		continue;
                	
                	List<String> childNames = new ArrayList<String>();
                	for(Pointer other_target_child = KestrelApi.keson_child(other_target); other_target_child != null; other_target_child = KestrelApi.keson_child_next(other_target_child))
                		childNames.add(KestrelApi.keson_key(other_target_child));
                	
                	for(String childName : childNames) {
                		Pointer attributeToMerge = KestrelApi.keson_detach_item_from_object(other_target, childName);
                		
                		Pointer item = KestrelApi.keson_get_object_item(first_target, childName);
            			if(item != null)
    						KestrelApi.keson_add_item_to_object(first_target, childName + "_" + index, attributeToMerge);
            			else
            				KestrelApi.keson_add_item_to_object(first_target, childName, attributeToMerge);
                	}
            		
            		break;
                }
            }

	    	KesonUtils.kesonDeepDelete(kesons[index]);
        }
		
		return kesons[0];
	}
	
	public static void scaleAllTargetUp(Pointer keson, double rate) {
		if(Math.abs(rate) < 0.01)
			return ;
		
		Pointer targets = KestrelApi.keson_get_object_item(keson, "targets");
		int size = KestrelApi.keson_array_size(targets);
		
		Memory area2dMemory = new Memory(KestrelApi._SIZE_kestrel_area2d_t);
		for(int index = 0; index < size; index ++) {
			Pointer target = KestrelApi.keson_get_array_item(targets, index);
			
			Pointer roi = KestrelApi.keson_detach_item_from_object(target, "roi");
			if(roi == null) 
				continue;
			
			kestrel_area2d_t area2d = new kestrel_area2d_t();
			KestrelApi.keson_get_ext_data(roi, new PointerByReference(area2d.getPointer()));
			area2d.read();
			
            KesonUtils.kesonDeepDelete(roi);

            Integer width = null, height = null;
			Pointer image = KestrelApi.keson_get_object_item(target, "image");
			if(image != null) {
				PointerByReference imageData = new PointerByReference();
				KestrelApi.keson_get_ext_data(image, imageData);
				width  = KestrelApi.kestrel_frame_video_width(imageData.getValue());
				height = KestrelApi.kestrel_frame_video_height(imageData.getValue());
			}
			
            kestrel_area2d_t newArea2d_t = new kestrel_area2d_t(area2dMemory);
            newArea2d_t.left = (int)Math.max(0, area2d.left - area2d.width * rate);
            newArea2d_t.top = (int)Math.max(0, area2d.top - area2d.height * rate);
            newArea2d_t.width = (int)(area2d.width * (1 + rate));
            newArea2d_t.height = (int)(area2d.height * (1 + rate));
            
            if(width != null && newArea2d_t.left + newArea2d_t.width > width)
            	newArea2d_t.width = width - newArea2d_t.left;
            
            if(height != null && newArea2d_t.top + newArea2d_t.height > height)
            	newArea2d_t.height = height - newArea2d_t.top;
            
            newArea2d_t.write();
            
            KestrelApi.keson_add_item_to_object(target, "roi", KestrelApi.keson_create_ext_object(KestrelApi.KESON_AREA2D(), area2dMemory));
		}
	}

	public static String kesonToString(PointerByReference keson) {		
		return kesonToString(keson.getValue(), true);
	}
	
	public static String kesonToString(Pointer keson) {		
		return kesonToString(keson, true);
	}
	
	public static String kesonToString(PointerByReference keson, boolean toReplace) {		
		return kesonToString(keson.getValue(), toReplace);
	}
	
	public static String kesonToString(Pointer keson, boolean toReplace) {		
		return JSON.toJSONString(kesonToJson(keson, toReplace));
	}
	
	public static Object kesonToJson(Pointer keson) {
		return kesonToJson(keson, true);
	}
	
	public static Object kesonToJson(PointerByReference keson) {
		return kesonToJson(keson.getValue(), true);
	}
	
	public static <T> T kesonToType(Pointer keson, Class<T> clazz) {
		return JSON.parseObject(kesonToString(keson, true), clazz);
	}
	
	public static <T> T kesonToType(PointerByReference keson, Class<T> clazz) {
		return kesonToType(keson, clazz);
	}
	
	public static PointerByReference[] tryReformFlockKeson(PointerByReference... kesons) {
		for(PointerByReference keson : kesons) {
			int type = KestrelApi.keson_type(keson.getValue());
			if(type != KestrelApi.KESTREL_BSON_ARRAY) 
				continue ;
			
			Pointer targets = KestrelApi.keson_create_array();
			int arraySize = KestrelApi.keson_array_size(keson.getValue());
			
			for(int index = 0; index < arraySize; index ++) {
				Pointer oldTargets = KestrelApi.keson_get_object_item(KestrelApi.keson_get_array_item(keson.getValue(), index), "targets");
				int targetSize = KestrelApi.keson_array_size(oldTargets);
				
				for(int  jndex = targetSize - 1; jndex >= 0; jndex --) {
					Pointer target = KestrelApi.keson_add_item_to_object(KestrelApi.keson_detach_from_array(oldTargets, jndex), "flock_index", KestrelApi.keson_create_int(index));
					KestrelApi.keson_add_item_to_array(targets, target);
				}
			}
			
			kesonDeepDelete(keson);

			keson.setValue(KestrelApi.keson_add_item_to_object(KestrelApi.keson_create_object(), "targets", targets));
		}
		
		return kesons;
	}
	
	public static Memory roiMapToMemory(Map<String, Number> roi) {
		Memory area2dMemory = new Memory(KestrelApi._SIZE_kestrel_area2d_t);
		kestrel_area2d_t newArea2d_t = new kestrel_area2d_t(area2dMemory);
        newArea2d_t.left = roi.get("left").intValue();
        newArea2d_t.top = roi.get("top").intValue();
        newArea2d_t.width = roi.get("width").intValue();
        newArea2d_t.height = roi.get("height").intValue();
        newArea2d_t.write();
        
        return area2dMemory;
	}
	
	public static Pointer roiMapToKeson(Map<String, Number> roi) {
		Memory area2dMemory = new Memory(KestrelApi._SIZE_kestrel_area2d_t);
		kestrel_area2d_t newArea2d_t = new kestrel_area2d_t(area2dMemory);
        newArea2d_t.left = roi.get("left").intValue();
        newArea2d_t.top = roi.get("top").intValue();
        newArea2d_t.width = roi.get("width").intValue();
        newArea2d_t.height = roi.get("height").intValue();
        newArea2d_t.write();
        
        return KestrelApi.keson_create_ext_object(KestrelApi.KESON_AREA2D(), area2dMemory);
	}
	
	public static Pointer buildFlockRemoveInput(long contextId, String... streams) {
		Pointer input = KestrelApi.keson_create_object();
		
		KestrelApi.keson_add_item_to_object(input, "source_id", KestrelApi.keson_create_int(contextId));
		KestrelApi.keson_add_item_to_object(input, "context_id", KestrelApi.keson_create_int(contextId));
		
		Pointer array = KestrelApi.keson_create_array();
		KestrelApi.keson_add_item_to_object(input, "streams", array);
		
		for(String stream : streams) {
			Pointer item = KestrelApi.keson_create_object();
			KestrelApi.keson_add_item_to_array(array, item);
			
			KestrelApi.keson_add_item_to_object(item, "name", KestrelApi.keson_create_string(stream));
			
			KestrelApi.keson_add_item_to_object(item, "source_id", KestrelApi.keson_create_int(contextId));
			KestrelApi.keson_add_item_to_object(item, "context_id", KestrelApi.keson_create_int(contextId));
		}
		
		return input;
	}
	
	public static PointerByReference stringToKeson(String keson) {
		return new PointerByReference(KestrelApi.keson_parse(keson));
	}
	
	public static void kesonDeepDelete(Pointer keson) {
		PointerByReference tub = new PointerByReference();
		if(keson != null) {
			tub.setValue(keson);
			KestrelApi.keson_deep_delete(tub);
		}
	}
	
	public static void kesonDeepDelete(PointerByReference keson) {
		if(keson != null) {
			KestrelApi.keson_deep_delete(keson);
			keson.setValue(null);
		}
	}
	
	public static void kesonDeepDelete(Pointer... kesons) {
		PointerByReference tub = new PointerByReference();
		for(Pointer keson : kesons) {
			if(keson != null) {
				tub.setValue(keson);
				KestrelApi.keson_deep_delete(tub);
			}
		}
	}
	
	public static void kesonDeepDelete(PointerByReference... kesons) {
		for(PointerByReference keson : kesons) {
			if(keson != null) {
				KestrelApi.keson_deep_delete(keson);
				keson.setValue(null);
			}
		}
	}
	
	public static boolean compareBsonTargetImageSame(Pointer target1, Pointer target2) {
		Pointer image1 = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(target1, "image")).getValue();
		Pointer image2 = KestrelApi.keson_get_ext_data(KestrelApi.keson_get_object_item(target2, "image")).getValue();
		
		return KestrelApi.kestrel_frame_is_same_source(image1, image2);
	}
	
	public static Pointer createCraneInputKeson(Pointer[] befores, Pointer[] afters) {
		if(befores.length != afters.length)
			throw new RuntimeException("befores and afters length is not equal.");
		
        Pointer param_keson = KestrelApi.keson_create_object();
        KestrelApi.keson_add_item_to_object(param_keson, "id", KestrelApi.keson_create_int(0));
        
        Pointer targets_array = KestrelApi.keson_create_array();
        KestrelApi.keson_add_item_to_object(param_keson, "targets", targets_array);

        Memory area2dMemory = new Memory(KestrelApi._SIZE_kestrel_area2d_t);
        kestrel_area2d_t area2d_t = new kestrel_area2d_t(area2dMemory);
        
        for(int index = 0; index < befores.length; index ++) {
            Pointer target = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_array(targets_array, target);

            KestrelApi.keson_add_item_to_object(target, "id", KestrelApi.keson_create_int(index));
            KestrelApi.keson_add_item_to_object(target, "image_id", KestrelApi.keson_create_int(index));
            
            Pointer before = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_object(before, "id", KestrelApi.keson_create_int(0));
            KestrelApi.keson_add_item_to_object(target, "before", before);
            
            Pointer after = KestrelApi.keson_create_object();
            KestrelApi.keson_add_item_to_object(after, "id", KestrelApi.keson_create_int(1));
            KestrelApi.keson_add_item_to_object(target, "after", after);

            Pointer src = befores[index];  
            Pointer dst = afters[index];   
            
            area2d_t.width = KestrelApi.kestrel_frame_video_width(src);
            area2d_t.height = KestrelApi.kestrel_frame_video_height(src);
            area2d_t.write();
            
            KestrelApi.keson_add_item_to_object(before, "roi", KestrelApi.keson_create_ext_object(KestrelApi.KESON_AREA2D(), area2dMemory));
            KestrelApi.keson_add_item_to_object(after,  "roi", KestrelApi.keson_create_ext_object(KestrelApi.KESON_AREA2D(), area2dMemory));

            KestrelApi.keson_add_item_to_object(before, "image" , KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), src));
            KestrelApi.keson_add_item_to_object(after,  "image" , KestrelApi.keson_create_ext_object(KestrelApi.KESON_FRAME(), dst));
        }
        
        return param_keson;
    }

	public static Object kesonToJson(Pointer keson, boolean toReplace) {
		if(keson == null)
			return null;
		
		PointerByReference data = new PointerByReference();
		LongByReference len = new LongByReference();
		
		KestrelApi.keson_encode_to_data(keson, data, len, KestrelApi.KESTREL_BSON_FMT_JSON_CANONICAL);
		
		String rawString = new String(data.getValue().getByteArray(0, (int)len.getValue()));
		Native.free(Pointer.nativeValue(data.getValue()));
		
		if(toReplace)	
			return replaceBson(JSON.parse(rawString));
		else
			return JSON.parse(rawString);
	}
	
	@SuppressWarnings({ "unchecked"})
	private static Object replaceBson(Object json) {
		if(json instanceof Map) {
			Map<String, Object> jsonObject = (Map<String, Object>)json;
			if(jsonObject.size() <= 1) {
				if(jsonObject.containsKey("$numberInt"))
					return Integer.parseInt(jsonObject.get("$numberInt").toString());
				else if(jsonObject.containsKey("$numberLong"))
					return Long.parseLong(jsonObject.get("$numberLong").toString());
				else if(jsonObject.containsKey("$numberDouble"))
					return Double.parseDouble(jsonObject.get("$numberDouble").toString());
			}
		
			for(Entry<String, Object> entry : jsonObject.entrySet())
				replaceBson(entry);
		}else if(json instanceof List) {
			List<Object> jsonArray = (List<Object>)json;
			
			for(int index = 0 ;index < jsonArray.size(); index ++)
				jsonArray.set(index, replaceBson(jsonArray.get(index)));
		}
		
		return json;
	}
	
	@SuppressWarnings({ "unchecked" })
	private static void replaceBson(Entry<String, Object> entry) {
		if(entry.getValue() instanceof Map) {
			if(replaceNumber(entry))
				return;

			Map<String, Object> value = ((Map<String, Object>)entry.getValue());
			String subType = (String)((Map<String, Object>)value.getOrDefault("$binary", Map.of())).get("subType");
			
			if("81".equals(subType)) {
				replaceFeatureInt(entry);
				return ;
			}else if("85".equals(subType)) {
				replaceFeatureFloat(entry);
				return ;
			}else if("88".equals(subType) || "89".equals(subType)) {
				replacePoint(entry);
				return ;
			}else if("84".equals(subType)) {
				replaceImage(entry);
				return ;
			}else if("8b".equals(subType)) {
				replaceRoi(entry);
				return ;
			}else if("8c".equals(subType)) {
				replaceKeyPoints(entry);
				return ;
			}else {
				entry.setValue(replaceBson(entry.getValue()));
			}
		}else if(entry.getValue() instanceof List) {
			List<Object> jsonArray = (List<Object>)entry.getValue();
			
			for(int index = 0 ;index < jsonArray.size(); index ++)
				jsonArray.set(index, replaceBson(jsonArray.get(index)));
		}
	}

	@SuppressWarnings("unchecked")
	private static boolean replaceNumber(Entry<String, Object> jsonEntry) {
		if(!(jsonEntry.getValue() instanceof Map))
			return false;
		
		Number value = ((Map<String, Object>)jsonEntry.getValue())
			.entrySet().stream()
			.map(e -> {
				if("$numberInt".equals(e.getKey())) {
					return Integer.parseInt(e.getValue().toString());
				}else if("$numberLong".equals(e.getKey())) {
					return Long.parseLong(e.getValue().toString());
				}else if("$numberDouble".equals(e.getKey())) {
					return Double.parseDouble(e.getValue().toString());
				}else
					return (Number)null;
			})
			.filter(Objects::nonNull)
			.findAny()
			.orElse(null);
		
		if(value != null) {
			jsonEntry.setValue(value);
			return true;
		}else {
			return false;
		}
	}
	
	@SuppressWarnings("unchecked")
	private static void replaceFeatureInt(Entry<String, Object> jsonEntry) {
		Map<String, Object> value = ((Map<String, Object>)jsonEntry.getValue());
		Map<String, Map<String, String>> readable = (Map<String, Map<String, String>>)((Map<String, Object>)value.getOrDefault("$binary", Map.of())).get("$readable");
		
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("dims",    Integer.parseInt(readable.get("dims").get("$numberInt")));
		result.put("version", Integer.parseInt(readable.get("version").get("$numberInt")));
		jsonEntry.setValue(result);
	}
	
	@SuppressWarnings("unchecked")
	private static void replaceFeatureFloat(Entry<String, Object> jsonEntry) {
		Map<String, Object> value = ((Map<String, Object>)jsonEntry.getValue());
		Map<String, Map<String, String>> readable = (Map<String, Map<String, String>>)((Map<String, Object>)value.getOrDefault("$binary", Map.of())).get("$readable");

		Map<String, Object> result = new HashMap<String, Object>();
		result.put("dims",     ((List<Map<String, Object>>)readable.get("dims")).stream().map(item -> item.get("$numberLong")).collect(Collectors.toList()));
		result.put("name",     readable.get("name"));
		result.put("elemType", readable.get("elemType"));
		jsonEntry.setValue(result);
	}
	
	
	@SuppressWarnings("unchecked")
	private static void replacePoint(Entry<String, Object> jsonEntry) {
		Map<String, Object> value = ((Map<String, Object>)jsonEntry.getValue());
		Map<String, Map<String, String>> readable = (Map<String, Map<String, String>>)((Map<String, Object>)value.getOrDefault("$binary", Map.of())).get("$readable");
		
		Map<String, String> x = readable.get("x");
		Map<String, String> y = readable.get("y");
		
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("x", Float.parseFloat(x.getOrDefault("$numberInt", x.getOrDefault("$numberDouble", "-1"))));
		result.put("y", Float.parseFloat(y.getOrDefault("$numberInt", y.getOrDefault("$numberDouble", "-1"))));
		jsonEntry.setValue(result);
	}
	
	@SuppressWarnings("unchecked")
	private static void replaceImage(Entry<String, Object> jsonEntry) {
		try {
			Map<String, Object> value = ((Map<String, Object>) jsonEntry.getValue());
			Map<String, Object> readable = (Map<String, Object>) ((Map<String, Object>) value.getOrDefault("$binary", Map.of())).get("$readable");

			Map<String, Object> result = new HashMap<String, Object>();
			result.put("type", readable.get("memoryType"));
			result.put("format", readable.get("format"));
			result.put("media", readable.get("media"));
			result.put("pts", ((Map<String, Number>) readable.get("PTS")).get("$numberLong"));
			result.put("width", ((Map<String, Number>) readable.get("width")).get("$numberLong"));
			result.put("height", ((Map<String, Number>) readable.get("height")).get("$numberLong"));
			jsonEntry.setValue(result);
		}catch (Exception e){
			e.printStackTrace();
		}
	}
	
	@SuppressWarnings("unchecked")
	private static void replaceKeyPoints(Entry<String, Object> jsonEntry) {
		Map<String, Object> value = ((Map<String, Object>)jsonEntry.getValue());
		Map<String, Map<String, String>> readable = (Map<String, Map<String, String>>)((Map<String, Object>)value.getOrDefault("$binary", Map.of())).get("$readable");
		
		Map<String, Number> result = new HashMap<String, Number>();
		result.put("elementSize" , Integer.parseInt(readable.get("elementSize").get("$numberLong")));
		result.put("elementCount", Integer.parseInt(readable.get("elementCount").get("$numberLong")));
		result.put("elementType" , Integer.parseInt(readable.get("elementType").get("$numberInt")));
		jsonEntry.setValue(result);
	}

	@SuppressWarnings("unchecked")
	private static void replaceRoi(Entry<String, Object> jsonEntry) {
		Map<String, Object> value = ((Map<String, Object>)jsonEntry.getValue());
		Map<String, Map<String, String>> readable = (Map<String, Map<String, String>>)((Map<String, Object>)value.getOrDefault("$binary", Map.of())).get("$readable");
		
		Map<String, Number> result = new HashMap<String, Number>();
		result.put("top"   , Integer.parseInt(readable.get("top").get("$numberInt")));
		result.put("left"  , Integer.parseInt(readable.get("left").get("$numberInt")));
		result.put("width" , Integer.parseInt(readable.get("width").get("$numberInt")));
		result.put("height", Integer.parseInt(readable.get("height").get("$numberInt")));
		jsonEntry.setValue(result);
	}
	
	public static interface Kesonable {

		public int getId();
		
		public int getImageId();
		
		@SuppressWarnings("unchecked")
		public static <T extends Kesonable> T[][] rearranged(T[] array, int originSize){
			T[][] result = (T[][])Array.newInstance(array.getClass().getComponentType(), originSize, 0);
			
			if(array == null || array.length <= 0)
				return result;
			
			for(int index = 0; index < result.length; index ++) {
				int position = index;
				int count = (int)Arrays.stream(array).filter(item -> item.getImageId() == position).count();
				result[index] = (T[])Array.newInstance(array[0].getClass(), count);
				
				for(int jndex = 0; jndex < count; jndex ++) {
					for(int kndex = 0; kndex < array.length; kndex ++) {
						if(array[kndex].getImageId() == index && array[kndex].getId() == jndex) {
							result[index][jndex] = array[kndex];
							break;
						}
					}
				}
			}
			
			return result;
		}
	}
}
