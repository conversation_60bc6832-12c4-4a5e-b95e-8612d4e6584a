package com.sensetime.intersense.cognitivesvc.server.entities;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;

@Entity
@Table(name = "${db.video_stream_pedestrian.table.name:video_stream_pedestrian}")
@Accessors(chain = true)
@Schema(title = "直接视频流设备", description = "直接视频流设备")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamPedestrian {

    @Id
    @Column(name = "device_id")
    @Schema(description = "设备id")
    @Getter
    @Setter
    private String deviceId;

    @Column(name = "quality_threshold")
    @Schema(description = "人脸人体追踪质量阈值")
    @Getter
    @Setter
    private Float qualityThreshold;

    @Column(name = "ped_quality_threshold")
    @Schema(description = "人脸人体追踪质量阈值")
    @Getter
    @Setter
    private Float pedQualityThreshold;

    @Column(name = "run_face_attr_model", columnDefinition = "int DEFAULT 0")
    @Schema(description = "是否输出人脸属性")
    @Getter
    @Setter
    private Integer runFaceAttrModel;


    @Column(name = "run_ped_attr_model", columnDefinition = "int DEFAULT 0")
    @Schema(description = "是否输出人体属性")
    @Getter
    @Setter
    private Integer runPedAttrModel;

    @Column(name = "face_feature_threshold")
    @Schema(description = "人脸特征比对阈值")
    @Getter
    @Setter
    private Float faceFeatureThreshold;

    @Column(name = "body_feature_threshold")
    @Schema(description = "人体特征比对阈值")
    @Getter
    @Setter
    private Float bodyFeatureThreshold;

    @Column(name = "seek_num")
    @Schema(description = "搜索数量")
    @Getter
    @Setter
    private Integer seekNum;

    @Column(name = "refinement")
    @Schema(description = "是否全追踪特征融合")
    @Getter
    @Setter
    private Boolean refinement;

    @Column(name = "yaw")
    @Schema(description = "yaw")
    @Getter
    @Setter
    private Float yaw;

    @Column(name = "pitch")
    @Schema(description = "pitch")
    @Getter
    @Setter
    private Float pitch;

    @Column(name = "roll")
    @Schema(description = "roll")
    @Getter
    @Setter
    private Float roll;

    @Column(name = "min_face_size")
    @Schema(description = "最小人脸size")
    @Getter
    @Setter
    private Integer minFaceSize;

    @Column(name = "min_body_size")
    @Schema(description = "最小人体size")
    @Getter
    @Setter
    private Integer minBodySize;

    @Column(name = "store_scene")
    @Schema(description = "是否存大图")
    @Getter
    @Setter
    private Boolean storeScene;

    @Column(name = "store_target")
    @Schema(description = "是否大小图")
    @Getter
    @Setter
    private Boolean storeTarget;

    @Column(name = "store_passer")
    @Schema(description = "是否存陌生人")
    @Getter
    @Setter
    private Boolean storePasser;

    @Column(name = "target_group")
    @Schema(description = "目标组")
    @Getter
    @Setter
    private String targetGroup;

    @Column(name = "roi")
    @Schema(description = "热区")
    @Getter
    @Setter
    private String roi;

    @Column(name = "roi_ids")
    @Schema(description = "热区的外部id")
    @Getter
    @Setter
    private String roiIds;

    @Column(name = "quick_response_time")
    @Schema(description = "快速人脸识别响应时间")
    @Getter
    @Setter
    private Integer quickResponseTime;

    @Column(name = "time_interval")
    @Schema(description = "选帧触发的时间间隔")
    @Getter
    @Setter
    private Integer timeInterval;

    @Column(name = "max_track_time")
    @Schema(description = "目标最大的跟踪时长")
    @Getter
    @Setter
    private Integer maxTrackTime;

    @Column(name = "max_tracklet_num")
    @Schema(description = "追踪目标上限(影响性能)")
    @Getter
    @Setter
    private Integer maxTrackletNum;

    @Column(name = "processors")
    @Schema(description = "人体扩展")
    @Getter
    @Setter
    private String  processors;

	@Transient
    @JsonIgnore
    @Schema(hidden = true)
    private Polygon[] polygons;
    
    public Polygon[] queryPolygons() {
    	try {
	    	if(polygons == null) {
	    		if(StringUtils.isBlank(roi)) {
	    			polygons = new Polygon[0];
	    		}else {
		    		int[][][] rois = JSON.parseObject(roi, int[][][].class);
		    		Polygon[] polygons = new Polygon[rois.length];
		    		
		    		for(int index = 0 ; index < rois.length; index ++) {
		    			int pointNum = rois[index].length;
		    			int[] xs = new int[pointNum];
		    			int[] ys = new int[pointNum];
		    			for(int jndex = 0 ; jndex < rois[index].length; jndex ++) {
		    				xs[jndex] = rois[index][jndex][0];
		    				ys[jndex] = rois[index][jndex][1];
		    			}
		    			
		    			polygons[index] = new Polygon(xs, ys, pointNum);
		    		}
		    		
		    		this.polygons = polygons;
	    		}
	    	}
    	}catch(Exception e) {
    		e.printStackTrace();
    		polygons = new Polygon[0];
    	}
    	
    	return polygons;
    }

	public Integer getGlobalMaxTrackTime(){

		//精准模式才有用
		if(Utils.instance.maxTrackingTime > 0 && Utils.instance.faceRecognitionMode == 0){
			return Utils.instance.maxTrackingTime;
		}
		return -1;
	}
	public Integer getSelectFrameTimeInterVal(){

		//精准模式才有用
		if(Utils.instance.selectFrameTimeInterVal > 0 && Utils.instance.faceRecognitionMode == 0){
			return Utils.instance.selectFrameTimeInterVal;
		}
		return -1;
	}

    public VideoStreamFace.Processor getProcessFace(){
        if(this.processors == null){
            return new VideoStreamFace.Processor();
        }
        if(this.processors.isEmpty()){
            return new VideoStreamFace.Processor();
        }
        return JSON.parseObject(this.processors, VideoStreamFace.Processor.class);
    }

    @Accessors(chain = true)
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static final class Processor {
        public static final VideoStreamFace.Processor empty = new VideoStreamFace.Processor();

        @Getter @Setter
        private Integer eventTrackTime = -1;//-1持续告警>0的值 实际检测时长(单位：秒)

        @Getter @Setter
        private Integer strangerPolicy = 0;//是否是陌生人策略 0 陌生人策略，1 非陌生人策略

        @Getter @Setter
        private Float largeDetectThresh;

        @Getter @Setter
        private Float smallDetectThresh;


    }
}
