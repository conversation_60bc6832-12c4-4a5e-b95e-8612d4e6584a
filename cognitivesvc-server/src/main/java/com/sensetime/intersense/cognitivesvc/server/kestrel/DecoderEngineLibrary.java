package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.*;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;

import java.util.Arrays;
import java.util.List;

public interface DecoderEngineLibrary extends Library {
    String JNA_LIBRARY_NAME = "decoderEngine";
    NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(DecoderEngineLibrary.JNA_LIBRARY_NAME);
    DecoderEngineLibrary INSTANCE = (DecoderEngineLibrary) Native.load(JNA_LIBRARY_NAME, DecoderEngineLibrary.class);

    void DecoderHello();

    class DecoderResult extends Structure {
        public Pointer demuxerCtx;
        public Pointer decodeCtx;
        public Pointer fp;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("demuxerCtx", "decodeCtx", "fp");
        }
    }
    class DecoderResult3 extends Structure {
        public Pointer demuxerCtx;
        public Pointer decoderCtx;

        @Override
        protected List<String> getFieldOrder() {
            return List.of("demuxerCtx", "decoderCtx");
        }
    }
    Pointer CreateDecoder(byte[] videoFilePath, int length);

    Pointer CreateFramePool(byte[] filePath, int length);

    Pointer CreateCodec(byte[] videoFilePath, int length, Pointer demuxerCtx);


    Pointer CreateDecoderRev( byte[] videoFilePath, int length);

    void ReleaseDecoder(byte[] videoFilePath, int length);

    Pointer PullFrame(Pointer videoFilePath, int length,IntByReference errCode);

    Pointer PullFrame2(Pointer demuxerCtx, Pointer decodeCtx);

    Pointer CreateDemuxer(byte[] videoFilePath, int length);

    void CreateConstruct(byte[] videoFilePath, int length, String format,int nvBuffer,int buffer, String extra);

    Pointer CreateDecoder2(Pointer demuxer);

    DecoderResult3 CreateDecoder3(String videoFilePath);

    void ReleaseFrame(Pointer demuxer);

    public class GoString extends Structure {
        public static class ByValue extends GoString implements Structure.ByValue {}
        public String p;
        public long n;
        protected List getFieldOrder(){
            return Arrays.asList(new String[]{"p","n"});
        }

    }
}


//public class Main {
//    public static void main(String[] args) {
//        try {
//            String videoFilePath = "rtsp://10.151.5.1:8554/performance/crossLineBody1";
//            DecoderEngineLibrary.DecoderResult decoderResult = DecoderEngineLibrary.INSTANCE.CreateDecoder(videoFilePath);
//            System.out.println("Decoder created successfully with demuxerCtx: " + decoderResult.demuxerCtx);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//}
