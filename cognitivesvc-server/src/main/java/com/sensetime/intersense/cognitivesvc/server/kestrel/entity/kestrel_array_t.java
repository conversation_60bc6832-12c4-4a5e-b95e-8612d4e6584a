package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;

import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;

/**
 * <i>native declaration : include/kestrel_array.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_array_t extends Structure {
	public byte keson_code;
	public long element_count;
	public long element_size;
	/** C type : uint8_t* */
	public Pointer data;
	/** C type : Pointer */
	public Pointer buffer;
	public kestrel_array_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("keson_code", "element_count", "element_size", "data", "buffer");
	}
	/**
	 * @param data C type : uint8_t*<br>
	 * @param buffer C type : kestrel_buffer
	 */
	public kestrel_array_t(byte keson_code, long element_count, long element_size, Pointer data, Pointer buffer) {
		super();
		this.keson_code = keson_code;
		this.element_count = element_count;
		this.element_size = element_size;
		this.data = data;
		this.buffer = buffer;
	}
	public kestrel_array_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_array_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_array_t implements Structure.ByValue {
		
	};
}
