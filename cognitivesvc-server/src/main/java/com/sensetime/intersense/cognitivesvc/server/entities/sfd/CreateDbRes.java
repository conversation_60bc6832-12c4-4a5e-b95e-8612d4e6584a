package com.sensetime.intersense.cognitivesvc.server.entities.sfd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateDbRes {
    //description:
    //创建新库回复.
    //
    //db_id	string
    //库UUID, 范围:UUIDv4格式.
    //
    //object_type	string
    //对象类型, 见commonapis.ObjectFeature的type和ObjectType的说明, 目前支持"face"等, 视模型而定.
    //
    //name	string
    //库名称.
    //
    //indexes	[
    //分片信息.
    //
    //static_feature_dbIndexInfo{
    //Jump to path
    //description:
    //分片信息.
    //
    //uuid	[...]
    //default_opq_model	[...]
    //object_type	[...]
    //feature_version	[...]
    //cache_raw_feature	[...]
    //status	static_feature_dbStatus[...]
    //size	[...]
    //creation_time	[...]
    //last_seq_id	[...]
    //last_retrained_seq_id	[...]
    //worker_id	[...]
    //capacity	[...]
    //
    //}]
    //creation_time	string($date-time)
    //库的创建时间.

    String db_id;
    String object_type;
    String name;
    List<SfdIndex> indexes;
    String creation_time;

}
