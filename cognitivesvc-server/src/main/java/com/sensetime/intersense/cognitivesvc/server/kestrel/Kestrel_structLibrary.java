package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * JNA Wrapper for library <b>kestrel_struct</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_structLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_structLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_structLibrary INSTANCE = (Kestrel_structLibrary)Native.load(Kestrel_structLibrary.JNA_LIBRARY_NAME, Kestrel_structLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_struct.h</i><br>
	 * enum values
	 */
	public static interface kestrel_data_type_e {
		/** <i>native declaration : include/kestrel_struct.h:78</i> */
		public static final int KESTREL_BYTE = 0;
		/** <i>native declaration : include/kestrel_struct.h:80</i> */
		public static final int KESTREL_BOOL = 1;
		/** <i>native declaration : include/kestrel_struct.h:82</i> */
		public static final int KESTREL_INT8 = 11;
		/** <i>native declaration : include/kestrel_struct.h:84</i> */
		public static final int KESTREL_INT8x2 = 12;
		/** <i>native declaration : include/kestrel_struct.h:86</i> */
		public static final int KESTREL_INT8x3 = 13;
		/** <i>native declaration : include/kestrel_struct.h:88</i> */
		public static final int KESTREL_INT8x4 = 14;
		/** <i>native declaration : include/kestrel_struct.h:90</i> */
		public static final int KESTREL_INT16 = 21;
		/** <i>native declaration : include/kestrel_struct.h:92</i> */
		public static final int KESTREL_INT16x2 = 22;
		/** <i>native declaration : include/kestrel_struct.h:94</i> */
		public static final int KESTREL_INT16x3 = 23;
		/** <i>native declaration : include/kestrel_struct.h:96</i> */
		public static final int KESTREL_INT16x4 = 24;
		/** <i>native declaration : include/kestrel_struct.h:98</i> */
		public static final int KESTREL_INT32 = 31;
		/** <i>native declaration : include/kestrel_struct.h:100</i> */
		public static final int KESTREL_INT32x2 = 32;
		/** <i>native declaration : include/kestrel_struct.h:102</i> */
		public static final int KESTREL_INT32x3 = 33;
		/** <i>native declaration : include/kestrel_struct.h:104</i> */
		public static final int KESTREL_INT32x4 = 34;
		/** <i>native declaration : include/kestrel_struct.h:106</i> */
		public static final int KESTREL_INT64 = 4;
		/** <i>native declaration : include/kestrel_struct.h:108</i> */
		public static final int KESTREL_UINT8 = 51;
		/** <i>native declaration : include/kestrel_struct.h:110</i> */
		public static final int KESTREL_UINT8x2 = 52;
		/** <i>native declaration : include/kestrel_struct.h:112</i> */
		public static final int KESTREL_UINT8x3 = 53;
		/** <i>native declaration : include/kestrel_struct.h:114</i> */
		public static final int KESTREL_UINT8x4 = 54;
		/** <i>native declaration : include/kestrel_struct.h:116</i> */
		public static final int KESTREL_UINT16 = 61;
		/** <i>native declaration : include/kestrel_struct.h:118</i> */
		public static final int KESTREL_UINT16x2 = 62;
		/** <i>native declaration : include/kestrel_struct.h:120</i> */
		public static final int KESTREL_UINT16x3 = 63;
		/** <i>native declaration : include/kestrel_struct.h:122</i> */
		public static final int KESTREL_UINT16x4 = 64;
		/** <i>native declaration : include/kestrel_struct.h:124</i> */
		public static final int KESTREL_UINT32 = 71;
		/** <i>native declaration : include/kestrel_struct.h:126</i> */
		public static final int KESTREL_UINT32x2 = 72;
		/** <i>native declaration : include/kestrel_struct.h:128</i> */
		public static final int KESTREL_UINT32x3 = 73;
		/** <i>native declaration : include/kestrel_struct.h:130</i> */
		public static final int KESTREL_UINT32x4 = 74;
		/** <i>native declaration : include/kestrel_struct.h:132</i> */
		public static final int KESTREL_UINT64 = 8;
		/** <i>native declaration : include/kestrel_struct.h:134</i> */
		public static final int KESTREL_FLOAT16 = 91;
		/** <i>native declaration : include/kestrel_struct.h:136</i> */
		public static final int KESTREL_FLOAT16x2 = 92;
		/** <i>native declaration : include/kestrel_struct.h:138</i> */
		public static final int KESTREL_FLOAT16x3 = 93;
		/** <i>native declaration : include/kestrel_struct.h:140</i> */
		public static final int KESTREL_FLOAT16x4 = 94;
		/** <i>native declaration : include/kestrel_struct.h:142</i> */
		public static final int KESTREL_FLOAT32 = 101;
		/** <i>native declaration : include/kestrel_struct.h:144</i> */
		public static final int KESTREL_FLOAT32x2 = 102;
		/** <i>native declaration : include/kestrel_struct.h:146</i> */
		public static final int KESTREL_FLOAT32x3 = 103;
		/** <i>native declaration : include/kestrel_struct.h:148</i> */
		public static final int KESTREL_FLOAT32x4 = 104;
		/** <i>native declaration : include/kestrel_struct.h:150</i> */
		public static final int KESTREL_FLOAT64 = 111;
		/** <i>native declaration : include/kestrel_struct.h:152</i> */
		public static final int KESTREL_FLOAT64x2 = 112;
		/** <i>native declaration : include/kestrel_struct.h:154</i> */
		public static final int KESTREL_FLOAT64x3 = 113;
		/** <i>native declaration : include/kestrel_struct.h:156</i> */
		public static final int KESTREL_FLOAT64x4 = 114;
		/** <i>native declaration : include/kestrel_struct.h:158</i> */
		public static final int KESTREL_COMPLEX32 = 121;
		/** <i>native declaration : include/kestrel_struct.h:160</i> */
		public static final int KESTREL_COMPLEX32x2 = 122;
		/** <i>native declaration : include/kestrel_struct.h:162</i> */
		public static final int KESTREL_COMPLEX32x3 = 123;
		/** <i>native declaration : include/kestrel_struct.h:164</i> */
		public static final int KESTREL_COMPLEX32x4 = 124;
		/** <i>native declaration : include/kestrel_struct.h:166</i> */
		public static final int KESTREL_COMPLEX64 = 141;
		/** <i>native declaration : include/kestrel_struct.h:168</i> */
		public static final int KESTREL_COMPLEX128 = 151;
		/** <i>native declaration : include/kestrel_struct.h:170</i> */
		public static final int KESTREL_DTYPE_UNKNOWN = 152;
	};
	/** <i>native declaration : include/kestrel_struct.h</i> */
	public static final int KESTREL_FOURCC_MAX_STRING_SIZE = (int)(32);
	/** <i>native declaration : include/kestrel_struct.h:0</i> */
	public static final int KESTREL_INVALID_FOURCC = (int)(0);
	/**
	 * @return Size of data type element in terms of bytes<br>
	 * Original signature : <code>size_t kestrel_data_type_size(kestrel_data_type_e)</code><br>
	 * <i>native declaration : include/kestrel_struct.h:177</i>
	 */
	long kestrel_data_type_size(int t);
}
