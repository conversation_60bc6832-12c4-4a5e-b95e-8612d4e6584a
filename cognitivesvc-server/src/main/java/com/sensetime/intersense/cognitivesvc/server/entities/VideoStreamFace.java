package com.sensetime.intersense.cognitivesvc.server.entities;

import java.awt.Polygon;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;

import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.experimental.Accessors;

@Entity
@Table(name = "${db.video_stream_face.table.name:video_stream_face}")
@Accessors(chain = true)
@Schema(title = "直接视频流设备", description = "直接视频流设备")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamFace{
	
	@Id
	@Column(name = "device_id")
	@Schema(description = "设备id")
	@Getter
	@Setter
	private String  deviceId;
	
	@Column(name = "run_face_model", columnDefinition = "int DEFAULT 1")
	@Schema(description = "该设备是否走face模型")
	@Getter
	@Setter
    private Integer runFaceModel;
	
	@Column(name = "run_face_attr_model", columnDefinition = "int DEFAULT 1")
	@Schema(description = "该设备是否走faceAttr模型")
	@Getter
	@Setter
    private Integer runFaceAttrModel;
	
	@Column(name = "base_threshold")
	@Schema(description = "基础阈值")
	@Getter
	@Setter
    private Float baseThreshold;
	
	@Column(name = "base_image_quality")
	@Schema(description = "基础质量阈值")
	@Getter
	@Setter
    private Float baseImageQuality;

	@Column(name = "integrate_quality")
	@Schema(description = "综合质量阈值")
	@Getter
	@Setter
	private Float integrateQuality;
	
	@Column(name = "yaw")
	@Schema(description = "yaw")
	@Getter
	@Setter
    private Float yaw;
	
	@Column(name = "pitch")
	@Schema(description = "pitch")
	@Getter
	@Setter
    private Float pitch;
	
	@Column(name = "roll")
	@Schema(description = "roll")
	@Getter
	@Setter
    private Float roll;
	
	@Column(name = "roi")
	@Schema(description = "热区")
	@Getter
	@Setter
    private String roi;
	
	@Column(name = "roi_ids")
	@Schema(description = "热区的外部id")
	@Getter
	@Setter
    private String roiIds;
	
	@Column(name = "min_face_size")
	@Schema(description = "最小脸size")
	@Getter
	@Setter
    private Integer minFaceSize;
	
	@Column(name = "store_scene")
	@Schema(description = "是否存大图")
	@Getter
	@Setter
    private Boolean storeScene;
	
	@Column(name = "store_passer")
	@Schema(description = "是否存陌生人")
	@Getter
	@Setter
    private Boolean storePasser;
	
	@Column(name = "target_group")
	@Schema(description = "目标组")
	@Getter
	@Setter
    private String targetGroup;
	
	@Column(name = "quick_response_time")
	@Schema(description = "快速人脸识别响应时间")
	@Getter
	@Setter
    private Integer quickResponseTime;
	
	@Column(name = "time_interval")
	@Schema(description = "选帧触发的时间间隔")
	@Getter
	@Setter
    private Integer timeInterval;
	
	@Column(name = "max_track_time")
	@Schema(description = "目标最大的跟踪时长")
	@Getter
	@Setter
    private Integer maxTrackTime;
	
	@Column(name = "max_tracklet_num")
	@Schema(description = "追踪目标上限(影响性能)")
	@Getter
	@Setter
    private Integer maxTrackletNum;


	@Column(name = "processors")
	@Schema(description = "人脸告警")
	@Getter
	@Setter
	private String  processors;
    
    @Transient
    @JsonIgnore
    @Schema(hidden = true)
    private transient Polygon polygons[];
    
    public Polygon[] queryPolygons() {
    	if(polygons != null)
    		return polygons;
    	
    	try {
    		if(StringUtils.isBlank(roi)) {
    			polygons = new Polygon[0];
    		}else {
	    		int[][][] rois = JSON.parseObject(roi, int[][][].class);
	    		Polygon[] polygons = new Polygon[rois.length];
	    		
	    		for(int index = 0 ; index < rois.length; index ++) {
	    			int pointNum = rois[index].length;
	    			int[] xs = new int[pointNum];
	    			int[] ys = new int[pointNum];
	    			for(int jndex = 0 ; jndex < rois[index].length; jndex ++) {
	    				xs[jndex] = rois[index][jndex][0];
	    				ys[jndex] = rois[index][jndex][1];
	    			}
	    			
	    			polygons[index] = new Polygon(xs, ys, pointNum);
	    		}
	    		
	    		this.polygons = polygons;
    		}
    	}catch(Exception e) {
    		e.printStackTrace();
    		polygons = new Polygon[0];
    	}
    	
    	return polygons;
    }

	public Integer getGlobalMaxTrackTime(){

		//精准模式才有用
		if(Utils.instance.maxTrackingTime > 0 && Utils.instance.faceRecognitionMode == 0){
			return Utils.instance.maxTrackingTime;
		}
		return -1;
	}
	public Integer getSelectFrameTimeInterVal(){

		//精准模式才有用
		if(Utils.instance.selectFrameTimeInterVal > 0 && Utils.instance.faceRecognitionMode == 0){
			return Utils.instance.selectFrameTimeInterVal;
		}
		return -1;
	}
	public Processor getProcessFace(){
    	if(this.processors == null){
			return new Processor();
		}
		if(this.processors.isEmpty()){
			return new Processor();
		}
		return JSON.parseObject(this.processors, Processor.class);
	}

	@Accessors(chain = true)
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	@Data
	public static final class Processor {
		public static final Processor empty = new Processor();

		@Getter @Setter
		private Integer eventTrackTime = -1;//-1持续告警>0的值 实际检测时长(单位：秒)

		@Getter @Setter
		private Integer strangerPolicy = 0;//是否是陌生人策略 0 陌生人策略，1 非陌生人策略

		@Getter @Setter
		private Float largeDetectThresh;

		@Getter @Setter
		private Float smallDetectThresh;

		@Getter @Setter
		private Float faceTrackQualityThresh;

		@Getter @Setter
		private Float pedTrackQualityThresh;

		@Getter @Setter
		private Float integrateQuality;



	}


}
