package com.sensetime.intersense.cognitivesvc.server.utils;

import com.sensetime.intersense.cognitivesvc.server.entities.FrameParamReq;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 视频帧提取工具类
 * 用于从视频流中提取帧并进行处理
 */
@Slf4j
@Component
public class FetchFrameVideo {
    
    private static final String EVENT_NAME = "imageData";
    private static final String EVENT_ACTION = "extractImage";
    private static final String EOF_ERROR = "KPLUGIN_E_EOF";
    private static final int FRAME_LOG_INTERVAL = 500;
    private static final int ERROR_THRESHOLD = 20;
    
    @Autowired
    private BaseOutput imageDataEventOutput;

    private final ConcurrentHashMap<String, Boolean> stopProcessingMap = new ConcurrentHashMap<>();

    /**
     * 使用默认GPU设置提取视频帧
     * @param req 帧参数请求
     * @return 视频帧
     */
    public synchronized VideoStream.VideoFrame fetch_up_down_load_video_path(FrameParamReq req) {
        boolean isGpu = Initializer.isGpu();
        return fetch_up_down_load_video_path(req, isGpu, !isGpu, isGpu);
    }

    /**
     * 从视频中提取帧
     * @param req 帧参数请求
     * @param useGpuDecoder 是否使用GPU解码
     * @param fetchCpu 是否使用CPU获取帧
     * @param fetchGpu 是否使用GPU获取帧
     * @return 视频帧
     */
    public synchronized VideoStream.VideoFrame fetch_up_down_load_video_path(
            FrameParamReq req, 
            boolean useGpuDecoder, 
            boolean fetchCpu, 
            boolean fetchGpu) {
            
        String videoId = req.getVideoId();
        stopProcessingMap.put(videoId, false);

        VideoStream stream = initializeVideoStream(req, useGpuDecoder);
        VideoStream.VideoFrame videoFrame = VideoStream.VideoFrame.builder().build();
        boolean keepRunning = true;
        int errorTimeApi = 0;

        try {
            while (keepRunning && !stopProcessingMap.getOrDefault(videoId, false) && (req.getRange() <= 0 || videoFrame.frameIndex < req.getRange())) {
                try {
                    videoFrame = processNextFrame(stream, videoFrame, req, fetchCpu, fetchGpu);
                } catch (Exception e) {
                    keepRunning = handleFrameProcessingError(e, stream, videoFrame, req, ++errorTimeApi);
                }
            }
        } finally {
            cleanup(stream, videoFrame, keepRunning, req);
            stopProcessingMap.remove(videoId);
        }

        return videoFrame;
    }

    /**
     * 异步提取视频帧
     */
    public CompletableFuture<VideoStream.VideoFrame> fetch_up_down_load_video_path_sync(FrameParamReq req) {
        boolean isGpu = Initializer.isGpu();
        return fetch_up_down_load_video_path_sync(req, isGpu, !isGpu, isGpu);
    }

    /**
     * 异步提取视频帧的具体实现
     */
    public CompletableFuture<VideoStream.VideoFrame> fetch_up_down_load_video_path_sync(
            FrameParamReq req,
            boolean useGpuDecoder,
            boolean fetchCpu,
            boolean fetchGpu) {
        return CompletableFuture.supplyAsync(() -> 
            fetch_up_down_load_video_path(req, useGpuDecoder, fetchCpu, fetchGpu)
        );
    }

    /**
     * 发送处理状态消息
     */
    private void sendMessage(String videoId, long frameIndex, String imageUrl,String imageLibraryId, String result, String reason) {
        String message = String.format(
            "{\"eventName\":\"%s\",\"eventAction\":\"%s\",\"data\":{\"videoId\":\"%s\",\"frameIdx\":\"%d\"," +
            "\"imageUrl\":\"%s\",\"imageLibraryId\":\"%s\",\"result\":\"%s\",\"reason\":\"%s\"}}",
            EVENT_NAME, EVENT_ACTION, videoId, frameIndex, imageUrl, imageLibraryId,result, reason
        );

        imageDataEventOutput.send(MessageBuilder
            .withPayload(message)
            .setHeader(KafkaHeaders.KEY, videoId.getBytes())
            .build());
        boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -444;
        if(loggedForCost){
            log.info("fetchVideoFrame sendMessage:{}", message);
        }
    }

    /**
     * 初始化视频流
     */
    private VideoStream initializeVideoStream(FrameParamReq req, boolean useGpuDecoder) {
        VideoStream stream = new VideoStream(VideoStreamInfra.builder()
            .deviceId(req.getVideoId())
            .rtspSource(req.getRtspSource())
            .frameBuffer(0)
            .frameBufferStrategy("smart")
            .build());
        stream.setUseDeviceDecoder(useGpuDecoder);
        stream.start();
        return stream;
    }

    /**
     * 处理下一帧
     */
    private VideoStream.VideoFrame processNextFrame(
            VideoStream stream, 
            VideoStream.VideoFrame videoFrame,
            FrameParamReq req, 
            boolean fetchCpu, 
            boolean fetchGpu) throws Exception {
            
        boolean handleCurrentFrame = shouldHandleFrame(videoFrame, req);
        videoFrame = stream.grabberNextFrame(fetchCpu & handleCurrentFrame, fetchGpu & handleCurrentFrame);

        if (videoFrame.getFrame() == null) {
            if (handleCurrentFrame) {
                log.error("saveImageError={},frameIndex={},getFrame() is null", videoFrame, videoFrame.frameIndex);
            }
            return videoFrame;
        }

        String targetImage = FrameUtils.save_image_as_jpg(videoFrame.getFrame(), ImageUtils.newFile(req.getSaveDir()), 0);
        validateAndProcessFrame(targetImage, videoFrame, req);
        
        return videoFrame;
    }

    /**
     * 判断是否应该处理当前帧
     */
    private boolean shouldHandleFrame(VideoStream.VideoFrame videoFrame, FrameParamReq req) {
        return videoFrame.frameIndex == -1 || 
               videoFrame.frameIndex % (Math.abs(req.getFrameSkip()) + 1) == 0;
    }

    /**
     * 验证和处理帧
     */
    private void validateAndProcessFrame(String targetImage, VideoStream.VideoFrame videoFrame, FrameParamReq req) {
        if (targetImage.isBlank() || targetImage.equals(FrameUtils.NOIMAGE)) {
            throw new RuntimeException("saveImageError targetImage:" + targetImage);
        }

        if (videoFrame.frameIndex % FRAME_LOG_INTERVAL == 0) {
            log.info("videoFrame.frameIndex={},targetImage={},req={}", 
                    videoFrame.frameIndex, targetImage, req);
        }

        sendMessage(req.getVideoId(), videoFrame.frameIndex, targetImage, req.getImageLibraryId(),"processing", "");
        FrameUtils.batch_free_frame(videoFrame);
    }

    /**
     * 处理帧处理过程中的错误
     */
    private boolean handleFrameProcessingError(
            Exception e, 
            VideoStream stream, 
            VideoStream.VideoFrame videoFrame,
            FrameParamReq req, 
            int errorTimeApi) {
            
        log.error("handle fetch frameOffline err={},frameIndex={},videoId={}", e.getMessage(), videoFrame.frameIndex, req.getVideoId());
        
        boolean keepRunning = true;
        if (EOF_ERROR.equals(e.getMessage())) {
            sendMessage(req.getVideoId(), videoFrame.frameIndex, "",req.getImageLibraryId(), "success", "");
            return false;
        }
        if (stream.errorAgainTime >= Utils.instance.videoStatusErrorKestrelAgainTime ||
            stream.errorInternalTime >= Utils.instance.videoStatusErrorInternalTime ||
            errorTimeApi % ERROR_THRESHOLD == 0) {
            
            keepRunning = false;
            log.error("handle fetch frame send errFetchMessage videoid={},err={},errorTimeApi={}", 
                    req.getVideoId(), e.getMessage(), errorTimeApi);
            sendMessage(req.getVideoId(), videoFrame.frameIndex, "",req.getImageLibraryId(), "fail", e.getMessage());
        }

        
        return keepRunning;
    }

    /**
     * 清理资源
     */
    private void cleanup(VideoStream stream, VideoStream.VideoFrame videoFrame, boolean keepRunning, FrameParamReq req) {
        stream.stop();
        if (keepRunning) {
            sendMessage(req.getVideoId(), videoFrame.frameIndex,"", req.getImageLibraryId(), "success", "");
        }
        log.info("handle offline fetchFrameByVideo done,req={},frameIndex={},keepRunning={}", 
                req, videoFrame.frameIndex, keepRunning);
    }

    /**
     * Stops video processing for a specific videoId.
     * @param videoId the ID of the video to stop processing
     */
    public void stopVideoProcessing(String videoId) {
        stopProcessingMap.put(videoId, true);
    }
}
