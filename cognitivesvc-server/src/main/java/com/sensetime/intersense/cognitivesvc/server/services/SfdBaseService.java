package com.sensetime.intersense.cognitivesvc.server.services;

import com.alibaba.fastjson.JSONObject;
import com.sensetime.intersense.cognitivesvc.server.entities.sfd.*;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.DP;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SeekParam;
import com.sensetime.intersense.cognitivesvc.server.properties.SfdProperties;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker;
import com.sensetime.intersense.cognitivesvc.server.utils.FeatureConversionEncryptionUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.storage.service.FileAccessor;
import com.sensetime.storage.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.nio.file.Files;

import java.util.*;
import java.util.stream.Collectors;
import java.lang.reflect.Field;

import org.apache.commons.lang3.tuple.Pair;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity;


/**
 * SFD服务抽象基类
 * 参考FaissSeeker设计，提供SFD服务调用的统一框架
 * @param <T> 检索参数类型
 * @param <D> 结果对象类型
 * @param <R> 返回结果类型
 */
@Slf4j
public abstract class SfdBaseService<T extends SeekParam, D extends DP, R> {

    @Autowired
    protected SfdProperties sfdProperties;
    
    @Autowired
    protected FileAccessor fileAccessor;

    /**
     * 获取SFD数据库列表
     */
    protected List<InsightDbInfo> queryDbList() {
        String featureHost = sfdProperties.getStaticFaceDbServiceHost();
        String queryDbListUrl = featureHost + sfdProperties.getDbList();
        ResponseEntity<String> httpResp = null;
        Integer[] pc = {HttpStatus.OK.value()};
        List<Integer> passCondition = Arrays.asList(pc);
        try {
            RestTemplate restTemplate = RestUtils.restTemplate60000ms;
            httpResp = restTemplate.getForEntity(queryDbListUrl, String.class);
            QueryDbListRes resp = JSONObject.parseObject(httpResp.getBody(), QueryDbListRes.class);
            if (resp != null && CollectionUtils.isNotEmpty(resp.getDb_infos())) {
                return resp.getDb_infos();
            }
            log.info("queryDbList:{}", httpResp.getBody());
        } catch (Exception ex) {
            log.error("SFD queryDbList error:", ex);
        }
        return new ArrayList<>();
    }

    /**
     * 替换URL中的占位符
     */
    protected String replaceBraces(String input, String... placeholders) {
        for (int i = 0; i < placeholders.length; i++) {
            input = input.replace("{" + i + "}", placeholders[i]);
        }
        return input;
    }

    /**
     * 在指定数据库中搜索
     * @param param 搜索参数
     * @param dbId 数据库ID
     * @return 搜索结果
     */
    protected R searchInDb(T param, String dbId) {
        long startTime = System.currentTimeMillis();
        
        if (StringUtils.isEmpty(dbId)) {
            log.error("SFD database ID is empty");
            return getEmptyResult();
        }
        //String dbId = getDbId(group);

        // 构建请求
        AlertFeatureReq alertFeatureReq = buildRequest(param, dbId);
        
        // 执行SFD搜索
        RestTemplate restTemplate = RestUtils.restTemplate10000ms;


        log.info("sfdReq:alertFeatureReq:{},{}", dbId, JSONObject.toJSONString(alertFeatureReq));
      
        String searchUrl = getSearchUrl(dbId);

        log.info("sfdReq:searchUrl:{}", searchUrl);
        
        try {
            ResponseEntity<FaceRecognitionResponse> response = restTemplate.postForEntity(
                    searchUrl, 
                    JSONObject.toJSONString(alertFeatureReq), 
                    FaceRecognitionResponse.class
            );
            
            FaceRecognitionResponse sfdResponse = response.getBody();
            if (sfdResponse == null) {
                log.error("SFD search failed, response is null");
                return getEmptyResult();
            }
            log.info("sfdResponse:{}", JSONObject.toJSONString(sfdResponse));
            
            // 处理并返回结果
            R result = processResponse(sfdResponse, param);
            log.info("SFD search in db {} success, cost time: {}ms", dbId, (System.currentTimeMillis() - startTime));
            return result;
            
        } catch (RestClientException restClientException) {
            log.error("SFD search failed, rest api failed, url = {}, error: {}", 
                   searchUrl, restClientException.getMessage());
        } catch (Exception e) {
            log.error("SFD search exception: {}", e.getMessage(), e);
        } finally {
            log.debug("SFD search in db {} cost time: {}ms", dbId, (System.currentTimeMillis() - startTime));
        }
        
        return getEmptyResult();
    }

    /**
     * 合并搜索结果
     * @param param 搜索参数
     * @param groups 搜索组
     * @return 搜索结果
     */
    protected abstract R mergeResults(T param, List<String> groups);

    /**
     * 统一的搜索方法
     * @param param 搜索参数
     * @return 搜索结果
     */
    @SuppressWarnings("unchecked")
    public R search(T param) {
        long startTime = System.currentTimeMillis();
        
        // 检查是否是PersonParam并且有personGroups配置
        String[] groups = null;
        if (param instanceof CognitiveEntity.PersonParam) {
            groups = ((CognitiveEntity.PersonParam) param).personGroups;
        }
        
        // 如果没有指定组，使用默认数据库
        if (groups == null || groups.length == 0) {
            String dbId = getDbId("");
            if (dbId == null) {
                log.error("SFD database not found, db name: {}", getDbName());
                return getEmptyResult();
            }
            return searchInDb(param, dbId);
        }
        
        // 在每个组中搜索
        return mergeResults(param, Arrays.asList(groups));
    }

    /**
     * 获取数据库ID
     */
    protected String getDbId(String group) {
        List<InsightDbInfo> insightDbInfos = queryDbList();
        log.info("queryDbList getDbId:{},dbname:{},dbId:{}", JSONObject.toJSONString(insightDbInfos),getDbName(),sfdProperties.getDb().getId());
        String dbId = null;
        if (StringUtils.isNotBlank(group)) {
            dbId = insightDbInfos.stream()
                    .filter(info -> group.equals(info.getName()))
                    .map(InsightDbInfo::getDb_id)
                    .findFirst()
                    .orElse(null);
        } else {
            dbId = sfdProperties.getDb().getId();
            if (StringUtils.isBlank(dbId) && CollectionUtils.isNotEmpty(insightDbInfos)) {
                dbId = insightDbInfos.stream()
                        .filter(info -> getDbName().equals(info.getName()))
                        .map(InsightDbInfo::getDb_id)
                        .findFirst()
                        .orElse(null);
                log.info("queryDbList getDbId passer, dbname:{}", dbId);
            }
        }
        return dbId;
    }
    
    /**
     * 构建SFD请求
     */
    protected AlertFeatureReq buildRequest(T param, String dbId) {

        Base64.Encoder encoder = Base64.getEncoder();
        
        String featureBlob = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeader(
                FeatureConversionEncryptionUtils.encodeBlob(FaissSeeker.featureToString(getFeature(param)), sfdProperties.getSfdEncKey())));
        
        ReqFeature reqFeature = ReqFeature.builder()
                .blob(featureBlob)
                .type(getFeatureType())
                .version(getFeatureVersion())
                .build();
                
        return AlertFeatureReq.builder()
                .return_raw_feature(true)
                .col_id(dbId)
                .min_score(getMinScore(param))
                .top_k(getTopK(param))
                .features(Collections.singletonList(reqFeature))
                .build();
    }
    
    /**
     * 获取SFD搜索URL
     */
    protected String getSearchUrl(String dbId) {
        return replaceBraces(sfdProperties.getStaticFaceDbServiceHost() + getSfdSearchPath(), dbId);
    }

    // 由子类实现的抽象方法
    protected abstract float[] getFeature(T param);
    protected abstract String getFeatureType();
    protected abstract Integer getFeatureVersion();
    protected abstract String getDbName();
    protected abstract String getSfdSearchPath();
    protected abstract int getTopK(T param);
    protected abstract float getMinScore(T param);
    protected abstract R processResponse(FaceRecognitionResponse response, T param);

/**
 * 处理多数据库搜索响应
 * @param response 多数据库响应
 * @param param 搜索参数
 * @return 处理后的结果
 */
protected abstract R processMultiDbResponse(FaceRecognitionMultiDbResponse response, T param);
    protected abstract R getEmptyResult();

/**
 * 多数据库搜索接口
 * @param param 搜索参数
 * @param groups 数据库组名列表
 * @param count 每个数据库的返回数量限制
 * @param threshold 相似度阈值
 * @return 搜索结果
 */
public R searchMultiDb(T param, String[] groups, Integer count, Float threshold) {
    long startTime = System.currentTimeMillis();
    // 如果groups为空，则获取所有数据库ID
    boolean useAllDatabases = (groups == null || groups.length == 0);
   
    if (useAllDatabases) {
        log.info("No specific SFD database groups provided, will use all available databases");
    }else{
        log.info("searchMultiDb groups:{}", JSONObject.toJSONString(groups));
    }
    
    // 构建请求
    AlertFeatureMultiDbReq alertFeatureMultiDbReq = new AlertFeatureMultiDbReq();
    
    // 设置图像信息
    AlertFeatureMultiDbReq.Image image = new AlertFeatureMultiDbReq.Image();
    AlertFeatureMultiDbReq.ImageData imageData = new AlertFeatureMultiDbReq.ImageData();
    

    imageData.setData(""); 
    image.setImage(imageData);
    image.setFace_selection("LargestFace");
    alertFeatureMultiDbReq.setImage(image);
    
    // 设置特征值
    float[] feature = getFeature(param);
    if (feature != null) {
        alertFeatureMultiDbReq.setFeature(feature);
    } else {
        log.error("Feature is null");
        return getEmptyResult();
    }
    Base64.Encoder encoder = Base64.getEncoder();

    String featureBlob = encoder.encodeToString(FeatureConversionEncryptionUtils.addHeader(
            FeatureConversionEncryptionUtils.encodeBlob(FaissSeeker.featureToString(getFeature(param)), sfdProperties.getSfdEncKey())));

    ReqFeature reqFeature = ReqFeature.builder()
            .blob(featureBlob)
            .type(getFeatureType())
            .version(getFeatureVersion())
            .build();

    alertFeatureMultiDbReq.setFeatures(reqFeature);
    
    // 设置特征类型和版本
    alertFeatureMultiDbReq.setFeature_type(getFeatureType());
    alertFeatureMultiDbReq.setFeature_version(getFeatureVersion());
    
    // 设置数据库列表
    List<AlertFeatureMultiDbReq.DbInfo> dbList = new ArrayList<>();
    
    // 只调用一次queryDbList获取所有数据库信息
    List<InsightDbInfo> insightDbInfos = queryDbList();
    log.info("queryDbList for multiDb search:{}", JSONObject.toJSONString(insightDbInfos));
    
    // 创建一个映射，从group名称到dbId
    Map<String, String> groupToDbIdMap = insightDbInfos.stream()
            .filter(info -> info.getName() != null)
            .collect(Collectors.toMap(
                InsightDbInfo::getName,
                InsightDbInfo::getDb_id,
                (existing, replacement) -> existing  // 如果有重复的group名称，保留第一个
            ));
    
    // 如果useAllDatabases为true，则使用所有数据库；否则只使用指定的数据库
    if (useAllDatabases) {
        // 使用所有可用的数据库
        for (InsightDbInfo dbInfo : insightDbInfos) {
            if (StringUtils.isNotEmpty(dbInfo.getDb_id())) {
                AlertFeatureMultiDbReq.DbInfo reqDbInfo = new AlertFeatureMultiDbReq.DbInfo();
                reqDbInfo.setDb_id(dbInfo.getDb_id());
                reqDbInfo.setTop_k(count != null ? count : getTopK(param));
                reqDbInfo.setMin_score(threshold != null ? threshold : getMinScore(param));
                dbList.add(reqDbInfo);
                log.info("Adding all db to multi-search: {}, top_k={}, min_score={}", 
                        dbInfo.getDb_id(), reqDbInfo.getTop_k(), reqDbInfo.getMin_score());
            }
        }
    } else {
        // 处理指定的group
        for (String group : groups) {
            String dbId = groupToDbIdMap.get(group);
            if (StringUtils.isNotEmpty(dbId)) {
                AlertFeatureMultiDbReq.DbInfo reqDbInfo = new AlertFeatureMultiDbReq.DbInfo();
                reqDbInfo.setDb_id(dbId);
                reqDbInfo.setTop_k(count != null ? count : getTopK(param));
                reqDbInfo.setMin_score(threshold != null ? threshold : getMinScore(param));
                dbList.add(reqDbInfo);
                log.info("Adding db to multi-search: {}, top_k={}, min_score={}", 
                        dbId, reqDbInfo.getTop_k(), reqDbInfo.getMin_score());
            } else {
                log.warn("Could not find dbId for group: {}", group);
            }
        }
    }
    
    if (dbList.isEmpty()) {
        log.error("No valid databases found for groups");
        return getEmptyResult();
    }
    
    alertFeatureMultiDbReq.setDbs(dbList);
    alertFeatureMultiDbReq.setType("ALERT_FEATURE_DB");
    
    // 执行SFD搜索
    RestTemplate restTemplate = RestUtils.restTemplate10000ms;
    // 使用新的apiWrapperHost和faceMultiWrapper配置
    String searchUrl = sfdProperties.getApiWrapperHost() + sfdProperties.getFaceMultiWrapper();
    
    log.info("sfdMultiDbReq:searchUrl:{},{}", searchUrl, JSONObject.toJSONString(alertFeatureMultiDbReq));
    
    try {
        ResponseEntity<FaceRecognitionMultiDbResponse> response = restTemplate.postForEntity(
                searchUrl, 
                JSONObject.toJSONString(alertFeatureMultiDbReq), 
                FaceRecognitionMultiDbResponse.class
        );
        
        FaceRecognitionMultiDbResponse sfdResponse = response.getBody();
        if (sfdResponse == null) {
            log.error("SFD multi-db search failed, response is null");
            return getEmptyResult();
        }
        log.info("sfdMultiDbResponse:{}", JSONObject.toJSONString(sfdResponse));
        
        // 处理并返回结果
        R result = processMultiDbResponse(sfdResponse, param);
        log.info("SFD multi-db search success, cost time: {}ms", (System.currentTimeMillis() - startTime));
        return result;
        
    } catch (RestClientException restClientException) {
        log.error("SFD multi-db search failed, rest api failed, url = {}, error: {}", 
               searchUrl, restClientException.getMessage());
    } catch (Exception e) {
        log.error("SFD multi-db search exception: {}", e.getMessage(), e);
    } finally {
        log.debug("SFD multi-db search cost time: {}ms", (System.currentTimeMillis() - startTime));
    }
    
    return getEmptyResult();
}

    /**
     * 批量添加特征到SFD
     * 
     * @param items 特征项列表
     * @return 添加结果
     */
    public boolean batchAdd(List<FeatureItem> items) {
        String dbId = getDbId("");
        if (dbId == null) {
            log.error("SFD database not found, db name: {}", getDbName());
            return false;
        }
        return batchAdd(items, dbId);
    }

    /**
     * 批量添加特征到指定的SFD数据库
     * 
     * @param items 特征项列表
     * @param dbId 数据库ID
     * @return 添加结果
     */
    public boolean batchAdd(List<FeatureItem> items, String dbId) {
        long startTime = System.currentTimeMillis();
        
        if (StringUtils.isEmpty(dbId)) {
            log.error("SFD database ID is empty");
            return false;
        }

        // 构建请求
        AddFeatureReq addFeatureReq = AddFeatureReq.builder()
                .col_id(dbId)
                .items(items)
                .build();
        
        // 执行SFD添加
        RestTemplate restTemplate = RestUtils.restTemplate10000ms;
        String addUrl = getBatchAddUrl(dbId);
        
        log.info("sfdBatchAdd: url={}, req={}", addUrl, JSONObject.toJSONString(addFeatureReq));
        
        try {
            ResponseEntity<FeatureAddRes> response = restTemplate.postForEntity(
                    addUrl, 
                    JSONObject.toJSONString(addFeatureReq),
                    FeatureAddRes.class
            );
            FeatureAddRes sfdResponse = response.getBody();
            if (sfdResponse == null) {
                log.error("SFD search failed, response is null");
                return false;
            }
            log.info("sfdBatchAddRes:{}", JSONObject.toJSONString(sfdResponse));
            
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("SFD batch add success, cost time: {}ms", (System.currentTimeMillis() - startTime));
                return true;
            } else {
                log.error("SFD batch add failed, response code: {}", response.getStatusCodeValue());
                return false;
            }
            
        } catch (RestClientException restClientException) {
            log.error("SFD batch add failed, rest api failed, url = {}, error: {}", 
                   addUrl, restClientException.getMessage());
        } catch (Exception e) {
            log.error("SFD batch add exception: {}", e.getMessage(), e);
        } finally {
            log.debug("SFD batch add cost time: {}ms", (System.currentTimeMillis() - startTime));
        }
        
        return false;
    }

    /**
     * 获取SFD批量添加URL
     */
    protected String getBatchAddUrl(String dbId) {
        return replaceBraces(sfdProperties.getStaticFaceDbServiceHost() + "/v1/databases/{0}/batch_add", dbId);
    }
} 