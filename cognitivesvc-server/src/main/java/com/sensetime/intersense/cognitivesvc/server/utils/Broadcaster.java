package com.sensetime.intersense.cognitivesvc.server.utils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.sensetime.lib.clientlib.response.BaseRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class Broadcaster {
	
	@Autowired
	private DiscoveryClient discoveryClient;
	
	@Value("${spring.application.name}")
	private String name;

	public <T> List<T> getForObject(String appName, String url, Map<String, Object> param, Class<T> returnClass) {
		return getForObject(appName, url, param, returnClass, 3) ;
	}
	
	public <T> List<T> getForObject(String appName, String url, Map<String, Object> param, Class<T> returnClass, int retry) {
		if(StringUtils.isBlank(appName))
			appName = name;
		
		return getForObject(discoveryClient.getInstances(appName), url, param, returnClass, retry);
	}
	
	public <T> List<Pair<ServiceInstance, T>> getForPair(String appName, String url, Map<String, Object> param, Class<T> returnClass, int retry) {
		if(StringUtils.isBlank(appName))
			appName = name;
		
		List<ServiceInstance> instances = discoveryClient.getInstances(appName);
		List<T> datas = getForObject(instances, url, param, returnClass, retry);

		return Stream.iterate(0, i -> i + 1).limit(Math.min(instances.size(), datas.size())).map(index -> new MutablePair<ServiceInstance, T>(instances.get(index), datas.get(index))).collect(Collectors.toList());
	}
	
	public static <T> List<T> getForObject(List<ServiceInstance> instances, String url, Map<String, Object> param, Class<T> returnClass, int retry) {		
		return instances.parallelStream()
					  .map(instance -> {
						  for(int index = 0; index < retry; index ++)
							  try {
							  	if(Utils.instance.instanceLocalSwitch==1){
									return RestUtils.restTemplate60000ms.getForObject("http://" + "localhost" + ":" + instance.getPort() + "/" + url, returnClass, param);
								}else{
									return RestUtils.restTemplate60000ms.getForObject("http://" + instance.getHost() + ":" + instance.getPort() + "/" + url, returnClass, param);
								}

							  }catch(Exception e) {
							  	  e.printStackTrace();
							  }
						
						  return null;
					  })
					  .collect(Collectors.toList());
	}
	
	public <T> List<T> postForObject(String appName, String url, Object body, Class<T> returnClass) {
		return postForObject(appName, url, body, returnClass, 3);
	}
	
	public <T> List<T> postForObject(List<ServiceInstance> instances, String url, Object body, Class<T> returnClass) {
		return postForObject(instances, url, body, returnClass, 3);
	}
	
	public <T> List<T> postForObject(String appName, String url, Object body, Class<T> returnClass, int retry) {
		if(StringUtils.isBlank(appName))
			appName = name;
		
		return postForObject(discoveryClient.getInstances(appName), url, body, returnClass, retry);
	}
	
	public static <T> List<T> postForObject(List<ServiceInstance> instances, String url, Object body, Class<T> returnClass, int retry) {
		HttpEntity<Object> httpEntity = new HttpEntity<Object>(body, RestUtils.headers);
		
		return instances.parallelStream()
						.map(instance -> {
							for(int index = 0; index < retry; index ++) {
								try {
									if(Utils.instance.instanceLocalSwitch==1){
										return RestUtils.restTemplate60000ms.postForEntity("http://" + "localhost" + ":" + instance.getPort() + "/" + url, httpEntity, returnClass).getBody();
									}else{
										return RestUtils.restTemplate60000ms.postForEntity("http://" + instance.getHost() + ":" + instance.getPort() + "/" + url, httpEntity, returnClass).getBody();
									}

								}catch(Exception e) {
									e.printStackTrace();
								}
							}
							return null;
						})
						.collect(Collectors.toList()); 
	}
	
	/**
	 * 广播POST请求到除当前实例外的所有实例
	 * @param appName 服务名称
	 * @param url 请求路径
	 * @param body 请求体
	 * @param returnClass 返回类型
	 * @param retry 重试次数
	 */
	public <T> List<T> postForObjectExcludeSelf(String appName, String url, Object body, Class<T> returnClass, int retry) {
		if(StringUtils.isBlank(appName)) {
			appName = name;
		}
		
		String currentHostname = Utils.instance.getLocalHostname();
		String currentHostip = Utils.instance.getLocalIpAddress(); // 实际上 注册中心获取到的是ip，所以这里用 ip过滤会准确；为了避免漏 域名过滤也带上
		List<ServiceInstance> instances = discoveryClient.getInstances(appName)
			.stream()
            .filter(instance -> !instance.getHost().equals(currentHostname))
            .filter(instance -> !instance.getHost().equals(currentHostip))
			.collect(Collectors.toList());
			
		return postForObject(instances, url, body, returnClass, retry);
	}
	
	/**
	 * 广播GET请求到除当前实例外的所有实例
	 * @param appName 服务名称
	 * @param url 请求路径
	 * @param param 请求参数
	 * @param returnClass 返回类型
	 * @param retry 重试次数
	 */
	public <T> List<T> getForObjectExcludeSelf(String appName, String url, Map<String, Object> param, Class<T> returnClass, int retry) {
		if(StringUtils.isBlank(appName)) {
			appName = name;
		}
        
        String currentHostname = Utils.instance.getLocalHostname();
        String currentHostip = Utils.instance.getLocalIpAddress(); // 实际上 注册中心获取到的是ip，所以这里用 ip过滤会准确；为了避免漏 域名过滤也带上
		List<ServiceInstance> instances = discoveryClient.getInstances(appName)
			.stream()
                .filter(instance -> !instance.getHost().equals(currentHostname))
                .filter(instance -> !instance.getHost().equals(currentHostip))
			.collect(Collectors.toList());
			
		return getForObject(instances, url, param, returnClass, retry);
	}
}
