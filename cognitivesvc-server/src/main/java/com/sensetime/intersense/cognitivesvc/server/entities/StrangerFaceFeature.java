package com.sensetime.intersense.cognitivesvc.server.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "${db.stranger_face_feature.table.name:stranger_face_feature}")
@Data
public class StrangerFaceFeature{
	
	@Id
	@Column(name = "id")
	public int id;
	@Column(name = "avatar_image_url")
	public String avatarImageUrl;
	@Column(name = "image_feature")
	public String imageFeature;
	@Column(name = "attribute")
	public String attribute;
	@Column(name = "create_ts")
	public Date createTs;
	@Column(name = "groupKey")
	public String groupKey;
	@Column(name = "device_id")
	public String deviceId;
}
