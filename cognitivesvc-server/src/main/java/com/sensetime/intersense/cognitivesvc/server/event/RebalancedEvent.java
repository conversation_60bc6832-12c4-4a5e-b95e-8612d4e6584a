package com.sensetime.intersense.cognitivesvc.server.event;

import java.util.HashMap;
import java.util.Map;

import org.springframework.context.ApplicationEvent;

import lombok.Getter;
import lombok.Setter;

public class RebalancedEvent extends ApplicationEvent {
	
	private static final long serialVersionUID = 851249368183225895L;
	
	@Getter
	private boolean master;

	@Getter @Setter
	private boolean keepGoing;

	@Getter
	private int counting;
	
	@Getter
	@Setter
	public Map<String, Object> parameter = new HashMap<String, Object>();

	public RebalancedEvent(boolean master, int counting) {
		super(Thread.currentThread());
		
		this.master = master;
		this.counting = counting;
		this.keepGoing = true;
	}
	
	public static final int NoMatter = 0;
	public static final int SeekerSplit = 50;
	public static final int DispatchToWorker = 1500;
	public static final int RemappedSource = 2000;
	public static final int ReabalanceStream = 2500;
}
