package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_license.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_atsha204a_cmd_t extends Structure {
	/**
	 * @see kestrel_atsha204a_cmd_code_e<br>
	 * C type : kestrel_atsha204a_cmd_code_e
	 */
	public int command;
	public byte address;
	public byte data_len;
	/** C type : uint8_t* */
	public Pointer data;
	public kestrel_atsha204a_cmd_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("command", "address", "data_len", "data");
	}
	/**
	 * @param command @see kestrel_atsha204a_cmd_code_e<br>
	 * C type : kestrel_atsha204a_cmd_code_e<br>
	 * @param data C type : uint8_t*
	 */
	public kestrel_atsha204a_cmd_t(int command, byte address, byte data_len, Pointer data) {
		super();
		this.command = command;
		this.address = address;
		this.data_len = data_len;
		this.data = data;
	}
	public kestrel_atsha204a_cmd_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_atsha204a_cmd_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_atsha204a_cmd_t implements Structure.ByValue {
		
	};
}
