package com.sensetime.intersense.cognitivesvc.server.entities;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "设备监控info", description = "设备监控info")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstanceDeviceInfoItem {
    private String deviceId;
    private String videoStatus;
    private String annotatorName;
    private String runningPod;
    private String handleTotal;
    private String handleInMinute;
    private String sendMsgTotal;
    private String sendMsgInMinute;
    private String unhandledTotal;
    private String unhandledInMinute;
    private String type;
}
