package com.sensetime.intersense.cognitivesvc.server.entities.sfd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SfdIndex {
    //"uuid": "string",
    //      "default_opq_model": "string",
    //      "object_type": "string",
    //      "feature_version": 0,
    //      "cache_raw_feature": true,
    //      "status": "INITED",
    //      "size": "string",
    //      "creation_time": "2024-01-24T06:12:43.166Z",
    //      "last_seq_id": "string",
    //      "last_retrained_seq_id": "string",
    //      "worker_id": "string",
    //      "capacity": "string"

    String uuid;
    String default_opq_model;
    String object_type;
    int feature_version;
    boolean cache_raw_feature;
    String status;
    String size;
    String creation_time;
    String last_seq_id;
    String last_retrained_seq_id;
    String worker_id;
    String capacity;

}
