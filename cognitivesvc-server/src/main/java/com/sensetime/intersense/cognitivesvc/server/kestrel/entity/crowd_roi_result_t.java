package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_roi_result_t extends Structure {
	/** < roi编号 */
	public int roi_id;
	/**
	 * < roi顶点坐标<br>
	 * C type : kestrel_point2d_t[1024]
	 */
	public kestrel_point2d_t[] coords = new kestrel_point2d_t[1024];
	/** roi顶点数目 */
	public int coord_num;
	/**
	 * <目标列表<br>
	 * C type : crowd_head_target_list_t
	 */
	public crowd_head_target_list_t head_targets;
	/** <人群速度 */
	public float crowd_speed;
	public crowd_roi_result_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("roi_id", "coords", "coord_num", "head_targets", "crowd_speed");
	}
	/**
	 * @param roi_id < roi编号<br>
	 * @param coords < roi顶点坐标<br>
	 * C type : kestrel_point2d_t[1024]<br>
	 * @param coord_num roi顶点数目<br>
	 * @param head_targets <目标列表<br>
	 * C type : crowd_head_target_list_t<br>
	 * @param crowd_speed <人群速度
	 */
	public crowd_roi_result_t(int roi_id, kestrel_point2d_t coords[], int coord_num, crowd_head_target_list_t head_targets, float crowd_speed) {
		super();
		this.roi_id = roi_id;
		if ((coords.length != this.coords.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.coords = coords;
		this.coord_num = coord_num;
		this.head_targets = head_targets;
		this.crowd_speed = crowd_speed;
	}
	public crowd_roi_result_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_roi_result_t implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_roi_result_t implements Structure.ByValue {
		
	};
}