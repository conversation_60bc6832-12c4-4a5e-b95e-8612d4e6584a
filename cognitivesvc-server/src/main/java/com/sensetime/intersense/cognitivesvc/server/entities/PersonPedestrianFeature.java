package com.sensetime.intersense.cognitivesvc.server.entities;

import java.util.Date;

import jakarta.persistence.*;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SP;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "${db.person_pedestrian_feature.table.name:person_pedestrian_feature}")
@Data
@Accessors(chain = true)
public class PersonPedestrianFeature implements SP{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "avatar_image_url")
    private String avatarImageUrl;
    @Column(name = "image_feature")
    private String imageFeature;
    @Column(name = "model_version")
    private String modelVersion;
    @Column(name = "person_uuid")
    private String personUuid;
    @Column(name = "person_cn_name")
    private String personCnName;
    @Column(name = "person_en_name")
    private String personEnName;
    @Column(name = "sts")
    private Integer sts;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "create_ts")
    private Date createTs;
    @Column(name = "last_mod_user")
    private String lastModUser;
    @Column(name = "last_mod_ts")
    private Date lastModTs;
    @Column(name = "tag")
    private String tag;
    @Column(name = "privilege")
    private String privilege;
}
