package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_nn_def.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_nn_properties_t extends Structure {
	public int is_input_reshapeable;
	public int is_batch_size_variable;
	public int cur_batch_size;
	public int max_batch_size;
	public int has_prepared;
	public kestrel_nn_properties_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("is_input_reshapeable", "is_batch_size_variable", "cur_batch_size", "max_batch_size", "has_prepared");
	}
	public kestrel_nn_properties_t(int is_input_reshapeable, int is_batch_size_variable, int cur_batch_size, int max_batch_size, int has_prepared) {
		super();
		this.is_input_reshapeable = is_input_reshapeable;
		this.is_batch_size_variable = is_batch_size_variable;
		this.cur_batch_size = cur_batch_size;
		this.max_batch_size = max_batch_size;
		this.has_prepared = has_prepared;
	}
	public kestrel_nn_properties_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_nn_properties_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_nn_properties_t implements Structure.ByValue {
		
	};
}
