package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;

import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_io.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_io_buffer_t extends Structure {
	/** C type : uint8_t* */
	public Pointer buffer;
	public long size;
	public kestrel_io_buffer_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("buffer", "size");
	}
	/** @param buffer C type : uint8_t* */
	public kestrel_io_buffer_t(Pointer buffer, long size) {
		super();
		this.buffer = buffer;
		this.size = size;
	}
	public kestrel_io_buffer_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_io_buffer_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_io_buffer_t implements Structure.ByValue {
		
	};
}
