package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * JNA Wrapper for library <b>kestrel_device_def</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_device_defLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_device_defLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_device_defLibrary INSTANCE = (Kestrel_device_defLibrary)Native.load(Kestrel_device_defLibrary.JNA_LIBRARY_NAME, Kestrel_device_defLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_device_def.h</i><br>
	 * enum values
	 */
	public static interface kestrel_mem_trans_e {
		/** <i>native declaration : include/kestrel_device_def.h:37</i> */
		public static final int KESTREL_MEM_TRANSMISSION_UNKNOWN = 0;
		/** <i>native declaration : include/kestrel_device_def.h:39</i> */
		public static final int KESTREL_MEM_HOST_TO_DEVICE = 1;
		/** <i>native declaration : include/kestrel_device_def.h:41</i> */
		public static final int KESTREL_MEM_DEVICE_TO_HOST = 2;
		/** <i>native declaration : include/kestrel_device_def.h:43</i> */
		public static final int KESTREL_MEM_DEVICE_TO_DEVICE = 3;
		/** <i>native declaration : include/kestrel_device_def.h:45</i> */
		public static final int KESTREL_MEM_TRANSMISSION_AUTO = 4;
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public static final int KESTREL_DEVICE_PLUGIN = (int)(0);
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public static final String METHOD_DEVICE_GET_CONFIG = (String)"kdevice_get_config";
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public static final String METHOD_DEVICE_GET_ID = (String)"kdevice_get_id";
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public static final int KESTREL_DEV_INVALID = (int)(-1);
}
