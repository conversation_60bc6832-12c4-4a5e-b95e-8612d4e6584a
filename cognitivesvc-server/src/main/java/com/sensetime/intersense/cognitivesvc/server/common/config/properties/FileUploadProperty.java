package com.sensetime.intersense.cognitivesvc.server.common.config.properties;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.List;

/**
 * @Author: Cirmons
 * @Date: 2018-06-06
 */
@Component
@ConfigurationProperties(prefix = "file")
@Setter
public class FileUploadProperty {
    
    private String acceptImageFormat;
    private String acceptImageSize;

    private String offline;

    private String acceptFileFormat;
    private String acceptFileSize;
    
    @Getter
    private ImagePrehandleConfig imagePrehandleConfig;
    @Getter
    private Directory directory;

    public String getOffline() {
        return offline;
    }

    public String[] getAcceptImageFormats() {
        return acceptImageFormat.toLowerCase().split(",");
    }
    
    public String[] getAcceptFileFormats() {
        return acceptFileFormat.toLowerCase().split(",");
    }
    
    public long getAcceptImageSize() {
        return Long.parseLong(acceptImageSize);
    }
    
    public long getAcceptFileSize() {
        return Long.parseLong(acceptFileSize);
    }
    
    private long executeJSEngine(String command) {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        Object result = null;
        try {
            result = engine.eval(command);
        } catch (ScriptException e) {
            e.printStackTrace();
        }
        return ((Integer) result).longValue();
    }
    
    @Data
    public static class ImagePrehandleConfig {
        private Boolean enabled;
        private Boolean enabledSaveImage;
        private Boolean enableMultiface;
        private Boolean deleteFailedImage;
        private List<String> handleImageType;
        private FaceDetectConfig faceDetectConfig;
        private QualityDetectConfig qualityDetectConfig;
        
        @Data
        public static class FaceDetectConfig {
            private Boolean enabled;
            private Double faceEnlargeRate;
            private Boolean rotatedImageDetectEnabled;
            private Boolean resizeImageEnabled;
            private Integer resizeImageWidth;
        }
        
        @Data
        public static class QualityDetectConfig {
            private Double imageApproveScore;
        }
    }
    
    @Setter
    public static class Directory {
        // 图片目录
        private String tempImage;
        private String personImage;
        private String mapImage;
        private String senseIdImage;
        private String deviceImage;
        private String policymonitorImage;
        private String clipImage;
        private String vehicleImage;

        // 文件目录
        private String tempFile;
        private String logFile;
        private String videoFile;
        private String otaFile;
        private String passLogoFile;
        private String passBackGroundFile;

        private String xDynamicModelFile;
        private String offlineVideo;



    }
    
    
}
