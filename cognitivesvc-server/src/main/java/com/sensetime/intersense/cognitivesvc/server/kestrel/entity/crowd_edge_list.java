package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_edge_list extends Structure {
	/** 边的数量 */
	public int edge_num;
	/** C type : int32_t* */
	public Pointer start_indexes;
	/** C type : int32_t* */
	public Pointer end_indexes;
	public crowd_edge_list() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("edge_num", "start_indexes", "end_indexes");
	}
	/**
	 * @param edge_num 边的数量<br>
	 * @param start_indexes C type : int32_t*<br>
	 * @param end_indexes C type : int32_t*
	 */
	public crowd_edge_list(int edge_num, Pointer start_indexes, Pointer end_indexes) {
		super();
		this.edge_num = edge_num;
		this.start_indexes = start_indexes;
		this.end_indexes = end_indexes;
	}
	public crowd_edge_list(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_edge_list implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_edge_list implements Structure.ByValue {
		
	};
}