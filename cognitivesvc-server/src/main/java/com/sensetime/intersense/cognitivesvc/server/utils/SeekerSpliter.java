package com.sensetime.intersense.cognitivesvc.server.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.netflix.eureka.EurekaServiceInstance;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.appinfo.ApplicationInfoManager;
import com.netflix.appinfo.InstanceInfo.InstanceStatus;
import com.sensetime.intersense.cognitivesvc.server.controller.BaseProvider;
import com.sensetime.intersense.cognitivesvc.server.event.RebalancedEvent;
import com.sensetime.intersense.cognitivesvc.server.event.SeekSplitChangedEvent;


import io.swagger.v3.oas.annotations.Operation;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Component
@ConditionalOnExpression("${seeker.enabled:true}")
public class SeekerSpliter {
	
	@Value("${spring.application.name}")
	private String appName;
	
	@Autowired
	private DiscoveryClient discoveryClient;
	
	@Autowired
	private Broadcaster broadcastService;
	
	@Autowired
	private Utils paramUtils;
	
	@Autowired
	private ApplicationInfoManager infoManager;

	@Autowired
	private ApplicationContext applicationContext;
	
	@Getter
	private volatile SplitEntity splitEntity = SplitEntity.of(-1, 0);

	@Getter
	private volatile Map<String, SplitEntity> splitTopology = new HashMap<String, SplitEntity>();
	
	@Getter
	private volatile List<ServiceInstance> instanceTargets = new ArrayList<ServiceInstance>();
	
	@Order(value = RebalancedEvent.SeekerSplit)
	@EventListener(classes = {RebalancedEvent.class})
	void onApplicationEvent(RebalancedEvent event) throws Exception {
		if(!event.isMaster())
			return;

		
		List<ServiceInstance> instances = discoveryClient.getInstances(appName)
					 .stream()
					 .filter(item -> !(item instanceof EurekaServiceInstance) || ((EurekaServiceInstance)item).getInstanceInfo().getStatus() == InstanceStatus.UP)
					 .collect(Collectors.toList());
		
		int targetSplit = paramUtils.seekSplit;
		if(targetSplit < 0 || targetSplit > instances.size())
			targetSplit = instances.size();
		
		if(targetSplit == 0) {
			Map<String, SplitEntity> fullTopology = instances.stream().map(instance -> instance.getInstanceId()).collect(Collectors.toMap(Function.identity(), instance -> SplitEntity.FULL));
			broadcastService.postForObject(appName, "/cognitive/spliter/update/split/topology", fullTopology, String.class);
		}else {
			Map<String, SplitEntity> currentTopology = instances.parallelStream()
					 .collect(Collectors.toMap(
							instance -> instance.getInstanceId(), 
							instance -> {
								try {
									return RestUtils.restTemplate500ms.getForEntity("http://" + instance.getHost() + ":" + instance.getPort() + "/cognitive/spliter/query/split/entity", SplitEntity.class).getBody();
								}catch(Exception e) {
									return SplitEntity.ERROR;
								}
							}
					 ));
			
			for(Entry<String, SplitEntity> entry : currentTopology.entrySet())
				if(entry.getValue().getTotalSplitNum() != targetSplit || entry.getValue().getCurrentSplitNum() < 0 || entry.getValue().getCurrentSplitNum() >= targetSplit) 
					entry.setValue(SplitEntity.of(-1, targetSplit));
			
			for(int index = 0; index < targetSplit; index ++) {
				boolean found = false;
				for(SplitEntity entity : currentTopology.values())
					if(entity.getCurrentSplitNum() == index && entity.getTotalSplitNum() == targetSplit) {
						found = true;
						break;
					}
				
				if(found) 
					continue;
				
				for(Entry<String, SplitEntity> entry : currentTopology.entrySet())
					if(entry.getValue().getCurrentSplitNum() == -1) {
						entry.setValue(SplitEntity.of(index, targetSplit));
						break;
					}
			}
			
			broadcastService.postForObject(appName, "/cognitive/spliter/update/split/topology", currentTopology, String.class);
		}
	}
	
	public void setSplitTopology(Map<String, SplitEntity> splitTopology) {
		this.splitTopology = splitTopology;
		
		SplitEntity currentEntity = splitTopology.getOrDefault(infoManager.getInfo().getInstanceId(), SplitEntity.of(-1, 0));
		
		if(splitEntity.equals(currentEntity)) {
			//Nothing to do
		}else {
			this.splitEntity = currentEntity;
			applicationContext.publishEvent(new SeekSplitChangedEvent());
		}
		
		instanceTargets = discoveryClient.getInstances(appName)
			.stream()
			.filter(instance -> splitTopology.getOrDefault(instance.getInstanceId(), SplitEntity.ERROR).getCurrentSplitNum() >= 0)
			.collect(Collectors.toList());
	}
	
	@RestController("spliterProvider")
	@RequestMapping(value = "/cognitive/spliter/", produces = MediaType.APPLICATION_JSON_VALUE)
	@Tag(name = "SpliterProvider", description = "spliter controller")
	@ConditionalOnExpression("${seeker.enabled:true}")
	public static class SpliterProvider extends BaseProvider {

		@Autowired
		private SeekerSpliter seekerUtils;
		
		@Operation(summary = "设置split entity",method = "GET", hidden = true)
		@RequestMapping(value = "/query/split/entity", method = RequestMethod.GET)
		@ResponseBody
		public SplitEntity querySplitEntity() {
			return seekerUtils.getSplitEntity();
		}
		
		@Operation(summary = "设置split topology", method = "POST", hidden = true)
		@RequestMapping(value = "/update/split/topology", method = RequestMethod.POST)
		@ResponseBody
		public String updateSplitTopology(@RequestBody Map<String, SplitEntity> splitTopology) {
			seekerUtils.setSplitTopology(splitTopology);
			return "OK";
		}
	}

	@Getter
	@Setter
    @Builder
    @Accessors(chain = true)
	@EqualsAndHashCode
	public static class SplitEntity{
		private int currentSplitNum;
		private int totalSplitNum;
		
		private SplitEntity(int currentSplitNum, int totalSplitNum) {
			this.currentSplitNum = currentSplitNum;
			this.totalSplitNum = totalSplitNum;
		}

		public static final SplitEntity of(int currentSplitNum, int totalSplitNum) {
			return new SplitEntity(currentSplitNum, totalSplitNum);
		}
		
		public static final SplitEntity FULL = SplitEntity.of(0, 0);
		
		public static final SplitEntity ERROR = SplitEntity.of(-999, 0);
	}
	
	public static final String SPLIT_PREFIX = "seek_split_";
}
