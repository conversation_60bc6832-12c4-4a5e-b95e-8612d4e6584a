package com.sensetime.intersense.cognitivesvc.server.mapper;

import com.sensetime.intersense.cognitivesvc.server.entities.XDynamicModel;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
@Repository
public interface XDynamicModelRepository extends JpaRepositoryImplementation<XDynamicModel, String> {

    @Transactional
    @Query("update XDynamicModel x set x.runtime = ?1")
    @Modifying(flushAutomatically = true, clearAutomatically = true)
    public int updateRuntime(String runtime);

    @Transactional
    @Query("update XDynamicModel x set x.runtime = ?1 where x.annotatorName = ?2")
    @Modifying(flushAutomatically = true, clearAutomatically = true)
    public int updateRuntimeByAnnotatorName(String runtime, String whereAnnotatorName);

    @Transactional
    @Query("update XDynamicModel x set x.sts = ?1 where x.annotatorName = ?2")
    @Modifying(flushAutomatically = true, clearAutomatically = true)
    public int updatests(int sts, String whereAnnotatorName);

    @Transactional
    @Query("update XDynamicModel x set x.imgSaveTag = ?1 where x.annotatorName = ?2")
    @Modifying(flushAutomatically = true, clearAutomatically = true)
    public int updateImgSaveTag(int imgSaveTag, String whereAnnotatorName);

}
