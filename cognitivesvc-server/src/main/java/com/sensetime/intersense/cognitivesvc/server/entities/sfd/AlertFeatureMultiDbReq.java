package com.sensetime.intersense.cognitivesvc.server.entities.sfd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 多数据库检索请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertFeatureMultiDbReq {
    
    private Image image;
    private float[] feature;
    private String blob;
    private ReqFeature features;
    private String feature_type;
    private Integer feature_version;
    private List<DbInfo> dbs;
    private String type;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Image {
        private ImageData image;
        private String face_selection;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageData {
        private String data;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DbInfo {
        private String db_id;
        private Integer top_k;
        private Float min_score;
    }
}
