package com.sensetime.intersense.cognitivesvc.server.entities.sfd;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.List;

/**
 * 1:n识别返回结果
 */
@Data
@Builder
public class FaceRecognitionResponse {

    // 1：n 接口响应结果
    private List<RecognitionResponse> results;
    // 识别结果
    private List<RecognitionResult> feature_results;
    // 底库id，是dbId
    private String col_id;
    private boolean is_refined;

    @Data
    public static class RecognitionResponse {
        // 0为正常
        private int code;
        private String error;
        // OK为正常
        private String status;
    }

    @Data
    public static class RecognitionResult {
        // 底库数据集合
        private List<BaselineData> results;
    }
    @Data
    public static class BaselineData {
        // 具体底库数据
        private BaselineItem item;
        // 相似度 0-1
        private Double score;
    }
    @Data
    public static class BaselineItem {
        // imageId 对应的是person_image的uuid
        private String image_id;
        // personId
        private String key;

        private String extra_info;
    }

    @Tolerate
    public FaceRecognitionResponse(){}

}