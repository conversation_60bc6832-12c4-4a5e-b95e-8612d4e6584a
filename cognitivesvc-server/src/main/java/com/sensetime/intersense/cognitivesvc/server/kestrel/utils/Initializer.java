package com.sensetime.intersense.cognitivesvc.server.kestrel.utils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.Objects;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import com.sensetime.intersense.cognitivesvc.server.utils.HostUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Initializer {
	public static enum DeviceType{ HOST, P4, T4, A2, A16, STPU, L4 }

	public static enum CudaType{ HOST, CUDA110, CUDA114, STPU, CUDA121 }

	public static final String deviceId = Utils.getProperty("DeviceId", "0");
	public static final DeviceType deviceType;
	public static final CudaType   cudaType;
	public static final int        batchSize;

	public static final String licenseFile;
	public static final String licenseProd = Utils.getProperty("SDK_LIC_PROD");
	public static final String licenseCode = Utils.getProperty("SDK_LIC_CODE");

	public static String  deviceName;
	public static String  demuxerName;
	public static String  decoderName;
	public static String  encoderName;
	public static boolean initialized;
	public static String  zipLibPath;

	public static final Map<String, String> modelPathMap = new HashMap<String, String>();

	private static final ThreadLocal<String> threadDeviceName = ThreadLocal.withInitial(() -> KestrelApi.kestrel_device_get_name(deviceName));

	static {
		DeviceType dType = DeviceType.HOST;
		try {
			dType = DeviceType.valueOf(Utils.getProperty("DeviceType").toUpperCase());
		}catch(Exception e) {
			log.warn("*****************************************************************");
			log.warn("***************using ENV [--env DeviceType=HOST]*****************");
			log.warn("**************option (HOST, P4, T4, A2, A16, STPU)***************");
			log.warn("*****************************************************************");
			log.warn("*********************RUNNING ON HOST MODE************************");
			log.warn("*****************************************************************");
		}
		deviceType = dType;

		CudaType cType = CudaType.HOST;
		try {
			cType = CudaType.valueOf(Utils.getProperty("CudaType").toUpperCase());
		}catch(Exception e) {
			switch(deviceType) {
				case HOST: cType = CudaType.HOST;    break;
				case P4:   cType = CudaType.CUDA110;  break;
				case T4:   cType = CudaType.CUDA110;  break;
				case A2:   cType = CudaType.CUDA114; break;
				case A16:  cType = CudaType.CUDA114; break;
				case STPU: cType = CudaType.STPU;    break;
				case L4:   cType = CudaType.CUDA121; break;
				default:
					break;
			}
		}
		cudaType = cType;

		String batch = Utils.getProperty("BatchSize");
		if(StringUtils.isBlank(batch)) {
			switch(deviceType) {
				case HOST: batchSize = 1;  break;
				case P4:   batchSize = 16; break;
				case T4:   batchSize = 32; break;
				case A2:   batchSize = 32; break;
				case A16:  batchSize = 32; break;
				case STPU: batchSize = 8;  break;
				case L4:   batchSize = 32; break;
				default:
					batchSize = 1;  break;
			}
		}else {
			batchSize = Integer.parseInt(batch);
		}

		String libPath, modelPath;

		if(StringUtils.isBlank(Utils.getProperty("LibPath"))) {
			libPath = "/kestrel/kestrel_lib/" + cudaType.toString().toLowerCase() + "/";

			log.warn("*****************************************************************");
			log.warn("*LibPath is blank, use default [" + libPath + "].*");
			log.warn("*****************************************************************");
		}else {
			libPath = Utils.getProperty("LibPath");

			log.warn("*****************************************************************");
			log.warn("*using LibPath [" + libPath + "].*");
			log.warn("*****************************************************************");
		}

		if(!new File(libPath).exists())
			throw new RuntimeException("[" + libPath + "] does not exist.");

		if(StringUtils.isBlank(Utils.getProperty("ModelPath"))) {
			String modelType = List.of(deviceType.name(), cudaType.name()).stream().distinct().collect(Collectors.joining());
			modelPath = "/kestrel/kestrel_model/" + modelType.toLowerCase() + "/";

			log.warn("*****************************************************************");
			log.warn("*ModelPath is blank, use default [" + modelPath + "].*");
			log.warn("*****************************************************************");
		}else {
			modelPath = Utils.getProperty("ModelPath");

			log.warn("*****************************************************************");
			log.warn("*using ModelPath [" + modelPath + "].*");
			log.warn("*****************************************************************");
		}

		if(!new File(modelPath).exists())
			throw new RuntimeException("[" + modelPath + "] does not exist.");

		String lFile = Utils.getProperty("SDK_LIC_FILE");
		if(StringUtils.isBlank(lFile))
			licenseFile = "/kestrel/other/FOREVER.lic";
		else
			licenseFile = lFile;

		System.setProperty("jna.library.path", "/usr/cognitivesvc/");

		File libFile = new File(libPath);
		for(String subFile : libFile.list())
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + libPath + "/" + subFile + " " + "/usr/cognitivesvc/" + subFile + ";"});


		File flockFile = new File(libPath + "/flock_modules");
		if(flockFile.exists())
			for(String subFile : flockFile.list())
				HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + libPath+ "/flock_modules/" + subFile + " " + "/usr/cognitivesvc/" + subFile + ";"});

		File modelFile = new File(modelPath);
		for(String subFile : modelFile.list())
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ln -sf " + modelPath + "/" + subFile + " " + "/usr/cognitivesvc/" + subFile + ";"});


		String zipLibPathEnv = Utils.getProperty("zipLibPath");
		if(StringUtils.isBlank(zipLibPathEnv)) {
			zipLibPath =  "/kestrel/kestrel_lib/" + cudaType.toString().toLowerCase() +"/zipLib";
		}else{
			zipLibPath =  zipLibPathEnv;
		}
	}

	static {
		if(deviceType == DeviceType.A2 && cudaType == CudaType.CUDA114) {
			//A2 CUDA114
			modelPathMap.put("hunter_common_module",      "KM_hunter_Hunter_Common_nart_cuda11.4-trt8.4.1-int8-A2_b8_0.0.4.model");
			modelPathMap.put("hunter_small_module",       "KM_hunter_SmallFace_Gray_nart_cuda11.4-trt8.4.1-fp16-A2_b24_4.15.0.model");
			modelPathMap.put("hunter_large_module",       "KM_hunter_LargeFace_Gray_nart_cuda11.4-trt8.4.1-int8-A2_b64_9.6.6.model");
			modelPathMap.put("aligner_106_module",        "KM_aligner_deepface_nart_cuda11.4-trt8.4.1-fp16-A2_b64_2.17.2.model");
			modelPathMap.put("blur_module",               "KM_blur_gray_nart_cuda11.4-trt8.4.1-fp16-A2_b16_6.0.2.model");
			modelPathMap.put("feature_module",            "KM_feature_face-trt_nart_cuda11.4-trt8.4.1-int8-A2_b32_2.54.0.model");
//            modelPathMap.put("headpose_module",           "KM_headpose_headpose_nart_cuda11.4-trt8.4.1-fp16-A2_b64_1.0.1.model");
			modelPathMap.put("headpose_module",           "KM_headpose__nart_cuda11.4-trt8.4.1-fp32-A2_b64_1.0.1.model"); // oid: 55f52a909f789f89a29c6f43372e936a2d29fd2669ec91e8184cbe33ab4218e0
			modelPathMap.put("attribute_classify_module", "KM_attribute_face_nart_cuda11.4-trt8.4.1-int8-A2_b32_3.4.0.model");
			modelPathMap.put("classifier_quality_module", "KM_classifier_Face_Quality_Security_nart_cuda11.4-trt8.4.1-fp16-A2_b64_3.1.0.model");
			modelPathMap.put("attribute_mtnet_module",    "");

			modelPathMap.put("harpy_module",              "");
			modelPathMap.put("hermes_module",             "");
			modelPathMap.put("horae_module",              "");

			modelPathMap.put("pageant_module",            "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.4.1-int8-A2_b16_1.0.8.model");
			modelPathMap.put("ped_filter_module",         "KM_classifier_ped_filter_gray_nart_cuda11.4-trt8.4.1-int8-A2_b64_3.0.1.model");
			modelPathMap.put("ped_attribute_module",      "KM_classifier_Ped_32of32_nart_cuda11.4-trt8.4.1-int8-A2_b16_2.12.0.model");
			modelPathMap.put("ped_attribute_extraction",  "KM_classifier_Ped_32of32_nart_cuda11.4-trt8.4.1-int8-A2_b16_2.12.0.model");
			modelPathMap.put("senu_body_feature_module",  "KM_senu_Senu_nart_cuda11.4-trt8.4.1-int8-A2_b32_7.0.1.model");
			modelPathMap.put("senu_feature_module",       "KM_senu_reid_156_nart_cuda11.4-trt8.4.1-int8-A2_b32_1.56.0.model");
			modelPathMap.put("facebody_module",           "KM_hunter_facebody_nart_cuda11.4-trt8.4.1-fp16-A2_b32_4.2.4.model");


			modelPathMap.put("classifier_blur_module_scg",    "KM_classifier_Face_Blur_Security_nart_cuda11.4-trt8.4.1-int8-A2_b64_3.1.0.model");
			modelPathMap.put("hunter_small_module_scg",       "KM_hunter_SmallFace_Gray_nart_cuda11.4-trt8.4.1-int8-A2_b64_4.15.1.model");
			modelPathMap.put("face_classifier_module_scg",    "KM_classifier_FaceNoFace_cls_sz_nart_cuda11.4-trt8.4.1-int8-A2_b32_1.0.2.model");
			modelPathMap.put("aligner_106_module_scg",        "KM_aligner_occlusion_106_nart_cuda11.4-trt8.4.1-int8-A2_b64_1.20.0.model");
			modelPathMap.put("pageant_module_scg",            "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.4.1-int8-A2_b16_1.0.8.model");
			modelPathMap.put("feature_face_module_scg",       "KM_feature_face-trt_nart_cuda11.4-trt8.4.1-int8-A2_b32_2.54.0.model");

		}else if(deviceType == DeviceType.A16 && cudaType == CudaType.CUDA114) {
			//A16 CUDA11
			modelPathMap.put("hunter_common_module",      "");
			modelPathMap.put("hunter_small_module",       "KM_hunter_SmallFace_Gray_nart_cuda11.4-trt8.2.0-fp16-A16_b64_4.13.2.model");
			modelPathMap.put("hunter_large_module",       "");
			modelPathMap.put("aligner_106_module",        "KM_aligner_deepface_nart_cuda11.4-trt8.2.0-fp16-A16_b64_2.17.2.model");
			modelPathMap.put("blur_module",               "KM_blur_gray_nart_cuda11.4-trt8.2.0-fp16-A16_b64_6.0.2.model");
			modelPathMap.put("feature_module",            "KM_feature_face_nart_cuda11.4-trt8.2.0-int8-A16_b32_2.51.0.model");
			modelPathMap.put("headpose_module",           "KM_headpose_nart_cuda11.4-trt8.2.0-fp16-A16_b64_1.0.1.model");
			modelPathMap.put("attribute_classify_module", "KM_attribute_face_nart_cuda11.4-trt8.2.0-int8-A16_b32_3.3.1.model");
			modelPathMap.put("attribute_mtnet_module",    "");

			modelPathMap.put("harpy_module",              "");
			modelPathMap.put("hermes_module",             "");
			modelPathMap.put("horae_module",              "");

			modelPathMap.put("pageant_module",            "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.2.0-fp16-A16_b32_1.0.8.model");
			modelPathMap.put("ped_filter_module",         "KM_classifier_ped_filter_gray_nart_cuda11.4-trt8.2.0-fp16-A16_b64_3.0.1.model");
			modelPathMap.put("ped_attribute_module",      "KM_classifier_Ped_32of32_nart_cuda11.4-trt8.2.0-fp16-A16_b16_2.13.0.model");
			modelPathMap.put("senu_feature_module",       "KM_senu_ped_nart_cuda11.4-trt8.2.0-int8-A16_b32_1.5.0.model");
			modelPathMap.put("facebody_module",           "KM_hunter_facebody_nart_cuda11.4-trt8.2.0-fp16-A16_b32_4.0.8.model");
		}else if(deviceType == DeviceType.T4 && cudaType == CudaType.CUDA110){
			//T4 CUDA110
			modelPathMap.put("hunter_common_module",      "KM_hunter_Hunter_Common_nart_cuda11.0-trt7.1-int8-T4_b8_0.0.4.model");
			modelPathMap.put("hunter_small_module",       "KM_hunter_SmallFace_Gray_EQHist_nart_cuda11.0-trt7.1-int8-T4_b24_4.15.0.model");
			modelPathMap.put("hunter_large_module",       "KM_hunter_LargeFace_Gray_nart_cuda11.0-trt7.1-int8-T4_b64_9.6.6.model");
			modelPathMap.put("aligner_106_module",        "KM_aligner_deepface_nart_cuda11.0-trt7.1-fp16-T4_b64_2.17.2.model");
			modelPathMap.put("blur_module",               "KM_blur_gray_nart_cuda11.0-trt7.1-fp16-T4_b64_6.0.2.model");
			modelPathMap.put("feature_module",            "KM_feature_face_nart_cuda11.0-trt7.1-int8-T4_b32_2.52.0.model");
			modelPathMap.put("headpose_module",           "KM_headpose__nart_cuda11.0-trt7.1-fp16-T4_b64_1.0.1.model");
			modelPathMap.put("attribute_classify_module", "KM_attribute_face_nart_cuda11.0-trt7.1-int8-T4_b64_3.3.1.model");
			modelPathMap.put("attribute_mtnet_module",    "KM_Attribute_MTNet_2.1.0.model");
			modelPathMap.put("classifier_quality_module", "KM_classifier_Face_Quality_Security_nart_cuda11.0-trt7.1-int8-T4_b64_3.1.0.model");

			modelPathMap.put("harpy_module",              "KM_harpy_struct_nart_cuda11.0-trt7.1-fp16-T4_b8_8.4.1.model");
			modelPathMap.put("hermes_module",             "KM_hermes_surv_nart_cuda11.0-trt7.1-int8-T4_b128_3.1.0.model");
			modelPathMap.put("horae_module",              "KM_horae_Horae_fp32_nart_cpu-nart-fp32_b1_1.6.0.model");

			modelPathMap.put("pageant_module",            "KM_pageant_Pageant_face300_mbn3sigmoid_nart_cuda11.0-trt7.1-fp16-T4_b16_1.1.0.model");
			modelPathMap.put("ped_filter_module",         "KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.1.model");
			modelPathMap.put("ped_attribute_module",      "KM_classifier_Ped_33of33_nart_cuda11.0-trt7.1-int8-T4_b16_2.17.0.model");
			modelPathMap.put("ped_attribute_extraction",  "KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-T4_b64_3.0.1.model");

			modelPathMap.put("senu_body_feature_module",  "KM_senu_Senu_nart_cuda11.0-trt7.1-int8-T4_b32_7.0.1.model");
			modelPathMap.put("senu_feature_module",       "KM_senu_ped_nart_cuda11.0-trt7.1-int8-T4_b32_1.54.0.model");
			modelPathMap.put("facebody_module",           "KM_hunter_facebody_nart_cuda11.0-trt7.1-fp16-T4_b32_4.2.2.model");
			modelPathMap.put("essos_struct_module", "KM_essos_struct_nart_cuda11.0-trt7.1-fp16-T4_b8_3.0.0.model");
			modelPathMap.put("aligner_occlusion_module","KM_aligner_occlusion_106_nart_cuda11.0-trt7.1-fp16-T4_b64_1.20.0.model");
			modelPathMap.put("attribute_Attribute_module","KM_attribute_Attribute_nart_cuda11.0-trt7.1-fp16-T4_b64_3.0.2.model");

			modelPathMap.put("classifier_blur_module_scg",    "KM_classifier_Face_Blur_Security_nart_cuda11.0-trt7.1-int8-T4_b64_3.1.0.model");
			modelPathMap.put("hunter_small_module_scg",       "KM_hunter_SmallFace_Gray_nart_cuda11.0-trt7.1-int8-T4_b48_4.15.1.model");
			modelPathMap.put("face_classifier_module_scg",    "KM_classifier_FaceNoFace_cls_sz_nart_cuda11.0-trt7.1-int8-T4_b32_1.0.2.model");
			modelPathMap.put("aligner_106_module_scg",        "KM_aligner_occlusion_106_nart_cuda11.0-trt7.1-fp16-T4_b64_1.20.0.model");
			modelPathMap.put("pageant_module_scg",            "KM_pageant_88id_nart_cuda11.0-trt7.1-int8-T4_b16_1.0.8.model");
			modelPathMap.put("feature_face_module_scg",       "KM_feature_face_nart_cuda11.0-trt7.1-int8-T4_b32_2.52.0.model");

		}else if(deviceType == DeviceType.P4 && cudaType == CudaType.CUDA110) {
			//P4 CUDA11
			modelPathMap.put("hunter_common_module",      "KM_hunter_Common_nart_cuda11.0-trt7.1-int8-P4_b8_0.0.4.model");
			modelPathMap.put("hunter_small_module",       "KM_hunter_smallface_gray_nart_cuda11.0-trt7.1-int8-P4_b24_4.12.0.model");
			modelPathMap.put("hunter_large_module",       "KM_hunter_largeface_gray_nart_cuda11.0-trt7.1-int8-P4_b32_9.4.0.model");
			modelPathMap.put("aligner_106_module",        "KM_aligner_deepface_nart_cuda11.0-trt7.1-fp32-P4_b32_2.17.2.model");
			modelPathMap.put("blur_module",               "KM_blur_gray_nart_cuda11.0-trt7.1-fp32-P4_b32_6.0.2.model");
			modelPathMap.put("feature_module",            "KM_feature_nart_cuda11.0-trt7.1-int8-P4_b32_2.50.0.model");
			modelPathMap.put("headpose_module",           "KM_headpose_nart_cuda11.0-trt7.1-fp32-P4_b32_1.0.1.model");
			modelPathMap.put("attribute_classify_module", "KM_attribute_nart_cuda11.0-trt7.1-fp32-P4_b32_3.0.2.model");
			modelPathMap.put("attribute_mtnet_module",    "KM_Attribute_MTNet_2.1.0.model");

			modelPathMap.put("harpy_module",              "");
			modelPathMap.put("hermes_module",             "");
			modelPathMap.put("horae_module",              "");

			modelPathMap.put("pageant_module",            "KM_pageant_88id_nart_cuda11.0-trt7.1-int8-P4_b16_1.0.8.model");
			modelPathMap.put("ped_filter_module",         "KM_classifier_ped_filter_gray_nart_cuda11.0-trt7.1-int8-P4_b32_3.0.1.model");
			modelPathMap.put("ped_attribute_module",      "KM_classifier_Ped_32of32_nart_cuda11.0-trt7.1-int8-P4_b16_2.12.0.model");
			modelPathMap.put("senu_feature_module",       "KM_senu_ped_nart_cuda11.0-trt7.1-int8-P4_b32_1.5.0.model");
			modelPathMap.put("facebody_module",           "KM_hunter_facebody_nart_cuda11.0-trt7.1-fp32-P4_b8_4.0.8.model");
		}else if(deviceType == DeviceType.HOST) {
			//CPU
			modelPathMap.put("hunter_common_module",      "KM_hunter_Hunter_Common_nart_cpu-ppl2-fp32_b1_0.0.4.model");
			modelPathMap.put("hunter_small_module",       "KM_hunter_SmallFace_Gray_nart_cpu-ppl2-fp32_b1_4.13.1.model");
			modelPathMap.put("hunter_large_module",       "KM_hunter_largeFace_Gray_nart_cpu-ppl2-fp32_b1_9.6.1.model");
			modelPathMap.put("aligner_106_module",        "KM_aligner_deepface_nart_cpu-ppl2-fp32_b1_2.17.2.model");
			modelPathMap.put("feature_module",            "KM_feature_face_nart_cpu-nart-fp32_b1_2.51.0.model");
			modelPathMap.put("blur_module",               "KM_blur_gray_nart_cpu-ppl2-fp32_b1_6.0.2.model");
			modelPathMap.put("headpose_module",           "KM_headpose__nart_cpu-ppl2-fp32_b1_1.0.1.model");
			modelPathMap.put("attribute_classify_module", "KM_attribute_face_nart_cpu-ppl2-fp32_b1_3.3.1.model");
			modelPathMap.put("attribute_mtnet_module",    "KM_Attribute_MTNet_ppl_2.1.0.model");
			modelPathMap.put("harpy_module",              "");
			modelPathMap.put("hermes_module",             "");
			modelPathMap.put("horae_module",              "");
			modelPathMap.put("classifier_quality_module", "KM_classifier_Face_Quality_Security_nart_cpu-ppl2-fp32_b64_3.1.0.model");

			modelPathMap.put("pageant_module",            "KM_pageant_r88stretch_nart_cpu-ppl2-fp32_b16_1.0.7.model");
			modelPathMap.put("ped_filter_module",         "");
			modelPathMap.put("ped_attribute_module",      "");
			modelPathMap.put("ped_attribute_extraction",  "");
			modelPathMap.put("senu_feature_module",       "");
			modelPathMap.put("facebody_module",           "");

			modelPathMap.put("classifier_blur_module_scg",    "KM_classifier_Face_Blur_Security_nart_cpu-ppl2-fp32_b32_3.1.0.model");
			modelPathMap.put("hunter_small_module_scg",       "KM_hunter_SmallFace_Gray_nart_cpu-ppl2-fp32_b1_4.15.1.model");
			modelPathMap.put("face_classifier_module_scg",    "KM_classifier_FaceNoFace_cls_sz_nart_cpu-ppl2-fp32_b1_1.0.2.model");
			modelPathMap.put("aligner_106_module_scg",        "KM_aligner_deepface_nart_cpu-ppl2-fp32_b1_2.17.2.model");
			modelPathMap.put("pageant_module_scg",            "KM_pageant_88id_nart_cpu-ppl2-fp32_b16_1.0.8.model");
			modelPathMap.put("feature_face_module_scg",       "KM_feature_face_nart_cpu-ppl2-fp32_b1_2.54.0.model");

		}else if(deviceType == DeviceType.T4 && cudaType == CudaType.CUDA114){
			//T4 CUDA114
			modelPathMap.put("hunter_common_module",      "");
			modelPathMap.put("hunter_small_module",       "KM_hunter_SmallFace_Gray_nart_cuda11.4-trt8.4.1-int8-T4_b24_4.15.0.model");
//			modelPathMap.put("hunter_large_module",       "KM_hunter_LargeFace_Gray_nart_cuda11.4-trt8.4.1-int8-T4_b16_9.6.6.model");
			modelPathMap.put("hunter_large_module",       "KM_hunter_LargeFace_Gray_nart_cuda11.4-trt8.4.1-int8-T4_b64_9.6.6.model"); // OID: 70bdf2c3cc69ced8269833fcc6cd631f8e3d18f8a82aa033c62d239ece4bcfee
			modelPathMap.put("aligner_106_module",        "KM_aligner_deepface_nart_cuda11.4-trt8.4.1-fp16-T4_b64_2.17.2.model");
			modelPathMap.put("blur_module",               "KM_blur_gray_nart_cuda11.4-trt8.4.1-fp16-T4_b16_6.0.2.model");
			modelPathMap.put("feature_module",            "KM_feature_face-trt_nart_cuda11.4-trt8.4.1-int8-T4_b32_2.54.0.model");
//			modelPathMap.put("headpose_module",           "KM_headpose_headpose_nart_cuda11.4-trt8.4.1-fp16-T4_b64_1.0.1.model");
			modelPathMap.put("headpose_module",           "KM_headpose__nart_cuda11.4-trt8.4.1-fp32-T4_b64_1.0.1.model"); // oid: b78c76accb31246452cbed3fecd50811cba8a4391fd0139c72a6e897eef6370e
			modelPathMap.put("attribute_classify_module", "KM_attribute_face_nart_cuda11.4-trt8.4.1-int8-T4_b16_3.4.0.model");
			modelPathMap.put("attribute_mtnet_module",    "");
			modelPathMap.put("classifier_quality_module", "KM_classifier_Face_Quality_Security_nart_cuda11.4-trt8.4.1-fp16-T4_b16_3.1.0.model");

			modelPathMap.put("harpy_module",              "");
			modelPathMap.put("hermes_module",             "");//人体的暂时没有
			modelPathMap.put("horae_module",              "");

			modelPathMap.put("pageant_module",            "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.4.1-int8-T4_b16_1.0.8.model");
			modelPathMap.put("ped_filter_module",         "KM_classifier_ped_filter_gray_nart_cuda11.4-trt8.4.1-int8-T4_b64_3.0.1.model");
			modelPathMap.put("ped_attribute_module",      "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.4.1-int8-T4_b16_1.0.8.model");
			modelPathMap.put("ped_attribute_extraction",  "KM_classifier_Ped_32of32_nart_cuda11.4-trt8.4.1-int8-T4_b16_2.12.0.model");


			modelPathMap.put("senu_body_feature_module",   "KM_senu_Senu_nart_cuda11.4-trt8.4.1-int8-T4_b32_7.0.1.model");
			modelPathMap.put("senu_feature_module",        "KM_senu_reid_156_nart_cuda11.4-trt8.4.1-int8-T4_b32_1.56.0.model");
			modelPathMap.put("facebody_module",            "KM_hunter_facebody_nart_cuda11.4-trt8.4.1-fp16-T4_b32_4.2.4.model");


			modelPathMap.put("classifier_blur_module_scg",    "KM_classifier_Face_Blur_Security_nart_cuda11.4-trt8.4.1-int8-T4_b64_3.1.0.model");
			modelPathMap.put("hunter_small_module_scg",       "KM_hunter_SmallFace_Gray_nart_cuda11.4-trt8.4.1-int8-T4_b48_4.15.1.model");
			modelPathMap.put("face_classifier_module_scg",    "KM_classifier_FaceNoFace_cls_sz_nart_cuda11.4-trt8.4.1-int8-T4_b32_1.0.2.model");
			modelPathMap.put("aligner_106_module_scg",        "KM_aligner_occlusion_106_nart_cuda11.4-trt8.4.1-int8-T4_b64_1.20.0.model");
			modelPathMap.put("pageant_module_scg",            "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.4.1-int8-T4_b16_1.0.8.model");
			modelPathMap.put("feature_face_module_scg",       "KM_feature_face-trt_nart_cuda11.4-trt8.4.1-int8-T4_b32_2.54.0.model");

		}else if(deviceType == DeviceType.L4 && cudaType == CudaType.CUDA121){
			//L4 CUDA121 ： 部分模型暂未适配，第一波 仅适配了通行场景后端识别 for disney项目
			modelPathMap.put("hunter_common_module",      "");
			modelPathMap.put("hunter_small_module",       "KM_hunter_SmallFace_Gray_nart_cuda12.1-trt8.6.0-fp16-L4_b48_4.15.1.model");
			modelPathMap.put("hunter_large_module",       "KM_hunter_LargeFace_Gray_nart_cuda12.1-trt8.6.0-int8-L4_b64_9.6.6.model");
			modelPathMap.put("aligner_106_module",        "KM_aligner_deepface_nart_cuda12.1-trt8.6.0-fp16-L4_b64_2.17.2.model");
			modelPathMap.put("blur_module",               "KM_blur_gray_nart_cuda12.1-trt8.6.0-fp16-L4_b16_6.0.2.model");
			modelPathMap.put("feature_module",            "KM_feature_face-trt_nart_cuda12.1-trt8.6.0-int8-L4_b32_2.54.0.model");
//			modelPathMap.put("feature_module",            "KM_feature_face-chs4-trt-qdrop2048_nart_cuda12.1-trt8.6.0-int8-L4_b32_2.52.0.model");
			modelPathMap.put("headpose_module",           "KM_headpose__nart_cuda12.1-trt8.6.0-fp16-L4_b64_1.0.1.model");
			modelPathMap.put("attribute_classify_module", "KM_attribute_face_nart_cuda12.1-trt8.6.0-int8-L4_b64_3.3.1.model");
			modelPathMap.put("attribute_mtnet_module",    "");
			modelPathMap.put("classifier_quality_module", "KM_classifier_Face_Quality_Security_nart_cuda12.1-trt8.6.0-int8-L4_b64_3.1.0.model");

			modelPathMap.put("harpy_module",              "");
			modelPathMap.put("hermes_module",             "");//人体的暂时没有
			modelPathMap.put("horae_module",              "");

			modelPathMap.put("pageant_module",            "KM_pageant_88id_nart_cuda12.1-trt8.6.0-int8-L4_b16_1.0.8.model");
			modelPathMap.put("ped_filter_module",         "KM_classifier_ped_filter_gray_nart_cuda12.1-trt8.6.0-int8-L4_b64_3.0.1.model");
			modelPathMap.put("ped_attribute_module",      "KM_pageant_88id_nart_cuda12.1-trt8.6.0-int8-L4_b16_1.0.8.model");
			modelPathMap.put("ped_attribute_extraction",  "KM_classifier_Ped_32of32_nart_cuda12.1-trt8.6.0-int8-L4_b16_2.14.0.model");

			modelPathMap.put("senu_body_feature_module",   "KM_senu_Senu_nart_cuda12.1-trt8.6.0-int8-L4_b32_7.0.1.model");
			modelPathMap.put("senu_feature_module",        "KM_senu_reid_156_nart_cuda12.1-trt8.6.0-int8-L4_b32_1.56.0.model");
			modelPathMap.put("facebody_module",            "KM_hunter_facebody_nart_cuda12.1-trt8.6.0-fp16-L4_b32_4.2.2.model");

			modelPathMap.put("classifier_blur_module_scg",    "KM_classifier_Face_Blur_Security_nart_cuda12.1-trt8.6.0-int8-L4_b64_3.1.1.model");
			modelPathMap.put("hunter_small_module_scg",       "KM_hunter_SmallFace_Gray_nart_cuda12.1-trt8.6.0-fp16-L4_b48_4.15.1.model");
			modelPathMap.put("face_classifier_module_scg",    "KM_classifier_FaceNoFace_cls_sz_nart_cuda12.1-trt8.6.0-int8-L4_b32_1.0.2.model");
			modelPathMap.put("aligner_106_module_scg",        "KM_aligner_occlusion_106_nart_cuda12.1-trt8.6.0-fp16-L4_b64_1.20.0.model");
			modelPathMap.put("pageant_module_scg",            "KM_pageant_Pageant_88id_nart_cuda12.1-trt8.6.0-int8-L4_b128_1.0.8.model");
			modelPathMap.put("feature_face_module_scg",       "KM_feature_face-trt_nart_cuda12.1-trt8.6.0-int8-L4_b32_2.54.0.model");
//			modelPathMap.put("feature_face_module_scg",       "KM_feature_face-chs4-trt-qdrop2048_nart_cuda12.1-trt8.6.0-int8-L4_b32_2.52.0.model");
		}else if(deviceType == DeviceType.T4 && cudaType == CudaType.CUDA121){
			//T4 CUDA114
			modelPathMap.put("hunter_common_module",      "");
			modelPathMap.put("hunter_small_module",       "KM_hunter_SmallFace_Gray_nart_cuda12.1-trt8.6.0-fp16-L4_b48_4.15.1.model");
			modelPathMap.put("hunter_large_module",       "KM_hunter_LargeFace_Gray_nart_cuda12.1-trt8.6.0-int8-T4_b64_9.6.6.model");
			modelPathMap.put("aligner_106_module",        "KM_aligner_deepface_nart_cuda12.1-trt8.6.0-fp16-L4_b64_2.17.2.model");
			modelPathMap.put("blur_module",               "KM_blur_gray_nart_cuda12.1-trt8.6.0-fp16-T4_b16_6.0.2.model");
			modelPathMap.put("feature_module",            "KM_feature_face-trt_nart_cuda12.1-trt8.6.0-int8-T4_b32_2.54.0.model");
			modelPathMap.put("headpose_module",           "KM_headpose__nart_cuda12.1-trt8.6.0-fp32-T4_b64_1.0.1.model");
			modelPathMap.put("attribute_classify_module", "KM_attribute_face_nart_cuda11.4-trt8.4.1-int8-T4_b16_3.4.0.model");
			modelPathMap.put("attribute_mtnet_module",    "");
			modelPathMap.put("classifier_quality_module", "KM_classifier_Face_Quality_Security_nart_cuda11.4-trt8.4.1-fp16-T4_b16_3.1.0.model");

			modelPathMap.put("harpy_module",              "");
			modelPathMap.put("hermes_module",             "");//人体的暂时没有
			modelPathMap.put("horae_module",              "");

			modelPathMap.put("pageant_module",            "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.4.1-int8-T4_b16_1.0.8.model");
			modelPathMap.put("ped_filter_module",         "KM_classifier_ped_filter_gray_nart_cuda11.4-trt8.4.1-int8-T4_b64_3.0.1.model");
			modelPathMap.put("ped_attribute_module",      "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.4.1-int8-T4_b16_1.0.8.model");
			modelPathMap.put("ped_attribute_extraction",  "KM_classifier_Ped_32of32_nart_cuda11.4-trt8.4.1-int8-T4_b16_2.12.0.model");

			modelPathMap.put("senu_body_feature_module",   "KM_senu_Senu_nart_cuda11.4-trt8.4.1-int8-T4_b32_7.0.1.model");
			modelPathMap.put("senu_feature_module",        "KM_senu_reid_156_nart_cuda12.1-trt8.6.0-int8-T4_b32_1.56.0.model");
			modelPathMap.put("facebody_module",            "KM_hunter_facebody_nart_cuda11.4-trt8.4.1-fp16-T4_b32_4.2.4.model");

			modelPathMap.put("classifier_blur_module_scg",    "KM_classifier_Face_Blur_Security_nart_cuda11.4-trt8.4.1-int8-T4_b64_3.1.0.model");
			modelPathMap.put("hunter_small_module_scg",       "KM_hunter_SmallFace_Gray_nart_cuda11.4-trt8.4.1-int8-T4_b48_4.15.1.model");
			modelPathMap.put("face_classifier_module_scg",    "KM_classifier_FaceNoFace_cls_sz_nart_cuda11.4-trt8.4.1-int8-T4_b32_1.0.2.model");
			modelPathMap.put("aligner_106_module_scg",        "KM_aligner_occlusion_106_nart_cuda11.4-trt8.4.1-int8-T4_b64_1.20.0.model");
			modelPathMap.put("pageant_module_scg",            "KM_pageant_Pageant_88id_nart_cuda11.4-trt8.4.1-int8-T4_b16_1.0.8.model");
			modelPathMap.put("feature_face_module_scg",       "KM_feature_face-trt_nart_cuda11.4-trt8.4.1-int8-T4_b32_2.54.0.model");
		}
		else if(deviceType == DeviceType.STPU) {
			throw new RuntimeException("deviceType STPU not surpported.");
		}else {
			throw new RuntimeException("deviceType or cudaType not surpported.");
		}
	}

	public static boolean isDevice() {
		return deviceType != DeviceType.HOST;
	}

	public static boolean isGpu() {
		return deviceType == DeviceType.P4 || deviceType == DeviceType.T4 || deviceType == DeviceType.A2 || deviceType == DeviceType.A16 || deviceType == DeviceType.L4;
	}

	/**
	 * 初始化，需要调用，某些参数可以为null，需要什么功能，就传什么model进去
	 */
	public synchronized static void initialize(Environment env) {
		if(initialized)
			return ;

		for(Entry<String, String> entry : Initializer.modelPathMap.entrySet()) {
			String value = env.getProperty(entry.getKey());

			if(value == null)
				value = env.getProperty(entry.getKey().toUpperCase());

			if(value == null)
				value = env.getProperty(entry.getKey().toLowerCase());

			if(StringUtils.isNotBlank(value))
				entry.setValue(value);
		}

		KestrelApi.kestrel_log_set_level(KestrelApi.KESTREL_LL_INFO);

		KestrelApi.kestrel_init(Objects.requireNonNullElse(licenseProd, "sensestudio"));
		KestrelApi.kestrel_license_add(kestrel_lic, null);

		demuxerName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "demuxer.kep", "");

		if(isGpu()) {
			deviceName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "cuda.kep", "");
			if(!"cuda".equals(deviceName))
				throw new RuntimeException("[" + deviceName + "] failed to bind device on gpu.");

			decoderName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "decoder.kep", "");
			encoderName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "imagesharp.kep", "");
		}else if(deviceType == DeviceType.STPU) {
			deviceName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "stpu_device.kep", "");
			if(!"stpu_device".equals(deviceName))
				throw new RuntimeException("[" + deviceName + "] failed to bind device on stpu.");

			decoderName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "stpu_decoder.kep", "");
			encoderName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "stpu_encoder.kep", "");
		}else if(deviceType == DeviceType.HOST) {
			decoderName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "decoder.kep", "");
			encoderName = KestrelApi.kestrel_plugin_load("/usr/cognitivesvc/" + "imagesharp.kep", "");
		}

		bindDeviceOrNot();

		initialized = true;
	}

	public static final int bindDeviceOrNot() {
		if(deviceType == DeviceType.HOST)
			return 0;

		int ret = 0;

		if(!Objects.equals(deviceName, threadDeviceName.get())) {
			ret = KestrelApi.kestrel_device_bind(deviceName, deviceId);
			threadDeviceName.remove();
		}

		return ret;
	}

	public final static String kestrel_lic =  !"CAOLIISSUPERMAN".equals(licenseCode) ? loadLicFile() :
			"############################################################\n" +
					"# SenseTime License\n"                                          +
					"# License Product: sensestudio\n"                               +
					"# Expiration: 20190101~20991231\n"                              +
					"# License SN: 7d7c4808-5812-402b-8284-878984b69905\n"           +
					"############################################################\n" +
					"sGfdd19zNtR5PYCVcKd9hW+uV+T57pophFV8AuslqGS7XZ+EV29Xyo2GGy8k\n" +
					"qnKZ0ijWL/pxrDZdrQ+0LBo//D9fS5nl4pibWfEfv1AlZPlVf8UBcU82xONz\n" +
					"ui5iBe9uY9arCP0N0aQyhUOidFQ0/gie2w+VGi48uE1bGqB7B7+QpS6EAQAA\n" +
					"AAEAAAC28FcQeVIhUb2UF7iWljOPoa5KdKdnG3Q7tXg2AM4dqMWL5P1wj86M\n" +
					"1QKpLFILeJ/L3sKK/0gH987anUzMDXEky7rGw6nJUTPrXMWVB8JYMyChpIKW\n" +
					"18GIy8FJ9GCbhGaLltr0NAtKw2h3IEVPdCjYP0eqgyrGK7kot8c6xcim88zz\n" +
					"x99V7QZ8wL8BLlWaRkGIV2M5ovY3fNMw/eR4fJASmCYmCgD8W+6BqmqdNRiP\n" +
					"uhh9Yx+UUT46EWFjIxmIUMX6fokrT+YCalbtuwcwCKoJ3umLoL7vjcJa/2o1\n" +
					"Q95dtwyl4SC63MA0DVvjKzRwIa19+g9Po652uMwQLjbtHFnvAQABAAAAAAAD\n" +
					"AAAAAAAAAAAAAACSrMAJHDCj/Nt12H+YjAlXk9bAad9xz3xAYqpPwVGZ42zY\n" +
					"glvWoNwk31Zdrorkb0svxlYL8qqBidi0UwrSPZAr5LCDZLemA15hF6xbiSHd\n" +
					"1IgHGdvaSAZ2/+bmoEJUvapmxT+KuQ4n1j66SfNF1Gg6LvVzfOYjXIJVt8Ab\n" +
					"4KroobFKO+KYJM2ee2FG+b9Y77LcDPgHyQOXx6YSGy10AIC7SMqt5Bhc49PH\n" +
					"xtD3SClkcNUO4PtM8jA/cEN7vAJYt9mXog+Sq8XMjUhxKG+CEaejoHRO4rRw\n" +
					"24wsrBt4U1RnvjOzjoK6yJpnEkIBhaUzVzKJL7Qe+t59Vq4olKbpI2oJbc62\n" +
					"bdk4O3bil6VDGhYpY2z5XO6dXpW7IxO3i31LPzKnL8pU5b5kkt3MYi49zPpg\n" +
					"zTAKRxObeMMoXiU9W9lIUPtITclUJpERSDvX+vleNak4CQZDJUyh3cqeEtma\n" +
					"qgnfwvLQGrIJ9iTkeF1aq3Drtkb3SZR2itTWY7uvhStNY48WDyxUw6yE9wfd\n" +
					"J5vUGZwW5Wq/OnmWmUdUeT94q0SOgpRYyhjqcwKdy0AH9gub1D2Af8TTlc1g\n" +
					"cUl7oOMVcTcdbEDBC7XAYgATQSsP2yfwTkb3pUL34/POQTBaHhbX+sAUKjxa\n" +
					"gx2yGcS2SkqC6kjTsEq49KCXzp5Ev/mXx7SI/jqiKc29Qm7TY2Pv1K//Uo2m\n" +
					"ew5w9mocTHAKTrs2X/Uy1Rb5pm9+JZezZxHySrvtJA==\n"                 +
					"------------------------------------------------------------\n" +
					"sGfdd6w1aDfxTvCI1bolnTuVSxHxPMYY/OPzZwECBvCNi13LGwmaeAvg6u5M\n" +
					"HJgrzEu8eFejNXN7U5G+YmLDs2gH8ENfRFXySK17sh8FnRM81bVRTVzw8wKa\n" +
					"zSmHsLnf4nSAgUnX4qs3Rn7oKfWXceV+5tctz5wnsYU2ROM6oI7LjeA4AQAA\n" +
					"AAIAAABQv3Lipe0uFQDYRPYVuEwvpRLoQRaoNdXZqayS6uCMhLTffpATAREd\n" +
					"r0zv7jGYi0iJec3A/WVYPJtNvyBzpTQCJ8cMn8yu/OTzfeG6Wn0K+vQKvbVJ\n" +
					"4xwfJy1ScLtnYjFvATKc30c6n36FlgOV8LiQpQfhsP/Tihh88ypXI294bs6B\n" +
					"FU8NQwSE40h4AwJOxxWKRuNCILq6hd019UZ+xlBZLqAeUor+6TkmIQ0bR6hF\n" +
					"S/0o/Ll59yKAVizU46sfpX7fUJuoTNoVgYl1Y5G0ugoVVqD1Jal3tVaLUZRz\n" +
					"XWNUCDqFVjYqgeNS4KOqgJ/W+7QiHulu8roqp5qqNBdEtRXRAAAAAAAAAAAA\n" +
					"AAAAAAAAAAAAAACmp0/uxWkGiy5c0GUfkbxev8X9fkyOtSjectRmGUftuGMA\n" +
					"au+ao2oyw96dXtDnsT5rJ0ZrWqKOA3wTyAzGCYCZYwv/7sQeY5YrEYqIUH5Q\n" +
					"7VNUjoNDjwqdLjtL6ddUEYRZTmfM1cc5Sizsm5gXbeCXO8xT5EBMzi8QbL0E\n" +
					"lDRhMBphnE7RvNwfb6Tzx44Yn7EbRbGEyuWk+x9wo+GZFsBeVt75o/kyxVog\n" +
					"hNlWOvmNZ9QGabTu85M4bJzYku5Vx1riqXTrqVdlpwMRakGRfezY+F5E/+y8\n" +
					"OHI3VykkVRhOTwOCcRQT/UAmT5NZlyJtJTRFTxWTOPJ8HRVE6QZZgyWXUQQe\n" +
					"v08tB3IrAc8eLdRP7UQW9YtnRKmomtvKR5IJWUknSaG4cvrQOwd05ZniSJx5\n" +
					"hK2GKsgKbs5zAK/0vB8ypJj+1voKmzDNLd0KJ4DWKHyLUzfkglF04GE+SnHD\n" +
					"wsmkc+sPUJcflchEdCgfyxV2PI97yYdDFUj61NCnzCdmukFTa+19dTEJZLBx\n" +
					"Z5i9qKUj6aItdYpsyy2GtndRT+CYPVW+28bf11JsEZmyD8pUIrJkLahCr+mn\n" +
					"eXWzz45StcIX6dtxBVKbAHPPt5UgGzFSVoq0Ayy4IfCOXGxQwEVI3JtCs8ww\n" +
					"kjUA+wUy7kMsmqAeHScZvNTffzxudRxAhuSMWhpIwhLKn+fNM8RnaNiSW1nJ\n" +
					"6sOCSrrEXJ1W7i/KMZRZidHe161CDNAfgMDpL1caLl0DIro=\n"             +
					"############################################################"   ;


	private static String loadLicFile() {
		if(licenseFile == null) {
			throw new RuntimeException("license file not exist, please check.");
		}
		try {
			return FileUtils.readFileToString(new File(licenseFile), Charset.defaultCharset());
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
}
