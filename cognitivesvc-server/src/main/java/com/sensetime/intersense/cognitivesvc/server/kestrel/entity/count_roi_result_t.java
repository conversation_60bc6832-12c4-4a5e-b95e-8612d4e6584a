package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class count_roi_result_t extends Structure {
	/**
	 * <roi的顶点坐标<br>
	 * C type : kestrel_point2d_t[1024]
	 */
	public kestrel_point2d_t[] vertexes = new kestrel_point2d_t[1024];
	public int vertex_num;
	/** 计数结果.-1表明所在帧没有经过计数处理 */
	public float count;
	public count_roi_result_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("vertexes", "vertex_num", "count");
	}
	/**
	 * @param vertexes <roi的顶点坐标<br>
	 * C type : kestrel_point2d_t[1024]<br>
	 * @param count 计数结果.-1表明所在帧没有经过计数处理
	 */
	public count_roi_result_t(kestrel_point2d_t vertexes[], int vertex_num, float count) {
		super();
		if ((vertexes.length != this.vertexes.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.vertexes = vertexes;
		this.vertex_num = vertex_num;
		this.count = count;
	}
	public count_roi_result_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends count_roi_result_t implements Structure.ByReference {
		
	};
	public static class ByValue extends count_roi_result_t implements Structure.ByValue {
		
	};
}