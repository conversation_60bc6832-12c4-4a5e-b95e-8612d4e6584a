package com.sensetime.intersense.cognitivesvc.server.feign;

import com.sensetime.lib.clientlib.response.BaseRes;

import lombok.Data;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * Create by <PERSON> in 2018/4/17 上午9:53
 */
@FeignClient(value = "person", configuration = {})
public interface MemberFeign {
    
    @RequestMapping(value = "/member/getMemberIdsFromGroup", produces = MediaType.APPLICATION_JSON_VALUE)
    BaseRes<List<String>> getMemberIdsFromGroup(@RequestHeader(value = "baggage-deptid", required = false) String deptId, @RequestHeader(value = "baggage-subdeptids", required = false) String subDeptIds, @Valid @RequestBody GroupMemberIdsReq req);
    
    @Data
    public static class GroupMemberIdsReq {
        @NotBlank(message = "4001")
        private String groupId;
    }
    
    @RequestMapping("/person/queryPersonGroupIds")
    BaseRes<GroupIdsRes> queryPersonGroupIds(@RequestHeader(value = "baggage-deptid", required = false) String deptId, @RequestHeader(value = "baggage-subdeptids", required = false) String subDeptIds, @RequestParam(value = "uid") String uid);
    
    
    @Data
    public static class GroupIdsRes {
        @NotBlank(message = "4001")
        private List<String> personGroupIds;;
    }
    
    
    @RequestMapping(value = "/person/batchQueryPersonGroupIds", produces = MediaType.APPLICATION_JSON_VALUE)
    BaseRes<Map<String,List<String>>> batchQueryPersonGroupId(@RequestHeader(value = "baggage-deptid", required = false) String deptId, @RequestHeader(value = "baggage-subdeptids", required = false) String subDeptIds, @Valid @RequestBody PersonIdListReq req);
    
    @Data
    public static class PersonIdListReq {
        @NotBlank(message = "4001")
        private List<String> uidList;
    }
    
    
    
    
}
