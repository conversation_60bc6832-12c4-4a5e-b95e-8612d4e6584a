package com.sensetime.intersense.cognitivesvc.server.utils;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.sensetime.intersense.cognitivesvc.server.kestrel.FrameEncoderLibrary;
import com.sensetime.intersense.cognitivesvc.server.kestrel.Kestrel_frame_utils_structLibrary;
import com.sensetime.lib.weblib.exception.BusinessException;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_packet_t;
import com.sensetime.storage.autoconfigure.StorageProperties;
import com.sensetime.storage.entity.ImageInfo;
import com.sensetime.storage.factory.FileStorageType;
import com.sun.jna.Native;
import com.sun.jna.ptr.LongByReference;
import org.apache.commons.lang3.ArrayUtils;
import org.bytedeco.javacpp.BytePointer;
import org.bytedeco.javacpp.IntPointer;
import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_area2d_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import com.sensetime.storage.service.FileAccessor;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.opencv.core.Scalar;
@Slf4j
public class FrameUtils {
	public static final String NOIMAGE = "no image.";

	public static FileAccessor fileAccessor;

	public static StorageProperties storageProperties;

	public static Pattern datePattern = Pattern.compile("^(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])$");

	public static final ConcurrentHashMap<String, ImageCache> SAVEDIMAGES = new ConcurrentHashMap<String, ImageCache>();

	/** 解码图片 并上传到显卡 返回两种帧  都需要被释放 */
	public static VideoFrame decode_load_frame_path(String image_path) {
		Initializer.bindDeviceOrNot();

		if(!new File(image_path).exists()) {
            log.error("image_path [" + image_path + "] file not exist.");
            throw new BusinessException("3003","image_path [" + image_path + "] file not exist.");
        }

		Pointer cpu_frame = KestrelApi.kestrel_frame_load(image_path);
		if(cpu_frame == null)
			return null;

		return VideoFrame.builder().cpuFrame(cpu_frame).build();
	}

	public static VideoFrame decode_load_frame_memory(byte[] bytes) {
		Initializer.bindDeviceOrNot();


		Pointer cpu_frame = KestrelApi.kestrel_frame_load_from_memory(bytes);
		if(cpu_frame == null)
			return null;

		return VideoFrame.builder().cpuFrame(cpu_frame).build();
	}

	/** 解码图片 并上传到显卡 返回两种帧  都需要被释放 */
	public static VideoFrame decode_up_down_load_frame_path(String image_path) {
		Initializer.bindDeviceOrNot();

		if(!new File(image_path).exists()) {
            log.error("image_path [" + image_path + "] file not exist.");
            throw new BusinessException("3003", "image_path [" + image_path + "] file not exist.");
        }

		Pointer cpu_frame = KestrelApi.kestrel_frame_load(image_path);
		if(cpu_frame == null)
			return null;

		Pointer gpu_frame = ref_or_upload_frame(cpu_frame);
		return VideoFrame.builder().cpuFrame(cpu_frame).gpuFrame(gpu_frame).build();
	}

	/** 解码图片 并上传到显卡 返回两种帧  都需要被释放 */
	public static VideoFrame decode_up_down_load_Buffered_frame_path(String image_path) {
		Initializer.bindDeviceOrNot();

		if(!new File(image_path).exists()) {
            log.error("image_path [" + image_path + "] file not exist.");
            throw new BusinessException("3003", "image_path [" + image_path + "] file not exist.");
        }

		Pointer cpu_frame = KestrelApi.kestrel_frame_load(image_path);
		if(cpu_frame == null) {
			log.error(">>> [decode error] KestrelApi.kestrel_frame_load() failed！imagePath="+image_path);
			throw new BusinessException("1001", "KestrelApi.kestrel_frame_load() failed！imagePath="+image_path);
		}

		Pointer gpu_frame = ref_or_upload_buffered_frame(cpu_frame);
		return VideoFrame.builder().cpuFrame(cpu_frame).gpuFrame(gpu_frame).build();
	}

	/** 解码图片 并上传到显卡 返回两种帧  都需要被释放 */
	public static VideoFrame decode_up_down_load_Buffered_frame_base64(String ImageBase64) {
		Initializer.bindDeviceOrNot();

		Pointer cpu_frame = KestrelApi.kestrel_frame_load_from_memory(ImageUtils.base64ToBytes(ImageBase64));
		if(cpu_frame == null)
			return null;

		Pointer gpu_frame = ref_or_upload_buffered_frame(cpu_frame);
		return VideoFrame.builder().cpuFrame(cpu_frame).gpuFrame(gpu_frame).build();
	}

	public static VideoFrame decode_up_down_load_from_memory(byte[] bytes) {
		Initializer.bindDeviceOrNot();

		Pointer cpu_frame = KestrelApi.kestrel_frame_load_from_memory(bytes);
		if(cpu_frame == null)
			return null;

		Pointer gpu_frame = ref_or_upload_frame(cpu_frame);
		return VideoFrame.builder().cpuFrame(cpu_frame).gpuFrame(gpu_frame).build();
	}


	public static VideoFrame decode_up_down_load_from_buffered_memory(byte[] bytes) {
		Initializer.bindDeviceOrNot();

		Pointer cpu_frame = KestrelApi.kestrel_frame_load_from_memory(bytes);
		if(cpu_frame == null)
			return null;

		Pointer gpu_frame = ref_or_upload_buffered_frame(cpu_frame);
		return VideoFrame.builder().cpuFrame(cpu_frame).gpuFrame(gpu_frame).build();
	}
	/** 释放frame */
	public static void batch_free_frame(AutoCloseable... frames) {
		for(AutoCloseable frame : frames) {
			if(frame != null)
				try { frame.close(); } catch (Exception e) { }
		}
	}

	/** 释放frame */
	public static void batch_free_frame(Pointer... frame){
		if(ArrayUtils.isEmpty(frame))
			return;

		PointerByReference tub = new PointerByReference();
		for(int index = 0; index < frame.length; index ++) {
			if(frame[index] != null) {
				tub.setValue(frame[index]);
				KestrelApi.kestrel_frame_free(tub);
			}
		}
	}
	/** 释放frame */
	public static void batch_free_frame_cgo(Pointer... frame){
		if(ArrayUtils.isEmpty(frame))
			return;

		for (Pointer pointer : frame) {
			if (pointer != null) {
				KestrelApi.ReleaseFrame(pointer);
			}
		}
	}

	/** 释放frame */
	public static void batch_free_frame(PointerByReference... frame){
		if(ArrayUtils.isEmpty(frame))
			return;

		for(int index = 0; index < frame.length; index ++)
			if(frame[index] != null && frame[index].getValue() != null)
				KestrelApi.kestrel_frame_free(frame[index]);
	}

	/** 引用ROI */
	public static Pointer roi_frame(Pointer frame, kestrel_area2d_t area) {
		return roi_frame(frame, area.left, area.top, area.width, area.height, 0.0f);
	}

	/** 引用ROI */
	public static Pointer roi_frame(Pointer frame, int left, int top, int width, int height) {
		return roi_frame(frame, left, top, width, height, 0.0f);
	}

	/** 引用ROI */
	public static Pointer roi_frame(Pointer frame, Map<String, Number> roi, float extendedRate) {
		int left = roi.get("left").intValue();
		int top = roi.get("top").intValue();
		int width = roi.get("width").intValue();
		int height = roi.get("height").intValue();

		return roi_frame(frame, left, top, width, height, extendedRate);
	}

	/** 引用ROI */
	public static Pointer roi_frame(Pointer frame, kestrel_area2d_t area, float extendedRate) {
		return roi_frame(frame, area.left, area.top, area.width, area.height, extendedRate);
	}

	/** 引用ROI */
	public static Pointer roi_frame(Pointer frame, int left, int top, int width, int height, float extendedRate) {
		kestrel_area2d_t.ByValue area_by_value= new kestrel_area2d_t.ByValue();
		area_by_value.left = Math.max(0, left - (int)(width * extendedRate));
		area_by_value.top =  Math.max(0, top - (int)(height * extendedRate));
		area_by_value.width = width + (int)(width * extendedRate * 2);
		area_by_value.height = height + (int)(height * extendedRate * 2);
		area_by_value.write();

		return KestrelApi.kestrel_frame_roi(frame, area_by_value);
	}

	public static String save_image_packet(Pointer frame, File file) {

		String filePath = file.getAbsolutePath();
		try{

			kestrel_packet_t packet_t = new kestrel_packet_t(frame);
			packet_t.read();

			byte[] imageRes= null;
			if(packet_t.size > 0)
				imageRes = packet_t.data.getByteArray(0, packet_t.size);

			packet_t.clear();


			if (imageRes != null && imageRes.length > 0) {
				FileOutputStream output = new FileOutputStream(filePath);
				output.write(imageRes);
				output.close();
			}

		}catch (Exception e){
			e.printStackTrace();
		}
		return filePath;

	}

	public static String save_image_packet2(Pointer packet, File file) {

		String filePath = file.getAbsolutePath();
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
			 BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath))) {
			kestrel_packet_t packet_t = null;
			try {
				packet_t = new kestrel_packet_t(KestrelApi.keson_get_ext_data(packet).getValue());
				packet_t.read();
				if (packet_t.size > 0 && packet_t.data !=null) {
					byte[] arr = packet_t.data.getByteArray(0, packet_t.size);
					packet_t = null;
					if (arr != null && arr.length > 0) {
						baos.write(arr);
					}
				}
			} finally {
				KesonUtils.kesonDeepDelete(packet);
				if (packet_t != null) packet_t.clear();
			}
			baos.writeTo(bos);
			baos.flush();
			bos.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return filePath;

	}

	//todo
	public static String save_image_packet_byte(byte[] imageRes, File file) {
		long now = System.currentTimeMillis();
		boolean logged = Utils.instance.watchFrameTiktokLevel == -789;
		if (Utils.instance.videoSaveImageType < 0)
			return NOIMAGE;

		String filePath = file.getAbsolutePath();
		String fileAfterSave = filePath;
		try{
			ImageInfo imageInfo = new ImageInfo();
			imageInfo.setImagePath(getBucketNameFromUrl(filePath));
			imageInfo.setData(imageRes);
			imageInfo.setCheckPath(false);
			fileAfterSave = fileAccessor.writeImage(imageInfo);
		}catch (Exception e){
			e.printStackTrace();
			fileAfterSave = "/error";
		}finally {
			long saveCost = (System.currentTimeMillis() - now);
			if(logged){
				log.info("[VideoHandleLog] [Cost] saveOsgImage count: 1 costTime:[" + saveCost + "]");
			}
			if(saveCost > Utils.instance.longCostSaveImgTs){
				log.warn("[VideoHandleLog] [Cost] saveOsgImageNewFunc  videoSaveImageWay cost:{}", saveCost);
			}
		}
		if(Utils.instance.saveImagePathDisk){
			saveImagePathDisk(fileAfterSave);
		}
		return fileAfterSave;

	}
	//todo
	public static String save_image_packet_(kestrel_packet_t packet_t, File file) {

		String filePath = file.getAbsolutePath();
		String fileAfterSave = filePath;
		try{

			if(packet_t !=null) {
//			kestrel_packet_t packet_t = new kestrel_packet_t(frame);
				packet_t.read();

				byte[] imageRes = null;
				if (packet_t.size > 0)
					imageRes = packet_t.data.getByteArray(0, packet_t.size);
				try{
					ImageInfo imageInfo = new ImageInfo();
					imageInfo.setImagePath(getBucketNameFromUrl(filePath));
					imageInfo.setData(imageRes);
					imageInfo.setCheckPath(false);
					fileAfterSave = fileAccessor.writeImage(imageInfo);
				}catch (Exception e){
					e.printStackTrace();
					fileAfterSave = "/error";
				}
				imageRes = null;
			}

		}catch (Exception e){
			e.printStackTrace();
		}
		return fileAfterSave;

	}
	/**
	 * save image by kestrel frame ==> todo 改名字，只用在本地存储
	 */
	public static String save_image_as_jpg(Pointer frame, File file) {

		boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage save_image_as_jpg start at now");
		}


		if (frame == null || Utils.instance.videoSaveImageType < 0)
			return NOIMAGE;

		//Boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

		String filePath = file.getAbsolutePath();

		String filePathSaved = filePath;

//		long step0 = 0l;
//		if(loggedForCost){
//			step0 = new Date().getTime();
//		}
//		Boolean exist = file.exists();
//		if(loggedForCost){
//			log.info("[VideoHandleLog] [Cost] [into saveImage] file exist cost {}", new Date().getTime() - step0);
//		}
//		Boolean exist = false;
//
//		if(exist) {
//			new RuntimeException("image [" + filePath + "] already exists. ignore this one.").printStackTrace();
//			return filePath;
//		}

		int  width  = KestrelApi.kestrel_frame_video_width(frame);
		int  height = KestrelApi.kestrel_frame_video_height(frame);
		long plane  = KestrelApi.kestrel_frame_plane_origin(frame, 0);
		long pts    = KestrelApi.kestrel_frame_pts(frame);

		boolean shouldCached = FramePoolUtils.bufferedSizeLimit.contains(width + "*" + height);
		if(!shouldCached) {
			try {
				long step1 = 0l;
				if(loggedForCost){
					step1 = new Date().getTime();
				}
				filePathSaved = encode_image_as_jpg2(frame, filePath,true);
				if(loggedForCost){
					log.info("[VideoHandleLog] [Cost] [into saveImage] encode_image_as_jpg2 cost {}", new Date().getTime() - step1);
				}

				long step2 = 0l;
				if(loggedForCost){
					step2 = new Date().getTime();
				}
//				if (imageRes != null && imageRes.length > 0) {
//					FileOutputStream output = new FileOutputStream(filePath);
//					output.write(imageRes);
//					output.close();
//				}

				if(loggedForCost){
					log.info("[VideoHandleLog] [Cost] [into saveImage] output write cost {}", new Date().getTime() - step2);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			if(Utils.instance.saveImagePathDisk){
				saveImagePathDisk(filePathSaved);
			}
			return filePathSaved;
		}

		String key = plane + "_" + pts;
		ImageCache cache = SAVEDIMAGES.get(key);
		if(cache == null) {
			cache = new ImageCache(null, System.currentTimeMillis(), new CountDownLatch(1));

			ImageCache previous = SAVEDIMAGES.putIfAbsent(key, cache);
			if (previous != null) {
				try {
					previous.getLatch().await();
				} catch (InterruptedException e) {
				}
				if(Utils.instance.saveImagePathDisk){
					saveImagePathDisk(previous.getPath());
				}
				return previous.getPath();
			}
		} else {
			try {
				cache.getLatch().await();
			} catch (InterruptedException e) {
			}
			if(Utils.instance.saveImagePathDisk){
				saveImagePathDisk(cache.getPath());
			}
			return cache.getPath();
		}

		try {
			long step1 = 0l;
			if(loggedForCost){
				step1 = new Date().getTime();
			}
			filePathSaved = encode_image_as_jpg2(frame, filePath,true);

			if(loggedForCost){
				log.info("[VideoHandleLog] [Cost] [into saveImage] encode_image_as_jpg2 cost {}", new Date().getTime() - step1);
			}


			long step2 = 0l;
			if(loggedForCost){
				step2 = new Date().getTime();
			}

//			if (imageRes != null && imageRes.length > 0) {
//				FileOutputStream output = new FileOutputStream(filePath);
//				output.write(imageRes);
//				output.close();
//			}

			if(loggedForCost){
				log.info("[VideoHandleLog] [Cost] [into saveImage] output write cost {}", new Date().getTime() - step2);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		long step3 = 0l;
		if(loggedForCost){
			step3 = new Date().getTime();
		}

		cache.setPath(filePathSaved);
		cache.getLatch().countDown();

		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] [into saveImage] catch wait cost {}", new Date().getTime() - step3);
		}
		if(Utils.instance.saveImagePathDisk){
			saveImagePathDisk(filePath);
		}


		return filePath;
	}


	private static void saveImagePathDisk(String filePath) {

		try{
			//String path = "/images/cognitivesvc/crowdAnalysis/20240531/23/99/11.jpg";
			String[] parts = filePath.split("/");
			if(parts.length <=0){
				return;
			}
			String modelName = parts[3];

			String day =ImageUtils.dateFormatter_DAY.get().format(new Date(new Date().getTime()));

			String hour = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH"));

			String pathsFile = ImageSaveUtils.getInstance().newFileWithPathImage(day, hour, modelName, false);
			if(pathsFile.isBlank()){
				return;
			}

			ImageSaveUtils.getInstance().writeToFileNio(filePath, pathsFile);

//			String pathsFile = ImageUtils.newFileWithPathImage2(filePath, false);
//			if (pathsFile != null) {
//				Path paths = Path.of(pathsFile);
//				try (BufferedWriter writer = Files.newBufferedWriter(paths, StandardOpenOption.APPEND)) {
//					writer.write(filePath);
//					writer.newLine();
//					// 可以根据需求添加更多的写入语句
//				} catch (IOException e) {
//					System.err.println("Error appending data to file: " + e.getMessage());
//				}
//			}



		}catch (Exception e){
			e.printStackTrace();
		}

	}


	public static String save_image_as_jpg(Pointer frame, File file, int saveImgTag) {

		if(saveImgTag == 1){
			return FrameUtils.NOIMAGE;
		}

		boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage save_image_as_jpg start at now");
		}


		if (frame == null || Utils.instance.videoSaveImageType < 0)
			return NOIMAGE;

		//Boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

		String filePath = file.getAbsolutePath();
		String fileAfterSaved = filePath;

//		long step0 = 0l;
//		if(loggedForCost){
//			step0 = new Date().getTime();
//		}
//		Boolean exist = file.exists();
//		if(loggedForCost){
//			log.info("[VideoHandleLog] [Cost] [into saveImage] file exist cost {}", new Date().getTime() - step0);
//		}
//		Boolean exist = false;
//
//		if(exist) {
//			new RuntimeException("image [" + filePath + "] already exists. ignore this one.").printStackTrace();
//			return filePath;
//		}

		int  width  = KestrelApi.kestrel_frame_video_width(frame);
		int  height = KestrelApi.kestrel_frame_video_height(frame);
		long plane  = KestrelApi.kestrel_frame_plane_origin(frame, 0);
		long pts    = KestrelApi.kestrel_frame_pts(frame);

		boolean shouldCached = FramePoolUtils.bufferedSizeLimit.contains(width + "*" + height);
		if(!shouldCached) {
			try {
				long step1 = 0l;
				if(loggedForCost){
					step1 = new Date().getTime();
				}
				fileAfterSaved = encode_image_as_jpg2(frame, filePath, false);
				if(loggedForCost){
					log.info("[VideoHandleLog] [Cost] [into saveImage] encode_image_as_jpg2 cost {}", new Date().getTime() - step1);
				}

				long step2 = 0l;
				if(loggedForCost){
					step2 = new Date().getTime();
				}
//				if (imageRes != null && imageRes.length > 0) {
//					FileOutputStream output = new FileOutputStream(filePath);
//					output.write(imageRes);
//					output.close();
//				}

				if(loggedForCost){
					log.info("[VideoHandleLog] [Cost] [into saveImage] output write cost {}", new Date().getTime() - step2);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			if(Utils.instance.saveImagePathDisk){
				saveImagePathDisk(fileAfterSaved);
			}
			return fileAfterSaved;
		}

		String key = plane + "_" + pts;
		ImageCache cache = SAVEDIMAGES.get(key);
		if(cache == null) {
			cache = new ImageCache(null, System.currentTimeMillis(), new CountDownLatch(1));

			ImageCache previous = SAVEDIMAGES.putIfAbsent(key, cache);
			if (previous != null) {
				try {
					previous.getLatch().await();
				} catch (InterruptedException e) {
				}
				if(Utils.instance.saveImagePathDisk){
					saveImagePathDisk(previous.getPath());
				}
				return previous.getPath();
			}
		} else {
			try {
				cache.getLatch().await();
			} catch (InterruptedException e) {
			}
			if(Utils.instance.saveImagePathDisk){
				saveImagePathDisk(cache.getPath());
			}
			return cache.getPath();
		}

		try {
			long step1 = 0l;
			if(loggedForCost){
				step1 = new Date().getTime();
			}
			fileAfterSaved = encode_image_as_jpg2(frame, filePath,false);

			if(loggedForCost){
				log.info("[VideoHandleLog] [Cost] [into saveImage] encode_image_as_jpg2 cost {}", new Date().getTime() - step1);
			}


			long step2 = 0l;
			if(loggedForCost){
				step2 = new Date().getTime();
			}
//
//			if (imageRes != null && imageRes.length > 0) {
//				FileOutputStream output = new FileOutputStream(filePath);
//				output.write(imageRes);
//				output.close();
//			}

			if(loggedForCost){
				log.info("[VideoHandleLog] [Cost] [into saveImage] output write cost {}", new Date().getTime() - step2);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		long step3 = 0l;
		if(loggedForCost){
			step3 = new Date().getTime();
		}

		cache.setPath(fileAfterSaved);
		cache.getLatch().countDown();

		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] [into saveImage] catch wait cost {}", new Date().getTime() - step3);
		}
		if(Utils.instance.saveImagePathDisk){
			saveImagePathDisk(fileAfterSaved);
		}
		return fileAfterSaved;
	}
	// rename
	public static String encode_image_as_jpg2(Pointer frame, String filePath, Boolean onlyForGeneralFileType) {
		//Pointer frame = inputFrame;
		int mem_type = KestrelApi.kestrel_frame_mem_type(frame);

		Boolean logged = Utils.instance.watchFrameTiktokLevel == -789 || Utils.instance.printLog;
		if(logged){
			log.info("[VideoHandleLog] [Cost] videoSaveImageWay:{}, saveImg mem_type:{},{}", Utils.instance.videoSaveImageWay, mem_type, filePath);
		}
		Float videoSaveImageScale = Utils.instance.videoSaveImageScale;
		if (videoSaveImageScale != null) {
			int width = KestrelApi.kestrel_frame_video_width(frame);
			int height = KestrelApi.kestrel_frame_video_height(frame);

			if (FramePoolUtils.bufferedSizeLimit.contains(width + "*" + height)) {
				int fmt = KestrelApi.kestrel_frame_video_format(frame);

				int scaleWidth = (int) (videoSaveImageScale * width);
				int scaleHeight = (int) (videoSaveImageScale * height);

				PointerByReference buffered_frame = new PointerByReference();
				if (mem_type == KestrelApi.KESTREL_MEM_DEVICE)
					buffered_frame.setValue(FramePoolUtils.buffered(fmt, scaleWidth, scaleHeight));

				KestrelApi.kestrel_frame_scale(frame, buffered_frame, videoSaveImageScale);

				frame = buffered_frame.getValue();
			}
		}
		long now = System.currentTimeMillis();

		//todo 加osg
		if(!onlyForGeneralFileType && storageProperties.getFileStorageType().equals(FileStorageType.OSG) ){
			ImageInfo imageInfo = new ImageInfo();
			// 获取bucketname
			imageInfo.setImagePath(getBucketNameFromUrl(filePath));

			// frame 转 byte[] 不要增加程序间调用
			Initializer.bindDeviceOrNot();
			long frame2ByteTime = 0L;
			if(logged) {
				 frame2ByteTime = System.currentTimeMillis();
			}
			try {
				byte[] imageResPointer = null;
				if (Utils.instance.frameEncoderType == 0) {
					//调用kestrel的 keson_encode_to_data
					LongByReference outSize = new LongByReference();
					Pointer resultPointer = FrameEncoderLibrary.INSTANCE.KestrelEncoder(frame, Kestrel_frame_utils_structLibrary.kestrel_frame_enc_format_e.KESTREL_ENC_FMT_JPG, outSize);
					if (resultPointer != null) {
						long size = outSize.getValue();
						imageResPointer = resultPointer.getByteArray(0, (int) size);

						Native.free(Pointer.nativeValue(resultPointer));
					} else {
						log.warn("EncodeFrame failed");
					}
				} else if (Utils.instance.frameEncoderType == 1) {
					//调用image sharp kep 插件encode
					imageResPointer = encode_image_as_jpg(frame);
				} else if (Utils.instance.frameEncoderType == 2) {
					//调用 vps imageio jna or c++ so
					LongByReference outSize = new LongByReference();
					Pointer resultPointer = KestrelApi.kestrel_frame_encoder(frame, 0, outSize);
					if (resultPointer != null) {
						// 使用result字节数组
						long size = outSize.getValue();
						imageResPointer = resultPointer.getByteArray(0, (int) size);
						// 释放内存
						Native.free(Pointer.nativeValue(resultPointer));
					} else {
						log.error("EncodeFrame failed");
					}
				}else if (Utils.instance.frameEncoderType == 3) {
					log.warn("unSupport type {}", Utils.instance.frameEncoderType);
				} else {//unsupport type
					//imageResPointer = null;
					log.warn("unSupport type {}", Utils.instance.frameEncoderType);
				}
				if(logged){
					log.info("[VideoHandleLog] [Cost] frame2ByteTime count: 1 costTime:[" +  (System.currentTimeMillis() - frame2ByteTime)  + "]");
				}
				if (imageResPointer != null && imageResPointer.length > 0 ) {
					imageInfo.setData(imageResPointer);
				}

			} catch (Exception e) {
				log.error("[VideoHandleLog] saveImage frame to byte error:{}", e.getMessage());
			}finally {
				// 外层会free
				//FrameUtils.batch_free_frame(frame);
			}
			//1000只编码不落盘
			if(Utils.instance.videoSaveImageType == 1000){
				return filePath;
			}

            String fileSaved = filePath;
            try {
                fileSaved = fileAccessor.writeImage(imageInfo);
            } catch (Exception e) {
				log.error("[VideoHandleLog] saveImage fileAccessor.writeImage error:{}", e.getMessage());
                fileSaved = "/error";
            }finally {
				long saveCost = (System.currentTimeMillis() - now);
				if(logged){
					log.info("[VideoHandleLog] [Cost] saveOsgImage count: 1 costTime:[" + saveCost + "]");
				}
				if(saveCost > Utils.instance.longCostSaveImgTs){
					log.warn("[VideoHandleLog] [Cost] saveOsgImageNewFunc  videoSaveImageWay cost:{}", saveCost);
				}
			}
            return fileSaved;
		}

		try {
			if (Utils.instance.videoSaveImageType == 0 && KestrelApi.KESTREL_MEM_DEVICE == mem_type) {
				if(Utils.instance.videoSaveImageWay == 0){
					Initializer.bindDeviceOrNot();
					if(logged){
						log.info("[VideoHandleLog] [Cost] saveImage bindDeviceOrNot count: 1 costTime:[" + (System.currentTimeMillis() - now) + "]");
					}
					try {
						//now = 0L;
						//if(logged){
						//now = System.currentTimeMillis();
						//}
						encode_jpeg(frame, filePath);

						long saveCost = (System.currentTimeMillis() - now);
						if(logged){
							log.info("[VideoHandleLog] [Cost] saveImage encode_jpeg count: 1 costTime:[" + (System.currentTimeMillis() - now) + "]");
						}

						if(saveCost > Utils.instance.longCostSaveImgTs){
							log.warn("[VideoHandleLog] [Cost] saveImageNewFunc  videoSaveImageWay cost:{}", saveCost);
						}
						//encode_image_as_jpg_by_opencv4(frame, filePath);
					} finally {
						//batch_free_frame(cpuFrame);
					}
				}else if(Utils.instance.videoSaveImageWay == 1){
					Initializer.bindDeviceOrNot();
					if(logged){
						log.info("[VideoHandleLog] [Cost] saveImage bindDeviceOrNot count: 1 costTime:[" + (System.currentTimeMillis() - now) + "]");
					}
					try {
						//now = 0L;
						//if(logged){
						//now = System.currentTimeMillis();
						//}
						KestrelApi.kestrel_frame_save(frame, filePath);

						long saveCost = (System.currentTimeMillis() - now);
						if(logged){
							log.info("[VideoHandleLog] [Cost] saveImage kestrel_frame_save count: 1 costTime:[" + (System.currentTimeMillis() - now) + "]");
						}

						if(saveCost > Utils.instance.longCostSaveImgTs){
							log.warn("[VideoHandleLog] [Cost] saveImageGpu long cost:{}", saveCost);
						}
						//encode_image_as_jpg_by_opencv4(frame, filePath);
					} finally {
						//batch_free_frame(cpuFrame);
					}
				}else {
					NvJpgEncoders.encodeFrame2(frame, filePath);

					if(logged){
						log.info("[VideoHandleLog] [Cost] saveImage NvJpgEncoders.encodeFrame2 count: 1 costTime:[" + (System.currentTimeMillis() - now) + "]");
					}
				}
				return filePath;

			}else {
				//Initializer.bindDeviceOrNot();
				//Pointer cpuFrame = ref_or_download_frame(frame);
				try {
					if(Utils.instance.videoSaveImageWay == 0){
						//KestrelApi.kestrel_frame_save(frame, filePath);
						encode_jpeg(frame, filePath);
						long saveCost = (System.currentTimeMillis() - now);
						if(logged){
							log.info("[VideoHandleLog] [Cost] saveImageCpu encode_jpeg cpu count: 2 costTime:[" + saveCost + "]");
						}
						if(saveCost > Utils.instance.longCostSaveImgTs){
							log.warn("[VideoHandleLog] [Cost] saveImageCpu long cost:{}", saveCost);
						}

					}else{
						KestrelApi.kestrel_frame_save(frame, filePath);
						//encode_image_as_jpg_by_opencv4(frame, filePath);
						if(logged){
							log.info("[VideoHandleLog] [Cost] saveImageCpu encode_image_as_jpg_by_opencv4 count: 1 costTime:[" + (System.currentTimeMillis() - now) + "]");
						}
					}
					return filePath;
				} finally {
					//batch_free_frame(cpuFrame);
				}
			}
		} finally {
//			if (frame != inputFrame)
//				batch_free_frame(frame);
		}
	}

	public static byte[] encode_image_as_jpg(Pointer inputFrame) {
		Pointer frame = inputFrame;
		int mem_type = KestrelApi.kestrel_frame_mem_type(frame);

		Float videoSaveImageScale = Utils.instance.videoSaveImageScale;
		if(videoSaveImageScale != null) {
			int width  = KestrelApi.kestrel_frame_video_width(frame);
			int height = KestrelApi.kestrel_frame_video_height(frame);

			if(FramePoolUtils.bufferedSizeLimit.contains(width + "*" + height)) {
				int fmt = KestrelApi.kestrel_frame_video_format(frame);

				int scaleWidth  = (int)(videoSaveImageScale * width);
				int scaleHeight = (int)(videoSaveImageScale * height);

				PointerByReference buffered_frame = new PointerByReference();
				if(mem_type == KestrelApi.KESTREL_MEM_DEVICE)
					buffered_frame.setValue(FramePoolUtils.buffered(fmt, scaleWidth, scaleHeight));

				KestrelApi.kestrel_frame_scale(frame, buffered_frame, videoSaveImageScale);

				frame = buffered_frame.getValue();
			}
		}

		try {
			if(Utils.instance.videoSaveImageType == 0 && KestrelApi.KESTREL_MEM_DEVICE == mem_type) {

				return NvJpgEncoder.encodeFrame(frame);

			}else {
				Initializer.bindDeviceOrNot();
				Pointer cpuFrame = ref_or_download_frame(frame);
				try {
					return encode_image_as_jpg_by_opencv(cpuFrame);
				}finally {
					batch_free_frame(cpuFrame);
				}
			}
		}finally {
			if(frame != inputFrame)
				batch_free_frame(frame);
		}
	}

	/** 将cpu frame 编码成jpg 返回裸指针 这个指针需要close 否则内存泄漏*/
	private static byte[] encode_image_as_jpg_by_opencv(Pointer frame) {
		if(frame == null)
			return new byte[0];

		Pointer bgrFrame = FrameUtils.ref_or_cvtcolor_frame(frame, KestrelApi.KESTREL_VIDEO_BGR);

		int width = KestrelApi.kestrel_frame_video_width(bgrFrame);
		int height = KestrelApi.kestrel_frame_video_height(bgrFrame);
		int stride = KestrelApi.kestrel_frame_video_stride(bgrFrame, 0);
		int channels = 3;
		int depth = Frame.DEPTH_UBYTE;

		org.bytedeco.javacpp.Pointer data = new org.bytedeco.javacpp.Pointer(KestrelApi.kestrel_frame_plane(bgrFrame, 0).getByteBuffer(0, height * stride).position(0));
		Mat imgSrc = new Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels), data, stride * Math.abs(depth) / 8);
		Mat imgDst = new Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels));
		IntPointer param = new IntPointer(Utils.instance.imwriteFlags);
		try {
			BytePointer bufPointer = new BytePointer();
			opencv_imgcodecs.imencode(".jpg", imgSrc, bufPointer, param);
			return bufPointer.getStringBytes();
		} finally {
			imgSrc.close();
			imgDst.close();
			data.close();
			param.close();

			FrameUtils.batch_free_frame(bgrFrame);
		}
	}

	/** 将cpu frame 编码成jpg 返回裸指针 这个指针需要close 否则内存泄漏*/
	private static void encode_image_as_jpg_by_opencv4(Pointer frame, String filePath) {
		if (frame == null) { return; }

		Pointer bgrFrame = FrameUtils.ref_or_cvtcolor_frame(frame, KestrelApi.KESTREL_VIDEO_BGR);

		int width = KestrelApi.kestrel_frame_video_width(bgrFrame);
		int height = KestrelApi.kestrel_frame_video_height(bgrFrame);
		int stride = KestrelApi.kestrel_frame_video_stride(bgrFrame, 0);
		int channels = 3;
		int depth = Frame.DEPTH_UBYTE;

		org.bytedeco.javacpp.Pointer data = new org.bytedeco.javacpp.Pointer(KestrelApi.kestrel_frame_plane(bgrFrame, 0).getByteBuffer(0, height * stride).position(0));
		Mat imgSrc = new Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels), data, stride * Math.abs(depth) / 8);
		Mat imgDst = new Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels));
		IntPointer param = new IntPointer(Utils.instance.imwriteFlags);
		BytePointer bufPointer = new BytePointer();
		try {
			opencv_imgcodecs.imencode(".jpg", imgSrc, bufPointer, param);
			if (bufPointer.getStringBytes() != null && bufPointer.getStringBytes().length > 0) {
				// 写入文件
				try (FileOutputStream output = new FileOutputStream(filePath)) {
					output.write(bufPointer.getStringBytes());
				}
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			imgSrc.close();
			imgDst.close();
			data.close();
			param.close();
			bufPointer.close(); // 关闭字节指针对象
			FrameUtils.batch_free_frame(bgrFrame);
		}
	}

	private static byte[] encode_image_as_jpg_by_opencv3(Pointer frame,String filePath) {
		if(frame == null)
			return new byte[0];
		int width = KestrelApi.kestrel_frame_video_width(frame);
		int height = KestrelApi.kestrel_frame_video_height(frame);
		int stride = KestrelApi.kestrel_frame_video_stride(frame, 0);
		int channels = 3;
		int depth = Frame.DEPTH_UBYTE;
		try (org.bytedeco.javacpp.Pointer data = new org.bytedeco.javacpp.Pointer(KestrelApi.kestrel_frame_plane(frame, 0).getByteBuffer(0, height * stride).position(0));
			 org.bytedeco.opencv.opencv_core.Mat imgSrc = new org.bytedeco.opencv.opencv_core.Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels), data, stride * Math.abs(depth) / 8)) {
			opencv_imgcodecs.imwrite(filePath, imgSrc);
			imgSrc.release();
			data.close();
		}
		return new byte[0];
	}

	/**
	 * 将cpu frame 编码成jpg 返回裸指针 这个指针需要close 否则内存泄漏
	 */
	private static BytePointer encode_image_as_jpg_by_opencv2(Pointer frame, String filePath) {
		if (frame == null)
			return null;

		//Pointer bgrFrame = FrameUtils.ref_or_cvtcolor_frame(frame, KestrelApi.KESTREL_VIDEO_BGR);

		int width = KestrelApi.kestrel_frame_video_width(frame);
		int height = KestrelApi.kestrel_frame_video_height(frame);
		int stride = KestrelApi.kestrel_frame_video_stride(frame, 0);
		int channels = 3;
		int depth = Frame.DEPTH_UBYTE;

		org.bytedeco.javacpp.Pointer data = new org.bytedeco.javacpp.Pointer(KestrelApi.kestrel_frame_plane(frame, 0).getByteBuffer(0, (long) height * stride).position(0));
		Mat imgSrc = new Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels), data, (long) stride * Math.abs(depth) / 8);
		//Mat imgDst = new Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels));
		IntPointer param = new IntPointer(Utils.instance.imwriteFlags);
		//BytePointer bufPointer = new BytePointer();
		try {
			//opencv_imgcodecs.imencode(".jpg", imgSrc, bufPointer);
			opencv_imgcodecs.imwrite(filePath, imgSrc, param);
			return null;
		} catch (Exception e) {
			log.error("saveImageErr", e);
		} finally {
			data.close();
			imgSrc.release();
			imgSrc = null;
			data = null;
			//imgDst.release();
			param.close();
			//bufPointer.close();

			//FrameUtils.batch_free_frame(bgrFrame);
		}
		return null;
	}

	public static Mat grayFrameToMat(Pointer gpuFrame) {
		int width = KestrelApi.kestrel_frame_video_width(gpuFrame);
		int height = KestrelApi.kestrel_frame_video_height(gpuFrame);
		int stride = KestrelApi.kestrel_frame_video_stride(gpuFrame, 0);
		int channels = 1;
		int depth = Frame.DEPTH_UBYTE;

		org.bytedeco.javacpp.Pointer data = new org.bytedeco.javacpp.Pointer(KestrelApi.kestrel_frame_plane(gpuFrame, 0).getByteBuffer(0, height * stride).position(0));

		return new Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels), data, stride * Math.abs(depth) / 8);
	}
// todo 错误处理
	public static String separatePathBeforeDate(String imageUrl){
		Path path = Path.of(imageUrl);
		int index = 0;
		int dateIndex = -1;
		for (Path part : path) {
			Matcher matcher = datePattern.matcher(part.toString());
			if (matcher.matches()) {
				dateIndex = index;
				break;
			}
			index++;
		}
		// 如果未找到日期部分，返回 null
		if (dateIndex == -1) {
			return path.getParent().toString();
		}
		// 构建前缀路径
		Path prefixPath = path.getRoot(); // 获取根路径（如 "/"）
		if (prefixPath == null) {
			prefixPath = Paths.get("");
		}
		if(dateIndex >= 1){
			prefixPath = path.getName(dateIndex - 1);
			return prefixPath.toString();
		}

		return path.getParent().toString();

	}

	public static String getBucketNameFromUrl(String imageUrl){
		String bucketName = separatePathBeforeDate(imageUrl);
		if(bucketName.contains("tmp_ped")) return "faceped";
		if(bucketName.contains("tmp_face")) return "face";
		return bucketName;
	}

	/** 传入frame变成 ref
	 * @param frames
	 * @return
	 */
	public static Pointer ref_frame(Pointer frame) {
		if(frame == null)
			return null;

		return KestrelApi.kestrel_frame_ref(frame);
	}
	public static kestrel_packet_t ref_packet(kestrel_packet_t frame) {
		if(frame == null)
			return null;

		return KestrelApi.kestrel_packet_ref(frame);
	}

	public static void free_packet(PointerByReference frame) {
		if(frame == null)
			return ;

		KestrelApi.kestrel_packet_free(frame);
	}

	public static Pointer ref_or_download_frame(Pointer frame) {
		if(frame == null)
			return null;

		if(KestrelApi.KESTREL_MEM_DEVICE == KestrelApi.kestrel_frame_mem_type(frame)) {
			PointerByReference kestrel_cpu_frame = new PointerByReference();
			int ret = KestrelApi.kestrel_frame_download(frame, kestrel_cpu_frame);
			if(ret == 0)
				return kestrel_cpu_frame.getValue();
			else
				throw new RuntimeException("can not download frame.");
		}else {
			return FrameUtils.ref_frame(frame);
		}
	}

	public static Pointer ref_or_upload_frame(Pointer frame) {
		if(frame == null)
			return null;

		if(!Initializer.isGpu() || KestrelApi.KESTREL_MEM_DEVICE == KestrelApi.kestrel_frame_mem_type(frame)) {
			return FrameUtils.ref_frame(frame);
		}else {
			PointerByReference kestrel_gpu_frame = new PointerByReference();
			int ret = KestrelApi.kestrel_frame_upload(frame, kestrel_gpu_frame);
			if(ret == 0)
				return kestrel_gpu_frame.getValue();
			else
				return null;
		}
	}

	public static Pointer ref_or_upload_buffered_frame(Pointer frame) {
		if(frame == null)
			return null;

		if(!Initializer.isGpu() || KestrelApi.KESTREL_MEM_DEVICE == KestrelApi.kestrel_frame_mem_type(frame)) {
			return FrameUtils.ref_frame(frame);
		}else {
			int fmt = KestrelApi.kestrel_frame_video_format(frame);
			int width  = KestrelApi.kestrel_frame_video_width(frame);
			int height = KestrelApi.kestrel_frame_video_height(frame);

			Pointer pooledFrame = null;
			if(FramePoolUtils.bufferedSizeLimit.contains(width + "*" + height))
				pooledFrame = FramePoolUtils.buffered(fmt, width, height);

			PointerByReference kestrel_gpu_frame = new PointerByReference(pooledFrame);
			int ret = KestrelApi.kestrel_frame_upload(frame, kestrel_gpu_frame);
			if(ret == 0) {
				return kestrel_gpu_frame.getValue();
			}else {
				if(pooledFrame != null)
					batch_free_frame(pooledFrame);

				return null;
			}
		}
	}

	public static Pointer ref_or_cvtcolor_frame(Pointer frame, int video_format) {
		if(frame == null)
			return null;

		if(KestrelApi.kestrel_frame_video_format(frame) == video_format)
			return FrameUtils.ref_frame(frame);

		PointerByReference cvtFrame = new PointerByReference();
		int ret = KestrelApi.kestrel_frame_cvt_color(frame, cvtFrame, video_format);
		if(ret == 0)
			return cvtFrame.getValue();
		else
			return null;
	}

	public static Pointer ref_or_cvtcolor_buffered_frame(Pointer frame, int fmt) {
		if(frame == null)
			return null;

		int memType = KestrelApi.kestrel_frame_mem_type(frame);
		if(KestrelApi.KESTREL_MEM_UNKNOWN == memType)
			return null;

		if(KestrelApi.kestrel_frame_video_format(frame) == fmt)
			return FrameUtils.ref_frame(frame);

		if(KestrelApi.KESTREL_MEM_HOST == memType) {
			PointerByReference cvtFrame = new PointerByReference();
			int ret = KestrelApi.kestrel_frame_cvt_color(frame, cvtFrame, fmt);
			if(ret == 0)
				return cvtFrame.getValue();
			else
				return null;
		}else{
			int width  = KestrelApi.kestrel_frame_video_width(frame);
			int height = KestrelApi.kestrel_frame_video_height(frame);

			Pointer pooledFrame = null;
			if(FramePoolUtils.bufferedSizeLimit.contains(width + "*" + height))
				pooledFrame = FramePoolUtils.buffered(fmt, width, height);

			PointerByReference cvtFrame = new PointerByReference(pooledFrame);
			int ret = KestrelApi.kestrel_frame_cvt_color(frame, cvtFrame, fmt);
			if(ret == 0) {
				return cvtFrame.getValue();
			}else {
				if(pooledFrame != null)
					batch_free_frame(pooledFrame);

				return null;
			}
		}
	}

	public static synchronized VideoFrame fetch_up_down_load_frame_path(String rtspSource) {
		boolean isGpu = Initializer.isGpu();
		return fetch_up_down_load_frame_path(rtspSource, isGpu, !isGpu, isGpu);
	}

	public static synchronized VideoFrame fetch_up_down_load_frame_path(String rtspSource, boolean useGpuDecoder, boolean fetchCpu, boolean fetchGpu) {
		Utils.instance.useH265 = 2;
		VideoStream stream = new VideoStream(VideoStreamInfra.builder().deviceId(rtspSource).rtspSource(rtspSource).frameBuffer(0).frameBufferStrategy("smart").build());
		stream.setUseDeviceDecoder(useGpuDecoder);
		stream.setH265CodeC(false);
		int frameRate = 0;
		try {
			stream.start();
			frameRate = (Integer) VideoStream.chosenStreamInfoMap.get(rtspSource).get("frameRate");
		}catch(Exception e) {
			log.warn("fetchFrame error {},rtspSource:{},retry once", e.getMessage(), rtspSource);
			//e.printStackTrace();
			try {
				stream.stop();
			} catch (Exception ex) {}

			Utils.instance.useH265 = 2;
			//重试一次?
			try {
				stream.start();
			} catch (Exception exe) {
				//e.printStackTrace();
				log.error("fetchFrame retry failed {},rtspSource:{} ", exe.getMessage(), rtspSource);
				try {
					stream.stop();
				} catch (Exception ex) {}

				throw new BusinessException("1001", "decode frame error");
			}
		}
		log.info("this open stream codec type h265: {}", stream.h265CodeC);

		VideoFrame videoFrame = VideoFrame.builder().build();

		int randomNumber = Utils.instance.fetchFrameSize;

		if(stream.h265CodeC){

			for(int index = 0; index < 300; index ++) {
				try {
					videoFrame = stream.grabberNextFrame(fetchCpu, fetchGpu);
					if(index > randomNumber){
						if(videoFrame.getFrame() !=null && !FrameUtils.isGreenScreen(videoFrame.getFrame())){
							break;
						}else{
							FrameUtils.batch_free_frame(videoFrame);
						}
					}else{
						FrameUtils.batch_free_frame(videoFrame);
					}

				}catch(Exception e) {}
			}
		}else{

			for(int index = 0; index < 300; index ++) {
				try {
					videoFrame = stream.grabberNextFrame(fetchCpu, fetchGpu);
					if(index > randomNumber){
						if(videoFrame.getFrame() !=null && !FrameUtils.isGreenScreen(videoFrame.getFrame())){
							break;
						}else{
							FrameUtils.batch_free_frame(videoFrame);
						}
					}else{
						FrameUtils.batch_free_frame(videoFrame);
					}

				}catch(Exception e) {}
			}
		}

		stream.stop();
		Utils.instance.useH265 = 1;
		if(videoFrame.getFrame() == null){
			log.error("fetch frame is null: rtsp:{},h265CodeC:{}", rtspSource, stream.h265CodeC);
		}else{
			log.info("fetch frame success: {},{}",rtspSource, stream.h265CodeC);
		}
		videoFrame.setRFrameRate(String.valueOf(frameRate));

		return videoFrame;
	}

	public static synchronized VideoFrame fetch_up_down_load_video_path(String rtspSource,String savePath,int frameSkip,int headRangeNum) {
		boolean isGpu = Initializer.isGpu();
		return fetch_up_down_load_video_path(rtspSource, isGpu, !isGpu, isGpu,savePath,frameSkip,headRangeNum);
	}

	public static synchronized VideoFrame fetch_up_down_load_video_path(String rtspSource, boolean useGpuDecoder, boolean fetchCpu, boolean fetchGpu,String savePath,int frameSkip,int headRangeNum) {
		VideoStream stream = new VideoStream(VideoStreamInfra.builder().deviceId(rtspSource).rtspSource(rtspSource).frameBuffer(0).frameBufferStrategy("smart").build());
		stream.setUseDeviceDecoder(useGpuDecoder);
		stream.start();
		VideoFrame videoFrame = VideoFrame.builder().build();
		boolean keepRunning = true;
		while(keepRunning && videoFrame.frameIndex < headRangeNum ){
			try {
				boolean handleCurrentFrame = videoFrame.frameIndex % (Math.abs(frameSkip) + 1) == 0;
				if(videoFrame.frameIndex==-1){
					handleCurrentFrame = true;
				}
				videoFrame = stream.grabberNextFrame(fetchCpu& handleCurrentFrame, fetchGpu& handleCurrentFrame);
				String path = FrameUtils.save_image_as_jpg(videoFrame.getFrame(), ImageUtils.newFileWithTs(savePath, videoFrame.frameIndex),0);
				log.info("fetchFrame imagePath:{}", path);
				FrameUtils.batch_free_frame(videoFrame);
			}catch(Exception e) {
				if("KPLUGIN_E_EOF".equals(e.getMessage())) {
					keepRunning = false;
				}
			}finally {
				FrameUtils.batch_free_frame(videoFrame);
			}

		}
		stream.stop();

		return videoFrame;
	}

	public static class FramePoolUtils{
		public static volatile Set<String> bufferedSizeLimit = Set.of("1920*1080", "2560*1440", "3840*2160", "4096*2160", "960*720", "1280*720", "640*480");

		private static final ThreadLocal<String> bufferKey = new ThreadLocal<String>();

		private static final ConcurrentHashMap<String, Pointer> framePool = new ConcurrentHashMap<String, Pointer>();

		public static void useBufferKey(String key) {
			bufferKey.set(key);
		}

		public static Pointer buffered(int fmt, int width, int height) {
			Initializer.bindDeviceOrNot();

			String groupKey = bufferKey.get();
			bufferKey.remove();

			String key = Objects.requireNonNullElse(groupKey, "default") + fmt + "_" + width + "_" + height;

			Pointer pool = framePool.get(key);
			if(pool != null)
				return KestrelApi.kestrel_frame_pool_get(pool);

			synchronized(framePool) {
				pool = framePool.get(key);
				if(pool != null)
					return KestrelApi.kestrel_frame_pool_get(pool);

				log.info("creating frame pool (" + fmt + "," + width + "," + height + ", " + Utils.instance.frameBufferSize + ")");
				pool = KestrelApi.kestrel_frame_pool_alloc(KestrelApi.KESTREL_MEM_DEVICE, fmt, width, height, 0, Utils.instance.frameBufferSize);

				framePool.put(key, pool);
			}

			return KestrelApi.kestrel_frame_pool_get(pool);
		}
	}

	@Getter
	@Setter
	@Builder
	public static class ImageCache{
		private volatile String path;
		private Long time;
		private CountDownLatch latch;
	}


	public static  byte[]  encode_jpeg(Pointer frame, String  filePath) {

//		jpeg_encoderLibrary library = jpeg_encoderLibrary.INSTANCE;
//		int width = KestrelApi.kestrel_frame_video_width(frame);
//		int height = KestrelApi.kestrel_frame_video_height(frame);
//		int stride = KestrelApi.kestrel_frame_video_stride(frame, 0);

		Initializer.bindDeviceOrNot();
		//Pointer cpuFrame = ref_or_download_frame(frame);

		//int frameSize = height *stride;
//		// Allocate memory using JNA Memory class
//		Memory data = new Memory(frameSize);
//
//		// Copy the frame data from the Pointer to the allocated Memory
//		data.write(0, frame.getByteArray(0, frameSize), 0, frameSize);

//		ByteBuffer data = KestrelApi.kestrel_frame_plane(frame, 0).getByteBuffer(0, height * stride).position(0);
//		int mem_type = KestrelApi.kestrel_frame_mem_type(frame);
//		log.info("encode_jpeg_mem_type{}", mem_type);

//		PointerByReference jpegDataRef = new PointerByReference();
//		IntByReference jpegDataSizeRef = new IntByReference();
		try{

			//synchronized(frame) {
			int result = KestrelApi.kestrel_frame_jpeg4(frame, filePath);
			//int result2 = KestrelApi.kestrel_frame_jpeg3( width);

			//batch_free_frame(cpuFrame);
			if (result == 0) {
				//				Pointer jpegDataPointer = jpegDataRef.getValue();
				//				int jpegDataSize = jpegDataSizeRef.getValue();
				//
				//				if(jpegDataSize > 0) {
				//					//jpegDataPointer.clear(jpegDataSize);
				//					//KestrelApi.kestrel_frame_free(jpegDataRef);
				//					// Access the jpeg data and size here
				//					//jpegDataPointer.clear(jpegDataSize);
				//					return jpegDataPointer.getByteArray(0, jpegDataSize);
				//				}
				// Don't forget to free the memory allocated by C function
				//library.SomeFreeFunction(jpegDataPointer);
			} else {
				// Error handling
				log.info("saveImageErr{}", result);
			}
			//}


		}catch (Exception e){

		}finally {
			//data.clear();

		}

		return null;


	}




	private static final double GREEN_THRESHOLD = 0.5; // 蓝色像素占比阈值


	public static boolean isGreenScreen(Pointer frame) {
		if (frame == null) {
			return true;
		}
		if(!Initializer.isDevice()){
			return false;
		}

		Initializer.bindDeviceOrNot();
		Pointer cpuFrame = ref_or_download_frame(frame);
		Pointer bgrFrame = FrameUtils.ref_or_cvtcolor_frame(cpuFrame, KestrelApi.KESTREL_VIDEO_BGR);

		int width = KestrelApi.kestrel_frame_video_width(cpuFrame);
		int height = KestrelApi.kestrel_frame_video_height(cpuFrame);
		int stride = KestrelApi.kestrel_frame_video_stride(cpuFrame, 0);
		int channels = 3;
		int depth = opencv_core.CV_8U;

		BytePointer data = new BytePointer(KestrelApi.kestrel_frame_plane(bgrFrame, 0).getByteBuffer(0, height * stride).position(0));
		Mat imgSrc = new Mat(height, width, opencv_core.CV_MAKETYPE(depth, channels), data, stride);

		// 转换为HSV颜色空间
		Mat hsvImg = new Mat();
		try {
			opencv_imgproc.cvtColor(imgSrc, hsvImg, opencv_imgproc.COLOR_BGR2HSV);
			//System.out.println("HSV conversion successful.");

		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

		// HSV范围
		int lowerHue = 35;
		int upperHue = 85;
		int lowerSaturation = 100;
		int upperSaturation = 255;
		int lowerValue = 100;
		int upperValue = 255;

		// 遍历每个像素，检查是否在绿色范围内
		int greenPixelCount = 0;
		byte[] pixelData = new byte[(int) (hsvImg.total() * hsvImg.channels())];
		hsvImg.data().get(pixelData);

		for (int i = 0; i < pixelData.length; i += 3) {
			int h = pixelData[i] & 0xFF;
			int s = pixelData[i + 1] & 0xFF;
			int v = pixelData[i + 2] & 0xFF;

			if (h >= lowerHue && h <= upperHue && s >= lowerSaturation && s <= upperSaturation && v >= lowerValue && v <= upperValue) {
				greenPixelCount++;
			}
		}

		int totalPixels = width * height;
		double greenRatio = (double) greenPixelCount / totalPixels;

		// 释放资源
		imgSrc.close();
		hsvImg.close();
		data.close();
		FrameUtils.batch_free_frame(bgrFrame);
		FrameUtils.batch_free_frame(cpuFrame);

		// 输出调试信息
//		System.out.println("Total Pixels: " + totalPixels);
//		System.out.println("Green Pixels: " + greenPixelCount);
//		System.out.println("Green Ratio: " + greenRatio);

		// 判断绿色像素比例是否超过阈值
		return greenRatio > GREEN_THRESHOLD;
	}
}