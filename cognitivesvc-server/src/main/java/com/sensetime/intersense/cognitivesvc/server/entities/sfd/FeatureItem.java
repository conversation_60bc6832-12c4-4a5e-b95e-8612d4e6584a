package com.sensetime.intersense.cognitivesvc.server.entities.sfd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SFD特征项实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeatureItem {
    String id; //对应person externalUuid
    String seq_id; // sfd batch_get 接口有返回这个字段
    InsightFeature feature;

    //  特征对应图片的ID，update时候没有这个参数
    String image_id;

    //  可选, 业务附带额外信息, 不超过1KB.
    String extra_info;

    String meta_data; // sfd batch_get 接口有返回这个字段

    // 可选, 用户自定义key，update时候没有这个参数
    String key;
} 