package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Callback;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_plugin.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_plugin_t extends Structure {
	/** C type : const char* */
	public String plugin_name;
	public int type;
	/** C type : version_callback* */
	public Pointer version;
	/** C type : revision_callback* */
	public Pointer revision;
	/** C type : install_fx_callback* */
	public Pointer install_fx;
	/** C type : uninstall_fx_callback* */
	public Pointer uninstall_fx;
	/** C type : create_fx_callback* */
	public Pointer create_fx;
	/** C type : destroy_fx_callback* */
	public Pointer destroy_fx;
	/** C type : void* */
	public Pointer ppi;
	
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public interface version_callback extends Callback {
		String apply();
	};
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public interface revision_callback extends Callback {
		String apply();
	};
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public interface install_fx_callback extends Callback {
		Pointer apply();
	};
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public interface uninstall_fx_callback extends Callback {
		void apply(Pointer voidPtr1);
	};
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public interface create_fx_callback extends Callback {
		Pointer apply(Pointer voidPtr1);
	};
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public interface kestrel_bson_callback extends Callback {
		int apply(Pointer voidPtr1, int kestrel_ctrl_type_t1, Pointer charPtr1);
	};
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public interface destroy_fx_callback extends Callback {
		void apply(Pointer voidPtr1);
	};
	public kestrel_plugin_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("plugin_name", "type", "version", "revision", "install_fx", "uninstall_fx", "create_fx", "destroy_fx", "ppi");
	}
	/**
	 * @param plugin_name C type : const char*<br>
	 * @param version C type : version_callback*<br>
	 * @param revision C type : revision_callback*<br>
	 * @param install_fx C type : install_fx_callback*<br>
	 * @param uninstall_fx C type : uninstall_fx_callback*<br>
	 * @param create_fx C type : create_fx_callback*<br>
	 * @param destroy_fx C type : destroy_fx_callback*<br>
	 * @param ppi C type : void*
	 */
	public kestrel_plugin_t(String plugin_name, int type, Pointer version, Pointer revision, Pointer install_fx, Pointer uninstall_fx, Pointer create_fx, Pointer destroy_fx, Pointer ppi) {
		super();
		this.plugin_name = plugin_name;
		this.type = type;
		this.version = version;
		this.revision = revision;
		this.install_fx = install_fx;
		this.uninstall_fx = uninstall_fx;
		this.create_fx = create_fx;
		this.destroy_fx = destroy_fx;
		this.ppi = ppi;
	}
	public kestrel_plugin_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_plugin_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_plugin_t implements Structure.ByValue {
		
	};
}
