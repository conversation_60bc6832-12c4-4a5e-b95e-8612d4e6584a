package com.sensetime.intersense.cognitivesvc.server.vaxtor.demo;

import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.Vaxtor_ocrLibrary;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.tAnalytics;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.tInitMMC;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.tPlateInfo;
import com.sun.jna.Memory;
import com.sun.jna.NativeLong;
import com.sun.jna.ptr.NativeLongByReference;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.file.Files;
import java.nio.file.Paths;

public class VaxtorOCRTest {
    private static final Vaxtor_ocrLibrary OCR_LIB = Vaxtor_ocrLibrary.INSTANCE;
    private static NativeLong ocrId = new NativeLong(-1);

    // 检查文件是否为JPEG
    private static boolean hasJpegExtension(String fileName) {
        if (fileName == null || fileName.length() < 4) return false;
        String ext = fileName.substring(fileName.length() - 3).toLowerCase();
        return ext.equals("jpg") || ext.equals("jpeg");
    }

    // 获取文件大小L
    private static long getFileSize(String fileName) {
        try {
            return Files.size(Paths.get(fileName));
        } catch (IOException e) {
            System.out.println("Could not open specified file: " + e.getMessage() + ", " + fileName);
            return 0;
        }
    }

    // 查找车牌
    private static int findPlate(String fileName) {
        try {
            // 获取文件大小
            long fileSize = getFileSize(fileName);
            if (fileSize == 0) return -1;

            // 读取图片数据
            byte[] fileData = Files.readAllBytes(Paths.get(fileName));

            // 查找车牌
            if (hasJpegExtension(fileName)) {
                return OCR_LIB.ocrFindPlatesJPEG(ocrId, fileData, new NativeLong(fileSize), 0, 0).intValue();
            } else {
                return OCR_LIB.ocrFindPlatesGray8(ocrId, fileData, new NativeLong(1280), new NativeLong(960), 0, 0).intValue();
            }
        } catch (IOException e) {
            System.err.println("Error reading file: " + e.getMessage());
            return -1;
        }
    }

    // 转换字节数组为字符串，去除空字节
    private static String byteArrayToString(byte[] bytes) {
        int length;
        for (length = 0; length < bytes.length && bytes[length] != 0; length++);
        return new String(bytes, 0, length);
    }

    // 测试单个文件
    private static int testFile(String fileName) {
        tPlateInfo plateInfo = new tPlateInfo();
        tAnalytics analytics = new tAnalytics();

        int numPlates = findPlate(fileName);
        if (numPlates < 0) {
            byte[] message = new byte[256];
            ByteBuffer buffer = ByteBuffer.wrap(message);
            OCR_LIB.ocrGetErrorDescription(new NativeLong(numPlates), buffer);
            System.err.println("Error calling ocrFindPlatesJPEG() function: " + new String(message).trim());
            return -1;
        }

        if (numPlates == 0) {
            System.out.println("Plate number : not found");
        } else {
            OCR_LIB.ocrGetPlateInfo(ocrId, new NativeLong(0), plateInfo, analytics);
            
            // 修改打印方式，使用转换后的字符串
            System.out.println("Plate number..... " + byteArrayToString(plateInfo._plate_number_ascii));
            System.out.println("Country.......... " + byteArrayToString(plateInfo._plate_country));
            System.out.println("Make............. " + byteArrayToString(analytics._vehicle_make));
            System.out.println("Model............ " + byteArrayToString(analytics._vehicle_model));
            System.out.println("Color............ " + byteArrayToString(analytics._vehicle_color));
            System.out.println("Class............ " + byteArrayToString(analytics._vehicle_class));
            System.out.println("OCR Time......... " + OCR_LIB.ocrGetReadTime(ocrId));
            
            // 对于边界框数组，需要先检查是否为null
            if (plateInfo._plate_bounding_box != null && plateInfo._plate_bounding_box.length >= 4) {
                System.out.println("Plate BB......... " + 
                    (plateInfo._plate_bounding_box[0] != null ? plateInfo._plate_bounding_box[0].longValue() : "N/A") + ", " +
                    (plateInfo._plate_bounding_box[1] != null ? plateInfo._plate_bounding_box[1].longValue() : "N/A") + ", " +
                    (plateInfo._plate_bounding_box[2] != null ? plateInfo._plate_bounding_box[2].longValue() : "N/A") + ", " +
                    (plateInfo._plate_bounding_box[3] != null ? plateInfo._plate_bounding_box[3].longValue() : "N/A"));
            }
        }
        return 1;
    }

    // 测试文件夹
    private static void testFolder(String dirPath) {
        int numFiles = 0, numMissed = 0, numFailed = 0, numCorrect = 0;
        double ocrTime = 0;

        System.out.println("\nPerforming VaxOCR Engine benchmark....");
        System.out.println("==============================================================\n");

        File dir = new File(dirPath);
        if (!dir.isDirectory()) {
            System.err.println("Invalid directory path");
            return;
        }

        for (File file : dir.listFiles()) {
            if (!file.isFile() || !hasJpegExtension(file.getName())) continue;

            int numPlates = findPlate(file.getAbsolutePath());

            if (numPlates == 0) {
                numMissed++;
            } else {
                boolean matched = false;
                tPlateInfo plateInfo = new tPlateInfo();
                tAnalytics plateAnalytic = new tAnalytics();

                String expectedPlate = file.getName().substring(0, file.getName().lastIndexOf('.'));

                for (int i = 0; i < numPlates; i++) {
                    OCR_LIB.ocrGetPlateInfo(ocrId, new NativeLong(i), plateInfo, plateAnalytic);
                    if (expectedPlate.equals(plateInfo._plate_number_ascii)) {
                        numCorrect++;
                        matched = true;
                        break;
                    }
                }

                if (!matched) {
                    numFailed++;
                }
            }

            numFiles++;
            double perMissed = 100.0 * numMissed / numFiles;
            double perFailed = 100.0 * numFailed / numFiles;
            double perCorrect = 100.0 * numCorrect / numFiles;

            ocrTime += OCR_LIB.ocrGetReadTime(ocrId);

            System.out.printf("\rMissed:%2.4f%%  Failed:%2.4f%%  Correct:%2.4f%% (%d) -- Total:%d       ",
                    perMissed, perFailed, perCorrect, numCorrect, numFiles);
        }
        System.out.println();
    }

    public static void main(String[] args) {
        if (args.length != 3) {
            System.out.println("Use: VaxtorOCRTest <ocr complexity:1...3> <-f,-d> <image_jpeg,directory_jpeg>");
            return;
        }

        OCR_LIB.ocrSetFullPathDataFiles("/sensetime/javacvDemo/src/main/java/com/sensetime/intersense/cognitivesvc/server/vaxtor_c/ocr_data.bin");

        if (!args[1].equals("-f") && !args[1].equals("-d")) {
            System.out.println("Use: VaxtorOCRTest <ocr complexity:1...3> <-f,-d> <image_jpeg,directory_jpeg>");
            return;
        }

        // 设置国家
        int numCountries = 2;
        NativeLong[] codeCountries = new NativeLong[numCountries];
        codeCountries[0] = OCR_LIB.ocrGetCountryStateCode("Ireland", null);
        codeCountries[1] = OCR_LIB.ocrGetCountryStateCode("Ireland", null);

        // OCR复杂度
        int ocrComplexity = Integer.parseInt(args[0]);
        if (ocrComplexity < 1 || ocrComplexity > 3) {
            System.out.println("Invalid algorithm complexity value");
            return;
        }

        // 创建 Memory 来存储国家数组
        Memory countriesMemory = new Memory(NativeLong.SIZE * numCountries);
        for (int i = 0; i < numCountries; i++) {
            countriesMemory.setNativeLong(i * NativeLong.SIZE, codeCountries[i]);
        }
        NativeLongByReference countriesRef = new NativeLongByReference();
        countriesRef.setPointer(countriesMemory);

        // 初始化参数
        ocrId = OCR_LIB.ocrInitializeEx(
                new NativeLong(1),
                countriesRef,
                new NativeLong(numCountries),
                new NativeLong(0),
                new NativeLong(14),
                new NativeLong(60),
                new NativeLong(ocrComplexity),
                new NativeLong(0)
        );

        if (ocrId.intValue() < 0) {
            byte[] message = new byte[256];
            ByteBuffer buffer = ByteBuffer.wrap(message);
            OCR_LIB.ocrGetErrorDescription(ocrId, buffer);
            System.err.println("Error initializing OCR Engine. Code = " + ocrId + ", " + new String(message).trim());
            return;
        }

        // 配置MMC
        tInitMMC mmc = new tInitMMC();
        mmc.analytic_type = new NativeLong(3);
        mmc.analytics_quality = new NativeLong(1);
        mmc.min_global_confidence = new NativeLong(20);
        NativeLong mmcOut = OCR_LIB.ocrConfigAnalyticsMMC(ocrId, mmc);

        if (mmcOut.intValue() <= 0) {
            byte[] message = new byte[256];
            ByteBuffer buffer = ByteBuffer.wrap(message);
            OCR_LIB.ocrGetErrorDescription(mmcOut, buffer);
            System.err.println("Error initializing VaxMMC Engine. Code = " + mmcOut + ", " + new String(message).trim());
            return;
        }

        try {
            if (args[1].equals("-f")) {
                testFile(args[2]);
            } else {
                testFolder(args[2]);
            }
        } finally {
            OCR_LIB.ocrShutdown(ocrId);
        }
    }
} 