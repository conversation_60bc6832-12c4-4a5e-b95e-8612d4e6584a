package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;

import com.sun.jna.Callback;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.ptr.PointerByReference;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_device_def.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_device_api_t extends Structure {
	/** C type : mem_alloc_callback* */
	public kestrel_device_api_t.mem_alloc_callback mem_alloc;
	/** C type : mem_calloc_callback* */
	public kestrel_device_api_t.mem_calloc_callback mem_calloc;
	/** C type : mem_realloc_callback* */
	public kestrel_device_api_t.mem_realloc_callback mem_realloc;
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface mem_alloc_callback extends Callback {
		Pointer apply(Pointer hdl, long size, int policy);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface mem_calloc_callback extends Callback {
		Pointer apply(Pointer hdl, long nmemb, long size, int policy);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface mem_realloc_callback extends Callback {
		Pointer apply(Pointer hdl, Pointer ptr, long size, int policy);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback extends Callback {
		int apply(Pointer hdl, Pointer ptr);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback2 extends Callback {
		int apply(Pointer hdl, Pointer ptr, byte value, long size);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback3 extends Callback {
		int apply(Pointer hdl, Pointer src, Pointer dst, long size, int t);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback4 extends Callback {
		int apply(Pointer hdl, Pointer src, Pointer dst, long size, int t, PointerByReference e);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback5 extends Callback {
		int apply(Pointer hdl, Pointer src, long src_pitch, Pointer dst, long dst_pitch, long width, long height, int t);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback6 extends Callback {
		int apply(Pointer hdl, Pointer src, long src_pitch, Pointer dst, long dst_pitch, long width, long height, int t, PointerByReference e);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback7 extends Callback {
		int apply(Pointer hdl, Pointer event);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback8 extends Callback {
		int apply(Pointer hdl, Pointer src, PointerByReference dst);
	};
	/** <i>native declaration : include/kestrel_device_def.h</i> */
	public interface k_err_callback9 extends Callback {
		int apply(Pointer hdl, Pointer ptr);
	};
	public kestrel_device_api_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("mem_alloc", "mem_calloc", "mem_realloc");
	}
	/**
	 * @param mem_alloc C type : mem_alloc_callback*<br>
	 * @param mem_calloc C type : mem_calloc_callback*<br>
	 * @param mem_realloc C type : mem_realloc_callback*
	 */
	public kestrel_device_api_t(kestrel_device_api_t.mem_alloc_callback mem_alloc, kestrel_device_api_t.mem_calloc_callback mem_calloc, kestrel_device_api_t.mem_realloc_callback mem_realloc) {
		super();
		this.mem_alloc = mem_alloc;
		this.mem_calloc = mem_calloc;
		this.mem_realloc = mem_realloc;
	}
	public kestrel_device_api_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_device_api_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_device_api_t implements Structure.ByValue {
		
	};
}
