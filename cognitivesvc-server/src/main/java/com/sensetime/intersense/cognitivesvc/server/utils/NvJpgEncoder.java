package com.sensetime.intersense.cognitivesvc.server.utils;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_packet_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NvJpgEncoder {
	
	private static Pointer imagesharp_annotator;
	private static final LinkedBlockingQueue<BatchItem> encodingQueue = new LinkedBlockingQueue<BatchItem>(Initializer.batchSize + 8);
	
	public static byte[] encodeFrame(Pointer frame) {
		initialize();
		
		BatchItem item = new BatchItem(frame);
		try { 
			encodingQueue.put(item); 
			item.getLatch().await(); 
		} catch (Exception e) { 
			e.printStackTrace();
		}
		
		Pointer packet = item.getPacket_keson();
		if(packet == null)
			return new byte[0];
		
		try {
			kestrel_packet_t packet_t = new kestrel_packet_t(KestrelApi.keson_get_ext_data(packet).getValue());
			packet_t.read();
			
			if(packet_t.size > 0)
				return packet_t.data.getByteArray(0, packet_t.size);
			else
				return new byte[0];
		}finally {
			KesonUtils.kesonDeepDelete(packet);
		}
	}
	
	private static Pointer[] encodeBatchFrame(Pointer... frames){
		Initializer.bindDeviceOrNot();
		
		long now = 0;
		if(Utils.instance.watchFrameTiktokLevel == -567)	
			now = System.currentTimeMillis();
		
		Pointer[] rgbFrames = new Pointer[frames.length];
		for(int index = 0; index < frames.length; index ++)
			rgbFrames[index] = FrameUtils.ref_or_cvtcolor_buffered_frame(frames[index], KestrelApi.KESTREL_VIDEO_BGR);
		
		Pointer input = KestrelApi.keson_create_object();
		KestrelApi.keson_add_item_to_object(input, "id", KestrelApi.keson_create_int(0));
		
		Pointer images_array = KestrelApi.keson_create_array();
		KestrelApi.keson_add_item_to_object(input, "images", images_array);
		
		for(int index = 0; index < rgbFrames.length; index ++) {
			Pointer image = KestrelApi.keson_create_object();
			KestrelApi.keson_add_item_to_array(images_array, image);
			
			KestrelApi.keson_add_item_to_object(image, "id", KestrelApi.keson_create_int(index));
			KestrelApi.keson_add_item_to_object(image, "image", KestrelApi.keson_create_ext_frame(rgbFrames[index]));
		}
		
		Pointer[] result = new Pointer[rgbFrames.length];
		PointerByReference out = new PointerByReference();
		synchronized(imagesharp_annotator) {
			KestrelApi.kestrel_annotator_process(imagesharp_annotator, input, out);
		}

		if(Utils.instance.watchFrameTiktokLevel == -567)
			log.info("nvencoder encode count:[" + frames.length + "] costTime:[" + (System.currentTimeMillis() - now) + "]");
		
		Pointer packetItems = KestrelApi.keson_get_object_item(out.getValue(), "packets");
		int size = KestrelApi.keson_array_size(packetItems);
		for(int index = 0; index < size; index ++) {
			Pointer packetItem = KestrelApi.keson_get_array_item(packetItems, index);
			int id = (int)KestrelApi.keson_get_int(KestrelApi.keson_get_object_item(packetItem, "id"));
			result[id] = KestrelApi.keson_detach_item_from_object(packetItem, "packet");
		}
		
		KesonUtils.kesonDeepDelete(input);
		KesonUtils.kesonDeepDelete(out);
		FrameUtils.batch_free_frame(rgbFrames);
		
		return result;
	}
	
	private static Thread encodeThread = new Thread(Utils.cogGroup, () -> {
		Initializer.bindDeviceOrNot();
		
		while(true) {
			try {				
				drainBatchFrame();
			}catch(Exception e) {
				try { Thread.sleep(100); } catch (InterruptedException e1) { }
				e.printStackTrace();
			}
		}
	});
	
	private static void drainBatchFrame(){
		List<BatchItem> handlingList = Utils.drainFromQueue(encodingQueue, Initializer.batchSize, 2, 5);
		if(handlingList.isEmpty()) {
			try { Thread.sleep(40); } catch (InterruptedException e) { }
			return ;
		}
		
		Pointer[] frames = new Pointer[handlingList.size()];
		for(int index = 0; index < handlingList.size(); index ++)
			frames[index] = handlingList.get(index).getFrame();
		
		Pointer[] packets = encodeBatchFrame(frames);
		
		for(int index = 0; index < handlingList.size(); index ++) {
			BatchItem item = handlingList.get(index);
			item.setPacket_keson(packets[index]);
			item.getLatch().countDown();
		}
	}
	
	private static void initialize() {
		if(imagesharp_annotator != null)
			return ;
		
		synchronized(NvJpgEncoder.class) {
			if(imagesharp_annotator != null)
				return ;
			
			Initializer.bindDeviceOrNot();
			String config = "{\"type\":\"encode\", \"quality\":" + Utils.instance.videoSaveImageNvencoderQuality + "}";
			log.info("loading model [imagesharp], config is " + config);
			imagesharp_annotator = KestrelApi.kestrel_annotator_open("imagesharp", config);
		}
		
		encodeThread.setDaemon(true);
		encodeThread.setPriority(Thread.NORM_PRIORITY + 2);
		encodeThread.setName("Nvidia-JPEG-Encoder");
		encodeThread.start();
	}
	
	@Data
	private static class BatchItem{
    	private Pointer frame;
    	private CountDownLatch latch;
    	private Pointer packet_keson;
    	
		public BatchItem(Pointer frame) {
			this.frame = frame;
			this.latch = new CountDownLatch(1);
		}
    }
}