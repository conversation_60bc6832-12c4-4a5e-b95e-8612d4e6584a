package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;

import java.nio.ByteBuffer;

public  interface JpegEncoderLibrary extends Library {

    public static final String JNA_LIBRARY_NAME = "jpeg_encoder";

    public static final JpegEncoderLibrary INSTANCE = (JpegEncoderLibrary) Native.load(JpegEncoderLibrary.JNA_LIBRARY_NAME, JpegEncoderLibrary.class);

//    int EncodeJPEG(ByteBuffer data, int width, int height, int pixel_format, boolean gpu, PointerByReference jpeg_data, IntByReference jpeg_data_size);

    int EncodeJPEG(Pointer data, String filePath);
}
