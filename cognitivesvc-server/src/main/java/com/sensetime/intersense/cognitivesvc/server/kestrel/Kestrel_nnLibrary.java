package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_nn_properties_t;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_tensor_meta_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
/**
 * JNA Wrapper for library <b>kestrel_nn</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_nnLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_nnLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_nnLibrary INSTANCE = (Kestrel_nnLibrary)Native.load(Kestrel_nnLibrary.JNA_LIBRARY_NAME, Kestrel_nnLibrary.class);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_extend_output(kestrel_plugin_instance, const char*)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:14</i>
	 */
	int kestrel_nn_extend_output(Pointer nn, String tensor_name);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_prepare(kestrel_plugin_instance)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:21</i>
	 */
	int kestrel_nn_prepare(Pointer nn);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_preprocess(kestrel_plugin_instance, kestrel_nn_transform_param)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:29</i>
	 */
	int kestrel_nn_preprocess(Pointer nn, Pointer param);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_get_properties(kestrel_plugin_instance, kestrel_nn_properties_t*)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:37</i>
	 */
	int kestrel_nn_get_properties(Pointer nn, kestrel_nn_properties_t properties);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_tensor_info(kestrel_plugin_instance, const char*, kestrel_tensor_meta_t*)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:46</i>
	 */
	int kestrel_nn_tensor_info(Pointer nn, String name, kestrel_tensor_meta_t tensor_info);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_reshape(kestrel_plugin_instance, const char*, const kestrel_tensor_meta_t*)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:56</i>
	 */
	int kestrel_nn_reshape(Pointer nn, String name, kestrel_tensor_meta_t in);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_get_tensor(kestrel_plugin_instance, const char*, kestrel_tensor*)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:66</i>
	 */
	int kestrel_nn_get_tensor(Pointer nn, String name, PointerByReference tensor);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_get_tensor(kestrel_plugin_instance, const char*, kestrel_tensor*)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:66</i>
	 */
	int kestrel_nn_get_tensor(Pointer nn, Pointer name, PointerByReference tensor);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_forward(kestrel_plugin_instance)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:73</i>
	 */
	int kestrel_nn_forward(Pointer nn);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_forward_async(kestrel_plugin_instance, kestrel_event*)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:81</i>
	 */
	int kestrel_nn_forward_async(Pointer nn, PointerByReference e);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_NN_PLUGIN)<br>
	 * Original signature : <code>int kestrel_nn_forward_await(kestrel_plugin_instance, kestrel_event)</code><br>
	 * <i>native declaration : include/kestrel_nn.h:89</i>
	 */
	int kestrel_nn_forward_await(Pointer nn, Pointer e);
}
