package com.sensetime.intersense.cognitivesvc.server.kestrel;


import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;
/**
 * J<PERSON> Wrapper for library <b>kestrel_base64</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */
public interface Kestrel_base64Library extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_base64"; 
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_base64Library.JNA_LIBRARY_NAME);
	public static final Kestrel_base64Library INSTANCE = (Kestrel_base64Library)Native.load(Kestrel_base64Library.JNA_LIBRARY_NAME, Kestrel_base64Library.class);
	/** <i>native declaration : include/kestrel_base64.h</i> */
	public static final int KESTREL_BASE64_H = (int)1;
	/**
	 * @{<br>
	 * Original signature : <code>char* kestrel_base64_encode(const uint8_t*, size_t, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_base64.h:10</i>
	 */
	Pointer kestrel_base64_encode(byte in[], long len, int url_safe);
	/**
	 * Original signature : <code>uint8_t* kestrel_base64_decode(const char*, size_t*, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_base64.h:13</i>
	 */
	Pointer kestrel_base64_decode(String in, LongByReference len, int url_safe);
}
