package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Callback;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.ptr.PointerByReference;

/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_buffer</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */
public interface Kestrel_bufferLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_bufferLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_bufferLibrary INSTANCE = (Kestrel_bufferLibrary)Native.load(Kestrel_bufferLibrary.JNA_LIBRARY_NAME, Kestrel_bufferLibrary.class);
	/** <i>native declaration : include/kestrel_buffer.h</i> */
	public interface Pointer extends Callback {
		void apply(Pointer ud, Pointer buf);
	};
	/**
	 * @note The buffer is auto refed, and ref count is `1`.<br>
	 * Original signature : <code>Pointer kestrel_buffer_make(void*, size_t, kestrel_mem_type_e, kestrel_buf_finalizer, void*)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:51</i>
	 */
	Pointer kestrel_buffer_make(Pointer data, long size, int type, Pointer finalizer, Pointer finalizer_ud);
	/**
	 * @note The buffer is auto refed, and ref count is 1.<br>
	 * Original signature : <code>Pointer kestrel_buffer_alloc(size_t, kestrel_mem_type_e)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:60</i>
	 */
	Pointer kestrel_buffer_alloc(long size, int type);
	/**
	 * @note The buffer is auto refed, and ref count is 1.<br>
	 * Original signature : <code>Pointer kestrel_buffer_alloc_with_policy(size_t, kestrel_mem_type_e, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:70</i>
	 */
	Pointer kestrel_buffer_alloc_with_policy(long size, int type, int policy);
	/**
	 * @return Buffer memory type<br>
	 * Original signature : <code>kestrel_mem_type_e kestrel_buffer_mem_type(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:77</i>
	 */
	int kestrel_buffer_mem_type(Pointer buf);
	/**
	 * @return Buffer memory binded device name<br>
	 * Original signature : <code>char* kestrel_buffer_mem_device_name(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:83</i>
	 */
	String kestrel_buffer_mem_device_name(Pointer buf);
	/**
	 * @return Buffer memory binded device ID<br>
	 * Original signature : <code>kestrel_device_index kestrel_buffer_mem_device_id(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:89</i>
	 */
	int kestrel_buffer_mem_device_id(Pointer buf);
	/**
	 * @return `KESTREL_OK` for successful, otherwise return error code.<br>
	 * Original signature : <code>int kestrel_buffer_set(kestrel_buffer, uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:96</i>
	 */
	int kestrel_buffer_set(Pointer buf, byte val);
	/**
	 * does not work, and returns CV_E_UNSUPPORTED.<br>
	 * Original signature : <code>int kestrel_buffer_map(kestrel_buffer, kestrel_buffer*)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:108</i>
	 */
	int kestrel_buffer_map(Pointer buf, PointerByReference mapped_buffer);
	/**
	 * @return A reference of sliced src, or `NULL` if error occurred.<br>
	 * Original signature : <code>Pointer kestrel_buffer_slice(kestrel_buffer, size_t, size_t)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:116</i>
	 */
	Pointer kestrel_buffer_slice(Pointer src, long offset, long len);
	/**
	 * @return A duplication of src, or `NULL` if error occurred.<br>
	 * Original signature : <code>Pointer kestrel_buffer_duplicate(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:122</i>
	 */
	Pointer kestrel_buffer_duplicate(Pointer src);
	/**
	 * is `KESTREL_MEM_HOST` buffer, this function will do memory transmission automatically.<br>
	 * Original signature : <code>int kestrel_buffer_copy(kestrel_buffer, kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:131</i>
	 */
	int kestrel_buffer_copy(Pointer src, Pointer dst);
	/**
	 * is `KESTREL_MEM_HOST` buffer, this function will do memory transmission automatically.<br>
	 * Original signature : <code>int kestrel_buffer_copy2D(kestrel_buffer, size_t, size_t, size_t, kestrel_buffer, size_t, size_t, size_t, size_t, size_t)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:148</i>
	 */
	int kestrel_buffer_copy2D(Pointer src, long src_x, long src_y, long src_stride, Pointer dst, long dst_x, long dst_y, long dst_stride, long linesize, long height);
	/**
	 * is `KESTREL_MEM_HOST` buffer, this function will do memory transmission automatically.<br>
	 * Original signature : <code>int kestrel_buffer_copy_async(kestrel_buffer, kestrel_buffer, kestrel_event*)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:160</i>
	 */
	int kestrel_buffer_copy_async(Pointer src, Pointer dst, PointerByReference e);
	/**
	 * is `KESTREL_MEM_HOST` buffer, this function will do memory transmission automatically.<br>
	 * Original signature : <code>int kestrel_buffer_copy2D_async(kestrel_buffer, size_t, size_t, size_t, kestrel_buffer, size_t, size_t, size_t, size_t, size_t, kestrel_event*)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:178</i>
	 */
	int kestrel_buffer_copy2D_async(Pointer src, long src_x, long src_y, long src_stride, Pointer dst, long dst_x, long dst_y, long dst_stride, long linesize, long height, PointerByReference e);
	/**
	 * @return `KESTREL_OK` for successful, otherwise return error code.<br>
	 * Original signature : <code>int kestrel_buffer_copy_await(kestrel_event)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:188</i>
	 */
	int kestrel_buffer_copy_await(Pointer e);
	/**
	 * @return Pointer to buffer raw data if successful, otherwise return NULL.<br>
	 * Original signature : <code>uint8_t* kestrel_buffer_raw_pointer(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:194</i>
	 */
	Pointer kestrel_buffer_raw_pointer(Pointer buf);
	/**
	 * @return Input buffer self.<br>
	 * Original signature : <code>Pointer kestrel_buffer_ref(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:200</i>
	 */
	Pointer kestrel_buffer_ref(Pointer buf);
	/**
	 * @return Reference count of buffer.<br>
	 * Original signature : <code>int32_t kestrel_buffer_get_ref_cnt(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:206</i>
	 */
	int kestrel_buffer_get_ref_cnt(Pointer buf);
	/**
	 * @return Size of buffer in bytes.<br>
	 * Original signature : <code>size_t kestrel_buffer_size(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:212</i>
	 */
	long kestrel_buffer_size(Pointer buf);
	/**
	 * the buffer, else just modify the size field simply.<br>
	 * Original signature : <code>int kestrel_buffer_resize(kestrel_buffer, size_t)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:221</i>
	 */
	int kestrel_buffer_resize(Pointer buf, long size);
	/**
	 * be used anymore.<br>
	 * Original signature : <code>int kestrel_buffer_shrink(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:229</i>
	 */
	int kestrel_buffer_shrink(Pointer buf);
	/**
	 * `KESTREL_MEM_HOST` buffer, this function will do memory transmission automatically.<br>
	 * Original signature : <code>int kestrel_buffer_append(kestrel_buffer, kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:238</i>
	 */
	int kestrel_buffer_append(Pointer buf, Pointer append);
	/**
	 * neither `buf` nor `*append` will be touched.<br>
	 * Original signature : <code>int kestrel_buffer_append_and_free(kestrel_buffer, kestrel_buffer*)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:248</i>
	 */
	int kestrel_buffer_append_and_free(Pointer buf, PointerByReference append);
	/**
	 * @return `KESTREL_OK` for successful, otherwise return error code.<br>
	 * Original signature : <code>int kestrel_buffer_append_data(kestrel_buffer, void*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:257</i>
	 */
	int kestrel_buffer_append_data(Pointer buf, Pointer data, long len);
	/**
	 * @return Capacity of buffer in bytes.<br>
	 * Original signature : <code>size_t kestrel_buffer_capacity(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:263</i>
	 */
	long kestrel_buffer_capacity(Pointer buf);
	/**
	 * handle) only when reference count reduces to `0`.<br>
	 * Original signature : <code>int32_t kestrel_buffer_free(kestrel_buffer*)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:273</i>
	 */
	int kestrel_buffer_free(PointerByReference buf);
	/**
	 * Original signature : <code>kestrel_plugin_instance kestrel_buffer_dev_hdl(kestrel_buffer)</code><br>
	 * <i>native declaration : include/kestrel_buffer.h:276</i>
	 */
	Pointer kestrel_buffer_dev_hdl(Pointer buf);
}
