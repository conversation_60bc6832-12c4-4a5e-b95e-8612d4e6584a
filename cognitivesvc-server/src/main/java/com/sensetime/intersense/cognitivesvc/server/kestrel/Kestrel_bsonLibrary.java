package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_bson_binary_ext_handler_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.DoubleByReference;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;
import java.nio.ByteBuffer;

/**
 * J<PERSON> Wrapper for library <b>kestrel_bson</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_bsonLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_bsonLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_bsonLibrary INSTANCE = (Kestrel_bsonLibrary)Native.load(Kestrel_bsonLibrary.JNA_LIBRARY_NAME, Kestrel_bsonLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_bson.h</i><br>
	 * enum values
	 */
	public static interface kestrel_bson_format_e {
		/** <i>native declaration : include/kestrel_bson.h:16</i> */
		public static final int KESTREL_BSON_FMT_RAW = 0;
		/** <i>native declaration : include/kestrel_bson.h:17</i> */
		public static final int KESTREL_BSON_FMT_JSON_CANONICAL = 1;
		/** <i>native declaration : include/kestrel_bson.h:18</i> */
		public static final int KESTREL_BSON_FMT_JSON_RELAXED = 2;
	};
	/**
	 * <i>native declaration : include/kestrel_bson.h</i><br>
	 * enum values
	 */
	public static interface kestrel_bson_type_e {
		/** <i>native declaration : include/kestrel_bson.h:22</i> */
		public static final int KESTREL_BSON_INVALID = 0x00;
		/** <i>native declaration : include/kestrel_bson.h:23</i> */
		public static final int KESTREL_BSON_FLOAT64 = 0x01;
		/** <i>native declaration : include/kestrel_bson.h:24</i> */
		public static final int KESTREL_BSON_STRING = 0x02;
		/** <i>native declaration : include/kestrel_bson.h:25</i> */
		public static final int KESTREL_BSON_DOCUMENT = 0x03;
		/** <i>native declaration : include/kestrel_bson.h:26</i> */
		public static final int KESTREL_BSON_ARRAY = 0x04;
		/** <i>native declaration : include/kestrel_bson.h:27</i> */
		public static final int KESTREL_BSON_BINARY = 0x05;
		/**
		 * Deprecated<br>
		 * <i>native declaration : include/kestrel_bson.h:28</i>
		 */
		public static final int KESTREL_BSON_UNDEFINED = 0x06;
		/** <i>native declaration : include/kestrel_bson.h:29</i> */
		public static final int KESTREL_BSON_OBJECTID = 0x07;
		/** <i>native declaration : include/kestrel_bson.h:30</i> */
		public static final int KESTREL_BSON_BOOLEAN = 0x08;
		/** <i>native declaration : include/kestrel_bson.h:31</i> */
		public static final int KESTREL_BSON_DATE = 0x09;
		/** <i>native declaration : include/kestrel_bson.h:32</i> */
		public static final int KESTREL_BSON_NULL = 0x0A;
		/** <i>native declaration : include/kestrel_bson.h:33</i> */
		public static final int KESTREL_BSON_REGEX = 0x0B;
		/**
		 * Deprecated<br>
		 * <i>native declaration : include/kestrel_bson.h:34</i>
		 */
		public static final int KESTREL_BSON_DBPOINTER = 0x0C;
		/**
		 * Unimplemented<br>
		 * <i>native declaration : include/kestrel_bson.h:35</i>
		 */
		public static final int KESTREL_BSON_JSCODE = 0x0D;
		/**
		 * Deprecated<br>
		 * <i>native declaration : include/kestrel_bson.h:36</i>
		 */
		public static final int KESTREL_BSON_SYMBOL = 0x0E;
		/**
		 * Deprecated<br>
		 * <i>native declaration : include/kestrel_bson.h:37</i>
		 */
		public static final int KESTREL_BSON_CODEWS = 0x0F;
		/** <i>native declaration : include/kestrel_bson.h:38</i> */
		public static final int KESTREL_BSON_INT32 = 0x10;
		/** <i>native declaration : include/kestrel_bson.h:39</i> */
		public static final int KESTREL_BSON_TIMESTAMP = 0x11;
		/** <i>native declaration : include/kestrel_bson.h:40</i> */
		public static final int KESTREL_BSON_INT64 = 0x12;
		/**
		 * Unimplemented<br>
		 * <i>native declaration : include/kestrel_bson.h:41</i>
		 */
		public static final int KESTREL_BSON_FLOAT128 = 0x13;
		/** <i>native declaration : include/kestrel_bson.h:42</i> */
		public static final int KESTREL_BSON_MAXKEY = 0x7F;
		/** <i>native declaration : include/kestrel_bson.h:43</i> */
		public static final int KESTREL_BSON_MINKEY = 0xFF;
	};
	/** <i>native declaration : include/kestrel_bson.h</i> */
	public static final int KESTREL_BSON_H = (int)1;
	/**
	 * Original signature : <code>Pointer kestrel_bson_decode_from_data(uint8_t*, size_t, kestrel_bson_format_e)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:55</i>
	 */
	Pointer kestrel_bson_decode_from_data(ByteBuffer data, long len, int fmt);
	/**
	 * Original signature : <code>Pointer kestrel_bson_decode_from_file(const char*, kestrel_bson_format_e)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:58</i>
	 */
	Pointer kestrel_bson_decode_from_file(String filename, int fmt);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_encode_to_data(kestrel_bson, uint8_t**, size_t*, kestrel_bson_format_e)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:61</i>
	 */
	int kestrel_bson_encode_to_data(Pointer item, PointerByReference data, LongByReference len, int fmt);
	/**
	 * Original signature : <code>Pointer kestrel_bson_decode(kestrel_io, kestrel_bson_format_e)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:65</i>
	 */
	Pointer kestrel_bson_decode(Pointer reader, int fmt);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_encode(kestrel_bson, kestrel_io, kestrel_bson_format_e)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:68</i>
	 */
	int kestrel_bson_encode(Pointer item, Pointer writer, int fmt);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_delete(kestrel_bson*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:71</i>
	 */
	int kestrel_bson_delete(PointerByReference item);
	/**
	 * Original signature : <code>int32_t kestrel_bson_child_number(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:74</i>
	 */
	int kestrel_bson_child_number(Pointer array);
	/**
	 * Original signature : <code>Pointer kestrel_bson_get_array_item(kestrel_bson, size_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:77</i>
	 */
	Pointer kestrel_bson_get_array_item(Pointer array, long index);
	/**
	 * Original signature : <code>Pointer kestrel_bson_get_document_item(kestrel_bson, const char*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:80</i>
	 */
	Pointer kestrel_bson_get_document_item(Pointer document, String key);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_has_document_item(kestrel_bson, const char*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:83</i>
	 */
	int kestrel_bson_has_document_item(Pointer document, String key);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_string(kestrel_bson, uint8_t**, size_t*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:86</i>
	 */
	int kestrel_bson_get_string(Pointer item, PointerByReference data, LongByReference len);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_binary_subtype(kestrel_bson, uint8_t*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:89</i>
	 */
	int kestrel_bson_get_binary_subtype(Pointer item, ByteBuffer subtype);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_binary(kestrel_bson, uint8_t**, size_t*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:92</i>
	 */
	int kestrel_bson_get_binary(Pointer item, PointerByReference data, LongByReference len);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_boolean(kestrel_bson, kestrel_bool*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:95</i>
	 */
	int kestrel_bson_get_boolean(Pointer item, IntByReference val);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_date(kestrel_bson, int64_t*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:98</i>
	 */
	int kestrel_bson_get_date(Pointer item, LongByReference val);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_int32(kestrel_bson, int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:101</i>
	 */
	int kestrel_bson_get_int32(Pointer item, IntByReference val);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_int64(kestrel_bson, int64_t*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:104</i>
	 */
	int kestrel_bson_get_int64(Pointer item, LongByReference val);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_float64(kestrel_bson, double*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:107</i>
	 */
	int kestrel_bson_get_float64(Pointer item, DoubleByReference val);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_number(kestrel_bson, double*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:110</i>
	 */
	int kestrel_bson_get_number(Pointer item, DoubleByReference val);
	/**
	 * Original signature : <code>Pointer kestrel_bson_next(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:113</i>
	 */
	Pointer kestrel_bson_next(Pointer item);
	/**
	 * Original signature : <code>Pointer kestrel_bson_prev(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:116</i>
	 */
	Pointer kestrel_bson_prev(Pointer item);
	/**
	 * Original signature : <code>Pointer kestrel_bson_child(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:119</i>
	 */
	Pointer kestrel_bson_child(Pointer item);
	/**
	 * Original signature : <code>char* kestrel_bson_key(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:122</i>
	 */
	String kestrel_bson_key(Pointer item);
	/**
	 * Original signature : <code>kestrel_bson_type_e kestrel_bson_type(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:125</i>
	 */
	int kestrel_bson_type(Pointer item);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_null()</code><br>
	 * <i>native declaration : include/kestrel_bson.h:149</i>
	 */
	Pointer kestrel_bson_create_null();
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_boolean(kestrel_bool)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:152</i>
	 */
	Pointer kestrel_bson_create_boolean(int boolean$);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_date(int64_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:155</i>
	 */
	Pointer kestrel_bson_create_date(long date);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_timestamp(uint32_t, uint32_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:158</i>
	 */
	Pointer kestrel_bson_create_timestamp(int ts, int inc);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_float64(double)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:161</i>
	 */
	Pointer kestrel_bson_create_float64(double val);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_int32(int32_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:164</i>
	 */
	Pointer kestrel_bson_create_int32(int val);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_int64(int64_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:167</i>
	 */
	Pointer kestrel_bson_create_int64(long val);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_binary(uint8_t, const uint8_t*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:170</i>
	 */
	Pointer kestrel_bson_create_binary(byte subtype, byte data[], long len);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_undefined()</code><br>
	 * <i>native declaration : include/kestrel_bson.h:173</i>
	 */
	Pointer kestrel_bson_create_undefined();
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_string(uint8_t*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:176</i>
	 */
	Pointer kestrel_bson_create_string(ByteBuffer string, long len);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_cstring(const char*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:179</i>
	 */
	Pointer kestrel_bson_create_cstring(String string);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_objectid(uint8_t[12])</code><br>
	 * <i>native declaration : include/kestrel_bson.h:182</i>
	 */
	Pointer kestrel_bson_create_objectid(ByteBuffer oid);
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_array()</code><br>
	 * <i>native declaration : include/kestrel_bson.h:185</i>
	 */
	Pointer kestrel_bson_create_array();
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_document()</code><br>
	 * <i>native declaration : include/kestrel_bson.h:188</i>
	 */
	Pointer kestrel_bson_create_document();
	/**
	 * Original signature : <code>Pointer kestrel_bson_create_by_type(kestrel_bson_type_e)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:191</i>
	 */
	Pointer kestrel_bson_create_by_type(int bson_type);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_null(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:194</i>
	 */
	Pointer kestrel_bson_set_null(Pointer n);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_boolean(kestrel_bson, kestrel_bool)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:197</i>
	 */
	Pointer kestrel_bson_set_boolean(Pointer n, int boolean$);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_date(kestrel_bson, int64_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:200</i>
	 */
	Pointer kestrel_bson_set_date(Pointer n, long date);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_timestamp(kestrel_bson, uint32_t, uint32_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:203</i>
	 */
	Pointer kestrel_bson_set_timestamp(Pointer n, int ts, int inc);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_float64(kestrel_bson, double)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:206</i>
	 */
	Pointer kestrel_bson_set_float64(Pointer n, double val);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_int32(kestrel_bson, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:209</i>
	 */
	Pointer kestrel_bson_set_int32(Pointer n, int val);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_int64(kestrel_bson, int64_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:212</i>
	 */
	Pointer kestrel_bson_set_int64(Pointer n, long val);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_binary(kestrel_bson, uint8_t, const uint8_t*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:215</i>
	 */
	Pointer kestrel_bson_set_binary(Pointer n, byte subtype, byte data[], long len);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_undefined(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:219</i>
	 */
	Pointer kestrel_bson_set_undefined(Pointer n);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_string(kestrel_bson, uint8_t*, size_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:222</i>
	 */
	Pointer kestrel_bson_set_string(Pointer n, ByteBuffer string, long len);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_objectid(kestrel_bson, uint8_t[12])</code><br>
	 * <i>native declaration : include/kestrel_bson.h:225</i>
	 */
	Pointer kestrel_bson_set_objectid(Pointer n, ByteBuffer oid);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_key(kestrel_bson, const char*, kestrel_bool)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:228</i>
	 */
	Pointer kestrel_bson_set_key(Pointer n, String key, int constant_key);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_set_value(kestrel_bson, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:231</i>
	 */
	int kestrel_bson_set_value(Pointer dst, Pointer src);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_add_array_item(kestrel_bson, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:234</i>
	 */
	int kestrel_bson_add_array_item(Pointer array, Pointer item);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_insert_array_item(kestrel_bson, size_t, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:237</i>
	 */
	int kestrel_bson_insert_array_item(Pointer array, long which, Pointer newitem);
	/**
	 * Original signature : <code>Pointer kestrel_bson_detach_array_item(kestrel_bson, size_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:241</i>
	 */
	Pointer kestrel_bson_detach_array_item(Pointer array, long which);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_delete_array_item(kestrel_bson, size_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:244</i>
	 */
	int kestrel_bson_delete_array_item(Pointer array, long which);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_replace_array_item(kestrel_bson, size_t, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:247</i>
	 */
	int kestrel_bson_replace_array_item(Pointer array, long which, Pointer replacement);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_add_document_item_constant_key(kestrel_bson, const char*, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:251</i>
	 */
	int kestrel_bson_add_document_item_constant_key(Pointer document, String key, Pointer item);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_add_document_item(kestrel_bson, const char*, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:254</i>
	 */
	int kestrel_bson_add_document_item(Pointer document, String key, Pointer item);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_add_document_item_safe(kestrel_bson, const char*, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:258</i>
	 */
	int kestrel_bson_add_document_item_safe(Pointer document, String key, Pointer item);
	/**
	 * Original signature : <code>Pointer kestrel_bson_detach_document_item(kestrel_bson, const char*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:262</i>
	 */
	Pointer kestrel_bson_detach_document_item(Pointer document, String key);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_delete_document_item(kestrel_bson, const char*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:265</i>
	 */
	int kestrel_bson_delete_document_item(Pointer document, String key);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_replace_document_item(kestrel_bson, const char*, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:268</i>
	 */
	int kestrel_bson_replace_document_item(Pointer document, String key, Pointer replacement);
	/**
	 * Original signature : <code>Pointer kestrel_bson_detach_item_via_pointer(kestrel_bson, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:272</i>
	 */
	Pointer kestrel_bson_detach_item_via_pointer(Pointer parent, Pointer item);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_delete_item_via_pointer(kestrel_bson, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:275</i>
	 */
	int kestrel_bson_delete_item_via_pointer(Pointer parent, Pointer item);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_replace_item_via_pointer(kestrel_bson, kestrel_bson, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:278</i>
	 */
	int kestrel_bson_replace_item_via_pointer(Pointer parent, Pointer item, Pointer replacement);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_add_item_via_pointer(kestrel_bson, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:282</i>
	 */
	int kestrel_bson_add_item_via_pointer(Pointer parent, Pointer item);
	/**
	 * Original signature : <code>Pointer kestrel_bson_duplicate(kestrel_bson, kestrel_bool)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:285</i>
	 */
	Pointer kestrel_bson_duplicate(Pointer item, int recurse);
	/**
	 * Original signature : <code>kestrel_bson_iter kestrel_bson_iter_create(kestrel_bson, kestrel_bool)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:288</i>
	 */
	Pointer kestrel_bson_iter_create(Pointer item, int reverse);
	/**
	 * Original signature : <code>void kestrel_bson_iter_delete(kestrel_bson_iter*)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:291</i>
	 */
	void kestrel_bson_iter_delete(PointerByReference item);
	/**
	 * Original signature : <code>Pointer kestrel_bson_iter_next(kestrel_bson_iter)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:294</i>
	 */
	Pointer kestrel_bson_iter_next(Pointer item);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_set_binary_ext_handler(kestrel_bson_binary_ext_handler_t)</code><br>
	 * <i>native declaration : include/kestrel_bson.h:312</i>
	 */
	int kestrel_bson_set_binary_ext_handler(kestrel_bson_binary_ext_handler_t.ByValue handler);
	/**
	 * Original signature : <code>kestrel_bson_binary_ext_handler_t* kestrel_bson_get_binary_ext_handler()</code><br>
	 * <i>native declaration : include/kestrel_bson.h:315</i>
	 */
	kestrel_bson_binary_ext_handler_t kestrel_bson_get_binary_ext_handler();
}
