package com.sensetime.intersense.cognitivesvc.server;

import com.alibaba.fastjson.JSON;
import com.sensetime.intersense.cognitivesvc.server.event.RebalancedEvent;
import com.sensetime.intersense.cognitivesvc.server.event.send.BaseOutput;
import com.sensetime.intersense.cognitivesvc.server.properties.VaxtorConfigProperties;
import com.sensetime.intersense.cognitivesvc.server.utils.FaissSeeker.Faiss;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils.ImageCache;
import com.sensetime.intersense.cognitivesvc.server.utils.RestUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.Utils;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.core.PlateOcrProcessor;
import com.sensetime.storage.autoconfigure.StorageProperties;
import com.sensetime.storage.service.FileAccessor;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.*;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpEntity;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration("serverConfiguation")
@EnableFeignClients(basePackages = "com.sensetime.**.feign")
@EnableScheduling
@ComponentScan
@EnableCaching
@EnableAsync(proxyTargetClass = true)
@Slf4j
@EnableJpaRepositories
@EntityScan
@AutoConfigureBefore(LiquibaseAutoConfiguration.class)
//@Import({SpringDataRestConfiguration.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
@PropertySource("classpath:server.properties")
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class CognitivesvcConfiguation implements SchedulingConfigurer {

	static {
		if(System.getProperty("java.util.concurrent.ForkJoinPool.common.parallelism") == null)
			System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", "256");
		
		Faiss.faissType.toString();
	}

	@Autowired
	ApplicationContext ctx;
	
	@Autowired
	private RestTemplateBuilder restTemplateBuilder;

	@Autowired
	StorageProperties storageProperties;

	@Autowired
	FileAccessor fileAccessor;

	@Autowired
	private ApplicationContext applicationContext;
	
	@Value("${spring.application.name}")
	private String appName;

	@Value("${intersense.purpose:poc}")
	private String isPurpose;

	@Autowired
	private VaxtorConfigProperties vaxtorConfigProperties;

	private AtomicInteger counting = new AtomicInteger();

	@Override
	public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
		taskRegistrar.setScheduler(cogTaskExecutor());
	}

	@Bean(destroyMethod = "shutdown")
	public ScheduledExecutorService cogTaskExecutor() {
		
		return Executors.newScheduledThreadPool(5, new ThreadFactory() {
			private AtomicInteger poolNumber = new AtomicInteger();
			
			@Override
			public Thread newThread(Runnable r) {
				Thread t = new Thread(Utils.cogGroup, r, "cog-task-" + poolNumber.incrementAndGet(), 0);
				t.setDaemon(true);
				t.setPriority(Thread.MAX_PRIORITY);
				return t;
			}
		});
	}

	@Bean("plateOcrProcessor")
	@Lazy
	@ConditionalOnExpression("${vaxtor.enableVaxtorPlateRecognition:true} || ${stream.xswitcher.enabled:true}")
	public PlateOcrProcessor PlateOcrProcessor(){
//		PlateOcrProcessor processor = new PlateOcrProcessor(
//				vaxtorConfigProperties.getDetectorCount(),
//				vaxtorConfigProperties.getQueueSize(),
//				vaxtorConfigProperties.getOcrDataPath(),
//				vaxtorConfigProperties.getCountryList()
//		);

		PlateOcrProcessor processor = new PlateOcrProcessor(vaxtorConfigProperties);
		return processor;
	}


	@Bean(destroyMethod = "shutdown")
	public ExecutorService cogThreadPool() {		
		ThreadFactory factory = new ThreadFactory() {
			private AtomicInteger poolNumber = new AtomicInteger();
			
			@Override
			public Thread newThread(Runnable r) {
				Thread t = new Thread(Utils.cogGroup, r, "cog-threadPool-" + poolNumber.incrementAndGet(), 0);
				t.setDaemon(true);
				t.setPriority(Thread.MAX_PRIORITY);
				return t;
			}
		};
//
//		return new ThreadPoolExecutor(
//			128, // 核心池大小，等于CPU线程数
//			256, // 最大池大小，适当增加以处理突发流量
//			60L, // 保持活动时间
//			TimeUnit.SECONDS,
//			new LinkedBlockingQueue<>(500), // 队列容量，适中设置以平衡内存使用和任务等待
//			new ThreadPoolExecutor.CallerRunsPolicy() // 处理被拒绝的任务
//		);2.13-obk
//		return new ThreadPoolExecutor(128,256, 30, TimeUnit.SECONDS, new LinkedBlockingQueue<>(500), factory, new ThreadPoolExecutor.AbortPolicy());
		int cpuCount = 0;
		// 获取可用的CPU核心数
		try (BufferedReader reader = new BufferedReader(new FileReader("/proc/cpuinfo"))) {
			String line;
			while ((line = reader.readLine()) != null) {
				if (line.startsWith("processor")) {
					cpuCount++;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		int maxPoolSize = Math.max(2 * cpuCount, 256);
		//obk 与256 取最大，增量升级 给个sql  threadSelectPool=true

		if (cpuCount <= 0) {
			cpuCount = 30;
			maxPoolSize = 512;
		}
		// 最大线程数为CPU核心数的2倍
		//new LinkedBlockingQueue<>(cpuCount * 4),CPU 密集型任务 短的队列可以帮助提供更快的响应时间

		log.info("ThreadPoolExecutor coreSize {},{}", cpuCount, maxPoolSize);

		if(Utils.instance.threadSelectPool){
			return new ThreadPoolExecutor(cpuCount, maxPoolSize, 30, TimeUnit.SECONDS, new LinkedBlockingQueue<>(cpuCount * 4), factory, new ThreadPoolExecutor.CallerRunsPolicy());
		}else{
			return new ThreadPoolExecutor(20, (Utils.instance.threadPoolNum >0) ? Utils.instance.threadPoolNum: 1024, 30, TimeUnit.SECONDS, new SynchronousQueue<Runnable>(), factory, new ThreadPoolExecutor.AbortPolicy());
		}
	}

	@Bean
	@Lazy
	@Primary
	@ConditionalOnProperty(value = "senseye.event.using.kafka", havingValue = "no")
	public BaseOutput defaultOutput(Environment environment) {
		String eventCallBack = environment.getProperty("senseye.event.output.callback");
		String eventFile     = environment.getProperty("senseye.event.output.file");
		
		if(StringUtils.isNotBlank(eventCallBack)) {
			log.info("******************************************");	
			log.info("*****message sending Using callback.******");	
			log.info("******************************************");			
			
			return (message, timeout) -> {
				try {
					RestUtils.restTemplate10000ms.postForObject(eventCallBack, new HttpEntity<Object>(JSON.toJSONString(message.getPayload()), RestUtils.headers), String.class);
				}catch(Exception e) {
					log.info(e.getLocalizedMessage());
				}
				
				return true;
			};
		}else if(StringUtils.isNotBlank(eventFile)) {
			log.info("******************************************");	
			log.info("*******message sending Using file.********");	
			log.info("******************************************");		
			
			return new BaseOutput() {
				private OutputStream outputStream;
				
				@Override
				public boolean send(Message<?> message, long timeout) {
					try {
						synchronized(CognitivesvcConfiguation.this) {
							if(outputStream == null)
								outputStream = FileUtils.openOutputStream(new File(eventFile), true);
							
							IOUtils.write(JSON.toJSONString(message.getPayload()) + "\n", outputStream, Charset.defaultCharset());
						}
					}catch(Exception e) {
						log.info(e.getLocalizedMessage());
						
						if(outputStream != null)
							try { outputStream.close(); } catch (IOException e1) { }
						
						outputStream = null;
					}
					return true;
				}
			};
		}else 
			return (message, timeout) -> { return true; };
	}
	
	@Bean
	@LoadBalanced
	public RestTemplate restTemplate() {
		return restTemplateBuilder.build();
	}

	@Value("${hsqldb.storage.path:}")
	private String hsqldbStoragePath;
	
	@Scheduled(fixedDelayString = "${intersense.cronDispatch:20000}")
	public void reBalance() throws Exception {
		Thread.sleep(ThreadLocalRandom.current().nextInt(2000, 5000));
		
		boolean isMaster = Utils.instance.tryLock("XDispatcher-" + appName, Utils.instance.lockVideoExpire);
		Utils.incharge = isMaster;
		applicationContext.publishEvent(new RebalancedEvent(isMaster, counting.getAndIncrement()));
		
		if(StringUtils.isNotBlank(hsqldbStoragePath)) {
			File logfile = new File(hsqldbStoragePath + ".log");
			if(logfile.length() > 1024 * 1024 * 10)
				FileUtils.write(logfile, "", Charset.defaultCharset(), false);
		}
		
		Map<String, ImageCache> savedImages = FrameUtils.SAVEDIMAGES;
		Iterator<Entry<String, ImageCache>> its = savedImages.entrySet().iterator();
		while(its.hasNext()) {
			Entry<String, ImageCache> entry = its.next();
			if(60 * 1000 > System.currentTimeMillis() - entry.getValue().getTime())
				its.remove();
		}
	}

	public void rebalanceChange(boolean flagChange, Map<String, Object> param){
		boolean isMaster = Utils.instance.tryLock("XDispatcher-" + appName, Utils.instance.lockVideoExpire);
		Utils.incharge = isMaster;
		RebalancedEvent rebalancedEvent =  new RebalancedEvent(isMaster, counting.getAndIncrement());
		Map<String, Object> existingStreamMap = new HashMap<String, Object>();
		existingStreamMap.put("flagChange", flagChange);
		if(param !=null)
		   existingStreamMap.put("deviceId", param.getOrDefault("deviceId", ""));
		log.info("[v2.8.7]reBalance=====================flagChange");

		rebalancedEvent.setParameter(existingStreamMap);
		applicationContext.publishEvent(rebalancedEvent);
	}

	@PostConstruct
	public void initFrameUtils(){
		FrameUtils.fileAccessor = this.fileAccessor;
		FrameUtils.storageProperties = this.storageProperties;
	}

}
