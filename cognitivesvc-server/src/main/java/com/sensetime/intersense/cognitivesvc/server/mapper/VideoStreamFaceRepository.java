package com.sensetime.intersense.cognitivesvc.server.mapper;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamFace;
import org.springframework.stereotype.Repository;

@Repository
@Tag(name = "VideoStreamFaceRepository",description = "video_stream_face data jpa")
public interface VideoStreamFaceRepository extends JpaRepositoryImplementation<VideoStreamFace, String>{

}
