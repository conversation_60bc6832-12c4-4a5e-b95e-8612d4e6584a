package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_area2d_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import java.nio.ByteBuffer;
import java.nio.IntBuffer;
/**
 * JNA Wrapper for library <b>kestrel_frame</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_frameLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_aux";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_frameLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_frameLibrary INSTANCE = (Kestrel_frameLibrary)Native.load(Kestrel_frameLibrary.JNA_LIBRARY_NAME, Kestrel_frameLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_frame.h</i><br>
	 * enum values
	 */
	public static interface kestrel_frame_type_e {
		/** <i>native declaration : include/kestrel_frame.h:19</i> */
		public static final int KESTREL_FLUSH_FRAME = -2;
		/** <i>native declaration : include/kestrel_frame.h:21</i> */
		public static final int KESTREL_UNKNOWN_FRAME = -1;
		/** <i>native declaration : include/kestrel_frame.h:23</i> */
		public static final int KESTREL_VIDEO_FRAME = 0;
		/** <i>native declaration : include/kestrel_frame.h:25</i> */
		public static final int KESTREL_AUDIO_FRAME = 1;
	};
	/**
	 * <i>native declaration : include/kestrel_frame.h</i><br>
	 * enum values
	 */
	public static interface kestrel_audio_format_e {
		/** <i>native declaration : include/kestrel_frame.h:33</i> */
		public static final int KESTREL_AUDIO_NONE = -1;
		/** <i>native declaration : include/kestrel_frame.h:35</i> */
		public static final int KESTREL_AUDIO_U8 = 0;
		/** <i>native declaration : include/kestrel_frame.h:37</i> */
		public static final int KESTREL_AUDIO_S16 = 1;
		/** <i>native declaration : include/kestrel_frame.h:39</i> */
		public static final int KESTREL_AUDIO_S32 = 2;
		/** <i>native declaration : include/kestrel_frame.h:41</i> */
		public static final int KESTREL_AUDIO_FLT = 3;
		/** <i>native declaration : include/kestrel_frame.h:43</i> */
		public static final int KESTREL_AUDIO_DBL = 4;
		/** <i>native declaration : include/kestrel_frame.h:45</i> */
		public static final int KESTREL_AUDIO_U8P = 5;
		/** <i>native declaration : include/kestrel_frame.h:47</i> */
		public static final int KESTREL_AUDIO_S16P = 6;
		/** <i>native declaration : include/kestrel_frame.h:49</i> */
		public static final int KESTREL_AUDIO_S32P = 7;
		/** <i>native declaration : include/kestrel_frame.h:51</i> */
		public static final int KESTREL_AUDIO_FLTP = 8;
		/** <i>native declaration : include/kestrel_frame.h:53</i> */
		public static final int KESTREL_AUDIO_DBLP = 9;
	};
	/**
	 * <i>native declaration : include/kestrel_frame.h</i><br>
	 * enum values
	 */
	public static interface kestrel_video_format_e {
		/** <i>native declaration : include/kestrel/kestrel_frame.h:33</i> */
		public static final int KESTREL_VIDEO_NONE = Kestrel_structLibrary.KESTREL_INVALID_FOURCC;
		/** <i>native declaration : include/kestrel/kestrel_frame.h:40</i> */
		public static final int KESTREL_VIDEO_GRAY = (('G') | (('R') << 8) | (('E') << 16) | (('Y') << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:42</i> */
		public static final int KESTREL_VIDEO_BGR = (('B') | (('G') << 8) | (('R') << 16) | ((24) << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:44</i> */
		public static final int KESTREL_VIDEO_BGRA = (('B') | (('G') << 8) | (('R') << 16) | (('A') << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:46</i> */
		public static final int KESTREL_VIDEO_RGB = (('R') | (('G') << 8) | (('B') << 16) | ((24) << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:48</i> */
		public static final int KESTREL_VIDEO_ARGB = (('A') | (('R') << 8) | (('G') << 16) | (('B') << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:50</i> */
		public static final int KESTREL_VIDEO_YU12 = (('Y') | (('U') << 8) | (('1') << 16) | (('2') << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:52</i> */
		public static final int KESTREL_VIDEO_NV12 = (('N') | (('V') << 8) | (('1') << 16) | (('2') << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:54</i> */
		public static final int KESTREL_VIDEO_NV21 = (('N') | (('V') << 8) | (('2') << 16) | (('1') << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:56</i> */
		public static final int KESTREL_VIDEO_GRAY16LE = (('Y') | (('1') << 8) | ((0) << 16) | ((16) << 24));
		/** <i>native declaration : include/kestrel/kestrel_frame.h:58</i> */
		public static final int KESTREL_VIDEO_GRAY16BE = ((16) | ((0) << 8) | (('1') << 16) | (('Y') << 24));
	};
	/** <i>native declaration : include/kestrel_frame.h</i> */
	public static final int KESTREL_AUTO_STEP = (int)(0);
	/**
	 * @return Pixel size in bytes<br>
	 * Original signature : <code>size_t kestrel_frame_pixfmt_size(kestrel_video_format_e)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:89</i>
	 */
	long kestrel_frame_pixfmt_size(int fmt);
	/**
	 * @return Plane number<br>
	 * Original signature : <code>int32_t kestrel_frame_pixfmt_plane_num(kestrel_video_format_e)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:95</i>
	 */
	int kestrel_frame_pixfmt_plane_num(int fmt);
	/**
	 * @return Fmt string, literal values no free<br>
	 * Original signature : <code>char* kestrel_frame_pixfmt_to_string(kestrel_video_format_e)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:101</i>
	 */
	Pointer kestrel_frame_pixfmt_to_string(int fmt);
	/**
	 * @return Pixel format type<br>
	 * Original signature : <code>kestrel_video_format_e kestrel_frame_string_to_pixfmt(const char*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:107</i>
	 */
	int kestrel_frame_string_to_pixfmt(String fourcc);
	/**
	 * @note The frame raw data might be padded, use stride for data retrieve.<br>
	 * Original signature : <code>kestrel_frame kestrel_frame_alloc(kestrel_mem_type_e, kestrel_video_format_e, int32_t, int32_t, const int32_t*, const int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:162</i>
	 */
	Pointer kestrel_frame_alloc(int type, int fmt, int w, int h, int strides[], int padded_height[]);
	/**
	 * @return A frame following provided parameters, should be freed using kestrel_frame_free().<br>
	 * Original signature : <code>kestrel_frame kestrel_frame_make(kestrel_mem_type_e, kestrel_video_format_e, uint8_t*, int32_t, int32_t, const int32_t*, const int32_t*, kestrel_buf_finalizer, void*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:183</i>
	 */
	Pointer kestrel_frame_make(int type, int fmt, ByteBuffer data, int w, int h, int strides[], int padded_height[], Pointer finalizer, Pointer ud);
	/**
	 * @return A frame following provided parameters, should be freed using kestrel_frame_free().<br>
	 * Original signature : <code>kestrel_frame kestrel_frame_make2(kestrel_mem_type_e, kestrel_video_format_e, uint8_t**, int32_t, int32_t, const int32_t*, const int32_t*, kestrel_buf_finalizer, void*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:204</i>
	 */
	Pointer kestrel_frame_make2(int type, int fmt, PointerByReference data, int w, int h, int strides[], int padded_height[], Pointer finalizer, Pointer ud);
	/**
	 * @return Buffer memory type<br>
	 * Original signature : <code>kestrel_mem_type_e kestrel_frame_mem_type(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:213</i>
	 */
	int kestrel_frame_mem_type(Pointer frame);
	/**
	 * @brief Get type of a frame, video or audio<br>
	 * Original signature : <code>kestrel_frame_type_e kestrel_frame_type(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:217</i>
	 */
	int kestrel_frame_type(Pointer frame);
	/**
	 * @return 0 on error<br>
	 * Original signature : <code>int32_t kestrel_frame_video_width_origin(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:222</i>
	 */
	int kestrel_frame_video_width_origin(Pointer frame);
	/**
	 * @return 0 on error<br>
	 * Original signature : <code>int32_t kestrel_frame_video_height_origin(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:227</i>
	 */
	int kestrel_frame_video_height_origin(Pointer frame);
	/**
	 * @return NULL on error<br>
	 * Original signature : <code>uint8_t* kestrel_frame_plane_origin(kestrel_frame, int)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:233</i>
	 */
	Pointer kestrel_frame_plane_origin(Pointer frame, int plane);
	/**
	 * @return NULL on error<br>
	 * Original signature : <code>uint8_t* kestrel_frame_plane(kestrel_frame, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:239</i>
	 */
	Pointer kestrel_frame_plane(Pointer frame, int plane);
	/**
	 * @return a negative on error<br>
	 * Original signature : <code>int64_t kestrel_frame_stream_id(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:244</i>
	 */
	long kestrel_frame_stream_id(Pointer frame);
	/**
	 * @return a negative on error<br>
	 * Original signature : <code>int64_t kestrel_frame_pts(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:249</i>
	 */
	long kestrel_frame_pts(Pointer frame);
	/**
	 * @brief Set frame stream id<br>
	 * Original signature : <code>int kestrel_frame_set_stream_id(kestrel_frame, int64_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:253</i>
	 */
	int kestrel_frame_set_stream_id(Pointer frame, long stream_id);
	/**
	 * @brief Set frame pts<br>
	 * Original signature : <code>int kestrel_frame_set_pts(kestrel_frame, int64_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:257</i>
	 */
	int kestrel_frame_set_pts(Pointer frame, long pts);
	/**
	 * @brief Get frame picture planes number<br>
	 * Original signature : <code>int32_t kestrel_frame_plane_num(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:261</i>
	 */
	int kestrel_frame_plane_num(Pointer frame);
	/**
	 * @brief Get frame pixel format<br>
	 * Original signature : <code>kestrel_video_format_e kestrel_frame_video_format(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:265</i>
	 */
	int kestrel_frame_video_format(Pointer frame);
	/**
	 * @return 0 on error<br>
	 * Original signature : <code>int32_t kestrel_frame_video_width(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:270</i>
	 */
	int kestrel_frame_video_width(Pointer frame);
	/**
	 * @return 0 on error<br>
	 * Original signature : <code>int32_t kestrel_frame_video_height(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:275</i>
	 */
	int kestrel_frame_video_height(Pointer frame);
	/**
	 * @return 0 on error<br>
	 * Original signature : <code>int32_t kestrel_frame_video_stride(kestrel_frame, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:280</i>
	 */
	int kestrel_frame_video_stride(Pointer frame, int plane);
	/**
	 * @return 0 on error<br>
	 * Original signature : <code>int32_t kestrel_frame_video_padded_height(kestrel_frame, int32_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:285</i>
	 */
	int kestrel_frame_video_padded_height(Pointer frame, int plane);
	/**
	 * when calling frame upload/download APIs, do it manually if necessary.<br>
	 * Original signature : <code>int kestrel_frame_attach_extra_info(kestrel_frame, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:297</i>
	 */
	int kestrel_frame_attach_extra_info(Pointer frame, Pointer extra_info);
	/**
	 * @return Attached extra info.<br>
	 * Original signature : <code>kestrel_bson kestrel_frame_get_extra_info(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:302</i>
	 */
	Pointer kestrel_frame_get_extra_info(Pointer frame);
	/**
	 * @return Attached extra info.<br>
	 * Original signature : <code>kestrel_bson kestrel_frame_detach_extra_info(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:308</i>
	 */
	Pointer kestrel_frame_detach_extra_info(Pointer frame);
	/**
	 * @return Inferred size in bytes.<br>
	 * Original signature : <code>size_t kestrel_frame_inferred_size(kestrel_video_format_e, int32_t, int32_t, const int32_t*, const int32_t*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:322</i>
	 */
	long kestrel_frame_inferred_size(int fmt, int w, int h, int strides[], int padded_height[]);
	/**
	 * @note frame size calculated by height and width stride<br>
	 * Original signature : <code>size_t kestrel_frame_size(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:330</i>
	 */
	long kestrel_frame_size(Pointer frame);
	/**
	 * @return 1 for contiguous, 0 for not<br>
	 * Original signature : <code>int32_t kestrel_frame_is_contiguous(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:336</i>
	 */
	int kestrel_frame_is_contiguous(Pointer frame);
	/**
	 * @return KESTREL_OK for succeed, otherwise failure.<br>
	 * Original signature : <code>int kestrel_frame_reset(kestrel_frame, uint8_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:343</i>
	 */
	int kestrel_frame_reset(Pointer frame, byte val);
	/**
	 * to modify its image data.<br>
	 * Original signature : <code>kestrel_frame kestrel_frame_ref(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:352</i>
	 */
	Pointer kestrel_frame_ref(Pointer frame);
	/**
	 * does not work, and returns NULL.<br>
	 * Original signature : <code>int kestrel_frame_map(kestrel_frame, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:364</i>
	 */
	int kestrel_frame_map(Pointer frame, PointerByReference mapped_frame);
	/**
	 * , top, width, height is even number) image data<br>
	 * Original signature : <code>kestrel_frame kestrel_frame_roi(kestrel_frame, kestrel_area2d_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:375</i>
	 */
	Pointer kestrel_frame_roi(Pointer frame, kestrel_area2d_t.ByValue roi);
	/**
	 * , top, width, height is even number) image data<br>
	 * Original signature : <code>int kestrel_frame_locate_roi(kestrel_frame, kestrel_frame*, kestrel_area2d_t*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:387</i>
	 */
	int kestrel_frame_locate_roi(Pointer frame, PointerByReference origin_frame, kestrel_area2d_t roi);
	/**
	 * @return Reference count of frame.<br>
	 * Original signature : <code>int32_t kestrel_frame_get_ref_cnt(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:394</i>
	 */
	int kestrel_frame_get_ref_cnt(Pointer frame);
	/**
	 * @return KESTREL_OK for succeed, otherwise failure.<br>
	 * Original signature : <code>int kestrel_frame_copy(kestrel_frame, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:403</i>
	 */
	int kestrel_frame_copy(Pointer src, PointerByReference dst);
	/**
	 * @return Duplicated frame.<br>
	 * Original signature : <code>kestrel_frame kestrel_frame_duplicate(kestrel_frame)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:409</i>
	 */
	Pointer kestrel_frame_duplicate(Pointer src);
	/**
	 * Original signature : <code>int kestrel_frame_copy_async(kestrel_frame, kestrel_frame*, kestrel_event*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:412</i>
	 */
	int kestrel_frame_copy_async(Pointer src, PointerByReference dst, PointerByReference e);
	/**
	 * data transmission as a fallback.<br>
	 * Original signature : <code>int kestrel_frame_download(kestrel_frame, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:422</i>
	 */
	int kestrel_frame_download(Pointer src, PointerByReference dst);
	/**
	 * data transmission as a fallback.<br>
	 * Original signature : <code>int kestrel_frame_download_async(kestrel_frame, kestrel_frame*, kestrel_event*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:434</i>
	 */
	int kestrel_frame_download_async(Pointer src, PointerByReference dst, PointerByReference e);
	/**
	 * data transmission as a fallback.<br>
	 * Original signature : <code>int kestrel_frame_upload(kestrel_frame, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:445</i>
	 */
	int kestrel_frame_upload(Pointer src, PointerByReference dst);
	/**
	 * data transmission as a fallback.<br>
	 * Original signature : <code>int kestrel_frame_upload_async(kestrel_frame, kestrel_frame*, kestrel_event*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:458</i>
	 */
	int kestrel_frame_upload_async(Pointer src, PointerByReference dst, PointerByReference e);
	/**
	 * @return KESTREL_OK for succeed, otherwise failure.<br>
	 * Original signature : <code>int kestrel_frame_copy_await(kestrel_event, kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:465</i>
	 */
	int kestrel_frame_copy_await(Pointer e, PointerByReference dst);
	/**
	 * @param[in,out] frame Frame which is going to be freed.<br>
	 * Original signature : <code>void kestrel_frame_free(kestrel_frame*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:470</i>
	 */
	void kestrel_frame_free(PointerByReference frame);
	/**
	 * Original signature : <code>kestrel_frame_pool kestrel_frame_pool_alloc(kestrel_mem_type_e, kestrel_video_format_e, int32_t, int32_t, int32_t, size_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:493</i>
	 */
	Pointer kestrel_frame_pool_alloc(int mem_type, int fmt, int width, int height, int align, long capacity);
	/**
	 * @return frame when fp has enough memory, or will return NULL<br>
	 * Original signature : <code>kestrel_frame kestrel_frame_pool_get(kestrel_frame_pool)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:501</i>
	 */
	Pointer kestrel_frame_pool_get(Pointer fp);
	/**
	 * @return Actual capacity after shrink<br>
	 * Original signature : <code>size_t kestrel_frame_pool_shrink(kestrel_frame_pool, size_t)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:508</i>
	 */
	long kestrel_frame_pool_shrink(Pointer fp, long exepct_capacity);
	/**
	 * @return frame pool capacity, definition in kestrel_frame_pool_alloc<br>
	 * Original signature : <code>size_t kestrel_frame_pool_capacity(kestrel_frame_pool)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:514</i>
	 */
	long kestrel_frame_pool_capacity(Pointer fp);
	/**
	 * @return frame pool allocated frame count, definition in kestrel_frame_pool_alloc<br>
	 * Original signature : <code>size_t kestrel_frame_pool_allocated(kestrel_frame_pool)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:520</i>
	 */
	long kestrel_frame_pool_allocated(Pointer fp);
	/**
	 * @return frame pool used frame count, definition in kestrel_frame_pool_alloc<br>
	 * Original signature : <code>size_t kestrel_frame_pool_usage(kestrel_frame_pool)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:526</i>
	 */
	long kestrel_frame_pool_usage(Pointer fp);
	/**
	 * @return frame pool memory type<br>
	 * Original signature : <code>kestrel_mem_type_e kestrel_frame_pool_mem_type(kestrel_frame_pool)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:532</i>
	 */
	int kestrel_frame_pool_mem_type(Pointer fp);
	/**
	 * @return frame pool video param<br>
	 * Original signature : <code>int kestrel_frame_pool_video_param(kestrel_frame_pool, kestrel_video_format_e*, int32_t*, int32_t*, int*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:538</i>
	 */
	int kestrel_frame_pool_video_param(Pointer fp, IntBuffer fmt, IntBuffer width, IntBuffer height, IntBuffer align);
	/**
	 * @param[in,out] fp frame pool handle<br>
	 * Original signature : <code>void kestrel_frame_pool_free(kestrel_frame_pool*)</code><br>
	 * <i>native declaration : include/kestrel_frame.h:544</i>
	 */
	void kestrel_frame_pool_free(PointerByReference fp);
}
