package com.sensetime.intersense.cognitivesvc.server.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.DP;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SP;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveEntity.SeekParam;
import com.sensetime.intersense.cognitivesvc.server.event.SeekSplitChangedEvent;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.utils.SeekerSpliter.SplitEntity;
import com.sun.jna.*;
import lombok.Builder;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.aop.framework.AdvisedSupport;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;
import org.springframework.scheduling.config.ScheduledTask;

import jakarta.annotation.PreDestroy;
import java.lang.reflect.Field;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 利用faiss搜索的基类
 */
@Slf4j
public abstract class FaissSeeker<S extends SP, D extends DP> implements BeanNameAware {
    
    
    protected final ThreadLocal<Memory> searchMemory = ThreadLocal.withInitial(() -> new Memory(dims() * 4));
    protected final LinkedBlockingQueue<BatchItem> hanlderQueue = new LinkedBlockingQueue<BatchItem>(256);
    protected final Thread handlerThreads[] = new Thread[2];
    protected final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    @Autowired
    protected SeekerSpliter seekerSpliter;
    @Autowired
    protected ApplicationContext applicationContext;
    @Autowired
    protected ScheduledAnnotationBeanPostProcessor scheduledProcessor;
    @Setter
    protected String beanName;
    protected int cursor = 0;
    protected int dummyCount = 0;
    protected volatile Pointer faissIndex;
    protected volatile List<D> cachedTarget = new ArrayList<D>();
    protected volatile ConcurrentLinkedQueue<Integer> deletingQueue = new ConcurrentLinkedQueue<Integer>();
    
    
    @Value("${intersense.faissSeeker.dailyReFetchDataEnabled: true}")
    private Boolean dailyReFetchDataEnabled;
    @Value("${intersense.faissSeeker.dailyReFetchDataDelEnabled:true}")
    private Boolean dailyReFetchDataDelEnabled;
    @Value("${intersense.faissSeeker.selfCheckEnabled:true}")
    private Boolean selfCheckEnabled;

    // 将getCglibProxyTargetObject方法修改如下
    protected static Object getUltimateTargetObject(Object proxy) throws Exception {
        return AopProxyUtils.ultimateTargetClass(proxy);
    }
    protected static Object getCglibProxyTargetObject(Object proxy) throws Exception {
        Field[] fields = proxy.getClass().getDeclaredFields();
        log.info("proxy fields ");
        for (Field field : fields) {
            log.info(field.getName());
        }
        Field h = proxy.getClass().getDeclaredField("CGLIB$CALLBACK_0");
        h.setAccessible(true);
        Object dynamicAdvisedInterceptor = h.get(proxy);

        Field advised = dynamicAdvisedInterceptor.getClass().getDeclaredField("advised");
        advised.setAccessible(true);
        
        return ((AdvisedSupport) advised.get(dynamicAdvisedInterceptor)).getTargetSource().getTarget();
    }
    
    public static float[] stringToFeature(String feature) {
        if (feature == null)
            return null;
        
        try {
            FloatBuffer floatBuffer = ByteBuffer.wrap(Base64.getDecoder().decode(URLDecoder.decode(feature, "utf-8"))).asFloatBuffer();
            float result[] = new float[floatBuffer.capacity()];
            floatBuffer.get(result);
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 把特征向量序列化成String
     */
    public static String featureToString(float[] feature) {
        if (feature == null)
            return null;
        
        ByteBuffer byteBuffer = ByteBuffer.allocate(feature.length * 4);
        byteBuffer.asFloatBuffer().put(FloatBuffer.wrap(feature));
        
        try {
            return URLEncoder.encode(Base64.getEncoder().encodeToString(byteBuffer.array()), "utf-8");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 对比特征 计算相似度 并进行归一化
     *
     * @return
     */
    public static float compare_feature_normalize(float[] feature_1, float[] feature_2, float[] kSrcPoint, float[] kDstPoint) {
        return normalize_feature_score(compare_feature(feature_1, feature_2), kSrcPoint, kDstPoint);
    }
    
    /**
     * 对比特征 计算相似度
     *
     * @return
     */
    public static float compare_feature(float[] feature_1, float[] feature_2) {
        if (feature_1.length != feature_2.length)
            return 0.0f;
        
        float result = 0f;
        
        for (int index = 0; index < feature_1.length; index++)
            result += feature_1[index] * feature_2[index];
        
        if (Float.isNaN(result))
            return -1;
        else
            return result;
    }
    
    /**
     * 相似度归一化
     *
     * @return
     */
    public static float normalize_feature_score(float score, float[] kSrcPoint, float[] kDstPoint) {
        if (score <= kSrcPoint[0])
            return kDstPoint[0];
        
        if (score >= kSrcPoint[kSrcPoint.length - 1])
            return kDstPoint[kDstPoint.length - 1];
        
        for (int index = 0; index < kSrcPoint.length; index++)
            if (kSrcPoint[index] > score)
                return (score - kSrcPoint[index - 1]) * (kDstPoint[index] - kDstPoint[index - 1]) / (kSrcPoint[index] - kSrcPoint[index - 1]) + kDstPoint[index - 1];
        
        if (Float.isNaN(score))
            return 0f;
        else
            throw new RuntimeException("should not be here , score : [" + score + "]");
    }
    
    protected abstract D convert(S sp);
    
    protected abstract Integer queryMaxId();
    
    protected abstract long countSplit(int totalSplitNum, int currentSplitNum);
    
    protected abstract List<S> querySplit(int start, int end, int totalSplitNum, int currentSplitNum);
    
    protected int dims() {
        return 256;
    }
    
    protected int step() {
        return 10000;
    }
    
    protected boolean gpuFaiss() {
        return false;
    }
    
    protected float normalize_feature_score(float score) {
        return score;
    }
    
    protected String name() {
        return getClass().getSimpleName();
    }
    
    protected Stream<Pair<D, Float>> find(Stream<Pair<D, Float>> baseStream, SeekParam param) {
        return baseStream;
    }
    
    public List<Pair<D, Float>> find(SeekParam param) {
        long timeTagStart = System.currentTimeMillis();
        if (faissIndex == null || param.feature == null || cachedTarget.isEmpty())
            return Lists.newArrayList();
        
        Stream<Pair<D, Float>> baseStream = null;
        param.count = Math.min(64, Objects.requireNonNullElse(param.count, 1));
        param.threshold = Objects.requireNonNullElse(param.threshold, Utils.instance.Lv0Threshold);
        
        BatchItem item = BatchItem.builder().feature(param.feature).search_num(param.count).build();
        if (gpuFaiss()) {
            synchronized (item) {
                try {
                    hanlderQueue.put(item);
                    item.wait();
                } catch (Exception e) {
                    return Lists.newArrayList();
                }
                //加锁
                List<String> filterLogs = new ArrayList<>();
                baseStream = Stream.iterate(0, i -> i + 1).limit(item.search_num)
                        .filter(index -> item.indexs[index] >= 0)
                        .map(index -> {
                            return (Pair<D, Float>) new MutablePair<D, Float>(cachedTarget.get((int) item.indexs[index]), normalize_feature_score(item.scores[index]));
                        })
                        .filter(pair -> {
                            if (pair.getRight() < param.threshold) {
                                filterLogs.add(String.format("object filtered out due to score %.2f below threshold %.2f;", pair.getRight(), param.threshold));
                                return false;
                            }

                            if (deletingQueue.contains(pair.getLeft().getId())) {
                                filterLogs.add(String.format("object filtered out due to id %d in deletingQueue, queue size: %d;", pair.getLeft().getId(), deletingQueue.size()));
                                return false;
                            }

                            return true;
                        })
                        .sorted((l, r) -> -Float.compare(l.getRight(), r.getRight()));

                // 集中打印过滤日志
                if (log.isDebugEnabled() && !filterLogs.isEmpty()) {
                    log.debug(">>> [filter] Summary of filtered objects:\n{}", String.join("\n", filterLogs));
                }
            }
        } else {
            Memory search_mem = searchMemory.get();
            
            for (int j = 0; j < dims(); j++)
                search_mem.setFloat(j * 4, item.feature[j]);
            
            Pointer result = null;
            
            try {
                lock.readLock().lock();
                if (faissIndex != null)
                    result = faiss4cogLibrary.INSTANCE.search(faissIndex, item.search_num, 1, search_mem);
                
                search_result s_result = new search_result(result);
                s_result.read();
                
                item.scores = s_result.D.getFloatArray(0, item.search_num);
                item.indexs = s_result.I.getLongArray(0, item.search_num);
                
                faiss4cogLibrary.INSTANCE.destroy_result(result);
                
                //加锁
                List<String> filterLogs = new ArrayList<>();
                baseStream = Stream.iterate(0, i -> i + 1).limit(item.search_num)
                        .filter(index -> item.indexs[index] >= 0)
                        .map(index -> {
                            return (Pair<D, Float>) new MutablePair<D, Float>(cachedTarget.get((int) item.indexs[index]), normalize_feature_score(item.scores[index]));
                        })
                        .filter(pair -> {
                            if (pair.getRight() < param.threshold) {
                                filterLogs.add(String.format("object filtered out due to score %.2f below threshold %.2f;", pair.getRight(), param.threshold));
                                return false;
                            }

                            if (deletingQueue.contains(pair.getLeft().getId())) {
                                filterLogs.add(String.format("object filtered out due to id %d in deletingQueue, queue size: %d;", pair.getLeft().getId(), deletingQueue.size()));
                                return false;
                            }

                            return true;
                        })
                        .sorted((l, r) -> -Float.compare(l.getRight(), r.getRight()));

                // 集中打印过滤日志
                if (log.isDebugEnabled() && !filterLogs.isEmpty()) {
                    log.debug(">>> [filter] Summary of filtered objects:\n{}", String.join("\n", filterLogs));
                }
            } finally {
                lock.readLock().unlock();
            }
        }
        long timeTagFaissSearchEnd = System.currentTimeMillis();
        
        // Check if baseStream is empty
        if (Utils.instance.logged) {
            List<Pair<D, Float>> checkList = baseStream.collect(Collectors.toList());
            if (checkList.isEmpty()) {
                log.info(">>> [baseStream check] baseStream is empty after filtering");
            }
            baseStream = checkList.stream();
        }
        
        // Add peek to log baseStream content before find
        baseStream = baseStream.peek(pair -> {
            if (Utils.instance.logged) {
                log.info(">>> [baseStream content] id={}, score={}", pair.getLeft().getId(), pair.getRight());
            }
        });
        
        List<Pair<D, Float>> result = find(baseStream, param).limit(param.count).collect(Collectors.toList());
        long timeTagLogicSearchEnd = System.currentTimeMillis();
        
        if (Utils.instance.logged) {
            SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
            filter.getExcludes().add("feature");
            
            log.info(">>>[feature search local] featureMD5={},search param(exclude feature)= {}. result is= {}, " +
                            "execute bean classname={}, totalSpend: {}ms, faissSearchSpend: {}ms",
                    Utils.computeMD5(param.feature), JSON.toJSONString(param, filter), JSON.toJSONString(result, filter),
                    this.getClass().getName(), (timeTagLogicSearchEnd - timeTagStart), (timeTagFaissSearchEnd - timeTagStart));
        }
        return result;
    }
    
    /**
     * 一分钟一次 从数据库拉取最近一分钟创建的 这个时间可以缩短。
     * person和passer 各自调用，会有两个faiss index
     */
    @Scheduled(fixedDelay = 1000)
    public synchronized void fetchData() {
        // 如果featureSearchSfd为true，直接返回
        if (Utils.instance.featureSearchSfd) {
            return;
        }
        
        long t_start = System.currentTimeMillis();
        dummyCount++;
        Initializer.bindDeviceOrNot();
        
        try {
            lock.writeLock().lock();
        
            if (cursor == 0) {
                if (faissIndex != null) {
                    faiss4cogLibrary.INSTANCE.destroy_index(faissIndex);
                    faissIndex = null;
                    cursor = 0;
                }
                cachedTarget = new ArrayList<D>();
    //            if (log.isDebugEnabled())
    //                log.debug(">>> [fetchData] @Scheduled(fixedDelay = 1000), cursor ==0, destroy index and cachedTarget!");
            }
            
            SplitEntity splitEntity = seekerSpliter.getSplitEntity();
            if (splitEntity.getCurrentSplitNum() < 0)
                return;
            
            Integer maxCusor = Objects.requireNonNullElse(queryMaxId(), -1);
            
            boolean hasAdd = cursor <= maxCusor;
            boolean hasDel = dummyCount % 30 == 0 && !deletingQueue.isEmpty();
    
    //        if (log.isDebugEnabled())
    //            log.debug(">>> [fetchData] @Scheduled(fixedDelay = 1000). runtime info, maxCusor={},hasAdd={},hasDel={}", maxCusor, hasAdd, hasDel);
            if (!hasAdd && !hasDel)
                return;
            
            List<D> newCachedTarget = new ArrayList<D>(cachedTarget);
            int added = 0, deled = 0;
            
            while (hasAdd && cursor <= maxCusor) {
                List<S> pfs = query(cursor, maxCusor, splitEntity);
                if (!pfs.isEmpty() && maxCusor - cursor < step() && pfs.size() != pfs.get(pfs.size() - 1).getId() - pfs.get(0).getId() + 1) {
                    log.warn(">>> [fetchData] @Scheduled(fixedDelay = 1000). List<S> pfs = query({}, {}, {}); fisrt query pfs not corretct! will try again!", cursor, maxCusor, pfs.size());
                    pfs = query(cursor, maxCusor, splitEntity);//如果失败了  尝试重试一次   jpa就是狗屎
                    if (pfs.isEmpty()) {
                        log.error(">>> [fetchData] @Scheduled(fixedDelay = 1000). List<S> pfs = query({}, {}); second query pfs not corretct! error!", cursor, maxCusor);
                    }
                }
                
                if (pfs.isEmpty()) {
                    if (cursor < maxCusor) {
                        cursor = Math.min(maxCusor, cursor + step() - 1);
                        continue;
                    } else
                        cursor = maxCusor + 1;
                } else
                    cursor = pfs.get(pfs.size() - 1).getId() + 1;
                
                List<D> newComes = pfs.stream()
                        .map(this::convert)
                        .filter(item -> item != null && ArrayUtils.isNotEmpty(item.getFeature()) && item.getFeature().length == dims())
                        .collect(Collectors.toList());
    
    //            if (log.isDebugEnabled())
    //                log.debug(">>> [fetchData] @Scheduled(fixedDelay = 1000). pfs={} , newComes={}", pfs.size(), newComes.size());
                
                if (!newComes.isEmpty()) {
                    Memory feature_mem = new Memory(newComes.size() * dims() * 4);
                    
                    for (int i = 0; i < newComes.size(); i++) {
                        for (int j = 0; j < dims(); j++)
                            feature_mem.setFloat((i * dims() + j) * 4, newComes.get(i).getFeature()[j]);
                        
                        newComes.get(i).setFeature(null);
                        newCachedTarget.add(newComes.get(i));
                    }
                    if (log.isDebugEnabled())
                        log.debug(">>> [fetchData] @Scheduled(fixedDelay = 1000). preparing to update faiss index! newComes.size={}, the last newCome.id={},cachedTarget.size={}"
                                , newComes.size(), newCachedTarget.size(), newComes.get(newComes.size() - 1).getId());
                    
                    if (faissIndex == null) {
                        faissIndex = faiss4cogLibrary.INSTANCE.create_index_l2(dims(), newComes.size(), feature_mem);
                    } else {
                        faiss4cogLibrary.INSTANCE.add_item(faissIndex, newComes.size(), feature_mem);
                    }
                    
                    added += newComes.size();
                }
            }
            //不做刪除只add
            if (hasDel) {
    //            ConcurrentLinkedQueue<Integer> deletingQueue = this.deletingQueue;
    //            this.deletingQueue = new ConcurrentLinkedQueue<Integer>();
    //
    //            List<Integer> indexs = new ArrayList<Integer>();
    //            for (int index = 0; index < newCachedTarget.size() && !deletingQueue.isEmpty(); index++) {
    //                boolean ret = deletingQueue.remove(newCachedTarget.get(index).getId());
    //                if (ret)
    //                    indexs.add(index);
    //            }
    //
    //            if (!indexs.isEmpty()) {
    //                try {
    //                    lock.writeLock().lock();
    //
    //                    Memory remove_mem = new Memory(Native.LONG_SIZE * indexs.size());
    //
    //                    for (int i = indexs.size() - 1; i >= 0; i--) {
    //                        remove_mem.setLong(i * Native.LONG_SIZE, indexs.get(i));
    //                        newCachedTarget.remove((int) indexs.get(i));
    //                    }
    //                    faiss4cogLibrary.INSTANCE.remove_item(faissIndex, indexs.size(), remove_mem);
    //                } finally {
    //                    lock.writeLock().unlock();
    //                }
    //            }
    //
    //            deled = indexs.size();
            }
            
            cachedTarget = newCachedTarget;
            
            if (Utils.instance.logged && (added > 0 || deled > 0)) {
                String text = "\n";
                text += ("-------------------------------------------------------------\n");
                text += ("************[" + name() + "] has new data. ****************\n");
                text += ("Adding " + (hasAdd ? "Y" : "N") + ", count[" + added + "]" + ", Delete " + (hasDel ? "Y" : "N") + ", count[" + deled + "], queue[" + deletingQueue.size() + "]\n");
                text += ("*************************************************************\n");
                
                log.info(text);
            }
            
        } finally {
            lock.writeLock().unlock();
        }
        
        long t_end = System.currentTimeMillis();
//        if (log.isDebugEnabled())
//            log.debug(">>> [fetchData] @Scheduled(fixedDelay = 1000) fetch latest data cronjob finished! ts: {}ms", t_end - t_start);
    }
    
    @SuppressWarnings("unchecked")
    @PreDestroy
    protected synchronized void preDestroy() {
        try {
            lock.writeLock().lock();
            if (faissIndex != null) {
                faiss4cogLibrary.INSTANCE.destroy_index(faissIndex);
                faissIndex = null;
                cursor = 0;
            }
        } finally {
            lock.writeLock().unlock();
        }
        
        cachedTarget.clear();
        
        try {
            Field field = scheduledProcessor.getClass().getDeclaredField("scheduledTasks");
            field.setAccessible(true);

            Map<Object, Set<ScheduledTask>> tasks = (Map<Object, Set<ScheduledTask>>) field.get(scheduledProcessor);
            for (Entry<Object, Set<ScheduledTask>> entry : tasks.entrySet()) {
//                if (this == getCglibProxyTargetObject(entry.getKey())) {
                if (this == getUltimateTargetObject(entry.getKey())) {
                    scheduledProcessor.postProcessBeforeDestruction(entry.getKey(), beanName);
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 全量同步
     */
    @Scheduled(cron = "${intersense.faissSeeker.dailyReFetchDataCron:0 0 2 * * ?}")
    @EventListener(classes = SeekSplitChangedEvent.class)
    public synchronized void reFetchData() {
        // 如果featureSearchSfd为false，直接返回
        if (Utils.instance.featureSearchSfd) {
            return;
        }
        log.info(">>> [dailyReFetchDataCron] execute! dailyReFetchDataEnabled={}",dailyReFetchDataEnabled);
        if (!dailyReFetchDataEnabled)
            return;
        cursor = 0;
        // 主动调用一次，避免selfcheck的定时器不匹配
        //selfCheck();
    }
    
    /**
     * 全量刪除faiss queue
     */
    @Scheduled(cron = "${intersense.faissSeeker.dailyReFetchDataCronDelQ:0 0 1 * * ?}")
    public synchronized void reFetchDataDelQueue() {
        if (Utils.instance.featureSearchSfd) {
            return;
        }
        log.info(">>> [dailyReFetchDataCronDelQ] execute! dailyReFetchDataDelEnabled={}", dailyReFetchDataDelEnabled);
        if (!dailyReFetchDataDelEnabled)
            return;
        
        
        List<D> newCachedTarget = new ArrayList<D>(cachedTarget);
        
        int deled = 0;
        
        ConcurrentLinkedQueue<Integer> deletingQueue = this.deletingQueue;
        this.deletingQueue = new ConcurrentLinkedQueue<Integer>();
        
        List<Integer> indexs = new ArrayList<Integer>();
        //循环newCachedTargetSize次  index是list索引值
        for (int index = 0; index < newCachedTarget.size() && !deletingQueue.isEmpty(); index++) {
            boolean ret = deletingQueue.remove(newCachedTarget.get(index).getId());
            if (ret)
                indexs.add(index);
        }
        
        if (!indexs.isEmpty()) {
            try {
                lock.writeLock().lock();
                
                Memory remove_mem = new Memory(Native.LONG_SIZE * indexs.size());
                
                for (int i = indexs.size() - 1; i >= 0; i--) {
                    remove_mem.setLong(i * Native.LONG_SIZE, indexs.get(i));
                    newCachedTarget.remove((int) indexs.get(i));
                }
                faiss4cogLibrary.INSTANCE.remove_item(faissIndex, indexs.size(), remove_mem);
            } finally {
                lock.writeLock().unlock();
            }
        }
        
        deled = indexs.size();
        
        if (Utils.instance.logged && (deled > 0)) {
            String text = "\n";
            text += ("-------------------------------------------------------------\n");
            text += ("************[" + name() + "] has new data. ****************\n");
            text += (" Delete " + ", count[" + deled + "], queue[" + deletingQueue.size() + "]\n");
            text += ("*************************************************************\n");
            
            log.info(text);
        }
        cachedTarget = newCachedTarget;
        
    }
    
    /**
     * 全量同步
     */
//    @Scheduled(cron = "0 0 * * * ?")
    //@Scheduled(cron = "${intersense.faissSeeker.selfCheckCron:0 0 * * * ?}")
    protected synchronized void selfCheck() {
        log.info(">>> [faiss selfCheck] execute! selfCheckEnabled={}",selfCheckEnabled);
        
        if (!selfCheckEnabled)
            return;
        
        long s = System.currentTimeMillis();
        log.info(">>> [faiss selfCheck] refetch data...");
        SplitEntity splitEntity = seekerSpliter.getSplitEntity();
        if (splitEntity.getTotalSplitNum() > 0) {
            if ((cachedTarget.size() - deletingQueue.size()) != countSplit(splitEntity.getTotalSplitNum(), splitEntity.getCurrentSplitNum())) {
                log.warn("************************************************************************************");
                log.warn("**load split feature!" + name() + " check fail, refetching.*******");
                log.warn("************************************************************************************");
                
                reFetchData();
            }
        } else {
            if ((cachedTarget.size() - deletingQueue.size()) != countSplit(1, 0)) {
                log.warn("************************************************************************************");
                log.warn("**load full feature!" + name() + " check fail, refetching.*******");
                log.warn("************************************************************************************");
                
                reFetchData();
            }
        }
        log.info(">>> [faiss selfCheck] refetch data, finished! time spend: {}ms", System.currentTimeMillis() - s);
    }
    
    public synchronized void deleteTargets(List<Integer> ids, boolean refetch) {
        try {
            lock.writeLock().lock();
            if (refetch) {
                reFetchData();
            } else {
                deletingQueue.addAll(ids);
            }
        } finally {
            lock.writeLock().unlock();
        }
        log.info("deletingQueueParam{},{}", ids, refetch);
    }
    
    @EventListener(classes = ContextRefreshedEvent.class)
    @Async("cogThreadPool")
    protected synchronized void onApplicationEvent(ContextRefreshedEvent event) {
        if (applicationContext != event.getApplicationContext())
            return;
        
        try {
            int faissGpuId = Integer.parseInt(StringUtils.isBlank(Utils.instance.faissGpuId) ? Initializer.deviceId : Utils.instance.faissGpuId);
            faiss4cogLibrary.INSTANCE.set_gpu_id(faissGpuId);
        } catch (Exception e) {
            e.printStackTrace();
            faiss4cogLibrary.INSTANCE.set_gpu_id(0);
        }
        
        if (handlerThreads[0] != null || !gpuFaiss())
            return;
        
        for (int index = 0; index < handlerThreads.length; index++) {
            handlerThreads[index] = new Thread(Utils.cogGroup, () -> {
                Initializer.bindDeviceOrNot();
                
                while (true) {
                    List<BatchItem> batch = List.of();
                    
                    try {
                        batch = searchBatch();
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        for (BatchItem item : batch)
                            synchronized (item) {
                                item.notifyAll();
                            }
                    }
                }
            });
            
            handlerThreads[index].setDaemon(true);
            handlerThreads[index].setPriority(Thread.NORM_PRIORITY + 2);
            handlerThreads[index].setName(name() + " [" + index + "]");
            handlerThreads[index].start();
        }
    }
    
    protected List<BatchItem> searchBatch() throws Exception {
        List<BatchItem> handlingList = List.of();
        if (faissIndex == null) {
            Thread.sleep(1000);
            return handlingList;
        }
        
        synchronized (hanlderQueue) {
            handlingList = Utils.drainFromQueue(hanlderQueue, 64, 5, 2);
        }
        
        if (CollectionUtils.isEmpty(handlingList)) {
            Thread.sleep(40);
            return handlingList;
        }
        
        int search_num = Math.min(64, handlingList.stream().map(item -> item.search_num).max(Integer::compareTo).get());
        
        Memory search_mem = searchMemory.get();
        if (search_mem == null) {
            searchMemory.set(new Memory(dims() * 4 * 128));
            search_mem = searchMemory.get();
        }
        
        for (int i = 0; i < handlingList.size(); i++)
            for (int j = 0; j < dims(); j++)
                search_mem.setFloat(i * dims() * 4 + j * 4, handlingList.get(i).feature[j]);
        
        Pointer result = null;
        try {
            lock.writeLock().lock();
            if (faissIndex != null)
                result = faiss4cogLibrary.INSTANCE.search(faissIndex, search_num, handlingList.size(), search_mem);
        } finally {
            lock.writeLock().unlock();
        }
        
        if (result == null)
            return handlingList;
        
        search_result s_result = new search_result(result);
        s_result.read();
        
        float[] scores = s_result.D.getFloatArray(0, search_num * handlingList.size());
        long[] indexs = s_result.I.getLongArray(0, search_num * handlingList.size());
        
        for (int ix = 0; ix < handlingList.size(); ix++) {
            BatchItem item = handlingList.get(ix);
            item.scores = new float[search_num];
            item.indexs = new long[search_num];
            
            System.arraycopy(scores, ix * search_num, item.scores, 0, search_num);
            System.arraycopy(indexs, ix * search_num, item.indexs, 0, search_num);
        }
        
        faiss4cogLibrary.INSTANCE.destroy_result(result);
        
        return handlingList;
    }
    
    protected List<S> query(int cursor, int maxCusor, SplitEntity splitEntity) {
        List<S> pfs;
        
        if (splitEntity.getTotalSplitNum() > 0)
            pfs = querySplit(cursor, Math.min(cursor + step(), maxCusor + 1), splitEntity.getTotalSplitNum(), splitEntity.getCurrentSplitNum());
        else
            pfs = querySplit(cursor, Math.min(cursor + step(), maxCusor + 1), 1, 0);
        
        return pfs;
    }
    
    public static enum FaissType {HOST, GPU}
    
    protected static interface faiss4cogLibrary extends Library {
        
        public static final String JNA_LIBRARY_NAME = "faiss4cog";
        
        public static final faiss4cogLibrary INSTANCE = (faiss4cogLibrary) Native.load(faiss4cogLibrary.JNA_LIBRARY_NAME, faiss4cogLibrary.class);
        
        /**
         * 获取当前index的gpuid
         *
         * @return
         */
        int get_gpu_id();
        
        /**
         * 设置gpu的id，只有gpu的才有效果
         *
         * @param id
         */
        void set_gpu_id(int id);
        
        /**
         * 创建一个暴力搜索索引
         *
         * @param dimension
         * @param feature_num
         * @param features
         * @return
         */
        Pointer create_index_l2(int dimension, int feature_num, Pointer features);
        
        /**
         * 创建一个聚类搜索索引
         *
         * @param dimension
         * @param feature_num
         * @param features
         * @return
         */
        Pointer create_index_ivf(int dimension, int feature_num, Pointer features);
        
        /**
         * 搜索
         *
         * @param index       索引句柄
         * @param search_num  搜索数量
         * @param feature_num 待搜索特征数量
         * @param feature     特征句柄
         * @return
         */
        Pointer search(Pointer index, int search_num, int feature_num, Pointer feature);
        
        /**
         * 向索引添加一个特征
         *
         * @param index
         * @param feature_num
         * @param feature
         */
        void add_item(Pointer index, int feature_num, Pointer feature);
        
        /**
         * 按照index移除特征
         *
         * @param index
         * @param remove_num
         * @param remove_ids
         */
        void remove_item(Pointer index, int remove_num, Pointer remove_ids);
        
        /**
         * 摧毁索引，释放内催
         *
         * @param index
         */
        void destroy_index(Pointer index);
        
        /**
         * 摧毁搜索结果
         *
         * @param result
         */
        void destroy_result(Pointer result);
    }
    
    @Builder
    protected static class BatchItem {
        public long indexs[];
        public float scores[];
        public float feature[];
        public int search_num;
    }
    
    protected static class search_index extends Structure {
        public Pointer indexIP;
        public Pointer indexL2;
        public Pointer indexIVF;
        
        public search_index() {
            super();
        }
        
        public search_index(Pointer indexL2, Pointer indexIVF) {
            super();
            this.indexL2 = indexL2;
            this.indexIVF = indexIVF;
        }
        
        public search_index(Pointer peer) {
            super(peer);
        }
        
        protected List<String> getFieldOrder() {
            return Arrays.asList("indexIP", "indexL2", "indexIVF");
        }
        
        public static class ByReference extends search_result implements Structure.ByReference {
        }

        ;

        public static class ByValue extends search_result implements Structure.ByValue {
        }

        ;
    }
    
    protected static class search_result extends Structure {
        public Pointer I;
        public Pointer D;
        
        public search_result() {
            super();
        }
        
        public search_result(Pointer I, Pointer D) {
            super();
            this.I = I;
            this.D = D;
        }
        
        public search_result(Pointer peer) {
            super(peer);
        }
        
        protected List<String> getFieldOrder() {
            return Arrays.asList("I", "D");
        }
        
        public static class ByReference extends search_result implements Structure.ByReference {
        }

        ;

        public static class ByValue extends search_result implements Structure.ByValue {
        }

        ;
    }
    
    public static class Faiss {
        public static final FaissType faissType;
        
        static {
            String faiss_type = Utils.getProperty("FAISS_TYPE");
            if ("HOST".equalsIgnoreCase(faiss_type)) {
                faissType = FaissType.HOST;
            } else if ("GPU".equalsIgnoreCase(faiss_type) && Initializer.isGpu()) {
                if (Initializer.isGpu()) {
                    faissType = FaissType.GPU;
                } else {
                    faissType = FaissType.HOST;
                    
                    log.warn("************************PedFaiss******************************");
                    log.warn("**********DeviceType is not GPU, using HOST as default.*******");
                    log.warn("**************************************************************");
                }
            } else {
                faissType = FaissType.HOST;
                
                log.warn("***********************faceFaiss******************************");
                log.warn("ENV (FAISS_TYPE) is not in (GPU, HOST), using HOST as default.");
                log.warn("**************************************************************");
            }
            
            String type = faissType == FaissType.GPU ? "gpu" : "cpu";
            HostUtils.runLinux(new String[]{"/bin/sh", "-c", "ln -sf /kestrel/other/faiss/libfaiss.so." + type + "     /usr/cognitivesvc/libfaiss.so;"});
            HostUtils.runLinux(new String[]{"/bin/sh", "-c", "ln -sf /kestrel/other/faiss/libfaiss4cog.so." + type + " /usr/cognitivesvc/libfaiss4cog.so;"});
            HostUtils.runLinux(new String[]{"/bin/sh", "-c", "ln -sf /kestrel/other/faiss/libgfortran.so.3    /usr/cognitivesvc/libgfortran.so.3;"});
            HostUtils.runLinux(new String[]{"/bin/sh", "-c", "ln -sf /kestrel/other/faiss/libopenblas.so.0    /usr/cognitivesvc/libopenblas.so.0;"});
            HostUtils.runLinux(new String[]{"/bin/sh", "-c", "ln -sf /kestrel/other/faiss/libquadmath.so.0    /usr/cognitivesvc/libquadmath.so.0;"});
        }
    }
}
