package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * JNA Wrapper for library <b>kestrel_annotator_def</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_annotator_defLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_annotator_defLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_annotator_defLibrary INSTANCE = (Kestrel_annotator_defLibrary)Native.load(Kestrel_annotator_defLibrary.JNA_LIBRARY_NAME, Kestrel_annotator_defLibrary.class);
	/** <i>native declaration : include/kestrel_annotator_def.h</i> */
	public static final int KESTREL_ANNOTATOR_PLUGIN = (int)(3);
}
