package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_close_roi_result extends Structure {
	public int roi_id;
	/**
	 * < roi区域<br>
	 * C type : kestrel_point2d_t[1024]
	 */
	public kestrel_point2d_t[] coords = new kestrel_point2d_t[1024];
	public int coord_num;
	/**
	 * 报警类别<br>
	 * C type : crowd_alarm_t
	 */
	public int alarm_type;
	/**
	 * roi中所有人头<br>
	 * C type : crowd_head_target_list_t
	 */
	public crowd_head_target_list_t all_targets;
	/**
	 * 距离过近的边列表，使用all_targets的索引<br>
	 * C type : crowd_edge_list_t
	 */
	public crowd_edge_list close_edge_list;
	public crowd_close_roi_result() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("roi_id", "coords", "coord_num", "alarm_type", "all_targets", "close_edge_list");
	}
	/**
	 * @param coords < roi区域<br>
	 * C type : kestrel_point2d_t[1024]<br>
	 * @param alarm_type 报警类别<br>
	 * C type : crowd_alarm_t<br>
	 * @param all_targets roi中所有人头<br>
	 * C type : crowd_head_target_list_t<br>
	 * @param close_edge_list 距离过近的边列表，使用all_targets的索引<br>
	 * C type : crowd_edge_list_t
	 */
	public crowd_close_roi_result(int roi_id, kestrel_point2d_t coords[], int coord_num, int alarm_type, crowd_head_target_list_t all_targets, crowd_edge_list close_edge_list) {
		super();
		this.roi_id = roi_id;
		if ((coords.length != this.coords.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.coords = coords;
		this.coord_num = coord_num;
		this.alarm_type = alarm_type;
		this.all_targets = all_targets;
		this.close_edge_list = close_edge_list;
	}
	public crowd_close_roi_result(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_close_roi_result implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_close_roi_result implements Structure.ByValue {
		
	};
}