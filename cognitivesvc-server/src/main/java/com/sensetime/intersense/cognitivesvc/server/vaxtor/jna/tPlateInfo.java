package com.sensetime.intersense.cognitivesvc.server.vaxtor.jna;

import com.sun.jna.NativeLong;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : header/vaxtor_types.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class tPlateInfo extends Structure {
    /**
     * Plate number in ASCII format<br>
     * C type : char[128]
     */
    public byte[] _plate_number_ascii = new byte[128];
    /**
     * Plate number in UNICODE format<br>
     * C type : wchar_t[128]
     */
    public char[] _plate_number_unicode = new char[128];
    /**
     * Country name<br>
     * C type : char[128]
     */
    public byte[] _plate_country = new byte[128];
    /**
     * State name<br>
     * C type : wchar_t[128]
     */
    public char[] _plate_state = new char[128];
    /**
     * Other plate identifiers like category in UAE<br>
     * C type : wchar_t[128]
     */
    public char[] _plate_category = new char[128];
    /**
     * Formatted plate, country dependent<br>
     * C type : wchar_t[128]
     */
    public char[] _plate_formatted = new char[128];
    /**
     * Additional results, country dependent<br>
     * C type : wchar_t[128]
     */
    public char[] _extended_info = new char[128];
    /**
     * DEPRECATED. Ignore the result<br>
     * C type : double[128]
     */
    public double[] _character_read_confidence = new double[128];
    /**
     * DEPRECATED. Ignore the result<br>
     * C type : vx_int32[128]
     */
    public NativeLong[] _character_bouding_rect = new NativeLong[128];
    /** Global read confidence (]0...100]%) */
    public double _plate_read_confidence;
    /** Average characters height in pixels */
    public double _average_characters_height;
    /**
     * # of characters<br>
     * C type : vx_int32
     */
    public NativeLong _num_characters;
    /**
     * Plate number of rows (1,2,3)<br>
     * C type : vx_int32
     */
    public NativeLong _num_rows;
    /**
     * ID of the inclusion ROI where the plate was found (-1:no ROI)<br>
     * C type : vx_int32
     */
    public NativeLong _roi_id;
    /**
     * Bounding box (x0,y0,x1,y1) of the license plate in the source image<br>
     * C type : vx_int32[4]
     */
    public NativeLong[] _plate_bounding_box = new NativeLong[4];
    /**
     * # of times the plate was read before publishing the final result<br>
     * C type : vx_int32
     */
    public NativeLong _multiplate_rate;
    /** OCR processing time in ms (detection plus recognition plus analytics) */
    public double _ocr_proc_time;
    /** OCR plate detection time in ms */
    public double _detection_proc_time;
    public tPlateInfo() {
        super();
    }
    protected List<String> getFieldOrder() {
        return Arrays.asList("_plate_number_ascii", "_plate_number_unicode", "_plate_country", "_plate_state", "_plate_category", "_plate_formatted", "_extended_info", "_character_read_confidence", "_character_bouding_rect", "_plate_read_confidence", "_average_characters_height", "_num_characters", "_num_rows", "_roi_id", "_plate_bounding_box", "_multiplate_rate", "_ocr_proc_time", "_detection_proc_time");
    }
    public tPlateInfo(Pointer peer) {
        super(peer);
    }
    public static class ByReference extends tPlateInfo implements Structure.ByReference {

    };
    public static class ByValue extends tPlateInfo implements Structure.ByValue {

    };
}
