package com.sensetime.intersense.cognitivesvc.server.entities;

import jakarta.persistence.*;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Entity
@Table(name = "${db.video_stream_xswitcher.table.name:video_stream_xswitcher}")
@Data
@Accessors(chain = true)
@Schema(title = "X配置", description = "X配置")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamXswitcher{
	
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Schema(description = "id")
	private Integer id;
	
	@Column(name = "device_id")
	@Schema(description = "设备id")
	private String  deviceId;
	
	@Column(name = "type")
	@Schema(description = "流类型, 0低频, 1高频")
    private Integer type;
	
	@Column(name = "processors")
	@Schema(description = "跑哪些模型")
    private String  processors;
	
	@Column(name = "dispatch_desc")
	@Schema(description = "cogx高频流分配信息")
    private String  dispatchDesc;
}
