package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_movement_roi_result_t extends Structure {
	public int roi_id;
	/** C type : kestrel_point2d_t[1024] */
	public kestrel_point2d_t[] coords = new kestrel_point2d_t[1024];
	public int coord_num;
	/** C type : crowd_head_target_list_t* */
	public Pointer head_target_list_array;
	public int array_size;
	/**
	 * <顺序跟head_target_list_array一致<br>
	 * C type : int32_t[1024]
	 */
	public int[] track_ids = new int[1024];
	/** C type : crowd_direct_e[1024] */
	public int[] crowd_direct = new int[1024];
	public crowd_movement_roi_result_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("roi_id", "coords", "coord_num", "head_target_list_array", "array_size", "track_ids", "crowd_direct");
	}
	/**
	 * @param coords C type : kestrel_point2d_t[1024]<br>
	 * @param head_target_list_array C type : crowd_head_target_list_t*<br>
	 * @param track_ids <顺序跟head_target_list_array一致<br>
	 * C type : int32_t[1024]<br>
	 * @param crowd_direct C type : crowd_direct_e[1024]
	 */
	public crowd_movement_roi_result_t(int roi_id, kestrel_point2d_t coords[], int coord_num, Pointer head_target_list_array, int array_size, int track_ids[], int crowd_direct[]) {
		super();
		this.roi_id = roi_id;
		if ((coords.length != this.coords.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.coords = coords;
		this.coord_num = coord_num;
		this.head_target_list_array = head_target_list_array;
		this.array_size = array_size;
		if ((track_ids.length != this.track_ids.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.track_ids = track_ids;
		if ((crowd_direct.length != this.crowd_direct.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.crowd_direct = crowd_direct;
	}
	public crowd_movement_roi_result_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_movement_roi_result_t implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_movement_roi_result_t implements Structure.ByValue {
		
	};
}