package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
/**
 * J<PERSON> Wrapper for library <b>flock</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface FlockLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "flock"; 
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(FlockLibrary.JNA_LIBRARY_NAME);
	public static final FlockLibrary INSTANCE = (FlockLibrary)Native.load(FlockLibrary.JNA_LIBRARY_NAME, FlockLibrary.class);


	Pointer  flock_module_env_init();

	/**
	 * @brief 创建Pipeline句柄，并初始化Pipeline资源<br>
	 * @param[in] pipeline_json Pipeline的json字符串或json文件路径<br>
	 * @return 成功返回Pipeline句柄，失败返回nullptr<br>
	 * Original signature : <code>flock_pipeline flock_pipeline_create(const char*)</code><br>
	 * <i>native declaration : include/flock.h:21</i>
	 */
	Pointer flock_pipeline_create(String pipeline_json);
	/**
	 * @brief 启动Pipeline<br>
	 * @param[in] pipeline Pipeline句柄<br>
	 * @return 成功返回KESTREL_OK，失败返回其他错误码<br>
	 * Original signature : <code>int flock_pipeline_start(flock_pipeline)</code><br>
	 * <i>native declaration : include/flock.h:28</i>
	 */
	int flock_pipeline_start(Pointer pipeline);
	/**
	 * @brief 控制Pipeline<br>
	 * @param[in] pipeline Pipeline句柄<br>
	 * @param[in] cmd 控制命令<br>
	 * @param[in] params 控制参数<br>
	 * @param[out] result 控制结果（如果有），为 nullptr 时代表不需要结果<br>
	 * @return 成功返回KESTREL_OK，失败返回其他错误码<br>
	 * Original signature : <code>int flock_pipeline_control(flock_pipeline, flock_ctrl_cmd_e, kestrel_bson, kestrel_bson*)</code><br>
	 * <i>native declaration : include/flock.h:38</i>
	 */
	int flock_pipeline_control(Pointer pipeline, int cmd, Pointer params, PointerByReference result);
	/**
	 * @brief 输入数据<br>
	 * @param[in] pipeline Pipeline句柄<br>
	 * @param[in] stream_name 处理流名称<br>
	 * @param[in] input 输入的keson数据<br>
	 * @return 成功返回KESTREL_OK，失败返回其他错误码<br>
	 * Original signature : <code>int flock_pipeline_input(flock_pipeline, const char*, kestrel_bson)</code><br>
	 * <i>native declaration : include/flock.h:48</i>
	 */
	int flock_pipeline_input(Pointer pipeline, String stream_name, Pointer input);
	/**
	 * @brief 输出结果<br>
	 * @param[in] pipeline Pipeline句柄<br>
	 * @param[in] stream_name 处理流名称<br>
	 * @param[out] output 输出的keson数据<br>
	 * @return 成功返回KESTREL_OK，失败返回其他错误码<br>
	 * Original signature : <code>int flock_pipeline_output(flock_pipeline, const char*, kestrel_bson*)</code><br>
	 * <i>native declaration : include/flock.h:68</i>
	 */
	int flock_pipeline_output(Pointer pipeline, String stream_name, PointerByReference output);
	/**
	 * @brief 尝试输入数据，非阻塞接口<br>
	 * @param[in] pipeline Pipeline句柄<br>
	 * @param[in] stream_name 处理流名称<br>
	 * @param[in] input 输入的keson数据<br>
	 * @return 成功返回KESTREL_OK，内部队列未就绪返回CV_E_IO_NOT_READY，其他返回其他错误码<br>
	 * Original signature : <code>int flock_pipeline_try_input(flock_pipeline, const char*, kestrel_bson)</code><br>
	 * <i>native declaration : include/flock.h:58</i>
	 */
	int flock_pipeline_try_input(Pointer pipeline, String stream_name, Pointer input);
	/**
	 * @brief 尝试输出结果，非阻塞接口<br>
	 * @param[in] pipeline Pipeline句柄<br>
	 * @param[in] stream_name 处理流名称<br>
	 * @param[out] output 输出的keson数据<br>
	 * @return 成功返回KESTREL_OK，内部队列未就绪返回CV_E_IO_NOT_READY，其他返回其他错误码<br>
	 * Original signature : <code>int flock_pipeline_try_output(flock_pipeline, const char*, kestrel_bson*)</code><br>
	 * <i>native declaration : include/flock.h:78</i>
	 */
	int flock_pipeline_try_output(Pointer pipeline, String stream_name, PointerByReference output);
	/**
	 * @brief 停止Pipeline<br>
	 * @param[in] pipeline Pipeline句柄<br>
	 * @return 成功返回KESTREL_OK，失败返回其他错误码<br>
	 * Original signature : <code>int flock_pipeline_stop(flock_pipeline)</code><br>
	 * <i>native declaration : include/flock.h:86</i>
	 */
	int flock_pipeline_stop(Pointer pipeline);
	/**
	 * @brief 释放Pipeline资源，并销毁Pipeline句柄<br>
	 * Original signature : <code>void flock_pipeline_destroy(flock_pipeline)</code><br>
	 * <i>native declaration : include/flock.h:91</i>
	 */
	void flock_pipeline_destroy(Pointer pipeline);
}
