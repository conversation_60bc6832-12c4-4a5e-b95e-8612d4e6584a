package com.sensetime.intersense.cognitivesvc.server.vaxtor.entities;

import java.util.concurrent.CompletableFuture;

public class PlateRequest {
    private final PlateInput input; // 输入的车牌信息
    private final CompletableFuture<PlateOutput> resultFuture; // 处理结果的异步未来对象

    public PlateRequest(PlateInput input, CompletableFuture<PlateOutput> resultFuture) {
        this.input = input;
        this.resultFuture = resultFuture;
    }

    public PlateInput getInput() {
        return input;
    }

    public CompletableFuture<PlateOutput> getResultFuture() {
        return resultFuture;
    }
} 