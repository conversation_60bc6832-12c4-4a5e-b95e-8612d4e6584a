package com.sensetime.intersense.cognitivesvc.server.mapper;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import com.sensetime.intersense.cognitivesvc.server.entities.StrangerPedestrianFeature;
import org.springframework.stereotype.Repository;

@Repository
public interface StrangerPedestrianFeatureRepository extends JpaRepositoryImplementation<StrangerPedestrianFeature, Integer> {	
	public List<StrangerPedestrianFeature> findByCreateTsBetweenOrderByCreateTsDesc(Date startTime, Date endTime);
}
