package com.sensetime.intersense.cognitivesvc.server.utils;

import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.ParseException;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.util.Timeout;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.concurrent.TimeUnit;


public class RestUtils {

    public static final RestTemplate restTemplate60000ms;
    public static final RestTemplate restTemplate10000ms;
    public static final RestTemplate restTemplate4000ms;
    public static final RestTemplate restTemplate2000ms;
    public static final RestTemplate restTemplate1000ms;
    public static final RestTemplate restTemplate500ms;
    public static final RestTemplate restTemplate200ms;

    public static final HttpHeaders headers = new HttpHeaders();

    static {
        headers.setContentType(MediaType.APPLICATION_JSON);
    }

    static {
        restTemplate60000ms = get(60000);
        restTemplate10000ms = get(10000);
        restTemplate4000ms = get(4000);
        restTemplate2000ms = get(2000);
        restTemplate1000ms = get(1000);
        restTemplate500ms = get(500);
        restTemplate200ms = get(200);
    }

    private static final RestTemplate get(int timeout) {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(timeout);
//        requestFactory.setReadTimeout(timeout);
        requestFactory.setConnectionRequestTimeout(timeout);
        return new RestTemplate(requestFactory);
    }

    public static final RestTemplate get(int timeout, int maxTotal, int maxPerRoute) {
        ConnectionConfig connectionConfig = ConnectionConfig.custom()
                .setSocketTimeout(Timeout.ofMilliseconds(timeout))
                .setConnectTimeout(Timeout.ofMilliseconds(timeout))
                .build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(maxTotal);
        connectionManager.setDefaultMaxPerRoute(maxPerRoute);
        connectionManager.setDefaultConnectionConfig(connectionConfig);
        RequestConfig requestConfig = RequestConfig
                .custom()
                .setConnectionRequestTimeout(Timeout.of(timeout, TimeUnit.MILLISECONDS)) // timeout to get connection from pool
//				.setSocketTimeout(timeout) // standard connection timeout
                .setConnectTimeout(timeout, TimeUnit.MILLISECONDS) // standard connection timeout
                .build();

        HttpClient httpClient = HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig).build();

        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

        requestFactory.setConnectTimeout(timeout);
        requestFactory.setConnectionRequestTimeout(timeout);
        return new RestTemplate(requestFactory);
    }

    public static String get(String url) throws IOException, ParseException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = httpclient.execute(httpGet);
        HttpEntity entity = response.getEntity();
        return EntityUtils.toString(entity);
    }
}
