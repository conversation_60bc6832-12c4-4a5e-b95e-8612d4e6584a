package com.sensetime.intersense.cognitivesvc.server.mapper;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoStreamInfra;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
@Repository
@Tag(name = "VideoStreamInfraRepository", description = "video_stream_infra data jpa")
public interface VideoStreamInfraRepository extends JpaRepositoryImplementation<VideoStreamInfra, String>{
	
	public List<VideoStreamInfra> findByRtmpDestination(String rtmpDestination);
	
	public List<VideoStreamInfra> findByDeviceIdIn(Collection<String> deviceIds);
	
	public List<VideoStreamInfra> findByStsOrderByDeviceIdAsc(Integer sts);
	
	@Transactional
	@Query("update VideoStreamInfra i set i.seed = ?1, i.updateTs = ?2 where i.deviceId = ?3 and i.seed = ?4")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int updateSeed(String seed, Date updateTs, String whereDeviceId, String whereSeed);
	
	@Transactional
	@Query("update VideoStreamInfra i set i.seed = ?1, i.updateTs = ?2 where i.deviceId = ?3 and i.seed is NULL")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int updateNullSeed(String seed, Date updateTs, String whereDeviceId);
	
	@Transactional
	@Query("update VideoStreamInfra i set i.rtmpOn = ?1 where i.deviceId = ?2")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int updateRtmpOn(String rtmpOn, String whereDeviceId);


	@Transactional
	@Query("update VideoStreamInfra i set i.sts = ?1 where i.deviceId = ?2")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int updateSts(int rtmpOn, String whereDeviceId);

	@Transactional
	@Query("update VideoStreamInfra i set i.rtspMappedSource = ?1 where i.rtspSource = ?2")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int updateMappedSource(String rtspMappedSource, String rtspSource);
	
	@Transactional
	@Query("update VideoStreamInfra i set i.sts = ?1, i.seed = ?2, i.updateTs = ?3 where i.deviceId = ?4")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int updateStsAndSeedByDeviceId(Integer sts, String seed, Date updateTs, String whereDeviceId);
	
	@Query(value = "select i.deviceId from VideoStreamInfra i where i.sts = ?1")
	public Set<String> queryDeviceidsBySts(Integer sts);
}
