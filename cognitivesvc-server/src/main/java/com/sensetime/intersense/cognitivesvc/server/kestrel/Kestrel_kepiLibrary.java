package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;

/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_kepi</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_kepiLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_kepiLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_kepiLibrary INSTANCE = (Kestrel_kepiLibrary)Native.load(Kestrel_kepiLibrary.JNA_LIBRARY_NAME, Kestrel_kepiLibrary.class);
	/** <i>native declaration : include/kestrel_kepi.h</i> */
	public static final int KEPI_E_PARSE = (int)(-32700);
	/** <i>native declaration : include/kestrel_kepi.h</i> */
	public static final int KEPI_E_INVALID_REQ = (int)(-32600);
	/** <i>native declaration : include/kestrel_kepi.h</i> */
	public static final int KEPI_E_METHOD_NOT_FOUND = (int)(-32601);
	/** <i>native declaration : include/kestrel_kepi.h</i> */
	public static final int KEPI_E_INVALID_ARG = (int)(-32602);
	/** <i>native declaration : include/kestrel_kepi.h</i> */
	public static final int KEPI_E_INTERNAL = (int)(-32603);
	/**
	 * Original signature : <code>void kepi_error(kestrel_bson, kestrel_bson, int32_t, const char*)</code><br>
	 * <i>native declaration : include/kestrel_kepi.h:258</i>
	 */
	void kepi_error(Pointer req, Pointer resp, int code, String msg);
	/**
	 * Original signature : <code>void kepi_error_ex(kestrel_bson, kestrel_bson, int32_t, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_kepi.h:261</i>
	 */
	void kepi_error_ex(Pointer req, Pointer resp, int code, Pointer msg_node);
	/**
	 * Original signature : <code>void kepi_resp(kestrel_bson, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_kepi.h:264</i>
	 */
	void kepi_resp(Pointer req, Pointer resp);
}
