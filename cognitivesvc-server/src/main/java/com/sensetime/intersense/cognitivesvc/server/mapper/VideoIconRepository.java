package com.sensetime.intersense.cognitivesvc.server.mapper;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.sensetime.intersense.cognitivesvc.server.entities.VideoIconEntity.Key;
import com.sensetime.intersense.cognitivesvc.server.entities.VideoIconEntity.VideoIcon;
@Repository
public interface VideoIconRepository extends JpaRepositoryImplementation<VideoIcon, Key>{
	
	public Page<VideoIcon> findByPersonTag(String personTag, Pageable pageable);
	
	public Page<VideoIcon> findByDeviceId(String deviceId, Pageable pageable);
	
	public Page<VideoIcon> findByPersonTagAndDeviceId(String personTag, String deviceId, Pageable pageable);
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int deleteByPersonTag(String personTag);
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int deleteByDeviceId(String deviceId);
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public int deleteByPersonTagAndDeviceId(String personTag, String deviceId);
}
