package com.sensetime.intersense.cognitivesvc.server.mapper;

import java.util.Date;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveLock;

@Repository
public interface CognitiveLockRepository extends JpaRepositoryImplementation<CognitiveLock, String>{
	
	@Transactional
	@Query("update CognitiveLock l set l.seed = ?1 ,l.updateTs = ?2 where l.lockKey = ?3 and l.seed = ?4 and l.updateTs = ?5")
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	int update(String seed, Date updateTs, String whereLockKey, String whereSeed, Date whereUpdateTs);
}
