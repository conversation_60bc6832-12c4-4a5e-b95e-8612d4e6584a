package com.sensetime.intersense.cognitivesvc.server.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.*;
import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import jakarta.ws.rs.ext.ParamConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveLock;
import com.sensetime.intersense.cognitivesvc.server.entities.CognitiveParam;
import com.sensetime.intersense.cognitivesvc.server.event.AttributeTypeChangedEvent;
import com.sensetime.intersense.cognitivesvc.server.event.HunterTypeChangedEvent;
import com.sensetime.intersense.cognitivesvc.server.event.ParamRefreshedEvent;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.Initializer.DeviceType;
import com.sensetime.intersense.cognitivesvc.server.mapper.CognitiveLockRepository;
import com.sensetime.intersense.cognitivesvc.server.mapper.CognitiveParamRepository;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils.FramePoolUtils;

@Component
@Slf4j
public class Utils {
    
    public static volatile boolean incharge;
    
    public static final ThreadGroup cogGroup = new ThreadGroup("Cognitive");
    
    public static final ThreadLocal<SimpleDateFormat> dateFormat = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    public static final ThreadLocal<SimpleDateFormat> dateFormat_MS = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"));

    public static Utils instance;
    
    public static String VERSION = readVersion();

    public Utils() {
        instance = this;
    }
    
    @Value("${random.uuid}")
    private String seed;
    
    private String realSeed;
    
    public String getSeed() {
        if (realSeed == null) {
            try {
                InetAddress address = InetAddress.getLocalHost();
                realSeed = address.getHostAddress() + ":" + cogPort + "_" + seed;
            } catch (UnknownHostException e) {
                e.printStackTrace();
                realSeed = seed;
            }
        }
        return realSeed;
    }
    
    @Value("${image.save.path:/images/cognitivesvc}")
    public volatile String savePath;

    @Value("${subImageFolderCount:512}")
    public volatile Integer subImageFolderCount;

    @Value("${image.quality:0.3}")
    public volatile float imageQuality;
    
    @Value("${image.integrate.quality:0.45}")
    public volatile float integrateQuality;
    
    @Value("${image.scene.save:false}")
    public volatile boolean imageSceneSave;
    
    @Value("${person.compare.stranger.inspect.threshold:0.55}")
    public volatile float InspectThreshold;
    
    @Value("${person.compare.threshold.lv0:0.60}")
    public volatile float Lv0Threshold;
    
    @Value("${person.compare.threshold.lv1:0.65}")
    public volatile float Lv1Threshold;
    
    @Value("${person.compare.threshold.lv2:0.70}")
    public volatile float Lv2Threshold;
    
    @Value("${person.compare.threshold.lv3:0.80}")
    public volatile float Lv3Threshold;
    
    @Value("${seeker.target.split:0}")
    public volatile Integer seekSplit;
    
    @Value("${stranger.type:just}")
    public volatile String runningType;

    @Value("${stranger.removeSearchEngine:true}")
    public volatile boolean removeSearchEngine;

    @Value("${stranger.rolling.window:1}")
    public volatile int rollingWindow;
    
    @Value("${stranger.rolling.showcount:2}")//单位次
    public volatile int periodShowupNum;
    
    @Value("${stranger.rolling.devicecount:1}")//单位次
    public volatile int deviceShowupNum;
    
    @Value("${stranger.rolling.period:120}")//单位分钟
    public volatile int period;
    
    @Value("${video.gpu.mem.seen.rate.limit:0.8}")
    public volatile float seenGpuMemLimit; //继续添加视频流的显存上线。
    
    @Value("${video.gpu.mem.nonseen.rate.limit:0.8}")
    public volatile float nonSeenGpuMemLimit; //继续添加视频流的显存上线。

    @Value("${video.gpu.mem.offline.rate.limit:0.8}")
    public volatile float offlineVideoGpuMemLimit; //离线显存上线。

    @Value("${video.tracker.face.size.limit:30}")
    public volatile int faceSizeLimit; //视频追踪中的脸大小。。。。
    
    @Value("${video.tracker.nonseen.queue.size:128}")
    public volatile int nonSeenQueueSize; //视频追踪中的脸大小。。。。
    
    @Value("${video.tracker.seen.count:3}")
    public volatile int seenMaxCount; //=0 不跳帧   |   <0 每一帧跳N帧   |   >0 每N帧，跳一帧
    
    @Value("${video.tracker.nonseen.count:64}")
    public volatile int nonSeenMaxCount; //=0 不跳帧   |   <0 每一帧跳N帧   |   >0 每N帧，跳一帧
    
    @Value("${video.xworker.highrate.count:32}")
    public volatile int xWorkerHighRateMaxCount; //=0 不跳帧   |   <0 每一帧跳N帧   |   >0 每N帧，跳一帧
    
    @Value("${video.tracker.nonseen.queue.type:0}")
    public volatile int nonSeenQueueType; //视频追踪中的脸大小。。。。
    
    @Value("${use.type.attribute:0}")
    public volatile int useTypeAttribute;
    
    @Value("${use.type.hunter:1}")
    public volatile int useTypeHunter; //0LC检测不成功返回空，1LSC检测不成功返回空，,2LSC检测不成功返回框，-1不检测直接返回框
    
    @Value("${retrieve.type:0}")
    public volatile int retrieveType; //0（默认）入库提特征必须成功，1入库提特征失败保存假特征，多张脸用最大的特征
    
    @Value("${video.tracker.seen.watch.frame.tiktok.level:0}")
    public volatile int watchFrameTiktokLevel; //=0 不跳帧   |   <0 每一帧跳N帧   |   >0 每N帧，跳一帧
    
    @Value("${video.tracker.update.seen.attribute.every.second:true}")
    public volatile boolean seenAttributeEverySecond; //每秒都上报attr的数值，渲染视频流
    
    @Value("${video.tracker.update.nonseen.attribute.every.second:true}")
    public volatile boolean nonSeenAttributeEverySecond; //每秒都上报attr的数值，非渲染视频流
    
    @Value("${video.tracker.nonseen.reused.frame.count:32}")
    public volatile int nonSeenReusedFrameCount; //=0 不跳帧   |   <0 每一帧跳N帧   |   >0 每N帧，跳一帧
    
    @Value("${video.stream.mempool.strategy:smart}")
    public volatile String mempoolStrategy; //smart, pedantic, pedantic_swallow
    
    @Value("${video.multiplex.decode.format:nv12}")
    public volatile String multiplexDecodeFormat; //复用解码器视频格式
    
    @Value("${video.multiplex.frame.decoder.count:256}")
    public volatile int multiplexDecoderFrameCount; //复用解码器的帧池
    
    @Value("${video.multiplex.decoder.duplicate:16}")
    public volatile int multiplexDecoderDuplicate;//每一路复用解码器可以接的路数
    
    @Value("${video.multiplex.strategy:smart}")
    public volatile String multiplexDecoderStrategy; //smart, pedantic, pedantic_swallow
    
    @Value("${video.tracker.nonseen.max.tracklet.count:16}")
    public volatile int nonSeenMaxTrackletCount; //=0 不跳帧   |   <0 每一帧跳N帧   |   >0 每N帧，跳一帧
    
    @Value("${video.tracker.update.nonseen.attribute.begin.end:false}")
    public volatile boolean nonSeenAttributeBeginEnd; //每秒都上报attr的数值，非渲染视频流
    
    @Value("${video.tracker.update.attribute.aggressive:}")
    public volatile Float attributeAggressive; //激进的显示情绪
    
    @Value("${video.tracker.decoder.cpu.count:0}")
    public volatile int decoderCpuCount; //使用多少个cpu的decoder
    
    @Value("${video.lock.expire:120}")
    public volatile int lockVideoExpire;
    
    @Value("${video.keep.alive:60}")
    public volatile int keepAliveExpire;
    
    @Value("${video.tracker.unknown.reg.count:10}")
    public volatile int unknownRegTimeCount;
    
    @Value("${video.buffer.size.limiter:1920*1080}")
    public volatile String videoBufferSizeLimit;
    
    @Value("${video.tracker.message.log:false}")
    public volatile boolean logged;
    
    @Value("${video.rebalance.on:true}")
    public volatile boolean rebalanceOn;
    
    @Value("${video.remapped.min.num:}")
    public volatile Integer remappedMinNum;
    
    @Value("${video.pedestrian.pipeline.harpy.enable:false}")
    public boolean pedestrianHarpyEnabled;

    @Value("${video.pedestrian.pipeline.faceLargeRoiExpend.enable:false}")
    public boolean faceLargeRoiExpand;

    @Value("${senseyex.video.create.affinity:false}")
    public volatile boolean senseyexVideoCreateAffinity;
    
    @Value("${senseyex.video.switcher.on.worker:true}")
    public volatile boolean senseyexVideoSwitcherOnWorker;
    
    @Value("${video.imwrite.flags:1,95}")
    public volatile int[] imwriteFlags;
    
    @Value("${video.save.image.nvencoder.quality:75}")
    public volatile int videoSaveImageNvencoderQuality;
    
    @Value("${video.save.image.scale:}")
    public volatile Float videoSaveImageScale;
    
    @Value("${image.saving.max.thread:50}")
    public volatile int imageSavingMaxThread;
    
    @Value("${video.save.image.type:0}")
    public volatile int videoSaveImageType;

    @Value("${video.save.osg.type:999}")
    public volatile int videoSaveOsgType;

    @Value("${video.frame.encode.type:2}")
    public volatile int frameEncoderType;

    @Value("${video.save.image.way:0}")
    public volatile int videoSaveImageWay;
    
    @Value("${nvidia.coder.count:1}")
    public volatile int nvCoderCount = 1;
    
    @Value("${frame.buffer.size:128}")
    public volatile int frameBufferSize = 128;
    
    @Value("${frame.skip:3}")
    public volatile int frameSkip = 3;
    
    @Value("${faiss.gpu.id:}")
    public String faissGpuId; //使用第几号gpu
    
    @Value("${server.port}")
    public int cogPort;
    
    @Value("${senseyex.shieldFace.switch:0}")
    public volatile int shieldFaceSwitch;
    
    @Value("${senseyex.imageCapture.switch:0}")
    public volatile int imageCaptureSwitch;
    
    @Value("${instance.local.switch:0}")
    public volatile int instanceLocalSwitch;
    
    @Value("${senseyex.face.recognition.mode:0}")
    public volatile int faceRecognitionMode;
    
    @Value("${senseyex.stranger.recognition:0}")
    public volatile int strangerRecognition;

    @Value("${video.lock.devices:150}")
    public volatile int lockVideoDevicesNum;


    @Value("${video.tracker.nonseen.frame.size:6}")
    public volatile int nvDecoderBuffSize;

    @Value("${video.tracker.nonseen.frame.pool.size:60}")
    public volatile int nvDecoderFramePoolSize;

    @Value("${video.kafka.message.mode:true}")
    public volatile boolean kafkaHeaderKeyMode;

    public volatile int maxTrackingTime;
    public volatile int selectFrameTimeInterVal;
    
    public volatile int dropFace;
    
    public volatile int duplicateTargetsTime;
    
    public volatile String displayTarget = "";
    public volatile Float smallDetectThresh;
    public volatile Float largeDetectThresh;
    public volatile Float selectFrameThresh;
    public volatile Float selectPedFrameThresh;
    public volatile Float qualityStepThresh;

    @Value("${video.tracker.message.useSelfCalCapturedTime:false}")
    public volatile boolean useSelfCalCapturedTime;
    
    @Value("${senseyex.stranger.dropFaceFlag:1}")
    public volatile int dropFaceFlag;

    @Value("${senseyex.stranger.dropPedFlag:1}")
    public volatile int dropPedFlag;

    @Value("${senseyex.stranger.dropFaceIndentify:1}")
    public volatile int dropFaceIndentify;

    @Value("${senseyex.face.cogDebug:1}")
    public volatile int cogDebug;

    public volatile String dropFaceQuality;

    @Value("${video.status.errorTime:1}")
    public volatile int videoStatusErrorTime;

    @Value("${video.status.videoStatusErrorInternalTime:20}")
    public volatile int videoStatusErrorInternalTime;

    @Value("${video.status.xStreamReopenErrorTime:60}")
    public volatile int xStreamReopenErrorTime;

    @Value("${video.status.faceStreamReopenErrorTime:60}")
    public volatile int faceStreamReopenErrorTime;

    @Value("${video.status.streamReopenSleepTime:5}")
    public volatile int streamReopenSleepTime;

    @Value("${video.status.streamOpenTimeout:5000}")
    public volatile int streamOpenTimeout;

    @Value("${video.status.videoFrameNullCount:40}")
    public volatile int videoFrameNullCount;

    @Value("${video.status.videoStatusErrorKestrelAgainTime:50}")
    public volatile int videoStatusErrorKestrelAgainTime;

    @Value("${senseyex.video.stability.flag:false}")
    public volatile boolean stabilityFlag;

    @Value("${frame.imageExt:png}")
    public volatile String imagesSaveExt;

    @Value("${senseyex.video.stability.threadPoolNum:0}")
    public volatile int threadPoolNum;

    @Value("${video.face.scgFace.enable:true}")
    public volatile boolean scgFacePipeline;

    @Value("${video.ped.saveCluster.enable:false}")
    public volatile boolean pedSaveCluster;


    public volatile boolean faceIsQueue ;

    @Value("${senseyex.video.image.saveImagePathDisk:false}")
    public volatile boolean saveImagePathDisk;

    @Value("${video.frame.useH265:1}")
    public volatile int useH265;

    @Value("${longCostTs:1000}")
    public volatile int longCostTs;

    @Value("${longCostSaveImgTs:300}")
    public volatile int longCostSaveImgTs;

    @Value("${frame.fetchFrame.size:20}")
    public volatile int fetchFrameSize;

    @Value("${video.clip.extend:0.2}")
    public volatile float clipExtend;

    @Value("${longAfterPipeCost:200}")
    public volatile int longAfterPipeCost;

    @Value("${senseyex.video.image.printLog:false}")
    public volatile boolean printLog;

    @Value("${video.max.file.size:1073741824}")
    public volatile long offlineVideoLimit;

    @Value("${senseyex.video.checkQueueTime:20}")
    public volatile long checkQueueTime;

    @Value("${senseyex.video.threadSelectPool:true}")
    public volatile boolean threadSelectPool;

    @Value("${video.status.printHandleTime:400}")
    public volatile int printHandleTime;

    @Value("${video.gpu.mem.switcher.rate.limit:0.8}")
    public volatile float xGpuMemRateLimit; //cogx继续添加视频流的显存上线。


    @Value("${senseyex.video.stream.dispatch:true}")
    public volatile boolean cameraDispatch;

    public volatile boolean debug_0 = false;
    
    public volatile boolean debug_1 = false;
    
    public volatile boolean debug_2 = false;
    
    public volatile boolean debug_3 = false;
    
    public volatile boolean debug_4 = false;
    
    public volatile boolean debug_5 = false;

    public volatile boolean offline_flag = false;


    //true=不走faiss，走sfd
    public volatile boolean featureSearchSfd = true;
    
    @Autowired
    private CognitiveLockRepository lockMapper;
    
    /**
     * 数据库锁
     */
    public synchronized boolean tryLock(String key, long timeout) {
        CognitiveLock current = lockMapper.findById(key).orElse(null);
        if (current == null) {
            try {
                CognitiveLock saved = lockMapper.save(CognitiveLock.builder().lockKey(key).seed(seed).createTs(new Date()).updateTs(new Date()).build());
                return saved != null;
            } catch (Exception e) {
                return false;
            }
        } else {
            if (seed.equals(current.getSeed()) || System.currentTimeMillis() - current.getUpdateTs().getTime() > (timeout * 1000)) {
                int ret = lockMapper.update(seed, new Date(), current.getLockKey(), current.getSeed(), current.getUpdateTs());
                return ret > 0;
            }
            return false;
        }
    }
    
    /**
     * 数据库锁
     */
    public synchronized void releaseLock(String key) {
        CognitiveLock current = lockMapper.findById(key).orElse(null);
        if (current != null && current.getSeed().equals(seed))
            lockMapper.deleteById(current.getLockKey());
    }
    
    public static String getProperty(String key) {
        return getProperty(key, null);
    }
    
    public static String getProperty(String key, String def) {
        String property = System.getenv(key);
        if (StringUtils.isNotBlank(property))
            return property;
        
        property = System.getenv(key.toLowerCase());
        if (StringUtils.isNotBlank(property))
            return property;
        
        property = System.getenv(key.toUpperCase());
        if (StringUtils.isNotBlank(property))
            return property;
        
        property = System.getProperty(key);
        if (StringUtils.isNotBlank(property))
            return property;
        
        property = System.getProperty(key.toLowerCase());
        if (StringUtils.isNotBlank(property))
            return property;
        
        property = System.getProperty(key.toUpperCase());
        if (StringUtils.isNotBlank(property))
            return property;
        
        return def;
    }
    
    public static long keyToContextId(Object key) {
        return key == null ? 0 : Math.abs(key.hashCode());
    }
    
    /**
     * 在规定的时间内 从队列里拉取固定数量的东西 超时即返回
     *
     * @param <T>
     * @param queue
     * @param size
     * @param timeout
     * @return
     */
    public static <T> List<T> drainFromQueue(Queue<T> queue, int size, int pollcount, int timeout) {
        return drainFromQueue(queue, size, pollcount, timeout, null);
    }
    
    /**
     * 在规定的时间内 从队列里拉取固定数量的东西 超时即返回
     *
     * @param <T>
     * @param queue
     * @param size
     * @param timeout
     * @return
     */
    public static <T> List<T> drainFromQueue(Queue<T> queue, int size, int pollcount, int timeout, Predicate<T> predicate) {
        if (queue == null)
            return Lists.newArrayList();
        
        List<T> handlingList = new ArrayList<T>(size);
        for (int going = pollcount; handlingList.size() < size && going > 0; ) {
            T target = queue.poll();
            if (target != null) {
                if (predicate == null || predicate.test(target))
                    handlingList.add(target);
            } else {
                try {
                    Thread.sleep(timeout);
                } catch (InterruptedException e) {
                }
                going--;
            }
        }
        
        return handlingList;
    }
    
    /**
     * 在规定的时间内 从队列里拉取固定数量的东西 超时即返回
     *
     * @param <T>
     * @param mapQueue
     * @param pollcount
     * @param timeout
     * @return
     */
    public static <T> List<T> drainFromMapQueue(Map<String, Queue<T>> mapQueue, int pollcount, int timeout) {
        return drainFromMapQueue(mapQueue, pollcount, timeout, null);
    }
    
    /**
     * 在规定的时间内 从队列里拉取固定数量的东西 超时即返回
     *
     * @param <T>
     * @param mapQueue
     * @param pollcount
     * @param timeout
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> drainFromMapQueue(Map<String, Queue<T>> mapQueue, int pollcount, int timeout, Predicate<T> predicate) {
        if (MapUtils.isEmpty(mapQueue))
            return Lists.newArrayList();
        
        Queue<T>[] queues = mapQueue.values().stream().toArray(Queue[]::new);
        if (queues.length <= 0)
            return Lists.newArrayList();
        
        List<T> handlingList = new ArrayList<T>(queues.length);
        for (int limit = 0; limit < pollcount; limit++) {
            for (int index = 0; index < queues.length; index++) {
                if (queues[index] == null)
                    continue;
                
                T t = queues[index].poll();
                if (t != null) {
                    handlingList.add(t);
                    queues[index] = null;
                }
            }
            
            if (handlingList.size() < queues.length)
                try {
                    Thread.sleep(timeout);
                } catch (InterruptedException e) {
                }
            else
                break;
        }
        
        return handlingList;
    }
    
    /**
     * 将向量特征转换为md5
     * @param feature
     * @return
     */
    public static String computeMD5(float[] feature) {
        if (feature == null || feature.length <= 0)
            return "null";
        
        try {
            // 创建一个 MessageDigest 实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            
            // 将 float[] 数组转换为字节数组
            ByteBuffer buffer = ByteBuffer.allocate(4 * feature.length);
            for (float f : feature) {
                buffer.putFloat(f);
            }
            byte[] bytes = buffer.array();
            
            // 计算 MD5 值
            byte[] md5Bytes = md.digest(bytes);
            
            // 将 MD5 值转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error( "Error computing MD5 hash", e);
            return "error!";
        }
    }
    
    
    @Component
    @Lazy
    public static class Sync {
        
        @Autowired
        private Utils paramUtils;
        
        @Autowired
        private ApplicationContext applicationContext;
        
        @Autowired
        private CognitiveParamRepository mapper;
        
        @Value("${spring.application.name}")
        private String name;
        
        @Autowired(required = false)
        private List<FrameDefaultRequired> frameBufferRequireds = new ArrayList<>();
        
        /**
         * 每天一次，上一天的表没有用了，rename掉
         */
        @Scheduled(cron = "0/30 * * * * ?")
        @PostConstruct
        public void sync() {
            Initializer.deviceType.toString();
            
            Map<String, String> params = null;
            
            try {
                params = mapper.findAll().stream().collect(Collectors.toMap(CognitiveParam::getSKey, CognitiveParam::getSValue));
            } catch (Exception e) {
                return;
            }
            
            if (MapUtils.isEmpty(params)) {
                return;
            }
            
            try {
                if (params.containsKey("videoSaveImageType")) {
                    paramUtils.videoSaveImageType = Integer.valueOf(params.get("videoSaveImageType"));
                } else {
                    paramUtils.videoSaveImageType = 0;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("videoSaveOsgType")) {
                    paramUtils.videoSaveOsgType = Integer.valueOf(params.get("videoSaveOsgType"));
                } else {
                    paramUtils.videoSaveOsgType = 999;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("frameEncoderType")) {
                    paramUtils.frameEncoderType = Integer.valueOf(params.get("frameEncoderType"));
                } else {
                    paramUtils.frameEncoderType = 2;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("senseyexVideoCreateAffinity")) {
                    paramUtils.senseyexVideoCreateAffinity = Boolean.valueOf(params.get("senseyexVideoCreateAffinity"));
                } else {
                    paramUtils.senseyexVideoCreateAffinity = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("senseyexVideoSwitcherOnWorker")) {
                    paramUtils.senseyexVideoSwitcherOnWorker = Boolean.valueOf(params.get("senseyexVideoSwitcherOnWorker"));
                } else {
                    paramUtils.senseyexVideoSwitcherOnWorker = true;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("imageQuality")) {
                    paramUtils.imageQuality = Float.parseFloat(params.get("imageQuality"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("integrateQuality")) {
                    paramUtils.integrateQuality = Float.parseFloat(params.get("integrateQuality"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("subImageFolderCount")) {
                    if(StringUtils.isNotBlank(params.get("subImageFolderCount")))
                        paramUtils.subImageFolderCount = Integer.parseInt(params.get("subImageFolderCount"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("videoSaveImageNvencoderQuality")) {
                    paramUtils.videoSaveImageNvencoderQuality = Integer.parseInt(params.get("videoSaveImageNvencoderQuality"));
                } else {
                    paramUtils.videoSaveImageNvencoderQuality = 75;
                }
            } catch (Exception e) {
                paramUtils.videoSaveImageNvencoderQuality = 75;
            }
            
            try {
                if (params.containsKey("remappedMinNum")) {
                    paramUtils.remappedMinNum = Integer.parseInt(params.get("remappedMinNum"));
                    if (paramUtils.remappedMinNum < 1)
                        paramUtils.remappedMinNum = Integer.MAX_VALUE;
                } else {
                    paramUtils.remappedMinNum = Integer.MAX_VALUE;
                }
            } catch (Exception e) {
                paramUtils.remappedMinNum = Integer.MAX_VALUE;
            }
            
            try {
                if (params.containsKey("videoSaveImageScale")) {
                    paramUtils.videoSaveImageScale = Float.parseFloat(params.get("videoSaveImageScale"));
                    if (paramUtils.videoSaveImageScale < 0 || paramUtils.videoSaveImageScale > 1)
                        paramUtils.videoSaveImageScale = null;
                } else {
                    paramUtils.videoSaveImageScale = null;
                }
            } catch (Exception e) {
                paramUtils.videoSaveImageScale = null;
            }
            
            try {
                if (params.containsKey("rollingWindow")) {
                    paramUtils.rollingWindow = Integer.parseInt(params.get("rollingWindow"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("mempoolStrategy")) {
                    paramUtils.mempoolStrategy = params.get("mempoolStrategy");
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("runningType")) {
                    paramUtils.runningType = params.get("runningType");
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("periodShowupNum")) {
                    paramUtils.periodShowupNum = Integer.parseInt(params.get("periodShowupNum"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("deviceShowupNum")) {
                    paramUtils.deviceShowupNum = Integer.parseInt(params.get("deviceShowupNum"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("period")) {
                    paramUtils.period = Integer.parseInt(params.get("period"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("Lv0Threshold")) {
                    paramUtils.Lv0Threshold = Float.valueOf(params.get("Lv0Threshold"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("Lv1Threshold")) {
                    paramUtils.Lv1Threshold = Float.valueOf(params.get("Lv1Threshold"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("Lv2Threshold")) {
                    paramUtils.Lv2Threshold = Float.valueOf(params.get("Lv2Threshold"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("Lv3Threshold")) {
                    paramUtils.Lv3Threshold = Float.valueOf(params.get("Lv3Threshold"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("InspectThreshold")) {
                    paramUtils.InspectThreshold = Float.valueOf(params.get("InspectThreshold"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("retrieveType")) {
                    paramUtils.retrieveType = Integer.parseInt(params.get("retrieveType"));
                } else {
                    paramUtils.retrieveType = 0;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("needAttributeEverySecond")) {
                    paramUtils.seenAttributeEverySecond = Boolean.valueOf(params.get("needAttributeEverySecond"));
                } else {
                    paramUtils.seenAttributeEverySecond = true;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("nonSeenAttributeEverySecond")) {
                    paramUtils.nonSeenAttributeEverySecond = Boolean.valueOf(params.get("nonSeenAttributeEverySecond"));
                } else {
                    paramUtils.nonSeenAttributeEverySecond = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("watchFrameTiktokLevel"))
                    paramUtils.watchFrameTiktokLevel = Integer.parseInt(params.get("watchFrameTiktokLevel"));
                else
                    paramUtils.watchFrameTiktokLevel = 0;
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("gpuSeenMemLimit")) {
                    paramUtils.seenGpuMemLimit = Float.valueOf(params.get("gpuSeenMemLimit"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("nonSeenGpuMemLimit")) {
                    paramUtils.nonSeenGpuMemLimit = Float.valueOf(params.get("nonSeenGpuMemLimit"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("offlineVideoGpuMemLimit")) {
                    paramUtils.offlineVideoGpuMemLimit = Float.valueOf(params.get("offlineVideoGpuMemLimit"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("imageSceneSave")) {
                    paramUtils.imageSceneSave = Boolean.valueOf(params.get("imageSceneSave"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("multiplexDecoderFrameCount")) {
                    paramUtils.multiplexDecoderFrameCount = Integer.parseInt(params.get("multiplexDecoderFrameCount"));
                } else {
                    paramUtils.multiplexDecoderFrameCount = 128;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("multiplexDecodeFormat")) {
                    paramUtils.multiplexDecodeFormat = params.get("multiplexDecodeFormat");
                } else {
                    paramUtils.multiplexDecodeFormat = "nv12";
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("multiplexDecoderStrategy")) {
                    paramUtils.multiplexDecoderStrategy = params.get("multiplexDecoderStrategy");
                } else {
                    paramUtils.multiplexDecoderStrategy = "smart";
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("multiplexDecoderDuplicate")) {
                    paramUtils.multiplexDecoderDuplicate = Integer.parseInt(params.get("multiplexDecoderDuplicate"));
                } else {
                    paramUtils.multiplexDecoderDuplicate = 16;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("kestrelLogLevel")) {
                    KestrelApi.kestrel_log_set_level(Integer.parseInt(params.get("kestrelLogLevel")));
                } else {
                    KestrelApi.kestrel_log_set_level(1);
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("decoderCpuCount")) {
                    paramUtils.decoderCpuCount = Integer.parseInt(params.get("decoderCpuCount"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("seenMaxCount")) {
                    paramUtils.seenMaxCount = Integer.parseInt(params.get("seenMaxCount"));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("xWorkerHighRateMaxCount") && StringUtils.isNotBlank(params.get("xWorkerHighRateMaxCount"))) {
                    paramUtils.xWorkerHighRateMaxCount = Integer.parseInt(params.get("xWorkerHighRateMaxCount"));
                } else {
                    paramUtils.xWorkerHighRateMaxCount = 32;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("nonSeenMaxTrackletCount") && StringUtils.isNotBlank(params.get("nonSeenMaxTrackletCount"))) {
                    paramUtils.nonSeenMaxTrackletCount = Integer.parseInt(params.get("nonSeenMaxTrackletCount"));
                } else {
                    paramUtils.nonSeenMaxTrackletCount = 16;
                }
            } catch (Exception e) {
            }
            
            try {
                String newVideoBufferSizeLimit = "1920*1080,2560*1440,3840*2160,4096*2160,960*720,1280*720,640*480";
                
                if (params.containsKey("videoBufferSizeLimit") && StringUtils.isNotBlank(params.get("videoBufferSizeLimit")))
                    newVideoBufferSizeLimit = params.get("videoBufferSizeLimit");
                
                if (!StringUtils.equals(paramUtils.videoBufferSizeLimit, newVideoBufferSizeLimit)) {
                    paramUtils.videoBufferSizeLimit = newVideoBufferSizeLimit;
                    
                    FramePoolUtils.bufferedSizeLimit = Arrays.stream(paramUtils.videoBufferSizeLimit.split(","))
                            .map(limit -> {
                                String[] nums = limit.split("\\*");
                                return nums[0] + "*" + nums[1];
                            })
                            .collect(Collectors.toSet());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            try {
                if (params.containsKey("nonSeenQueueSize") && StringUtils.isNotBlank(params.get("nonSeenQueueSize"))) {
                    paramUtils.nonSeenQueueSize = Integer.parseInt(params.get("nonSeenQueueSize"));
                } else {
                    int requiredCount = 0;
                    
                    for (FrameDefaultRequired required : frameBufferRequireds)
                        requiredCount = Math.max(requiredCount, required.requiredNonSeenQueueSize(Initializer.deviceType));
                    
                    paramUtils.nonSeenQueueSize = requiredCount;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("nonSeenReusedFrameCount") && StringUtils.isNotBlank(params.get("nonSeenReusedFrameCount"))) {
                    paramUtils.nonSeenReusedFrameCount = Integer.parseInt(params.get("nonSeenReusedFrameCount"));
                } else {
                    int requiredCount = 0;
                    
                    for (FrameDefaultRequired required : frameBufferRequireds)
                        requiredCount = Math.max(requiredCount, required.requiredNonSeenReusedFrameCount(Initializer.deviceType));
                    
                    paramUtils.nonSeenReusedFrameCount = requiredCount;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("nonSeenMaxCount") && StringUtils.isNotBlank(params.get("nonSeenMaxCount"))) {
                    paramUtils.nonSeenMaxCount = Integer.parseInt(params.get("nonSeenMaxCount"));
                } else {
                    int requiredCount = Integer.MAX_VALUE;
                    
                    for (FrameDefaultRequired required : frameBufferRequireds)
                        requiredCount = Math.min(requiredCount, required.requiredNonSeenMaxCount(Initializer.deviceType));
                    
                    paramUtils.nonSeenMaxCount = requiredCount;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("nonSeenQueueType") && StringUtils.isNotBlank(params.get("nonSeenQueueType"))) {
                    paramUtils.nonSeenQueueSize = Integer.parseInt(params.get("nonSeenQueueType"));
                    
                    if (paramUtils.nonSeenQueueSize != 0 && paramUtils.nonSeenQueueSize != 1) {
                        paramUtils.nonSeenQueueType = 0;
                    }
                } else {
                    paramUtils.nonSeenQueueType = 0;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("unknownRegTimeCount")) {
                    paramUtils.unknownRegTimeCount = Integer.parseInt(params.get("unknownRegTimeCount"));
                } else {
                    paramUtils.unknownRegTimeCount = 10;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("faceSizeLimit")) {
                    paramUtils.faceSizeLimit = Integer.parseInt(params.get("faceSizeLimit"));
                }
            } catch (Exception e) {
            }
            try {
                if (params.containsKey("attributeAggressive")) {
                    paramUtils.attributeAggressive = Float.valueOf(params.get("attributeAggressive"));
                } else {
                    paramUtils.attributeAggressive = null;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("seekSplit"))
                    paramUtils.seekSplit = Integer.parseInt(params.get("seekSplit"));
                else
                    paramUtils.seekSplit = 0;
            } catch (Exception e) {
                paramUtils.seekSplit = 0;
            }
            
            try {
                if (params.containsKey("lockVideoExpire")) {
                    paramUtils.lockVideoExpire = Integer.parseInt(params.get("lockVideoExpire"));
                } else {
                    paramUtils.lockVideoExpire = 30;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("frameSkip")) {
                    paramUtils.frameSkip = Integer.parseInt(params.get("frameSkip"));
                } else {
                    paramUtils.frameSkip = 3;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("keepAliveExpire")) {
                    paramUtils.keepAliveExpire = Integer.parseInt(params.get("keepAliveExpire"));
                } else {
                    paramUtils.keepAliveExpire = 60;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("logged")) {
                    paramUtils.logged = Boolean.valueOf(params.get("logged").toString());
                } else {
                    paramUtils.logged = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("rebalanceOn")) {
                    paramUtils.rebalanceOn = Boolean.valueOf(params.get("rebalanceOn"));
                } else {
                    paramUtils.rebalanceOn = true;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("pedestrianHarpyEnabled")) {
                    paramUtils.pedestrianHarpyEnabled = Boolean.valueOf(params.get("pedestrianHarpyEnabled"));
                } else {
                    paramUtils.pedestrianHarpyEnabled = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("nonSeenAttributeBeginEnd")) {
                    paramUtils.nonSeenAttributeBeginEnd = Boolean.valueOf(params.get("nonSeenAttributeBeginEnd"));
                } else {
                    paramUtils.nonSeenAttributeBeginEnd = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("imwriteFlags")) {
                    paramUtils.imwriteFlags = Arrays.stream(params.get("imwriteFlags").split(",")).mapToInt(flag -> Integer.parseInt(flag)).toArray();
                } else {
                    paramUtils.imwriteFlags = new int[]{1, 95};
                }
            } catch (Exception e) {
            }
            
            try {
                int oldType = paramUtils.useTypeAttribute;
                int newType = paramUtils.useTypeAttribute;
                
                if (params.containsKey("useTypeAttribute")) {
                    newType = Integer.parseInt(params.get("useTypeAttribute"));
                } else {
                    newType = 0;
                }
                
                if (oldType != newType) {
                    paramUtils.useTypeAttribute = newType;
                    applicationContext.publishEvent(new AttributeTypeChangedEvent(oldType, newType));
                }
            } catch (Exception e) {
            }
            
            try {
                int oldType = paramUtils.useTypeHunter;
                int newType = paramUtils.useTypeHunter;
                
                if (params.containsKey("useTypeHunter")) {
                    newType = Integer.parseInt(params.get("useTypeHunter"));
                } else {
                    newType = 1;
                }
                
                if (oldType != newType) {
                    paramUtils.useTypeHunter = newType;
                    applicationContext.publishEvent(new HunterTypeChangedEvent(newType, oldType));
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("debug_0")) {
                    paramUtils.debug_0 = Boolean.valueOf(params.get("debug_0"));
                } else {
                    paramUtils.debug_0 = false;
                }
            } catch (Exception e) {
            }
            
            
            try {
                if (params.containsKey("debug_1")) {
                    paramUtils.debug_1 = Boolean.valueOf(params.get("debug_1"));
                } else {
                    paramUtils.debug_1 = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("debug_2")) {
                    paramUtils.debug_2 = Boolean.valueOf(params.get("debug_2"));
                } else {
                    paramUtils.debug_2 = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("debug_3")) {
                    paramUtils.debug_3 = Boolean.valueOf(params.get("debug_3"));
                } else {
                    paramUtils.debug_3 = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("debug_4")) {
                    paramUtils.debug_4 = Boolean.valueOf(params.get("debug_4"));
                } else {
                    paramUtils.debug_4 = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("debug_5")) {
                    paramUtils.debug_5 = Boolean.valueOf(params.get("debug_5"));
                } else {
                    paramUtils.debug_5 = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("shieldFace_switch")) {
                    paramUtils.shieldFaceSwitch = Integer.valueOf(params.get("shieldFace_switch"));
                } else {
                    paramUtils.shieldFaceSwitch = 0;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("imageCapture_switch")) {
                    paramUtils.imageCaptureSwitch = Integer.valueOf(params.get("imageCapture_switch"));
                } else {
                    paramUtils.imageCaptureSwitch = 0;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("maxTrackingTime")) {
                    paramUtils.maxTrackingTime = Integer.valueOf(params.get("maxTrackingTime"));
                } else {
                    paramUtils.maxTrackingTime = -1;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("selectFrameTimeInterVal")) {
                    paramUtils.selectFrameTimeInterVal = Integer.valueOf(params.get("selectFrameTimeInterVal"));
                } else {
                    paramUtils.selectFrameTimeInterVal = -1;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("faceRecognitionMode")) {
                    paramUtils.faceRecognitionMode = Integer.valueOf(params.get("faceRecognitionMode"));
                } else {
                    paramUtils.faceRecognitionMode = 0;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("strangerRecognition")) {
                    paramUtils.strangerRecognition = Integer.valueOf(params.get("strangerRecognition"));
                } else {
                    paramUtils.strangerRecognition = 0;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("dropFace")) {
                    paramUtils.dropFace = Integer.valueOf(params.get("dropFace"));
                } else {
                    paramUtils.dropFace = 1;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("duplicateTargetsTime")) {
                    paramUtils.duplicateTargetsTime = Integer.valueOf(params.get("duplicateTargetsTime"));
                } else {
                    paramUtils.duplicateTargetsTime = 1;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("smallDetectThresh")) {
                    paramUtils.smallDetectThresh = Float.valueOf(params.get("smallDetectThresh"));
                } else {
                    paramUtils.smallDetectThresh = 0.3f;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("largeDetectThresh")) {
                    paramUtils.largeDetectThresh = Float.valueOf(params.get("largeDetectThresh"));
                } else {
                    paramUtils.largeDetectThresh = 0.6f;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("selectFrameThresh")) {
                    paramUtils.selectFrameThresh = Float.valueOf(params.get("selectFrameThresh"));
                } else {
                    paramUtils.selectFrameThresh = 0.2f;
                }
            } catch (Exception e) {
            }
            try {
                if (params.containsKey("selectPedFrameThresh")) {
                    paramUtils.selectPedFrameThresh = Float.valueOf(params.get("selectPedFrameThresh"));
                } else {
                    paramUtils.selectPedFrameThresh = 0.3f;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("useSelfCalCapturedTime")) {
                    paramUtils.useSelfCalCapturedTime = Boolean.valueOf(params.get("useSelfCalCapturedTime").toString());
                } else {
                    paramUtils.useSelfCalCapturedTime = false;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("dropFaceQuality")) {
                    paramUtils.dropFaceQuality = params.get("dropFaceQuality");
                }
            } catch (Exception e) {
            }
            
            try {
                paramUtils.displayTarget = params.getOrDefault("displayTarget", "");
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("dropFaceFlag")) {
                    paramUtils.dropFaceFlag = Integer.valueOf(params.get("dropFaceFlag"));
                } else {
                    paramUtils.dropFaceFlag = 1;
                }
            } catch (Exception e) {
            }
            try {
                if (params.containsKey("dropPedFlag")) {
                    paramUtils.dropPedFlag = Integer.valueOf(params.get("dropPedFlag"));
                } else {
                    paramUtils.dropPedFlag = 1;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("dropFaceIndentify")) {
                    paramUtils.dropFaceIndentify = Integer.valueOf(params.get("dropFaceIndentify"));
                } else {
                    paramUtils.dropFaceIndentify = 1;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("videoStatusErrorTime")) {
                    paramUtils.videoStatusErrorTime = Integer.valueOf(params.get("videoStatusErrorTime"));
                } else {
                    paramUtils.videoStatusErrorTime = 1;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("videoStatusErrorInternalTime")) {
                    paramUtils.videoStatusErrorInternalTime = Integer.valueOf(params.get("videoStatusErrorInternalTime"));
                } else {
                    paramUtils.videoStatusErrorInternalTime = 10;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("xStreamReopenErrorTime")) {
                    paramUtils.xStreamReopenErrorTime = Integer.valueOf(params.get("xStreamReopenErrorTime"));
                } else {
                    paramUtils.xStreamReopenErrorTime = 15;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("faceStreamReopenErrorTime")) {
                    paramUtils.faceStreamReopenErrorTime = Integer.valueOf(params.get("faceStreamReopenErrorTime"));
                } else {
                    paramUtils.faceStreamReopenErrorTime = 60;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("streamReopenSleepTime")) {
                    paramUtils.streamReopenSleepTime = Integer.valueOf(params.get("streamReopenSleepTime"));
                } else {
                    paramUtils.streamReopenSleepTime = 3;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("streamOpenTimeout")) {
                    paramUtils.streamOpenTimeout = Integer.valueOf(params.get("streamOpenTimeout"));
                } else {
                    paramUtils.streamOpenTimeout = 5000;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("videoFrameNullCount")) {
                    paramUtils.videoFrameNullCount = Integer.valueOf(params.get("videoFrameNullCount"));
                } else {
                    paramUtils.videoFrameNullCount = 20;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("videoStatusErrorKestrelAgainTime")) {
                    paramUtils.videoStatusErrorKestrelAgainTime = Integer.valueOf(params.get("videoStatusErrorKestrelAgainTime"));
                } else {
                    paramUtils.videoStatusErrorKestrelAgainTime = 10;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("videoSaveImageWay")) {
                    paramUtils.videoSaveImageWay = Integer.valueOf(params.get("videoSaveImageWay"));
                } else {
                    paramUtils.videoSaveImageWay = 0;
                }
            } catch (Exception e) {
            }
            
            
            try {
                if (params.containsKey("instanceLocalSwitch")) {
                    paramUtils.instanceLocalSwitch = Integer.valueOf(params.get("instanceLocalSwitch"));
                } else {
                    paramUtils.instanceLocalSwitch = 0;
                }
            } catch (Exception e) {
            }
            
            try {
                if (params.containsKey("stabilityFlag")) {
                    paramUtils.stabilityFlag = Boolean.valueOf(params.get("stabilityFlag"));
                } else {
                    paramUtils.stabilityFlag = false;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("threadPoolNum")) {
                    paramUtils.threadPoolNum = Integer.valueOf(params.get("threadPoolNum"));
                } else {
                    paramUtils.threadPoolNum = 0;
                }
            } catch (Exception e) {
            }


            try {
                if (params.containsKey("imagesSaveExt")) {
                    paramUtils.imagesSaveExt =params.get("imagesSaveExt");
                } else {
                    paramUtils.imagesSaveExt = "png";
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("longCostTs")) {
                    paramUtils.longCostTs =Integer.parseInt(params.get("longCostTs"));
                } else {
                    paramUtils.longCostTs = 1000;
                }
            } catch (Exception e) {
                paramUtils.longCostTs = 1000;
            }

            try {
                if (params.containsKey("longCostSaveImgTs")) {
                    paramUtils.longCostSaveImgTs =Integer.parseInt(params.get("longCostSaveImgTs"));
                } else {
                    paramUtils.longCostSaveImgTs = 300;
                }
            } catch (Exception e) {
                paramUtils.longCostSaveImgTs = 300;
            }


            try {
                if (params.containsKey("faceLargeRoiExpend")) {
                    paramUtils.faceLargeRoiExpand = Boolean.valueOf(params.get("faceLargeRoiExpend"));
                } else {
                    paramUtils.faceLargeRoiExpand = false;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("cogDebug")) {
                    paramUtils.cogDebug = Integer.valueOf(params.get("cogDebug"));
                } else {
                    paramUtils.cogDebug = 1;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("scgFacePipeline")) {
                    paramUtils.scgFacePipeline = Boolean.valueOf(params.get("scgFacePipeline"));
                } else {
                    paramUtils.scgFacePipeline = true;
                }
            } catch (Exception e) {
            }
            try {
                if (params.containsKey("pedSaveCluster")) {
                    paramUtils.pedSaveCluster = Boolean.valueOf(params.get("pedSaveCluster"));
                } else {
                    paramUtils.pedSaveCluster = false;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("faceIsQueue")) {
                    paramUtils.faceIsQueue = Boolean.valueOf(params.get("faceIsQueue"));
                } else {
                    paramUtils.faceIsQueue = false;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("qualityStepThresh")) {
                    paramUtils.qualityStepThresh = Float.valueOf(params.get("qualityStepThresh"));
                } else {
                    paramUtils.qualityStepThresh = 0.01f;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("clipExtend")) {
                    paramUtils.clipExtend = Float.valueOf(params.get("clipExtend"));
                } else {
                    paramUtils.clipExtend = 0.2f;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("saveImagePathDisk")) {
                    paramUtils.saveImagePathDisk = Boolean.valueOf(params.get("saveImagePathDisk"));
                } else {
                    paramUtils.saveImagePathDisk = false;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("printLog")) {
                    String printLogValue = params.get("printLog");
                    paramUtils.printLog = printLogValue != null && printLogValue.contains(HostUtils.UUID());
                } else {
                    paramUtils.printLog = false;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("useH265")) {
                    paramUtils.useH265 = Integer.valueOf(params.get("useH265"));
                } else {
                    paramUtils.useH265 = 1;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("fetchFrameSize")) {
                    paramUtils.fetchFrameSize = Integer.valueOf(params.get("fetchFrameSize"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("removeSearchEngine")) {
                    paramUtils.removeSearchEngine = Boolean.valueOf(params.get("removeSearchEngine"));
                } else {
                    paramUtils.removeSearchEngine = true;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("nvDecoderBuffSize") && StringUtils.isNotBlank(params.get("nvDecoderBuffSize"))) {
                    paramUtils.nvDecoderBuffSize = Integer.parseInt(params.get("nvDecoderBuffSize"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("nvDecoderFramePoolSize") && StringUtils.isNotBlank(params.get("nvDecoderFramePoolSize"))) {
                    paramUtils.nvDecoderFramePoolSize = Integer.parseInt(params.get("nvDecoderFramePoolSize"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("lockVideoDevicesNum")) {
                    paramUtils.lockVideoDevicesNum = Integer.parseInt(params.get("lockVideoDevicesNum"));
                } else {
                    paramUtils.lockVideoDevicesNum = 150;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("longAfterPipeCost")) {
                    paramUtils.longAfterPipeCost =Integer.parseInt(params.get("longAfterPipeCost"));
                } else {
                    paramUtils.longAfterPipeCost = 200;
                }
            } catch (Exception e) {
                paramUtils.longAfterPipeCost = 200;
            }

            try {
                if (params.containsKey("offlineVideoLimit")) {
                    paramUtils.offlineVideoLimit =Long.parseLong(params.get("offlineVideoLimit"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("checkQueueTime")) {
                    paramUtils.checkQueueTime =Long.parseLong(params.get("checkQueueTime"));
                } else {
                    paramUtils.checkQueueTime = 20;
                }
            } catch (Exception e) {
                paramUtils.checkQueueTime = 20;
            }

            try {
                if (params.containsKey("threadSelectPool")) {
                    paramUtils.threadSelectPool = Boolean.valueOf(params.get("threadSelectPool"));
                } else {
                    paramUtils.threadSelectPool = true;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("kafkaHeaderKeyMode")) {
                    paramUtils.kafkaHeaderKeyMode = Boolean.valueOf(params.get("kafkaHeaderKeyMode"));
                } else {
                    paramUtils.kafkaHeaderKeyMode = true;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("printHandleTime")) {
                    paramUtils.printHandleTime =Integer.parseInt(params.get("printHandleTime"));
                } else {
                    paramUtils.printHandleTime = 300;
                }
            } catch (Exception e) {
                paramUtils.printHandleTime = 300;
            }
            try {
                if (params.containsKey("xGpuMemRateLimit")) {
                    paramUtils.xGpuMemRateLimit = Float.valueOf(params.get("xGpuMemRateLimit"));
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("offline_flag")) {
                    paramUtils.offline_flag = Boolean.valueOf(params.get("offline_flag"));
                } else {
                    paramUtils.offline_flag = false;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("cameraDispatch")) {
                    paramUtils.cameraDispatch = Boolean.valueOf(params.get("cameraDispatch"));
                } else {
                    paramUtils.cameraDispatch = true;
                }
            } catch (Exception e) {
            }

            try {
                if (params.containsKey("featureSearchSfd")) {
                    paramUtils.featureSearchSfd = Boolean.valueOf(params.get("featureSearchSfd"));
                } else {
                    paramUtils.featureSearchSfd = true;
                }
            } catch (Exception e) {
            }

            applicationContext.publishEvent(new ParamRefreshedEvent());
        }
    }
    
    private static String readVersion() {
        String version = "na";
        
        InputStream in = Utils.class.getResourceAsStream("/HEAD");
        BufferedReader br = new BufferedReader(new InputStreamReader(in));
        String head = "";
        try {
            head = br.readLine();
        } catch (IOException e) {
        } finally {
            try {
                br.close();
                in.close();
            } catch (IOException e) {
            }
        }
        
        if (StringUtils.isBlank(head))
            return version;
        
        if (head.indexOf("ref:") < 0) {
            version = head;
            return String.format("%s:%s", "na", version);
        }
        
        String ref = head.split(" ")[1];
        in = Utils.class.getResourceAsStream("/" + ref);
        br = new BufferedReader(new InputStreamReader(in));
        try {
            version = br.readLine();
        } catch (IOException e) {
        } finally {
            try {
                br.close();
                in.close();
            } catch (IOException e) {
            }
        }
        
        return String.format("%s:%s", ref, version);
    }
    
    public static interface FrameDefaultRequired {
        
        public int requiredNonSeenReusedFrameCount(DeviceType type);
        
        public int requiredNonSeenQueueSize(DeviceType type);
        
        public int requiredNonSeenMaxCount(DeviceType type);
    }

    /**
     * 获取本机hostname
     */
    public String getLocalHostname() {
        try {
            String hostname = InetAddress.getLocalHost().getHostName();
            if(hostname != null && !hostname.equalsIgnoreCase("localhost")){
                if(log.isDebugEnabled()) log.debug(">>> [get localhostname] method by InetAddress.getLocalHost().getHostName(),Hostname is " + hostname);
                return hostname;
            }

            hostname = System.getenv("HOSTNAME");
            if(hostname != null && !hostname.equalsIgnoreCase("localhost")){
                if(log.isDebugEnabled()) log.debug(">>> [get localhostname] method by System.getenv(\"HOSTNAME\"),Hostname is " + hostname);
                return hostname;
            }

            return hostname;
        } catch (UnknownHostException e) {
            log.error("Failed to get local hostname", e);
            return "localhost";
        }
    }

    public String getLocalIpAddress() {

        try {
            // 获取所有网络接口
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface ni = interfaces.nextElement();
                // 跳过回环接口、虚拟接口等
                if (ni.isLoopback() || ni.isVirtual() || !ni.isUp()) {
                    continue;
                }
                Enumeration<InetAddress> addresses = ni.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    // 只获取IPv4地址
                    if (addr instanceof Inet4Address) {
                        String ip = addr.getHostAddress();
                        if(log.isDebugEnabled()) {
                            log.debug(">>> [get local ip] interface={}, ip={}", ni.getDisplayName(), ip);
                        }
                        return ip;
                    }
                }
            }
            // 如果没有找到合适的IP，返回本地回环地址
            return "127.0.0.1";
        } catch (SocketException e) {
            log.error("Failed to get local ip", e);
            return "127.0.0.1";
        }
    }


}
