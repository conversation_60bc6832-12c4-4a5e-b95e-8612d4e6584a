package com.sensetime.intersense.cognitivesvc.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ThreadLocalRandom;

import javax.sql.DataSource;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Component("unlockLiquibase")
@Slf4j
public class DatasourceBeanPostProcessor implements BeanPostProcessor {
	
	@Override
	public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
		if (bean instanceof DataSource) {
			try (Connection connection = ((DataSource)bean).getConnection();) {
				try (PreparedStatement stat = connection.prepareStatement("select LOCKED from DATABASECHANGELOGLOCK")) {
					try (ResultSet reset = stat.executeQuery()) {
						if (reset.next()) {
							boolean locked = reset.getBoolean("LOCKED");
							if (locked) {
								int randomSecond = ThreadLocalRandom.current().nextInt(10000);
								log.warn("liquibase has bean locked. wait 30s to unlock it.");
								Thread.sleep(20000 + randomSecond);

								try (PreparedStatement statUpdate = connection .prepareStatement("update DATABASECHANGELOGLOCK set LOCKED = 0")) {
									int num = statUpdate.executeUpdate();
									if (num > 0) {
										log.warn("success to unlock liquibase lock.");
									}
								}
							}
						}
					}
				}
			} catch (Exception e) {
				log.warn(e.getLocalizedMessage());
			}
		}
		
		return bean;
	}
	
	@Autowired
	private ApplicationContext applicationContext;

//	@Override
//	public void onApplicationEvent(ContextRefreshedEvent event) {
//		if(applicationContext != event.getApplicationContext())
//			return ;
//
//		Timer timer = new Timer();
//
//		timer.schedule(new TimerTask() {
//			@Override
//			public void run() {
//				DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
//				defaultListableBeanFactory.removeBeanDefinition("unlockLiquibase");
//			}
//		}, 10000);
//	}
}
