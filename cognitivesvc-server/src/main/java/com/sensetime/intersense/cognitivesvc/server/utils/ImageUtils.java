package com.sensetime.intersense.cognitivesvc.server.utils;

import com.alibaba.fastjson.JSON;
import com.sensetime.lib.weblib.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class ImageUtils {
	
	public static final ThreadLocal<SimpleDateFormat> dateFormatter = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd/HH"));

	public static final ThreadLocal<SimpleDateFormat> 	dateFormatter_DAY = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));

	public static final ThreadLocal<SimpleDateFormat> 	dateFormatter_HOUR = ThreadLocal.withInitial(() -> new SimpleDateFormat("HH"));

	public static final  Pattern pattern = Pattern.compile("^(\\d{8})-([0-9a-fA-F]{8})-([0-9a-fA-F]{14})-([0-9a-fA-F]{8})-([0-9a-fA-F]{8})$");


	public static Boolean preMkDirPostContructCompleted = false;


	public static final File storeImageToMemoryFileSystem(String figureImageBase64){
		return storeImageToFile(figureImageBase64, "/dev/shm");
	}
	
	public static final File storeImageToFile(String figureImageBase64, String basePath){
		if(figureImageBase64.startsWith("data:image")) {
            log.error("input should not begin with 'data:image/jpg;base64,xxxxxxx', just raw data");
            throw new BusinessException("3007","input should not begin with 'data:image/jpg;base64,xxxxxxx', just raw data");
        }
		
		File tmpImage = new File(basePath + "/" + Math.abs(ThreadLocalRandom.current().nextLong()) + ".jpg");
		if(tmpImage.exists())
			tmpImage.delete();
		
		try(FileOutputStream output = new FileOutputStream(tmpImage)){
			output.write(base64ToBytes(figureImageBase64));
			output.flush();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		return tmpImage;
	}

	public static final File storeImageToFileName(String figureImageBase64, String fileName){
		if(figureImageBase64.startsWith("data:image")) {
			log.error("input should not begin with 'data:image/jpg;base64,xxxxxxx', just raw data");
			throw new BusinessException("3007","input should not begin with 'data:image/jpg;base64,xxxxxxx', just raw data");
		}

		File descFile = new File(fileName);
		if(descFile.exists())
			descFile.delete();

		try(FileOutputStream output = new FileOutputStream(descFile)){
			output.write(base64ToBytes(figureImageBase64));
			output.flush();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}

		return descFile;
	}
	
	public static final String bytesToBase64(byte[] image){
		return Base64.getEncoder().encodeToString(image);
	}
	
	public static final byte[] base64ToBytes(String figureImageBase64){
		byte[] image = null;
		
		try{
			image = Base64.getDecoder().decode(figureImageBase64);
		}catch(Exception e) {
			image = Base64.getMimeDecoder().decode(figureImageBase64);
		}
		
		if(image == null) {
            log.error("base64 image read error.");
            throw new BusinessException("3008","base64 image read error.");
        }
		
		for(int i = 0; i < image.length; ++i)
            if(image[i] < 0)
            	image[i] += 256;
		
		return image;
	}

	//	解析中生成的文件 提前创建目录
    public static File newFile_returnFile(String savePath) {
		boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;
		boolean loggedForMkdir = Utils.instance.watchFrameTiktokLevel == -7991;
// todo
    	File path = new File(Utils.instance.savePath + "/" + Objects.requireNonNullElse(savePath, "") + "/" + dateFormatter.get().format(new Date()) + "/" + ThreadLocalRandom.current().nextInt(Utils.instance.subImageFolderCount) + "/");
		// 启动时目录创建比较慢时，仍然创建目录

		if(!preMkDirPostContructCompleted){
			if(loggedForMkdir){
				log.info("newFile path.exists executed...");
			}
			if(!path.exists())
				path.mkdirs();
		}

		//		if(!path.exists())
		//			path.mkdirs();

		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage newFile start at now");
		}

		File files = new File(path.getAbsolutePath() + "/" + UUID.randomUUID().toString().replaceAll("-", "") + ".jpg");
		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage File start at now");
		}
		return files;
    }


	// 为了和动态模型包兼容，保留该方法
	public static File newFile(String savePath) {
		boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;
		boolean loggedForMkdir = Utils.instance.watchFrameTiktokLevel == -7991;
		// todo
		File path = new File(Utils.instance.savePath + "/" + Objects.requireNonNullElse(savePath, "") + "/" + dateFormatter.get().format(new Date()) + "/" + ThreadLocalRandom.current().nextInt(Utils.instance.subImageFolderCount) + "/");
		// 启动时目录创建比较慢时，仍然创建目录
		// todo 存储时再判断。
		if(!preMkDirPostContructCompleted){
			if(loggedForMkdir){
				log.info("newFile path.exists executed...");
			}
			if(!path.exists())
				path.mkdirs();
		}

		//		if(!path.exists())
		//			path.mkdirs();

		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage newFile start at now");
		}

		File files = new File(path.getAbsolutePath() + "/" + UUID.randomUUID().toString().replaceAll("-", "") + ".jpg");
		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage File start at now");
		}
		return files;
	}

	public static File newFileWithMkdir(String savePath) {
		boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

		File path = new File(Utils.instance.savePath + "/" + Objects.requireNonNullElse(savePath, "") + "/" + dateFormatter.get().format(new Date()));
		if(!path.exists())
			path.mkdirs();

		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage newFile start at now");
		}

		File files = new File(path.getAbsolutePath() + "/" + UUID.randomUUID().toString().replaceAll("-", "") + ".jpg");
		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage File start at now");
		}
		return files;
	}

	public static File newFileWithMkdir(String savePath,Boolean exist) {
		boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;

		File path = new File(Utils.instance.savePath + "/" + Objects.requireNonNullElse(savePath, "") + "/" + dateFormatter.get().format(new Date()));
		if(!exist) {
			if (!path.exists())
				path.mkdirs();
		}
		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage newFile start at now");
		}

		File files = new File(path.getAbsolutePath() + "/" + UUID.randomUUID().toString().replaceAll("-", "") + ".jpg");
		if(loggedForCost){
			log.info("[VideoHandleLog] [Cost] saveImage File start at now");
		}
		return files;
	}
	public static File newFileWithPathImage(String savePath,Boolean exist) {

		File file = new File(savePath);

		File parentDir = file.getParentFile();
		if (parentDir != null) {
			File grandParentDir = parentDir.getParentFile();
			if (grandParentDir != null) {
				return new File(grandParentDir.getParent() + "/" + dateFormatter_HOUR.get().format(new Date())  + ".txt");
			}
		}
       return null;

	}

	public static String newFileWithPathImage2(String imagePath, Boolean exist) {

		// 将路径字符串解析为Path对象
		Path path = Paths.get(imagePath);

		// 获取图片路径的上上级目录
		Path parentDir = path.getParent();
		if (parentDir != null) {
			Path grandParentDir = parentDir.getParent();
			if (grandParentDir != null) {
				return grandParentDir.getParent().toAbsolutePath() + "/" + dateFormatter_HOUR.get().format(new Date())  + ".txt" ;
			} else {
				System.out.println("Image's path does not have a grandparent directory.");
			}
		} else {
			System.out.println("Image's path does not have a parent directory.");
		}
		return null;

	}

//	public static File newFile(String savePath, Boolean checkPath) {
//		boolean loggedForCost = Utils.instance.watchFrameTiktokLevel == -789;
//
//		File path = new File(Utils.instance.savePath + "/" + Objects.requireNonNullElse(savePath, "") + "/" + dateFormatter.get().format(new Date()));
////		// todo
//
//		if(checkPath)
//		{
//			if(!path.exists())
//				path.mkdirs();
//
//			if(loggedForCost){
//				log.info("[VideoHandleLog] [Cost] saveImage checkExist start at now");
//			}
//
//		}
//
//		if(loggedForCost){
//			log.info("[VideoHandleLog] [Cost] saveImage newFile start at now");
//		}
//
//		File files = new File(path.getAbsolutePath() + "/" + UUID.randomUUID().toString().replaceAll("-", "") + ".jpg");
//		if(loggedForCost){
//			log.info("[VideoHandleLog] [Cost] saveImage File start at now");
//		}
//		return files;
//	}

	public static File newFileWithTs(String savePath,long frameIndex ) {
		File path = new File(Utils.instance.savePath + "/" + Objects.requireNonNullElse(savePath, "") + "/" + dateFormatter.get().format(new Date()));
		if(!path.exists())
			path.mkdirs();

		return new File(path.getAbsolutePath() + "/" + System.currentTimeMillis()+"_"+frameIndex + ".jpg");
	}

	public static File newFileName(String savePath, String name,String annotatorName) {
		File path = new File(Utils.instance.savePath + "/" + Objects.requireNonNullElse(savePath + "/" + dateFormatter.get().format(new Date()), "") + "/" + annotatorName);
		if(!path.exists())
			path.mkdirs();

		return new File(path.getAbsolutePath() + "/" + name + ".jpg");
	}
	public static void newFileNameJson(String savePath, String fileName, Map<String, Object> target, String annotatorName) {

		try {
			File path = new File(Utils.instance.savePath + "/" + Objects.requireNonNullElse(savePath + "/" + dateFormatter.get().format(new Date()), "") + "/" + annotatorName);
			if (!path.exists()) {
				path.mkdirs();
			}
			File jsonFile = new File(path.getAbsolutePath() + "/" + fileName + ".json");
			FileOutputStream output = new FileOutputStream(jsonFile.getAbsolutePath());
			output.write(JSON.toJSONBytes(target));
			output.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void mkdirsByHour(List<String> dirs, boolean today){
		//用 preMakeDirList 创建目录
		if(today){
			log.info("[preMakeDirsCron] start to mkdir {} for today ", dirs);
		}else {
			log.info("[preMakeDirsCron] start to mkdir {} for tomorrow ", dirs);
		}

		String[] paths = new String[]{"00","01","02","03","04","05","06","07","08","09",
				"10","11","12","13","14","15","16","17","18","19",
				"20","21","22","23"};

		for (int i = 0; i < dirs.size(); i++) {
			try{
				String dir ;
				if(!today)
					dir = Utils.instance.savePath + "/" + dirs.get(i).trim() + "/" + ImageUtils.dateFormatter_DAY.get().format(new Date(new Date().getTime() + 24 * 3600 * 1000));
				else
					dir = Utils.instance.savePath + "/" + dirs.get(i).trim() + "/" + ImageUtils.dateFormatter_DAY.get().format(new Date(new Date().getTime()));
				File dirFile = new File(dir);
				if(!dirFile.exists())
					dirFile.mkdirs();
				for (int j = 0; j < paths.length; j++) {
					for (int k = 0; k < Utils.instance.subImageFolderCount; k++) {
						try{
							File hour = new File(dir + "/" + paths[j] + "/" + k);
							if(!hour.exists())
								hour.mkdirs();
						}catch (Exception e){
							e.printStackTrace();
						}
					}

				}
				log.info("[preMakeDirsCron] mkdir {}", dir);
			}catch (Exception e){
				e.printStackTrace();
			}

		}

		if(Utils.instance.saveImagePathDisk) {
            // Extract common logic outside the loop
			String day = ImageUtils.dateFormatter_DAY.get().format(new Date());
			String dayAdd = ImageUtils.dateFormatter_DAY.get().format(new Date(System.currentTimeMillis() + 24 * 3600 * 1000));
			// Iterate over paths
			for (String path : paths) {
				// Create writers for each directory using the pre-calculated dates
				dirs.forEach(item -> ImageSaveUtils.getInstance().createWriter(item, path, day));
				dirs.forEach(item -> ImageSaveUtils.getInstance().createWriter(item, path, dayAdd));
			}
		}

	}


	/*public static Boolean matchesOsgImageUrl(String imageUrl){
		if(StringUtils.isBlank(imageUrl))
			return false;
		else {
			String[] split = imageUrl.split("/");
			if(split.length > 1) {
				String key = split[split.length - 1];
				Matcher matcher = pattern.matcher(key);
				return matcher.matches();
			}
		}
		return false;
	}*/
}
