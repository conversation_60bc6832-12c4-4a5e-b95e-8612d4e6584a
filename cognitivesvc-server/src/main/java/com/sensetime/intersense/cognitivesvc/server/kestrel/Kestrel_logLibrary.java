package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sensetime.intersense.cognitivesvc.server.kestrel.entity.kestrel_log_meta_t;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_log</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */
public interface Kestrel_logLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_log"; 
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_logLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_logLibrary INSTANCE = (Kestrel_logLibrary)Native.load(Kestrel_logLibrary.JNA_LIBRARY_NAME, Kestrel_logLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_log.h</i><br>
	 * enum values
	 */
	public static interface kestrel_log_level_e {
		/** <i>native declaration : include/kestrel_log.h:17</i> */
		public static final int KESTREL_LL_TRACE = 0;
		/** <i>native declaration : include/kestrel_log.h:19</i> */
		public static final int KESTREL_LL_DEBUG = 1;
		/** <i>native declaration : include/kestrel_log.h:21</i> */
		public static final int KESTREL_LL_INFO = 2;
		/** <i>native declaration : include/kestrel_log.h:23</i> */
		public static final int KESTREL_LL_WARNING = 3;
		/** <i>native declaration : include/kestrel_log.h:25</i> */
		public static final int KESTREL_LL_ERROR = 4;
		/** <i>native declaration : include/kestrel_log.h:27</i> */
		public static final int KESTREL_LL_ESSENTIAL = 999;
	};
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_COLOR = (int)0x01;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_LABEL = (int)0x02;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_LINENO = (int)0x04;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_DATE = (int)0x08;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_TIME = (int)0x10;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_NANOSECOND = (int)0x20;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_THREADID = (int)0x40;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_FUNCNAME = (int)0x80;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_FILENAME = (int)0x100;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_NO_TAG = (int)0x200;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_MILLISECOND = (int)0x400;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_MICROSECOND = (int)0x800;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_CFG_SAFE_OUTPUT = (int)0x1000;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final int KESTREL_LOG_LABEL_MAX_LEN = (int)15;
	/** <i>native declaration : include/kestrel_log.h</i> */
	public static final String KESTREL_LOG_LABEL = (String)"";
	/**
	 * @return A log meta<br>
	 * Original signature : <code>kestrel_log_meta_t kestrel_log_meta_make(kestrel_log_level_e, const char*, int32_t, const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_log.h:136</i>
	 */
	kestrel_log_meta_t.ByValue kestrel_log_meta_make(int level, String label, int line_no, String filename, String funcname);
	/**
	 * @return Log message length, including EOS character<br>
	 * Original signature : <code>int32_t kestrel_log_default(kestrel_log_meta_t, const char*, va_list)</code><br>
	 * <i>native declaration : include/kestrel_log.h:244</i>
	 */
	int kestrel_log_default(kestrel_log_meta_t.ByValue meta, String fmt, Object... vl);
	/**
	 * Original signature : <code>void kestrel_log_reset_callback()</code><br>
	 * <i>native declaration : include/kestrel_log.h:256</i>
	 */
	void kestrel_log_reset_callback();
	/**
	 * @param[in] level Output level, all log info that has higher level then it will output.<br>
	 * Original signature : <code>void kestrel_log_set_level(kestrel_log_level_e)</code><br>
	 * <i>native declaration : include/kestrel_log.h:261</i>
	 */
	void kestrel_log_set_level(int level);
	/**
	 * `KESTREL_LOG_CFG_DATE | KESTREL_LOG_CFG_TIME | KESTREL_LOG_CFG_LABEL | KESTREL_LOG_CFG_COLOR`<br>
	 * Original signature : <code>int32_t kestrel_log_get_config()</code><br>
	 * <i>native declaration : include/kestrel_log.h:268</i>
	 */
	int kestrel_log_get_config();
	/**
	 * @param[in] config Log config mixed by KESTREL_LOG_CFG_XXX.<br>
	 * Original signature : <code>void kestrel_log_set_config(int32_t)</code><br>
	 * <i>native declaration : include/kestrel_log.h:273</i>
	 */
	void kestrel_log_set_config(int config);
	/**
	 * Original signature : <code>void kestrel_log_reset_config()</code><br>
	 * <i>native declaration : include/kestrel_log.h:280</i>
	 */
	void kestrel_log_reset_config();
	/**
	 * @return Current log output level<br>
	 * Original signature : <code>kestrel_log_level_e kestrel_log_get_level()</code><br>
	 * <i>native declaration : include/kestrel_log.h:285</i>
	 */
	int kestrel_log_get_level();
	/**
	 * @return Log message length, including EOS character<br>
	 * Original signature : <code>int32_t kestrel_log(kestrel_log_meta_t, const char*, null)</code><br>
	 * <i>native declaration : include/kestrel_log.h:295</i>
	 */
	int kestrel_log(kestrel_log_meta_t.ByValue meta, String fmt, Object... varArgs1);
	/**
	 * @return Log message length, including EOS character<br>
	 * Original signature : <code>int32_t kestrel_log_va(kestrel_log_meta_t, const char*, va_list)</code><br>
	 * <i>native declaration : include/kestrel_log.h:305</i>
	 */
	int kestrel_log_va(kestrel_log_meta_t.ByValue meta, String fmt, Object... vl);
	/**
	 * @param[in] tag<br>
	 * Original signature : <code>int32_t kestrel_log_set_tag(const char*)</code><br>
	 * <i>native declaration : include/kestrel_log.h:310</i>
	 */
	int kestrel_log_set_tag(String tag);
}
