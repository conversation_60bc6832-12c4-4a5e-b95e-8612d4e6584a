package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_plugin</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */
public interface Kestrel_pluginLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_pluginLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_pluginLibrary INSTANCE = (Kestrel_pluginLibrary)Native.load(Kestrel_pluginLibrary.JNA_LIBRARY_NAME, Kestrel_pluginLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_plugin.h</i><br>
	 * enum values
	 */
	public static interface kestrel_ctrl_type_e {
		/** <i>native declaration : include/kestrel_plugin.h:40</i> */
		public static final int KESTREL_CTRL_TYPE_INVALID = 0;
		/** <i>native declaration : include/kestrel_plugin.h:41</i> */
		public static final int KESTREL_CTRL_TYPE_REQUEST = 1;
		/** <i>native declaration : include/kestrel_plugin.h:42</i> */
		public static final int KESTREL_CTRL_TYPE_NOTIFY = 2;
	};
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KESTREL_E_PLUGIN = (int)0x1;
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KPLUGIN_OK = (int)0;
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KPLUGIN_E_AGAIN = (int)(0x80000000 | ((0x4B) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0x0001) & 0xffff));
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KPLUGIN_E_EOF = (int)(0x80000000 | ((0x4B) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0x0002) & 0xffff));
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KPLUGIN_E_INTERNAL = (int)(0x80000000 | ((0x4B) & 0x7ff) << 20 | ((0x1) & 0xf) << 16 | ((0x0003) & 0xffff));
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KESTREL_UNKNOWN_PLUGIN_TYPE = (int)(-1);
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final int KESTREL_DEFAULT_PLUGIN_TYPE = (int)(0x40000002);
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final String PLUGIN_EVENT_PLUGIN_INSTALL = (String)"plugin_install";
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final String PLUGIN_EVENT_PLUGIN_UNINSTALL = (String)"plugin_uninstall";
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final String PLUGIN_EVENT_INSTANCE_CREATE = (String)"instance_create";
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final String PLUGIN_EVENT_INSTANCE_CONTROL = (String)"instance_control";
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final String PLUGIN_EVENT_INSTANCE_DESTROY = (String)"instance_destroy";
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final char PLUGIN_EVENT_TYPE_BEGIN = (char)'B';
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final char PLUGIN_EVENT_TYPE_END = (char)'E';
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final char PLUGIN_EVENT_SCOPE_GLOBAL = (char)'g';
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final char PLUGIN_EVENT_SCOPE_PROCESS = (char)'p';
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final char PLUGIN_EVENT_SCOPE_THREAD = (char)'t';
	/** <i>native declaration : include/kestrel_plugin.h</i> */
	public static final String PLUGIN_EVENT_CMD_NAME = (String)"plugin_event_listener";
	/**
	 * warning<br>
	 * Original signature : <code>int kestrel_plugin_setup_builtin(const kestrel_plugin_t*)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:159</i>
	 */
	int kestrel_plugin_setup_builtin(Pointer p);
	/**
	 * print a warning<br>
	 * Original signature : <code>kestrel_plugin_t* kestrel_plugin_load(const char*)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:167</i>
	 */
	Pointer kestrel_plugin_load(String file);
	/**
	 * @return Info structure of found plug-in, or NULL for end<br>
	 * Original signature : <code>kestrel_plugin_t* kestrel_plugin_find(const char*)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:173</i>
	 */
	Pointer kestrel_plugin_find(String name);
	/**
	 * @return Info structure of next plug-in, or NULL for end<br>
	 * Original signature : <code>kestrel_plugin_t* kestrel_plugin_get_next(const kestrel_plugin_t*)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:179</i>
	 */
	Pointer kestrel_plugin_get_next(Pointer plugin);
	/**
	 * @return Version string for succeed, NULL for error<br>
	 * Original signature : <code>char* kestrel_plugin_version(const kestrel_plugin_t*)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:185</i>
	 */
	String kestrel_plugin_version(Pointer plugin);
	/**
	 * @return Revision string for succeed, NULL for error<br>
	 * Original signature : <code>char* kestrel_plugin_revision(const kestrel_plugin_t*)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:191</i>
	 */
	String kestrel_plugin_revision(Pointer plugin);
	/**
	 * @return An instance handle, NULL if failed<br>
	 * Original signature : <code>kestrel_plugin_instance kestrel_plugin_create(const kestrel_plugin_t*, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:198</i>
	 */
	Pointer kestrel_plugin_create(Pointer plugin, Pointer cfg);
	/**
	 * @return An instance handle, NULL if failed<br>
	 * Original signature : <code>kestrel_plugin_instance kestrel_plugin_create_by_name(const char*, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:205</i>
	 */
	Pointer kestrel_plugin_create_by_name(String name, Pointer cfg);
	/**
	 * @note referenced plugin instance also should be freed using `kestrel_plugin_destroy()`.<br>
	 * Original signature : <code>kestrel_plugin_instance kestrel_plugin_ref(kestrel_plugin_instance)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:212</i>
	 */
	Pointer kestrel_plugin_ref(Pointer ins);
	/**
	 * @return An response BSON, in format of KEPI<br>
	 * Original signature : <code>kestrel_bson kestrel_plugin_control(kestrel_plugin_instance, kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:219</i>
	 */
	Pointer kestrel_plugin_control(Pointer instance, Pointer in);
	/**
	 * @param[in] instance Instance of plugin<br>
	 * Original signature : <code>void kestrel_plugin_destroy(kestrel_plugin_instance*)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:224</i>
	 */
	void kestrel_plugin_destroy(PointerByReference instance);
	/**
	 * @note Only allowed call on primary thread<br>
	 * Original signature : <code>void kestrel_plugin_unload(kestrel_plugin_t*)</code><br>
	 * <i>native declaration : include/kestrel_plugin.h:230</i>
	 */
	void kestrel_plugin_unload(Pointer plugin);
}
