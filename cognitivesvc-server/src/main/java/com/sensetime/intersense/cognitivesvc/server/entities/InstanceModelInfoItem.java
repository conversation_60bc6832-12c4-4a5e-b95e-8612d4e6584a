package com.sensetime.intersense.cognitivesvc.server.entities;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@Schema(title = "模型监控info", description = "模型监控info")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstanceModelInfoItem {
    private String runningPod;
    private String status;
    private List<String> runningDeviceIdList;
    private Map<String, Object> monitor;
}
