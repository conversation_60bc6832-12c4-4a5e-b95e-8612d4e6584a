package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class count_roi_result_list_t extends Structure {
	/**
	 * roi信息列表<br>
	 * C type : count_roi_result_t*
	 */
	public Pointer roi_results;
	/** roi数量 */
	public int roi_cnt;
	public count_roi_result_list_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("roi_results", "roi_cnt");
	}
	/**
	 * @param roi_results roi信息列表<br>
	 * C type : count_roi_result_t*<br>
	 * @param roi_cnt roi数量
	 */
	public count_roi_result_list_t(Pointer roi_results, int roi_cnt) {
		super();
		this.roi_results = roi_results;
		this.roi_cnt = roi_cnt;
	}
	public count_roi_result_list_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends count_roi_result_list_t implements Structure.ByReference {
		
	};
	public static class ByValue extends count_roi_result_list_t implements Structure.ByValue {
		
	};
}