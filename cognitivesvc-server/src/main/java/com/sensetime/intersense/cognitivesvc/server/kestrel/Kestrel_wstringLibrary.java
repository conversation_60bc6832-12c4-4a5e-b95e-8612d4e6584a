package com.sensetime.intersense.cognitivesvc.server.kestrel;


import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.ShortByReference;
import java.nio.ByteBuffer;
import java.nio.ShortBuffer;
/**
 * JNA Wrapper for library <b>kestrel_wstring</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_wstringLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_wstringLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_wstringLibrary INSTANCE = (Kestrel_wstringLibrary)Native.load(Kestrel_wstringLibrary.JNA_LIBRARY_NAME, Kestrel_wstringLibrary.class);
	/**
	 * @return[in] the number of units in wstring<br>
	 * Original signature : <code>size_t kestrel_wstring_strlen(const kestrel_wchar_t*)</code><br>
	 * <i>native declaration : include/kestrel_wstring.h:21</i>
	 */
	long kestrel_wstring_strlen(ShortBuffer wstring);
	/**
	 * @return returns output buffer on success or NULL on failure<br>
	 * Original signature : <code>uint8_t* kestrel_wsting_to_u8(const kestrel_wchar_t*, size_t, uint8_t*, size_t*)</code><br>
	 * <i>native declaration : include/kestrel_wstring.h:31</i>
	 */
	Pointer kestrel_wsting_to_u8(ShortBuffer wstring, long wstring_len, ByteBuffer u8, LongByReference u8_len);
	/**
	 * @return returns output buffer on success or NULL on failure<br>
	 * Original signature : <code>kestrel_wchar_t* kestrel_u8_to_wstring(const uint8_t*, size_t, kestrel_wchar_t*, size_t*)</code><br>
	 * <i>native declaration : include/kestrel_wstring.h:42</i>
	 */
	ShortByReference kestrel_u8_to_wstring(byte u8[], long u8_len, ShortBuffer wstring, LongByReference wstring_len);
}
