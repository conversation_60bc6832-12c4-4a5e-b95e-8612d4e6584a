package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;

/**
 * JNA Wrapper for library <b>kestrel_core_env</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_core_envLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_core_envLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_core_envLibrary INSTANCE = (Kestrel_core_envLibrary)Native.load(Kestrel_core_envLibrary.JNA_LIBRARY_NAME, Kestrel_core_envLibrary.class);
	/**
	 * Original signature : <code>int kestrel_env_init(const char*)</code><br>
	 * <i>native declaration : include/kestrel_core_env.h:7</i>
	 */
	int kestrel_env_init(String product);
	/**
	 * Invoke it with `NULL` to reset it.<br>
	 * Original signature : <code>int kestrel_env_workdir(const char*)</code><br>
	 * <i>native declaration : include/kestrel_core_env.h:12</i>
	 */
	int kestrel_env_workdir(String workdir);
	/**
	 * Original signature : <code>char* kestrel_env_product_name()</code><br>
	 * <i>native declaration : include/kestrel_core_env.h:15</i>
	 */
	String kestrel_env_product_name();
	/**
	 * Original signature : <code>void kestrel_env_deinit()</code><br>
	 * <i>native declaration : include/kestrel_core_env.h:18</i>
	 */
	void kestrel_env_deinit();
}
