package com.sensetime.intersense.cognitivesvc.server.entities;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Entity
@Table(name = "${db.video_stream_infra.table.name:video_stream_infra}")
@Data
@Accessors(chain = true)
@Schema(title = "直接视频流设备", description = "直接视频流设备")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamInfra {

    @Id
    @Column(name = "device_id")
    @Schema(description = "设备id")
    private String deviceId;

    @Column(name = "device_tag")
    @Schema(description = "设备tag")
    private String deviceTag;

    @Column(name = "rtsp_source")
    @Schema(description = "视频rtsp源")
    private String rtspSource;

    @Column(name = "rtsp_mapped_source")
    @Schema(description = "映射成内部rtsp流")
    private String rtspMappedSource;

    @Column(name = "rtsp_width")
    @Schema(description = "视频width")
    private Integer rtspWidth;

    @Column(name = "rtsp_height")
    @Schema(description = "视频height")
    private Integer rtspHeight;

    @Column(name = "rtmp_destination")
    @Schema(description = "rtmp推源")
    private String rtmpDestination;

    @Column(name = "rtmp_on")
    @Schema(description = "是否播放rtmp")
    private String rtmpOn;

    @Column(name = "rtmp_option", columnDefinition = "varchar(256) DEFAULT 'tune:zerolatency,preset:ultrafast'")
    @Schema(description = "ffmpeg的option参数")
    private String rtmpOption;

    @Column(name = "decoder_format")
    @Schema(description = "解码类型(optional)")
    private String decoderFormat;

    @Column(name = "frame_max")
    @Schema(description = "该路视频最大检测帧数，达到数值后关闭流")
    private Integer frameMax;

    @Column(name = "frame_skip", columnDefinition = "int DEFAULT 3")
    @Schema(description = "检测1帧跳N帧")
    private Integer frameSkip;

    @Column(name = "frame_buffer")
    @Schema(description = "预留帧池大小")
    private Integer frameBuffer;

    @Column(name = "frame_buffer_strategy")
    @Schema(description = "预留帧池策略")
    private String frameBufferStrategy;

	@Column(name = "sts", columnDefinition = "int DEFAULT 0")
	@Schema(description = "设备状态")
	private Integer sts;

	@Column(name = "seed")
	@Schema(description = "种子")
	private String seed;

	@Column(name = "update_ts")
	@Schema(description = "最后活跃时间")
	private Date updateTs;

	@Column(name = "keep_alive")
	@Schema(description = "是否存活继续播放")
	private Date keepAlive;

	@Column(name = "video_rate")
	@Schema(description = "速率")
	private Integer videoRate;
	
	@Column(name = "privilege")
	@Schema(description = "权限组")
    private String privilege;

    @Column(name = "priority")
    @Schema(description = "优先级")
    private Integer priority;

    @Column(name = "processors", columnDefinition = "mediumblob")
    @Schema(description = "处理器配置，JSON格式，包含stream_multiplier等参数")
    private String processors;

    /**
     * Processors配置映射类
     */
    @Data
    public static class ProcessorsConfig {
        private Float stream_multiplier;
        private Integer useH265;
        // 可以根据需要添加其他字段
    }

    public static final String initSeed = "waiting to steal";
    
    public static final String nonRtspDoneSeed = "done";

	public String[] privileges() {
		return StringUtils.isNotBlank(privilege) ? privilege.split(",") : null;
	}
	
	public String realRtspSource() {
		return StringUtils.isBlank(rtspMappedSource) ? rtspSource : rtspMappedSource;
	}

    // 从processors解析stream_multiplier
    public Float getStreamMultiplier() {
        if (StringUtils.isBlank(processors)) {
            return 0.0f;
        }

        try {
            // 使用对象映射直接解析JSON
            ProcessorsConfig config = com.alibaba.fastjson.JSON.parseObject(processors, ProcessorsConfig.class);
            if (config != null && config.getStream_multiplier() != null) {
                return config.getStream_multiplier();
            }
        } catch (Exception e) {
            // 解析失败，返回默认值
        }

        return 0.0f;
    }

    public Integer getDeviceUseH265() {
        if (StringUtils.isBlank(processors)) {
            return -1;
        }

        try {
            // 使用对象映射直接解析JSON
            ProcessorsConfig config = com.alibaba.fastjson.JSON.parseObject(processors, ProcessorsConfig.class);
            if (config != null && config.getUseH265() != null) {
                return config.getUseH265();
            }
        } catch (Exception e) {
            // 解析失败，返回默认值
        }

        return -1;
    }
    
    /**
     * 计算设备占用的算力倍数
     * 以1920*1080为基准单位1.0
     * 如果设置了stream_multiplier，优先使用该值
     * 否则根据分辨率计算倍数: (width * height) / (1920 * 1080)
     * @return 算力倍数，最小为0.1
     */
    public float getComputePower() {
        // 1. 首先检查是否有stream_multiplier的配置
        Float multiplier = getStreamMultiplier();
        if (multiplier != null && multiplier > 0.0f) {
            return multiplier;
        }
        
        // 2. 如果没有stream_multiplier，根据分辨率计算
        if (rtspWidth != null && rtspHeight != null && rtspWidth > 0 && rtspHeight > 0) {
            // 基准分辨率1920*1080 = 2073600像素
            final int BASE_RESOLUTION = 1920 * 1080;
            int actualResolution = rtspWidth * rtspHeight;
            
            // 计算倍数 = 实际分辨率 / 基准分辨率
            float power = (float) actualResolution / BASE_RESOLUTION;
            
            // 设置一个最小值，避免算力倍数过小
            return Math.max(0.1f, power);
        }
        
        // 3. 无法计算时返回默认值1.0
        return 1.0f;
    }
}
