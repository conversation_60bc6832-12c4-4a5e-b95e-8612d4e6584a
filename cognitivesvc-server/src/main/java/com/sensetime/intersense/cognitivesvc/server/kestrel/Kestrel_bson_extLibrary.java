package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;

/**
 * J<PERSON> Wrapper for library <b>kestrel_bson_ext</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_bson_extLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_bson_extLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_bson_extLibrary INSTANCE = (Kestrel_bson_extLibrary)Native.load(Kestrel_bson_extLibrary.JNA_LIBRARY_NAME, Kestrel_bson_extLibrary.class);
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_INVALID = (int)(0);
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_POINTER = (int)64;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_INT8 = (int)65;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_INT16 = (int)66;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_INT32 = (int)67;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_INT64 = (int)68;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_UINT8 = (int)69;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_UINT16 = (int)70;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_UINT32 = (int)71;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_UINT64 = (int)72;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_FLOAT16 = (int)73;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_FLOAT32 = (int)74;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_FLOAT64 = (int)75;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_FEATURE = (int)1;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_BUFFER = (int)2;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_PACKET = (int)3;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_FRAME = (int)4;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_TENSOR = (int)5;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_MODEL = (int)6;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_PLUGIN = (int)7;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_POINT2I = (int)8;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_POINT2F = (int)9;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_SIZE2D = (int)10;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_AREA2D = (int)11;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_ARRAY = (int)12;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_WSTRING = (int)13;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_RANGE = (int)14;
	/** <i>native declaration : include/kestrel_bson_ext.h</i> */
	public static final int KESON_TENSOR_SHAPE = (int)15;
	/**
	 * subtype ::= "\x95"<br>
	 * data ::= ele_type int32<dims> (int32*dims)<br>
	 * json ::= {"base64": "data", "subType": "95",<br>
	 *           "$readable": {<br>
	 *               "$kesonType": "tensor_shape",<br>
	 *               "elemType": "KESTREL_UINT8",<br>
	 *               "dims": [1, 3, 224, 224]<br>
	 *           }<br>
	 *          }<br>
	 * Original signature : <code>Pointer kestrel_bson_create_ext_data(uint8_t, void*)</code><br>
	 * <i>native declaration : include/kestrel_bson_ext.h:273</i>
	 */
	Pointer kestrel_bson_create_ext_data(byte type, Pointer data);
	/**
	 * Original signature : <code>Pointer kestrel_bson_set_ext_data(kestrel_bson, uint8_t, void*)</code><br>
	 * <i>native declaration : include/kestrel_bson_ext.h:276</i>
	 */
	Pointer kestrel_bson_set_ext_data(Pointer n, byte subtype, Pointer data);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_is_ext_data(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson_ext.h:279</i>
	 */
	int kestrel_bson_is_ext_data(Pointer node);
	/**
	 * Original signature : <code>uint8_t kestrel_bson_get_ext_type(kestrel_bson)</code><br>
	 * <i>native declaration : include/kestrel_bson_ext.h:282</i>
	 */
	byte kestrel_bson_get_ext_type(Pointer node);
	/**
	 * Original signature : <code>kestrel_bool kestrel_bson_get_ext_data(kestrel_bson, void**)</code><br>
	 * <i>native declaration : include/kestrel_bson_ext.h:285</i>
	 */
	int kestrel_bson_get_ext_data(Pointer node, PointerByReference data);
}
