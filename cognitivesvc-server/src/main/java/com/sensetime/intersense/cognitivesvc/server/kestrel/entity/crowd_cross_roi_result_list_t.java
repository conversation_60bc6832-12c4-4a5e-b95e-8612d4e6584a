package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_cross_roi_result_list_t extends Structure {
	/**
	 * < 跨线roi结果数组<br>
	 * C type : crowd_cross_roi_result_t*
	 */
	public Pointer cross_roi_results;
	/** < 跨线roi个数 */
	public int cross_roi_cnt;
	public crowd_cross_roi_result_list_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("cross_roi_results", "cross_roi_cnt");
	}
	/**
	 * @param cross_roi_results < 跨线roi结果数组<br>
	 * C type : crowd_cross_roi_result_t*<br>
	 * @param cross_roi_cnt < 跨线roi个数
	 */
	public crowd_cross_roi_result_list_t(Pointer cross_roi_results, int cross_roi_cnt) {
		super();
		this.cross_roi_results = cross_roi_results;
		this.cross_roi_cnt = cross_roi_cnt;
	}
	public crowd_cross_roi_result_list_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_cross_roi_result_list_t implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_cross_roi_result_list_t implements Structure.ByValue {
		
	};
}