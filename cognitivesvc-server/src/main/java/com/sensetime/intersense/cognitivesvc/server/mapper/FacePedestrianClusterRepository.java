package com.sensetime.intersense.cognitivesvc.server.mapper;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.sensetime.intersense.cognitivesvc.server.entities.FacePedestrianCluster;
@Repository
public interface FacePedestrianClusterRepository extends JpaRepositoryImplementation<FacePedestrianCluster, Integer>{

	public List<FacePedestrianCluster> findByFacePersonId(String facePersonId);

	public List<FacePedestrianCluster> findByPedestrianPersonId(String pedestrianPersonId);
	
	public List<FacePedestrianCluster> findByFacePersonIdAndPedestrianPersonId(String facePersonId, String pedestrianPersonId);

	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByFacePersonIdAndPedestrianPersonId(String personId, String pedestrianPersonId);

	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByFacePersonIdIn(List<String> PersonIds);

	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByPedestrianPersonIdIn(List<String> pedestrianPersonIds);
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByFacePersonType(Integer facePersonType);
	
	@Transactional
	@Modifying(flushAutomatically = true, clearAutomatically = true)
	public Integer deleteByPedestrianPersonType(Integer pedestrianPersonType);
}
