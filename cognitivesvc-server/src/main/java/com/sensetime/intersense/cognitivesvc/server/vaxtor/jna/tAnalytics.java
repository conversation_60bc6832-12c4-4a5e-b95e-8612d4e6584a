package com.sensetime.intersense.cognitivesvc.server.vaxtor.jna;

import com.sun.jna.NativeLong;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : header/vaxtor_types.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class tAnalytics extends Structure {
    /**
     * 0:unknown, 1:getting closer, 2:getting farther, 3:stopped<br>
     * C type : vx_int32
     */
    public NativeLong _vehicle_direction;
    /** Vehicle instant speed in Km/h */
    public double _vehicle_speed;
    /**
     * MMC: make<br>
     * C type : char[128]
     */
    public byte[] _vehicle_make = new byte[128];
    /**
     * MMC: model<br>
     * C type : char[128]
     */
    public byte[] _vehicle_model = new byte[128];
    /**
     * MMC: color<br>
     * C type : char[128]
     */
    public byte[] _vehicle_color = new byte[128];
    /**
     * Vehicle class<br>
     * C type : char[128]
     */
    public byte[] _vehicle_class = new byte[128];
    /** MMC analytic processing time in ms */
    public double _mmc_proc_time;
    /**
     * 0:unknown, 1:enter, 2:exit, 3:overstay, 4:lost<br>
     * C type : vx_int32
     */
    public NativeLong _vehicle_access;
    /**
     * Vehicle access dwell time in seconds<br>
     * C type : vx_int32
     */
    public NativeLong _dwell_time;
    /**
     * 0:unknown, 1:frontal, 2:rear<br>
     * C type : vx_int32
     */
    public NativeLong _vehicle_pose;
    /**
     * The plate number matchess the grammar of the country origin, 1:yes, 0:no<br>
     * C type : vx_int32
     */
    public NativeLong _grammar_strict_match;
    /**
     * The plate number has been read inside more than 1 ROI, 1:yes, 0:no<br>
     * C type : vx_int32
     */
    public NativeLong _roi_crossing;
    public tAnalytics() {
        super();
    }
    protected List<String> getFieldOrder() {
        return Arrays.asList("_vehicle_direction", "_vehicle_speed", "_vehicle_make", "_vehicle_model", "_vehicle_color", "_vehicle_class", "_mmc_proc_time", "_vehicle_access", "_dwell_time", "_vehicle_pose", "_grammar_strict_match", "_roi_crossing");
    }
    public tAnalytics(Pointer peer) {
        super(peer);
    }
    public static class ByReference extends tAnalytics implements Structure.ByReference {

    };
    public static class ByValue extends tAnalytics implements Structure.ByValue {

    };

}
