package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_annotator</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">J<PERSON></a>.
 */
public interface Kestrel_annotatorLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_annotatorLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_annotatorLibrary INSTANCE = (Kestrel_annotatorLibrary)Native.load(Kestrel_annotatorLibrary.JNA_LIBRARY_NAME, Kestrel_annotatorLibrary.class);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_ANNOTATOR_PLUGIN)<br>
	 * Original signature : <code>char* kestrel_annotator_get_config_schema(kestrel_plugin_t*)</code><br>
	 * <i>native declaration : include/kestrel_annotator.h:14</i>
	 */
	String kestrel_annotator_get_config_schema(Pointer plugin);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_ANNOTATOR_PLUGIN)<br>
	 * Original signature : <code>char* kestrel_annotator_get_params_schema(kestrel_plugin_t*)</code><br>
	 * <i>native declaration : include/kestrel_annotator.h:21</i>
	 */
	String kestrel_annotator_get_params_schema(Pointer plugin);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_ANNOTATOR_PLUGIN)<br>
	 * Original signature : <code>char* kestrel_annotator_get_result_schema(kestrel_plugin_t*)</code><br>
	 * <i>native declaration : include/kestrel_annotator.h:28</i>
	 */
	String kestrel_annotator_get_result_schema(Pointer plugin);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_ANNOTATOR_PLUGIN)<br>
	 * Original signature : <code>int kestrel_annotator_startup(kestrel_plugin_instance, kestrel_bson, kestrel_bson*)</code><br>
	 * <i>native declaration : include/kestrel_annotator.h:37</i>
	 */
	int kestrel_annotator_startup(Pointer annotator, Pointer in, PointerByReference out);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_ANNOTATOR_PLUGIN)<br>
	 * Original signature : <code>int kestrel_annotator_process(kestrel_plugin_instance, kestrel_bson, kestrel_bson*)</code><br>
	 * <i>native declaration : include/kestrel_annotator.h:47</i>
	 */
	int kestrel_annotator_process(Pointer annotator, Pointer in, PointerByReference out);
	/**
	 * @note ensure (nn->plugin_api->type == KESTREL_ANNOTATOR_PLUGIN)<br>
	 * Original signature : <code>int kestrel_annotator_terminate(kestrel_plugin_instance, kestrel_bson, kestrel_bson*)</code><br>
	 * <i>native declaration : include/kestrel_annotator.h:57</i>
	 */
	int kestrel_annotator_terminate(Pointer annotator, Pointer in, PointerByReference out);
}
