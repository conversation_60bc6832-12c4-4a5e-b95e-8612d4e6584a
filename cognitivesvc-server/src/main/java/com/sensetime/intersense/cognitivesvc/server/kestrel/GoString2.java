package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Structure;

import java.util.ArrayList;
import java.util.List;

public  class GoString2 extends Structure {

    public String str;
    public long length;

    public GoString2() {
    }

    public GoString2(String str) {
        this.str = str;
        this.length = str.length();
    }

    @Override
    protected List<String> getFieldOrder() {
        List<String> fields = new ArrayList<>();
        fields.add("str");
        fields.add("length");
        return fields;
    }

    public static class ByValue extends GoString2 implements Structure.ByValue {
        public ByValue() {
        }

        public ByValue(String str) {
            super(str);
        }
    }

    public static class ByReference extends GoString2 implements Structure.ByReference {
        public ByReference() {
        }

        public ByReference(String str) {
            super(str);
        }
    }
}