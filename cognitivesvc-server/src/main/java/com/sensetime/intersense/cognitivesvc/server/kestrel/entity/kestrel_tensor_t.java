package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;

import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_tensor.c</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_tensor_t extends Structure {
        /** C type : char[(63) + 1] */
        public byte[] name = new byte[(63) + 1];
        /** C type : kestrel_tensor_meta_t */
        public kestrel_tensor_meta_t  meta;
        /** C type : uint8_t* */
        public Pointer data;
        /** C type : kestrel_buffer */
        public Pointer buffer;
        
        public kestrel_tensor_t() {
                super();
        }
        
        protected List<String> getFieldOrder() {
                return Arrays.asList("name", "meta", "data", "buffer");
        }
        /**
         * @param name C type : char[(63) + 1]<br>
         * @param meta C type : kestrel_tensor_meta_t<br>
         * @param data C type : uint8_t*<br>
         * @param buffer C type : kestrel_buffer
         */
        public kestrel_tensor_t(byte name[], kestrel_tensor_meta_t meta, Pointer data, Pointer buffer) {
                super();
                if ((name.length != this.name.length))
                        throw new IllegalArgumentException("Wrong array size !");
                this.name = name;
                this.meta = meta;
                this.data = data;
                this.buffer = buffer;
        }
        public kestrel_tensor_t(Pointer peer) {
                super(peer);
        }
        public static class ByReference extends kestrel_tensor_t implements Structure.ByReference {

        };
        public static class ByValue extends kestrel_tensor_t implements Structure.ByValue {

        };
}
