package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Callback;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.PointerType;
import com.sun.jna.ptr.IntByReference;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
/**
 * JNA Wrapper for library <b>kestrel_os_compat</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_os_compatLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_os_compat"; 
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_os_compatLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_os_compatLibrary INSTANCE = (Kestrel_os_compatLibrary)Native.load(Kestrel_os_compatLibrary.JNA_LIBRARY_NAME, Kestrel_os_compatLibrary.class);
	/** <i>native declaration : include/kestrel_os_compat.h:21</i> */
	public interface kestrel_thread_create_job_callback extends Callback {
		Pointer apply(Pointer voidPtr1);
	};
	/** <i>native declaration : include/kestrel_os_compat.h:36</i> */
	public interface kestrel_thread_key_create_destructor_callback extends Callback {
		void apply(Pointer voidPtr1);
	};
	/**
	 * @{<br>
	 * Original signature : <code>int kestrel_thread_create(kestrel_thread_t*, kestrel_thread_create_job_callback*, void*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:20</i>
	 */
	int kestrel_thread_create(LongBuffer tid, Kestrel_os_compatLibrary.kestrel_thread_create_job_callback job, Pointer arg);
	/**
	 * Original signature : <code>int kestrel_thread_join(kestrel_thread_t)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:23</i>
	 */
	int kestrel_thread_join(long tid);
	/**
	 * Original signature : <code>size_t kestrel_thread_id()</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:26</i>
	 */
	long kestrel_thread_id();
	/**
	 * Original signature : <code>kestrel_thread_t kestrel_thread_self()</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:29</i>
	 */
	long kestrel_thread_self();
	/**
	 * Original signature : <code>int kestrel_thread_equal(kestrel_thread_t, kestrel_thread_t)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:32</i>
	 */
	int kestrel_thread_equal(long t1, long t2);
	/**
	 * Original signature : <code>int kestrel_thread_key_create(kestrel_thread_key_t*, kestrel_thread_key_create_destructor_callback*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:35</i><br>
	 * @deprecated use the safer methods {@link #kestrel_thread_key_create(java.nio.IntBuffer, kestrel_os_compat.Kestrel_os_compatLibrary.kestrel_thread_key_create_destructor_callback)} and {@link #kestrel_thread_key_create(com.sun.jna.ptr.IntByReference, kestrel_os_compat.Kestrel_os_compatLibrary.kestrel_thread_key_create_destructor_callback)} instead
	 */
	@Deprecated 
	int kestrel_thread_key_create(IntByReference key, Kestrel_os_compatLibrary.kestrel_thread_key_create_destructor_callback destructor);
	/**
	 * Original signature : <code>int kestrel_thread_key_create(kestrel_thread_key_t*, kestrel_thread_key_create_destructor_callback*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:35</i>
	 */
	int kestrel_thread_key_create(IntBuffer key, Kestrel_os_compatLibrary.kestrel_thread_key_create_destructor_callback destructor);
	/**
	 * Original signature : <code>int kestrel_thread_key_delete(kestrel_thread_key_t)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:38</i>
	 */
	int kestrel_thread_key_delete(int key);
	/**
	 * Original signature : <code>int kestrel_thread_setspecific(kestrel_thread_key_t, const void*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:41</i>
	 */
	int kestrel_thread_setspecific(int key, Pointer value);
	/**
	 * Original signature : <code>void* kestrel_thread_getspecific(kestrel_thread_key_t)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:44</i>
	 */
	Pointer kestrel_thread_getspecific(int key);
	/**
	 * Original signature : <code>int kestrel_thread_mutex_init(kestrel_thread_mutex_t*, const kestrel_thread_mutexattr_t*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:47</i>
	 */
	int kestrel_thread_mutex_init(Pointer mutex, Pointer attr);
	/**
	 * Original signature : <code>int kestrel_thread_mutex_destroy(kestrel_thread_mutex_t*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:50</i>
	 */
	int kestrel_thread_mutex_destroy(Pointer mutex);
	/**
	 * Original signature : <code>int kestrel_thread_mutex_lock(kestrel_thread_mutex_t*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:53</i>
	 */
	int kestrel_thread_mutex_lock(Pointer mutex);
	/**
	 * Original signature : <code>int kestrel_thread_mutex_unlock(kestrel_thread_mutex_t*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:56</i>
	 */
	int kestrel_thread_mutex_unlock(Pointer mutex);
	/**
	 * Original signature : <code>void kestrel_clock_nanosec(timespec*)</code><br>
	 * <i>native declaration : include/kestrel_os_compat.h:59</i>
	 */
	void kestrel_clock_nanosec(Kestrel_os_compatLibrary.timespec ts);
	public static class timespec extends PointerType {
		public timespec(Pointer address) {
			super(address);
		}
		public timespec() {
			super();
		}
	};
}
