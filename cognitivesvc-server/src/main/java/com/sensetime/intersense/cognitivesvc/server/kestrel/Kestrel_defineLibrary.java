package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
/**
 * JNA Wrapper for library <b>kestrel_define</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_defineLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_defineLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_defineLibrary INSTANCE = (Kestrel_defineLibrary)Native.load(Kestrel_defineLibrary.JNA_LIBRARY_NAME, Kestrel_defineLibrary.class);
	/** <i>native declaration : include/kestrel_define.h</i> */
	public static final int KESTREL_OS_WINDOWS = (int)1;
	/** <i>native declaration : include/kestrel_define.h</i> */
	public static final int KESTREL_OS_WINDOWS_DESKTOP = (int)1;
	/** <i>native declaration : include/kestrel_define.h</i> */
	public static final int KESTREL_TRUE = (int)1;
	/** <i>native declaration : include/kestrel_define.h</i> */
	public static final int KESTREL_FALSE = (int)0;
}
