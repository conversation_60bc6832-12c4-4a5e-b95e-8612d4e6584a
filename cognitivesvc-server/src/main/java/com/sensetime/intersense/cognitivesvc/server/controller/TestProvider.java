package com.sensetime.intersense.cognitivesvc.server.controller;

import com.sensetime.intersense.cognitivesvc.server.feign.MemberFeign;
import com.sensetime.intersense.cognitivesvc.server.kestrel.utils.KestrelApi;
import com.sensetime.intersense.cognitivesvc.server.utils.FrameUtils;
import com.sensetime.intersense.cognitivesvc.server.utils.VideoStream.VideoFrame;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sun.jna.Pointer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@RestController(value = "serverTestProvider")
@RequestMapping(value = "/test", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "TestProvider", description = "Test")
@Slf4j
public class TestProvider extends BaseProvider {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private MemberFeign memberFeign;
    @Value("${server.port}")
    private String port;
    @Value("${spring.application.name}")
    private String applicationName;

    @Operation(summary = "runSql", method = "POST")
    @RequestMapping(value = "/runSql", method = RequestMethod.POST)
    public Object runSql(@RequestParam String sql) throws Exception {
        try (Connection conn = dataSource.getConnection()) {
            try (Statement stat = conn.createStatement()) {
                if (sql.toLowerCase().indexOf("select") > -1) {
                    try (ResultSet set = stat.executeQuery(sql)) {
                        StringBuilder builder = new StringBuilder();

                        while (set.next()) {
                            String result = IntStream.range(1, 50).mapToObj(index -> {
                                        try {
                                            return set.getObject(index);
                                        } catch (SQLException e) {
                                            log.warn(e.getLocalizedMessage());
                                            return 0;
                                        }
                                    })
                                    .filter(Objects::nonNull)
                                    .map(Object::toString)
                                    .collect(Collectors.joining(",,,"));

                            builder.append(result);
                            builder.append("\n");
                        }

                        return builder.toString();
                    }
                } else
                    return stat.executeUpdate(sql);
            }
        }
    }

    @Operation(summary = "test frame source", method = "GET")
    @RequestMapping(value = "/test/frame/source", method = RequestMethod.GET)
    public void testFrameSource(@RequestParam String image_path) {
        VideoFrame vf = FrameUtils.decode_up_down_load_frame_path(image_path);

        Pointer f0 = vf.getGpuFrame();

        Pointer f1 = FrameUtils.ref_frame(f0);
        Pointer f2 = FrameUtils.roi_frame(f0, 0, 0, 100, 100);
        Pointer f3 = FrameUtils.ref_frame(f2);
        Pointer f4 = FrameUtils.roi_frame(f3, 0, 0, 10, 10);

        System.out.println(KestrelApi.kestrel_frame_is_same_source(f0, f1));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f0, f2));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f0, f3));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f0, f4));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f1, f2));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f1, f3));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f1, f4));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f2, f3));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f2, f4));
        System.out.println(KestrelApi.kestrel_frame_is_same_source(f3, f4));

        FrameUtils.batch_free_frame(vf);
    }

    @Operation(summary = "exit", method = "GET")
    @RequestMapping(value = "/exit", method = RequestMethod.GET)
    public void testExit() {
        System.out.println(">>> [text exit] Forcibly Terminating JVM");
        Runtime.getRuntime().halt(0);
    }

    @Operation(summary = "groupIdsByperson", method = "GET")
    @RequestMapping(value = "/groupIdsByperson", method = RequestMethod.GET)
    public BaseRes<List<String>> getGroupIdsByperson(@RequestParam(value = "uid") String uid) {

        BaseRes<MemberFeign.GroupIdsRes> result = memberFeign.queryPersonGroupIds("*", "*", uid);
        return BaseRes.success(result.getData().getPersonGroupIds());
    }


}
