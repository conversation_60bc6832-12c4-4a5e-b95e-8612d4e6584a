package com.sensetime.intersense.cognitivesvc.server.utils;

import java.io.File;
import java.net.URLEncoder;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import lombok.SneakyThrows;

public class DarwinUtils {
	
	@SneakyThrows
	public static synchronized String addMappedRtsp(String ip, String src, String dst){
		if(!existDarwin())
			startDarwin();
		
		String url = "http://" + ip + ":" + httpPort(Utils.instance.cogPort) + "/api/v1/stream/start?";
		url += "url=" + URLEncoder.encode(src, "UTF-8") + "&customPath=" + dst + "&transType=TCP&idleTimeout=60&heartbeatInterval=20";
		return RestUtils.get(url).replaceAll("\"", "");
	}
	
	@SneakyThrows
	public static synchronized String deleteMappedRtsp(String ip, String id) {	
		if(!existDarwin())
			return StringUtils.EMPTY;
		
		String url = "http://" + ip + ":" + httpPort(Utils.instance.cogPort) + "/api/v1/stream/stop?id=" + id;
		String result = RestUtils.get(url).replaceAll("\"", "");
		
		JSONArray mappeds = getMappedRtsp(ip);
		if(mappeds.isEmpty())
			stopDarwin();
		
		return result;
	}
	
	@SneakyThrows
	public static synchronized JSONArray getMappedRtsp(String ip) {
		if(!existDarwin())
			return new JSONArray();
		
		if(memoryDarwin() > 2 * 1024 * 1024) {//检查一下内存 如果超过2048mb就杀掉重启
			stopDarwin();
			startDarwin();
		}
		
		String url = "http://" + ip + ":" + httpPort(Utils.instance.cogPort) + "/api/v1/pushers";
		JSONObject get = JSON.parseObject(RestUtils.get(url));
		return (JSONArray)get.get("rows");
	}
	
	public static int rtspPort(int cogPort) { return cogPort + 10100; }
	
	public static int httpPort(int cogPort) { return cogPort + 10101; }

	private static synchronized void startDarwin() {
		File exist = new File("/usr/cognitivesvc/easyDarwin");
		if(!exist.exists()) {			
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "cp -rf /kestrel/other/easyDarwin/ /usr/cognitivesvc/"});
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "chmod -R 777 /usr/cognitivesvc/easyDarwin/"});
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "sed -i 's#88888#" + rtspPort(Utils.instance.cogPort) + "#' /usr/cognitivesvc/easyDarwin/easydarwin.ini"});
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "sed -i 's#99999#" + httpPort(Utils.instance.cogPort) + "#' /usr/cognitivesvc/easyDarwin/easydarwin.ini"});
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "/usr/cognitivesvc/easyDarwin/easydarwin install"});
		}
		
		String process = HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ps -ef | grep easydarwin | grep -v grep"});
		if(StringUtils.isBlank(process)) {
			HostUtils.runLinux(new String[] {"/bin/sh", "-c", "/usr/cognitivesvc/easyDarwin/easydarwin start"});
		}
	}

	private static synchronized boolean existDarwin() {		
		String process = HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ps -ef | grep easydarwin | grep -v grep"});
		return StringUtils.isNotBlank(process);
	}
	
	private static synchronized void stopDarwin() {		
		HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ps -ef | grep easydarwin | grep -v grep  | awk '{print $2}' | xargs kill -9"});
		HostUtils.runLinux(new String[] {"/bin/sh", "-c", "rm -rf /usr/cognitivesvc/easyDarwin"});
	}
	
	private static synchronized int memoryDarwin() {
		String text = HostUtils.runLinux(new String[] {"/bin/sh", "-c", "ps -aux | grep easydarwin | grep -v grep | awk '{print $6}';"});
		try {
			return Integer.parseInt(text.substring(0, text.length() - 1));
		}catch(Exception e) {
			return -1;
		}
	}
}
