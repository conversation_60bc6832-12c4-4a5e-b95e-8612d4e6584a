package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Callback;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.ptr.PointerByReference;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_nn_def.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_nn_api_t extends Structure {
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback extends Callback {
		int apply(Pointer nn, Pointer tensor_name);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback2 extends Callback {
		int apply(Pointer nn);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback3 extends Callback {
		int apply(Pointer nn, Pointer param);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback4 extends Callback {
		int apply(Pointer nn, Pointer name, kestrel_tensor_meta_t tensor_info);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback5 extends Callback {
		int apply(Pointer nn, Pointer name, kestrel_tensor_meta_t in);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback6 extends Callback {
		int apply(Pointer nn, Pointer name, Pointer tensor);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback7 extends Callback {
		int apply(Pointer nn, Pointer name, Pointer tensor);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback8 extends Callback {
		int apply(Pointer nn, kestrel_nn_properties_t prop);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback9 extends Callback {
		int apply(Pointer nn);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback10 extends Callback {
		int apply(Pointer nn, PointerByReference e);
	};
	/** <i>native declaration : include/kestrel_nn_def.h</i> */
	public interface k_err_callback11 extends Callback {
		int apply(Pointer nn, Pointer e);
	};
	public kestrel_nn_api_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList();
	}
	public kestrel_nn_api_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_nn_api_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_nn_api_t implements Structure.ByValue {
		
	};
}
