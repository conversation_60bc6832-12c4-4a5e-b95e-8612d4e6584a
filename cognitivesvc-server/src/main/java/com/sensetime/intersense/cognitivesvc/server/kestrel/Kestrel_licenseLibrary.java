package com.sensetime.intersense.cognitivesvc.server.kestrel;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.LongByReference;
import com.sun.jna.ptr.PointerByReference;
import java.nio.ByteBuffer;
import java.nio.IntBuffer;
/**
 * JNA Wrapper for library <b>kestrel_license</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_licenseLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel_core";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_licenseLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_licenseLibrary INSTANCE = (Kestrel_licenseLibrary)Native.load(Kestrel_licenseLibrary.JNA_LIBRARY_NAME, Kestrel_licenseLibrary.class);
	/**
	 * <i>native declaration : include/kestrel_license.h</i><br>
	 * enum values
	 */
	public static interface kestrel_atsha204a_cmd_code_e {
		/** <i>native declaration : include/kestrel_license.h:36</i> */
		public static final int KESTREL_ATSHA204A_CMD_READ_SN = 0;
		/** <i>native declaration : include/kestrel_license.h:37</i> */
		public static final int KESTREL_ATSHA204A_CMD_READ_OTP = 1;
		/** <i>native declaration : include/kestrel_license.h:38</i> */
		public static final int KESTREL_ATSHA204A_CMD_NONCE = 2;
		/** <i>native declaration : include/kestrel_license.h:39</i> */
		public static final int KESTREL_ATSHA204A_CMD_MAC = 3;
		/** <i>native declaration : include/kestrel_license.h:40</i> */
		public static final int KESTREL_ATSHA204A_CMD_SLOT_ID = 4;
	};
	public static final byte KESTREL_BSON_BIN_GENERIC = 0x00;
	public static final byte KESTREL_BSON_BIN_FUNCTION = 0x01;
	public static final byte KESTREL_BSON_BIN_OLD_BINARY = 0x02;
	public static final byte KESTREL_BSON_BIN_OLD_UUID = 0x03;
	public static final byte KESTREL_BSON_BIN_UUID = 0x04;
	public static final byte KESTREL_BSON_BIN_MD5 = 0x05;
	public static final byte KESTREL_BSON_BIN_ENCRYPTED = 0x06;
	/** <i>native declaration : include/kestrel_license.h</i> */
	public static final int MAX_PRODUCT_VERSION_LENGTH = (int)100;
	/**
	 * Original signature : <code>int udid_default_getter(char*, size_t*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:19</i>
	 */
	int udid_default_getter(ByteBuffer udid, LongByReference buf_size);
	/**
	 * @param[in] getter UDID getter function<br>
	 * Original signature : <code>void kestrel_license_set_udid_getter(getter kestrel_udid_getter)</code><br>
	 * <i>native declaration : include/kestrel_license.h:24</i>
	 */
	void kestrel_license_set_udid_getter(Pointer kestrel_udid_getter1);
	/**
	 * @note Kestrel uses encrypted UDID getter function as default.<br>
	 * Original signature : <code>void kestrel_license_reset_udid_getter()</code><br>
	 * <i>native declaration : include/kestrel_license.h:29</i>
	 */
	void kestrel_license_reset_udid_getter();
	/**
	 * @return KESTREL_OK for succeed, other for error<br>
	 * Original signature : <code>int kestrel_license_add(const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:87</i>
	 */
	int kestrel_license_add(String license, String signed_code);
	/**
	 * @return KESTREL_OK for succeed, other for error<br>
	 * Original signature : <code>int kestrel_license_add_from_file(const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:94</i>
	 */
	int kestrel_license_add_from_file(String license_file, String signed_code);
	/**
	 * @return Zero terminated user ID string, should be freed using free()<br>
	 * Original signature : <code>char* kestrel_license_user_id()</code><br>
	 * <i>native declaration : include/kestrel_license.h:99</i>
	 */
	Pointer kestrel_license_user_id();
	/**
	 * @return Zero terminated UDID string, should be freed using free()<br>
	 * Original signature : <code>char* kestrel_license_get_udid()</code><br>
	 * <i>native declaration : include/kestrel_license.h:104</i>
	 */
	Pointer kestrel_license_get_udid();
	/**
	 * @return KESTREL_OK for succeed, other for error<br>
	 * Original signature : <code>int kestrel_license_get_product_version(char[100])</code><br>
	 * <i>native declaration : include/kestrel_license.h:120</i>
	 */
	int kestrel_license_get_product_version(ByteBuffer product_version);
	/**
	 * @brief Reset License product version getter function<br>
	 * Original signature : <code>void kestrel_license_reset_product_version_getter()</code><br>
	 * <i>native declaration : include/kestrel_license.h:129</i>
	 */
	void kestrel_license_reset_product_version_getter();
	/**
	 * @return KESTREL_OK for succeed, Negative for error<br>
	 * Original signature : <code>int kestrel_license_get_time_limit(int*, int*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:136</i>
	 */
	int kestrel_license_get_time_limit(IntBuffer start, IntBuffer end);
	/**
	 * @return Zero terminated serial number string, should be freed using free()<br>
	 * Original signature : <code>char* kestrel_license_get_sn()</code><br>
	 * <i>native declaration : include/kestrel_license.h:141</i>
	 */
	Pointer kestrel_license_get_sn();
	/**
	 * @return KESTREL_OK for succeed, Negative for error<br>
	 * Original signature : <code>int kestrel_license_has_capability(const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:147</i>
	 */
	int kestrel_license_has_capability(String capability);
	/**
	 * @return 1 for succeed, otherwise 0<br>
	 * Original signature : <code>int32_t kestrel_license_has_uuid()</code><br>
	 * <i>native declaration : include/kestrel_license.h:152</i>
	 */
	int kestrel_license_has_uuid();
	/**
	 * @return Negative for error, else return limit option value<br>
	 * Original signature : <code>int32_t kestrel_license_get_limit(const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:158</i>
	 */
	int kestrel_license_get_limit(String item);
	/**
	 * @return Zero terminated serial number string, should be freed using free()<br>
	 * Original signature : <code>char* kestrel_license_get_limit_str(const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:164</i>
	 */
	Pointer kestrel_license_get_limit_str(String item);
	/**
	 * @return KESTREL_OK for succeed, other for error<br>
	 * Original signature : <code>int kestrel_license_checkin(const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:170</i>
	 */
	int kestrel_license_checkin(String item);
	/**
	 * @param[in] item Key name going to be checked out<br>
	 * Original signature : <code>void kestrel_license_checkout(const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:175</i>
	 */
	void kestrel_license_checkout(String item);
	/**
	 * @return KESTREL_OK for succeed, other for error<br>
	 * Original signature : <code>int kestrel_license_get_online_activation_code(const char*, uint64_t, char**, size_t*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:184</i>
	 */
	int kestrel_license_get_online_activation_code(String license, long time_expire, PointerByReference signed_code, LongByReference signed_code_len);
	/**
	 * @return KESTREL_OK for succeed, other for error<br>
	 * Original signature : <code>int kestrel_license_preview(const char*, const char*, char**)</code><br>
	 * <i>native declaration : include/kestrel_license.h:193</i>
	 */
	int kestrel_license_preview(String product_name, String license, PointerByReference info);
	/**
	 * @return 1 for succeed, otherwise 0<br>
	 * Original signature : <code>int32_t kestrel_license_preview_has_uuid(const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:200</i>
	 */
	int kestrel_license_preview_has_uuid(String product_name, String license);
	/**
	 * @return KESTREL_OK for succeed, other for error<br>
	 * Original signature : <code>int kestrel_license_preview_get_authtype(const char*, const char*, int*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:209</i>
	 */
	int kestrel_license_preview_get_authtype(String product_name, String license, IntBuffer type);
	/**
	 * @return Negative for error, else return limit option value<br>
	 * Original signature : <code>int kestrel_license_preview_get_limit(const char*, const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:218</i>
	 */
	int kestrel_license_preview_get_limit(String product_name, String license, String item);
	/**
	 * @return Zero terminated serial number string, should be freed using free()<br>
	 * Original signature : <code>char* kestrel_license_preview_get_limit_str(const char*, const char*, const char*)</code><br>
	 * <i>native declaration : include/kestrel_license.h:227</i>
	 */
	Pointer kestrel_license_preview_get_limit_str(String product_name, String license, String item);
	/**
	 * @return KESTREL_OK for success, other for error<br>
	 * Original signature : <code>int kestrel_license_register_offline_verifier()</code><br>
	 * <i>native declaration : include/kestrel_license.h:233</i>
	 */
	int kestrel_license_register_offline_verifier();
	/**
	 * @return KESTREL_OK for success, other for error<br>
	 * Original signature : <code>int kestrel_license_register_qr_verifier()</code><br>
	 * <i>native declaration : include/kestrel_license.h:238</i>
	 */
	int kestrel_license_register_qr_verifier();
	/**
	 * @return KESTREL_OK for success, other for error<br>
	 * Original signature : <code>int kestrel_license_register_online_act_verifier()</code><br>
	 * <i>native declaration : include/kestrel_license.h:243</i>
	 */
	int kestrel_license_register_online_act_verifier();
}
