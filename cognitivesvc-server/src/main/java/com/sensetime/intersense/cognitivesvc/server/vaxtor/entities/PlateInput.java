package com.sensetime.intersense.cognitivesvc.server.vaxtor.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlateInput {
    private byte[] imageData; // 图像数据
    private int width; // 图像宽度
    private int height; // 图像高度
    @Builder.Default
    private String pixformat = "bgr"; // 像素格式
    private int imageSize; // 图像大小
} 