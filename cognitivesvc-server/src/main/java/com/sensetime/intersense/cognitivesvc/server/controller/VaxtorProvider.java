package com.sensetime.intersense.cognitivesvc.server.controller;

import com.sensetime.intersense.cognitivesvc.server.entities.CarplateEntity;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.core.PlateOcrProcessor;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateInput;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateOutput;
import com.sensetime.lib.clientlib.response.BaseRes;
import com.sensetime.lib.weblib.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;

@ConditionalOnExpression("${vaxtor.enableVaxtorPlateRecognition:true} && ${stream.xswitcher.enabled:true}")
@RestController(value = "vaxtorProvider")
@RequestMapping(value = "/vaxtor", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "vaxtorProvider", description = "vaxtor")
@Slf4j
public class VaxtorProvider extends BaseProvider {


    @Autowired
    @Qualifier("plateOcrProcessor")
    private PlateOcrProcessor plateOcrProcessor;


    @Operation(summary = "vaxtor_test", method = "GET")
    @RequestMapping(value = "/plateOcr", method = RequestMethod.GET)
    public BaseRes<CarplateEntity> plateOcr(@RequestParam("imgPath") String imgPath) {

        CarplateEntity result = null;
        try {
            // 读取图片数据
            BufferedImage bufferedImage = ImageIO.read(new FileInputStream(imgPath));
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "jpeg", out);
            byte[] imageData=out.toByteArray();

            // 创建输入对象
            PlateInput input = PlateInput.builder()
                    .imageData(imageData).imageSize(imageData.length)
                    .width(bufferedImage.getWidth()).height(bufferedImage.getHeight())
                    .build();

            // 提交请求并等待结果
            CompletableFuture<PlateOutput> future = plateOcrProcessor.submitRequest(input);
            PlateOutput output = future.get();

            // 处理结果
            if (output == null) {
                log.warn("no plate info found,imgPath={}",imgPath);
            } else {
                result = CarplateEntity.builder().plateText(output.getPlateNumberAscii()).build();

                log.info("============ Plate Information:{} ============",imgPath);
                log.info("Plate Number: " + output.getPlateNumberAscii());
                log.info("Country: " + output.getPlateCountry());
                log.info("Vehicle Make: " + output.getVehicleMake());
                log.info("Vehicle Model: " + output.getVehicleModel());
                log.info("Vehicle Color: " + output.getVehicleColor());
                log.info("Vehicle Class: " + output.getVehicleClass());
                if(output.getPlateBB() != null){
                    log.info("PlateBoundingBox: " + output.getPlateBB().toString());
                }
                log.info("=========================================");
            }

        } catch (Exception e) {
            log.error("Failed to process image: " + e.getMessage(), e);
            throw new BusinessException("3012","vaxtor recognize failed");
        }
        return BaseRes.success(result);
    }


}
