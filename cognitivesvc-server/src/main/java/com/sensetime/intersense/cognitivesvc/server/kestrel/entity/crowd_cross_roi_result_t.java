package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_cross_roi_result_t extends Structure {
	public int roi_id;
	/**
	 * <跨线端点坐标<br>
	 * C type : kestrel_point2d_t[1024]
	 */
	public kestrel_point2d_t[] cross_line = new kestrel_point2d_t[1024];
	public int cross_line_num;
	/**
	 * roi的坐标<br>
	 * C type : coord_list_t
	 */
	public coord_list_t roi_coords;
	/**
	 * < 跨线跟踪的目标<br>
	 * C type : crowd_head_target_list_t
	 */
	public crowd_head_target_list_t head_targets;
	/** < roi动态进线计数，当前帧 */
	public int in_cnt;
	/**
	 * <入线人的track id数组，大小是in_cnt<br>
	 * C type : int32_t*
	 */
	public Pointer in_track_ids;
	/** < roi动态出线计数，当前帧 */
	public int out_cnt;
	/**
	 * <出线人的track id数组，大小是out_cnt<br>
	 * C type : int32_t*
	 */
	public Pointer out_track_ids;
	public crowd_cross_roi_result_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("roi_id", "cross_line", "cross_line_num", "roi_coords", "head_targets", "in_cnt", "in_track_ids", "out_cnt", "out_track_ids");
	}
	/**
	 * @param cross_line <跨线端点坐标<br>
	 * C type : kestrel_point2d_t[1024]<br>
	 * @param roi_coords roi的坐标<br>
	 * C type : coord_list_t<br>
	 * @param head_targets < 跨线跟踪的目标<br>
	 * C type : crowd_head_target_list_t<br>
	 * @param in_cnt < roi动态进线计数，当前帧<br>
	 * @param in_track_ids <入线人的track id数组，大小是in_cnt<br>
	 * C type : int32_t*<br>
	 * @param out_cnt < roi动态出线计数，当前帧<br>
	 * @param out_track_ids <出线人的track id数组，大小是out_cnt<br>
	 * C type : int32_t*
	 */
	public crowd_cross_roi_result_t(int roi_id, kestrel_point2d_t cross_line[], int cross_line_num, coord_list_t roi_coords, crowd_head_target_list_t head_targets, int in_cnt, Pointer in_track_ids, int out_cnt, Pointer out_track_ids) {
		super();
		this.roi_id = roi_id;
		if ((cross_line.length != this.cross_line.length)) 
			throw new IllegalArgumentException("Wrong array size !");
		this.cross_line = cross_line;
		this.cross_line_num = cross_line_num;
		this.roi_coords = roi_coords;
		this.head_targets = head_targets;
		this.in_cnt = in_cnt;
		this.in_track_ids = in_track_ids;
		this.out_cnt = out_cnt;
		this.out_track_ids = out_track_ids;
	}
	public crowd_cross_roi_result_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_cross_roi_result_t implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_cross_roi_result_t implements Structure.ByValue {
		
	};
}