package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_drop_head_target_list_t extends Structure {
	/**
	 * < 目标数组<br>
	 * C type : crowd_drop_head_target_t*
	 */
	public Pointer targets;
	/** < 目标个数 */
	public int targets_num;
	public crowd_drop_head_target_list_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("targets", "targets_num");
	}
	/**
	 * @param targets < 目标数组<br>
	 * C type : crowd_drop_head_target_t*<br>
	 * @param targets_num < 目标个数
	 */
	public crowd_drop_head_target_list_t(Pointer targets, int targets_num) {
		super();
		this.targets = targets;
		this.targets_num = targets_num;
	}
	public crowd_drop_head_target_list_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_drop_head_target_list_t implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_drop_head_target_list_t implements Structure.ByValue {
		
	};
}