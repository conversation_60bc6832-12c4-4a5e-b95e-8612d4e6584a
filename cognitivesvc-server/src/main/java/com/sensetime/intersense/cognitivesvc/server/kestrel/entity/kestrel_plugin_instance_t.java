package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_plugin.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_plugin_instance_t extends Structure {
	/** C type : const kestrel_plugin_t* */
	public Pointer plugin_api;
	
	/** C type : void* */
	public Pointer instance_handle;
	
	public int refcount;
	
	public kestrel_plugin_instance_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("plugin_api", "instance_handle", "refcount");
	}
	/**
	 * @param plugin_api C type : const kestrel_plugin_t*<br>
	 * @param instance_handle C type : void*
	 */
	public kestrel_plugin_instance_t(Pointer plugin_api, Pointer instance_handle, int refcount) {
		super();
		this.plugin_api = plugin_api;
		this.instance_handle = instance_handle;
		this.refcount = refcount;
	}
	public kestrel_plugin_instance_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_plugin_instance_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_plugin_instance_t implements Structure.ByValue {
		
	};
}
