package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/crowd_oversea_define.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class crowd_drop_head_target_t extends Structure {
	/**
	 * 人头/体框<br>
	 * C type : kestrel_area2d_t
	 */
	public kestrel_area2d_t rect_position;
	public int track_id;
	public crowd_drop_head_target_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("rect_position", "track_id");
	}
	/**
	 * @param rect_position 人头/体框<br>
	 * C type : kestrel_area2d_t
	 */
	public crowd_drop_head_target_t(kestrel_area2d_t rect_position, int track_id) {
		super();
		this.rect_position = rect_position;
		this.track_id = track_id;
	}
	public crowd_drop_head_target_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends crowd_drop_head_target_t implements Structure.ByReference {
		
	};
	public static class ByValue extends crowd_drop_head_target_t implements Structure.ByValue {
		
	};
}