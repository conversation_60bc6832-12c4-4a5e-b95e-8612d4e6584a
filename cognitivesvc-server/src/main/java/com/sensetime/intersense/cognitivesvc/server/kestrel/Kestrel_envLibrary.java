package com.sensetime.intersense.cognitivesvc.server.kestrel;
import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.NativeLibrary;
import com.sun.jna.Pointer;

/**
 * <PERSON><PERSON> Wrapper for library <b>kestrel_env</b><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public interface Kestrel_envLibrary extends Library {
	public static final String JNA_LIBRARY_NAME = "kestrel";
	public static final NativeLibrary JNA_NATIVE_LIB = NativeLibrary.getInstance(Kestrel_envLibrary.JNA_LIBRARY_NAME);
	public static final Kestrel_envLibrary INSTANCE = (Kestrel_envLibrary)Native.load(Kestrel_envLibrary.JNA_LIBRARY_NAME, Kestrel_envLibrary.class);
	/**
	 * allowed be called in the primary thread only.<br>
	 * Original signature : <code>int kestrel_init(const char*)</code><br>
	 * <i>native declaration : include/kestrel_env.h:30</i>
	 */
	int kestrel_init(String product);
	/**
	 * @return Product name if succeed, NULL for error<br>
	 * Original signature : <code>char* kestrel_product_name()</code><br>
	 * <i>native declaration : include/kestrel_env.h:35</i>
	 */
	Pointer kestrel_product_name();
	/**
	 * @note The user should guarantee that all non-primary thread terminated.<br>
	 * Original signature : <code>void kestrel_deinit()</code><br>
	 * <i>native declaration : include/kestrel_env.h:42</i>
	 */
	void kestrel_deinit();
}
