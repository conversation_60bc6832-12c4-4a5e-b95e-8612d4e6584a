package com.sensetime.intersense.cognitivesvc.server.kestrel.entity;
import com.sun.jna.Callback;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;
/**
 * <i>native declaration : include/kestrel_annotator_def.h</i><br>
 * This file was autogenerated by <a href="http://jnaerator.googlecode.com/">JNAerator</a>,<br>
 * a tool written by <a href="http://ochafik.com/"><PERSON></a> that <a href="http://code.google.com/p/jnaerator/wiki/CreditsAndLicense">uses a few opensource projects.</a>.<br>
 * For help, please visit <a href="http://nativelibs4java.googlecode.com/">NativeLibs4Java</a> , <a href="http://rococoa.dev.java.net/">Rococoa</a>, or <a href="http://jna.dev.java.net/">JNA</a>.
 */
public class kestrel_annotator_api_t extends Structure {
	/** C type : const char* */
	public Pointer config_schema;
	/** C type : const char* */
	public Pointer params_schema;
	/** C type : const char* */
	public Pointer result_schema;
	/** <i>native declaration : include/kestrel_annotator_def.h</i> */
	public interface k_err_callback extends Callback {
		int apply(Pointer hdl, Pointer in, Pointer out);
	};
	/** <i>native declaration : include/kestrel_annotator_def.h</i> */
	public interface k_err_callback2 extends Callback {
		int apply(Pointer hdl, Pointer in, Pointer out);
	};
	/** <i>native declaration : include/kestrel_annotator_def.h</i> */
	public interface k_err_callback3 extends Callback {
		int apply(Pointer hdl, Pointer in, Pointer out);
	};
	public kestrel_annotator_api_t() {
		super();
	}
	protected List<String> getFieldOrder() {
		return Arrays.asList("config_schema", "params_schema", "result_schema");
	}
	/**
	 * @param config_schema C type : const char*<br>
	 * @param params_schema C type : const char*<br>
	 * @param result_schema C type : const char*
	 */
	public kestrel_annotator_api_t(Pointer config_schema, Pointer params_schema, Pointer result_schema) {
		super();
		this.config_schema = config_schema;
		this.params_schema = params_schema;
		this.result_schema = result_schema;
	}
	public kestrel_annotator_api_t(Pointer peer) {
		super(peer);
	}
	public static class ByReference extends kestrel_annotator_api_t implements Structure.ByReference {
		
	};
	public static class ByValue extends kestrel_annotator_api_t implements Structure.ByValue {
		
	};
}
