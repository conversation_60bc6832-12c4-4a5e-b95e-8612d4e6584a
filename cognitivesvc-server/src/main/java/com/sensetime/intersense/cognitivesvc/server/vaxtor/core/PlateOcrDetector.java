package com.sensetime.intersense.cognitivesvc.server.vaxtor.core;

import com.sensetime.intersense.cognitivesvc.server.properties.VaxtorConfigProperties;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateInput;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.entities.PlateOutput;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.Vaxtor_ocrLibrary;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.tAnalytics;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.tInitMMC;
import com.sensetime.intersense.cognitivesvc.server.vaxtor.jna.tPlateInfo;
import com.sun.jna.Memory;
import com.sun.jna.NativeLong;
import com.sun.jna.ptr.NativeLongByReference;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.List;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


/**
 * 车牌OCR检测器
 * 负责初始化和执行车牌识别相关操作
 */
@Slf4j
public class PlateOcrDetector {
    private static final Vaxtor_ocrLibrary OCR_LIB = Vaxtor_ocrLibrary.INSTANCE;

    private final int detectorId;
    private final NativeLong ocrId;

    // 添加缩放比例常量
    private Double[] SCALE_FACTORS = {1.0};


    /**
     * 初始化车牌检测器
     *
     * @param id 检测器ID
     * @throws Exception 初始化失败时抛出异常
     */
    public PlateOcrDetector(int id, String ocrDataPath, List<String> countryList) throws Exception {
        this.detectorId = id;

        // 初始化OCR配置
        OCR_LIB.ocrSetFullPathDataFiles(ocrDataPath);

        // 配置支持的国家/地区
        ocrId = initializeOcr(countryList);

        if (ocrId.intValue() < 0) {
            throw new Exception(">>> OCR detector initialization failed: " + getErrorMessage(ocrId));
        }

        // 初始化MMC配置
        initializeMMC();

        log.info(">>> Detector " + detectorId + " initialized successfully");
    }


    public PlateOcrDetector(int id, VaxtorConfigProperties properties) throws Exception {
        this.detectorId = id;

        // 初始化OCR配置
        OCR_LIB.ocrSetFullPathDataFiles(properties.getOcrDataPath());

        // 配置支持的国家/地区
        VaxtorConfigProperties.InitializeConfig initConf = properties.getInitConf();
        ocrId = initializeOcr(properties.getCountryList(), initConf.getOperMode(), initConf.getSamePlateDelay(), initConf.getMinCharHeight(), initConf.getMaxCharHeight(), initConf.getOcrComplexity());

        if (ocrId.intValue() < 0) {
            throw new Exception(">>> OCR detector initialization failed: " + getErrorMessage(ocrId));
        }

        // 初始化MMC配置
        VaxtorConfigProperties.MMCConfig mmcConfig = properties.getMmcConfig();
        initializeMMC(mmcConfig.getAnalyticType(), mmcConfig.getAnalyticsQuality(), mmcConfig.getMin_globalConfidence());

        // 设置检测图片放大比例数组
        SCALE_FACTORS = properties.getScaleFactors() == null? SCALE_FACTORS: properties.getScaleFactors();

        log.info(">>> Detector " + detectorId + " initialized successfully");
    }


    private NativeLong initializeOcr(List<String> countryList) {
        return this.initializeOcr(countryList, 1l, 0l, 14l, 60l, 3l);
    }

    /**
     * 初始化OCR配置
     *
     * @return OCR实例ID
     */
    private NativeLong initializeOcr(List<String> countryList, Long operMode, Long samePlateDelay, Long minCharHeight, Long maxCharHeight, Long ocrComplexity) {
        // 配置支持的国家列表 - 单个国家的配置
//        NativeLong countryCode = OCR_LIB.ocrGetCountryStateCode("Singapore", null);
//        NativeLongByReference countriesRef = new NativeLongByReference(countryCode);


        // 设置国家 - 多个国家的配置
        int numCountries = countryList.size();
        NativeLong[] codeCountries = new NativeLong[numCountries];
        for (int i = 0; i < numCountries; i++) {
            codeCountries[i] = OCR_LIB.ocrGetCountryStateCode(countryList.get(i), null);
        }

        // 创建 Memory 来存储国家数组
        Memory countriesMemory = new Memory(NativeLong.SIZE * numCountries);
        for (int i = 0; i < numCountries; i++) {
            countriesMemory.setNativeLong(i * NativeLong.SIZE, codeCountries[i]);
        }

        NativeLongByReference countriesRef = new NativeLongByReference();
        countriesRef.setPointer(countriesMemory);


        return OCR_LIB.ocrInitializeEx(
                new NativeLong(operMode),      // 同步模式
                countriesRef,           // 国家代码
                new NativeLong(numCountries),      // 支持的国家数量
                new NativeLong(samePlateDelay),      // 相同车牌延迟
                new NativeLong(minCharHeight),     // 最小字符高度
                new NativeLong(maxCharHeight),     // 最大字符高度
                new NativeLong(ocrComplexity),      // OCR复杂度
                new NativeLong(0)       // 保留字段
        );
    }

    private void initializeMMC() throws Exception {
        initializeMMC(3l, 1l, 20l);
    }

    /**
     * 初始化MMC(Make Model Color)配置
     *
     * <p>
     * 参数说明:
     * internal_analytic:
     * 1(默认) - OCR内部运行分析
     * 0 - 使用ocrAnalyzeMMCAlone()自行分析
     * <p>
     * analytic_type:
     * 分析类型,可选值1-3
     * 1 - 仅分析车辆品牌
     * 2 - 分析车辆品牌和型号
     * 3 - 分析车辆品牌、型号和颜色
     * <p>
     * analytics_quality:
     * 分析质量,可选值1-3
     * 1 - 低质量(快速)
     * 2 - 中等质量
     * 3 - 高质量(较慢)
     * <p>
     * min_global_confidence:
     * 最小全局置信度,范围0-100
     * 建议值:20-30
     *
     * @throws Exception 初始化失败时抛出异常
     */
    private void initializeMMC(Long analytic_type, Long analytics_quality, Long min_global_confidence) throws Exception {
        tInitMMC mmc = new tInitMMC();
        mmc.analytic_type = new NativeLong(analytic_type);        // 分析类型:品牌+型号+颜色
        mmc.analytics_quality = new NativeLong(analytics_quality);     // 分析质量:低(快速)
        mmc.min_global_confidence = new NativeLong(min_global_confidence);// 最小全局置信度

        // 使用新的MMC配置方法,设置为内部分析模式
        NativeLong result = OCR_LIB.ocrConfigAnalyticsMMCEx(
                ocrId,
                new NativeLong(1),  // internal_analytic=1,使用内部分析
                mmc
        );

        if (result.intValue() <= 0) {
            throw new Exception("MMC初始化失败: " + getErrorMessage(result));
        }
    }

    /**
     * 处理输入图像并识别车牌
     *
     * @param input 输入图像数据
     * @return 车牌识别结果
     */
    public PlateOutput process(PlateInput input) {
        PlateOutput result = null;

        try {
            for (double scaleFactor : SCALE_FACTORS) {
                // 第一次使用原始图片，后续进行缩放
                PlateInput processInput = scaleFactor == 1.0 ? input : scaleImage(input, scaleFactor);
                
                // 查找并识别车牌
                int numPlates = findPlates(processInput);
                if (numPlates > 0) {
                    result = getPlateInfo();
                    log.debug(">>> found plate with scale factor {}", scaleFactor);
                    break;  // 找到车牌就退出循环
                }
                
                log.debug(">>> No plate found with scale factor {}, trying next scale...", scaleFactor);
            }
        } catch (Exception e) {
            log.warn(">>> Image processing failed: " + e.getMessage());
        }

        return result;
    }

    private PlateInput scaleImage(PlateInput plateInput,double scaleFactor) throws Exception {
        // Step 1: 将 byte[] 转为 BufferedImage
        ByteArrayInputStream inputStream = new ByteArrayInputStream(plateInput.getImageData());
        BufferedImage originalImage = ImageIO.read(inputStream);

        if(plateInput.getHeight() == 0 || plateInput.getWidth() == 0) {
            throw new IllegalArgumentException("宽高参数不合法，无法进行放大");
        }

        if (originalImage == null) {
            throw new IllegalArgumentException("无法读取图像数据");
        }

        // Step 2: 创建放大的图像
        Double newWidthDouble = plateInput.getWidth() * scaleFactor;
        int newWidth = newWidthDouble.intValue();
        Double newHeightDouble = plateInput.getHeight() * scaleFactor;
        int newHeight = newHeightDouble.intValue();
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, originalImage.getType());

        Graphics2D g2d = scaledImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        // Step 3: 将放大的图像转回 byte[]
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(scaledImage, "jpg", outputStream); // 假设图像格式为 JPG
        byte[] scaledImageData = outputStream.toByteArray();

        // Step 4: 更新 PlateInput 对象
        PlateInput newPlateInput = new PlateInput();
        newPlateInput.setImageData(scaledImageData);
        newPlateInput.setWidth(newWidth);
        newPlateInput.setHeight(newHeight);
        newPlateInput.setImageSize(scaledImageData.length);
        newPlateInput.setPixformat(plateInput.getPixformat());

        return newPlateInput;
    }

    /**
     * 在图像中查找车牌
     *
     * @param input 输入图像数据
     * @return 找到的车牌数量
     */
    private int findPlates(PlateInput input) {
        CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> processImage(input));

        int result = 0;
        try {
            // 设置超时时间为500毫秒
            result = future.orTimeout(500, TimeUnit.MILLISECONDS).join();
        } catch (Exception e) {
            if (e.getCause() instanceof TimeoutException) {
                log.warn(">>> Processing image timed out after 500 milliseconds.");
                result = -1; // 超时返回特定的错误代码
            } else {
                log.error(">>> Error processing image: {}", e.getMessage(), e);
                result = -1; // 处理失败返回特定的错误代码
            }
        }

        return result;
    }

    private int processImage(PlateInput input) {
        int result = 0;
        String format = input.getPixformat();
        long start = System.currentTimeMillis();
        // 处理图像格式
        switch (format.toLowerCase()) {
            case "nv12":
                log.debug(">>> Processing NV12 format image: {}x{}", input.getWidth(), input.getHeight());
                result = processNV12Format(input);
                break;
            case "bgr":
                log.debug(">>> Processing BGR format image: size={}", input.getImageSize());
                result = processBGRFormat(input);
                break;
            case "rgb":
                log.debug(">>> Processing BGR format image: size={}", input.getImageSize());
                result = processRGBFormat(input);
                break;
            default:
                result = -9999;
                break;
        }

        if (result == -9999)
            throw new IllegalArgumentException("不支持的图像格式: " + format);

        long end = System.currentTimeMillis();
        log.debug(">>> vaxtor plate processor, ts= {} ms", end - start);
        return result;
    }

    private int processNV12Format(PlateInput input) {
        int result = OCR_LIB.ocrFindPlatesNV12(
                ocrId,
                input.getImageData(),
                new NativeLong(input.getWidth()),
                new NativeLong(input.getHeight()),
                0L,
                0L
        ).intValue();

        if (result < 0) {
            log.error(">>> NV12 format processing failed: {}", getErrorMessage(new NativeLong(result)));
        }
        return result;
    }

    private int processBGRFormat(PlateInput input) {
        // BGR格式需要检查imageSize
        if (input.getImageSize() <= 0) {
            throw new IllegalArgumentException("BGR格式需要有效的imageSize");
        }

        int result = OCR_LIB.ocrFindPlatesJPEG(
                ocrId,
                input.getImageData(),
                new NativeLong(input.getImageSize()),
                0L,
                0L
        ).intValue();

        if (result < 0) {
            log.error(">>> BGR format processing failed: {}", getErrorMessage(new NativeLong(result)));
        }
        return result;
    }

    private int processRGBFormat(PlateInput input) {
        // RGB格式需要检查width和height
        if (input.getWidth() <= 0 || input.getHeight() <= 0) {
            throw new IllegalArgumentException("BGR格式需要有效的width和height");
        }
        int result = OCR_LIB.ocrFindPlatesRGB(
                ocrId,
                input.getImageData(),
                new NativeLong(input.getWidth()),
                new NativeLong(input.getHeight()),
                0L,
                0L
        ).intValue();

        if (result < 0) {
            log.error(">>> RGB format processing failed: {}", getErrorMessage(new NativeLong(result)));
        }
        return result;
    }

    /**
     * 获取识别到的车牌信息
     *
     * @return 车牌详细信息
     */
    private PlateOutput getPlateInfo() {
        tPlateInfo plateInfo = new tPlateInfo();
        tAnalytics analytics = new tAnalytics();

        OCR_LIB.ocrGetPlateInfo(ocrId, new NativeLong(0), plateInfo, analytics);

        PlateOutput output = new PlateOutput();
        output.setPlateNumberAscii(new String(plateInfo._plate_number_ascii).trim());
        output.setPlateCountry(new String(plateInfo._plate_country).trim());
        output.setVehicleMake(new String(analytics._vehicle_make).trim());
        output.setVehicleModel(new String(analytics._vehicle_model).trim());
        output.setVehicleColor(new String(analytics._vehicle_color).trim());
        output.setVehicleClass(new String(analytics._vehicle_class).trim());

        return output;
    }

    /**
     * 获取错误描述信息
     *
     * @param code 错误代码
     * @return 错误描述
     */
    private String getErrorMessage(NativeLong code) {
        byte[] message = new byte[256];
        ByteBuffer buffer = ByteBuffer.wrap(message);
        OCR_LIB.ocrGetErrorDescription(code, buffer);
        return new String(message).trim();
    }

    /**
     * 终止检测器并释放资源
     */
    public void terminate() {
        OCR_LIB.ocrShutdown(ocrId);
        log.info(">>> Detector " + detectorId + " terminated");
    }
} 